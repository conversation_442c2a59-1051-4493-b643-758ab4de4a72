version: 0.0
os: linux
files:
  - source: /
    destination: /var/www/myapp
permissions:
  - object: /var/www/myapp
    owner: ec2-user
    group: ec2-user
    mode: 755
hooks:
  ApplicationStop:
    - location: scripts/stop.sh
      timeout: 60
      runas: ec2-user
  BeforeInstall:
    - location: scripts/before-install
      timeout: 300
      runas: root
  AfterInstall:
    - location: scripts/after-install
      timeout: 300
      runas: root
