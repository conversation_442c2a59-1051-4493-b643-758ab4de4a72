/* eslint-disable no-undef */
/** @type {import('tailwindcss').Config} */

const colors = require('tailwindcss/colors')

module.exports = {
  darkMode: 'class',
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      animation: {
        // Bounces 5 times 1s equals 5 seconds
        'bounce-short': 'bounce 1s ease-in-out 5',
        fadein: 'fade-in 1s ease-in-out 0.25s 1',
        fadeout: 'fade-out 1s ease-out 0.25s 1',
        fadeindown: 'fade-in-down 1s ease-in 0.25s 1',
        fadeintopleft: 'fade-in-top-left 1s ease-out 0.25s 1',
        fadeintopright: 'fade-in-top-right 1s ease-out 0.25s 1',
        fadeinbottomleft: 'fade-in-bottom-left 1s ease-out 0.25s 1',
        fadeinbottomright: 'fade-in-bottom-right 1s ease-out 0.25s 1',
        fadeinleft: 'fade-in-left 1s ease-in-out 0.25s 1',
        fadeinbouncedown: 'fade-in-bouncedown 1s ease-in-out 0.25s 1',
        fadeinbounceup: 'fade-in-bounceup 1s ease-in-out 0.25s 1',
        fadeinbounceright: 'fade-in-bounce-right 1s ease-in-out 0.25s 1',
        fadeinbounceleft: 'fade-in-bounce-left 1s ease-in-out 0.25s 1',
        fadeinright: 'fade-in-right 1s ease-in-out 0.25s 1',
        fadeinup: 'fade-in-up 1s ease-in-out 0.25s 1',
        fadeoutdown: 'fade-out-down 1s ease-in-out 0.25s 1',
        fadeouttopleft: 'fade-out-top-left 1s ease-in-out 0.25s 1',
        fadeouttopright: 'fade-out-top-right 1s ease-in-out 0.25s 1',
        fadeoutleft: 'fade-out-left 1s ease-in-out 0.25s 1',
        fadeoutright: 'fade-out-right 1s ease-in-out 0.25s 1',
        fadeoutup: 'fade-out-up 1s ease-in-out 0.25s 1',
        slideindown: 'slide-in-down 1s ease-in-out 0.25s 1',
        slideinleft: 'slide-in-left 1s ease-in-out 0.25s 1',
        slideinright: 'slide-in-right 1s ease-in-out 0.25s 1',
        slideinup: 'slide-in-up 1s ease-in-out 0.25s 1',
        slideoutdown: 'slide-out-down 1s ease-in-out 0.25s 1',
        slideoutleft: 'slide-out-left 1s ease-in-out 0.25s 1',
        slideoutright: 'slide-out-right 1s ease-in-out 0.25s 1',
        slideoutup: 'slide-out-up 1s ease-in-out 0.25s 1',
        slidedown: 'slide-down 1s ease-in-out 0.25s 1',
        slideleft: 'slide-left 1s ease-in-out 0.25s 1',
        slideright: 'slide-right 1s ease-in-out 0.25s 1',
        slideup: 'slide-up 1s ease-in-out 0.25s 1',
        zoomin: 'zoom-in 1s ease-in-out 0.25s 1',
        zoomout: 'zoom-out 1s ease-in-out 0.25s 1',
        tada: 'tada 1s ease-in-out 0.25s 1',
        spinnergrow: 'spinner-grow 1s ease-in-out 0.25s 1',
        placeholderwave: 'placeholder-wave 1s ease-in-out 0.25s 1',
        showupclock: 'show-up-clock 1s ease-in-out 0.25s 1',
        dropin: 'drop-in 0.5s ease-in-out 0.25s 1',
        dropout: 'drop-out 0.5s ease-in-out 0.25s 1',
        flyin: 'fly-in 0.6s ease-in-out 0.25s 1',
        flyinup: 'fly-in-up 0.6s ease-in-out 0.25s 1',
        flyindown: 'fly-in-down 0.6s ease-in-out 0.25s 1',
        flyinleft: 'fly-in-left 0.6s ease-in-out 0.25s 1',
        flyinright: 'fly-in-right 0.6s ease-in-out 0.25s 1',
        flyout: 'fly-out 0.6s ease-in-out 0.25s 1',
        flyoutup: 'fly-out-up 0.6s ease-in-out 0.25s 1',
        flyoutdown: 'fly-out-down 0.6s ease-in-out 0.25s 1',
        flyoutleft: 'fly-out-left 0.6s ease-in-out 0.25s 1',
        flyoutright: 'fly-out-right 0.6s ease-in-out 0.25s 1',
        browsein: 'browse-in 0.4s ease-in-out 0.25s 1',
        browseout: 'browse-out 0.4s ease-in-out 0.25s 1',
        browseoutleft: 'browse-out-left 0.4s ease-in-out 0.25s 1',
        browseoutright: 'browse-out-right 0.4s ease-in-out 0.25s 1',
        jiggle: 'jiggle 0.6s ease-in-out 0.25s 1',
        flash: 'flash 0.6s ease-in-out 0.25s 1',
        shake: 'shake 0.6s ease-in-out 0.25s 1',
        glow: 'glow 0.6s ease-in-out 0.25s 1',
        zoomIn: 'zoom-in 1s ease-out 0.25s 1',
        zoomOut: 'zoom-out 1s ease-out 0.25s 1',
        zoomOutLeft: 'zoom-out-left 1s ease-out 0.25s 1',
        zoomOutRight: 'zoom-out-right 1s ease-out 0.25s 1',
        zoomOutUp: 'zoom-out-up 1s ease-out 0.25s 1',
        zoomOutDown: 'zoom-out-down 1s ease-out 0.25s 1',
        zoomInUp: 'zoom-in-up 1s ease-out 0.25s 1',
        zoomInBotoomRight: 'zoom-in-bottom-right 1s ease-out 0.25s 1',
        zoomInBotoomLeft: 'zoom-in-bottom-left 1s ease-out 0.25s 1',
        zoomInTopRight: 'zoom-in-top-right 1s ease-out 0.25s 1',
        zoomInTopLeft: 'zoom-in-top-left 1s ease-out 0.25s 1',
        zoomInDown: 'zoom-in-down 1s ease-out 0.25s 1',
        zoomInRight: 'zoom-in-right 1s ease-out 0.25s 1',
        zoomInLeft: 'zoom-in-left 1s ease-out 0.25s 1',
        flipx: 'flipx 2s 0.25s 1',
        flipxRight: 'flipx-right 1s ease 0.25s 1',
        flipxLeft: 'flipx-left 1s ease 0.25s 1',
        flipxTopLeft: 'flipx-top-left 1s ease 0.25s 1',
        flipxTopRight: 'flipx-top-right 1s ease 0.25s 1',
        flipxBottomLeft: 'flipx-bottom-left 1s ease 0.25s 1',
        flipxBottomRight: 'flipx-bottom-right 1s ease 0.25s 1',
        flipxUp: 'flipx-up 1s ease 0.25s 1',
        flipxDown: 'flipx-down 1s ease 0.25s 1',
        flipRight: 'flip-right 1s ease 0.25s 1',
        flipLeft: 'flip-left 1s ease 0.25s 1',
        flipTopLeft: 'flip-top-left 1s ease 0.25s 1',
        flipTopRight: 'flip-top-right 1s ease 0.25s 1',
        flipBottomLeft: 'flip-bottom-left 1s ease 0.25s 1',
        flipBottomRight: 'flip-bottom-right 1s ease 0.25s 1',
        flipUp: 'flip-up 1s ease 0.25s 1',
        flipDown: 'flip-down 1s ease 0.25s 1',
        flip: 'flip 2s 0.25s 1',
        wiggle: 'wiggle 0.8s ease 0.25s 2',
      },
      width: {
        '1p': '1%',
        '2p': '2%',
        '3p': '3%',
        '4p': '4%',
        '5p': '5%',
        '6p': '6%',
        '7p': '7%',
        '8p': '8%',
        '9p': '9%',
        '10p': '10%',
        '11p': '11%',
        '12p': '12%',
        '13p': '13%',
        '14p': '14%',
        '15p': '15%',
        '16p': '16%',
        '17p': '17%',
        '18p': '18%',
        '19p': '19%',
        '20p': '20%',
        '21p': '21%',
        '22p': '22%',
        '23p': '23%',
        '24p': '24%',
        '25p': '25%',
        '26p': '26%',
        '27p': '27%',
        '28p': '28%',
        '29p': '29%',
        '30p': '30%',
        '31p': '31%',
        '32p': '32%',
        '33p': '33%',
        '34p': '34%',
        '35p': '35%',
        '36p': '36%',
        '37p': '37%',
        '38p': '38%',
        '39p': '39%',
        '40p': '40%',
        '41p': '41%',
        '42p': '42%',
        '43p': '43%',
        '44p': '44%',
        '45p': '45%',
        '46p': '46%',
        '47p': '47%',
        '48p': '48%',
        '49p': '49%',
        '50p': '50%',
        '51p': '51%',
        '52p': '52%',
        '53p': '53%',
        '54p': '54%',
        '55p': '55%',
        '56p': '56%',
        '57p': '57%',
        '58p': '58%',
        '59p': '59%',
        '60p': '60%',
        '61p': '61%',
        '62p': '62%',
        '63p': '63%',
        '64p': '64%',
        '65p': '65%',
        '66p': '66%',
        '67p': '67%',
        '68p': '68%',
        '69p': '69%',
        '70p': '70%',
        '71p': '71%',
        '72p': '72%',
        '73p': '73%',
        '74p': '74%',
        '75p': '75%',
        '76p': '76%',
        '77p': '77%',
        '78p': '78%',
        '79p': '79%',
        '80p': '80%',
        '81p': '81%',
        '82p': '82%',
        '83p': '83%',
        '84p': '84%',
        '85p': '85%',
        '86p': '86%',
        '87p': '87%',
        '88p': '88%',
        '89p': '89%',
        '90p': '90%',
        '91p': '91%',
        '92p': '92%',
        '93p': '93%',
        '94p': '94%',
        '95p': '95%',
        '96p': '96%',
        '97p': '97%',
        '98p': '98%',
        '99p': '99%',
      },
      fontFamily: {
        poppins: ['Poppins', 'sans-serif'],
        dm: ['DM Sans', 'sans-serif'],
        Montserrat: ['Montserrat', 'sans-serif'],
        Inter: ['Inter', 'sans-serif'],
      },
      boxShadow: {
        '3xl': '14px 17px 40px 4px',
        inset: 'inset 0px 18px 22px',
        darkinset: '0px 4px 4px inset',
      },
      borderRadius: {
        primary: '20px',
      },
      keyframes: {
        'fade-in': {
          '0%': {
            opacity: 0,
          },
          '100%': {
            opacity: 1,
          },
        },
        'fade-out': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
          },
        },
        'fade-in-down': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(0, -100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-top-left': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(-100%, -100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-top-right': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(100%, -100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },

        'fade-in-bottom-left': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(100%, 100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-bottom-right': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(-100%, 100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-bounce-right': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(100%, 0%, 0)',
          },
          '33%': {
            opacity: 0.5,
            transform: 'translate3d(0%, 0%, 0)',
          },
          '66%': {
            opacity: 0.7,
            transform: 'translate3d(20%, 0%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-bounce-left': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(-100%, 0%, 0)',
          },
          '33%': {
            opacity: 0.5,
            transform: 'translate3d(0%, 0%, 0)',
          },
          '66%': {
            opacity: 0.7,
            transform: 'translate3d(-20%, 0%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-bouncedown': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(0%, -100%, 0)',
          },
          '33%': {
            opacity: 0.5,
            transform: 'translate3d(0%, 0%, 0)',
          },
          '66%': {
            opacity: 0.7,
            transform: 'translate3d(0%, -20%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-bounceup': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(0%, 100%, 0)',
          },
          '33%': {
            opacity: 0.5,
            transform: 'translate3d(0%, 0%, 0)',
          },
          '66%': {
            opacity: 0.7,
            transform: 'translate3d(0%, 20%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-left': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(-100%, 0, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-right': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(100%, 0, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-in-up': {
          '0%': {
            opacity: 0,
            transform: 'translate3d(0, 100%, 0)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fade-out-down': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d(0, 100%, 0)',
          },
        },
        'fade-out-left': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d(-100%, 0, 0)',
          },
        },
        'fade-out-top-left': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d(-100%, -100%, 0)',
          },
        },
        'fade-out-top-right': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d( 100%, -100%, 0)',
          },
        },
        'fade-out-right': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d(100%, 0, 0)',
          },
        },
        'fade-out-up': {
          '0%': {
            opacity: 1,
          },
          '100%': {
            opacity: 0,
            transform: 'translate3d(0, -100%, 0)',
          },
        },
        'slide-in-down': {
          '0%': {
            visibility: 'visible',
            transform: 'translate3d(0, -100%, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'slide-in-left': {
          '0%': {
            visibility: 'visible',
            transform: 'translate3d(-100%, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'slide-in-right': {
          '0%': {
            visibility: 'visible',
            transform: 'translate3d(100%, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'slide-in-up': {
          '0%': {
            visibility: 'visible',
            transform: 'translate3d(0, 100%, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'slide-out-down': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            visibility: 'hidden',
            transform: 'translate3d(0, 100%, 0)',
          },
        },
        'slide-out-left': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            visibility: 'hidden',
            transform: 'translate3d(-100%, 0, 0)',
          },
        },
        'slide-out-right': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            visibility: 'hidden',
            transform: 'translate3d(100%, 0, 0)',
          },
        },
        'slide-out-up': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            visibility: 'hidden',
            transform: 'translate3d(0, -100%, 0)',
          },
        },
        'slide-down': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 100%, 0)',
          },
        },
        'slide-left': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(-100%, 0, 0)',
          },
        },
        'slide-right': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(100%, 0, 0)',
          },
        },
        'slide-up': {
          '0%': {
            transform: 'translate3d(0, 0, 0)',
          },
          '100%': {
            transform: 'translate3d(0, -100%, 0)',
          },
        },
        tada: {
          '0%': {
            transform: 'scale3d(1, 1, 1)',
          },
          '10%, 20%': {
            transform: 'scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg)',
          },
          '30%, 50%, 70%, 90%': {
            transform: 'scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)',
          },
          '40%, 60%, 80%': {
            transform: 'scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)',
          },
          '100%': {
            transform: 'scale3d(1, 1, 1)',
          },
        },
        'spinner-grow': {
          '0%': {
            transform: 'scale(0)',
          },

          '100%': {
            transform: 'none',
            opacity: '1',
          },
        },
        'placeholder-wave': {
          '100%': {
            maskPosition: '-200% 0%',
          },
        },
        'show-up-clock': {
          '0%': {
            opacity: '0',
            transform: 'scale(0.7)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        'drop-in': {
          '0%': {
            opacity: '0',
            transform: 'scale(0)',
            animationTimingFunction: 'cubic-bezier(0.34, 1.61, 0.7, 1)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale(1)',
          },
        },
        'drop-out': {
          '0%': {
            opacity: '1',
            transform: 'scale(1)',
            animationTimingFunction: 'cubic-bezier(0.34, 1.61, 0.7, 1)',
          },
          '100%': {
            opacity: '0',
            transform: 'scale(0)',
          },
        },
        'fly-in': {
          '0%': {
            opacity: '0',
            transform: 'scale3d(0.3, 0.3, 0.3)',
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '40%': {
            transform: 'scale3d(0.9, 0.9, 0.9)',
          },
          '60%': {
            opacity: '1',
            transform: 'scale3d(1.03, 1.03, 1.03)',
          },
          '80%': {
            transform: 'scale3d(0.97, 0.97, 0.97)',
          },
          '100%': {
            opacity: '1',
            transform: 'scale3d(1, 1, 1)',
          },
        },
        'fly-in-up': {
          '0%': {
            opacity: '0',
            transform: 'translate3d(0, 1500px, 0)',
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '60%': {
            opacity: '1',
            transform: 'translate3d(0, -20px, 0)',
          },
          '75%': {
            transform: 'translate3d(0, 10px, 0)',
          },
          '90%': {
            transform: 'translate3d(0, -5px, 0)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0)',
          },
        },
        'fly-in-down': {
          '0%': {
            opacity: '0',
            transform: 'translate3d(0, -1500px, 0)',
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '60%': {
            opacity: '1',
            transform: 'translate3d(0, 25px, 0)',
          },
          '75%': {
            transform: 'translate3d(0, -10px, 0)',
          },
          '90%': {
            transform: 'translate3d(0, 5px, 0)',
          },
          '100%': {
            transform: 'none',
          },
        },
        'fly-in-left': {
          '0%': {
            opacity: '0',
            transform: 'translate3d(1500px, 0, 0)',
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '60%': {
            opacity: '1',
            transform: 'translate3d(-25px, 0, 0)',
          },
          '75%': {
            transform: 'translate3d(10px, 0, 0)',
          },
          '90%': {
            transform: 'translate3d(-5px, 0, 0)',
          },
          '100%': {
            transform: 'none',
          },
        },
        'fly-in-right': {
          '0%': {
            opacity: '0',
            transform: 'translate3d(-1500px, 0, 0)',
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '60%': {
            opacity: '1',
            transform: 'translate3d(25px, 0, 0)',
          },
          '75%': {
            transform: 'translate3d(-10px, 0, 0)',
          },
          '90%': {
            transform: 'translate3d(5px, 0, 0)',
          },
          '100%': {
            transform: 'none',
          },
        },
        'fly-out': {
          '0%': {
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            transform: 'scale3d(0.9, 0.9, 0.9)',
          },
          '50%, 55%': {
            opacity: '1',
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: '0',
            transform: 'scale3d(0.3, 0.3, 0.3)',
          },
        },
        'fly-out-up': {
          '0%': {
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            transform: 'translate3d(0, 10px, 0)',
          },
          '40%, 45%': {
            opacity: '1',
            transform: 'translate3d(0, -20px, 0)',
          },
          '100%': {
            opacity: '0',
            transform: 'translate3d(0, 2000px, 0)',
          },
        },
        'fly-out-down': {
          '0%': {
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            transform: 'translate3d(0, -10px, 0)',
          },
          '40%, 45%': {
            opacity: '1',
            transform: 'translate3d(0, 20px, 0)',
          },
          '100%': {
            opacity: '0',
            transform: 'translate3d(0, -2000px, 0)',
          },
        },
        'fly-out-left': {
          '0%': {
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            opacity: '1',
            transform: 'translate3d(-20px, 0, 0)',
          },
          '100%': {
            opacity: '0',
            transform: 'translate3d(2000px, 0, 0)',
          },
        },
        'fly-out-right': {
          '0%': {
            transitionTimingFunction: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
          },
          '20%': {
            opacity: '1',
            transform: 'translate3d(20px, 0, 0)',
          },
          '100%': {
            opacity: '0',
            transform: 'translate3d(-2000px, 0, 0)',
          },
        },
        'browse-in': {
          '0%': {
            transform: 'scale(0.8) translateZ(0px)',
            zIndex: '-1',
          },
          '10%': {
            transform: 'scale(0.8) translateZ(0px)',
            zIndex: '-1',
            opacity: '0.7',
          },
          '80%': {
            transform: 'scale(1.05) translateZ(0px)',
            zIndex: '999',
            opacity: '1',
          },
          '100%': {
            transform: 'scale(1) translateZ(0px)',
            zIndex: '999',
          },
        },
        'browse-out': {
          '0%': {
            transform: 'translateX(0%) rotateY(0deg) rotateX(0deg)',
            zIndex: '999',
          },
          '50%': {
            transform:
              'translateX(-105%) rotateY(35deg) rotateX(10deg) translateZ(-10px)',
            zIndex: '-1',
          },
          '80%': {
            opacity: '1',
          },
          '100%': {
            zIndex: '-1',
            opacity: '0',
            transform:
              'translateX(0%) rotateY(0deg) rotateX(0deg) translateZ(-10px)',
          },
        },
        'browse-out-left': {
          '0%': {
            transform: 'translateX(0%) rotateY(0deg) rotateX(0deg)',
            zIndex: '999',
          },
          '50%': {
            transform:
              'translateX(-105%) rotateY(35deg) rotateX(10deg) translateZ(-10px)',
            zIndex: '-1',
          },
          '80%': {
            opacity: '1',
          },
          '100%': {
            zIndex: '-1',
            opacity: '0',
            transform:
              'translateX(0%) rotateY(0deg) rotateX(0deg) translateZ(-10px)',
          },
        },
        'browse-out-right': {
          '0%': {
            transform: 'translateX(0%) rotateY(0deg) rotateX(0deg)',
            zIndex: '999',
          },
          '50%': {
            transform:
              'translateX(105%) rotateY(35deg) rotateX(10deg) translateZ(-10px)',
            zIndex: '1',
          },
          '80%': {
            opacity: '1',
          },
          '100%': {
            zIndex: '1',
            opacity: '0',
            transform:
              'translateX(0%) rotateY(0deg) rotateX(0deg) translateZ(-10px)',
          },
        },
        jiggle: {
          '0%': {
            transform: 'scale3d(1, 1, 1)',
          },
          '30%': {
            transform: 'scale3d(1.25, 0.75, 1)',
          },
          '40%': {
            transform: 'scale3d(0.75, 1.25, 1)',
          },
          '50%': {
            transform: 'scale3d(1.15, 0.85, 1)',
          },
          '65%': {
            transform: 'scale3d(0.95, 1.05, 1)',
          },
          '75%': {
            transform: 'scale3d(1.05, 0.95, 1)',
          },
          '100%': {
            transform: 'scale3d(1, 1, 1)',
          },
        },
        flash: {
          '0%, 50%, 100%': {
            opacity: '1',
          },
          '25%, 75%': {
            opacity: '0',
          },
        },
        shake: {
          '0%, 100%': {
            transform: 'translateX(0)',
          },
          '10%, 30%, 50%, 70%, 90%': {
            transform: 'translateX(-10px)',
          },
          '20%, 40%, 60%, 80%': {
            transform: 'translateX(10px)',
          },
        },
        glow: {
          '0%': {
            backgroundColor: '#fcfcfd',
          },
          '30%': {
            backgroundColor: '#fff6cd',
          },
          '100%': {
            backgroundColor: '#fcfcfd',
          },
        },

        wiggle: {
          '5%': {
            transform: 'rotate(-5deg)',
          },
          '20%': {
            transform: 'rotate(5deg)',
          },
          '40%': {
            transform: 'rotate(-5deg)',
          },
          '80%': {
            transform: 'rotate(5deg)',
          },
        },
        flip: {
          '0%': {
            transform: 'rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg)',
          },
          '100%': {
            transform: 'rotateY(0deg)',
          },
        },
        'flip-up': {
          '0%': {
            transform: 'translate3d(0, 100%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-down': {
          '0%': {
            transform: 'translate3d(0, -100%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-left': {
          '0%': {
            transform: 'translate3d(-100%, 0%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-right': {
          '0%': {
            transform: 'translate3d(100%, 0%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-top-left': {
          '0%': {
            transform: 'translate3d(-100%, -100%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-top-right': {
          '0%': {
            transform: 'translate3d(100%, -100%, 0)  rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },

        'flip-bottom-left': {
          '0%': {
            transform: 'translate3d(-100%, 100%, 0) rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        'flip-bottom-right': {
          '0%': {
            transform: 'translate3d(100%, 100%, 0)  rotateY(-180deg)',
          },
          '50%': {
            transform: 'rotateY(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateY(0deg)',
          },
        },
        flipy: {
          '0%': {
            transform: 'rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg)',
          },
          '100%': {
            transform: 'rotateX(0deg)',
          },
        },
        'flipy-up': {
          '0%': {
            transform: 'translate3d(0, 100%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-down': {
          '0%': {
            transform: 'translate3d(0, -100%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg)',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-left': {
          '0%': {
            transform: 'translate3d(-100%, 0%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-right': {
          '0%': {
            transform: 'translate3d(100%, 0%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-top-left': {
          '0%': {
            transform: 'translate3d(-100%, -100%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-top-right': {
          '0%': {
            transform: 'translate3d(100%, -100%, 0)  rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-bottom-left': {
          '0%': {
            transform: 'translate3d(-100%, 100%, 0) rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'flipy-bottom-right': {
          '0%': {
            transform: 'translate3d(100%, 100%, 0)  rotateX(-180deg)',
          },
          '50%': {
            transform: 'rotateX(-90deg) ',
          },
          '100%': {
            transform: 'translate3d(0, 0, 0) rotateX(0deg)',
          },
        },
        'zoom-in': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
          },
        },
        'zoom-in-up': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(0, 100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-down': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(0, -100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-right': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(-100%, 0, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-left': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(100%, 0, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-bottom-right': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(100%, 100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-top-right': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(100%, -100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-top-left': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(-100%, -100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-in-bottom-left': {
          '0%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(-100%, 100%, 0)',
          },
          '80%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
        },
        'zoom-out': {
          '0%': {
            opacity: 1,
          },
          '15%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3)',
          },
        },
        'zoom-out-down': {
          '0%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
          '15%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(0, 100%, 0)',
          },
        },
        'zoom-out-up': {
          '0%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
          '15%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(0, -100%, 0)',
          },
        },
        'zoom-out-left': {
          '0%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
          15: {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(-100%, 0%, 0)',
          },
        },
        'zoom-out-right': {
          '0%': {
            opacity: 1,
            transform: 'translate3d(0, 0%, 0)',
          },
          '15%': {
            opacity: 0.8,
            transform: 'scale3d(1.1, 1.1, 1.1)',
          },
          '100%': {
            opacity: 0,
            transform: 'scale3d(0.3, 0.3, 0.3) translate3d(100%, 0%, 0)',
          },
        },
      },
      colors: {
        // you can either spread `colors` to apply all the colors
        ...colors,
        // or add them one by one and name whatever you want
        emerald: colors.emerald,
      },
    },
    screens: {
      sm: '576px',
      'sm-max': { max: '576px' },
      md: '768px',
      'md-max': { max: '768px' },
      lg: '992px',
      'lg-max': { max: '992px' },
      xl: '1200px',
      'xl-max': { max: '1200px' },
      '2xl': '1320px',
      '2xl-max': { max: '1320px' },
      '3xl': '1600px',
      '3xl-max': { max: '1600px' },
      '4xl': '1850px',
      '4xl-max': { max: '1850px' },
    },
    colors: () => ({
      white: '#ffffff',
      lightPrimary: '#F4F7FE',
      blueSecondary: '#4318FF',
      brandLinear: '#868CFF',
      gray: {
        50: '#f8f9fa',
        100: '#edf2f7',
        200: '#e9ecef',
        300: '#cbd5e0',
        400: '#a0aec0',
        500: '#adb5bd',
        600: '#a3aed0',
        700: '#707eae',
        800: '#252f40',
        900: '#1b2559',
      },
      navy: {
        50: '#d0dcfb',
        100: '#aac0fe',
        200: '#a3b9f8',
        300: '#728fea',
        400: '#3652ba',
        500: '#1b3bbb',
        600: '#24388a',
        700: '#1B254B',
        800: '#111c44',
        900: '#0b1437',
      },
      red: {
        50: '#ee5d501a',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#f53939',
        600: '#ea0606',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d',
      },
      orange: {
        50: '#fff7ed',
        100: '#ffedd5',
        200: '#fed7aa',
        300: '#fdba74',
        400: '#fb923c',
        500: '#f97316',
        600: '#ea580c',
        700: '#c2410c',
        800: '#9a3412',
        900: '#7c2d12',
      },
      amber: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f',
      },
      yellow: {
        50: '#fefce8',
        100: '#fef9c3',
        200: '#fef08a',
        300: '#fde047',
        400: '#fbcf33',
        500: '#eab308',
        600: '#ca8a04',
        700: '#a16207',
        800: '#854d0e',
        900: '#713f12',
      },
      lime: {
        50: '#f7fee7',
        100: '#ecfccb',
        200: '#d9f99d',
        300: '#bef264',
        400: '#98ec2d',
        500: '#82d616',
        600: '#65a30d',
        700: '#4d7c0f',
        800: '#3f6212',
        900: '#365314',
      },
      green: {
        50: '#05cd991a',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e',
        600: '#17ad37',
        700: '#15803d',
        800: '#166534',
        900: '#14532d',
      },
      teal: {
        50: '#f0fdfa',
        100: '#ccfbf1',
        200: '#99f6e4',
        300: '#5eead4',
        400: '#2dd4bf',
        500: '#14b8a6',
        600: '#0d9488',
        700: '#0f766e',
        800: '#115e59',
        900: '#134e4a',
      },
      cyan: {
        50: '#ecfeff',
        100: '#cffafe',
        200: '#a5f3fc',
        300: '#67e8f9',
        400: '#21d4fd',
        500: '#17c1e8',
        600: '#0891b2',
        700: '#0e7490',
        800: '#155e75',
        900: '#164e63',
      },
      blue: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2152ff',
        700: '#1d4ed8',
        800: '#344e86',
        900: '#00007d',
      },
      indigo: {
        50: '#eef2ff',
        100: '#e0e7ff',
        200: '#c7d2fe',
        300: '#a5b4fc',
        400: '#818cf8',
        500: '#6366f1',
        600: '#4f46e5',
        700: '#4338ca',
        800: '#3730a3',
        900: '#312e81',
      },
      purple: {
        50: '#faf5ff',
        100: '#f3e8ff',
        200: '#e9d5ff',
        300: '#d8b4fe',
        400: '#c084fc',
        500: '#a855f7',
        600: '#9333ea',
        700: '#7928ca',
        800: '#6b21a8',
        900: '#581c87',
      },
      pink: {
        50: '#fdf2f8',
        100: '#fce7f3',
        200: '#fbcfe8',
        300: '#f9a8d4',
        400: '#f472b6',
        500: '#ff0080',
        600: '#db2777',
        700: '#be185d',
        800: '#9d174d',
        900: '#831843',
      },
      brand: {
        50: '#E9E3FF',
        100: '#C0B8FE',
        200: '#A195FD',
        300: '#8171FC',
        400: '#7551FF',
        500: '#422AFB',
        600: '#3311DB',
        700: '#2111A5',
        800: '#190793',
        900: '#11047A',
      },
      shadow: {
        500: 'rgba(112, 144, 176, 0.08)',
      },
    }),
  },
  plugins: [require('tailwindcss-rtl')],
};
