import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgrPlugin from 'vite-plugin-svgr';
import fixReactVirtualized from 'esbuild-plugin-react-virtualized';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), svgrPlugin({ icon: true })],
  build: {
    outDir: 'build',
    commonjsOptions: {
      include: [/node_modules/],
    },
  },
  optimizeDeps: {
    include: ['@workspace/ckeditor5-custom-build'],
    esbuildOptions: {
      plugins: [fixReactVirtualized],
    },
  },
  server: {
    port: 3000,
    open: true,
  },
});
