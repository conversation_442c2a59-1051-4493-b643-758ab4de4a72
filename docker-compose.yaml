version: "3.4"

services:
  nginx:
    image: nginx:1.17.2-alpine
    restart: always
    volumes: 
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./build:/etc/build
    ports:
      - 80:80

    logging:
      options:
        max-size: "10m"
        max-file: "3"
        
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 4512M
        reservations:
          cpus: "2"
          memory: 4512M