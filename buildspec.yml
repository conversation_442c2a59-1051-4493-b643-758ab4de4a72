version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 16
    commands:
      - rm -rf node_modules
      - yum install npm -y # Install NPM
      - npm install yarn -g #Install YARN
  pre_build:
    commands:
      - yarn install # Install project dependencies
  build:
    commands:
      - yarn build # Execute your build script or command
post_build:
  commands:
    - mkdir -p $CODEBUILD_SRC_DIR/artifacts/dist # Create the artifacts/dist directory
    - rsync -av $CODEBUILD_SRC_DIR/build/dist/. $CODEBUILD_SRC_DIR/artifacts/dist/ # Copy the contents of the dist directory to artifacts/dist

artifacts:
  files:
    - '**/*'

timeout: 60 # Set the timeout for the entire build process (in minutes)
