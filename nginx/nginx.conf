http {

  include mime.types;

  set_real_ip_from        0.0.0.0/0;
  real_ip_recursive       on;
  real_ip_header          X-Forward-For;
  limit_req_zone          $binary_remote_addr zone=mylimit:10m rate=10r/s;

  server {
    # Listen HTTP
    listen 80;
    server_name localhost;
    client_max_body_size 2M;
    location /  {
        root   /etc/build;
        index  index.html index.htm;
        try_files $uri /index.html;                                                         
    }
}
}

events {}
