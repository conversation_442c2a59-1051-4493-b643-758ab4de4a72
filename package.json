{"name": "indeed-jobs-fe", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "5.0.1", "@ckeditor/ckeditor5-react": "^8.0.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/styled": "11.11.0", "@fortawesome/fontawesome-svg-core": "6.2.1", "@fortawesome/free-regular-svg-icons": "6.2.1", "@fortawesome/free-solid-svg-icons": "6.2.1", "@fortawesome/react-fontawesome": "0.2.0", "@headlessui/react": "1.7.15", "@heroicons/react": "2.0.18", "@highcharts/map-collection": "^2.3.0", "@material-tailwind/react": "2.0.4", "@mui/material": "5.13.6", "@nylas/react": "^1.1.0-canary.18", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@reduxjs/toolkit": "1.9.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supercharge/promise-pool": "^3.2.0", "@tanstack/query-sync-storage-persister": "4.33.0", "@tanstack/react-query": "4.33.0", "@tanstack/react-query-persist-client": "4.33.0", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "@typescript-eslint/eslint-plugin": "6.20.0", "@typescript-eslint/parser": "6.20.0", "antd": "5.12.1", "apexcharts": "3.41.0", "autoprefixer": "10.4.16", "axios": "1.4.0", "base-64": "1.0.0", "basic-auth": "2.0.1", "bootstrap": "5.2.3", "chart.js": "^2.9.4", "ckeditor5": "^43.2.0", "ckeditor5-premium-features": "^43.2.0", "cors": "2.8.5", "country-list-json": "^1.1.0", "country-region-data": "2.7.0", "country-state-city": "3.1.4", "date-fns": "^3.6.0", "dayjs": "1.11.9", "dotenv": "16.3.1", "env-cmd": "10.1.0", "esbuild-plugin-react-virtualized": "^1.0.4", "express": "4.18.2", "flag-icons": "^7.2.3", "font-awesome": "4.7.0", "framer-motion": "6.2.4", "highcharts": "^11.4.7", "highcharts-map": "^0.1.7", "highcharts-react-official": "^3.2.1", "html-react-parser": "4.0.0", "html-to-image": "^1.11.11", "https": "1.0.0", "jspdf": "^2.5.2", "lodash": "4.17.21", "lucide-react": "^0.454.0", "moment": "2.29.4", "prop-types": "15.8.1", "rc-tween-one": "3.0.6", "react": "^18.0.0", "react-apexcharts": "1.4.0", "react-autocomplete": "1.8.1", "react-autosuggest": "10.1.0", "react-beautiful-dnd": "13.1.1", "react-calendar": "4.2.1", "react-chartjs-2": "^2.11.1", "react-cookie": "^7.2.2", "react-copy-to-clipboard": "5.1.0", "react-country-state-city": "1.0.4", "react-csv": "2.2.2", "react-date-range": "^2.0.1", "react-dom": "^18.0.0", "react-grid-layout": "1.3.4", "react-hook-form": "7.45.2", "react-icons": "4.9.0", "react-infinite-scroll-component": "6.1.0", "react-intersection-observer": "^9.8.1", "react-markdown": "8.0.7", "react-redux": "8.0.5", "react-router-dom": "6.4.3", "react-scripts": "5.0.1", "react-select": "5.7.3", "react-table": "7.8.0", "react-virtualized": "^9.22.5", "recharts": "^2.1.8", "redux-saga": "1.2.1", "saas": "1.0.0", "sass": "1.56.1", "styled-components": "^5.3.3", "sweetalert2": "11.7.3", "swiper": "11.0.5", "tailwindcss": "3.4.17", "tailwindcss-rtl": "0.9.0", "uuid": "^11.1.0", "visitorapi": "2.0.0", "web-vitals": "2.1.4", "xlsx": "^0.18.5", "yarn": "1.22.19", "zustand": "4.4.1"}, "devDependencies": {"@vitejs/plugin-react": "4.0.0", "eslint": "7.32.0 || ^8.2.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.3.0", "husky": "8.0.0", "lerna": "7.1.5", "lint-staged": "15.2.0", "prettier": "3.2.4", "vite": "4.4.9", "vite-plugin-svgr": "3.2.0"}, "scripts": {"start": "vite --mode dev", "build": "vite build", "build:staging": "vite build --mode staging", "build:dev": "vite build --mode dev", "preview": "vite preview", "prepare": "husky install", "lint": "eslint --fix", "pretty": "prettier --write ."}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.jsx": ["eslint --fix", "prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}