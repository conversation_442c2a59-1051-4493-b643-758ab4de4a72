***Check before merging:***
- [ ] Kiểm tra xem console log dư thừa (log vô nghĩa) vd: 123123, gege, hehe,.. (Chú ý: Trên dev và Staging vẫn có thể có log nhưng lúc lên Prod thì tuyệt đối không log trên Prod)
- [ ] Xóa (hoặc Comment trong trường hợp SẼ dùng hoặc rollback về sau )  toàn bộ biến và function không dùng tới
- [ ] Format của code: revert toàn bộ những chỗ bị change format (Trong trường hợp đẹp nhất khuyến khích mn đều xài Prettier extension v10.4.0)
- [ ] Merging dev/ staging (latest) vào nhánh đang làm xem có conflict không? nếu có thì resolve và tiếp tục đè 1 commit.
- [ ] Kiểm tra xem có magic constants nào không? (key, password,...)
- [ ] Kiểm tra xem trong lúc develop mình có comment lại function nào khác (block function mình đang làm để tiện debug) không? nếu có thì uncomment lại.
- [ ] Comment chỗ nào đọc khó hiểu (hoặc nghĩ người khác đọc vào sẽ khó hiểu)