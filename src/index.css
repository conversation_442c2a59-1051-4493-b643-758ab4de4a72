@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');
@tailwind base;

@tailwind components;

@tailwind utilities;

@font-face {
  font-family: 'Calibri';
  src:
    local('Calibri'),
    url(./assets/fonts/Calibri/Calibri.ttf) format('truetype');
}

@font-face {
  font-family: 'Calibrib';
  src:
    local('Calibrib'),
    url(./assets/fonts/Calibri/calibrib.ttf) format('truetype');
}

@font-face {
  font-family: 'Calibril';
  src:
    local('Calibril'),
    url(./assets/fonts/Calibri/calibril.ttf) format('truetype');
}

*:not(
    .editor-container mark,
    .ck-content strong,
    .ck-content p,
    .ck-content span,
    .ck-content li,
    .ck-content u,
    .ck-content a
  ) {
  font-family:
    'Montserrat',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code,
.ant-tag {
  font-family: 'Montserrat', source-code-pro, Menlo, Monaco, Consolas,
    'Courier New', monospace;
}

@font-face {
  font-family: 'PoppinsLight';
  src:
    local('PoppinsLight'),
    url(./assets/fonts/Poppins/Poppins-Light.ttf) format('truetype');
}

@font-face {
  font-family: 'PoppinsMedium';
  src:
    local('PoppinsMedium'),
    url(./assets/fonts/Poppins/Poppins-Medium.ttf) format('truetype');
}

@font-face {
  font-family: 'PoppinsBold';
  src:
    local('PoppinsBold'),
    url(./assets/fonts/Poppins/Poppins-Bold.ttf) format('truetype');
}

@font-face {
  font-family: 'PoppinsRegular';
  src:
    local('PoppinsRegular'),
    url(./assets/fonts/Poppins/Poppins-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'PoppinsSemiBold';
  src:
    local('PoppinsSemiBold'),
    url(./assets/fonts/Poppins/Poppins-SemiBold.ttf) format('truetype');
}

@layer base {
  html {
    font-family: 'Montserrat', 'DM Sans', sans-serif !important;
    font-feature-settings: 'kern' !important;
    -webkit-font-smoothing: antialiased;
    letter-spacing: -0.5px;
  }
}

.welcomeBanner > .ant-image {
  display: block !important;
  margin: auto !important;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #aaaaaa;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c2c1c1;
}

.custom-input {
  position: relative;
}

.input-text {
  position: absolute;
  pointer-events: none;
  display: none;
  border-radius: 3px;
  padding: 3px;
  top: 2px;
  left: 1%;
  letter-spacing: 0.2px;
  z-index: 99;
}

.input-text-select {
  position: absolute;
  pointer-events: none;
  background-color: #0079c2;
  color: white;
  border-radius: 3px;
  padding: 3px;
  top: 7px;
  left: 1%;
  letter-spacing: 0.2px;
  z-index: 99;
}

.input-text-select-company-contact {
  position: absolute;
  pointer-events: none;
  background-color: #0079c2;
  color: white;
  border-radius: 3px;
  padding: 3px;
  top: 7px;
  left: 1%;
  letter-spacing: 0.2px;
  z-index: 99;
}

.input-text-contact {
  position: absolute;
  pointer-events: none;
  background-color: #0079c2;
  color: white;
  border-radius: 3px;
  padding: 3px;
  top: 7px;
  left: 1%;
  letter-spacing: 0.2px;
  z-index: 99;
}

.input-text-select-add {
  position: absolute;
  pointer-events: none;
  background-color: #adadad;
  color: black;
  border-radius: 3px;
  padding: 3px;
  top: 7px;
  left: 1%;
  letter-spacing: 0.2px;
  z-index: 99;
}

.hidden-input {
  position: relative;
  z-index: 1;
  opacity: 0;
}

mark {
  background-color: yellow;
}

.bg-primary {
  background-color: #0891b2;
}

.border-primary {
  border-color: #0891b2;
}

.border-t-primary {
  --tw-border-opacity: 1;
  border-top-color: rgb(6 105 187 / var(--tw-border-opacity)) /* #0891b2 */;
}

.border-l-primary {
  --tw-border-opacity: 1;
  border-left-color: rgb(6 105 187 / var(--tw-border-opacity)) /* #0891b2 */;
}

.border-r-primary {
  --tw-border-opacity: 1;
  border-right-color: rgb(6 105 187 / var(--tw-border-opacity)) /* #0891b2 */;
}

.border-b-primary {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(6 105 187 / var(--tw-border-opacity)) /* #0891b2 */;
}

.text-primary {
  color: #0891b2;
}

.text-secondary {
  color: #5e768d;
}

.skills-container li {
  background-color: yellow;
  margin-top: 5px;
  width: fit-content;
}

/* Redesign search and syncs table */
.search-table-new-design-container
  .ant-table-wrapper
  .ant-table-thead
  tr
  th::before {
  display: none !important;
}
.search-table-new-design-container .ant-table-cell {
  border-left: none !important;
  border-right: none !important;
}
.search-table-new-design-container .ant-table-wrapper .ant-table-thead {
  background-color: white !important;
}

.search-table-new-design-container th.ant-table-cell {
  color: black !important;
}
.search-table-new-design-container .ant-table-thead .ant-table-cell {
  background-color: white !important;
}

/* table in expand component */
/* .expand-table-new-design-container .ant-table-thead .ant-table-cell {
  background-color: #f7f7f7 !important;
} */
.expand-table-new-design-container .ant-table-cell {
  background-color: #f5f5f5 !important;
}

.search-table-new-design-container .ant-table-cell {
  font-family: 'Montserrat';
  position: static;
  float: none;
  display: table-cell;
}
.Montserrat {
  font-family: 'Montserrat' !important;
}
.customize-search-container
  span.ant-input-affix-wrapper.ant-input-affix-wrapper-lg,
.customize-search-container
  button.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button {
  border-start-start-radius: 0px !important;
  border-end-start-radius: 0px !important;
  border-start-end-radius: 0px !important;
  border-end-end-radius: 0px !important;
  font-family: 'Montserrat' !important;
}

.customize-search-container
  button.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button {
  background-color: #fff !important;
  /* color: #b6b4b4 !important; */
  border: 1px solid #d9d9d9;
}

.ant-input::-webkit-input-placeholder {
  font-family: 'Montserrat', Arial, Helvetica, sans-serif;
}

.ant-input:-ms-input-placeholder {
  font-family: 'Montserrat', Arial, Helvetica, sans-serif;
}

.ant-input:-moz-placeholder {
  font-family: 'Montserrat', Arial, Helvetica, sans-serif;
}

.ant-input::-moz-placeholder {
  font-family: 'Montserrat', Arial, Helvetica, sans-serif;
}

.customize-search-container
  button.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button:hover {
  color: #40464d !important;
  border-color: #a3acb5 !important;
  background-color: #f9f9fa !important;
}

.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

.animated {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

.tag {
  background-color: #f9f9fa;
  border: 1px solid #d9dee2;
  color: #d9dee2;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 6px;
  margin: 2px;
  display: inline-block;
  border-radius: 2px;
  /* text-transform: uppercase; */
}

.tag-cyan {
  background-color: rgba(230, 255, 251, 1);
  border-color: rgb(8, 151, 156, 1);
  color: rgb(8, 151, 156, 1);
}

.tag-blue {
  background-color: rgba(74, 137, 220, 0.25);
  border-color: #4a89dc;
  color: #4a89dc;
}

.tag-orange {
  background-color: rgba(255, 131, 13, 0.25);
  border-color: #ff830d;
  color: #ff830d;
}

.tag-purple {
  background-color: rgba(155, 143, 239, 0.25);
  border-color: #9b8fef;
  color: #9b8fef;
}

.tag-normal {
  background-color: rgba(216, 212, 238, 0.25);
  border-color: #bbbbbb;
  color: #000000;
}

.tag-company {
  background-color: rgba(216, 212, 238, 0.25);
  font-weight: 400;
  color: #000000;
  border-radius: 3px;
  padding: 3px;
  letter-spacing: 0.2px;
  font-size: 14px;
}

.tag-company-selected {
  /* border-color: #bbbbbb; */
  font-weight: 400;
  background-color: rgb(8 145 178);
  color: white;
  border-radius: 3px;
  padding: 3px 10px;
  letter-spacing: 0.2px;
  font-size: 14px;
}

.pe-preserve-ancestor {
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
}
.customize-tooltip-suggestion .ant-popconfirm-buttons,
.customize-tooltip-widget .ant-popconfirm-buttons {
  display: none !important;
}
.customize-tooltip-suggestion .ant-popover-inner,
.customize-tooltip-widget .ant-popover-inner {
  padding: 0px !important;
}
.customize-tooltip-suggestion .ant-popconfirm-title,
.customize-tooltip-widget .ant-popconfirm-title {
  font-family: 'Montserrat';
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 3px 3px 0 0;
}

.warning .ant-popconfirm-title {
  background-color: #ff9966 !important;
}
/* .customize-tooltip-widget .ant-popconfirm-message-text{
  height: 7rem !important;
} */
.customize-tooltip-suggestion .ant-popconfirm-description,
.customize-tooltip-widget .ant-popconfirm-description {
  padding: 12px 16px !important;
  font-family: 'Montserrat';
}

.customize-tooltip-widget .ant-popconfirm-description {
  width: 260px;
}
.customize-tooltip-suggestion .ant-popconfirm-description {
  min-width: min-content !important;
}

.filter-by-org-container .ant-select-selector {
  border-radius: 0 !important;
}
.ant-select-item-option-content {
  font-family: 'Montserrat' !important;
}

.progress2 {
  padding: 3px;
  border-radius: 30px;
  background: rgb(244 244 244 / var(--tw-bg-opacity));
  box-shadow: 5px 8px 20px 0px #ccc;
  border: 2px solid #000;
}

.progress-bar2 {
  height: 18px;
  border-radius: 30px;
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.05)
  );
  transition: 0.4s linear;
  transition-property: width, background-color;
}

.progress-moved .progress-bar2 {
  width: var(--target-width, 100%);
  background-color: #0891b2;
  animation: progressAnimation 3s;
}

@keyframes progressAnimation {
  0% {
    width: 5%;
    background-color: #7e96aa;
  }
  100% {
    width: 96%;
    background-color: #0891b2;
  }
}

.process-value-cus {
  background-color: #000;
  font-size: 20px;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  transform: translateX(0);
  animation: moveLeft 3s forwards;
  position: absolute;
  width: 70px;
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% 75%,
    59% 75%,
    52% 100%,
    44% 75%,
    0% 75%
  );
  margin-top: -50px;
  height: 50px;
  border-radius: 5px;
  box-shadow: 5px 8px 20px 0px red;
}

@keyframes moveLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(44.5vw);
  }
}

.process-body-cus {
  position: relative;
  width: 50%;
  margin: 0 auto;
  margin-top: 46vh;
}

.process-modal {
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.45);
  position: fixed;
  z-index: 1000;
}

.progress-bar2-logo {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  margin-top: -70px;
  margin-left: 45%;
}

.progress-bar2-logo img {
  width: 100%;
}

@keyframes ripple {
  from {
    opacity: 1;
    transform: scale3d(0.75, 0.75, 1);
  }

  to {
    opacity: 0;
    transform: scale3d(2, 2, 1);
  }
}

.step-custom {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 80px;
  width: 80px;
  border: 3px solid #0460cc;
  border-radius: 100%;
  background: #ffffff;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.25);
  position: absolute;
  margin-top: -55px;
  margin-left: 45%;
}

.step-custom::after {
  content: '';
  opacity: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  background: #0460cc;
  border-radius: 100%;
  animation-name: ripple;
  animation-duration: 1s;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(0.65, 0, 0.34, 1);
  z-index: -1;
}

.step-custom::after {
  animation-delay: 0s;
}

.step-custom span {
  position: relative;
  font-size: 72px;
  top: 5px;
  left: -5px;
}

.cusomized-tabs .ant-tabs-tab-btn {
  font-family: 'Montserrat' !important;
  font-weight: 500;
}

.process-bg {
  background-color: #fff;
  height: 100vh;
  width: 150vw;
  margin-left: -19.5vw;
  top: 0;
  position: fixed;
  z-index: 1000 !important;
}

.customize-input-container {
  border-radius: 0px !important;
  font-family: 'Montserrat' !important;
  padding-top: 7px !important;
  padding-bottom: 7px !important;
}
.ant-input,
.ant-dropdown-menu-title-content span {
  font-family: 'Montserrat' !important;
}

.contact-modal-container .ant-modal-content {
  background-color: #f4f4f4;
}

.email-finder-container.ant-collapse-header {
  background-color: #fff !important;
  border-radius: 0 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

.email-finder-container.ant-collapse {
  border: none !important;
}

.email-finder-container.ant-collapse-header .ant-collapse-header-text span {
  font-weight: 500 !important;
}

.width-inherit {
  width: inherit;
  text-align-last: center;
}

.create-sequence-detail .sequence-container {
  place-content: center;
}

.sequence-detail .sequence-container {
  place-content: center;
}

.sequence-detail .sequence-action-container {
  justify-content: center;
}

.linkedin-step-container .ant-collapse-header {
  background-color: #fff !important;
  border-radius: 10px !important;
}

.linkedin-step-container .ant-collapse-content-active {
  /* border-top: none!important; */
  border-radius: 10px !important;
}

.linkedin-step-container .ant-collapse-header-text {
  font-weight: 600;
  font-size: medium;
}

.linkedin-type-choosen,
.linkedin-type-choosen.ant-collapse-item {
  border-color: #0891b2;
  /* border-bottom-color: #0891b2 !important; */
  border-bottom: 1px solid #0891b2 !important;
  transition: 0.5s;
}

.linkedin-type-choosen .ant-collapse-header-text,
.linkedin-type-choosen .send-message-text {
  color: #0891b2;
  transition: 0.5s;
}

.group-name-text {
  color: #1677ff;
  font-weight: 600;
  /* font-size: 1rem; */
}

.group-name-type-text {
  color: #088395;
  font-weight: 600;
  /* font-size: 1rem; */
}
.activity-logs-table-container .ant-table-row-level-1 {
  background-color: #f5f7f8 !important;
}

.activity-logs-table-container .ant-table-row-level-2 {
  background-color: #f5f7f8 !important;
}

._src_components_Container_Container_module__hover {
  background-color: #ebebeb;
}

.my-lead-sequence-container .sequence-component-container {
  overflow-x: hidden;
}

.linkedin-setting-form .ant-form-item {
  margin-bottom: 0;
}

.actions-dropdown-btn button {
  color: white !important;
  font-weight: 500;
}

.customTable .ant-table-cell-fix-left-last {
  border-right: 1px solid #ccc !important;
}

.ant-btn-primary {
  @apply bg-white !text-cyan-600 !border !border-cyan-600 focus:ring-4 focus:outline-none focus:ring-cyan-300 font-semibold hover:bg-white hover:text-cyan-300;
}
.ant-btn-primary:hover {
  background-color: #fff !important;
  color: #67e8f9 !important;
  border-color: #67e8f9 !important;
}

button[data-cke-tooltip-text='Pilot AI'],
button[data-cke-tooltip-text='AI Tags'],
.customized-tool-bar-btn {
  @apply !border !font-semibold;
}

/* Pilot AI toolbar button restyle */
button[data-cke-tooltip-text='Pilot AI'] {
  @apply !border-purple-400 !text-purple-800;
}

button[data-cke-tooltip-text='Pilot AI'] svg {
  @apply !text-purple-400;
}

/* AI Tags toolbar button restyle */
button[data-cke-tooltip-text='AI Tags'] {
  @apply !border-red-400 !text-red-600;
}

button[data-cke-tooltip-text='AI Tags'] svg {
  @apply !text-red-400;
}

/* Rewrite selected text By Zileo AI. toolbar button restyle */
button[data-cke-tooltip-text='Rewrite selected text By Zileo AI.'] {
  @apply !border-purple-400 !text-purple-800;
}

button[data-cke-tooltip-text='Rewrite selected text By Zileo AI.'] svg {
  @apply !text-purple-400;
}

button[data-cke-tooltip-text='Analyze email'] {
  @apply !border-purple-400 !text-purple-800;
}

button[data-cke-tooltip-text='Analyze email'] svg {
  @apply !text-purple-400;
}

/* Merge Tags toolbar button restyle */
button[data-cke-tooltip-text='Insert merge field'],
button[data-cke-tooltip-text='Merge fields preview'] {
  @apply !border-[#2775FC];
}

button[data-cke-tooltip-text='Insert merge field'] svg,
button[data-cke-tooltip-text='Merge fields preview'] svg {
  @apply !text-[#2775FC];
}

.ant-steps .ant-steps-item-process .ant-steps-item-icon {
  @apply text-white bg-cyan-600 border-none;
}

.start-from-sratch-container .sequence-component-container {
  overflow-x: hidden;
}

.ck-table-resized,
.ck table tr td,
.ck table tr th {
  border: none !important;
}

.sequence-template-container .sequence-component-container {
  @apply !max-h-[40rem];
}

.start-from-sratch-container .sequence-component-container {
  @apply !max-h-[43rem];
}

.create-sequence-from-vacancy-container .sequence-component-container {
  @apply !max-h-[43rem];
}
.sequence-background-container {
  position: relative;
  min-height: 45rem;
}

.sequence-background-container .ant-modal-title,
.sequence-template-container .ant-modal-title {
  /* background-color: #f1f5f8; */
}

.sequence-background-container .ant-modal-content,
.sequence-template-container .ant-modal-content {
  /* background-color: #f1f5f8; */
}

.preview-template .ant-drawer-body {
  padding: 0 !important;
  /* overflow: hidden; */
}
/* 
.preview-template .ant-drawer-content-wrapper,
.preview-template .ant-drawer-content,
.preview-template .ant-drawer-wrapper-body {
  border-radius: 1rem !important;
} */

.preview-template .ant-drawer-left > .ant-drawer-content-wrapper {
  left: 1rem !important;
}

/* .preview-template .ant-drawer .ant-drawer-content {
  background-color: #f1f5f8;
} */

.preview-template .ant-drawer-content-wrapper {
  box-shadow: none;
}

.sequence-component-container .ant-select {
  max-height: 10rem;
  overflow-y: scroll;
}

/* CKEditor restyle */
.ck-balloon-panel {
  z-index: 999 !important;
}

.ck-content {
  line-height: 1.6 !important;
  word-break: break-word;
  min-height: 14rem;
  /* max-height: 24rem; */
  padding-top: 1rem;
}

.editor-container_classic-editor .editor-container__editor {
  min-width: 795px;
  max-width: 100%;
}

.ck-content h3.category {
  /* font-family: 'Oswald'; */
  font-size: 20px;
  font-weight: bold;
  color: #555;
  letter-spacing: 10px;
  margin: 0;
  padding: 0;
}

.ck-content h2.document-title {
  /* font-family: 'Oswald'; */
  font-size: 50px;
  font-weight: bold;
  margin: 0;
  padding: 0;
  border: 0;
}

.ck-content h3.document-subtitle {
  /* font-family: 'Oswald'; */
  font-size: 20px;
  color: #555;
  margin: 0 0 1em;
  font-weight: bold;
  padding: 0;
}

.ck-content p.info-box {
  --background-size: 30px;
  --background-color: #e91e63;
  padding: 1.2em 2em;
  border: 1px solid var(--background-color);
  background: linear-gradient(
      135deg,
      var(--background-color) 0%,
      var(--background-color) var(--background-size),
      transparent var(--background-size)
    ),
    linear-gradient(
      135deg,
      transparent calc(100% - var(--background-size)),
      var(--background-color) calc(100% - var(--background-size)),
      var(--background-color)
    );
  border-radius: 10px;
  /* margin: 1.5em 2em; */
  box-shadow: 5px 5px 0 #ffe6ef;
}

.ck-content blockquote.side-quote {
  /* font-family: 'Oswald'; */
  font-style: normal;
  float: right;
  width: 35%;
  position: relative;
  border: 0;
  overflow: visible;
  z-index: 1;
  margin-left: 1em;
}

.ck-content blockquote.side-quote::before {
  content: '“';
  position: absolute;
  top: -37px;
  left: -10px;
  display: block;
  font-size: 200px;
  color: #e7e7e7;
  z-index: -1;
  line-height: 1;
}

.ck-content blockquote.side-quote p {
  font-size: 2em;
  line-height: 1;
}

.ck-content blockquote.side-quote p:last-child:not(:first-child) {
  font-size: 1.3em;
  text-align: right;
  color: #555;
}

.ck-content span.marker {
  background: yellow;
}

.ck-content span.spoiler {
  background: #000;
  color: #000;
}

.ck-content span.spoiler:hover {
  background: #000;
  color: #fff;
}

.ck-content pre.fancy-code {
  border: 0;
  margin-left: 2em;
  margin-right: 2em;
  border-radius: 10px;
}

.ck-content pre.fancy-code::before {
  content: '';
  display: block;
  height: 13px;
  margin-bottom: 8px;
  background-repeat: no-repeat;
}

.ck-content pre.fancy-code-dark {
  background: #272822;
  color: #fff;
  box-shadow: 5px 5px 0 #0000001f;
}

.ck-content pre.fancy-code-bright {
  background: #dddfe0;
  color: #000;
  box-shadow: 5px 5px 0 #b3b3b3;
}

/* .start-from-sratch-container .ant-drawer-content {
  background-color: #f1f5f8;
} */

/* remove sticky pannel on CKEditor */

.ck .ck-sticky-panel .ck-sticky-panel__content_sticky {
  position: static !important;
}

/* Sequence detail restyle */
.ant-tabs .ant-tabs-content,
.sequence-detail-page .ant-tabs-content {
  position: unset !important;
}

.custom-tab .ant-tabs-tab-btn {
  width: 100px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
}

.custom-tab .ant-tabs {
  width: 95% !important;
}

.sequence-container .ant-dropdown {
  max-height: 10rem !important;
  overflow-y: auto;
}

.more-index {
  z-index: 1700 !important;
}

.ant-mentions-measure,
.ant-mentions-measure span {
  z-index: 9999 !important;
}

.ant-mentions-dropdown {
  z-index: 9999 !important;
}

.contact-table-container .ant-select-selector {
  border: none !important;
  /* padding: 0 !important; */
  background-color: inherit !important;
  min-width: 10rem !important;
}

.ant-select-dropdown {
  min-width: max-content;
}

span[title='Missing input'] {
  @apply italic text-gray-500;
}

.ck-merge-field {
  padding-left: 2px;
  padding-right: 2px;
}

.ck-merge-field_with-warning {
  display: inline-flex;
  width: fit-content;
  line-height: 1 !important;
}

.ck-merge-field_with-value {
  outline-color: #17c1e8 !important;
}

.ant-drawer-body .sequence-action-container {
  display: none !important;
}

.ck.ck-editor__editable span[data-ck-unsafe-element] {
  display: inline !important;
}

.segmented-mailbox-container .ant-segmented {
  background-color: #cecece;
  font-size: 1rem;
  font-weight: 600;
  /* color: #ccc; */
}

.segmented-mailbox-container .ant-segmented-item-selected {
  @apply bg-cyan-400 text-white;
}

.linkedin-chat-custome .ant-upload-list-item-name {
  @apply line-clamp-1 max-w-[7rem];
}
.linkedin-chat-custome .ant-upload-wrapper {
  @apply flex justify-center items-center flex-col;
}

/* Task table custom */

.task-table-new-design-container
  .ant-table-wrapper
  .ant-table-thead
  tr
  th::before {
  display: none !important;
}
.task-table-new-design-container .ant-table-cell {
  border-left: none !important;
  border-right: none !important;
}
.task-table-new-design-container .ant-table-wrapper .ant-table-thead {
  background-color: #f4f4f4 !important;
}
.task-table-new-design-container th.ant-table-cell {
  color: #5e768d !important;
}
.task-table-new-design-container .ant-table-thead .ant-table-cell,
.task-table-new-design-container .ant-table-placeholder {
  background-color: #f4f4f4 !important;
}
.task-table-new-design-container .ant-table-wrapper .ant-table-tbody > tr > td {
  border-bottom: 5px solid #f4f4f4;
}

.task-table-new-design-container .ant-table-row-level-1 {
  @apply bg-[#F2F9FF];
}

.search-table-new-design-container .ant-tag,
.task-table-new-design-container .ant-tag {
  @apply flex items-center w-fit font-medium;
}

.custom-tag .ant-tag {
  @apply flex items-center w-full font-medium justify-center py-2 px-3;
}
.ant-tag {
  margin-inline-end: 0 !important;
}
.task-detail-container .ant-collapse-header {
  border-radius: 8px !important;
}
.task-table-new-design-container .ck-content {
  @apply !min-h-[14rem];
}

.task-table-new-design-container
  .ant-table-wrapper
  .ant-table-thead
  > tr
  > td:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  @apply !invisible;
}

.contact-finder-v2-filter-container .ant-collapse-header {
  @apply bg-white rounded-md shadow-sm px-5 py-3 gap-2 flex items-center font-medium !text-[#5e768d] !rounded-lg;
  width: 282px;
  background: #fff !important;
}

.contact-finder-v2-filter-container .ant-collapse-item {
  background: #fff !important;
}

.customized-collapse .ant-collapse-header:first-child {
  @apply bg-white rounded-md shadow-sm px-5 py-3 gap-2 flex items-center font-medium !text-[#5e768d] !rounded-lg;
}

.customized-collapse .ant-collapse-item-active .ant-collapse-content {
  @apply !border-t-cyan-500;
}

.active-customized-collapse,
.active-customized-collapse .ant-collapse-item,
.active-customized-collapse .ant-collapse-item-active {
  @apply !border-cyan-500 !border-b-cyan-500;
}

.customized-collapse .ant-collapse-item-active .ant-collapse-header {
  /* @apply border border-cyan-500 */
}

.customized-body-collapse .ant-collapse-content-box:first-child {
  @apply !p-0;
}

.customized-tool-bar-btn-apply {
  border: 1px solid #a48cfa;
  background-color: #a48cfa;
}

.analyze-modal-content {
  display: flex;
  justify-content: space-between;
  height: 85vh;
}

.analyze-modal-content-left {
  width: 40%;
  border: 1px solid #000;
  padding: 10px;
}

.analyze-modal-content-right {
  width: 59%;
  border: 1px solid #000;
  padding: 10px;
}

.analyze-score {
}

.custom-analyze-collapse {
  border-radius: 0 !important;
  border: none !important;
}

.custom-analyze-collapse .ant-collapse-header {
  background-color: #fff !important;
  border-radius: 0 !important;
}

.custom-analyze-collapse .ant-collapse-content {
  border-radius: 0 !important;
}

.notification-bulk-add-contacts .ant-drawer-header {
  @apply bg-cyan-700 text-white;
}

.arrow-box {
  clip-path: polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%, 10% 50%);
}

.ai-color,
.ai-switch.ant-switch-checked {
  background: linear-gradient(
    89deg,
    #2dbdad 0%,
    #30a3c5 20%,
    #804cbd 70%
  ) !important;
}

.ai-border-gradient {
  border: 2px solid transparent;
  border-radius: 6px;
  background:
    linear-gradient(white, white) padding-box,
    linear-gradient(89deg, #2dbdad 0%, #30a3c5 20%, #804cbd 70%) border-box;
}

.ai-notification,
.ai-notification .ant-modal-content {
  color: #fff;
  font-weight: 500;
  --grid-color: rgba(16, 185, 129, 0.25);
  background: linear-gradient(
    89deg,
    #2dbdad 0%,
    #30a3c5 20%,
    #804cbd 70%
  ) !important;
  border-radius: 6px;
}

.ai-notification .ant-modal-title,
.ai-notification .ant-modal-header {
  background-color: inherit !important;
}

/* chart growth upward */

.growth-downward p,
.growth-upward p {
  font-size: 14px;
  color: #868eae;
  margin: 0;
}

.growth-downward h1 sub svg,
.growth-upward h1 sub svg {
  position: relative;
  top: 2px;
  font-size: 14px;
  font-weight: 600;
  left: 5px;
  bottom: 0;
}

/*
.growth-downward h1, .growth-upward h1 {
  font-size: 22px;
  margin: 6px 0 0;
} */

.growth-downward h1 sub {
  color: #ff4d4f;
}

.growth-upward h1 sub {
  color: #20c997;
}

/* Chart */

.chart-label {
  display: flex;
}

.chart-label .chart-label__single {
  align-items: center;
}

.chart-label .chart-label__single:not(:last-child) {
  margin-right: 40px;
}

.chart-label .chart-label__single p {
  margin: 0;
  color: #868eae;
}

.gradient-text {
  font-size: 20px;
  font-weight: bold;
  color: transparent; /* Makes the text transparent */
  background: linear-gradient(
    to right,
    #06b6d4,
    #0891b2,
    #9333ea
  ); /* Gradient background */
  background-clip: text; /* Clips the background to the text */
  -webkit-background-clip: text; /* For WebKit browsers */
}

.sync-search-filter-container .ant-form-item {
  margin-bottom: 0 !important;
}

.search-form-container label {
  font-weight: 500;
  color: #5e768d;
}

.restyle-search-input .ant-input-group-addon button {
  border-top-color: #d9d9d9 !important;
  border-right-color: #d9d9d9 !important;
  border-bottom-color: #d9d9d9 !important;
  border-left: none;
  font-weight: 500;
  font-size: 14px;
}
.main-padding {
  @apply p-5;
}
.main-padding:has(.no-padding) {
  padding: 0 !important;
}
