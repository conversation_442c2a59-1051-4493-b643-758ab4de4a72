import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useOnboarding = create(
  persist(
    (set, get) => ({
      // State
      onboardingRequired: false,
      currentStep: 0,
      personalInfo: {
        password: '',
        fullName: '',
        jobTitle: '',
        linkedinUrl: '',
        timezone: '',
        email: '',
        username: '',
        avatarId: '',
      },

      // Actions
      setOnboardingRequired: (required) => set({ onboardingRequired: required }),

      setCurrentStep: (step) => set({ currentStep: step }),

      setPersonalInfo: (info) => set({
        personalInfo: {
          ...get().personalInfo,
          ...info
        }
      }),

      setPassword: (password) => set({
        personalInfo: {
          ...get().personalInfo,
          password
        }
      }),

      // Reset all
      resetOnboardingData: () => set({
        currentStep: 0,
        personalInfo: {
          password: '',
          fullName: '',
          jobTitle: '',
          linkedinUrl: '',
          timezone: '',
          email: '',
          username: '',
          avatarId: '',
        }
      }),
    }),
    {
      name: 'onboarding',
    }
  )
);
