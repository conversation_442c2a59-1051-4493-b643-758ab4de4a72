/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useAuth = create(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      profile: null,
      setAuth: ({ profile }) => set({ profile, isAuthenticated: true }),
      clearAuth: () => set({ isAuthenticated: false, profile: null }),
    }),
    {
      name: 'auth', // name of the item in the storage (must be unique)
    }
  )
);
