import { createSlice } from '@reduxjs/toolkit';

// Initial state for the slice
const initialState = {
  // FORM BULLHORN SUBMISSION
  companyResponse: null,

  // GROUP JOB BY COMPANY
  isGroupCompany: false,
  allCompany: [],
  displayedItemsCount: 10,

  // CONFIG FORM
  configForm: [],

  // ALL USER - STATUS LEAD
  allUsers: [],
  allStatusLead: [],

  // FILTER JOB
  filterJob: null,
  isSearching: false,
  isReset: false,

  // Recent Jobs
  recentJobs: [],
  isResetSearch: false,
  isLoadingPin: false,

  // POTENTIAL RETURN PERCENT

  potentialLeadValue: null,
};

// Create a slice using createSlice
const commonSlice = createSlice({
  name: 'common', // Name of the slice
  initialState, // Initial state
  reducers: {
    // FORM BULLHORN SUBMISSION
    saveCompanyResponse: (state, action) => {
      state.companyResponse = action.payload;
    },

    // GROUP JOB BY COMPANY
    saveIsGroupCompany: (state, action) => {
      state.isGroupCompany = action.payload;
    },
    // REFINE BY SEARCH
    saveIsRefineBySearch: (state, action) => {
      state.isRefineBySearch = action.payload;
    },
    saveAllCompany: (state, action) => {
      state.allCompany = action.payload;
    },
    setIsSearching: (state, action) => {
      state.isSearching = action.payload;
    },
    loadMoreCompany: (state) => {
      state.displayedItemsCount += 10;
    },
    resetAllCompany: (state) => {
      state.allCompany = [];
      state.displayedItemsCount = 10;
    },

    // CONFIG FORM
    saveConfigForm: (state, action) => {
      state.configForm = action.payload;
    },

    // ALL USER - ALL STATUS LEAD
    saveAllUsers: (state, action) => {
      state.allUsers = action.payload;
    },
    saveAllStatusLead: (state, action) => {
      state.allStatusLead = action.payload;
    },

    // FILTER JOB
    saveFilterJob: (state, action) => {
      state.filterJob = action.payload;
    },

    setIsReset: (state, action) => {
      state.isReset = action.payload;
    },

    // Recent Jobs
    saveRecentJobs: (state, action) => {
      state.recentJobs = action.payload;
    },

    setIsResetSearch: (state, action) => {
      state.isResetSearch = action.payload;
    },

    setIsLoadingPin: (state, action) => {
      state.isLoadingPin = action.payload;
    },

    // POTENTIAL RETURN PERCENT
    savePotentialLeadValue: (state, action) => {
      state.potentialLeadValue = action.payload;
    },
  },
});

// Export the generated reducer and actions
export const {
  saveIsGroupCompany,
  saveIsRefineBySearch,
  saveCompanyResponse,
  saveAllCompany,
  loadMoreCompany,
  resetAllCompany,
  saveConfigForm,
  saveAllUsers,
  saveAllStatusLead,
  saveFilterJob,
  setIsSearching,
  setIsReset,
  saveRecentJobs,
  setIsResetSearch,
  setIsLoadingPin,
  savePotentialLeadValue,
} = commonSlice.actions;

export const selectIsGroupCompany = (state) => state.common.isGroupCompany;
export const selectIsRefineBySearch = (state) => state.common.isRefineBySearch;
export const selectCompanyResponse = (state) => state.common.companyResponse;
export const selectAllCompany = (state) => state.common.allCompany;
export const selectIsSearchingStatus = (state) => state.common.isSearching;
export const selectIsResetStatus = (state) => state.common.isReset;
export const selectIsResetSearchStatus = (state) => state.common.isResetSearch;
export const selectIsLoadingPin = (state) => state.common.isLoadingPin;
export const selectDisplayedItemsCount = (state) =>
  state.common.displayedItemsCount;

export const selectConfigForm = (state) => state.common.configForm;

export const selectAllUsers = (state) => state.common.allUsers;
export const selectAllStatusLead = (state) => state.common.allStatusLead;

export const selectFilterJob = (state) => state.common.filterJob;

export const selectRecentJobs = (state) => state.common.recentJobs;

export const selectPotentialLeadValue = (state) =>
  state.common.potentialLeadValue;

// Export the reducer function
export default commonSlice.reducer;
