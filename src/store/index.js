import { configureStore } from '@reduxjs/toolkit';
import { restAPI } from '../app/core';
import commonSlide from './common';
const store = configureStore({
  reducer: {
    [restAPI.reducerPath]: restAPI.reducer,
    common: commonSlide, // Add the common reducer to the store
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(restAPI.middleware),
});

export default store;
