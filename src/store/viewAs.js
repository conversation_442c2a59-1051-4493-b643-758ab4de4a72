/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useViewAs = create(
  persist(
    (set, get) => ({
      viewerAsUser: false,
      profileUser: null,
      setViewAs: ({ profileUser }) => set({ profileUser, viewerAsUser: true }),
      clearViewAs: () => set({ viewerAsUser: false, profileUser: null }),
    }),
    {
      name: 'viewAs',
    }
  )
);
