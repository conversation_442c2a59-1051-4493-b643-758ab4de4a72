.ant-table-wrapper {
  .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-content
    > table
    > thead
    > tr
    > th {
    border-inline-end: unset;
  }
}

// .ant-btn-primary {
//   background-color: #0891b2 !important;
// }

.ant-switch {
  background-color: rgba(0, 0, 0, 0.25);
}

// .ant-segmented-item-selected {
//   background-color: #0891b2 !important;
//   color: #fff !important;
// }

.ant-checkbox-wrapper > span:nth-child(2) {
  width: 100%;
}

.custom_input {
  input {
    color: #f0f0f0;
  }

  .ant-select-selection-placeholder {
    color: #f0f0f0;
  }

  .ant-select-selection-search-input {
    color: #f0f0f0 !important;
  }

  .ant-select-selector {
    background-color: #0891b2 !important;
  }
}

.custom_input_sequence {
  .ant-select-selector {
    height: 100% !important;
    min-height: 50px;
  }
}

.custom_input_vacancy {
  input {
    color: #f0f0f0;
  }

  .ant-select-selection-placeholder {
    color: #f0f0f0;
  }

  .ant-select-selection-search-input {
    color: #f0f0f0 !important;
  }

  .ant-select-selector {
    background-color: rgb(182, 81, 102) !important;
    color: #fff !important;
  }
}

.scheduling-container .footer {
  display: none !important;
}

.add-contact-container .ant-select-selection-search {
  min-width: 10rem !important;
}
.keywords-container .ant-select-selection-search {
  min-width: 10rem !important;
}
.tag_status_sequence_list {
  height: fit-content !important;
  text-align: center;
  width: 90px;
  margin-top: 0.6rem;
}

/* default card style */

.ant-card {
  box-shadow: 0 5px 20px #9299b803;
}

.ant-card-head {
  padding-left: 25px !important;
  padding-right: 25px !important;
}

.ant-card-head-title span {
  display: inline-block;
  margin-left: 15px;
  font-size: 11px;
  font-weight: 500;
  color: #868eae;
}

.ant-card-head .ant-card-extra {
  display: flex;
  align-items: center;
}

.ant-card-head .ant-card-extra a {
  color: #868eae;
}

.ant-card-extra .ant-dropdown-trigger {
  line-height: 0;
  order: 1;
  margin-left: 20px;
}

.sDash_unresizable {
  resize: none;
}

/* card nav */

.card-nav ul {
  list-style: none;
  display: flex;
  margin: 0 -8px !important;
}

.card-nav ul li {
  margin: 0 8px !important;
  position: relative;
}

.card-nav ul li a {
  font-weight: 500;
  color: #868eae;
  font-size: 12px;
}

.card-nav ul li.active a {
  color: #5f63f2;
  font-weight: 500;
}

.card-nav ul li.active:before {
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  background: #5f63f2;
  left: 0;
  bottom: -19px;
}

/* Chart */

.chart-label {
  display: flex;
}

.chart-label .chart-label__single {
  align-items: center;
}

.chart-label .chart-label__single:not(:last-child) {
  margin-right: 40px;
}

.chart-label .chart-label__single p {
  margin: 0;
  color: #868eae;
}

.keywords-table-container .ant-table-wrapper .ant-table-thead tr th::before {
  display: none !important;
}
.keywords-table-container .ant-table-cell {
  border-left: none !important;
  border-right: none !important;
}
.keywords-table-container .ant-table-tbody .ant-table-cell,
.live-feed-table-container .ant-table-tbody .ant-table-cell {
  border-bottom: none !important;
}
.keywords-table-container .ant-table-wrapper .ant-table-thead {
  background-color: white !important;
}
.keywords-table-container th.ant-table-cell {
  color: black !important;
}
.keywords-table-container .ant-table-thead .ant-table-cell {
  background-color: #f8f9fb !important;
}

.keywords-table-container .ant-table-cell {
  font-family: 'Montserrat';
  position: static;
  float: none;
  display: table-cell;
}

.keywords-table-container .ant-table-wrapper {
  @apply border rounded-md;
}
.ant-layout-sider {
  z-index: 1 !important;
}
.main-content-container {
  z-index: 0 !important;
}

.ant-select-selector {
  max-height: 10rem;
  overflow-y: auto;
}
.company-status-container .ant-select-selector {
  overflow: unset;
}

.custom-input-inemail {
  border-radius: 0px !important;
}

.ant-table-container {
  ::-webkit-scrollbar {
    width: 6px;
    height: 5px;
  }
}

.custom-button-load-more {
  .buttons {
    display: flex;
    justify-content: space-around;
    top: 20px;
    left: 20px;
  }

  .buttons button {
    width: 180px;
    height: 42px;
    // background-color: white;
    margin: 10px;
    color: #08979c;
    position: relative;
    overflow: hidden;
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 500;
    text-transform: uppercase;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
  }

  .buttons button:before,
  .buttons button:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: #08979c;
    transition: all 0.3s cubic-bezier(0.35, 0.1, 0.25, 1);
  }

  .buttons button:before {
    right: 0;
    top: 0;
    transition: all 0.5s cubic-bezier(0.35, 0.1, 0.25, 1);
  }

  .buttons button:after {
    left: 0;
    bottom: 0;
  }

  .buttons button span {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
    z-index: 1;
  }

  .buttons button span:before,
  .buttons button span:after {
    content: '';
    position: absolute;
    width: 2px;
    height: 0;
    background-color: #08979c;
    transition: all 0.3s cubic-bezier(0.35, 0.1, 0.25, 1);
  }

  .buttons button span:before {
    right: 0;
    top: 0;
    transition: all 0.5s cubic-bezier(0.35, 0.1, 0.25, 1);
  }

  .buttons button span:after {
    left: 0;
    bottom: 0;
  }

  .buttons button p {
    padding: 0;
    margin: 0;
    transition: all 0.4s cubic-bezier(0.35, 0.1, 0.25, 1);
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .buttons button p:before,
  .buttons button p:after {
    position: absolute;
    width: 100%;
    transition: all 0.4s cubic-bezier(0.35, 0.1, 0.25, 1);
    z-index: 1;
    left: 0;
  }

  .buttons button p:before {
    content: attr(data-title);
    top: 50%;
    transform: translateY(-50%);
  }

  .buttons button p:after {
    content: attr(data-text);
    top: 150%;
    color: #08979c;
  }

  // .buttons button:hover:before,
  // .buttons button:hover:after {
  //   width: 100%;
  // }

  // .buttons button:hover span {
  //   z-index: 1;
  // }

  // .buttons button:hover span:before,
  // .buttons button:hover span:after {
  //   height: 100%;
  // }

  // .buttons button:hover p:before {
  //   top: -50%;
  //   transform: rotate(5deg);
  // }

  // .buttons button:hover p:after {
  //   top: 50%;
  //   transform: translateY(-50%);
  // }

  .buttons button.start {
    background-color: #44d8a4;
    box-shadow: 0px 5px 10px -10px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }

  .buttons button.start p:before {
    top: -50%;
    transform: rotate(5deg);
  }

  .buttons button.start p:after {
    color: white;
    transition: all 0s ease;
    content: attr(data-start);
    top: 50%;
    transform: translateY(-50%);
    animation: start 0.3s ease;
    animation-fill-mode: forwards;
  }

  @keyframes start {
    from {
      top: -50%;
    }
  }

  .buttons button.start:hover:before,
  .buttons button.start:hover:after {
    display: none;
  }

  .buttons button.start:hover span {
    display: none;
  }

  .buttons button:active {
    outline: none;
    border: none;
  }

  .buttons button:focus {
    outline: 0;
  }
}

.customized-modal-bg .ant-modal-content {
  background-color: #5f63f2;
}

.ant-pagination {
  display: flex;
  gap: 5px;
  .ant-spin-nested-loading {
    width: 100%;
  }
  .ant-pagination-prev,
  .ant-pagination-next {
    @apply bg-white border shadow-md font-medium;
  }
  .ant-pagination-item {
    @apply bg-white border shadow-md font-medium;
  }

  .ant-pagination-options {
    @apply ml-auto;
  }

  .ant-pagination-item-active {
    @apply bg-cyan-600 border-none;
    a {
      @apply text-white;
    }
    a:hover {
      @apply text-gray-200;
    }
  }
  .ant-pagination-options {
    @apply ml-5;
  }
  .ant-pagination-item-link {
    display: flex !important;
    @apply items-center justify-center;
  }
}

.search-in-contact-finder-v2-container {
  .ant-input-search-button {
    @apply bg-cyan-600 font-medium text-white;
  }
}

.task-table-new-design-container .ant-slider .ant-slider-mark-text-active {
  color: #0891b2 !important;
}

.customized-segmented-contact-finder-v2 {
  background-color: #e9ebef;
  padding: 0.2rem;
  .ant-segmented-item-label {
    @apply font-semibold px-8 py-2 text-lg;
  }
  .ant-segmented-item-selected {
    @apply rounded-md;
    .ant-segmented-item-label {
      @apply text-[#08979c] bg-[#e6fffb] border border-[#87e8de] rounded-md;
    }
  }
}

.customized-segmented-sequence-template {
  background-color: #e9ebef;
  padding: 0.2rem;
  .ant-segmented-item-label {
    @apply font-medium;
  }
  .ant-segmented-item-selected {
    @apply rounded-md;
    .ant-segmented-item-label {
      @apply text-[#08979c] bg-[#e6fffb] border border-[#87e8de] rounded-md;
    }
  }
}

// .contact-finder-v2-root-container .ant-select-selection-search-input{
//   @apply min-w-[5rem]
// }

.segmented-filter-container {
  @apply w-fit;
  .ant-segmented-group {
    @apply justify-center;
  }
  .ant-segmented-item-label {
    @apply font-medium;
  }
  .ant-segmented-item-selected {
    @apply rounded-md;
    .ant-segmented-item-label {
      @apply text-[#08979c] bg-[#e6fffb] rounded-md;
    }
  }
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #17c1e8 !important;
}
.ant-tabs-tab-active {
  border-bottom-color: #17c1e8 !important;
}

.ant-table-cell:has(.expand-row-container) {
  padding: 0 !important;
}

// .ant-steps-item-container {
//   @apply flex items-center w-full;
// }

.ant-steps-item-description {
  max-width: none !important;
}

.sequence-background-container .ant-input-affix-wrapper::before {
  padding-left: 6px;
}
.customized-icon-button {
  .ant-btn-icon {
    margin-inline-end: 0 !important;
  }
}

.custom-modal-bulk-add-contact .ant-modal-content {
  padding: 0;
  background-color: transparent !important;
  border-radius: 10px !important;
}

.custom-modal-bulk-add-contact .ant-modal-footer {
  margin-top: 1px !important;
}

.modal-content {
  width: 100%;
}

.custom-modal-bulk-add-contact .ant-modal-body {
  background-color: transparent !important;
}

.ant-table-cell-row-hover {
  background-color: rgb(*********** / var(--tw-bg-opacity)) !important;
}

// .ant-dropdown-placement-bottom:has(.create-new-sequence-button-container) {
//   margin-left: 10.2rem;
// }
.btn-revert-contact span {
  // letter-spacing: 1px;
  transition: top 0.5s;
}
.btn-text-one {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.btn-text-two {
  position: absolute;
  width: 100%;
  top: 150%;
  left: 0;
  transform: translateY(-50%);
}

.btn-revert-contact:hover .btn-text-one {
  top: -100%;
}

.btn-revert-contact:hover .btn-text-two {
  top: 50%;
}

.hide-selection-container .ant-table-selection-col,
.hide-selection-container .ant-table-selection-column {
  display: none;
}

.ant-select-item-option-active {
  background-color: rgb(*********** / var(--tw-bg-opacity)) !important;
}

.ant-select-dropdown:has(.customized-dropdown-background) {
  background-color: #e0ebf9 !important;
}

.company-detail-container {
  .ant-input,
  .ant-select-selector,
  .ant-input-number {
    border-radius: 0 !important;
    border-top: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;
  }
}
