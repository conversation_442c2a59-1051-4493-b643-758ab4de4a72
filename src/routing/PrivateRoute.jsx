import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useOnboarding } from '../store/onboarding';

const PrivateRoute = (props) => {
  const { component: Component, isAuthenticated } = props;
  const location = useLocation();
  const { onboardingRequired } = useOnboarding();
  // Only allow access to onboarding page if onboarding is required
  if (isAuthenticated && onboardingRequired && location.pathname !== '/onboarding') {
    return <Navigate to={'/onboarding'} />;
  }

  return (
    <>
      <div>
        {isAuthenticated ? Component : <Navigate to={'/'} />}
      </div>
    </>
  );
};

PrivateRoute.propTypes = {
  component: PropTypes.any,
  isAuthenticated: PropTypes.bool,
};

export default PrivateRoute;
