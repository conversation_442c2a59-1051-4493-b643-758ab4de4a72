import React from 'react';
import { Navigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useOnboarding } from '../store/onboarding';

const PublicRoute = (props) => {
  const { component: Component, isAuthenticated } = props; // destructing props
  const { onboardingRequired } = useOnboarding();

  return (
    <>
      <div>
        {/* if User Authenticated and he will try to access auth routes then he will be redirected to landing Path */}
        {isAuthenticated && onboardingRequired ? (
          <Navigate to={'/onboarding'} />
        ) : isAuthenticated ? (
          <Navigate to={'/dashboard'} />
        ) : (
          Component
        )}
      </div>
    </>
  );
};

PublicRoute.propTypes = {
  component: PropTypes.any,
  isAuthenticated: PropTypes.bool,
};

export default PublicRoute;
