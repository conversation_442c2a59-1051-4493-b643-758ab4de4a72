import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import PrivateRoute from './PrivateRoute';
import PublicRoute from './PublicRoute';
import { useAuth } from '../store/auth';

const RouteWithLayout = (props) => {
  const { isAuthenticated } = useAuth();

  const {
    layout: Layout,
    component: Component,
    protectedRoute,
    isNotRequireAuth,
    ...rest
  } = props;

  return protectedRoute ? (
    <PrivateRoute
      isAuthenticated={isAuthenticated}
      component={
        <Layout>
          <Component />
        </Layout>
      }
      {...rest}
    />
  ) : (
    <PublicRoute
      isAuthenticated={isNotRequireAuth ? false : isAuthenticated}
      component={
        <Layout>
          <Component />
        </Layout>
      }
      {...rest}
    />
  );
};

RouteWithLayout.propTypes = {
  component: PropTypes.any.isRequired,
  layout: PropTypes.any.isRequired,
  // path: PropTypes.string.isRequired,
  path: PropTypes.oneOfType([PropTypes.string, PropTypes.array]).isRequired,
  protectedRoute: PropTypes.bool,
  isNotRequireAuth: PropTypes.bool,
};

export default RouteWithLayout;
