import React, { useEffect } from 'react';
import { Main, Minimal } from '../layouts';
import RouteWithLayout from './RouteWithLayout';
import { AccessDenied, ForgotPassword } from '../components';
import Dashboard from '../containers/Dashboard';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import AddJob from '../components/AddJob';
import AddLead from '../containers/AddLead';
import MyLeads from '../containers/MyLeads';
import UserManagement from '../containers/UserManagement';
import CompanyManagement from '../containers/CompanyManagement';
import EditUserManagement from '../containers/EditUserManagement';
import SearchV2 from '../containers/SearchVer2';
import SearchDetailV2 from '../containers/SearchDetailV2';
import SyncPageV2 from '../containers/SyncPageV2';
import SyncDetailPageV2 from '../containers/SyncDetailPageV2';
import SyncSearchJobListV2 from '../containers/SyncSearchJobListV2';
import ReportedAgencyDetail from '../containers/ReportedAgencyV2/ReportedAgencyDetail';
import ReportedAgenciesTable from '../containers/ReportedAgencyV2/ReportedAgenciesTable';
import RegisterPage from '../containers/Register/RegisterPage';
import LoginPage from '../containers/Login/LoginPage';
import EmailFinderContainer from '../containers/EmailFinder/EmailFinderContainer';
import ManualLeads from '../containers/ManualLeads';
import StaffPerformance from '../containers/StaffPerformance';
import StaffPerformanceDetail from '../containers/StaffPerformance/StaffPerformanceDetail';
import NoAccess from '../containers/NoAccess';
import JobSame from '../containers/JobSame';
import Settings from '../containers/Settings';
import Sequences from '../containers/Sequence';
import MailPermission from '../components/MailPermission/MailPermission';
import MailBoxTab from '../containers/MailBox';
import MailBoxPermission from '../components/MailBoxPermission/MailBoxPermission';
import SchedulingPreview from '../containers/SchedulingPreview';
import DetailCompany from '../containers/CompanyManagement/Detail';
import ContactList from '../containers/ContactList';
import HotList from '../containers/HotList';
import TaskManagement from '../components/TaskManagement';
import EmailFinderContainerV2 from '../containers/Dashboard/EmailFinderContainerV2';
import SyncVacancyPage from '../containers/SyncVacancyPage';
import SyncLeadPage from '../containers/SyncLeadPage';
import SyncOpportunityPage from '../containers/SyncOpportunityPage';
import NotFoundPage from '../containers/NotFound';
import CRMDashboardPage from '../containers/CRM';
import CRMCompaniesPage from '../containers/CRM/Companies';
import CRMContactsPage from '../containers/CRM/Contacts';
import CRMLeadsPage from '../containers/CRM/Leads';
import EditCompany from '../containers/CRM/Companies/EditCompany';
import CreateNewCompany from '../containers/CRM/Companies/CreateNewCompany';
import CreateNewContact from '../containers/CRM/Contacts/CreateNewContact';
import EditContact from '../containers/CRM/Contacts/EditContact';
import CreateNewLead from '../containers/CRM/Leads/CreateNewLead';
import EditLead from '../containers/CRM/Leads/EditLead';
import ViewCompany from '../containers/CRM/Companies/ViewCompany';
import ViewContact from '../containers/CRM/Contacts/ViewContact';
import ViewLead from '../containers/CRM/Leads/ViewLead';
import ImportLeadsPage from '../containers/ImportLeads';
import ManualLeadsPage from '../containers/ManualLeadPage';
import CRMTagsPage from '../containers/CRM/Tags';
import UnipileCallback from '../components/UnipileCallback/UnipileCallback';
import CRMSettingPage from '../containers/CRM/Setting';
import CompanyOnboardingPage from '../containers/CompanyOnboarding';
import SyncStandardLeadPage from '../containers/SyncStandardLeadPage';
import CRMIndustriesPage from '../containers/CRM/Industries';
import CRMSkillsPage from '../containers/CRM/Skills';
import ProtectedMinimal from '../layouts/ProtectedMinimal';
import CreditManagementCompany from '../containers/page/CreditManagementCompany';
import CreditManagementAdmin from '../containers/page/CreditManagementAdmin';
import OnboardingPage from '../containers/Onboarding/OnboardingPage';

const MainRouting = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Navigate to="/login" />} />
        <Route
          path="/login"
          element={
            <RouteWithLayout
              path="/login"
              layout={Minimal}
              component={LoginPage}
            />
          }
        />
        <Route
          path="/register"
          element={
            <RouteWithLayout
              path="/register"
              layout={Minimal}
              component={RegisterPage}
            />
          }
        />
        <Route
          path="/forgot-password"
          element={
            <RouteWithLayout
              path={'/forgot-password'}
              layout={Main}
              component={ForgotPassword}
              // protectedRoute={true}
            />
          }
        />
        <Route
          path="/access-denied"
          element={
            <RouteWithLayout
              path={'/access-denied'}
              layout={Main}
              component={AccessDenied}
              // protectedRoute={true}
            />
          }
        />
        <Route
          path="/dashboard"
          element={
            <RouteWithLayout
              path={'/dashboard'}
              layout={Main}
              component={Dashboard}
              protectedRoute={true}
            />
          }
        />
        {/*  Not using Staff Performance anymore */}
        {/* <Route
          path="/staff-performance"
          element={
            <RouteWithLayout
              path={'/staff-performance'}
              layout={Main}
              component={StaffPerformance}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/staff-performance/:id"
          element={
            <RouteWithLayout
              path={'/staff-performance'}
              layout={Main}
              component={StaffPerformanceDetail}
              protectedRoute={true}
            />
          }
        /> */}
        <Route
          path="/user-management"
          element={
            <RouteWithLayout
              path={'/user-management'}
              layout={Main}
              component={UserManagement}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/user-management/company"
          element={
            <RouteWithLayout
              path={'/user-management/company'}
              layout={Main}
              component={CompanyManagement}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/user-management/company/:id"
          element={
            <RouteWithLayout
              path={'/user-management/company'}
              layout={Main}
              component={DetailCompany}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/user-management/:id/edit"
          element={
            <RouteWithLayout
              path={'/user-management'}
              layout={Main}
              component={EditUserManagement}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/syncs"
          element={
            <RouteWithLayout
              path={'/syncs'}
              layout={Main}
              component={SyncPageV2}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/email-verification"
          element={
            <RouteWithLayout
              path={'/email-verification'}
              layout={Main}
              component={() => <></>}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/email-finder"
          element={
            <RouteWithLayout
              path={'/email-finder'}
              layout={Main}
              component={EmailFinderContainer}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/email-finder-beta"
          element={
            <RouteWithLayout
              path={'/email-finder-beta'}
              layout={Main}
              component={EmailFinderContainerV2}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/reported_agencies"
          element={
            <RouteWithLayout
              path={'/reported_agencies'}
              layout={Main}
              component={ReportedAgenciesTable}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/search"
          element={
            <RouteWithLayout
              path={'/search'}
              layout={Main}
              // component={Search}
              component={SearchV2}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/duplicate-jobs"
          element={
            <RouteWithLayout
              path={'/duplicate-jobs'}
              layout={Main}
              component={JobSame}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/task"
          element={
            <RouteWithLayout
              path={'/task'}
              layout={Main}
              component={TaskManagement}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/settings"
          element={
            <RouteWithLayout
              path={'/settings'}
              layout={Main}
              component={Settings}
              protectedRoute={true}
            />
          }
        />

        <Route path="/schedule" element={<SchedulingPreview />} />

        <Route
          path="/syncs/:id"
          element={
            <RouteWithLayout
              path={'/syncs'}
              layout={Main}
              component={SyncDetailPageV2}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/sync-vacancy/:searchId/:jobId"
          element={
            <RouteWithLayout
              path={'/sync-vacancy'}
              layout={Main}
              component={SyncVacancyPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/sync-lead/:searchId/:jobId"
          element={
            <RouteWithLayout
              path={'/sync-lead'}
              layout={Main}
              component={SyncLeadPage}
              protectedRoute={true}
            />
          }
        />
        {/* Syncing lead job in Zileo crm */}
        <Route
          path="/sync-standard-lead/:searchId/:jobId"
          element={
            <RouteWithLayout
              path={'/sync-standard-lead'}
              layout={Main}
              component={SyncStandardLeadPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/sync-opportunity/:searchId/:jobId"
          element={
            <RouteWithLayout
              path={'/sync-opportunity'}
              layout={Main}
              component={SyncOpportunityPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/syncs/:syncId/search/:searchId"
          element={
            <RouteWithLayout
              path={'/syncs'}
              layout={Main}
              component={SyncSearchJobListV2}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/search/:searchId"
          element={
            <RouteWithLayout
              path={'/search'}
              layout={Main}
              // component={SearchDetail}
              component={SearchDetailV2}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/reported_agencies/:id"
          element={
            <RouteWithLayout
              path={'/reported_agencies'}
              layout={Main}
              component={ReportedAgencyDetail}
              protectedRoute={true}
            />
          }
        />
        {/* <Route
          path="/add-lead"
          element={
            <RouteWithLayout
              path={'/add-lead'}
              layout={Main}
              component={AddLead}
              protectedRoute={true}
            />
          }
        /> */}
        <Route
          path="/my-leads"
          element={
            <RouteWithLayout
              path={'/my-leads'}
              layout={Main}
              component={MyLeads}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/my-leads/import"
          element={
            <RouteWithLayout
              path={'/my-leads'}
              layout={Main}
              component={ImportLeadsPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/my-leads/manual"
          element={
            <RouteWithLayout
              path={'/my-leads'}
              layout={Main}
              component={ManualLeadsPage}
              protectedRoute={true}
            />
          }
        />
        {/* <Route
          path="/sequence-email"
          element={
            <RouteWithLayout
              path={'/sequence-email'}
              layout={Main}
              component={SequenseEmail}
              protectedRoute={true}
            />
          }
        /> */}
        <Route
          path="/sequence"
          element={
            <RouteWithLayout
              path={'/sequence'}
              layout={Main}
              component={Sequences}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/contact_list/:contactListId"
          element={
            <RouteWithLayout
              path={'/contact_list'}
              layout={Main}
              component={ContactList}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/hot_list/:hotListId"
          element={
            <RouteWithLayout
              path={'/hot_list'}
              layout={Main}
              component={HotList}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/mail-box"
          element={
            <RouteWithLayout
              path={'/mail-box'}
              layout={Main}
              component={MailBoxTab}
              protectedRoute={true}
            />
          }
        />
         {/* <Route
          path="/credit-management/company/:companyId"
          element={
            <RouteWithLayout
              path={'/credit-management/company'}
              layout={Main}
              component={CreditManagementCompany}
              protectedRoute={true}
            />
          }
        /> */}
        {/* <Route
          path="/credit-management"
          element={
            <RouteWithLayout
              path={'/credit-management'}
              layout={Main}
              component={CreditManagementAdmin}
              protectedRoute={true}
            />
          }
        /> */}
        <Route
          path="/manual-leads"
          element={
            <RouteWithLayout
              path={'/manual-leads'}
              layout={Main}
              component={ManualLeads}
              protectedRoute={true}
            />
          }
        />
        {/* <Route
          path="/add-job"
          element={
            <RouteWithLayout
              path={'/add-job'}
              layout={Main}
              component={AddJob}
              protectedRoute={true}
            />
          }
        /> */}
        <Route
          path="/no-access"
          element={
            <RouteWithLayout
              path={'/no-access'}
              layout={Main}
              component={NoAccess}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/mail-callback"
          element={
            <RouteWithLayout
              isNotRequireAuth={true}
              path="/mail-callback"
              layout={Minimal}
              component={MailPermission}
            />
          }
        />
        <Route
          path="/mailbox-callback"
          element={
            <RouteWithLayout
              isNotRequireAuth={true}
              path="/mailbox-callback"
              layout={Minimal}
              component={MailBoxPermission}
            />
          }
        />
        <Route
          path="/unipile-callback"
          element={
            <RouteWithLayout
              isNotRequireAuth={true}
              path="/unipile-callback"
              layout={Minimal}
              component={UnipileCallback}
            />
          }
        />
        {/* Zileo CRM routing */}
        <Route
          path="/crm"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMDashboardPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/companies"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMCompaniesPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/companies/create"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CreateNewCompany}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/crm/companies/edit/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={EditCompany}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/companies/view/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={ViewCompany}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/contacts"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMContactsPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/contacts/create"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CreateNewContact}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/contacts/view/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={ViewContact}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/contacts/edit/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={EditContact}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/leads"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMLeadsPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/leads/create"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CreateNewLead}
              protectedRoute={true}
            />
          }
        />
        <Route
          path="/crm/leads/edit/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={EditLead}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/leads/view/:id"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={ViewLead}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/setting/tags"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMTagsPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/setting/industries"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMIndustriesPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/setting/skills"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMSkillsPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/crm/setting"
          element={
            <RouteWithLayout
              path={'/crm'}
              layout={Main}
              component={CRMSettingPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/company/onboarding"
          element={
            <RouteWithLayout
              path={'/company'}
              layout={ProtectedMinimal}
              component={CompanyOnboardingPage}
              protectedRoute={true}
            />
          }
        />

        <Route
          path="/onboarding"
          element={
            <RouteWithLayout
              path={'/onboarding'}
              layout={ProtectedMinimal}
              component={OnboardingPage}
              protectedRoute={true}
            />
          }
        />

        {/* <Route
          path="/vod"
          element={
            <RouteWithLayout
              path={"/vod"}
              layout={Main}
              component={Vod}
              protectedRoute={true}
            />
          }
        /> */}
        <Route path="/not-found" element={<NotFoundPage />} />
        <Route path="*" element={<Navigate to="/not-found" />} />
      </Routes>
    </BrowserRouter>
  );
};

export default MainRouting;
