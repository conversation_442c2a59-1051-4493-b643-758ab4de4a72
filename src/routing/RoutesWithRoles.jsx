export const commonRoutes = ['/settings'];
export const VALID_ROUTES_BY_ROLE = {
  admin: {
    paths: [
      '/dashboard',
      '/syncs',
      '/search',
      '/add-lead',
      '/my-leads',
      '/add-job',
      '/syncs/:id',
      '/search/:id',
      '/agency-detail/:id',
      '/reported_agencies',
      '/email-verification',
      '/email-finder',
      '/email-finder-beta',
      '/user-management',
      '/duplicate-jobs',
      ...commonRoutes,
    ],
    landingUrl: '/dashboard',
    redirectUrl: '/access-denied',
    signInUrl: '/signIn',
  },
  client: {
    paths: ['/dashboard', '/schedule', ...commonRoutes],
    landingUrl: '/dashboard',
    redirectUrl: '/access-denied',
    signInUrl: '/signIn',
  },
};
