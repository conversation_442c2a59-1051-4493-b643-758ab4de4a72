export const getRandomColor = () => {
  const randomColor = '#' + Math.floor(Math.random() * 16777215).toString(16);
  return randomColor;
};

export const colors = [
  { name: '<PERSON>', hex: '#FFBF00' },
  { name: '<PERSON>', hex: '#0000FF' },
  { name: '<PERSON><PERSON>', hex: '#00FFFF' },
  { name: 'Den<PERSON>', hex: '#1560BD' },
  { name: '<PERSON>', hex: '#50C878' },
  { name: '<PERSON><PERSON><PERSON>', hex: '#FF00FF' },
  { name: '<PERSON>', hex: '#FFD700' },
  { name: '<PERSON>', hex: '#8E7618' },
  { name: 'Indigo', hex: '#4B0082' },
  { name: '<PERSON>', hex: '#00A86B' },
  { name: 'Khaki', hex: '#C3B091' },
  { name: 'Lavender', hex: '#E6E6FA' },
  { name: '<PERSON><PERSON><PERSON>', hex: '#FF00FF' },
  { name: '<PERSON>', hex: '#000080' },
  { name: '<PERSON>', hex: '#808000' },
  { name: '<PERSON>', hex: '#800080' },
  { name: '<PERSON>uartz', hex: '#51484F' },
  { name: 'Ruby', hex: '#E0115F' },
  { name: 'Sapphire', hex: '#0F52BA' },
  { name: 'Teal', hex: '#008080' },
  { name: 'Ultramarine', hex: '#3F00FF' },
  { name: 'Violet', hex: '#EE82EE' },
  { name: 'Wine', hex: '#722F37' },
  { name: 'Xanadu', hex: '#738678' },
  { name: 'Yellow', hex: '#FFFF00' },
  { name: 'Zaffre', hex: '#0014A8' }
];


export const  getColorHexFromName = (name) => {
  const firstLetter = name?.charAt(0)?.toLowerCase();
  const resultColor = colors.find(color => color?.name.toLowerCase().startsWith(firstLetter));
  return resultColor ? resultColor.hex : null;
}
