import { useState, useEffect, useRef, useCallback } from 'react';
import _debounce from 'lodash/debounce';
import { notification } from 'antd';

const useInfiniteScrollWithSearch = (
  fetchData,
  pageSize = 10,
  debounceTime = 300,
  searchDebounceTime = 200,
  isStepMode = false
) => {
  const [options, setOptions] = useState([]);
  const [start, setStart] = useState(0);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingScroll, setLoadingScroll] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [companyId, setCompanyId] = useState('');
  const [valueNotFound, setValueNotFound] = useState('');
  const prevSearchTextRef = useRef('');
  const hasMoreResultsRef = useRef(true);

  const loadMoreOptions = async (
    start,
    searchText,
    companyID,
    options,
    count = null,
    handleSelectAll = null
  ) => {
    if (!isStepMode) {
      setLoading(true);
      setOptions([]);
      setLoadingScroll(true);
      const data = await fetchData(
        start,
        count || pageSize,
        searchText,
        companyID,
        '',
        searchText
      )
        .then(({ data }) => {
          return data;
        })
        .catch((err) => {
          notification.error({ message: err.response?.data?.message });
          return err;
        });
      try {
        if (data?.response?.status === 400) setLoading(false);
        let moreOptions = data?.result?.data || data?.result || [];

        if (options && options?.length !== 0)
          moreOptions = [...options, ...(moreOptions || [])];
        const isNameExists = moreOptions?.some(
          (option) => option?.name === searchText
        );

        if (!isNameExists) {
          if (searchText !== '' && typeof searchText !== 'undefined') {
            setValueNotFound(searchText);
            if (moreOptions.length === 0)
              moreOptions.push({ name: searchText, id: 0 });
          }
        }
        if (moreOptions && moreOptions.length === 0) {
          setLoading(false);
          hasMoreResultsRef.current = false;
          setLoading(false);
        } else {
          setOptions((prevOptions) => [
            ...(prevOptions || []),
            ...(moreOptions || []),
          ]);
          if (handleSelectAll) {
            handleSelectAll(null, [...moreOptions], true);
          }
        }
        setLoading(false);
        setLoadingScroll(false);
      } catch (error) {
        setLoading(false);
        setLoadingScroll(false);
        console.log('error: ', error);
      }
    }
  };

  const debouncedLoadMoreOptions = useRef(
    _debounce(loadMoreOptions, debounceTime)
  ).current;

  const debouncedHandleSearch = useCallback(
    _debounce((value, cmpnyId, count, handleSelectAll) => {
      setStart(0);
      setSearchText(value);
      hasMoreResultsRef.current = true;
      debouncedLoadMoreOptions(
        0,
        value,
        cmpnyId,
        [],
        count || null,
        handleSelectAll || null
      );
    }, searchDebounceTime),
    [debouncedLoadMoreOptions]
  );

  useEffect(() => {
    if (searchText !== prevSearchTextRef.current) {
      prevSearchTextRef.current = searchText;
      setOptions([]);
      setStart(0);
      hasMoreResultsRef.current = true;
      debouncedHandleSearch(searchText, companyId, []);
    }
  }, [searchText, debouncedHandleSearch]);

  const handlePopupScroll = async (e, companyId) => {
    const { target } = e;
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      hasMoreResultsRef.current
    ) {
      const newStart = start + pageSize;
      setStart(newStart);
      await debouncedLoadMoreOptions(newStart, searchText, companyId, options);
    }
  };

  const handleScrollPopup = async (e, entity, companyId) => {
    const { target } = e;
    setLoading(true);
    setLoadingScroll(true);
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      hasMoreResultsRef.current
    ) {
      const newStart = start + pageSize;
      setStart(newStart);
      try {
        const { data } = await fetchData(
          newStart,
          pageSize,
          searchText,
          companyId,
          '',
          searchText
        );

        setOptions([...options, ...data.result]);
      } catch (err) {
        console.log('err: ', err);
        notification.error({ message: err.response?.data?.message });
      }
    }
    setLoading(false);
    setLoadingScroll(false);
  };

  // useEffect(() => {
  //   loadMoreOptions();
  //   return () => {
  //     if (!isStepMode) {
  //       debouncedLoadMoreOptions.cancel();
  //       debouncedHandleSearch.cancel();
  //     }
  //   };
  // }, []);

  return {
    options,
    setOptions,
    handlePopupScroll,
    handleScrollPopup,
    handleSearch: debouncedHandleSearch,
    isLoading,
    setLoading,
    searchText,
    valueNotFound,
    setCompanyId,
    setValueNotFound,
    setStart,
    isLoadingScroll,
    setLoadingScroll,
    loadMoreOptions,
  };
};

export default useInfiniteScrollWithSearch;
