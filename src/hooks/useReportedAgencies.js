import { useState, useEffect, useCallback } from 'react';
import {
  getReportedAgencies,
  deleteReportedAgency,
} from '../services/reportedAgencies';
import { notification } from 'antd';

// Custom hook for search with debounce
const useReportedAgencies = () => {
  const [reportedAgencies, setReportedAgencies] = useState({
    result: { data: [] },
  });
  const [isLoading, setLoading] = useState(false);
  const [recordTable, setRecordTable] = useState({});
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [confirmLoadingDelete, setConfirmLoadingDelete] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchName, setSearchName] = useState('');
  const [status, setStatus] = useState('ALL');

  const handleDelete = async () => {
    try {
      if (isLoading) return;
      setLoading(true);
      await deleteReportedAgency(recordTable.id);
      await populateReportedAgencies(page, pageSize, searchName, status);
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
      notification.success({
        message: 'Success',
        description: `Success delete report agency ${recordTable.companyName}`,
      });
      setLoading(false);
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      notification.error({
        message: 'Error',
        description: errorMessage,
      });
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
      setLoading(false);
    }
  };

  const handleCancelDelete = () => {
    if (isLoading) return;
    setDeleteModalOpen(false);
  };

  const populateReportedAgencies = useCallback(
    async (page, pageSize, searchName, status) => {
      setLoading(true);
      const { data } = await getReportedAgencies(
        page,
        pageSize,
        searchName ?? '',
        status
      );
      setPage(page);
      setPageSize(pageSize);
      setSearchName(searchName);
      setReportedAgencies(data);
      setLoading(false);
    },
    []
  );

  useEffect(() => {
    populateReportedAgencies(page, pageSize, searchName, status);
  }, [searchName, status]);

  return {
    isLoading,
    reportedAgencies,
    setReportedAgencies,
    populateReportedAgencies,
    handleDelete,
    handleCancelDelete,
    recordTable,
    setRecordTable,
    deleteModalOpen,
    setDeleteModalOpen,
    confirmLoadingDelete,
    setConfirmLoadingDelete,
    page,
    setPage,
    pageSize,
    setPageSize,
    setSearchName,
    searchName,
    status,
    setStatus
  };
};

export default useReportedAgencies;
