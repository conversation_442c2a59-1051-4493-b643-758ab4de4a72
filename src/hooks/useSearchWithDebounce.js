import { useState, useEffect, useCallback } from 'react';

// Custom hook for search with debounce
const useSearchWithDebounce = (searchApi) => {
  const [searchOptions, setSearchOptions] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [isLoading, setLoading] = useState(false);
  const handleSearch = useCallback(
    debounce(async (value) => {
      setLoading(true);
      try {
        const { data } = await searchApi(value);
        setSearchOptions(data || []);
      } catch (error) {
        setSearchOptions([]);
      } finally {
        setLoading(false);
      }
    }, 500),
    []
  );

  useEffect(() => {
    if (!searchText || searchText === '') return;
    handleSearch(searchText);
  }, [handleSearch, searchText]);

  return { searchOptions, setSearchText, isLoading, searchText };
};

// Helper function for debounce
const debounce = (func, delay) => {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
};

export default useSearchWithDebounce;
