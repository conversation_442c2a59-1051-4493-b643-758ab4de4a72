import { useState, useEffect, useCallback } from 'react';
import { getAddressDetailsByPlaceId } from '../services/googleMap';
import { getCounties, getCountries } from '../services/bullhorn';

// Custom hook for search with debounce
const useGoogleMapAddressDetails = (
  setValue,
  openNotificationWithIcon,
  setFor
) => {
  const [placeId, setPlaceId] = useState('');
  const [isLoading, setLoading] = useState(false);

  const handleGetCounties = async (countryId) => {
    try {
      const { data } = await getCounties({
        filter: '',
        countryId,
      });
      return data.result;
    } catch (error) {
      return [];
    }
  };

  const handleParsingCountyOrState = (counties = [], data) => {
    if (counties.length === 0) return;
    const findCounty = counties?.find(
      (item) => item.label?.toLowerCase() === data?.county?.toLowerCase()
    )?.value;
    const findState = counties?.find(
      (item) => item.label?.toLowerCase() === data?.state?.toLowerCase()
    )?.value;
    const selectedCountyOrState =
      findCounty || findState || data?.county || data?.state;
    return selectedCountyOrState;
  };

  const handleSearch = useCallback(
    async (place_id) => {
      if (!place_id || place_id === '') return;
      setLoading(true);
      try {
        const { data } = await getAddressDetailsByPlaceId(place_id);
        if (openNotificationWithIcon) {
          if (!data.county && !data.city) {
            if (placeId === '') return;
            openNotificationWithIcon('info');
            setValue('location', '');
            setPlaceId('');
          }
        }

        if (setFor === 'companyBilling') {
          setValue('clientCorporation.billingZip', data.zip);
          setValue('clientCorporation.billingCounty', data.county);
          setValue('clientCorporation.billingCountySelect', data.county);
          setValue('clientCorporation.billingCity', data.city);
          setValue('clientCorporation.billingState', data.country);
        } else if (setFor === 'companyAddress') {
          setValue('clientCorporation.zip', data.zip);
          setValue('clientCorporation.city', data.city);
          // setValue('clientCorporation.state', data?.country || data?.county);
          setValue('clientCorporation.companyWebsite', data.website);
          setValue('clientCorporation.mainPhone', data.phone);
          setValue('clientCorporation.photo', data.photos[0]);
          const country = await getCountries(data.country);
          const stateId = country?.data?.result?.[0]?.value;

          const counties = await handleGetCounties(stateId);

          setValue('clientCorporation.stateId', `${stateId}`);
          setValue('clientCorporation.stateSelected', `${stateId}`);

          const selectedCountyOrState = handleParsingCountyOrState(
            counties,
            data
          );
          if (selectedCountyOrState) {
              setValue('clientCorporation.county', selectedCountyOrState);
              setValue('clientCorporation.countySelect', selectedCountyOrState);
          }
        } else if (setFor === 'contactAddress') {
          setValue('clientContact.zip', data.zip);
          setValue('clientContact.countySelect', data.county);
          setValue('clientContact.city', data.city);
          setValue('clientContact.state',data?.country || data?.county);
          const country = await getCountries(data.country);
          if (country) {
            setValue(
              'clientContact.stateId',
              `${country?.data?.result[0].value}`
            );
            setValue(
              'clientContact.stateSelected',
              `${country?.data?.result[0].value}`
            );
          }
        } else {
          setValue('zip', data.zip);
          setValue('city', data.city);
          const country = await getCountries(data.country || data?.county);

          const stateId = country?.data?.result?.[0]?.value;
          const counties = await handleGetCounties(stateId);
          const selectedCountyOrState = handleParsingCountyOrState(
            counties,
            data
          );  

          if (selectedCountyOrState) {
            setValue('county', selectedCountyOrState);
            setValue('countySelect', selectedCountyOrState);

            setValue('stateId', stateId);
            setValue('stateSelected', stateId);
          }
        }
      } catch (error) {
        return error;
      } finally {
        setLoading(false);
      }
    },
    [placeId]
  );

  useEffect(() => {
    handleSearch(placeId);
  }, [handleSearch, placeId]);

  return { setPlaceId, isLoading, placeId };
};

export default useGoogleMapAddressDetails;
