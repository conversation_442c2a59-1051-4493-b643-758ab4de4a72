import { useState } from 'react';

const useLogin = (loginApi) => {
  const [loading, setLoading] = useState(false);
  const handleLogin = async (formData) => {
    try {
      setLoading(true);
      const { data } = await loginApi(formData);
      setLoading(false);
      return data;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  return { loading, handleLogin };
};

export default useLogin;
