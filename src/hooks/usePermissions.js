import { useAuth } from '../store/auth';
import { useViewAs } from '../store/viewAs';
import {
  getUserViewAsRole,
  getUserViewAsBullhorn,
} from '../helpers/getUserViewAs';
import { userRole } from '../constants/common.constant';

export const usePermissions = () => {
  const { profile: data, clearAuth } = useAuth();
  const isUserRole = getUserViewAsRole() === userRole.BASIC_USER;
  const isEmptyBullhornApiKey = !getUserViewAsBullhorn();
  const { profileUser, viewerAsUser, clearViewAs } = useViewAs();

  if (!profileUser?.userPermissions && viewerAsUser) {
    clearAuth();
    clearViewAs();
  }

  const userPermissions = viewerAsUser
    ? profileUser?.userPermissions
    : data?.user?.userPermissions || [];
  let formattedPermission = {};
  userPermissions?.forEach((up) => {
    formattedPermission[up.permission.keyCode] = {
      allowRead: up.allowRead,
      allowWrite: up.allowWrite,
    };
  });

  // Check is have bullhorn api if role is USER
  if (isUserRole && isEmptyBullhornApiKey) {
    formattedPermission = {};
  }

  const publicKeyCode = {
    SETTINGS: {
      allowRead: true,
      allowWrite: true,
    },
  };

  return {
    formattedPermissionForRoutes: { ...formattedPermission, ...publicKeyCode },
    rawPermissions: userPermissions,
  };
};
