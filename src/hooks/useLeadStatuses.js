// useLeadStatuses.js
import { useState, useEffect } from 'react';
import {
  bulkUpdateLeadStatus,
  fetchLeadStatuses,
} from '../services/jobLeadStatuses';
import { v4 as uuid } from 'uuid';
import { notification } from 'antd';

const useLeadStatuses = (onFinish, leadSheetId) => {
  const [statuses, setStatuses] = useState([]);
  const [isSubmitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [nonDeletableStatuses, setNonDeletableStatuses] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    const fetchStatuses = async () => {
      const { data: fetchedStatuses } = await fetchLeadStatuses(leadSheetId);

      const isOldStatuses = fetchedStatuses?.some(
        (status) => status.orderOfDisplay === 0
      );

      if (fetchedStatuses?.length > 0) {
        let sortedData = fetchedStatuses?.sort(
          (a, b) => a.orderOfDisplay - b.orderOfDisplay
        );
        if (isOldStatuses) {
          sortedData = sortedData.map((status, index) => ({
            ...status,
            orderOfDisplay: index + 1,
          }));
        }
        setStatuses([...sortedData]);
      } else {
        setStatuses(fetchedStatuses);
      }
    };
    fetchStatuses().finally(() => setLoading(false));
  }, []);

  const handleStatusChange = (id, field, value) => {
    const updatedStatuses = [...statuses];
    const indexOfPassedInId = updatedStatuses.findIndex((a) => a.id == id);
    if (field === 'orderOfDisplay') {
      const indexOfOldStatus = updatedStatuses.findIndex(
        (a) => a[field] === value
      );
      updatedStatuses[indexOfOldStatus][field] =
        updatedStatuses[indexOfPassedInId][field];
    }
    updatedStatuses[indexOfPassedInId][field] = value;
    updatedStatuses[indexOfPassedInId].action = 'Update';

    setStatuses(
      updatedStatuses.sort((a, b) => a.orderOfDisplay - b.orderOfDisplay)
    );
  };

  const handleStatusDelete = (id) => {
    let updatedStatuses = [...statuses];
    let statusToDelete = updatedStatuses.find((status) => status.id == id);
    statusToDelete.isDeleted = true;
    statusToDelete.isNew = false;
    updatedStatuses = updatedStatuses.filter((a) => a.id != statusToDelete.id);

    updatedStatuses = updatedStatuses
      .filter((a) => !a.isDeleted)
      .map((status, index) => ({
        ...status,
        orderOfDisplay: index,
      }));
    console.log('updatedStatuses: ', updatedStatuses);
    updatedStatuses.push(statusToDelete);
    setStatuses(updatedStatuses);
  };

  const handleSubmit = async (confirmDelete = false) => {
    await updateLeadStatus(statuses, confirmDelete);
  };

  const handleAddStatus = () => {
    const newStatus = {
      id: uuid(),
      name: '',
      orderOfDisplay:
        statuses?.filter((status) => !status.isDeleted)?.length + 1,
      action: 'Insert',
      isNew: true,
      color: '#f5f5f6',
    };
    setStatuses([...statuses, newStatus]);
  };

  const updateLeadStatus = async (statuses, confirmDelete = false) => {
    setSubmitting(true);
    const isNameEmpty = statuses.some(
      (status) => !status.isDeleted && !status?.name?.trim()
    );
    const nonDeletable = statuses.filter(
      (status) => status.isDeleted && +status.total_company > 0
    );
    setNonDeletableStatuses(nonDeletable);
    if (nonDeletable?.length > 0 && !confirmDelete) {
      setShowDeleteModal(true);
      setSubmitting(false);
      return;
    }
    if (isNameEmpty) {
      notification.warning({
        description: 'Name is required!',
      });

      setSubmitting(false);
      return;
    }

    await bulkUpdateLeadStatus(
      statuses.map((status) => ({
        ...status,
        leadSheetId,
      }))
    );
    setSubmitting(false);
    onFinish();
  };

  return {
    statuses,
    handleStatusChange,
    handleStatusDelete,
    handleSubmit,
    handleAddStatus,
    setStatuses,
    isSubmitting,
    loading,
    nonDeletableStatuses,
    setNonDeletableStatuses,
    showDeleteModal,
    setShowDeleteModal,
  };
};

export default useLeadStatuses;
