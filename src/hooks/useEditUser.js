import { useState } from 'react';
import {
  getUserDetail,
  updatePermissions,
  getUserRoles,
} from '../services/users';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { message } from 'antd';
import { useAuth } from '../store/auth';
import { useViewAs } from '../store/viewAs';
import { filterAndTransformRoles } from '../helpers/filteredRoles';
import { getUserViewAs, isViewAs } from '../helpers/getUserViewAs';

const useEditUser = (userId = '') => {
  const { handleSubmit, control, getValues, setValue } = useForm();
  const { profile, setAuth } = useAuth();
  const { profileUser, viewerAsUser, clearViewAs, setViewAs } = useViewAs();
  const queryClient = useQueryClient();
  const { id } = useParams();
  const [user, setUser] = useState({});
  const [loading, setLoading] = useState(true);
  const [flagSave, setFlagSave] = useState(true);

  useQuery(
    ['GET_DETAIL_USER_EDIT'],
    {
      queryFn: async () => {
        try {
          // setLoading(false);
          setFlagSave(true);
          const { data } = await getUserDetail(userId || id || getUserViewAs());

          setValue('email', data?.email);
          setValue('role', data?.role?.id);
          setValue('roleName', data?.role?.name);
          setValue('username', data?.username);
          setValue('fullName', data?.fullName);
          setValue('organizationId', data?.organizationId);
          setValue('organization', data?.organization?.name);

          setValue('jobTitle', data?.jobTitle);
          setValue('disableAccount', data?.disableAccount);
          setValue('timezone', data?.timezone);
          setValue('linkedinUrl', data?.linkedinUrl);

          setUser(data);
        } catch (err) {
          setLoading(false);
        }
      },
    },
    []
  );

  const roles = useQuery(['GET_USER_ROLES'], async () => {
    const { data } = await getUserRoles();
    const output = filterAndTransformRoles({
      data,
      viewerAsUser,
      viewAsRole: profileUser?.role.keyCode,
      userLoginRole: profile?.user?.role?.keyCode,
    });
    setLoading(false);
    return output;
  });

  const fatchDetail = async () => {
    setFlagSave(true);
    // queryClient.invalidateQueries('GET_DETAIL_USER_EDIT');
  };

  const onSubmit = async (permissionList = []) => {
    try {
      setLoading(true);
      const body = {
        roleId: getValues()?.role?.id || user?.role?.id,
        // permissions:
        //   permissionList?.length === 0 ? user?.userPermissions : permissionList,
        username: getValues().username || user?.username,
        email: getValues().email || user?.email,
        fullName: getValues().fullName || user?.fullName,
        organizationId: getValues().organizationId || user?.organizationId,
        organization: getValues().organization || user?.organization,
        avatarId: getValues().avatarId || user?.avatarId,
        // new values
        disableAccount: getValues()?.disableAccount || user?.disableAccount,
        jobTitle: getValues()?.jobTitle || user?.jobTitle,
        timezone: getValues()?.timezone || user?.timezone,
        linkedinUrl: getValues()?.linkedinUrl || user?.linkedinUrl,
      };
      console.log(body);
      await updatePermissions({
        userId: userId || user?.id || getValues('id'),
        permissionsPayload: body,
      });
      fatchDetail();
      const currentUser = isViewAs() ? profileUser : profile?.user;

      clearViewAs();
      const { data } = await getUserDetail(getUserViewAs());
      isViewAs()
        ? setViewAs({ profileUser: data })
        : setAuth({ profile: { user: data } });

      message.success(`Success Update User ${user.email}`);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      message.error(`Error updating user: ${error.message}`);
    }
  };

  return {
    user,
    setUser,
    handleSubmit,
    onSubmit,
    control,
    roles,
    loading,
    setFlagSave,
    flagSave,
    getValues,
    setValue,
  };
};
export default useEditUser;
