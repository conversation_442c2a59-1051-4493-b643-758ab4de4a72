import { useState, useEffect, useRef, useCallback } from 'react';
import _debounce from 'lodash/debounce';
import { notification } from 'antd';

const useInfinitePaginationWithSearch = (
  fetchData,
  pageSize = 10,
  debounceTime = 300,
  searchDebounceTime = 200
) => {
  const [options, setOptions] = useState([]);
  const [start, setStart] = useState(0);
  const [isLoading, setLoading] = useState(false);
  const [isSilentLoading, setSilentLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [companyId, setCompanyId] = useState('');
  const [valueNotFound, setValueNotFound] = useState('');
  const prevSearchTextRef = useRef('');
  const hasMoreResultsRef = useRef(true);

  const loadMoreOptions = async (
    start,
    searchText,
    companyID,
    options,
    silentFetch = false
  ) => {
    if (silentFetch) {
      setSilentLoading(true);
    } else {
      setLoading(true);
      setOptions([]);
    }

    const pageLimit = options?.length ? pageSize : pageSize * 2;
    const data = await fetchData(
      start,
      pageLimit,
      searchText,
      companyID
    )
      .then(({ data }) => {
        return data;
      })
      .catch((err) => {
        notification.error({ message: err.response?.data?.message });
        return err;
      });
    if (data?.response?.status === 400) setLoading(false);
    let moreOptions = data.result || [];
    if (options?.length) {
      moreOptions = [...options, ...moreOptions];
    }

    const isNameExists = moreOptions.some(
      (option) => option.name === searchText
    );
    if (!isNameExists) {
      if (searchText !== '') {
        setValueNotFound(searchText);
      }
    }
    if (moreOptions && moreOptions.length === 0) {
      setLoading(false);
      hasMoreResultsRef.current = false;
      setLoading(false);
    } else {
      setOptions((prevOptions) => [
        ...(silentFetch ? [] : prevOptions),
        ...(moreOptions || []),
      ]);
    }
    setSilentLoading(false);
    setLoading(false);
  };

  const debouncedLoadMoreOptions = useRef(
    _debounce(loadMoreOptions, debounceTime)
  ).current;

  const debouncedHandleSearch = useCallback(
    _debounce((value, companyId) => {
      setStart(0);
      setSearchText(value);
      hasMoreResultsRef.current = true;
      debouncedLoadMoreOptions(0, value, companyId);
    }, searchDebounceTime),
    [debouncedLoadMoreOptions]
  );

  useEffect(() => {
    if (searchText !== prevSearchTextRef.current) {
      prevSearchTextRef.current = searchText;
      setOptions([]);
      setStart(0);
      hasMoreResultsRef.current = true;
      debouncedHandleSearch(searchText, companyId);
    }
  }, [searchText, debouncedHandleSearch]);

  const handleChangePagination = async (currentPage, tblPageSize) => {
    if (currentPage * tblPageSize !== options.length) {
      return;
    }
    const newStart = currentPage * tblPageSize;
    setStart(newStart);
    await debouncedLoadMoreOptions(
      newStart,
      searchText,
      companyId,
      options,
      true
    );
  };

  useEffect(() => {
    return () => {
      debouncedLoadMoreOptions.cancel();
      debouncedHandleSearch.cancel();
    };
  }, []);

  return {
    options,
    setOptions,
    handleChangePagination,
    handleSearch: debouncedHandleSearch,
    isLoading,
    setLoading,
    searchText,
    valueNotFound,
    setCompanyId,
    setValueNotFound,
    setStart,
    isSilentLoading,
  };
};

export default useInfinitePaginationWithSearch;
