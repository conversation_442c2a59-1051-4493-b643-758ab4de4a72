import { useMutation } from '@tanstack/react-query';
import { signIn } from '../services/auth';
import { useAuth } from '../store/auth';
import { useOnboarding } from '../store/onboarding';

export const useLogin = () => {
  const { setAuth } = useAuth();
  const { setOnboardingRequired } = useOnboarding();

  return useMutation(
    async ({ emailOrUsername, password }) => {
      const { data } = await signIn({ emailOrUsername, password });
      return data;
    },
    {
      onSuccess: (data) => {
        setAuth({ profile: data.result });

        // Set the onboardingRequired state based on the passwordChangeRequired flag
        const passwordChangeRequired = data?.result?.passwordChangeRequired;
        setOnboardingRequired(!!passwordChangeRequired);
      },
    }
  );
};
