import { useState } from 'react';
import {
  createReportedAgencyAlias,
  getFuzzySearch,
} from '../services/reportedAgencies';
import { message, notification } from 'antd';

const useReportedAgencyModal = () => {
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [companyName, setCompanyName] = useState('');
  const [listAgencyName, setListAgencyName] = useState([]);
  const [isLoadingListAgency, setIsLoadingListAgency] = useState(false);
  const [isLoadingNewListAgency, setIsLoadingNewListAgency] = useState(false);
  const showModal = (companyName) => {
    setOpen(true);
    setCompanyName(companyName);
  };

  const handleOk = async (fuzzySearch, country) => {
    setConfirmLoading(false);
    true;
    try {
      await createReportedAgencyAlias({
        companyName: companyName.trim(),
        aliasCompanyNames: fuzzySearch?.map((item) => item?.value),
        country: country,
      });
      setOpen(false);
      setConfirmLoading(false);
      notification.success({
        message: 'Successfully',
        description: 'Add Reported Agency Successfully',
      });
    } catch (err) {
      setConfirmLoading(false);
      notification.error({
        message: 'Error',
        description: 'Some thing went wrong',
      });
    }
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const handleGetFuzzySearch = async (nameCompany) => {
    setIsLoadingListAgency(true);
    try {
      const { data } = await getFuzzySearch(nameCompany.trim());
      setListAgencyName(data.result);
      setIsLoadingListAgency(false);
    } catch (error) {
      setIsLoadingListAgency(false);
      console.log('error', error);
    }
  };

  const handleGetNewFuzzySearch = async (nameCompany) => {
    setIsLoadingNewListAgency(true);
    try {
      const { data } = await getFuzzySearch(nameCompany.trim());
      setListAgencyName(data.result);
      setIsLoadingNewListAgency(false);
    } catch (error) {
      setIsLoadingNewListAgency(false);
      console.log('error', error);
    }
  };

  return {
    open,
    confirmLoading,
    companyName,
    listAgencyName,
    isLoadingListAgency,
    isLoadingNewListAgency,
    showModal,
    handleOk,
    handleCancel,
    handleGetFuzzySearch,
    handleGetNewFuzzySearch,
    setCompanyName,
  };
};

export default useReportedAgencyModal;
