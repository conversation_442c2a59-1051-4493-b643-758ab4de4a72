import { useParams } from 'react-router-dom';
import {
  getPinnedJobsSearchById,
  getSearchById,
  getPaginationSearchById,
} from '../services/search';
import { useEffect, useState } from 'react';

const useSearchDetails = () => {
  let { searchId } = useParams();
  const [searchData, setSearchData] = useState();
  const [pinnedJobsData, setPinnedJobsData] = useState();
  const [isLoading, setLoading] = useState(false);
  const [isLoadingPinned, setLoadingPinned] = useState(false);
  const [paginationSearch, setPaginationSearch] = useState({});
  const [isLoadingPagination, setLoadingPagination] = useState(false);

  const populateSearchData = async (extendedFilterObject, page = 1) => {
    setLoading(true);
    const { data } = await getSearchById(searchId, extendedFilterObject, page);
    setSearchData(data.result);
    setLoading(false);
  };

  const populatePinnedJobsData = async () => {
    setLoadingPinned(true);
    const { data } = await getPinnedJobsSearchById(searchId);
    setPinnedJobsData(data.result);
    setLoadingPinned(false);
  };

  const getPaginationSearch = async (extendedFilterObject, page) => {
    setLoadingPagination(true);
    const { data } = await getPaginationSearchById(
      searchId,
      extendedFilterObject,
      page
    );
    setPaginationSearch(data.result);
    setLoadingPagination(false);
  };

  useEffect(() => {
    if (searchId) {
      populateSearchData();
      populatePinnedJobsData();
      getPaginationSearch();
    }
  }, [searchId]);
  return {
    searchData,
    isLoading,
    populateSearchData,
    searchId,
    populatePinnedJobsData,
    isLoadingPinned,
    pinnedJobsData,
    isLoadingPagination,
    paginationSearch,
    getPaginationSearch,
    setPaginationSearch,
  };
};
export default useSearchDetails;
