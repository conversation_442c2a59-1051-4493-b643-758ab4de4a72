import { useState } from 'react';
import { getSyncSearches, postSearch } from '../services/search';
import { useQuery, useQueryClient } from '@tanstack/react-query';

const useSyncSearches = (isGetAll) => {
  const queryClient = useQueryClient();
  const [searches, setSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortField, setSortField] = useState('searchName');
  const [sortOrder, setSortOrder] = useState('ASC');
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: isGetAll ? 100 : 10,
    totalCount: 0,
  });

  const { data } = useQuery(
    ['LIST_JOB_SYNCS', sortField, sortOrder, pagination, searchText],
    {
      queryFn: async () => {
        try {
          setLoading(true);
          const { data } = await getSyncSearches(
            sortField,
            sortOrder,
            pagination.page,
            pagination.limit,
            searchText
          );
          setSearches(data.result.data);
          setPagination({
            page: data.result.currentPage,
            limit: data.result.pageSize,
            totalCount: data.result.totalCount,
          });
          setLoading(false);

          return data.result.data;
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      },
      keepPreviousData: false,
      refetchOnMount: true, // Ensures re-fetching every time the component is mounted
    },
    []
  );

  const fetchSearches = async () => {
    queryClient.invalidateQueries('LIST_JOB_SEARCH');
  };

  const createSearch = async (payload) => {
    setLoading(true);
    try {
      await postSearch(payload);
      setSearches([...searches, payload]);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return {
    searches,
    loading,
    error,
    createSearch,
    fetchSearches,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    pagination,
    setPagination,
    isGetAll,
    data,
    setSearchText,
    searchText,
  };
};
export default useSyncSearches;
