import { useState, useEffect, useCallback } from 'react';
import { getJobsLeads } from '../services/jobLeads';

// Custom hook for search with debounce
const useJobLeads = () => {
    const [jobLeads, setJobLeads] = useState([]);
    const [isLoading, setLoading] = useState(false);

  const populateJobLeads = useCallback(async (leadSheetId = '') => {
    if(!leadSheetId) return;
    setLoading(true);
    const { data } = await getJobsLeads(leadSheetId);
    if (data?.length > 0) {
      const sortedData = data?.sort(
        (a, b) => a.orderOfDisplay - b.orderOfDisplay
      );
      setJobLeads(sortedData);
    } else {
      setJobLeads(data);
    }

    setLoading(false);
  }, []);

    useEffect(() => {
        populateJobLeads();
    }, []);

    return {
        isLoading,
        jobLeads,
        setJobLeads,
        reloadJobLeads: populateJobLeads,
    };
};

export default useJobLeads;