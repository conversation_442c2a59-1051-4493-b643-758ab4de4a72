import { useState } from 'react';

const useRegistration = (registerApi) => {
  const [loading, setLoading] = useState(false);
  const handleRegistration = async (formData) => {
    try {
      setLoading(true);
      const { data } = await registerApi(formData);
      setLoading(false);
      return data;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  return { loading, handleRegistration };
};

export default useRegistration;
