import { useEffect, useState } from 'react';
import { getSearches, postSearch } from '../services/search';
import { useQuery, useQueryClient } from '@tanstack/react-query';

const useSearches = (isGetAll) => {
  const queryClient = useQueryClient();
  const [searches, setSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortField, setSortField] = useState('active_subprocess_id');
  const [sortOrder, setSortOrder] = useState('ASC');
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: isGetAll ? 100 : 10,
    totalCount: 0,
  });
  
  const { data, isLoading, refetch } = useQuery(
    ['LIST_JOB_SEARCH', sortField, sortOrder, pagination, searchText],
    {
      queryFn: async () => {
        try {
          setLoading(true);
          const { data } = await getSearches(
            sortField,
            sortOrder ?? 'ASC',
            pagination.page,
            pagination.limit,
            searchText
          );
          setSearches(data.result.data);
          setPagination({
            page: data.result.currentPage,
            limit: data.result.pageSize,
            totalCount: data.result.totalCount,
          });
          setLoading(false);
          return data.result.data;
        } catch (err) {
          setError(err);
          setLoading(false);
        }
      },
    },
    []
  );

  // useEffect(() => {
  //   // if(!sortOrder) return;
  //   console.log("sortOrder: ", sortOrder)
  //   refetch();
  // }, [sortOrder]);

  const fetchSearches = async () => {
    queryClient.invalidateQueries('LIST_JOB_SEARCH');
  };

  const createSearch = async (payload) => {
    setLoading(true);
    try {
      await postSearch(payload);
      setSearches([...searches, payload]);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return {
    searches,
    loading,
    error,
    createSearch,
    fetchSearches,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    pagination,
    setPagination,
    isGetAll,
    isLoading,
    data,
    setSearchText,
    searchText,
  };
};
export default useSearches;
