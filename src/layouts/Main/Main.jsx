/* eslint-disable react/prop-types */
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Sidebar from '../../components/Sidebar';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import routes from '../../components/Sidebar/consts';
import { usePermissions } from '../../hooks/usePermissions';
import { Layout } from 'antd';
import {
  getUserViewAsLicense,
  getUserViewAsLicenseType,
  getUserViewAsPermissions,
  getUserViewAsRole,
} from '../../helpers/getUserViewAs';
import { mainProtectedRoutes } from '../../utils/common';
import { SALES, SUPER_ADMIN } from '../../constants/common.constant';

const Main = (props) => {
  const { children } = props;
  const { ...rest } = props;
  const location = useLocation();
  const navigate = useNavigate();
  console.log('location', location.pathname);
  const pathName =
    location.pathname.split('/')?.[1]?.replaceAll('-', ' ')?.capitalize() ||
    'Dashboard';
  const [open, setOpen] = React.useState(true);
  const [currentRoute, setCurrentRoute] = React.useState(pathName);
  const [mode, setMode] = React.useState('website');
  const currentUserRole = getUserViewAsRole();
  const currentPermissions = getUserViewAsPermissions() || [];
  const currentUserPlan = getUserViewAsLicense();
  const currentUserLicenseType = getUserViewAsLicenseType()
  // const finalRoutes = routes.filter((route) =>
  //   route.roles.includes(currentUserRole)
  // );
  const finalRoutes =
    currentPermissions?.length > 0
      ? routes.filter((route) =>
          currentPermissions?.includes(route?.permission)
        )
      : [];

  React.useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200
        ? (setOpen(false), setMode('mobile'))
        : (setOpen(true), setMode('website'))
    );
  }, []);
  React.useEffect(() => {
    getActiveRoute(finalRoutes);
    if (currentUserRole === SUPER_ADMIN || currentUserRole === SALES) return;

    const routesValidator = (pathname) => {
      let isLicenseAllowed = true;
      const isOrgViewing = pathname?.includes('user-management/company');

      const currentRoute = mainProtectedRoutes.find((route) =>
        pathname?.includes(route.path)
      );

      if (!currentRoute) return true;

      // checking authorization for roles
      const isRoleAllowed = currentRoute.roles.includes(currentUserRole);
      // checking authorization for permissions
      const isPermissionAllowed = currentPermissions.includes(
        currentRoute.permission
      );
      if (currentRoute?.license) {
        isLicenseAllowed = currentRoute.license.includes(currentUserLicenseType);
      }

      return isOrgViewing
        ? isPermissionAllowed && isRoleAllowed && isLicenseAllowed
        : (isPermissionAllowed || isRoleAllowed) && isLicenseAllowed;
    };

    const isAllowAccess = routesValidator(location.pathname);

    if (finalRoutes.length === 0 || !isAllowAccess) {
      setCurrentRoute('No Access');
      navigate('/no-access');
    }
  }, [location.pathname]);

  const getActiveRoute = (routes) => {
    let activeRoute = 'Dashboard';
    for (let i = 0; i < routes.length; i++) {
      const officalPath = routes[i].path?.includes('?')
        ? routes[i].path?.split('?')[0]
        : routes[i].path;
      if (window.location.href.indexOf('/' + officalPath) !== -1) {
        setCurrentRoute(routes[i].name);
      }
    }
    return activeRoute;
  };
  const getActiveNavbar = (routes) => {
    let activeNavbar = false;
    for (let i = 0; i < routes.length; i++) {
      if (
        window.location.href.indexOf(routes[i].layout + routes[i].path) !== -1
      ) {
        return routes[i].secondary;
      }
    }
    return activeNavbar;
  };
  document.documentElement.dir = 'ltr';

  return (
    <Layout className="overflow-hidden lg:overflow-visible">
      <Sidebar
        open={open}
        onClose={() => setOpen(false)}
        setOpen={setOpen}
        mode={mode}
        routes={finalRoutes}
      />
      {/* Navbar & Main Content */}
      <Layout className="bg-white overflow-auto lg:overflow-visible min-h-screen main-content-container">
        <Navbar
          onOpenSidenav={() => setOpen(true)}
          logoText={'Horizon UI Tailwind React'}
          brandText={currentRoute}
          secondary={getActiveNavbar(finalRoutes)}
          {...rest}
        />
        <div className="main-padding h-full bg-gradient-to-br from-gray-50/50 via-white/50 to-gray-50/50">
          {children}
        </div>
        <footer className="mt-auto p-2">
          <Footer />
        </footer>
      </Layout>
    </Layout>
  );
};
export default Main;
