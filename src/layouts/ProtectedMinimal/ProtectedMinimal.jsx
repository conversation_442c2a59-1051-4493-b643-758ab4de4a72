/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { SUPER_ADMIN } from '../../constants/common.constant';
import {
  getUserViewAsLicense,
  getUserViewAsPermissions,
  getUserViewAsRole,
} from '../../helpers/getUserViewAs';
import { mainProtectedRoutes } from '../../utils/common';
import routes from '../../components/Sidebar/consts';

const ProtectedMinimal = (props) => {
  const { children } = props;
  const location = useLocation();
  const navigate = useNavigate();

  const currentUserPlan = getUserViewAsLicense();
  const currentUserRole = getUserViewAsRole();
  const currentPermissions = getUserViewAsPermissions() || [];

  const finalRoutes = routes.filter((route) =>
    currentPermissions.includes(route.permission)
  );

  React.useEffect(() => {
    if (currentUserRole === SUPER_ADMIN) return;

    const routesValidator = (pathname) => {
      let isLicenseAllowed = true;

      const currentRoute = mainProtectedRoutes.find((route) =>
        pathname?.includes(route.path)
      );

      if (!currentRoute) return true;

      // checking authorization for roles
      const isRoleAllowed = currentRoute.roles.includes(currentUserRole);
      // checking authorization for permissions
      const isPermissionAllowed = currentPermissions.includes(
        currentRoute.permission
      );
      if (currentRoute?.license) {
        isLicenseAllowed = currentRoute.license.includes(currentUserPlan);
      }

      return (isPermissionAllowed || isRoleAllowed) && isLicenseAllowed;
    };

    const isAllowAccess = routesValidator(location.pathname);

    if (finalRoutes.length === 0 || !isAllowAccess) {
      navigate('/no-access');
    }
  }, [location.pathname]);

  return (
    <>
      <div className="minimal-layout-wrapper">{children}</div>
    </>
  );
};

export default ProtectedMinimal;
