export const COMMON_STRINGS = {
  NAME: 'Name',
  SEARCH_NAME: 'Search Name',
  LOCATION: 'Location',
  KEYWORDS: 'Keywords',
  SALARY: 'Salary',
  DATE: 'Date',
  SEARCH_STATUS: 'Search Status',
  COUNT: 'Count',
  ACTION: 'Action',
  STATUS: 'Status',
  CREATE_NEW_LIST: 'Create New List',
  CREATE: 'Create',
  VACANCY: 'Vacancy',
  LEAD: 'Lead',
  OPPORTUNITY: 'Opportunity',
  FULL_NAME: 'Full Name',
  JOB_TITLE: 'Job Title',
  COMPANY: 'Company',
  PHONE: 'Phone',
  CONTACT_LOCATION: 'Contact Location',
  EMPLOYEES: 'Employees',
  INDUSTRY: 'Industry',
  EMAIL: 'Email',
  OCCUPATION: 'Occupation',
  SELECT_A_COUNTRY: 'Select A Country',
  SELECT_A_CONSULTANT: 'Select A Consultant',
  BULK_ADD_TO_BULLHORN: 'Add To Bullhorn',
  BULK_ADD_TO_CONTACT_LIST: 'Add To Contact List',
  BULK_ENRICH: 'Bulk Enrich',
  BULK_ADD_TO_SEQUENCE: 'Add to Sequence',
};

export const PRIMARY_COLOR = '#0891b2';

export const LICENSE_KEY = import.meta.env.VITE_CKEDITOR_KEY;

export const REDUCED_MATERIAL_COLORS = [
  { label: 'Red 50', color: '#ffebee' },
  { label: 'Purple 50', color: '#f3e5f5' },
  { label: 'Indigo 50', color: '#e8eaf6' },
  { label: 'Blue 50', color: '#e3f2fd' },
  { label: 'Cyan 50', color: '#e0f7fa' },
  { label: 'Teal 50', color: '#e0f2f1' },
  { label: 'Light green 50', color: '#f1f8e9' },
  { label: 'Lime 50', color: '#f9fbe7' },
  { label: 'Amber 50', color: '#fff8e1' },
  { label: 'Orange 50', color: '#fff3e0' },
  { label: 'Grey 50', color: '#fafafa' },
  { label: 'Blue grey 50', color: '#eceff1' },
  { label: 'Red 100', color: '#ffcdd2' },
  { label: 'Purple 100', color: '#e1bee7' },
  { label: 'Indigo 100', color: '#c5cae9' },
  { label: 'Blue 100', color: '#bbdefb' },
  { label: 'Cyan 100', color: '#b2ebf2' },
  { label: 'Teal 100', color: '#b2dfdb' },
  { label: 'Light green 100', color: '#dcedc8' },
  { label: 'Lime 100', color: '#f0f4c3' },
  { label: 'Amber 100', color: '#ffecb3' },
  { label: 'Orange 100', color: '#ffe0b2' },
  { label: 'Grey 100', color: '#f5f5f5' },
  { label: 'Blue grey 100', color: '#cfd8dc' },
  { label: 'Red 200', color: '#ef9a9a' },
  { label: 'Purple 200', color: '#ce93d8' },
  { label: 'Indigo 200', color: '#9fa8da' },
  { label: 'Blue 200', color: '#90caf9' },
  { label: 'Cyan 200', color: '#80deea' },
  { label: 'Teal 200', color: '#80cbc4' },
  { label: 'Light green 200', color: '#c5e1a5' },
  { label: 'Lime 200', color: '#e6ee9c' },
  { label: 'Amber 200', color: '#ffe082' },
  { label: 'Orange 200', color: '#ffcc80' },
  { label: 'Grey 200', color: '#eeeeee' },
  { label: 'Blue grey 200', color: '#b0bec5' },
  { label: 'Red 300', color: '#e57373' },
  { label: 'Purple 300', color: '#ba68c8' },
  { label: 'Indigo 300', color: '#7986cb' },
  { label: 'Blue 300', color: '#64b5f6' },
  { label: 'Cyan 300', color: '#4dd0e1' },
  { label: 'Teal 300', color: '#4db6ac' },
  { label: 'Light green 300', color: '#aed581' },
  { label: 'Lime 300', color: '#dce775' },
  { label: 'Amber 300', color: '#ffd54f' },
  { label: 'Orange 300', color: '#ffb74d' },
  { label: 'Grey 300', color: '#e0e0e0' },
  { label: 'Blue grey 300', color: '#90a4ae' },
  { label: 'Red 400', color: '#ef5350' },
  { label: 'Purple 400', color: '#ab47bc' },
  { label: 'Indigo 400', color: '#5c6bc0' },
  { label: 'Blue 400', color: '#42a5f5' },
  { label: 'Cyan 400', color: '#26c6da' },
  { label: 'Teal 400', color: '#26a69a' },
  { label: 'Light green 400', color: '#9ccc65' },
  { label: 'Lime 400', color: '#d4e157' },
  { label: 'Amber 400', color: '#ffca28' },
  { label: 'Orange 400', color: '#ffa726' },
  { label: 'Grey 400', color: '#bdbdbd' },
  { label: 'Blue grey 400', color: '#78909c' },
  { label: 'Red 500', color: '#f44336' },
  { label: 'Purple 500', color: '#9c27b0' },
  { label: 'Indigo 500', color: '#3f51b5' },
  { label: 'Blue 500', color: '#2196f3' },
  { label: 'Cyan 500', color: '#00bcd4' },
  { label: 'Teal 500', color: '#009688' },
  { label: 'Light green 500', color: '#8bc34a' },
  { label: 'Lime 500', color: '#cddc39' },
  { label: 'Amber 500', color: '#ffc107' },
  { label: 'Orange 500', color: '#ff9800' },
  { label: 'Grey 500', color: '#9e9e9e' },
  { label: 'Blue grey 500', color: '#607d8b' },
  { label: 'Red 600', color: '#e53935' },
  { label: 'Purple 600', color: '#8e24aa' },
  { label: 'Indigo 600', color: '#3949ab' },
  { label: 'Blue 600', color: '#1e88e5' },
  { label: 'Cyan 600', color: '#00acc1' },
  { label: 'Teal 600', color: '#00897b' },
  { label: 'Light green 600', color: '#7cb342' },
  { label: 'Lime 600', color: '#c0ca33' },
  { label: 'Amber 600', color: '#ffb300' },
  { label: 'Orange 600', color: '#fb8c00' },
  { label: 'Grey 600', color: '#757575' },
  { label: 'Blue grey 600', color: '#546e7a' },
  { label: 'Red 700', color: '#d32f2f' },
  { label: 'Purple 700', color: '#7b1fa2' },
  { label: 'Indigo 700', color: '#303f9f' },
  { label: 'Blue 700', color: '#1976d2' },
  { label: 'Cyan 700', color: '#0097a7' },
  { label: 'Teal 700', color: '#00796b' },
  { label: 'Light green 700', color: '#689f38' },
  { label: 'Lime 700', color: '#afb42b' },
  { label: 'Amber 700', color: '#ffa000' },
  { label: 'Orange 700', color: '#f57c00' },
  { label: 'Grey 700', color: '#616161' },
  { label: 'Blue grey 700', color: '#455a64' },
  { label: 'Red 800', color: '#c62828' },
  { label: 'Purple 800', color: '#6a1b9a' },
  { label: 'Indigo 800', color: '#283593' },
  { label: 'Blue 800', color: '#1565c0' },
  { label: 'Cyan 800', color: '#00838f' },
  { label: 'Teal 800', color: '#00695c' },
  { label: 'Light green 800', color: '#558b2f' },
  { label: 'Lime 800', color: '#9e9d24' },
  { label: 'Amber 800', color: '#ff8f00' },
  { label: 'Orange 800', color: '#ef6c00' },
  { label: 'Grey 800', color: '#424242' },
  { label: 'Blue grey 800', color: '#37474f' },
  { label: 'Red 900', color: '#b71c1c' },
  { label: 'Purple 900', color: '#4a148c' },
  { label: 'Indigo 900', color: '#1a237e' },
  { label: 'Blue 900', color: '#0d47a1' },
  { label: 'Cyan 900', color: '#006064' },
  { label: 'Teal 900', color: '#004d40' },
  { label: 'Light green 900', color: '#33691e' },
  { label: 'Lime 900', color: '#827717' },
  { label: 'Amber 900', color: '#ff6f00' },
  { label: 'Orange 900', color: '#e65100' },
  { label: 'Grey 900', color: '#212121' },
  { label: 'Blue grey 900', color: '#263238' },
];

export const MERTAGS_DEFINITION = [
  {
    id: 'RECIPIENT_NAME',
    label: 'Recipient Name',
    defaultValue: 'Recipient Name',
  },
  {
    id: 'RECIPIENT_EMAIL',
    label: 'Recipient Email',
    defaultValue: 'Recipient Email',
  },
  // {
  //   id: 'RECIPIENT_FAX',
  //   label: 'Recipient Fax',
  //   defaultValue: 'Recipient Fax',
  // },
  {
    id: 'RECIPIENT_PHONE_NUMBER',
    label: 'Recipient Phone Number',
    defaultValue: 'Recipient Phone Number',
  },
  // {
  //   id: 'RECIPIENT_CLIENT_CORPORATION',
  //   label: 'Recipient Client Corporation',
  //   defaultValue: 'Recipient Client Corporation',
  // },
  {
    id: 'RECIPIENT_OCCUPATION',
    label: 'Recipient Occupation',
    defaultValue: 'Recipient Occupation',
  },
  {
    id: 'RECIPIENT_ADDRESS_1',
    label: 'Recipient Address 1',
    defaultValue: 'Recipient Address 1',
  },
  {
    id: 'RECIPIENT_ADDRESS_2',
    label: 'Recipient Address 2',
    defaultValue: 'Recipient Address 2',
  },
  {
    id: 'RECIPIENT_ADDRESS_CITY',
    label: 'Recipient Address City',
    defaultValue: 'Recipient Address City',
  },
  {
    id: 'RECIPIENT_ADDRESS_COUNTRY',
    label: 'Recipient Address Country',
    defaultValue: 'Recipient Address Country',
  },
  {
    id: 'RECIPIENT_ADDRESS_STATE',
    label: 'Recipient Address County/State',
    defaultValue: 'Recipient Address County/State',
  },
  {
    id: 'RECIPIENT_ADDRESS_TIMEZONE',
    label: 'Recipient Address Timezone',
    defaultValue: 'Recipient Address Timezone',
  },
  {
    id: 'VACANCY_TITLE',
    label: 'Vacancy Title',
    defaultValue: 'Vacancy Title',
  },
  {
    id: 'VACANCY_ADDRESS',
    label: 'Vacancy Address',
    defaultValue: 'Vacancy Address',
  },
  // {
  //   id: 'VACANCY_ADDRESS_2',
  //   label: 'Vacancy Address 2',
  //   defaultValue: 'Vacancy Address 2',
  // },
  {
    id: 'VACANCY_CITY',
    label: 'Vacancy City',
    defaultValue: 'Vacancy City',
  },
  {
    id: 'COMPANY_NAME',
    label: 'Company Name',
    defaultValue: 'Company Name',
  },
  {
    id: 'VACANCY_COUNTRY',
    label: 'Vacancy Country',
    defaultValue: 'Vacancy Country',
  },
  {
    id: 'VACANCY_EMPLOYMENT_TYPE',
    label: 'Vacancy Employment Type',
    defaultValue: 'Vacancy Employment Type',
  },
  {
    id: 'VACANCY_OWNER_EMAIL',
    label: 'Vacancy Owner Email',
    defaultValue: 'Vacancy Owner Email',
  },
  {
    id: 'VACANCY_OWNER_FIRST_NAME',
    label: 'Vacancy Owner First Name',
    defaultValue: 'Vacancy Owner First Name',
  },
  {
    id: 'VACANCY_OWNER_FULL_NAME',
    label: 'Vacancy Owner Full Name',
    defaultValue: 'Vacancy Owner Full Name',
  },
  {
    id: 'VACANCY_SKILLS',
    label: 'Vacancy Skills',
    defaultValue: 'Vacancy Skills',
  },
  {
    id: 'SENDER_NAME',
    label: 'Sender Name',
    defaultValue: 'Sender Name',
  },
  {
    id: 'SENDER_EMAIL',
    label: 'Sender Email',
    defaultValue: 'Sender Email',
  },
  {
    id: 'SENT_DATE_TIME',
    label: 'Sent Date Time',
    defaultValue: 'Sent Date Time',
  },
  {
    id: 'CURRENT_SENT_DATE_TIME',
    label: 'Current Sent Date Time',
    defaultValue: 'Current Sent Date Time',
  },
  {
    id: 'COMPANY_MAIN_ADDRESS',
    label: 'Company Main Address',
    defaultValue: 'Company Main Address',
  },
  {
    id: 'COMPANY_ADDRESS_2',
    label: 'Company Address 2',
    defaultValue: 'Company Address 2',
  },
  {
    id: 'COMPANY_ADDRESS_CITY',
    label: 'Company Address City',
    defaultValue: 'Company Address City',
  },
  {
    id: 'COMPANY_ADDRESS_STATE',
    label: 'Company Address State/County',
    defaultValue: 'Company Address State/County',
  },
  {
    id: 'COMPANY_ADDRESS_POST_CODE',
    label: 'Company Address Post Code',
    defaultValue: 'Company Address Post Code',
  },
  {
    id: 'COMPANY_ADDRESS_COUNTRY',
    label: 'Company Address Country',
    defaultValue: 'Company Address Country',
  },
];

export const ZileoCommands = [
  {
    groupId: 'editOrReview',
    groupLabel: 'Edit or review',
    commands: [
      {
        id: 'improveWriting',
        label: 'Improve writing',
        prompt:
          'Fix spelling mistakes, use proper grammar and apply good writing practices. Do not lose the original meaning.',
      },
      {
        id: 'makeShorter',
        label: 'Make shorter',
        prompt:
          'Remove any repetitive, redundant, or non-essential writing in this content ' +
          'without changing the meaning or losing any key information.',
      },
      {
        id: 'makeLonger',
        label: 'Make longer',
        prompt:
          'Improve this content by using descriptive language and inserting more information and more detailed explanations.',
      },
      {
        id: 'simplifyLanguage',
        label: 'Simplify language',
        prompt:
          'Simplify the writing style of this content ' +
          'and reduce the complexity, so that the content is easy to understand.',
      },
    ],
  },
  {
    groupId: 'generate',
    groupLabel: 'Generate from selection',
    commands: [
      {
        id: 'summarize',
        label: 'Summarize',
        prompt:
          'Summarize this content into one paragraph of text. Include only the key ideas and conclusions. Keep it short. ',
      },
      {
        id: 'continue',
        label: 'Continue',
        prompt:
          'Start with the provided content and write at the end of it continuing this topic. Keep the added part short.',
      },
    ],
  },
  {
    groupId: 'changeTone',
    groupLabel: 'Change tone',
    commands: [
      {
        id: 'professional',
        label: 'Professional',
        prompt:
          'Improve using polished, formal, and respectful language ' +
          'to convey professional expertise and competence.',
      },
      {
        id: 'casual',
        label: 'Casual',
        prompt:
          'Improve using casual, informal language to convey a casual conversation with a real person.',
      },
      {
        id: 'direct',
        label: 'Direct',
        prompt:
          'Improve using direct language using only the essential information.',
      },
      {
        id: 'confident',
        label: 'Confident',
        prompt:
          'Improve using compelling, optimistic language to convey confidence in the writing.',
      },
      {
        id: 'friendly',
        label: 'Friendly',
        prompt:
          'Improve using friendly, comforting language, to convey understanding and empathy.',
      },
    ],
  },
  {
    groupId: 'changeStyle',
    groupLabel: 'Change style',
    commands: [
      {
        id: 'business',
        label: 'Business',
        prompt:
          'Rewrite this content as a business professional with formal language.',
      },
      {
        id: 'legal',
        label: 'Legal',
        prompt:
          'Rewrite this content as a legal professional using valid legal terminology.',
      },
      {
        id: 'journalism',
        label: 'Journalism',
        prompt:
          'Rewrite this content as a journalist using engaging language to convey the importance of the information.',
      },
      {
        id: 'poetic',
        label: 'Poetic',
        prompt:
          'Rewrite this content as a poem using poetic techniques without losing the original meaning.',
      },
    ],
  },
  {
    groupId: 'translate',
    groupLabel: 'Translate',
    commands: [
      {
        id: 'translateEnglish',
        label: 'English',
        prompt: 'Translate the content to English language.',
      },
      {
        id: 'translateSpanish',
        label: 'Spanish',
        prompt: 'Translate the content to Spanish language.',
      },
      {
        id: 'translatePortuguese',
        label: 'Portuguese',
        prompt: 'Translate the content to Portuguese language.',
      },
      {
        id: 'translateGerman',
        label: 'German',
        prompt: 'Translate the content to German language.',
      },
      {
        id: 'translateFrench',
        label: 'French',
        prompt: 'Translate the content to French language.',
      },
      {
        id: 'translateChinese',
        label: 'Simplified Chinese',
        prompt: 'Translate the content to Simplified Chinese language.',
      },
      {
        id: 'translateHindi',
        label: 'Hindi',
        prompt: 'Translate the content to Hindi language.',
      },
      {
        id: 'translateArabic',
        label: 'Arabic',
        prompt: 'Translate the content to Arabic language.',
      },
    ],
  },
];

export const ISO3166A2 = [
  { code: 'AF', name: 'Afghanistan' },
  { code: 'AX', name: 'Aland Islands' },
  { code: 'AL', name: 'Albania' },
  { code: 'DZ', name: 'Algeria' },
  { code: 'AS', name: 'American Samoa' },
  { code: 'AD', name: 'Andorra' },
  { code: 'AO', name: 'Angola' },
  { code: 'AI', name: 'Anguilla' },
  { code: 'AQ', name: 'Antarctica' },
  { code: 'AG', name: 'Antigua And Barbuda' },
  { code: 'AR', name: 'Argentina' },
  { code: 'AM', name: 'Armenia' },
  { code: 'AW', name: 'Aruba' },
  { code: 'AU', name: 'Australia' },
  { code: 'AT', name: 'Austria' },
  { code: 'AZ', name: 'Azerbaijan' },
  { code: 'BS', name: 'Bahamas' },
  { code: 'BH', name: 'Bahrain' },
  { code: 'BD', name: 'Bangladesh' },
  { code: 'BB', name: 'Barbados' },
  { code: 'BY', name: 'Belarus' },
  { code: 'BE', name: 'Belgium' },
  { code: 'BZ', name: 'Belize' },
  { code: 'BJ', name: 'Benin' },
  { code: 'BM', name: 'Bermuda' },
  { code: 'BT', name: 'Bhutan' },
  { code: 'BO', name: 'Bolivia' },
  { code: 'BA', name: 'Bosnia And Herzegovina' },
  { code: 'BW', name: 'Botswana' },
  { code: 'BV', name: 'Bouvet Island' },
  { code: 'BR', name: 'Brazil' },
  { code: 'IO', name: 'British Indian Ocean Territory' },
  { code: 'BN', name: 'Brunei Darussalam' },
  { code: 'BG', name: 'Bulgaria' },
  { code: 'BF', name: 'Burkina Faso' },
  { code: 'BI', name: 'Burundi' },
  { code: 'KH', name: 'Cambodia' },
  { code: 'CM', name: 'Cameroon' },
  { code: 'CA', name: 'Canada' },
  { code: 'CV', name: 'Cape Verde' },
  { code: 'KY', name: 'Cayman Islands' },
  { code: 'CF', name: 'Central African Republic' },
  { code: 'TD', name: 'Chad' },
  { code: 'CL', name: 'Chile' },
  { code: 'CN', name: 'China' },
  { code: 'CX', name: 'Christmas Island' },
  { code: 'CC', name: 'Cocos (Keeling) Islands' },
  { code: 'CO', name: 'Colombia' },
  { code: 'KM', name: 'Comoros' },
  { code: 'CG', name: 'Congo' },
  { code: 'CD', name: 'Congo}, Democratic Republic' },
  { code: 'CK', name: 'Cook Islands' },
  { code: 'CR', name: 'Costa Rica' },
  { code: 'CI', name: "Cote D'Ivoire" },
  { code: 'HR', name: 'Croatia' },
  { code: 'CU', name: 'Cuba' },
  { code: 'CY', name: 'Cyprus' },
  { code: 'CZ', name: 'Czech Republic' },
  { code: 'DK', name: 'Denmark' },
  { code: 'DJ', name: 'Djibouti' },
  { code: 'DM', name: 'Dominica' },
  { code: 'DO', name: 'Dominican Republic' },
  { code: 'EC', name: 'Ecuador' },
  { code: 'EG', name: 'Egypt' },
  { code: 'SV', name: 'El Salvador' },
  { code: 'GQ', name: 'Equatorial Guinea' },
  { code: 'ER', name: 'Eritrea' },
  { code: 'EE', name: 'Estonia' },
  { code: 'ET', name: 'Ethiopia' },
  { code: 'FK', name: 'Falkland Islands (Malvinas)' },
  { code: 'FO', name: 'Faroe Islands' },
  { code: 'FJ', name: 'Fiji' },
  { code: 'FI', name: 'Finland' },
  { code: 'FR', name: 'France' },
  { code: 'GF', name: 'French Guiana' },
  { code: 'PF', name: 'French Polynesia' },
  { code: 'TF', name: 'French Southern Territories' },
  { code: 'GA', name: 'Gabon' },
  { code: 'GM', name: 'Gambia' },
  { code: 'GE', name: 'Georgia' },
  { code: 'DE', name: 'Germany' },
  { code: 'GH', name: 'Ghana' },
  { code: 'GI', name: 'Gibraltar' },
  { code: 'GR', name: 'Greece' },
  { code: 'GL', name: 'Greenland' },
  { code: 'GD', name: 'Grenada' },
  { code: 'GP', name: 'Guadeloupe' },
  { code: 'GU', name: 'Guam' },
  { code: 'GT', name: 'Guatemala' },
  { code: 'GG', name: 'Guernsey' },
  { code: 'GN', name: 'Guinea' },
  { code: 'GW', name: 'Guinea-Bissau' },
  { code: 'GY', name: 'Guyana' },
  { code: 'HT', name: 'Haiti' },
  { code: 'HM', name: 'Heard Island & Mcdonald Islands' },
  { code: 'VA', name: 'Holy See (Vatican City State)' },
  { code: 'HN', name: 'Honduras' },
  { code: 'HK', name: 'Hong Kong' },
  { code: 'HU', name: 'Hungary' },
  { code: 'IS', name: 'Iceland' },
  { code: 'IN', name: 'India' },
  { code: 'ID', name: 'Indonesia' },
  { code: 'IR', name: 'Iran}, Islamic Republic Of' },
  { code: 'IQ', name: 'Iraq' },
  { code: 'IE', name: 'Ireland' },
  { code: 'IM', name: 'Isle Of Man' },
  { code: 'IL', name: 'Israel' },
  { code: 'IT', name: 'Italy' },
  { code: 'JM', name: 'Jamaica' },
  { code: 'JP', name: 'Japan' },
  { code: 'JE', name: 'Jersey' },
  { code: 'JO', name: 'Jordan' },
  { code: 'KZ', name: 'Kazakhstan' },
  { code: 'KE', name: 'Kenya' },
  { code: 'KI', name: 'Kiribati' },
  { code: 'KR', name: 'Korea' },
  { code: 'KW', name: 'Kuwait' },
  { code: 'KG', name: 'Kyrgyzstan' },
  { code: 'LA', name: "Lao People's Democratic Republic" },
  { code: 'LV', name: 'Latvia' },
  { code: 'LB', name: 'Lebanon' },
  { code: 'LS', name: 'Lesotho' },
  { code: 'LR', name: 'Liberia' },
  { code: 'LY', name: 'Libyan Arab Jamahiriya' },
  { code: 'LI', name: 'Liechtenstein' },
  { code: 'LT', name: 'Lithuania' },
  { code: 'LU', name: 'Luxembourg' },
  { code: 'MO', name: 'Macao' },
  { code: 'MK', name: 'Macedonia' },
  { code: 'MG', name: 'Madagascar' },
  { code: 'MW', name: 'Malawi' },
  { code: 'MY', name: 'Malaysia' },
  { code: 'MV', name: 'Maldives' },
  { code: 'ML', name: 'Mali' },
  { code: 'MT', name: 'Malta' },
  { code: 'MH', name: 'Marshall Islands' },
  { code: 'MQ', name: 'Martinique' },
  { code: 'MR', name: 'Mauritania' },
  { code: 'MU', name: 'Mauritius' },
  { code: 'YT', name: 'Mayotte' },
  { code: 'MX', name: 'Mexico' },
  { code: 'FM', name: 'Micronesia}, Federated States Of' },
  { code: 'MD', name: 'Moldova' },
  { code: 'MC', name: 'Monaco' },
  { code: 'MN', name: 'Mongolia' },
  { code: 'ME', name: 'Montenegro' },
  { code: 'MS', name: 'Montserrat' },
  { code: 'MA', name: 'Morocco' },
  { code: 'MZ', name: 'Mozambique' },
  { code: 'MM', name: 'Myanmar' },
  { code: 'NA', name: 'Namibia' },
  { code: 'NR', name: 'Nauru' },
  { code: 'NP', name: 'Nepal' },
  { code: 'NL', name: 'Netherlands' },
  { code: 'AN', name: 'Netherlands Antilles' },
  { code: 'NC', name: 'New Caledonia' },
  { code: 'NZ', name: 'New Zealand' },
  { code: 'NI', name: 'Nicaragua' },
  { code: 'NE', name: 'Niger' },
  { code: 'NG', name: 'Nigeria' },
  { code: 'NU', name: 'Niue' },
  { code: 'NF', name: 'Norfolk Island' },
  { code: 'MP', name: 'Northern Mariana Islands' },
  { code: 'NO', name: 'Norway' },
  { code: 'OM', name: 'Oman' },
  { code: 'PK', name: 'Pakistan' },
  { code: 'PW', name: 'Palau' },
  { code: 'PS', name: 'Palestinian Territory}, Occupied' },
  { code: 'PA', name: 'Panama' },
  { code: 'PG', name: 'Papua New Guinea' },
  { code: 'PY', name: 'Paraguay' },
  { code: 'PE', name: 'Peru' },
  { code: 'PH', name: 'Philippines' },
  { code: 'PN', name: 'Pitcairn' },
  { code: 'PL', name: 'Poland' },
  { code: 'PT', name: 'Portugal' },
  { code: 'PR', name: 'Puerto Rico' },
  { code: 'QA', name: 'Qatar' },
  { code: 'RE', name: 'Reunion' },
  { code: 'RO', name: 'Romania' },
  { code: 'RU', name: 'Russian Federation' },
  { code: 'RW', name: 'Rwanda' },
  { code: 'BL', name: 'Saint Barthelemy' },
  { code: 'SH', name: 'Saint Helena' },
  { code: 'KN', name: 'Saint Kitts And Nevis' },
  { code: 'LC', name: 'Saint Lucia' },
  { code: 'MF', name: 'Saint Martin' },
  { code: 'PM', name: 'Saint Pierre And Miquelon' },
  { code: 'VC', name: 'Saint Vincent And Grenadines' },
  { code: 'WS', name: 'Samoa' },
  { code: 'SM', name: 'San Marino' },
  { code: 'ST', name: 'Sao Tome And Principe' },
  { code: 'SA', name: 'Saudi Arabia' },
  { code: 'SN', name: 'Senegal' },
  { code: 'RS', name: 'Serbia' },
  { code: 'SC', name: 'Seychelles' },
  { code: 'SL', name: 'Sierra Leone' },
  { code: 'SG', name: 'Singapore' },
  { code: 'SK', name: 'Slovakia' },
  { code: 'SI', name: 'Slovenia' },
  { code: 'SB', name: 'Solomon Islands' },
  { code: 'SO', name: 'Somalia' },
  { code: 'ZA', name: 'South Africa' },
  { code: 'GS', name: 'South Georgia And Sandwich Isl.' },
  { code: 'ES', name: 'Spain' },
  { code: 'LK', name: 'Sri Lanka' },
  { code: 'SD', name: 'Sudan' },
  { code: 'SR', name: 'Suriname' },
  { code: 'SJ', name: 'Svalbard And Jan Mayen' },
  { code: 'SZ', name: 'Swaziland' },
  { code: 'SE', name: 'Sweden' },
  { code: 'CH', name: 'Switzerland' },
  { code: 'SY', name: 'Syrian Arab Republic' },
  { code: 'TW', name: 'Taiwan' },
  { code: 'TJ', name: 'Tajikistan' },
  { code: 'TZ', name: 'Tanzania' },
  { code: 'TH', name: 'Thailand' },
  { code: 'TL', name: 'Timor-Leste' },
  { code: 'TG', name: 'Togo' },
  { code: 'TK', name: 'Tokelau' },
  { code: 'TO', name: 'Tonga' },
  { code: 'TT', name: 'Trinidad And Tobago' },
  { code: 'TN', name: 'Tunisia' },
  { code: 'TR', name: 'Turkey' },
  { code: 'TM', name: 'Turkmenistan' },
  { code: 'TC', name: 'Turks And Caicos Islands' },
  { code: 'TV', name: 'Tuvalu' },
  { code: 'UG', name: 'Uganda' },
  { code: 'UA', name: 'Ukraine' },
  { code: 'AE', name: 'United Arab Emirates' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'US', name: 'United States' },
  { code: 'UM', name: 'United States Outlying Islands' },
  { code: 'UY', name: 'Uruguay' },
  { code: 'UZ', name: 'Uzbekistan' },
  { code: 'VU', name: 'Vanuatu' },
  { code: 'VE', name: 'Venezuela' },
  { code: 'VN', name: 'Vietnam' },
  { code: 'VG', name: 'Virgin Islands}, British' },
  { code: 'VI', name: 'Virgin Islands}, U.S.' },
  { code: 'WF', name: 'Wallis And Futuna' },
  { code: 'EH', name: 'Western Sahara' },
  { code: 'YE', name: 'Yemen' },
  { code: 'ZM', name: 'Zambia' },
  { code: 'ZW', name: 'Zimbabwe' },
];

export const IGNORE_MERGE_TAGS = [
  'RECIPIENT_ADDRESS_2',
  'RECIPIENT_ADDRESS_TIMEZONE',
];

export const departmentList = {
  Finance: [
    'Accounting',
    'Investment Management',
    'Tax',
    'Financial Planning & Analysis',
    'Risk',
    'Financial Reporting',
    'Investor Relations',
    'Financial Strategy',
    'Internal Audit & Control',
  ],
  HR: [
    'Compensation & Benefits',
    'Learning & Development',
    'Recruiting',
    'Diversity & Inclusion',
    'Employee & Labor Relations',
    'Talent Management',
  ],
  Health: [
    'Dental',
    'Doctor',
    'Fitness',
    'Nursing',
    'Therapy',
    'Wellness',
    'Medical Administration',
    'Medical Education & Training',
    'Medical Research',
    'Clinical Operations',
  ],
  Legal: [
    'Legal Counsel',
    'Compliance',
    'Contracts',
    'Corporate Secretary',
    'Litigation',
    'Privacy',
  ],
  Marketing: [
    'Brand Management',
    'Content Marketing',
    'Product Marketing',
    'Advertising',
    'Customer Experience',
    'Demand Generation',
    'Digital Marketing',
    'Search Engine Optimization (SEO)',
    'Social Media Marketing',
    'Event Marketing',
    'Public Relations (PR)',
  ],
  Operations: [
    'Logistics',
    'Office Operations',
    'Project Management',
    'Corporate Strategy',
    'Facilities Management',
    'Quality Management',
    'Supply Chain',
    'Manufacturing',
    'Call Center',
    'Customer Service / Support',
    'Real Estate',
  ],
  'Product & Engineering': [
    'Graphic Design',
    'Product Design',
    'Web Design',
    'Data Science',
    'DevOps',
    'Electrical Engineering',
    'Mechanical Engineering',
    'Network Operations',
    'Information Technology',
    'Project Engineering',
    'Quality Assurance',
    'Information Security',
    'Software Development',
    'Systems Administration',
    'Web Development',
    'Product Management',
    'Artificial Intelligence / Machine Learning',
    'Digital Transformation',
  ],
  Sales: [
    'Account Management',
    'Business Development',
    'Channel Sales',
    'Inside Sales',
    'Sales Enablement',
    'Sales Operations',
  ],
  'C-Suite': [
    'Executive',
    'Founder',
    'Product & Engineering Executive',
    'Finance Executive',
    'HR Executive',
    'Legal Executive',
    'Marketing Executive',
    'Health Executive',
    'Operations Executive',
    'Sales Executive',
  ],
  Education: ['Administration', 'Professor', 'Researcher', 'Teacher'],
};

export const industryList = {
  'Agriculture & Fishing': [
    'Agriculture',
    'Crops',
    'Farming Animals & Livestock',
    'Fishery & Aquaculture',
    'Ranching',
  ],
  'Business Services': [
    'Accounting & Accounting Services',
    'Auctions',
    'Business Services - General',
    'Call Centers & Business Centers',
    'Commercial Printing',
    'Design',
    'Digital Accessibility Services',
    'Engineering Services',
    'Equipment Rental Services',
    'Event Services',
    'Executive Search Services',
    'Facilities Management & Services',
    'Food Service',
    'Geography & Positioning',
    'Human Resources & Staffing',
    'Information Services',
    'Janitorial Services',
    'Management Consulting',
    'Sales, Marketing & Advertising',
    'Multimedia & Graphic Design',
    'Office Administration',
    'Outsourcing',
    'Repair & Maintenance',
    'Sales',
    'Security & Investigations Products & Services',
    'HR, Staffing, & Recruiting',
    'Translation & Localization',
    'Writing & Editing',
  ],
  Construction: [
    'Architecture Engineering & Design',
    'Building Construction',
    'Building Equipment Contractors',
    'Building Finishing Contractors',
    'Building Structure & Exterior Contractors',
    'Construction - General',
    'Highway, Street, & Bridge Construction',
    'Landscaping Services',
    'Mechanical Engineering',
    'Mechanical or Industrial Engineering',
    'Nonresidential Building Construction',
    'Residential Building Construction',
    'Specialty Trade Contractors',
    'Utility System Construction',
  ],
  'Consumer Services': [
    'Car & Truck Rental',
    'Caterers',
    'Child Care',
    'Consumer Services - General',
    'Funeral Homes & Funeral Related Services',
    'Hair Salons and Cosmetology',
    'Household Services',
    'Laundry & Dry Cleaning Services',
    'Photography',
    'Vehicle Repair & Maintenance',
    'Veterinary Care',
  ],
  Education: [
    'Career Services',
    'Colleges & Universities',
    'E-learning',
    'Education - General',
    'Education Management',
    'Fine Arts Schools',
    'Higher Education',
    'K-12 Schools',
    'Professional Training & Coaching',
    'Sports & Recreation Instruction',
    'Training',
  ],
  'Energy, Utilities & Waste Treatment': [
    'Electricity & Energy',
    'Energy, Utilities & Waste Treatment - General',
    'Environmental Services',
    'Nuclear',
    'Oil & Gas Exploration & Services',
    'Renewables & Environment',
    'Solar Electric Power Generation',
    'Utilities',
    'Water Energy & Waste Treatment',
  ],
  Finance: [
    'Banking',
    'Collection Agencies',
    'Credit Cards & Transaction Processing',
    'Finance - General',
    'Financial Services',
    'Funds, Trusts & Estates',
    'Holding Companies',
    'Investment Advice',
    'Investment Banking',
    'Loan Brokers',
    'Securities & Commodity Exchanges',
    'Venture Capital & Private Equity',
  ],
  'Government & Public Services': [
    'Chambers of Commerce',
    'Cities Towns & Municipalities - General',
    'Communities',
    'Conservation Programs',
    'Corrections Facilities',
    'Fire Protection',
    'Government - General',
    'Health & Human Services',
    'International Affairs',
    'Law Enforcement',
    'Military',
    'Public Policy',
    'Public Safety',
    'Space Research & Technology',
  ],
  Healthcare: [
    'Biotechnology',
    'Chiropractors',
    'Dentists',
    'Drug Manufacturing & Research',
    'Drug Stores & Pharmacies',
    'Emergency & Relief Services',
    'Emergency Medical Transportation & Services',
    'Healthcare - General',
    'Home Health Care Services',
    'Hospitals & Healthcare',
    'Medical & Diagnostic Laboratories',
    'Medical Practice',
    'Medicine',
    'Mental Health Care',
    'Optometrists',
    'Pharmaceuticals',
    'Physical, Occupational & Speech Therapists',
    'Public Health',
  ],
  'IT & Software': [
    'Business Intelligence Software',
    'Cloud Software',
    'Computer Games',
    'Content & Collaboration Software',
    'Customer Relationship Management Software(CRM)',
    'Database & File Management Software',
    'Data Infrastructure & Analytics',
    'Desktop Computing Software Products',
    'Embedded Software Products',
    'Enterprise Resource Planning Software(ERP)',
    'IT Systems & Services',
    'Mobile Computing Software Products',
    'Network Security Hardware & Software',
    'Software & Technical Consulting',
    'Software - General',
  ],
  'Law Firms & Legal Services': [
    'Courts of Law',
    'Law Firms & Legal Services - General',
  ],
  'Leisure & Hospitality': [
    'Amusement Parks Arcades & Attractions',
    'Bars, Taverns, & Nightclubs',
    'Cultural - General',
    'Elder & Disabled Care',
    'Entertainment',
    'Fine Arts',
    'Fitness & Dance Facilities',
    'Gambling & Casinos',
    'Gaming',
    'Golf Courses & Country Clubs',
    'Leisure & Hospitality - General',
    'Leisure, Travel & Tourism',
    'Libraries',
    'Hotels, Lodging, & Resorts',
    'Movie Theaters',
    'Museums & Art Galleries',
    'Performing Arts',
    'Recreation',
    'Restaurants',
    'Sports',
    'Zoos & National Parks',
  ],
  Manufacturing: [
    'Aerospace & Defense',
    'Animal & Leather Products',
    'Textiles, Fashion, & Apparel',
    'Appliances',
    'Audio & Video Equipment & Products',
    'Automotive',
    'Automotive Equipment',
    'Boats & Submarines',
    'Building Materials',
    'Business Supplies & Equipment',
    'Chemical Products',
    'Cleaning Products',
    'Commercial & Industrial Machinery Maintenance',
    'Commercial Machinery',
    'Computer Hardware',
    'Consumer Electronics',
    'Consumer Goods',
    'Cosmetics & Personal Care Products',
    'Dairy',
    'Electrical Equipment & Products',
    'Electronics & Electronics Manufacturing',
    'Energy Equipment & Products',
    'Fabricated Metal Products',
    'Food & Beverages',
    'Food Production',
    'Furniture',
    'Glass & Concrete',
    'Hand Power and Lawn-care Tools',
    'Health & Nutrition Products',
    'Health Wellness & Fitness',
    'Household Goods',
    'Industrial Machinery Equipment & Automation',
    'Lighting Equipment & Products',
    'Lumber & Wood Production',
    'Luxury Goods & Jewelry',
    'Manufacturing - General',
    'Maritime',
    'Measurement & Testing Equipment',
    'Medical Devices & Equipment',
    'Metal Products',
    'Miscellaneous Building Materials',
    'Office Equipment & Products',
    'Paper, Wood, & Forest Products',
    'Personal Computers & Peripherals',
    'Pet Products',
    'Petrochemicals',
    'Plastics & Packaging & Containers',
    'Plastics & Rubber Products',
    'Plumbing & HVAC Equipment',
    'Power Conversion & Protection Equipment',
    'Recyclable Materials',
    'Renewable Energy Equipment & Products',
    'Semiconductor & Semiconductor Equipment',
    'Shipbuilding',
    'Telecommunication Equipment',
    'Test & Measurement Equipment',
    'Textiles & Apparel',
    'Tires & Rubber',
    'Tobacco',
    'Toys & Games',
    'Wine, Beer, & Spirits',
    'Wire & Cable',
  ],
  'Media & Internet': [
    'Animation',
    'Blogs',
    'Broadcasting & Media',
    'Ebook & Audiobooks',
    'Ecommerce & Internet Retail',
    'Film Video & Media Production & Services',
    'Internet & Digital Media',
    'Internet News & Publishing',
    'Internet-related Services',
    'Media & Internet - General',
    'Music & Music Related Services',
    'Newspapers & News Services',
    'Public Relations & Communication',
    'Publishing',
    'Radio Stations',
    'Search Engines & Internet Portals',
    'Social Media',
    'Software Engineering',
    'Television Stations',
  ],
  'Metals & Mining': ['Metals & Mining - General', 'Mining'],
  Organizations: [
    'Non-Profit & Charitable Organizations & Foundations',
    'Organizations - General',
    'Philanthropy',
    'Professional Organizations',
    'Program Development',
    'Religious Organizations',
  ],
  'Real Estate': [
    'Commercial Real Estate',
    'Leasing Residential Real Estate',
    'Real Estate & Equipment Rental Services',
    'Real Estate - General',
    'Real Estate Agents & Brokers',
  ],
  'Research & Technology': [
    'Artificial Intelligence & Machine Learning',
    'Climate Data & Analytics',
    'Cryptocurrency',
    'Mobile',
    'Nanotechnology',
    'Research - General',
    'Robotics',
    'Technology',
    'Think Tanks',
  ],
  Retail: [
    'Apparel & Accessories Retail',
    'Consumer Electronics & Computers Retail',
    'Consumer Goods Rental',
    'Department Stores Shopping Centers & Superstores',
    'Flowers Gifts & Specialty',
    'Footwear',
    'Gas Stations Convenience & Liquor Stores',
    'Grocery & Supermarkets',
    'Home Improvement & Hardware',
    'Jewelry & Watch Retail',
    'Optometry & Eyewear',
    'Record, Video & Book Stores',
    'Rental - Other - Furniture A/V Construction & Industrial Equipment',
    'Rental - Video & DVD',
    'Retail - General',
    'Retail Motor Vehicles',
    'Retail Musical Instruments',
    'Sporting & Recreational Equipment',
  ],
  'Supply Chain & Logistics': [
    'Import & Export & Trade',
    'Logistics & Supply Chain - General',
    'Warehousing',
    'Wholesale',
  ],
  Telecommunications: [
    'Cable & Satellite',
    'Telecommunications - General',
    'Wireless',
  ],
  Transportation: [
    'Airlines & Aviation',
    'Delivery',
    'Freight & Logistics Services',
    'Ground Passenger Transportation',
    'Rail Bus & Taxi',
    'Rail Transportation',
    'School & Employee Bus Services',
    'Shipping',
    'Shuttles & Special Needs Transportation Services',
    'Sightseeing Transportation',
    'Taxi & Limousine Services',
    'Transportation - General',
    'Trucking Moving & Storage',
    'Urban Transit Services',
  ],
};

export const EMAIL_NOT_FOUND = '<EMAIL>';

export const fileLogo = {
  zip: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAABMlBMVEUAAADh5Obs7e2EvVnK0Ni1usCEvFmwt721usCxt73i5efi4uLi5eewt73k5Ovj5uext73h5efj5emDvFi1ub6DvFmEvVnh5efW29+DvVri5Ofj5ueDvVmCvVnh5Obh5efi5OaEvVrf5Oa2u8Hi5eXh5Ojh5eWEu1mEvVrh4efi5eeEvVrK0diwt73////W29+hyYbW4NX+/v7f4uSGvlzc7NCIv1/E36+LwWTP5b/h5Obm8d3R5sLh79fa686VxnL8/fuSxW2Pw2mKwGKt05GOwmj6/Pn3+/X0+fHr9OXq9OLM47rx9+zo8uDA3aq72qOr0Y2mz4ju9ejQ1drd7NLX6cq7wcbT6MSyub+115yizYKXxXjY3eHb4d7b4tzKz9PBx8u/zsXF4LKy0J+dx3+RwHLjP6niAAAAKnRSTlMA7w/f71ew7lzq0Ab4+Azy8pJwak779vLv6+Xg1MC6s6GfeV5XTEQ8NiqM1Qw6AAACSUlEQVRYw7TQTQrCMBiEYRVj40+1SlE3IiKipIYqBRe9hlBcqaD3P4MuCmIni69M+h7gYZgWNNyt20bQSLXkTSdGVBZosTkzwjIbxUJzOJCjNtjK0PFCjn5V2a8dUwe1ofaOwlZPqI20fxS38ij8yqO4lUd/qSbQvvKP4lYexa08WhZ7QW8WtvJoYSspHs0fVTTQNGryZ1UNFY2a7FpUf9UcWpYe/+p6QfNUhhJbeRS38ihu5VHcyqO4lUdxK49iqS8Ut/IobuVRVHnUrfIoqk2geUqi7pZNoO3G0f2mV3aq3evuRg/zhOidOdFVwnQ5O9EPO/WO2yAQRWG4yHKOdIorwCDAPIb3m8iWZafL/hcRIE5kQaohRRT5L680XzGjuS/Y1RP9T6jqnHv1CGDoYwBB7c29J6keOgi/cgGVSw/gws9C19ZCI9Oda0PSA2zhATBMsqyqKheWqQ4Kw5pLQobRIxpNw+CVvOo/1HS8uOERjZdxxlobVSXZYYui5Fkb7UTO6gc0zenoogPZ2gDWd5r6ZK+JBg2zG1aoP9UIi0gHXYgwwRpdkiaAFmrUpGNsUP90Mr1rBD10zKRS2KDxnr//VlDa49Lh11CP30kAFS7okZLuQZNceM+1YPnZOA+Ly9/bpx8bNZQKhspQZKYJdkOFRSkxNDUWe1NSWJJ8d0YnGhNq9OqQAWhhaLAVIUP1SQYxxkgt6aHZkRg1FAL4+CkylAvXhAwlQAnH1BFFDuVjwA4UeMg2U0oN54yUihwjOSbySAtqQo0AAL7CinJhURi6AAAAAElFTkSuQmCC',
  pdf: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAABR1BMVEUAAADh5Obs7e3wVkHK0Niwt73wVkK1usC1ub/i5ee1u7/i4uLi5eewt73i5uext73i5ejk5OvBhG2xt73j5efh5efyU0C1usC1ub7yVT7xVUHwVUHW29/xVkGwuL3xVUHyVUHh5ObxWUXh5efi5ObyVULi5eXh5Ojh5eXh4efi5efxVkLK0diwt73////W29/sgnXj09Lf4uTxXUr3opf2kYTxYU7++vrxWkf+/v797uz97evh5Ob6wbr5ubH+/f34sKbxZFHxXEn85OD70cv5vrb0hHbxV0T++Pf96uj98e/71c/4raPzd2fzdGT+/Pz+9PL83dr72dX6x8H4sqn2oJX0f3DyaVfi29zQ1dq7wcb6y8X3qJ72nZL2mY32lon1jYD0hnf0gnPpeWzybFvyZVPY3eHKz9PBx8vQvsHonJPsg3bpbl+zZQp4AAAAKnRSTlMA7w/f7+6vXVfQVgb4+PLrcwwC8uOSallOOfv27+vp1MC6tLOhn1dMRCoMTtQfAAACeklEQVRYw7TSzQ7BQBTFcQQz6psIQkSISKaaoSuLPgjdWhDef09jcaK6uM2c+T/ALyc3t/JXe7+tGkGdoCJuNOkZUXFDidGBERZH45nQbLfkaCTdOuzJ0Y+qRWjNlEGjrqKj2daAjWaNFRnFViqKu1JR/AAHRVMfaF8TUaR9oCtNRNHMB9qfMtA0yqXd0eSeRxvKGTXJI692A2fUxNc0f1flgCJ7+KlOQRNLRJFlothKRJFlothKRJFlothKRJFlothKRJFlolCZKFQmCtUHmlgiipY+0Kp3dL5rftscy/Z6XorR+SJ06BYXouvQpfOpEH2zUy+5CcNQFIYHXc6VziSExLVSg5OQd3jDgDeC/W+hXKeS06Ghg6rqPzuTL5Et+Y1e6h/9K2i4jriPQ5vx3Ay4td+EZGoPgy4/dEAvCiZR+jxnMKlZdeOpY4Gu6ckB9YEyjuNCQZ2JPCCXUpYKkFciGr6jS8jGDV1prb0VkGhGU+9ROwfirUGDsckjN3RE3A7yzuiHWV4qxL5DyeaMrpFvLUrbAvJ1NEJ576FhAnEz6IA7DR3RVfjoKLDwLMofwaZ3UbUjWlRVNQ8gjtRDsyXQWFTtHdGuoKY+Opxj6jEaTLiGXP80SZJlvcm+oX6A3csXxVk0vAQIrj+HxmmaLhc5kIZPo2egtksrfJVHGZ+sRP4EOl4ULdkSAUBMZTTRPLPRbPRb3tPPjRpKJUPladHsEZOixNCcCKyGConJkm2ke3KmEaFGrw4ZgBaGRtoQMlSfZJBmhNSSHpodiVFD4dMclABOXBMylAABHFNHFDlUEMcklwoP2WaKaOCckVJTZCTHRB4RUU2oEQDDsZI2xgsYwAAAAABJRU5ErkJggg==',
  psd: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAABR1BMVEUAAADs7e3h5OZQvujK0NhQveiwt721usC2ur/i5eext721usCn0dHi5ebi5ejn5+ff5OdPvuiwt7yxt73j5edPv+i1u8FMwemwt73W29/i5Obi5edPvedPveji5OdOvefh5Obh5efi5OZPveni5ufg5Obi5eW1u760t77h5Ojh5eXh4efi5edQvujK0diwt73////W2999yufP4Off4+W95vZcwulXwen6/f7n9vzQ7flfw+qQ1fBUv+hSvujz+/3T7vm/5/Zyy+xuyexqyOv8/v72+/3u+Pzg8/u75vag2/Kb2vKW2PFjxers9/zk9fuv4fR+z+55ze3w+fzb8frY8PnP7fjC6Par3/Om3vOD0e5mxurO7PjG6fe9w8jU7/my4vW14/SJ0+/Y4ufQ1dqyub+25PSY0ed3yOdoweRxw+O3ztrKz9NXl9PyAAAALHRSTlMADu/f77DvXVb16lgDz3MH/fn59ONqVjn17+7t69TTwLqzoZ+WjldPTkxEKnD/+3UAAAKkSURBVFjDtNA7CsJAFIXhKJrJy1cCiWIhoqDCxEFN61bSqGjh/msHYmPuFFdO7r+Aj8PxSMlhvdSMwsBjl2xDzaryFRsda2aVyfZMczLio4a7tRfyUasGPFTzUdtcCaB0K47aMiWAGj8XQE2qBFDyK4w2xRJoFAigdqsEGsXdo7ZdJ+jDkK04WptWAY7qdxv1FY5qMjXNcbS61+1fFYg2nY4/DSCUqjhKVRylKo5SFUepykABFUepiqNUxVGq4ihVcdSt4ihVUdStoqhblUD1QgLti6PFZvjt/HfPmxstZiXQa+pEVyXS9eJEP+zUO3KDMBSF4SLr0cxpjETEO4AhvM3TYI/tFNn/EiKQE5Okkt2kyN9dNPMVF8ETeah/9G+g3HwUzUpXtDkb2TJq6YkB+sVbztLXubTiimgJWVD489gHcrTnKYaMbQ01dAPoURTVQD4RYgBNYiS9zXbi7BnBVsQEO1BF1KOU8hYoCWmAF/HQ4j5ZUD0TZ9oZCAwlVDKE1+goKQCfyCSqkbnRRqSOihy0dN6io/1G5+e7O1Caw6GEFwA7DSP9gRrAoLhT0zS1I/AuxqxlEIVv1je0Ao5qaOM4TgHYcg1ecrSBoDTX6Ahs1FCZfVuatWewvTW6BxI1tO26rk98smoPNq5QWiDU1F/UNS/l1++MVV+oNUWAS+5FeY3cNXbVhSGfZjTs49g9bIHaV0Hj9XU3mwAy9irGAz6LJqKCVvnBvE18CDGnL1fKkEPopNlj/1PrY0E+kd5OQ6TkH/aGylJkph12Q0UpaqBlJmM1lEdUhnx3ZmWbEGr06pEBaGFoCsHmuSHpwATJ0KHZkRh5hkrhmuagBPDimpChBPDhmjqiAMgx4pjkUiI/AES0cM5IqSkwkWOigIiqNtQIAHqhvP0A9HSoAAAAAElFTkSuQmCC',
  png: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAABJlBMVEUAAADh5Obu7u6gZqnK0Ni1usCgZqmwt73b4eSxt721usHi4uLh5Ofi5uexuL3i5ei0ur+gZani5efh5ebh5ee1ub7h5efW29+wt73i5Ofj5uegZaqfZanh5Obh5efi5OagZaqgZqmfZavi5eWdZqqhY6rh4efi5eegZqrK0diwt73////W29+0jb3Z1d/f4uX8+vzw5/LXvtvs4e6lb6/x6fLn2er9/P2haKv49Pnk0+fh5OaserWkbK2iaqzcx+D6+PvJqc+5jsD28vfp3OvRtda9lcSpdbOncbD59vr17/bz7PTu5PC6kcG3i760h7uyg7qqeLTeyuKufLfUutjDosrBm8eugLjgzePd3OLYwdzQ1drOsNPMrtLCxM67wcba1+DKz9Mfx6evAAAAJ3RSTlMA7w/f77DvXVb16lgDz3MH/fn59ONqVjn17+7t69TTwLqzoZ+WjldPTkxEKnD/+3UAAAKkSURBVFjDtNA7CsJAFIXhKJrJy1cCiWIhoqDCxEFN61bSqGjh/msHYmPuFFdO7r+Aj8PxSMlhvdSMwsBjl2xDzaryFRsda2aVyfZMczLio4a7tRfyUasGPFTzUdtcCaB0K47aMiWAGj8XQE2qBFDyK4w2xRJoFAigdqsEGsXdo7ZdJ+jDkK04WptWAY7qdxv1FY5qMjXNcbS61+1fFYg2nY4/DSCUqjhKVRylKo5SFUepykABFUepiqNUxVGq4ihVcdSt4ihVUdStoqhblUD1QgLti6PFZvjt/HfPmxstZiXQa+pEVyXS9eJEP+zUO3KDMBSF4SLr0cxpjETEO4AhvM3TYI/tFNn/EiKQE5Okkt2kyN9dNPMVF8ETeah/9G+g3HwUzUpXtDkb2TJq6YkB+sVbztLXubTiimgJWVD489gHcrTnKYaMbQ01dAPoURTVQD4RYgBNYiS9zXbi7BnBVsQEO1BF1KOU8hYoCWmAF/HQ4j5ZUD0TZ9oZCAwlVDKE1+goKQCfyCSqkbnRRqSOihy0dN6io/1G5+e7O1Caw6GEFwA7DSP9gRrAoLhT0zS1I/AuxqxlEIVv1je0Ao5qaOM4TgHYcg1ecrSBoDTX6Ahs1FCZfVuatWewvTW6BxI1tO26rk98smoPNq5QWiDU1F/UNS/l1++MVV+oNUWAS+5FeY3cNXbVhSGfZjTs49g9bIHaV0Hj9XU3mwAy9irGAz6LJqKCVvnBvE18CDGnL1fKkEPopNlj/1PrY0E+kd5OQ6TkH/aGylJkph12Q0UpaqBlJmM1lEdUhnx3ZmWbEGr06pEBaGFoCsHmuSHpwATJ0KHZkRh5hkrhmuagBPDimpChBPDhmjqiAMgx4pjkUiI/AES0cM5IqSkwkWOigIiqNtQIAHqhvP0A9HSoAAAAAElFTkSuQmCC',
  unknown: `data:image/png;base64,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`,
};

export const MODE_CONSTANTS = {
  CREATE: 'CREATE',
  EDIT: 'EDIT',
  SYNC: 'SYNC',
};

export const CONSULTANT_SOURCE = {
  ZILEO: 'ZILEO_CRM',
  OTHER: 'OTHER_CRM',
};

// Array of PDF file extensions
export const zipExtensions = ['zip'];

// Array of PDF file extensions
export const pdfExtensions = ['pdf'];

// Array of PSD file extensions
export const psdExtensions = ['psd'];

// Array of image file extensions
export const imageExtensions = [
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'svg',
  'webp',
];

export const getThumbnailByType = (type) => {
  if (pdfExtensions.includes(type)) {
    return fileLogo.pdf;
  }
  if (psdExtensions.includes(type)) {
    return fileLogo.psd;
  }
  if (imageExtensions.includes(type)) {
    return fileLogo.png;
  }
  if (zipExtensions.includes(type)) {
    return fileLogo.zip;
  }
  return fileLogo.unknown;
};

export const FEATURES = {
  REPORTED_AGENCIES: 'REPORTED_AGENCIES',
  JOB_SEARCH: 'JOB_SEARCH',
  JOB_LEAD: 'JOB_LEAD',
  JOB_SYNC: 'JOB_SYNC',
  EMAIL_FINDER: 'EMAIL_FINDER',
  EMAIL_VERIFICATION: 'EMAIL_VERIFICATION',
  DASHBOARD: 'DASHBOARD',
  USER_MANAGEMENT: 'USER_MANAGEMENT',
  STAFF_PERFORMANCE: 'STAFF_PERFORMANCE',
  DUPLICATED_JOBS: 'DUPLICATED_JOBS',
  TEAM_MANAGEMENT: 'TEAM_MANAGEMENT',
  ACCOUNTS: 'ACCOUNTS',
  CONTRACT: 'CONTRACT',
  CRM: 'CRM',
  SETTING_ADMIN: 'SETTING_ADMIN',
  TASK: 'TASK',
  SEARCH: 'SEARCH',
  SYNC: 'SYNC',
  MY_LEADS: 'MY_LEADS',
  MAILBOX: 'MAILBOX',
  SEQUENCE: 'SEQUENCE',
  MANUAL_LEADS: 'MANUAL_LEADS',
  CONTACT_FINDER: 'CONTACT_FINDER',
  SETTINGS: 'SETTINGS',
  REPORTING: 'REPORTING',
  CONTACT_LIST: 'CONTACT_LIST',
  COMPANY_ONBOARDING: 'COMPANY_ONBOARDING',
  COMPANY_APPROVAL: 'COMPANY_APPROVAL',
  VIEW_AS: 'VIEW_AS',
  CREDIT_MANAGEMENT: 'CREDIT_MANAGEMENT',
};

export const permissionsOnFeatures = [
  {
    id: '718a25fd-d984-434f-bc58-d1d694cff2d1',
    name: 'Report Agency',
    keyCode: 'REPORTED_AGENCIES',
  },
  {
    id: '********-6261-44f7-9493-e2a26d78d79d',
    name: 'Job Search',
    keyCode: 'JOB_SEARCH',
  },
  {
    id: '1c981eec-d7d1-4e00-87fc-265a1b7e200f',
    name: 'Job Lead',
    keyCode: 'JOB_LEAD',
  },
  {
    id: '773fd5e4-eab4-4598-8087-a8385641e863',
    name: 'Job Sync',
    keyCode: 'JOB_SYNC',
  },
  {
    id: '664be5d4-285a-4f95-be02-a1ddb528579c',
    name: 'Email Finder',
    keyCode: 'EMAIL_FINDER',
  },
  {
    id: '84f1cce2-fc8d-478b-b732-4dbba2d6df71',
    name: 'Email verification',
    keyCode: 'EMAIL_VERIFICATION',
  },
  {
    id: 'a9490501-81d8-438e-b215-995146a630eb',
    name: 'Dashboard',
    keyCode: 'DASHBOARD',
  },
  {
    id: '393da2f3-7994-4a3b-b664-a93a7ecd05a3',
    name: 'User Management',
    keyCode: 'USER_MANAGEMENT',
  },
  // {
  //   id: '6ef68454-dd75-4edb-bead-14096a55ec27',
  //   name: 'Staff Performance',
  //   keyCode: 'STAFF_PERFORMANCE',
  // },
  {
    id: 'f2a7c8d9-e3b4-5a6c-7d8e-9f0a1b2c3d4e',
    name: 'Duplicated Jobs',
    keyCode: 'DUPLICATED_JOBS',
  },
  {
    id: 'a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d',
    name: 'Team Management',
    keyCode: 'TEAM_MANAGEMENT',
  },
  {
    id: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
    name: 'Accounts',
    keyCode: 'ACCOUNTS',
  },
  {
    id: 'c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f',
    name: 'Contract',
    keyCode: 'CONTRACT',
  },
  {
    id: 'd4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a',
    name: 'CRM',
    keyCode: 'CRM',
  },
  {
    id: 'e5f6a7b8-c9d0-e1f2-3a4b-5c6d7e8f9a0b',
    name: 'Task',
    keyCode: 'TASK',
  },
  {
    id: 'f6a7b8c9-d0e1-f23a-4b5c-6d7e8f9a0b1c',
    name: 'Search',
    keyCode: 'SEARCH',
  },
  {
    id: 'a7b8c9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d',
    name: 'Sync',
    keyCode: 'SYNC',
  },
  {
    id: 'b8c9d0e1-f23a-4b5c-6d7e-8f9a0b1c2d3e',
    name: 'My Leads',
    keyCode: 'MY_LEADS',
  },
  {
    id: 'c9d0e1f2-3a4b-5c6d-7e8f-9a0b1c2d3e4f',
    name: 'Mailbox',
    keyCode: 'MAILBOX',
  },
  {
    id: 'd0e1f23a-4b5c-6d7e-8f9a-0b1c2d3e4f5a',
    name: 'Sequence',
    keyCode: 'SEQUENCE',
  },
  {
    id: 'e1f23a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b',
    name: 'Manual Leads',
    keyCode: 'MANUAL_LEADS',
  },
  {
    id: 'f23a4b5c-6d7e-8f9a-0b1c-2d3e4f5a6b7c',
    name: 'Contact Finder',
    keyCode: 'CONTACT_FINDER',
  },
  {
    id: '3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d',
    name: 'Settings',
    keyCode: 'SETTINGS',
  },
  {
    id: '4b5c6d7e-8f9a-0b1c-2d3e-4f5a6b7c8d9e',
    name: 'Reporting',
    keyCode: 'REPORTING',
  },
  {
    id: '5c6d7e8f-9a0b-1c2d-3e4f-5a6b7c8d9e0f',
    name: 'Contact List',
    keyCode: 'CONTACT_LIST',
  },
  {
    id: '6d7e8f9a-0b1c-2d3e-4f5a-6b7c8d9e0f1a',
    name: 'Company Onboarding',
    keyCode: 'COMPANY_ONBOARDING',
  },
  {
    id: '7e8f9a0b-1c2d-3e4f-5a6b-7c8d9e0f1a2b',
    name: 'Company Approval',
    keyCode: 'COMPANY_APPROVAL',
  },
  {
    id: '8f9a0b1c-2d3e-4f5a-6b7c-8d9e0f1a2b3c',
    name: 'View As',
    keyCode: 'VIEW_AS',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440000',
    name: 'Credit Management',
    keyCode: 'CREDIT_MANAGEMENT',
  },
];

export const licenseType = {
  CONNECTED: 'CONNECTED',
  STANDARD: 'STANDARD',
};

export const userRole = {
  // SUPER_ADMIN: 'SUPER_ADMIN',
  // SALES: 'SALES',
  ADMIN: 'ADMIN',
  MANAGEMENT: 'MANAGEMENT',
  BASIC_USER: 'BASIC_USER',
};

export const SUPER_ADMIN = 'SUPER_ADMIN';
export const SALES = 'SALES';

export const DefaultRoleIds = {
  [SUPER_ADMIN]: '00fe7fea-95fa-4f75-b0dd-68196f447491',
  [userRole.SALES]: '3feb0476-bff6-4fda-8b97-420f9826fb39',
  [userRole.ADMIN]: '2feb0476-bff6-4fda-8b97-420f9826fb39',
  [userRole.MANAGEMENT]: '5feb0476-bff6-4fda-8b97-420f9826fb39',
  [userRole.BASIC_USER]: '400442ee-d48e-45c2-a560-f8442d351011',
};

// Generates a constant object mapping each alphabet letter to a random color (fixed at build time)
export const alphabetColors = Object.fromEntries(
  'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').map((letter) => [
    letter,
    [
      '#e57373', // Red
      '#ba68c8', // Purple
      '#7986cb', // Indigo
      '#64b5f6', // Blue
      '#4dd0e1', // Cyan
      '#4db6ac', // Teal
      '#aed581', // Light green
      '#ffd54f', // Amber
      '#ffb74d', // Orange
      '#e0e0e0', // Grey
      '#90a4ae', // Blue grey
      '#f06292', // Pink
      '#9575cd', // Deep Purple
      '#4fc3f7', // Light Blue
      '#81c784', // Green
      '#fff176', // Yellow
      '#ff8a65', // Deep Orange
      '#a1887f', // Brown
      '#fbc02d', // Dark Yellow
      '#388e3c', // Dark Green
      '#1976d2', // Dark Blue
      '#c62828', // Dark Red
      '#ad1457', // Dark Pink
      '#00695c', // Dark Teal
      '#455a64', // Dark Blue Grey
      '#212121', // Blackish
    ][letter.charCodeAt(0) - 65],
  ])
);
