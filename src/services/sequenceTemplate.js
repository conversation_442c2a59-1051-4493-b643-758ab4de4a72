import { getUserViewAs } from '../helpers/getUserViewAs';
import { API_ROUTES } from '../routing/SequenceTemplate';
import axios from '../utils/axios';

const getSequenceTemplates = async (params = '') => {
  return axios.get(API_ROUTES.ENDPOINT, { params });
};

const createSequenceTemplate = async (template) => {
  const payload ={
    ...template,
    updateFor:  getUserViewAs()
  }
  return axios.post(API_ROUTES.ENDPOINT, payload);
};

const getSingleSequenceTemplate = async (id) => {
  return axios.get(`${API_ROUTES.ENDPOINT}/${id}`);
};

const updateSingleSequenceTemplate = async (template) => {
  return axios.put(`${API_ROUTES.ENDPOINT}/${template?.id}`, template);
};

const deleteSingleSequenceTemplate = async (template) => {
  return axios.delete(`${API_ROUTES.ENDPOINT}/${template?.id}`);
};

export {
  getSequenceTemplates,
  createSequenceTemplate,
  deleteSingleSequenceTemplate,
  getSingleSequenceTemplate,
  updateSingleSequenceTemplate,
};
