import axios from 'axios';
import {
  getEMailViewAs,
  getNameViewAs,
  getUserViewAs,
} from '../helpers/getUserViewAs';
import zileoAxios from '../utils/axios';

export const NYLAS_API_VERSION = '/v3';
export const NYLAS_API_URL = import.meta.env.VITE_NYLAS_API;
export const NYLAS_CLIENT_ID = import.meta.env.VITE_NYLAS_CLIENT_ID;
export const NYLAS_API_KEY = import.meta.env.VITE_NYLAS_API_KEY;

export const NYLAS_API_GRANT = '/grants';

export const NYLAS_ROUTER = {
  SCHEDULING_UI_SETTINGS: '/scheduling/ui-settings',
  SCHEDULING_CONFIGURATIONS: '/scheduling/configurations',
};

const nylasApi = axios.create({
  baseURL: NYLAS_API_URL,
  headers: {
    Authorization: `Bearer ${NYLAS_API_KEY}`,
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});

export const getNylasSchedulingConfig = async (configId) => {
  return await nylasApi.get(
    `${NYLAS_API_VERSION}${NYLAS_ROUTER.SCHEDULING_UI_SETTINGS}?configuration_id=${configId}`
  );
};

export const getNylasSchedulingConfigs = async (userId = null) => {
  console.log('userId: ', userId);
  const userToSetId = userId || getUserViewAs();
  return await zileoAxios.get(
    `/emails/schedule/get-my-events/view-as/${userToSetId}`
  );
};

export const getCalendars = async (userId) => {
  const userToSetId = userId || getUserViewAs();
  return await zileoAxios.get(
    `/emails/schedule/calendars/view-as/${userToSetId}`
  );
};

export const createNewEvent = async (payload, user = null) => {
  const userToSetId = user?.id || getUserViewAs();

  if (!payload) return;
  const newPayload = {
    ...payload,
    participants: [
      ...payload.participants.map((item) => ({
        ...item,
        email: user?.email || getEMailViewAs(),
        name: user?.fullName || getNameViewAs(),
      })),
    ],
  };
  return await zileoAxios.post(
    `/emails/schedule/my-events/view-as/${userToSetId}`,
    newPayload
  );
};

export const getDetailNylasSchedulingConfig = async (
  configurationId,
  userId = null
) => {
  const userToSetId = userId || getUserViewAs();

  return await zileoAxios.get(
    `/emails/schedule/my-events/view-as/${userToSetId}/configuration/${configurationId}`
  );
};

export const deleteNylasSchedulingConfig = async (configurationId, userId) => {
  const userToSetId = userId || getUserViewAs();
  return await zileoAxios.delete(
    `/emails/schedule/my-events/view-as/${userToSetId}/configuration/${configurationId}`
  );
};

export const updateNylasSchedulingConfig = async (
  payload,
  configurationId,
  userId = null
) => {
  const userToSetId = userId || getUserViewAs();
  return await zileoAxios.put(
    `/emails/schedule/my-events/view-as/${userToSetId}/configuration/${configurationId}`,
    payload
  );
};

export const getTimezoneList = async () => {
  return await zileoAxios.get(`/emails/timezones`);
};
