import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';
import { notification } from 'antd';

export const postSearch = async (searchData) => {
  searchData.updatedFor = getUserViewAs();
  const { data } = await axios.post('/job-search', searchData);
  return { data };
};

export const getSearches = async (
  sortField,
  sortOrder,
  page = 1,
  limit = 10,
  searchText
) => {
  return await axios.get(
    `/job-search/get-all-user-searches/view-as/${getUserViewAs()}?sortOrder=${sortOrder}&sortField=${sortField}&page=${page}&limit=${limit}&searchText=${searchText ?? ''}`
  );
};

export const getSyncSearches = async (
  sortField,
  sortOrder,
  page = 1,
  limit = 10,
  searchText
) => {
  return await axios.get(
    `/search-sync/searches/view-as/${getUserViewAs()}?sortOrder=${sortOrder}&sortField=${sortField}&page=${page}&limit=${limit}&searchText=${searchText}`
  );
};

export const getSearchById = async (
  searchId,
  extendedFilterObject,
  page = 1,
  limit = 10
) => {
  delete extendedFilterObject.previousPage;
  let queryString = 'jobTitles=';
  if (
    extendedFilterObject?.jobTitles ||
    extendedFilterObject?.jobTitles?.length > 0
  ) {
    if (Array.isArray(extendedFilterObject?.jobTitles)) {
      queryString =
        'jobTitles=' + extendedFilterObject?.jobTitles?.join('&jobTitles=');
    } else {
      queryString = `jobTitles=${extendedFilterObject?.jobTitles}`;
    }
  }

  const jsonToSearch = Object.assign({}, extendedFilterObject);
  delete jsonToSearch.jobTitles;
  if (
    extendedFilterObject?.newJobOnly === 'true' &&
    extendedFilterObject?.notificationId !== ''
  ) {
    return await axios.get(
      `/job-search/${searchId}/view-as/${getUserViewAs()}/open-search?page=${page ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
    );
  }
  delete extendedFilterObject?.newJobOnly;
  delete extendedFilterObject?.notificationId;
  return await axios.get(
    `/job-search/${searchId}/view-as/${getUserViewAs()}/open-search?limit=${limit}&page=${page ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
  );
};

export const getSearchByIdDynamo = async (
  searchId,
  extendedFilterObject,
  exclusiveStartKey,
  limit = 10
) => {
  if (
    extendedFilterObject?.newJobOnly === 'true' &&
    extendedFilterObject?.notificationId !== ''
  )
    return await axios.get(
      `/dynamodb/${searchId}/view-as/${getUserViewAs()}?limit=${limit}&exclusiveStartKey=${exclusiveStartKey}&${new URLSearchParams(extendedFilterObject).toString()}`
    );
  delete extendedFilterObject?.newJobOnly;
  delete extendedFilterObject?.notificationId;
  return await axios.get(
    `/dynamodb/${searchId}/view-as/${getUserViewAs()}?limit=${limit}&exclusiveStartKey=${exclusiveStartKey}&${new URLSearchParams(extendedFilterObject).toString()}`
  );
};

export const updateSearchById = async (searchId, payload) => {
  payload.updatedFor = getUserViewAs();
  return await axios.put(`/job-search/${searchId}`, payload);
};
export const startSearchById = async (searchId) => {
  try {
    const payload = {};
    payload.updatedFor = getUserViewAs();
    return await axios.put(`/job-search/${searchId}/start`, payload);
  } catch (error) {
    return error;
  }
};

export const stopSearchById = async (searchId) => {
  try {
    const payload = {};
    payload.updatedFor = getUserViewAs();
    return await axios.put(`/job-search/${searchId}/stop`, payload);
  } catch (error) {
    return error;
  }
};

export const pinJobToSearchId = async (searchId, jobId) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  const enCodeJobId = encodeURIComponent(jobId);
  return await axios.put(`/job-search/${searchId}/${enCodeJobId}/pin`, payload);
};

export const unpinJobToSearchId = async (searchId, jobId) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  return await axios.put(
    `/job-search/${searchId}/${encodeURIComponent(jobId)}/unpin`,
    payload
  );
};

export const getPinnedJobsSearchById = async (searchId, includeSentJob) => {
  return await axios.get(
    `/job-search/${searchId}/pinned/view-as/${getUserViewAs()}?${includeSentJob ? `includeSentJob=${includeSentJob}` : ''}`
  );
};

export const deleteJobsV2ById = async (searchId, jobId) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  payload.jobId = jobId;
  return await axios.delete(`/job-search/${searchId}/remove-job-v2`, {
    data: payload,
  });
};

export const deleteJobsSearchById = async (searchId, jobId) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  return await axios.delete(`/job-search/${searchId}/remove-job/${jobId}`, {
    data: payload,
  });
};

export const deleteJobsSearchByCompanyName = async (searchId, companyName) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  payload.companyName = companyName;
  payload.userViewAs = getUserViewAs();
  return axios.delete(`/job-search/${searchId}/remove-job-by-company`, {
    data: payload,
  });
};

export const deleteJobSearchBySearchId = async (searchId) => {
  const payload = {};
  payload.updatedFor = getUserViewAs();
  return await axios.delete(`/job-search/${searchId}`, payload);
};

export const deletePinnedJobGroupByCompany = async (searchId, jobIds) => {
  return await axios.delete(
    `/job-search/${searchId}/remove-pin-job-group-by-company`,
    { data: jobIds }
  );
};

export const getListNotifications = async (page, pageSize) => {
  return await axios.get(
    `/notification/view-as/${getUserViewAs()}?page=${page}&limit=${pageSize}`
  );
};

export const getCountUnreadNotifi = async () => {
  return await axios.get(
    `/notification/view-as/${getUserViewAs()}/count-unread`
  );
};

export const updateNotificationIsRead = async (id) => {
  return await axios.patch(`/notification/${id}/update`, { isRead: true });
};

export const updateNotificationAllRead = async () => {
  return await axios.post(
    `notification/mark-all-read/view-as/${getUserViewAs()}`
  );
};

export const getCountNewSearch = async () => {
  return await axios.get('/job-search/count-new-search');
};

export const generateEmailData = async (data) => {
  return await axios.post('/emails/generate', data);
};

export const generateSubject = async (jobId) => {
  return await axios.get(`/emails/subject/${encodeURIComponent(jobId)}`);
};

export const generateContent = async (data) => {
  return await axios.post(`/emails/content`, data);
};

export const generateContentChildContent = async (data) => {
  return await axios.post(`/emails/gen-email/by-child-content`, data);
};

export const generateContentChildSubject = async (data) => {
  return await axios.post(`/emails/gen-email/by-child-subject`, data);
};

export const generateAIAssistantMessage = async (data) => {
  return await axios.post(`/open-ai/generate`, data);
};

export const getKeywordSuggestions = async (query) => {
  return await axios.get(`/open-ai/suggestion/keywords?query=${query}`);
};

export const getTitleSuggestions = async (query) => {
  return await axios.get(`/open-ai/suggestion/title?query=${query}`);
};

export const generateFreeContent = async (data) => {
  return await axios.post(`/emails/free-content`, data);
};

export const generateFreeSubject = async (data) => {
  return await axios.post(`/emails/free-subject`, data);
};

export const generateJobSkills = async (jobId) => {
  if (!jobId) return { data: { result: [] } };
  const data = {
    jobId,
    regenerate: true,
  };
  return await axios.post(`/open-ai/generate-job-skills`, data);
};

export const generateMailContentOptionsV2 = async (data) => {
  return await axios.post(`/open-ai/paraphrase-job-skills`, data);
};

export const getAllEmails = async (
  status = null,
  page = 1,
  limit = 10,
  keyword = null,
  createdFrom = null,
  emailSearch = null,
  fromDate = null,
  toDate = null
) => {
  return await axios.get(
    `/emails/view-as/${getUserViewAs()}?limit=${limit}&page=${page}${status ? `&status=${status}` : ''}${keyword ? `&keyword=${keyword}` : ''}${createdFrom ? `&createdFrom=${createdFrom}` : ''}${emailSearch ? `&emailSearch=${emailSearch}` : ''}${fromDate ? `&fromDate=${fromDate}` : ''}${toDate ? `&toDate=${toDate}` : ''}`
  );
};

export const getListEmailFromSequence = async (id) => {
  return await axios.get(`/emails/sequences/${id}`);
};

export const deleteEmailFromSequence = async (id) => {
  return await axios.delete(`/emails/sequences/${id}`);
};

export const deleteManySequence = async (data) => {
  return await axios.post(`/emails/sequences/delete-many`, data);
};

export const updateMarkCompleted = async (data) => {
  return await axios.patch(`/emails/sequences/update-mark-complete`, data);
};

export const bulkStopSequence = async (data) => {
  return await axios.patch(`/emails/bulk/pause-sequences`, data);
};

export const updateSequence = async (id, data) => {
  return await axios.put(`/emails/sequences/${id}`, data);
};

export const getActivityLog = async (
  id,
  {
    page = 1,
    limit = 10,
    stepType = null,
    stepId = null,
    actionType = null,
    search = null,
  }
) => {
  return await axios.get(`/emails/email-activity-log/${id}`, {
    params: {
      page,
      limit,
      ...(actionType ? { activityType: actionType } : {}),
      ...(stepId ? { stepId } : {}),
      ...(stepType ? { stepType } : {}),
      ...(search ? { searchText: search } : {}),
    },
  });
};

export const requestPermission = async (id) => {
  return await axios.get(`/emails/grant/send-grant-link/view-as/${id}`);
};

export const deleteBulkSearches = async (body) => {
  return await axios.patch(`/job-search/delete-bulk-search`, body);
};

export const checkContactWarning = async (payloads) => {
  return await axios.post(
    `/bullhorn-integration/check-contact-warning`,
    payloads,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const getContactBhDetail = async (id) => {
  return await axios.get(`/bullhorn-integration/client-contact/${id}`, {
    headers: {
      'view-as-user-id': getUserViewAs(),
    },
  });
};

export const getPaginationSearchById = async (
  searchId,
  extendedFilterObject,
  page = 1,
  pageSize = 10
) => {
  delete extendedFilterObject.sort;
  delete extendedFilterObject.previousPage;
  let queryString = 'jobTitles=';
  if (
    extendedFilterObject?.jobTitles ||
    extendedFilterObject?.jobTitles?.length > 0
  ) {
    if (Array.isArray(extendedFilterObject?.jobTitles)) {
      queryString =
        'jobTitles=' + extendedFilterObject?.jobTitles?.join('&jobTitles=');
    } else {
      queryString = `jobTitles=${extendedFilterObject?.jobTitles}`;
    }
  }

  const jsonToSearch = Object.assign({}, extendedFilterObject);
  delete jsonToSearch.jobTitles;

  if (
    extendedFilterObject?.newJobOnly === 'true' &&
    extendedFilterObject?.notificationId !== ''
  )
    return await axios.get(
      `/job-search/${searchId}/pagination-info/view-as/${getUserViewAs()}/open-search?page=${page ?? ''}&limit=${pageSize ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
    );
  delete extendedFilterObject?.newJobOnly;
  delete extendedFilterObject?.notificationId;
  return await axios.get(
    `/job-search/${searchId}/pagination-info/view-as/${getUserViewAs()}/open-search?page=${page ?? ''}&limit=${pageSize ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
  );
};

export const getPaginationGroupByCompanies = async (
  searchId,
  extendedFilterObject,
  page = 1,
  pageSize = 10
) => {
  delete extendedFilterObject.previousPage;
  let queryString = 'jobTitles=';
  if (
    extendedFilterObject?.jobTitles ||
    extendedFilterObject?.jobTitles?.length > 0
  ) {
    if (Array.isArray(extendedFilterObject?.jobTitles)) {
      queryString =
        'jobTitles=' + extendedFilterObject?.jobTitles?.join('&jobTitles=');
    } else {
      queryString = `jobTitles=${extendedFilterObject?.jobTitles}`;
    }
  }

  const jsonToSearch = Object.assign({}, extendedFilterObject);
  delete jsonToSearch.jobTitles;

  if (
    extendedFilterObject?.newJobOnly === 'true' &&
    extendedFilterObject?.notificationId !== ''
  )
    return await axios.get(
      `/job-search/${searchId}/companies/pagination-info/view-as/${getUserViewAs()}/open-search?page=${page ?? ''}&limit=${pageSize ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
    );
  delete extendedFilterObject?.newJobOnly;
  delete extendedFilterObject?.notificationId;
  return await axios.get(
    `/job-search/${searchId}/companies/pagination-info/view-as/${getUserViewAs()}/open-search?page=${page ?? ''}&limit=${pageSize ?? ''}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`
  );
};

export const getSequenceReport = async (seqId) => {
  return axios.get(`/emails/sequences/${seqId}/report`);
};

export const generateMailAnalyze = async (data) => {
  return await axios.post(`/gemini/mail-analytics`, data);
};

export const getActivityLogsStats = async (seqId = '') => {
  if (!seqId) {
    throw new Error('Sequence ID is required');
  }
  return await axios.get(`/emails/activity-logs/${seqId}/stats`);
};
