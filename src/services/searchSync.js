import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const postSync = async (syncData) => {
  syncData.updatedFor = getUserViewAs();
  return await axios.post('/search-sync', syncData);
};

export const getSyncs = async () => {
  return axios.get(`/search-sync/view-as/${getUserViewAs()}`);
};

export const getSearchesBySyncId = async (syncId, pagination) => {
  return axios.get(
    `/search-sync/${syncId}/view-as/${getUserViewAs()}?page=${pagination.page}&limit=${pagination.limit}`
  );
};

export const postToggleSync = async (payload) => {
  payload.updatedFor = getUserViewAs();
  return await axios.post('/search-sync/toggle-sync', payload);
};

export const getToggleSyncStatus = async (searchId) => {
  return axios.get(
    `/search-sync/toggle-sync-status/${searchId}/view-as/${getUserViewAs()}`
  );
};

export const getExcludeFilterData = async (searchId) => {
  return axios.get(
    `/search-sync/toggle-sync-status/${searchId}/view-as/${getUserViewAs()}`
  );
};

export const saveExcludeFilterData = async (searchId, payload) => {
  return axios.post(
    `/search-sync/toggle-sync-status/${searchId}/view-as/${getUserViewAs()}`,
    payload
  );
};
