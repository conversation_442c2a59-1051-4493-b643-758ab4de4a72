import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const LEAD_SHEET_TYPE = {
  VACANCY: 'VACANCY',
  OPPORTUNITY: 'OPPORTUNITY',
  LEAD: 'LEAD',
};

export const getLeadSheetsByType = (leadStatusType) => {
  return axios.get(
    `/lead-sheet/?leadStatusType=${leadStatusType}&userId=${getUserViewAs()}`
  );
};

export const getAllLeadSheets = () => {
  return axios.get(`/lead-sheet/?userId=${getUserViewAs()}`);
};

export const createNewLeadSheet = (data) => {
  const payload = {
    ...data,
    updateFor: getUserViewAs(),
  };
  return axios.post(`/lead-sheet`, payload);
};

export const deleteLeadSheet = (leadSheetId) => {
  return axios.delete(`/lead-sheet/${leadSheetId}`);
};

export const editLeadSheet = (leadSheet) => {
  const { id } = leadSheet;
  return axios.patch(`/lead-sheet/${id}`, leadSheet);
};
