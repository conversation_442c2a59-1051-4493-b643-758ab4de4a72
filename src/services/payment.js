import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const getPaymentInfor = async (subscriptionId) => {
  return await axios.post('/payments/pay-for-subscription', {
    subscriptionId,
  });
};

export const confirmPayment = async (paymentIntentId) => {
  return await axios.post('/payments/confirm-payment', { paymentIntentId });
};

export const getPaymentMethods = async () => {
  return await axios.get('/payments/cards');
};
