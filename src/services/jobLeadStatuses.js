import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const LEAD_STATUS_TYPE = {
  VACANCY: 'VACANCY',
  OPPORTUNITY: 'OPPORTUNITY',
  LEAD: 'LEAD',
};

export const fetchLeadStatuses = (leadSheetId) => {
  return axios.get(`/lead-statuses/view-as/${getUserViewAs()}?leadSheetId=${leadSheetId}`);
};

export const bulkUpdateLeadStatus = (payload) => {
  return axios.post('/lead-statuses/bulk-update', {
    data: payload,
    updatedFor: getUserViewAs(),
  });
};

export const getStatusLeads = (leadStatusType) => {
  return axios.get(
    `/lead-statuses/simple-list/view-as/${getUserViewAs()}?leadStatusType=${leadStatusType}`
  );
};
