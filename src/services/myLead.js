import axios from '../utils/axios';

export const getListLeadMailBox = async ({ pageParam = 1, queryKey }) => {
  const [, userId] = queryKey;
  return await axios.get(
    // hard code limit = 10 for avoid send request to check no more data (Currently this method only use for infinite scroll purpose)
    `job-lead/leads/view-as/${userId}/mail-box?page=${pageParam}&limit=10`
  );
};

export const countLeadMailBox = async (userId) => {
  return await axios.get(`job-lead/count-leads/view-as/${userId}/mail-box`);
};

export const updateStatusLeadMailBox = async (userId, leadId) => {
  return await axios.patch(
    `job-lead/leads/view-as/${userId}/update-status-mail-box/${leadId}`
  );
};

export const getMyLeadCompany = async (userId, search) => {
  return await axios.get(`/job-lead/lead-companies/view-as/${userId}?search=${search || ''}`);
};

export const getLeadsByCompany = async (userId, companyId) => {
  return await axios.get(`/job-lead/leads-by-company/view-as/${userId}/company/${companyId}`);
};
