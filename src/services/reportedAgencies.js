import axios from '../utils/axios';

export const getReportedAgencies = async (
  page = 1,
  limit = 10,
  searchName = '',
  status = ''
) => {
  return await axios.get(
    `/reported-agency?page=${page}&limit=${limit}&searchName=${searchName}${status === 'ALL' || !status ? '' : `&status=${status}`}`
  );
};

export const getJobsByAgencyId = async (id, page = 1) => {
  return await axios.get(`/reported-agency/${id}?page=${page}`);
};

export const createReportedAgency = async (companyName = '') => {
  return await axios.post('/reported-agency', {
    companyName: companyName,
  });
};

export const getFuzzySearch = async (companyName = '') => {
  const encodedCompanyName = encodeURIComponent(companyName);
  return await axios.get(
    `/reported-agency/get-fuzzy-search/${encodedCompanyName}`
  );
};

export const createReportedAgencyAlias = async (data) => {
  return await axios.post('/reported-agency', data);
};

export const updateReportStatus = async (id, status) => {
  const payload = {
    status, // APPROVED|REJECTED
  };
  return await axios.post(`/reported-agency/${id}/review`, payload);
};

export const deleteReportedAgency = async (id) => {
  return await axios.delete(`/reported-agency/${id}`);
};

export const bulkUpdateReportedAgencyAlias = async (values) => {
  return await axios.put('/reported-agency/bulk', { values });
};

export const exportReportedAgencies = async ({
  agencyType,
  delimiter,
  fromDate,
  toDate,
}) => {
  let queryText = '';
  if (agencyType) {
    queryText += `agencyType=${agencyType || ''}`;
  }
  if (delimiter) {
    queryText += `&delimiter=${delimiter || ''}`;
  }
  if (fromDate && fromDate !== 'Invalid date') {
    queryText += `&fromDate=${fromDate || ''}`;
  }
  if (toDate && toDate !== 'Invalid date') {
    queryText += `&toDate=${toDate || ''}`;
  }
  return await axios.get(`/reported-agency/export?${queryText}`);
};

export const bulkCreateReportedAgency = async (data) => {
  return await axios.post('/reported-agency/bulk-insert', data);
};
