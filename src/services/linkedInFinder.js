import { getUserViewAs } from '../helpers/getUserViewAs';
import axios from '../utils/axios';

export const searchLinkedInQueryParams = async ({ type, keywords, limit }) => {
  return await axios.get(
    `/linkedin-finder/view-as/${getUserViewAs()}/query-parameter?keywords=${keywords}&limit=${limit || 1000}&type=${type}`
  );
};

export const searchLinkedInContact = async (data, cursor) => {
  return await axios.post(
    `/linkedin-finder/view-as/${getUserViewAs()}/get-contact-linkedin?${cursor ? `cursor=${cursor}` : ''}`,
    data
  );
};

export const searchParameters = async (data) => {
  return await axios.post(
    `/linkedin-finder/view-as/${getUserViewAs()}/search-parameters`,
    data
  );
};

export const queryParameters = async ({ type, keywords }) => {
  return await axios.get(
    `/linkedin-finder/view-as/${getUserViewAs()}/query-parameter?keywords=${keywords || 'a'}&limit=10&type=${type}`
  );
};

export const searchPeople = async (payload) => {
  return await axios.post(
    `/linkedin-finder/view-as/${getUserViewAs()}/search-people`,
    payload
  );
};

export const getListCompanyLinkedin = async (payload) => {
  return await axios.post(
    `/linkedin-finder/view-as/${getUserViewAs()}/search-companies`,
    payload
  );
};

export const getCompanyDetail = async (companyId) => {
  return await axios.get(
    `/linkedin-finder/view-as/${getUserViewAs()}/company-detail/${companyId}`
  );
};

export const getContactDetail = async (payload) => {
  if(!payload?.identifier?.trim()){
    return new Error('Please provide a valid identifier');
  }
  return await axios.post(
    `/linkedin-finder/view-as/${getUserViewAs()}/retrieve-profile`,
    payload
  );
};
