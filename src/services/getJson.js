import axios from '../utils/axios';

export const getKeywordJobSearch = async () => {
  return await axios.get('/job-search/keyword-job-search');
};

export const updateKeywordJobSearch = async (value) => {
  const data = {
    name: value,
  };
  return await axios.put('/job-search/keyword-job-search', data);
};

export const getSuggestionKeywords = async (keyword) => {
  return await axios.get(`/suggestions/keywords?query=${keyword}`);
};
