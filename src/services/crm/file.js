import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const createNewCRMFile = async (payload = null) => {
  return await axios.post(`/crm/file`, payload);
};

export const getAllFiles = async ({
  resourceName = '',
  resourceId = '',
  page = 1,
  limit = 100,
}) => {
  if (!resourceName || !resourceId) {
    throw new Error('Resource Name and Resource ID is required');
  }
  return await axios.get(`/crm/file`, {
    params: {
      page,
      limit,
      resourceName,
      resourceId,
    },
  });
};

export const updateFileById = async (newFile = null) => {
  if (!newFile) {
    throw new Error('file is required');
  }
  const id = newFile?.id;
  return await axios.patch(
    `/crm/file/${id}`,
    newFile
  );
};

export const deleteFile = async (fileId = '') => {
  if (!fileId) {
    throw new Error('file ID is required');
  }
  return await axios.delete(
    `/crm/file/${fileId}`
  );
};
