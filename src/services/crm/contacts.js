import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';
import { contactsCheckerFunc, getEmailStatus } from '../bullhorn';

const mappingData = (data) =>
  data?.map((item) => ({
    name: `${item?.firstName} ${item?.middleName} ${item?.surName}`,
    id: item?.id,
    email: item?.email,
    fax: '',
    phone: item?.telephone,
    status: '',
    trackTitle: null,
    clientCorporation: {
      id: item?.companyId?.id,
      name: item?.companyId?.name,
    },
    occupation: item?.jobTitle,
    address: {
      address1: item?.address?.address1,
      address2: item?.address?.address2,
      city: item?.address?.city,
      countryCode: '',
      countryID: 0,
      countryName: item?.address?.country,
      state: item?.address?.county,
      timezone: null,
      zip: item?.address?.postCode,
    },
    businessSectors: {
      total: 0,
      data: [],
    },
    massMailOptOut: false,
    firstName: item?.firstName,
    lastName: item?.surName,
    middleName: item?.middleName,
    customText1: null,
    dateLastVisit: null,
    dateLastModified: item?.updatedAt,
    dateAdded: item?.createdAt,
    owner: {},
  }));

export const createNewCRMContact = async (payload = null) => {
  return await axios.post(`/crm/contact`, payload);
};

export const getAllContacts = async ({
  keyword = '',
  page = 1,
  limit = 10,
  companyIds = [],
}) => {
  return await axios.get(`/crm/contact`, {
    params: {
      keyword,
      page,
      limit,
      companyIds,
    },
  });
};

export const updateContactById = async (newContact = null) => {
  if (!newContact) {
    throw new Error('Contact is required');
  }
  const ContactId = newContact?.id;
  return await axios.patch(
    `/crm/contact/${ContactId}`,
    newContact
  );
};

export const getContactById = async (contactId = '') => {
  if (!contactId) {
    throw new Error('Contact ID is required');
  }
  return await axios.get(
    `/crm/contact/${contactId}`
  );
};

export const deleteContact = async (contactId = '') => {
  if (!contactId) {
    throw new Error('Contact ID is required');
  }
  return await axios.delete(
    `/crm/contact/${contactId}`
  );
};
// must be the same as the one in bullhorn-integration
export const searchContactsData =
  (entityName) =>
  async (_start, _count, searchText, companyId, _queryId, _email) => {
    const keyword = encodeURIComponent(searchText || '');
    return new Promise((resolve, reject) => {
      axios
        .get(`/crm/contact`, {
          params: {
            keyword,
            page: 1,
            count: 100,
            companyIds: [companyId],
          },
        })
        .then(async (res) => {
          if (entityName === 'Contact') {
            try {
              // changing format of the response to match the one in bullhorn-integration
              const gottenContactData = mappingData(
                res?.data?.result?.data || []
              );
              const newRes = {
                data: {
                  result: [...gottenContactData],
                },
              };
              const contactList = [...newRes?.data?.result];
              const emails = contactList.map((item) => item?.email);
              const [emailsStatus, contactsChecker] = await Promise.all([
                getEmailStatus(emails),
                contactsCheckerFunc(emails),
              ]);
              newRes.data.result = contactList.map((contact) => ({
                ...contact,
                emailStatus:
                  emailsStatus
                    .flat(Infinity)
                    .find(
                      (contactMail) => contactMail?.email === contact?.email
                    )?.result || null,
                isInActiveSequence: contactsChecker?.includes(contact?.email),
              }));
              resolve(newRes);
            } catch (error) {
              resolve(res);
            }
          } else {
            resolve(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
