import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const getCRMTagsByUser = async () => {
  return await axios.get(`/crm/tag`);
};

export const getCRMTagsByOrg = async (organizationId = '') => {
  return await axios.get(`/crm/tag`, {
    params: {
      organizationId,
    },
  });
};

export const createTag = async (payload = null) => {
  // Payload example
  // {
  //     "name": "string",
  //     "organization": "string",
  //     "description": "string"
  //   }
  return await axios.post(`/crm/tag`, payload);
};

export const editTag = async ({ id, name, description }) => {
  return await axios.patch(`/crm/tag/${id}`, { name, description });
};

export const deleteTag = async (tagId) => {
  return await axios.delete(`/crm/tag/${tagId}`);
};
