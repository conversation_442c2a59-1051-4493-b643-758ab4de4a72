import { RESOURCE_NAME } from '../../containers/CRM/crm.constant';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const getAllSequences = async ({
  entity,
  entityId, // resourceId
  page = 1,
  limit = 100,
}) => {
  if (!entityId) {
    throw new Error('entityId is required');
  }
  return await axios.get(
    `crm/${entity?.toLowerCase()}/${entityId}/sequences`,
    {
      params: {
        page,
        limit,
      },
    }
  );
};
