import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const createNewCompany = async (payload = null) => {
  return await axios.post(`/crm/company`, payload);
};

export const getAllCompanies = async ({
  keyword = '',
  page = 1,
  limit = 10,
}) => {
  return await axios.get(`/crm/company`, {
    params: {
      ...(keyword && { keyword }),
      page,
      limit,
    },
  });
};

export const updateCompanyById = async (newCompany = null) => {
  if (!newCompany) {
    throw new Error('Company is required');
  }
  const companyId = newCompany?.id;
  return await axios.patch(
    `/crm/company/${companyId}`,
    newCompany
  );
};

export const getCompanyById = async (companyId = '') => {
  if (!companyId) {
    throw new Error('Company ID is required');
  }
  return await axios.get(`/crm/company/${companyId}`);
};

export const deleteCompany = async (companyId = '') => {
  if (!companyId) {
    throw new Error('Company ID is required');
  }
  return await axios.delete(`/crm/company/${companyId}`);
};
