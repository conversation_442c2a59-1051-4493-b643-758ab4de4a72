import { getUserViewAsOrganizationId } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const getAllIndustries = async (keyword = '') => {
  return await axios.get(`/crm/industry`, {
    params: {
      organizationId: getUserViewAsOrganizationId(),
    },
  });
};

export const getCRMIndustriesByOrg = async (organizationId = '') => {
  return await axios.get(`/crm/industry`, {
    params: {
      organizationId,
    },
  });
};

export const createIndustry = async (payload = null) => {
  // Payload example
  // {
  //     "name": "string",
  //     "organization": "string",
  //     "description": "string"
  //   }
  return await axios.post(`/crm/industry`, payload);
};

export const editIndustry = async ({ id, name, description }) => {
  return await axios.patch(`/crm/industry/${id}`, { name, description });
};

export const deleteIndustry = async (industryId) => {
  return await axios.delete(`/crm/industry/${industryId}`);
};
