import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const createNewCRMLead = async (payload = null) => {
  return await axios.post(`/crm/lead`, payload);
};

export const getAllLeads = async ({
  keyword = '',
  page = 1,
  limit = 10,
  jobType = [],
  companyIds = [],
}) => {
  return await axios.get(`/crm/lead`, {
    params: {
      keyword,
      page,
      limit,
      ...(jobType?.length > 0 && { jobType }),
      ...(companyIds?.length > 0 && { companyIds }),
    },
  });
};

export const updateLeadById = async (newLead = null) => {
  if (!newLead) {
    throw new Error('Lead is required');
  }
  const leadId = newLead?.id;
  return await axios.patch(
    `/crm/lead/${leadId}`,
    newLead
  );
};

export const getLeadById = async (leadId = '') => {
  if (!leadId) {
    throw new Error('Lead ID is required');
  }
  return await axios.get(`/crm/lead/${leadId}`);
};

export const deleteLead = async (leadId = '') => {
  if (!leadId) {
    throw new Error('Lead ID is required');
  }
  return await axios.delete(
    `/crm/lead/${leadId}`
  );
};
