import { getUserViewAs } from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const createNewCRMNote = async (payload = null) => {
  return await axios.post(`/crm/note`, payload);
};

export const getAllNotes = async ({
  resourceName = '',
  resourceId = '',
  page = 1,
  limit = 100,
}) => {
  if (!resourceName || !resourceId) {
    throw new Error('Resource Name and Resource ID is required');
  }
  return await axios.get(`/crm/note`, {
    params: {
      page,
      limit,
      resourceName,
      resourceId,
    },
  });
};

export const updateNoteById = async (newNote = null) => {
  if (!newNote) {
    throw new Error('Note is required');
  }
  const id = newNote?.id;
  return await axios.patch(
    `/crm/note/${id}`,
    newNote
  );
};

export const deleteNote = async (noteId = '') => {
  if (!noteId) {
    throw new Error('Note ID is required');
  }
  return await axios.delete(
    `/crm/note/${noteId}`
  );
};
