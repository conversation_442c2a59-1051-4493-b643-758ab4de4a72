import {
  getUserViewAs,
  getUserViewAsOrganizationId,
} from '../../helpers/getUserViewAs';
import axios from '../../utils/axios';

export const getAllSkills = async (keyword = '') => {
  return await axios.get(`/crm/skill`);
};

export const getCRMSkillsByOrg = async () => {
  return await axios.get(`/crm/skill`);
};

export const createSkill = async (payload = null) => {
  // Payload example
  // {
  //   "data": [
  //     {
  //       "name": "string",
  //       "organizationId": "string",
  //       "description": "string"
  //     }
  //   ]
  // }

  const data = {
    data: [
      {
        name: payload?.name,
        organizationId: getUserViewAsOrganizationId(),
        description: payload?.description,
      },
    ],
  };

  return await axios.post(`/crm/skill`, data);
};

export const editSkill = async ({ id, name, description }) => {
  return await axios.put(`/crm/skill/${id}`, { name, description });
};

export const deleteSkill = async (skillId) => {
  const skillIds = Array.isArray(skillId) ? skillId : [skillId];
  return await axios.delete(`/crm/skill`, {
    params: {
      ids: skillIds.join(','),
    },
  });
};
