import axios from '../utils/axios';
import { getUserViewAs, isViewAs } from '../helpers/getUserViewAs';
import { getContentTypeLinkedinMessage } from '../utils/common';

export const getListMailBox = async(pageToken, limit) => {
        return await axios.get(
                `/emails/mailbox/get-view-as/${getUserViewAs()}?${pageToken ? `pageToken=${pageToken}` : ''}${limit ? `&limit=${limit}` : ''}`
  );
};

export const getDetailMailBox = async (payload) => {
  return await axios.post(`/emails/mailbox-thread`, payload);
};

export const handleSendMailBox = async (payload) => {
  return await axios.post(`emails/send-personal-mail`, payload);
};

export const deleteMailBoxId = async (payload, userId = '') => {
  return await axios.delete(
    `emails/unlink?type=${payload ? payload : ''}`,
    payload
  );
};

export const updateMailBox = async (id, payload) => {
  return await axios.put(`emails/update-thread/${id}`, payload);
};

export const deleteMailBox = async (payload) => {
  return await axios.delete(`emails/delete-thread/`, { data: payload });
};

export const getLiveFeed = async (data) => {
  return await axios.get(
    `emails/view-as/${getUserViewAs()}/live-feed?${data?.consultant ? `consultant=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}${data?.type && data?.type !== 'all' ? `&type=${data?.type}` : ''}${data?.offset ? `&offset=${data?.offset}` : ''}`
  );
};

export const getLinkedinMessages = async (cursor) => {
  return await axios.get(
    `/emails/linkedin-message/get-view-as/${getUserViewAs()}?${cursor ? `cursor=${cursor}` : ''}`
  );
};

export const getLinkedinMessagesInChat = async (chatId) => {
  return await axios.get(
    `/emails/linkedin-message/chatId/${chatId}`
  );
};

export const getRetrieveOwnProfile = async () => {
  return await axios.get(
    `/emails/linkedin-message/get-view-as/${getUserViewAs()}/retrieve-own-profile`
  );
};

export const syncLinkedInMessageData = async () => {
  return await axios.get(
    `/emails/linkedin-message/get-view-as/${getUserViewAs()}/sync-messages-data`
  );
};

export const getLinkedInTotalMessage = async () => {
  return await axios.get(
    `/emails/total-linkedin-message/view-as/${getUserViewAs()}`
  );
};

export const getAttachmentMessages = async (
  messageId,
  attachmentId,
  contentType = 'img'
) => {
  const headerOptions = getContentTypeLinkedinMessage(contentType);
  return await axios.get(
    `/emails/linkedin-message-file/get-view-as/${getUserViewAs()}/message/${messageId}/attachmentId/${attachmentId}?type=${contentType}`,
    {
      headers: {
        ...headerOptions,
      },
    }
  );
};

export const createMessageInChat = async (payload) => {
  return await axios.post(
    `/emails/view-as/${getUserViewAs()}/create-message-in-chat`,
    payload,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};