import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const updateReadNotification = async ({ id, data }) => {
  return await axios.patch(`/notification/${id}/update`, data);
};

export const markAllRead = async () => {
  return await axios.post('/notification/mark-all-read');
};

export const markAllReadViewAs = async () => {
  return await axios.post(
    `/notification/mark-all-read/view-as/${getUserViewAs()}`
  );
};

export const sendEmail = async (data) => {
  return await axios.post('/emails/send', data);
}
