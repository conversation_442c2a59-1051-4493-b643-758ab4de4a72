import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';

export const getJobsLeads = async (leadSheetId = '') => {
  return await axios.get(
    `/lead-statuses/view-as/${getUserViewAs()}?leadSheetId=${leadSheetId}`
  );
};

export const deleteLead = async (jobLeadId) => {
  return await axios.delete(`/job-lead/${jobLeadId}`);
};

export const updatePotentialLeadPercentValue = async (leadPercent) => {
  return await axios.put(`/users/me/view-as/${getUserViewAs()}`, {
    updatedFor: getUserViewAs(),
    potentialLeadValue: leadPercent,
  });
};

export const getPotentialLeadPercentValue = async () => {
  return await axios.get(`/users/me/view-as/${getUserViewAs()}`);
};

export const getPotentialLeadReturn = async (data) => {
  return getPotentialLeadReturnById(getUserViewAs(), data);
};

export const getLeadCounts = async (data) => {
  return await axios.get(
    `/job-lead/count-leads/${getUserViewAs()}?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getLeadCountsForEachStatus = async () => {
  return getLeadCountsForEachStatusById(getUserViewAs());
};

export const getLeadCountsForEachStatusById = async (id) => {
  return await axios.get(`/job-lead/count-for-each-lead-status/${id}`);
};

export const getLeadCountsById = async (id) => {
  return await axios.get(`/job-lead/count-leads/${id}`);
};

export const getPotentialLeadReturnById = async (id, data) => {
  return await axios.get(
    `/users/lead-value/${id}?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const createSentJob = async (payload) => {
  return await axios.post(`/job-lead/leads/sent-job`, payload);
};

export const getSentJob = async () => {
  return await axios.get(`/job-lead/lead/view-as/${getUserViewAs()}/sent-job`);
};

export const getJobDetail = async (id) => {
  return await axios.get(`/job-lead/job/${encodeURIComponent(id)}`);
};

export const deleteNewLead = async (jobLeadId) => {
  return await axios.delete(`/job-lead/leads/sent-job/${jobLeadId}`);
};

export const bulkDeleteLeads = async (payload) => {
  return await axios.delete(`/job-lead/bulk-delete`, { data: payload });
};

export const bulkChangeLeadsStatus = async (payload) => {
  return await axios.put(`/job-lead/bulk-change-status`, payload);
};

export const masterRefineLeads = async (payload) => {
  let queryText = `?search=${payload?.search || ``}&start=${payload?.start || 0}&count=${payload?.count || 100}`;

  if (payload?.required) {
    queryText += `&required=${payload?.required}`;
  }
  if (payload?.excluded) {
    queryText += `&excluded=${payload?.excluded}`;
  }
  if (payload?.datePostedFrom) {
    queryText += `&datePostedFrom=${payload?.datePostedFrom}`;
  }
  if (payload?.datePostedTo) {
    queryText += `&datePostedTo=${payload?.datePostedTo}`;
  }
  if (payload?.datePosted) {
    queryText += `&datePosted=${payload?.datePosted}`;
  }
  if (payload?.inRange) {
    queryText += `&inRange[]=${payload?.inRange[0]}&inRange[]=${payload?.inRange[1]}`;
  }
  if (payload?.outRange) {
    queryText += `&outRange[]=${payload?.outRange[0]}&outRange[]=${payload?.outRange[1]}`;
  }
  if (payload?.salary) {
    queryText += `&salary=${payload?.salary}`;
  }
  if (payload?.minSalary) {
    queryText += `&minSalary=${payload?.minSalary}`;
  }
  if (payload?.maxSalary) {
    queryText += `&maxSalary=${payload?.maxSalary}`;
  }

  if (payload?.jobType) {
    queryText += `&jobType=${payload?.jobType}`;
  }
  if (payload?.source) {
    queryText += `&source=${payload?.source}`;
  }

  return await axios.get(
    `/job-lead/search-vacancies/view-as/${getUserViewAs()}${queryText}`
  );
};

export const getCountForEachLeadStatus = async (data) => {
  return await axios.get(
    `/job-lead/count-for-each-lead-status/${getUserViewAs()}?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getStatsKeyword = async (data, page) => {
  return axios.get(
    `/job-lead/stats/view-as/${getUserViewAs()}/job-keywords?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}${page ? `&page=${page}` : ''}`
  );
};

export const getStatsNewLead = async (data) => {
  return axios.get(
    `/job-lead/stats/view-as/${getUserViewAs()}/new-leads?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getContactAdded = async (data) => {
  return axios.get(
    `/job-lead/stats/view-as/${getUserViewAs()}/contact-added?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getCompaniesAdded = async (data) => {
  return axios.get(
    `/job-lead/stats/company-added?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getEmailSent = async (data) => {
  return await axios.get(
    `emails/stats/view-as/${getUserViewAs()}/email-sent?${data?.consultant ? `consultant=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getActiveSequence = async (data) => {
  return await axios.get(
    `emails/stats/view-as/${getUserViewAs()}/active-sequence?${data?.consultant ? `consultant=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const markLeadStar = async (jobLeadId, payload) => {
  return await axios.put(`/job-lead/star/${jobLeadId}`, payload);
};

export const checkExistingJobLeadByExternalIds = async (ids = []) => {
  if (ids?.length === 0) {
    return [];
  }
  return await axios.get(
    `job-lead/view-as/${getUserViewAs()}/check-leads-by-external-ids`,
    {
      params: {
        ids,
      },
    }
  );
};
