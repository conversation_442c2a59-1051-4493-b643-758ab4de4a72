import axios from '../utils/axios';
import {
  getUserViewAs,
  getUserViewAsOrganizationId,
} from '../helpers/getUserViewAs';

export const UserSettingEnum = {
  DASHBOARD: 'dashboard',
  BULLHORN_VACANCY_FORM: 'bullhorn_vacancy_form',
  CRM_LEAD: 'crm_lead',
  CRM_CONTACT: 'crm_contact',
  CRM_COMPANY: 'crm_company',
};

export const OrganizationStatusEnum = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
};

export const getUserDetail = async (id) => {
  return await axios.get(`/users/${id}`);
};

export const getUsers = async (organizationId, searchValue = '') => {
  return axios.get(
    `/users/view-as/${getUserViewAs()}?${organizationId ? `organizationId=${organizationId}` : ''}${searchValue ? `&searchText=${searchValue}` : ''}`
  );
};

export const inviteUser = async (invitedUsers) => {
  return await axios.post(`/users/invite-user/view-as/${getUserViewAs()}`, {
    invitedUsers,
  });
};

export const deleteUser = async (userId) => {
  return await axios.delete(`/users/${userId}`);
};

export const updatePermissions = async ({ userId, permissionsPayload }) => {
  const userFor = getUserViewAs();
  permissionsPayload.updatedFor = userFor;
  return await axios.patch(`/users/update-user/${userId}`, permissionsPayload);
};

export const getUserOrganizations = async () => {
  return axios.get('/users/organizations');
};

export const getCreditManagementOrganizations = async () => {
  return axios.get(
    `/users/view-as/${getUserViewAs()}/credit-management/organizations`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const getUserOrganizationDetail = async (id) => {
  return axios.get(
    `/users/organization/${id || getUserViewAsOrganizationId()}`
  );
};

export const getCreditManagementDetail = async (organizationId) => {
  return axios.get(`users/credits/management`, {
    params: { organizationId },
  });
};

export const upsertCreditManagementDetail = async (orgId, body) => {
  return axios.put(
    `users/organization-id/${orgId}/upsert-credit-management`,
    body
  );
};

export const getUserRoles = async () => {
  return axios.get('/users/roles');
};

export const getUserSetting = async () => {
  return axios.get(`/user-settings/view-as/${getUserViewAs()}`);
};

export const updatUserSetting = async (data) => {
  const userFor = getUserViewAs();
  data.updatedFor = userFor;
  return axios.post('/user-settings', data);
};

export const updateConsultant = async (data, id) => {
  return axios.put(`/users/update-consultant/${id}`, data);
};

export const updateUserBullhornConfig = async (body) => {
  return await axios.patch(`/users/${getUserViewAs()}/update-bullhorn-config`, {
    clientId: body.clientId,
    clientSecret: body.clientSecret,
    username: body.username,
    password: body.password,
  });
};

export const linkedInConnections = async (body, userId) => {
  return await axios.post(`/users/view-as/${userId}/linkedin-connection`, body);
};

export const unLinkedInConnections = async (userId) => {
  return await axios.delete(`/users/view-as/${userId}/un-linkedin-connection`);
};

export const reConnectedAccount = async (data, userId) => {
  return await axios.post(
    `/users/view-as/${userId}/reconnect-uniplie-account`,
    data
  );
};

export const resolveUnipileCheckpoint = async (data, userId) => {
  return await axios.post(
    `/users/view-as/${userId}/resolve-unipile-checkpoint`,
    data
  );
};

export const resendUnipileCheckpoint = async (data, userId) => {
  return await axios.post(
    `/users/view-as/${userId}/resend-unipile-checkpoint`,
    data
  );
};

export const retrieveAccount = async (id) => {
  return await axios.get(`/users/retrieve-uniplie-account/${id}`);
};

export const updateUniplileAccountInApp = async (data, userId) => {
  return await axios.post(
    `/users/view-as/${userId}/update-uniplie-account-in-app`,
    data
  );
};

// export const retrieveAccount = async (id) => {
//   return await axios.get(`/users/retrieve-uniplie-account/${id}`);
// };

// export const updateUniplileAccountInApp = async (data) => {
//   return await axios.post(`/users/view-as/${getUserViewAs()}/update-uniplie-account-in-app`, data);
// };

export const getUserBullhornConfig = async (body) => {
  return await axios.get(`/users/${getUserViewAs()}/get-bullhorn-config`);
};

export const getMetrics = async () => {
  return await axios.get(`/users/view-as/${getUserViewAs()}/metrics`);
};

export const updateUserPassword = async (userId, password) => {
  return await axios.patch(`/users/view-as/${userId}/update-password`, {
    password,
  });
};

export const getSequenceMetrics = async (data) => {
  return await axios.get(
    `/users/view-as/${getUserViewAs()}/sequence-metrics?${data?.consultant ? `userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

export const getLinkedinMetrics = async (action, data) => {
  return await axios.get(
    `/users/view-as/${getUserViewAs()}/linkedin-metrics?action=${action}${data?.consultant ? `&userId=${data?.consultant}` : ''}${data?.country ? `&country=${data?.country}` : ''}${data?.fromDate ? `&fromDate=${data?.fromDate}` : ''}${data?.toDate ? `&toDate=${data?.toDate}` : ''}`
  );
};

// Companies features

export const getCompanies = async () => {
  return await axios.get(`/users/organizations`);
};

export const getCompanyDetail = async (id) => {
  return await axios.get(`/users/organization/${id}`);
};

export const createNewCompany = async (data) => {
  return await axios.post(`/users/organization`, data);
};

export const updateCompany = async (data, id) => {
  return await axios.put(`/users/organization/${id}`, data);
};

export const deleteCompany = async (id) => {
  return await axios.delete(`/users/organization/${id}`);
};

export const uploadImage = async (data) => {
  return await axios.post(`/users/upload-avatar`, data);
};

export const uploadFile = async (data) => {
  return await axios.post(`/users/upload-file`, data);
};

export const getUnipileProfile = async () => {
  return axios.get(`/users/view-as/${getUserViewAs()}/get-uniplie-profile`);
};

export const getSequenceTimeSettings = async () => {
  return axios.get(`/users/view-as/${getUserViewAs()}/working-times`);
};

export const updateSequenceTimeSettings = async (payload) => {
  return axios.post(`/users/view-as/${getUserViewAs()}/working-times`, payload);
};

export const reConnectLinkedinAccount = async (data, userId) => {
  return await axios.post(
    `/users/view-as/${userId}/reconnect-uniplie-account`,
    data
  );
};

export const linkGrantUnipile = async (userId) => {
  return await axios.get(`/users/unipile/${userId}/link`);
};

export const unlinkGrantUnipile = async (userId) => {
  return await axios.delete(`/users/unipile/${userId}/unlink`);
};

export const onboardingCompany = async (data) => {
  return await axios.post(
    `/users/view-as/${getUserViewAs()}/onboarding-company`,
    data
  );
};

export const updateOnboardingCompanyStatus = async (data) => {
  // {
  //   status: OrganizationStatusEnum;
  //   companyId: string;
  // }
  return await axios.put(
    `/users/view-as/${getUserViewAs()}/onboarding-company/status`,
    data
  );
};

export const singleOnboardingEmployee = async (data) => {
  // {
  //   status: OrganizationStatusEnum;
  //   companyId: string;
  // }
  return await axios.post(
    `/users/view-as/${getUserViewAs()}/single-onboarding-employee`,
    data
  );
};

export const createOrganizationDocument = async (orgId, payload) => {
  // {
  //   note: string;
  //   fileId: string;
  // }
  return await axios.post(`/users/organization/${orgId}/documents`, payload);
};

export const deleteOrganizationDocument = async (orgId, fileId) => {
  const encodedFileId = encodeURIComponent(fileId);
  const encodedOrgId = encodeURIComponent(orgId);
  return await axios.delete(
    `/users/organization/${encodedOrgId}/documents/${encodedFileId}`
  );
};

export const createDomainAuthenticate = async (payload) => {
  return await axios.post(`/users/domain-authentication/create`, payload);
};

export const verifyDomainAuthenticate = async () => {
  return await axios.get(`/users/domain-authentication/verify`);
};

export const getCompaniesAdded = async () => {
  return await axios.get(`/users/metrics/list-company-added`);
};

export const getSubscriptionsDashboard = async (orgId) => {
  return await axios.get(`/subscriptions/dashboard`, {
    params: {
      orgId,
    },
  });
};

// This endpoint is used to get the system credit stats for Super Admin and Sales
export const getSystemCreditStats = async () => {
  return axios.get(`users/credits/stats`);
};

export const getCreditStats = async (organizationId) => {
  if (!organizationId) {
    organizationId = getUserViewAsOrganizationId();
  }
  return axios.get(`users/credits/stats`, {
    params: { organizationId },
  });
};

export const getCreditPackages = async () => {
  return axios.get(`users/credits/packages`);
};

export const purchaseCreditsTopup = async (payload) => {
  console.log('purchaseCreditsTopup payload', payload);
  return axios.post(`users/credits/topup`, payload);
};

export const confirmCreditsTopup = async (payload) => {
  // payload should contain { paymentIntentId: string }
  console.log('confirmCreditsTopup payload', payload);
  return axios.post(`users/credits/topup/confirm`, payload);
};

export const getOrgLicenseInfor = async (orgId = null) => {
  const organizationId = orgId || getUserViewAsOrganizationId();
  return axios.get(`users/organization/${organizationId}/licenses`);
};

export const addNewUser = async (payload) => {
  // payload should contain { email: string, role: string }
  return axios.post(`users/add-user`, payload);
};
