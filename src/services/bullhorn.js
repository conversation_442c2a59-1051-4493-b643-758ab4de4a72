import axios from '../utils/axios';
import { useViewAs } from '../store/viewAs';
import { useAuth } from '../store/auth';
import { getUserViewAs } from '../helpers/getUserViewAs';
import { contactsChecker, validListEmail } from './emailFinder';
import { VALID_EMAIL_STATUS } from '../components/BullHorn/EmailtriggerStep';

export const getEmailStatus = async (emails) => {
  try {
    const { data } = await validListEmail({ emails });
    const result = data?.result?.map((item) => ({
      ...item,
      emailStatus: VALID_EMAIL_STATUS.includes(item?.result)
        ? item?.result
        : 'Invalid',
    }));

    console.log(' data?.result', data?.result);
    console.log('result', result);
    return result;
  } catch (error) {
    throw new Error(error.message || 'Error processing email statuses');
  }
};

export const contactsCheckerFunc = async (contactEmails) => {
  try {
    const { data } = await contacts<PERSON>hecker({
      contactEmails,
    });
    return data?.result;
  } catch (error) {
    return [];
  }
};
export const searchBullhornData =
  (entityName) =>
  async (start, count, searchText, companyId, queryId, email) => {
    const queryText = encodeURIComponent(searchText || '');
    return new Promise((resolve, reject) => {
      axios
        .get(
          `/bullhorn-integration/search?entityName=${entityName}&query=${queryText}&start=${start || 0}&count=${((entityName === 'BusinessSector' && 1000) || entityName === 'Skill' ? 100 : 0) || count || 10}&clientCorporationId=${companyId ? companyId : ''}&queryId=${queryId || ''}&email=${email || ''}`,
          {
            headers: {
              'view-as-user-id': getUserViewAs(),
            },
          }
        )
        .then(async (res) => {
          if (entityName === 'ClientContact' && res?.data?.result?.length > 0) {
            try {
              const newRes = { ...res };
              const contactList = [...res?.data?.result];
              const emails = contactList.map((item) => item?.email);
              const [emailsStatus, contactsChecker] = await Promise.all([
                getEmailStatus(emails),
                contactsCheckerFunc(emails),
              ]);
              newRes.data.result = contactList.map((contact) => ({
                ...contact,
                emailStatus:
                  emailsStatus
                    .flat(Infinity)
                    .find(
                      (contactMail) => contactMail?.email === contact?.email
                    )?.result || null,
                isInActiveSequence: contactsChecker?.includes(contact?.email),
              }));
              resolve(newRes);
            } catch (error) {
              resolve(res);
            }
          } else {
            resolve(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  };

export const searchBullhorn = async (
  entityName,
  start,
  count,
  searchText,
  companyId,
  queryId,
  email,
  tearSheetId,
  fromContactFinder = false,
  customSearchField = '',
  customSearchValue = '',
  optionCustom = '',
  typeCustom = '',
  vacancyId = ''
) => {
  return axios.get(
    `/bullhorn-integration/search?entityName=${entityName}&query=${searchText || ''}&start=${start || 0}&count=${count || 10}&clientCorporationId=${companyId ? companyId : ''}&queryId=${queryId || ''}&email=${email || ''}&tearSheetId=${tearSheetId || []}&fromContactFinder=${fromContactFinder}&customSearchField=${customSearchField}&customSearchValue=${customSearchValue}&optionCustom=${optionCustom}&typeCustom=${typeCustom}&vacancyId=${vacancyId}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const searchCommonBulhorn = async (
  entityName,
  query,
  fields,
  start = 0,
  count = 10
) => {
  return axios.get(
    `/bullhorn-integration/common/search?entityName=${entityName}&where=${encodeURIComponent(query)}&fields=${fields}&start=${start}&count=${count}&showTotalMatched=true`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const querySearchCommonBulhorn = async (
  entityName,
  query,
  fields,
  start = 0,
  count = 10
) => {
  return axios.get(
    `/bullhorn-integration/common/query-search?entityName=${entityName}&where=${encodeURIComponent(query)}&fields=${fields}&start=${start}&count=${count}&sort=-dateAdded&showTotalMatched=true`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const searchFileOfBullHorn = async (
  entityName,
  start,
  count,
  searchText,
  companyId,
  queryId
) => {
  return axios.get(
    `/bullhorn-integration/get-file?entityName=${entityName}&query=${searchText || ''}&start=${start || 0}&count=${count || 10}&clientCorporationId=${companyId ? companyId : ''}&queryId=${queryId || ''}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const uploadBHFile = async (payload) => {
  return await axios.post(`/bullhorn-integration/upload-file`, payload, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const sendJobToBullhorn = (data) => {
  if (!data?.companyId || data?.companyId == null) {
    alert('Please select a company to send to the bullhorn');
    return;
  }
  // if (!data?.contactId || data?.contactId == null) {
  //   alert('Please select a contact to send to the bullhorn');
  //   return;
  // }
  return new Promise((resolve, reject) => {
    data.updatedFor = useViewAs.getState().viewerAsUser
      ? useViewAs.getState().profileUser.id
      : useAuth.getState().profile.user.id;
    axios
      .post(`/bullhorn-integration/send-job/view-as/${getUserViewAs()}`, data, {
        headers: {
          'view-as-user-id': getUserViewAs(),
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getCounties = ({ filter = '', countryId }) => {
  return axios.get(
    `/bullhorn-integration/get-counties?filter=${filter}&countryId=${countryId}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const getDataTotal = (
  entityName,
  id,
  customSearchField = '',
  customSearchValue = '',
  optionCustom = '',
  typeCustom = '',
  searchName = ''
) => {
  return axios.get(
    `/bullhorn-integration/get-total-data?entityName=${entityName}&id=${id}&customSearchField=${customSearchField}&customSearchValue=${customSearchValue}&optionCustom=${optionCustom}&typeCustom=${typeCustom}&searchName=${searchName}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const getCountries = (filter) => {
  return axios.get(
    `/bullhorn-integration/get-countries?filter=${filter || ''}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const insertBullhorn = async (payload) => {
  return await axios.put(
    '/bullhorn-integration/insert',
    { ...payload, userId: getUserViewAs() },
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const bulkAddToBullhorn = async (payload) => {
  return await axios.post(
    `/email-finder/bulk-enrich/${getUserViewAs()}/create-task`,
    payload
  );
};

export const insertMassAdvance = async (payload) => {
  return await axios.post(
    '/bullhorn-integration/insert-mass-advance',
    payload,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const upladteBullhorn = async (id, payload) => {
  return await axios.post(`/bullhorn-integration/update/${id}`, payload, {
    headers: {
      'view-as-user-id': getUserViewAs(),
    },
  });
};

export const getSimilarJob = (body) => {
  return axios.post(`job-lead/similar-jobs`, body);
};

export const deleteBullhornContact = async (payload) =>
  await axios.delete(`/bullhorn-integration/delete`, { data: payload });

export const deleteBullhornFile = async (payload) =>
  await axios.delete(`/bullhorn-integration/delete-file`, { data: payload });

export const getBHCompanyDetail = async (companyId) =>
  axios.get(`/bullhorn-integration/search-company?companyId=${companyId}`, {
    headers: {
      'view-as-user-id': getUserViewAs(),
    },
  });

export const sendBhAppointment = (body) => {
  return axios.post(`/bullhorn-integration/send-appointment`, body);
};

export const searchLookup = async (entityName, start, searchText) => {
  return axios.get(
    `/bullhorn-integration/search-lookup?entity=${entityName}&start=${start || 0}&filter=${searchText}`,
    {
      headers: {
        'view-as-user-id': getUserViewAs(),
      },
    }
  );
};

export const bullhornMassUpdate = async (payload) =>
  await axios.post(`/bullhorn-integration/mass-update`, payload);

export const createNewEmailValid = async (payload) =>
  await axios.post(`email-finder/email-validation-results`, payload);

export const getNewEmailValid = async (payload) =>
  await axios.post(`email-finder/get-email-validation-results`, payload);

export const checkHotListEmail = async (id) => {
  return await axios.post(`bullhorn-integration/check-hotlist-email/${id}`);
};

export const searchShortlistByCompanyId = async (id) => {
  return await axios.get(
    `bullhorn-integration/search-short-list/list-vacancy?companyId=${id}`
  );
};

export const getShortlistById = async (id) => {
  return await axios.get(
    `/bullhorn-integration/search-short-list/list-short-list/${id}`
  );
};

export const bulkImportLeads = async (payload = null) => {
  return await axios.post(
    `/bullhorn-integration/bulk-import-job/view-as/${getUserViewAs()}`,
    payload
  );
};

export const searchCorporateEmployees = async (payload = null) => {
  // {
  //   "bhClientId": "string",
  //   "bhUsername": "string",
  //   "bhPassword": "string",
  //   "bhClientSecret": "string"
  // }
  return await axios.post(
    `/bullhorn-integration/guess/search-corporate-user`,
    payload
  );
};
