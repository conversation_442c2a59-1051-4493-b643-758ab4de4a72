import axios from '../utils/axios';
import { getUserViewAs, isViewAs } from '../helpers/getUserViewAs';

export const getJobs = ({ searchData }) => {
  return new Promise((resolve, reject) => {
    axios
      .get(
        `/api/jobs/getAll?company=${searchData.companies}&agency=${searchData.agencies}&joblocationcity=${searchData.city}&maxsalary=${searchData.maxSalary}&minsalary=${searchData.minSalary}&jobtype=${searchData.jobType}&posted=${searchData.posted}&keywords=${searchData.keywords}&jobTitle=${searchData.jobTitle}&source=${searchData.jobboards}&page=${searchData.page}&pageSize=${searchData.pageSize}`
      )
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const syncJobToBullhorn = async (jobId) => {
  await axios.post('/api/sync/syncToBullhorn', {
    jobId,
  });
};

export const addJob = ({ leadData }) => {
  return new Promise((resolve, reject) => {
    leadData.updatedFor = getUserViewAs();
    axios
      .post('/api/jobs/addJob', leadData)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const addEmailAddress = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/jobs/addemailaddress', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const configEmail = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post(`/emails/sequence-config/view-as/${getUserViewAs()}`, data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const saveDraftSequence = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post(`/emails/sequence-config/view-as/${getUserViewAs()}/draft`, data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const triggerEmail = (id) => {
  return new Promise((resolve, reject) => {
    axios
      .post(`/emails/trigger/view-as/${getUserViewAs()}/sequence/${id}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getConfigEmail = (seqId) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/emails/seq-info/${seqId}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getEmailConfigInJobBoard = (jobBoardId, isExternalId = false) => {
  return new Promise((resolve, reject) => {
    axios
      .get(
        `/emails/${encodeURIComponent(jobBoardId)}/view-as/${getUserViewAs()}${isExternalId ? '?type=external' : '?type=internal'}`
      )
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getAddress = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/jobs/getaddress', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const deleteJob = (id) => {
  return new Promise((resolve, reject) => {
    axios
      .delete(`/api/jobs/deleteJob/${id}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const deleteDashboardJob = (id) => {
  return new Promise((resolve, reject) => {
    axios
      .delete(`/api/jobs/deleteDashboardJob/${id}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const saveKeywords = () => {
  return Promise.resolve();
};

export const getKeywords = () => {
  return new Promise((resolve, reject) => {
    axios
      .get('/api/jobs/getJSONData')
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const startScraper = ({ status, keywords, location, jobTitleQuery }) => {
  return new Promise((resolve, reject) => {
    axios
      .get(
        `/api/jobs/startscraper?status=${status}&keywords=${keywords}&location=${location}&jobTitleQuery=${jobTitleQuery}`
      )
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getJobSame = (limit = 10, page = 1, searchText = '') => {
  return new Promise((resolve, reject) => {
    axios
      .get(
        `/job/duplicate-jobs?page=${page}&limit=${limit}&searchText=${searchText}`
      )
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getJobLogs = (jobId) => {
  return new Promise((resolve, reject) => {
    axios
      .post(`/job/logs`, { jobId })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const updateJobLogs = (payload) => {
  return new Promise((resolve, reject) => {
    if (isViewAs()) payload.updatedFor = getUserViewAs();
    axios
      .put(`/job/logs`, payload)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const deleteJobLogs = (payload) => {
  return new Promise((resolve, reject) => {
    if (isViewAs()) payload.updatedFor = getUserViewAs();
    axios
      .delete(`/job/logs`, { data: payload })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const sendTestEmail = async (payload) => {
  const response = await axios.post(
    `emails/view-as/${getUserViewAs()}/send-test-email`,
    payload
  );
  return response;
};

export const updateSequenceName = async (id, name) => {
  return await axios.patch(`emails/sequences/${id}/update-partial-sequence`, {
    name,
  });
};

export const getJobById = (jobId = '') => {
  if (!jobId) {
    return new Error('Job ID is required');
  }
  return axios.get(`/job/${jobId}`);
};
