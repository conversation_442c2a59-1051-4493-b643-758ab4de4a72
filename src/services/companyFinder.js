import { notification } from 'antd';
import axios from '../utils/axios';

export const getDetailCompanyById = ({ organizationId, withOutContext = false }) => {
  
  return new Promise((resolve, reject) => {
    axios
      .get(`/company-finder/${organizationId}?withOutContext=${withOutContext}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        notification.error({ message: err.message });
        reject(err);
      });
  });
};

export const getFacets = () => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/company-finder/mixed-companies/facets`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};