import axios from '../utils/axios';

export const createComment =
  async (leadId, content) => {
    const { data } = await axios.post('/comment', {
        leadId: leadId,
        content: content
    });
    return data ;
};

export const getLeadComment =
  async (leadId) => {
    const { data } = await axios.get(`/comment/${leadId}`);
    return {data} ;
};

export const editComment =
  async (leadId, content) => {
    const { data } = await axios.patch(`/comment/${leadId}`, {
        content: content
    });
    return data ;
};

export const deleteComment =
  async (leadId, content) => {
    const { data } = await axios.delete(`/comment/${leadId}`);
    return data ;
};