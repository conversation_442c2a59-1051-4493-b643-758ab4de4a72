import axios from '../utils/axios';

export const searchAddress = async (searchText) => {
  return axios.get(`/google-map/search-address?query=${searchText}`);
};

export const searchAddressWithOpenStreet = async (searchText) => {
  return axios.get(`/google-map/open-street/search-address?location=${searchText}`);
};

export const getAddressDetailsByPlaceId = async (place_id) => {
  return axios.get(`/google-map/get-address-details?place_id=${place_id}`);
};
