import axios from '../utils/axios';
import { getUserViewAs } from '../helpers/getUserViewAs';
import { isEmpty } from 'lodash';

// export const getAllTasks = async (query = {}) => {
//   return axios.get(
//     `emails/view-as/${getUserViewAs()}/get-list-sequence-tasks`,
//     {
//       params: {
//         ...query,
//       },
//     }
//   );
// };

export const getAllTasks = async (query = {}) => {
  return axios.get(`emails/view-as/${getUserViewAs()}/get-list-tasks`, {
    params: {
      ...query,
    },
  });
};

export const getTotalTypes = async (status) => {
  return axios.get(
    `emails/view-as/${getUserViewAs()}/get-total-task-by-type?${status ? `status=${status}` : ''}`
  );
};

export const getLinkedinProfile = async (linkedinUrl = '') => {
  if (!linkedinUrl || isEmpty(linkedinUrl)) {
    return Promise.reject('Linkedin URL is required');
  }
  return axios.get(
    `emails/view-as/${getUserViewAs()}/get-linkedin-profile-data`,
    {
      params: {
        linkedinUrl,
      },
    }
  );
};

export const updateTask = async (taskId, payload) => {
  return axios.patch(
    `emails/view-as/${getUserViewAs()}/task/${taskId}`,
    payload
  );
};

export const updateTasksByGroupId = async (groupId, payload) => {
  return axios.patch(
    `emails/view-as/${getUserViewAs()}/tasks/groups/${groupId}`,
    payload
  );
};

export const addManualTask = async (payload) => {
  return axios.post(`emails/view-as/${getUserViewAs()}/task`, payload);
};
