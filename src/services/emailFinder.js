import { getUserViewAs } from '../helpers/getUserViewAs';
import { PromisePool } from '@supercharge/promise-pool';
import axios from '../utils/axios';
import { chunk } from 'lodash';
import { splitArray } from '../components/BullHorn/EmailtriggerStep';

export const searchEmployees = async ({
  companyName,
  role,
  companyLinkedinURL,
}) => {
  return axios.get(
    `/email-finder/employees?companyName=${companyName || ''}&role=${role || ''}&companyLinkedinURL=${companyLinkedinURL || ''}`
  );
};

export const searchEmail = async ({
  companyName,
  fullName,
  companyLinkedinURL,
}) => {
  return axios.get(
    `/email-finder/email?companyName=${companyName || ''}&fullName=${fullName || ''}&companyLinkedinURL=${companyLinkedinURL || ''}`
  );
};

export const searchEmails = async ({ companyName, fullName }) => {
  return axios.get(
    `/email-finder/emails?company=${companyName || ''}&fullName=${fullName || ''}`
  );
};

export const addContactToMail = async (data) => {
  return axios.put(`emails/add-contact`, data);
};

export const validEmail = async (data) => {
  return axios.post(`emails/valid-email`, data);
};

export const validListEmail = async (data) => {
  const rawEmails = [...data?.emails];
  const emails = rawEmails?.map((email) => email?.toLowerCase());

  const chunkedEmails = chunk(emails, 15);

  const { results, errors } = await PromisePool.withConcurrency(1)
    .for(chunkedEmails)
    .process(async (emails, index, pool) => {
      return axios.post(`emails/valid-list-email`, { emails });
    });

  const validStatusEmails = results?.flatMap((result) => result?.data?.result);
  const rawValidStatusEmails = validStatusEmails?.map((result) => {
    const index = emails.indexOf(result?.email?.toLowerCase());
    return {
      ...result,
      email: rawEmails[index],
    };
  });
  return { data: { result: rawValidStatusEmails } };
};

export const contactsChecker = async (data) => {
  return axios.post(
    `/emails/view-as/${getUserViewAs()}/validate-contacts-in-active-sequences`,
    data
  );
};

export const findFloquerLocations = async () => {
  return axios.get('/email-finder/floqer/get-locations-data');
};

export const findFloquerSkills = async () => {
  return axios.get('/email-finder/floqer/get-skills-data');
};

export const getClayAccessEmailResults = async (body) => {
  return axios.post(`/email-finder/clay/get-enrich-data-results`, body);
};

export const createSavedSearch = async (body) => {
  return axios.post(
    `/email-finder/saved-searches/view-as/${getUserViewAs()}`,
    body
  );
};

export const getSavedSearch = async (type) => {
  return axios.get(
    `/email-finder/saved-searches/view-as/${getUserViewAs()}?searchType=${type}`
  );
};

export const updateSavedSearch = async (id, body) => {
  return axios.patch(
    `/email-finder/saved-searches/view-as/${getUserViewAs()}/update-contact/${id}`,
    body
  );
};

export const deleteSavedSearch = async (id) => {
  return axios.delete(
    `/email-finder/saved-searches/view-as/${getUserViewAs()}/delete-contact/${id}`
  );
};
