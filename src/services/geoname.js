export const getListCountries = async () => {
  try {
    const response = await fetch(
      'http://api.geonames.org/countryInfoJSON?formatted=true&lang=en&username=azputra'
    );
    if (!response.ok)
      throw new Error('HTTP error, status = ' + response.status);
    const data = await response.json();
    return data;
  } catch (error) {
    return error;
  }
};

export const getListCities = async (code) => {
  try {
    const response = await fetch(
      `http://api.geonames.org/searchJSON?country=${code}&username=azputra`
    );
    if (!response.ok)
      throw new Error('HTTP error, status = ' + response.status);
    const data = await response.json();
    return data;
  } catch (error) {
    return error;
  }
};
