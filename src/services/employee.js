import { getUserViewAs } from '../helpers/getUserViewAs';
import axios from '../utils/axios';
export const getListEmployee = ({
  companyLinkedUrl = '',
  nextPageCode = '',
  organizationId = '',
  page = 1,
  locations = [],
  employeeRanges = [],
  industryTagIds = [],
  personTitles = [],
  searchText = '',
  containOneKeywords = [],
  containAllKeyWords = [],
  excludeKeyWords = [],
  contactEmailStatus = [],
  contactEmailOpened = null,
  contactEmailOpenedAtLeast = null,
  contactEmailOpenedAtDateRangeMin = null,
  contactEmailOpenedAtDateRangeMax = null,
  intentStrengths = [],
  intentIds = [],
  searchSignalIds = [],
  recommendationScoresMinTranche = null,
  currentlyUsingAnyOfTechnologyUids = [],
  existFields = null,
  notExistFields = null,
  organizationTradingStatus = [],
  organizationLatestFundingStageCd = [],
  totalFundingRangeMin = null,
  totalFundingRangeMax = null,
  personName = null,
  organizationJobLocations = [],
  qOrganizationJobTitles = [],
  organizationNumJobsRangeMin = null,
  organizationNumJobsRangeMax = null,
  organizationJobPostedAtRangeMin = null,
  organizationJobPostedAtRangeMax = null,
  qPersonPersonaIds = null,
  mixedAccountIds = null,
  context = null,
  metaMode = null,
  notOrganizationIds = [],
  personPastOrganizationIds = [],
}) => {
  return new Promise((resolve, reject) => {
    const payload = {
      companyLinkedUrl,
      nextPageCode,
      organizationId,
      page,
      locations,
      employeeRanges,
      industryTagIds,
      personTitles,
      searchText,
      containOneKeywords,
      containAllKeyWords,
      excludeKeyWords,
      contactEmailStatuses: contactEmailStatus,
      contactEmailOpened,
      contactEmailOpenedAtLeast,
      contactEmailOpenedAtDateRangeMin,
      contactEmailOpenedAtDateRangeMax,
      intentStrengths,
      intentIds,
      searchSignalIds,
      recommendationScoresMinTranche,
      currentlyUsingAnyOfTechnologyUids,
      existFields,
      notExistFields,
      organizationTradingStatus,
      organizationLatestFundingStageCd,
      totalFundingRangeMin,
      totalFundingRangeMax,
      personName,
      organizationJobLocations,
      qOrganizationJobTitles,
      organizationNumJobsRangeMin,
      organizationNumJobsRangeMax,
      organizationJobPostedAtRangeMin,
      organizationJobPostedAtRangeMax,
      qPersonPersonaIds,
      mixedAccountIds,
      context,
      metaMode,
      notOrganizationIds,
      personPastOrganizationIds,
    };
    axios
      .post('/employee-finder', payload)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getListCompanies = ({
  organizationId = '',
  page = 1,
  locations = [],
  employeeRanges = [],
  industryTagIds = [],
  searchText = '',
  containOneKeywords = [],
  containAllKeyWords = [],
  excludeKeyWords = [],
  searchSignalIds = [],
  accountStageIds = [],
  notAccountStageIds = [],
  recommendationScoresMinTranche = '',
  currentlyUsingAnyOfTechnologyUids = [],
  intentStrengths = [],
  intentIds = [],
  existFields = null,
  notExistFields = null,
  organizationTradingStatus = [],
  organizationLatestFundingStageCd = [],
  totalFundingRangeMin = null,
  totalFundingRangeMax = null,
  organizationJobLocations = [],
  qOrganizationJobTitles = [],
  organizationNumJobsRangeMin = null,
  organizationNumJobsRangeMax = null,
  organizationJobPostedAtRangeMin = null,
  organizationJobPostedAtRangeMax = null,
  notOrganizationIds = [],
  limit = 25,
}) => {
  return new Promise((resolve, reject) => {
    const payload = {
      organizationId,
      page,
      locations,
      employeeRanges,
      industryTagIds,
      searchText,
      containOneKeywords,
      containAllKeyWords,
      excludeKeyWords,
      searchSignalIds,
      accountStageIds,
      notAccountStageIds,
      currentlyUsingAnyOfTechnologyUids,
      intentStrengths,
      intentIds,
      existFields,
      notExistFields,
      organizationTradingStatus,
      organizationLatestFundingStageCd,
      totalFundingRangeMin,
      totalFundingRangeMax,
      organizationJobLocations,
      qOrganizationJobTitles,
      organizationNumJobsRangeMin,
      organizationNumJobsRangeMax,
      organizationJobPostedAtRangeMin,
      organizationJobPostedAtRangeMax,
      notOrganizationIds,
      limit
    };
    if (recommendationScoresMinTranche) {
      payload.recommendationScoresMinTranche = recommendationScoresMinTranche;
    }
    axios
      .post('/company-finder/explore', payload)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getLDetailEmployee = ({ employeeId = '' }) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/employee-finder/${employeeId}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getListCompany = ({ searchText = ' ' }) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/company-finder?searchText=${encodeURIComponent(searchText)}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const employeeFinderSearchTag = ({
  searchText = ' ',
  type = 'location',
}) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/employee-finder/search/tag?searchText=${searchText}&type=${type}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const employeeFinderIntentCategory = () => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/employee-finder/search/intent-data-topic/category`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const employeeFinderIntentByTopic = (topicName) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/employee-finder/search/intent-data-topic/${topicName}`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const employeeFinderSearchSignals = () => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/employee-finder/search/signals`)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getOrganizationsSnippet = async (body) => {
  return await axios.post(`/employee-finder/organizations-snippet`, body);
};

export const getNewsOfCompany = async (body) => {
  return await axios.post(`/employee-finder/search/news`, body);
};

export const getNewFeedEvent = async (body) => {
  return await axios.post(`/employee-finder/search/newsfeed_events`, body);
};

export const getRevenue = (payload) => {
  return new Promise((resolve, reject) => {
    axios
      .post(`/employee-finder/search/facets`, payload)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getRecommendedContactList = async (filter) => {
  return await axios.get(`/employee-finder/search/recommended-contact-list`, {
    params: {
      ...filter,
    },
  });
};

export const handleBulkEnrichContactData = async (payload) =>
  axios.post(`/email-finder/clay/create-enrich-data `, payload);
  // axios.post(`/email-finder/floqer/create-enrich-linkedin-data`, payload);

export const handleGetEnrichData = async (listId, query) =>
  axios.get(`/email-finder/enrich-tasks/${listId}`, {
    params: { ...query },
  });

export const enrichAllContactList = async (listId) =>
  axios.post(
    `/email-finder/view-as/${getUserViewAs()}/enrich-all/contact-list/${listId}`
  );

export const enrichAllHotList = async (listId) =>
  axios.post(
    `/email-finder/view-as/${getUserViewAs()}/enrich-all/hotlist/${listId}`
  );

export const handleFloqerCreateSearchData = async (payload) =>
  axios.post(`/email-finder/floqer/create-search-data`, payload);

export const handleFloqerGetSearchData = async (payload) =>
  axios.post(`/email-finder/floqer/get-search-data`, payload);


export const handleFloqerCreateCompanyDetailSearchData = async (payload) =>
  axios.post(`/email-finder/floqer/create-search-company-detail`, payload);

 export const handleClayCreateSearchData = async (payload) =>
  axios.post(`/email-finder/clay/create-enrich-data`, payload);

export const handleSearchRawData = async (payload) =>
  axios.post(`/linkedin-finder/manual-raw-search`, payload);