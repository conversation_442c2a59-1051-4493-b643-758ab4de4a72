import { getUserViewAs } from '../helpers/getUserViewAs';
import axios from '../utils/axios';
const host = '';
export const signIn = async (data) => {
  return await axios.post('/auth/login', data);
};

export const signUp = async (data) => {
  return await axios.post('/auth/register', data);
};

export const refreshToken = async () => {
  return await axios.post('/auth/refresh-token');
};

export const logout = async () => {
  return await axios.post('/auth/log-out');
};

export const setPermission = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/user/setpermission', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const changePassword = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('api/user/changepassword', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const editUser = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/user/edituser', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const deleteUser = (data) => {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/user/deleteuser', data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getUser = (userId) => {
  return new Promise((resolve, reject) => {
    axios
      .get(`/api/user/singleUser/${userId}`, {
        headers: {
          authorization: `Bearer ${JSON.parse(
            localStorage.getItem(`${host}_token`)
          )}`,
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
export const getAllUsers = (data) => {
  const { page, pageSize } = data;
  return new Promise((resolve, reject) => {
    axios
      .get(`/api/user/getallusers?page=${page}&pageSize=${pageSize}`, {
        headers: {
          authorization: `Bearer ${JSON.parse(
            localStorage.getItem(`${host}_token`)
          )}`,
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getUserByEmail = async (email) => {
  return await axios.get(`/auth/get-user-by-email/${email}`);
};

export const getUserById = async (id) => {
  return await axios.get(`/users/${id}`);
};

export const getDataUniplieAccount = async (userId = '') => {
  return await axios.get(
    `/users/view-as/${userId || getUserViewAs()}/get-uniplie-account`
  );
};
