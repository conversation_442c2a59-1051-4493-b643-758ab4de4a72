import { getUserViewAs } from '../helpers/getUserViewAs';
import axios from '../utils/axios';

export const createNewContactList = async (content) => {
  return await axios.post(`/contact-list/view-as/${getUserViewAs()}`, content, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getExampleFile = async () => {
  return await axios.get(`/contact-list/download-example-csv`, {
    responseType: 'blob',
  });
};

export const downLoadErrorFile = async (data) => {
  return await axios.post(`/contact-list/download-csv-with-data/`, data, {
    responseType: 'blob',
  });
};

export const insertContactToList = async (data) => {
  return await axios.post(
    `/contact-list/view-as/${getUserViewAs()}/insert-to-list`,
    data,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
};

export const getNewContactList = async (exitedId) => {
  return await axios.get(
    `/contact-list/view-as/${getUserViewAs()}?${exitedId ? `exitedId=${exitedId}` : ''}`
  );
};

export const getAllContactLists = async (type) => {
  return await axios.get(`/contact-list/view-as/${getUserViewAs()}`, {
    params: {
      type,
    },
  });
};

export const addUserToList = async (data) => {
  return await axios.post(
    `/contact-list/view-as/${getUserViewAs()}/add-contact-to-list`,
    data
  );
};

export const bulkAddUserToList = async (data) => {
  return await axios.post(
    `/contact-list/view-as/${getUserViewAs()}/add-contact-to-list/bulk`,
    data
  );
};

export const getDetailContactList = async (
  id,
  page = 1,
  searchText = '',
  listContactIds = [],
  limit = 10
) => {
  return await axios.get(
    `/contact-list/view-as/${getUserViewAs()}/list/${id}?page=${page}&limit=${limit}${searchText ? `&searchText=${searchText}` : ''}${listContactIds.length > 0 ? `&listContactIds=${listContactIds.join(',')}` : ''}`
  );
};

export const getAllContact = async () => {
  return await axios.get(
    `/contact-list/view-as/${getUserViewAs()}/list/c447ff93-9a48-41d8-9c41-c6c9265bc16f`
  );
};

export const toggleUpdateSubscribed = async (payload) => {
  return await axios.post(
    `/contact-list/view-as/${getUserViewAs()}/toggle-update-subscribed`,
    payload
  );
};

export const deleteContactList = async (id) => {
  return await axios.delete(
    `/contact-list/view-as/${getUserViewAs()}/list/${id}`
  );
};

export const editContactList = async (id, content) => {
  return await axios.put(
    `/contact-list/view-as/${getUserViewAs()}/list/${id}`,
    content
  );
};

export const deleteContactFromList = async (listId, contactId) => {
  return await axios.delete(
    `/contact-list/view-as/${getUserViewAs()}/contact/${contactId}`
  );
};

export const updateContactFromList = async (listId, contactId, payload) => {
  return await axios.put(
    `/contact-list/view-as/${getUserViewAs()}/contact/${contactId}`,
    payload
  );
};

export const deleteBulkList = async (payload) => {
  return await axios.delete(`/contact-list/bulk-delete-list`, {
    data: payload,
  });
};

export const bulkDeleteContactsInList = async (payload) => {
  return await axios.delete(`/contact-list/bulk-delete-contacts`, {
    data: payload,
  });
};

export const getContactList = async (ids) => {
  if (!ids || ids?.length === 0) return;
  return await axios.get(`/contact-list/simple-list`, {
    params: {
      ids,
    },
  });
};
