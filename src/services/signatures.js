import axios from '../utils/axios';
import { getUserViewAs, isViewAs } from '../helpers/getUserViewAs';

export const createSignatures = async (payload, id = null) => {
  const userId = id || getUserViewAs();
  return await axios.post(`user-signatures/view-as/${userId}`, payload, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getSignatures = async (id = null) => {
  const userId = id || getUserViewAs();
  return await axios.get(`user-signatures/view-as/${userId}`);
};

export const updateSignaturesDefault = async (sigId, id = null) => {
  const userId = id || getUserViewAs();
  return await axios.put(
    `user-signatures/set-default-signature/${sigId}/view-as/${userId}`
  );
};

export const removeSignaturesDefault = async (sigId, id = null) => {
  const userId = id || getUserViewAs();
  return await axios.put(
    `user-signatures/remove-default-signature/${sigId}/view-as/${userId}`
  );
};

export const getSignatureDefault = async (id = null) => {
  const userId = id || getUserViewAs();
  return await axios.get(`user-signatures/view-as/${userId}/default`);
};

export const deleteSignature = async (id, userIdProp = null) => {
  const userId = userIdProp || getUserViewAs();
  return await axios.delete(`user-signatures/${id}/view-as/${userId}`);
};

export const updateSignatures = async (id, payload, userIdProp = null) => {
  const userId = userIdProp || getUserViewAs();
  return await axios.put(`user-signatures/view-as/${userId}/${id}`, payload, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const convertToBase64 = async (url, isUploadToS3 = false) => {
  return await axios.post(`user-signatures/app/url-to-base64`, {
    url,
    isUploadToS3
  });
};
