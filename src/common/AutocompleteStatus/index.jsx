import React, { useEffect, useMemo, useState } from 'react';

import _find from 'lodash/find';
import _get from 'lodash/get';

import PropTypes from 'prop-types';

import { Button, Modal, notification, Select } from 'antd';
import { Controller, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import {
  useChangeLogOfJobLeadsMutation,
  useLazyGetLogOfJobLeadsQuery,
} from '../../app/myLeads';
import { useAuth } from '../../store/auth';
import { selectAllStatusLead } from '../../store/common';
import useJobLeads from '../../hooks/useJobLeads';
import { PlusOutlined } from '@ant-design/icons';
import LeadStatusCRUD from '../../components/JobsLeads/LeadStatusCRUD';

const { Option } = Select;

const AutocompleteStatus = (props) => {
  const {
    lead,
    isUpdateMail = false,
    handleUpdaterDo = false,
    isShowInMailbox,
    leadSheetId,
  } = props;
  const { profile } = useAuth();
  const {
    jobLeads: jobLeadsWithStatuses,
    reloadJobLeads,
    isLoading,
  } = useJobLeads();
  const [getLogOfJobLeads] = useLazyGetLogOfJobLeadsQuery();
  const [isManageStatusesOpen, setIsManageStatusesOpen] = useState(false);
  const [updateStatusLoading, setUpdateStatusLoading] = useState(false);

  const handlShowModal = () => setIsManageStatusesOpen(true);
  const handleCloseModal = () => {
    reloadJobLeads(lead?.leadSheetId || leadSheetId);
    setIsManageStatusesOpen(false);
  };
  // const allStatusLead = useSelector(selectAllStatusLead);

  const userId = useMemo(() => {
    return _get(profile, 'user.id') || '';
  }, [profile]);

  const [changeLogOfJobLeads] = useChangeLogOfJobLeadsMutation();

  const handleChangeLogOfJobLeads = async (result) => {
    setUpdateStatusLoading(true);
    try {
      const payload = {
        jobLeadId: lead?.leadRawId || _get(lead, 'id'),
        newStatusId: result?.status,
        updatedFor: userId,
        isUpdateMailBox: isUpdateMail ? isUpdateMail : null,
      };

      const response = await changeLogOfJobLeads(payload);

      if (_get(response, 'data.success')) {
        await notification.open({
          message: 'Success!',
          description: 'Changed status successfully!',
        });

        if (handleUpdaterDo) {
          handleUpdaterDo();
        }
        const splittedLeadIds = lead?.id?.split('/') || [];
        const currLeadStatusId =
          splittedLeadIds?.length > 0
            ? splittedLeadIds[2]
            : lead?.lead_status_id;

        await getLogOfJobLeads({
          statusId: currLeadStatusId || _get(lead, 'id'),
          page: 1,
          limit: 10,
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Changed status failed!',
        });
      }
      setUpdateStatusLoading(false);
    } catch {
      setUpdateStatusLoading(false);
      notification.error({
        message: 'Error!',
        description: 'Changed status failed!',
      });
    }
  };

  const { handleSubmit, control, setValue } = useForm();

  const onSuccess = async (value) => {
    console.log('value status', value);
    await handleChangeLogOfJobLeads(value);
  };

  const onError = (err) => {
    console.log(err);
  };

  useEffect(() => {
    const splittedLeadIds = lead?.id?.split('/') || [];
    const currLeadStatusId =
      splittedLeadIds?.length > 0
        ? splittedLeadIds[2]
        : _get(lead, 'lead_status_id');
    const defaultValue =
      _get(
        _find(jobLeadsWithStatuses, (item) => item.id === currLeadStatusId),
        'name'
      ) || '';

    setValue('status', isShowInMailbox ? '' : defaultValue);
  }, [jobLeadsWithStatuses, lead]);

  useEffect(() => {
    if (!leadSheetId && !lead?.leadSheetId) return;
    reloadJobLeads(leadSheetId || lead?.leadSheetId);
  }, [leadSheetId, lead?.leadSheetId]);

  return (
    <>
      <Controller
        control={control}
        name="status"
        render={({ field }) => (
          <Select
            loading={isLoading || updateStatusLoading}
            disabled={(!leadSheetId && !lead?.leadSheetId) || isLoading}
            title={
              !leadSheetId && !lead?.leadSheetId && 'Select Lead Sheet first!'
            }
            {...field}
            placeholder="None"
            style={{ width: '100%' }}
            onSelect={(value) => {
              setValue('status', value);
              handleSubmit(onSuccess, onError)();
            }}
            notFoundContent={
              <div className="flex gap-3 flex-col w-full items-center justify-center py-4 border rounded-md">
                <div className="font-semibold">No Status Found</div>
                <Button
                  onClick={handlShowModal}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Add new
                </Button>
              </div>
            }
          >
            {jobLeadsWithStatuses.map((option) => (
              <Option key={option.id} value={option.id}>
                {option.name}
              </Option>
            ))}
          </Select>
        )}
      />
      <Modal
        title={'Manage Lead Statuses'}
        open={isManageStatusesOpen}
        onCancel={handleCloseModal}
        footer={null}
        destroyOnClose={true}
      >
        <LeadStatusCRUD onFinish={handleCloseModal} typeId={leadSheetId} />
      </Modal>
    </>
  );
};

AutocompleteStatus.propTypes = {
  lead: PropTypes.any,
};

export default AutocompleteStatus;
