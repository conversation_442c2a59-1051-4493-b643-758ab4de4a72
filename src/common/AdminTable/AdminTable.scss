.users-wrapper {
  font-family: var(--font-family);
  .admin-table-container {
    .ant-table {
      background: #2e3032;
      font-family: var(--font-family);
      color: #d3d3d3;
      border-color: #1f2021;
      .delete-icon {
        svg {
          height: 1.5em;
          width: 1.5em;
        }
      }
      .ant-table-tbody tr td {
        border-top: none;
        border-bottom: 2px solid #1f2021;
      }
      .ant-table-tbody > tr:last-child > td {
        border-bottom: 2px solid #1f2021;
      }
      .ant-table-column-sorter {
        color: var(--color-white);
      }
    }
    .ant-table-thead tr th {
      background: #2e3032;
      color: var(--color-white);
      border-bottom: 2px solid #1f2021;
    }
    //table-pagination
    .ant-pagination-item-link {
      color: var(--color-white);
    }
    //checkbox header color removed
    .ant-table-thead > tr > td {
      background: transparent;
      border-bottom: 2px solid #1f2021;
    }

    //sorter size and position
    .ant-table-column-sorters {
      display: block;
      .ant-table-column-sorter-down {
        font-size: 16px;
      }
    }

    //table checkbox
    .ant-checkbox-inner {
      // background-color: transparent;
    }
    .ant-checkbox-checked .ant-checkbox-inner {
      // background-color: #fcba2d;
      border-color: #0a0a0a;
    }
    .ant-checkbox-inner:after {
      // tick color
      border-color: #000000;
    }
    //Hover / Highlight Effect of tr

    .ant-table-row:hover td,
    .ant-table-row-selected td {
      // background-color: #696969 !important;
    }
    //right borders color in THead
    .ant-table-wrapper
      .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before,
    .ant-table-wrapper
      .ant-table-thead
      > tr
      > td:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before {
      // background-color: transparent;
    }
    //Pagination
    .ant-pagination .ant-pagination-item {
      // background-color: #2e3032;
      // border-color: #2e3032;
      margin-inline-end: 4px;
      a {
        color: var(--color-white);
      }
    }
    .ant-pagination .ant-pagination-item-active {
      background-color: #fcba2d;
      border-color: #fcba2d;
      a {
        color: #000425;
      }
    }
  }
}
