.vod-form-wrapper {
  width: 100%;
  padding-left: 2%;
  padding-right: 2%;
  .newVod-title {
    display: flex;
    width: 100%;
    color: var(--secondary-color);
  }
  .vod-form-container {
    .browseFields {
      .ant-row {
        display: grid;
      }
    }
    .upload-btn {
      .ant-btn-default {
        height: 48px;
        width: 415px;
        border-radius: 10px;
        box-sizing: border-box;
        border: 2px dashed #1b76ff;
        background: rgba(27, 118, 255, 0.2);
        box-shadow: 3px 3px 10px 1px rgba(0, 0, 0, 0.2);
        span {
          color: #ffffff;
          font-family: poppins;
          font-weight: 400;
          font-size: 14px;
          line-height: 21px;
        }
      }
    }
    .uploader-area {
      display: flex;
      justify-content: space-between;
      background: rgba(27, 118, 255, 0.2);
      border: 2px dashed #1b76ff;
      box-shadow: 3px 3px 10px 1px rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      .file-name {
        color: #ffffff;
        font-family: var(--font-family);
        font-weight: 500;
        font-size: 16px;
        padding: 10px 20px;
        white-space: nowrap;
        width: 60%;
        overflow: hidden;
        text-overflow: clip;
      }
      .uploader-btn {
        color: #034b5e;
        background: #d9d9d9;
        border-radius: 10px;
        padding: 10px 20px;
        font-family: var(--font-family);
        font-weight: 600;
        font-size: 16px;
      }
    }
    .video-error {
      color: #ff4d4f;
    }
    .upload-btn-labels {
      display: flex;
      color: #ffffff;
      font-family: poppins;
      font-weight: 400;
      font-size: 14px;
      line-height: 21px;
    }
    .save-btn {
      background: #fcba2d;
      border-radius: 8px;
      padding: 8px 16px;
      span {
        font-family: 'Roboto';
        font-size: 14px;
        font-weight: 500;
        line-height: 14px;
        display: flex;
        align-items: center;
        color: #000000;
      }
    }
    .checkbox-area {
      display: flex;
      font-family: 'Roboto';
      color: #ffffff;
      font-size: 16px;
      line-height: 16px;
    }
    .ant-input {
      height: 48px;
      color: #ffffff;
      // border-color: #313437;
      border-radius: 10px;
      // background-color: #313437;
      box-shadow: 3px 3px 10px 1px rgba(0, 0, 0, 0.2);
    }
    .ant-input::placeholder {
      font-family: poppins;
      font-weight: 400;
      font-size: 14px;
      line-height: 21px;
      color: #ffffff;
    }
    .ant-select-selector {
      height: 48px;
      color: #ffffff;
      border: 1px solid #313437;
      border-radius: 10px;
      // background-color: #313437;
      text-align: left;
      box-shadow: 3px 3px 10px 1px rgba(0, 0, 0, 0.2);
      .ant-select-selection-item {
        display: flex;
        margin: auto;
        font-family: poppins;
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;
        color: #ffffff;
      }
    }
    .ant-select-selection-placeholder {
      display: flex;
      margin: auto;
      font-family: poppins;
      font-weight: 400;
      font-size: 14px;
      line-height: 21px;
      color: #ffffff;
    }
    .ant-select-arrow {
      color: #ffffff;
    }
    // .ant-select-dropdown {
    //   background-color: red !important;
    // }
    // .rc-virtual-list-holder-inner {
    //   background-color: red !important;
    // }
    .ant-select-item {
      // background-color: red !important;
    }
    .ant-select-item-option-active {
      background-color: var(--main-color);
    }
    .ant-form-item-label > label {
      display: flex;
      color: #ffffff;
      font-size: 16px;
      line-height: 16px;
      font-style: normal;
      align-items: center;
      font-family: poppins;
    }
    input[type='date']::-webkit-calendar-picker-indicator {
      color-scheme: dark;
    }
    .anticon-close {
      color: #ffffff;
    }
  }
  .loader-form-item {
    .ant-form-item-control-input {
      justify-content: center;
    }
    .ant-form-item-control-input-content {
      max-width: 15%;
      display: flex;
      justify-content: space-between;
    }
  }
  .vod-loader {
    margin-top: 5px;
    .ant-spin-dot-item {
      background-color: var(--secondary-color);
    }
  }
}
