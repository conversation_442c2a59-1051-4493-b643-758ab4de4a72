import React, { useEffect, useMemo, useState } from 'react';

import _map from 'lodash/map';
import _get from 'lodash/get';
import _find from 'lodash/find';

import PropTypes from 'prop-types';

import {
  AutoComplete,
  Avatar,
  Button,
  notification,
  Select,
  Spin,
  Typography,
} from 'antd';
import { useForm, Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { selectAllUsers } from '../../store/common';
import {
  useLazyGetLogOfJobLeadsQuery,
  useUpdateLeadStatusMutation,
} from '../../app/myLeads';
import { useAuth } from '../../store/auth';
import { getColorHexFromName, getRandomColor } from '../../function/getRandomColor';
import { stringAvatar } from '../../function/stringAvatar';
import { SendOutlined } from '@ant-design/icons';

const AutocompleteAssign = ({ isModalDetailVisible, lead }) => {
  const { profile } = useAuth();
  const [isSubmit, setIsSubmit] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [newListUser, setNewListUser] = useState();
  const [defaultValueForm, setDefaultValueForm] = useState('');

  const [getLogOfJobLeads] = useLazyGetLogOfJobLeadsQuery();

  const allUsers = useSelector(selectAllUsers);

  useEffect(() => {
    setNewListUser(allUsers)
  }, [allUsers])

  const userId = useMemo(() => {
    return _get(profile, 'user.id') || '';
  }, [profile]);

  const [updateLeadStatus] = useUpdateLeadStatusMutation();

  const handleChangeStatusLead = async (result) => {
    try {
      const payload = {
        jobLeadId: (_get(lead, 'leadRawId')),
        assigneeId: _get(result, 'id'),
        creatorId: _get(lead, 'creatorId'),
        updatedBy: userId,
        consultantId: _get(result, 'consultantId'),
      };

      const response = await updateLeadStatus(payload);

      if (_get(response, 'data.success')) {
        setIsLoading(false);
        await notification.open({
          message: 'Success!',
          description: 'Changed status lead successfully!',
        });

        await getLogOfJobLeads({
          statusId: _get(lead, 'lead_status_id'),
          page: 1,
          limit: 10,
        });
      } else {
        setIsLoading(false);
        notification.error({
          message: 'Error!',
          description: response?.error?.data?.message,
        });
      }
    } catch {
      setIsLoading(false);
      notification.error({
        message: 'Error!',
        description: 'Changed status lead failed!',
      });
    }
  };

  const { control, handleSubmit, setValue } = useForm();

  const onSuccess = async (inputData) => {
    const selectData = _find(
      allUsers,
      (item) =>
        item.username === inputData.assign || item.email === inputData.assign
    );

    await handleChangeStatusLead(selectData);
  };

  const onError = (err) => {
    console.log(err);
  };

  useEffect(() => {
    const defaultValue =
      _get(
        _find(allUsers, (item) => item.id === _get(lead, 'assigneeId')),
        'username'
      ) ||
      _get(
        _find(allUsers, (item) => item.id === _get(lead, 'assigneeId')),
        'email'
      ) ||
      '';

    setValue('assign', defaultValue);
    setDefaultValueForm(defaultValue);
  }, [allUsers, lead, isModalDetailVisible]);

  const handleSearch = (searchText) => {
    setDefaultValueForm(searchText);
    const newArray = [];

    const lowercaseInput = searchText?.toLowerCase();

    allUsers.forEach((obj) => {
      const lowercaseName = obj?.email?.toLowerCase();
      const lowercaseEmail = obj?.username?.toLowerCase();
      if (
        lowercaseName?.includes(lowercaseInput) ||
        lowercaseEmail?.includes(lowercaseInput)
      ) {
        newArray.push(obj);
      }
    });

    setNewListUser(newArray);
  };

  return (
    <>
      <Controller
        control={control}
        name="assign"
        render={({ field }) => (
          <>
            <AutoComplete
              value={defaultValueForm}
              options={_map(newListUser, (option) => ({
                key: _get(option, 'id'),
                value: _get(option, 'username') || _get(option, 'email'),
                label: (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      background:
                        field.value === _get(option, 'username')
                          ? '#e1effe'
                          : 'transparent',
                    }}
                  >
                    <Avatar
                      style={{
                        backgroundColor: getColorHexFromName(
                          _get(option, 'username') || _get(option, 'email')
                        ),
                        verticalAlign: 'middle',
                      }}
                    >
                      {stringAvatar(
                        _get(option, 'username') || _get(option, 'email')
                      )}
                    </Avatar>
                    &ensp;
                    <Typography.Text
                      style={{
                        width: '150px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {_get(option, 'username') || _get(option, 'email')}
                    </Typography.Text>
                  </div>
                ),
              }))}
              placeholder="None"
              style={{
                width: '100%',
              }}
              onSelect={async (value) => {
                setDefaultValueForm(value);
                setIsSubmit(true);
                await field.onChange(value);
                // await handleSubmit(onSuccess, onError)();
              }}
              onSearch={(value) => {
                handleSearch(value);
              }}
            />
            {isSubmit && (
              <Button
                onClick={async (value) => {
                  setIsSubmit(false);
                  setIsLoading(true);
                  await handleSubmit(onSuccess, onError)();
                }}
                icon={<SendOutlined />}
                type="link"
                style={{ float: 'right', marginTop: '-26px' }}
              ></Button>
            )}
            {isLoading && (
              <Spin
                style={{
                  float: 'right',
                  marginTop: '-25px',
                  marginRight: '10px',
                }}
              />
            )}
          </>
        )}
      />
    </>
  );
};

AutocompleteAssign.propTypes = {
  lead: PropTypes.any,
};

export default AutocompleteAssign;
