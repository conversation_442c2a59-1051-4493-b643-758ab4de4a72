import React from 'react';

import PropTypes from 'prop-types';

import { Skeleton, Spin } from 'antd';

// eslint-disable-next-line react/prop-types
const LoadingAdvanced = ({ isSkeleton, className }) => {
  if (isSkeleton)
    return (
      <div className={`mb-8 ${className}`}>
        <Skeleton active className='w-full' rootClassName='w-full' />
      </div>
    );

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: '32px',
        paddingBottom: '32px',
      }}
    >
      <Spin tip="Loading" size="large" />
    </div>
  );
};

LoadingAdvanced.propTypes = {
  isSkeleton: PropTypes.bool,
};

export default LoadingAdvanced;
