import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { arrayUniqueByKey } from '../../utils/common';
import { tagRender } from '../../components/SearchDetailV2/NewSearchFilterComponent';

const DebounceSelect = ({
  fetchOptions,
  debounceTimeout = 200,
  labelValue,
  value,
  notFoundContent,
  defaultOpions = [],
  isClearable,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);
      if (fetchId !== fetchRef.current) {
        // for fetch callback order
        return;
      }
      fetchOptions(value)
        .then((newOptions) => {
          if (fetchId !== fetchRef.current) {
            // for fetch callback order
            return;
          }
          setOptions(newOptions);
        })
        .finally((err) => {
          setFetching(false);
        });
    };
    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  const existingOptions =
    value?.length > 0 && labelValue?.length > 0
      ? value.map((itemValue, index) => ({
          label: labelValue[index],
          value: itemValue,
        }))
      : [];

  useEffect(() => {
    if (!value) return;
  }, []);

  return (
    <Select
      labelInValue
      filterOption={false}
      onSearch={debounceFetcher}
      notFoundContent={
        fetching ? (
          <Spin size="small" />
        ) : notFoundContent ? (
          notFoundContent
        ) : null
      }
      value={isClearable ? [] : value}
      loading={fetching}
      options={arrayUniqueByKey(
        [
          ...options,
          ...existingOptions,
          ...(defaultOpions ? defaultOpions : []),
        ],
        'value'
      )}
      tagRender={(props) => tagRender({ ...props, tagColor: 'tag-cyan' })}
      {...props}
    />
  );
};

export default DebounceSelect;
