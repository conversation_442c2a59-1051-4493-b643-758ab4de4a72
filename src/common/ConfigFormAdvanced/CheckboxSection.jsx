import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

import _isArray from 'lodash/isArray';
import _map from 'lodash/map';

import { Controller } from 'react-hook-form';
import { Checkbox, Divider } from 'antd';

const CheckboxSection = ({ data, setValue, control }) => {
  const checkItemOptions = useMemo(() => {
    return _map(data.children, (item) => item.name);
  }, []);

  const handleCheckboxChange = (e) => {
    setValue(data.name, e.target.checked);

    if (e.target.checked) {
      setValue(`${data.name}-children`, checkItemOptions);
    } else {
      setValue(`${data.name}-children`, []);
    }
  };

  return (
    <>
      <Controller
        name={data.name}
        control={control}
        defaultValue={data.on}
        render={({ field }) => (
          <Checkbox
            {...field}
            checked={field.value}
            onChange={handleCheckboxChange}
          >
            <b>{data.name}</b>
          </Checkbox>
        )}
      />

      <Divider style={{ margin: 8 }} />
      {data.children && _isArray(data.children) && data.children.length > 0 && (
        <Controller
          name={`${data.name}-children`}
          control={control}
          defaultValue={data.children
            .filter((item) => item.on)
            .map((item) => item.name)}
          render={({ field }) => (
            <Checkbox.Group
              // options={data.children.map((item) => item.name)}
              value={field.value}
              onChange={(value) => field.onChange(value)}
            >
              <div className="grid grid-cols-2 gap-3">
                {data.children.map(({ name }) => (
                  <div className={`px-3 py-2 border rounded-md font-semibold ${field.value?.includes(name) ? 'border-[#d5e1fe]' : ''}`}>
                    <Checkbox value={name}>
                      <span className="line-clamp-1" title={name}>
                        {name}
                      </span>
                    </Checkbox>
                  </div>
                ))}
              </div>
            </Checkbox.Group>
          )}
        />
      )}
    </>
  );
};

CheckboxSection.propTypes = {
  data: PropTypes.shape({
    name: PropTypes.string.isRequired,
    on: PropTypes.bool.isRequired,
    children: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        on: PropTypes.bool.isRequired,
      })
    ),
  }).isRequired,
  setValue: PropTypes.func.isRequired,
  control: PropTypes.object.isRequired,
};

export default CheckboxSection;
