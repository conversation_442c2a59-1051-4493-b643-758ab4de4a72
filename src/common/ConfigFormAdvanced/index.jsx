import React, { useEffect, useMemo, useState } from 'react';

import PropTypes from 'prop-types';

import _map from 'lodash/map';
import _endsWith from 'lodash/endsWith';
import _flatMap from 'lodash/flatMap';
import _find from 'lodash/find';
import _get from 'lodash/get';

import {
  Button,
  Col,
  Divider,
  Dropdown,
  Menu,
  Row,
  Typography,
  notification,
} from 'antd';
import {
  SettingOutlined,
} from '@ant-design/icons';
import CheckboxSection from './CheckboxSection';
import { useForm } from 'react-hook-form';
import {
  useChangeConfigFormMutation,
  useLazyGetConfigFormQuery,
} from '../../app/configForm';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import LoadingAdvanced from '../LoadingAdvanced';
import { useDispatch } from 'react-redux';
import { saveConfigForm } from '../../store/common';

const ConfigFormAdvanced = ({
  onClose,
  title,
  formName,
  formDefault,
  subtitle,
  onCloseStep = false,
}) => {
  const dispatch = useDispatch();

  const { profileUser } = useViewAs();
  const { profile } = useAuth();

  const [visible, setVisible] = useState(false);

  const handleVisibleChange = (flag) => {
    setVisible(flag);
  };

  const handleCancelClick = () => {
    setVisible(false);
  };

  const userId = useMemo(() => {
    return _get(profileUser, 'id') || _get(profile, 'user.id') || '';
  }, [profile]);

  const [getConfigForm, { data: configFormData, isLoading }] =
    useLazyGetConfigFormQuery();

  const [changeConfigForm, { isLoading: isLoadingSave }] =
    useChangeConfigFormMutation();

  const handleChangeConfigForm = async (result) => {
    try {
      const payload = {
        userId,
        payload: result,
      };

      const response = await changeConfigForm(payload);

      if (_get(response, 'data.success')) {
        await notification.open({
          message: 'Success!',
          description: 'Changed config form successfully!',
        });

        await getConfigForm({
          userId,
          name: formName,
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Changed config form failed!',
        });
      }
    } catch {
      notification.error({
        message: 'Error!',
        description: 'Changed config form failed!',
      });
    }
  };

  const { control, handleSubmit, setValue, reset } = useForm();

  const onSuccess = async (inputData) => {
    const convertData = await _map(inputData, (value, key) => {
      const section = {
        name: key,
        on: value,
        children: [],
      };

      if (
        _endsWith(key, '-children') &&
        inputData[key.replace('-children', '')]
      ) {
        section.children = _map(inputData[key], (child) => ({
          name: child,
          on: true,
        }));
      }

      return section;
    });

    const transformedData = await _flatMap(convertData, (item) => {
      if (_endsWith(item.name, '-children')) {
        const parentName = item.name.replace('-children', '');
        const parentItem = _find(convertData, { name: parentName });
        parentItem.children = item.on.map((childName) => ({
          name: childName,
          on: true,
        }));
        return [];
      } else {
        return [item];
      }
    });

    await handleChangeConfigForm(transformedData);
  };

  const onError = (err) => {
    console.log(err);
  };

  useEffect(() => {
    if (!onCloseStep) {
      getConfigForm({
        userId,
        name: formName,
      });
    }
  }, []);

  useEffect(() => {
    if (_get(configFormData, 'result.0.value')) {
      dispatch(
        saveConfigForm({
          name: formName,
          data: _get(configFormData, 'result.0.value'),
        })
      );
    }
  }, [_get(configFormData, 'result.0.value')]);

  return (
    <Row justify="space-between" align="middle">
      <Col span="auto" className='w-10/12'>
        <Typography.Title level={2} className='line-clamp-1' title={title}>
          {title}
        </Typography.Title>
        {subtitle && (
          <Typography.Title className="!mt-0 !font-thin line-clamp-1" level={4} title={subtitle}>
            {subtitle}
          </Typography.Title>
        )}
      </Col>
      <Col span="auto">
        <Dropdown
          visible={visible}
          onVisibleChange={handleVisibleChange}
          trigger={['click']}
          placement="bottomRight"
          overlay={
            <Menu>
              <Row
                gutter={[8, 32]}
                style={{
                  background: 'white',
                  padding: '16px',
                  minWidth: '450px',
                  maxWidth: '450px',
                  maxHeight: '400px',
                  overflowY: 'auto',
                }}
              >
                {(isLoading || !_get(configFormData, 'result.0.value')) && (
                  <Col span={24}>
                    <LoadingAdvanced isSkeleton />
                    <LoadingAdvanced isSkeleton />
                  </Col>
                )}

                {!isLoading && _get(configFormData, 'result.0.value') && (
                  <>
                    {_map(
                      _get(configFormData, 'result.0.value'),
                      (item, idx) => (
                        <Col span={24} key={idx.toString()}>
                          <CheckboxSection
                            data={item}
                            control={control}
                            setValue={setValue}
                          />
                        </Col>
                      )
                    )}
                  </>
                )}
              </Row>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  width: '100%',
                  padding: '8px',
                }}
              >
                <Button
                  style={{
                    marginRight: 8,
                  }}
                  onClick={() => reset(formDefault)}
                >
                  Select All
                </Button>

                <Button
                  style={{
                    marginRight: 8,
                  }}
                  onClick={handleCancelClick}
                >
                  Cancel
                </Button>

                <Button
                  type="primary"
                  onClick={handleSubmit(onSuccess, onError)}
                  disabled={isLoadingSave}
                  loading={isLoadingSave}
                >
                  Save
                </Button>
              </div>
            </Menu>
          }
        >
          <SettingOutlined
            className="text-2xl font-bold transition-all hover:text-[#979595]"
            style={{ marginRight: 6, cursor: 'pointer' }}
          />
        </Dropdown>

        {/* <CloseOutlined
          onClick={() => onClose()}
          style={{ cursor: 'pointer' }}
        /> */}
      </Col>
      <Col span={24}>
        <Divider style={{ margin: 0, marginBottom: 16 }} />
      </Col>
    </Row>
  );
};

ConfigFormAdvanced.propTypes = {
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  formName: PropTypes.string.isRequired,
  formDefault: PropTypes.array.isRequired,
  subtitle: PropTypes.string,
};

export default ConfigFormAdvanced;
