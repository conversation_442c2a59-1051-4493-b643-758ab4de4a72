import { getUserViewAs } from '../../helpers/getUserViewAs';
import UserProfilePage from '../EditUserManagement/UserProfilePage';

const UserInformation = (props) => {
  const {setDisableStep} = props
  const userId = getUserViewAs();
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <UserProfilePage userId={userId} setDisableStep={setDisableStep} isOnboarding={true}/>
    </div>
  );
};
export default UserInformation;
