import React from 'react';
import PropTypes from 'prop-types';
import {
  LinkedinOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { Sequence } from '../../components/Sidebar/consts';
import SequencePage from '../Settings/Sequence';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import LinkedIn from '../Settings/LinkedIn';
import MailBox from '../Settings/MailBox';
import { Card } from 'antd';

const Integration = (props) => {
  const {step, setStep} = props
  const userId = getUserViewAs();

  const renderSetupIntegration = (step) => {
    if (step == 1) {
      return (
        <div style={{ width: '100vw' }}>
          <SequencePage userIdProp={userId} />
        </div>
      );
    } else if (step == 2) {
      return (
        <div>
          <LinkedIn userIdProp={userId} />
        </div>
      );
    } else if (step == 3) {
      return (
        <div>
          <MailBox userIdProp={userId} />
        </div>
      );
    } else {
      return (
        <div className="flex flex-col items-center">
          <section className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-4xl mx-auto px-4 py-8">
            <article className="border border-gray-300 rounded-lg p-4 flex flex-col justify-between">
              <div className="flex items-center space-x-3 mb-3">
                <Sequence className="w-8 h-6 object-contain text-cyan-600" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900 mb-1">
                  Link Sequence
                </h2>
                <p className="text-gray-600 text-sm mb-3">
                  A sequence is an ordered series of steps or events.
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  className="bg-cyan-600 text-white rounded-full px-4 py-1 text-sm font-semibold hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-600"
                  type="button"
                  onClick={() => setStep(1)}
                >
                  Connect
                </button>
                <button
                  className="text-gray-900 font-semibold text-sm hover:underline focus:outline-none"
                  type="button"
                >
                  Learn how
                </button>
              </div>
            </article>
            <article className="border border-gray-300 rounded-lg p-4 flex flex-col justify-between">
              <div className="flex items-center space-x-3 mb-3">
                <LinkedinOutlined className="w-12 h-8 text-[#0288d1] font-bold text-2xl object-contain" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900 mb-1">
                  Link LinkedIn
                </h2>
                <p className="text-gray-600 text-sm mb-3">
                  LinkedIn is a professional networking platform for connecting
                  with colleagues, finding jobs, and sharing career-related
                  content.
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  className="bg-cyan-600 text-white rounded-full px-4 py-1 text-sm font-semibold hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-600"
                  type="button"
                  onClick={() => setStep(2)}
                >
                  Connect
                </button>
                <button
                  className="text-gray-900 font-semibold text-sm hover:underline focus:outline-none"
                  type="button"
                >
                  Learn how
                </button>
              </div>
            </article>
            <article className="border border-gray-300 rounded-lg p-4 flex flex-col justify-between">
              <div className="flex items-center space-x-3 mb-3">
                <MailOutlined className="w-12 h-8 text-cyan-700 font-bold text-2xl object-contain" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900 mb-1">Link Mailbox</h2>
                <p className="text-gray-600 text-sm mb-3">
                  A mailbox is a digital storage space for sending, receiving, and
                  managing emails.
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  className="bg-cyan-600 text-white rounded-full px-4 py-1 text-sm font-semibold hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-600"
                  type="button"
                  onClick={() => setStep(3)}
                >
                  Connect
                </button>
                <button
                  className="text-gray-900 font-semibold text-sm hover:underline focus:outline-none"
                  type="button"
                >
                  Learn how
                </button>
              </div>
            </article>
          </section>


        </div>
      );
    }
  };

  return (
    <div>
      <div style={{marginTop: "10px"}}>
        {renderSetupIntegration(step)}
      </div>
    </div>
  );
};

Integration.propTypes = {
  step: PropTypes.number,
  setStep: PropTypes.func
};

export default Integration;
