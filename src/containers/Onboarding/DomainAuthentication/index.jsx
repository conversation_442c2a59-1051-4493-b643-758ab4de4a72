import { useState } from 'react';
import ProviderSelection from './ProviderSelection';
import InputDomain from './InputDomain';
import InstallDNSRecords from './InstallDNSRecords';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { createDomainAuthenticate } from '../../../services/users';

const stepConstants = {
  PROVIDER_SELECTION: 1,
  INPUT_DOMAIN: 2,
  INSTALL_DNS_RECORDS: 3,
};

const DomainAuthentication = (props) => {
  const { parentStep, setParentStep } = props;
  const [domainSetting, setDomainSetting] = useState({
    domain: '',
    dnsRecords: [],
  });

  const [loading, setLoading] = useState(false);
  const [activeNextBtn, setActiveNextBtn] = useState(false);

  const [currentStep, setCurrentStep] = useState(
    stepConstants.PROVIDER_SELECTION
  );

  const stepRenderer = (step) => {
    switch (step) {
      case stepConstants.PROVIDER_SELECTION:
        return <ProviderSelection />;
      case stepConstants.INPUT_DOMAIN:
        return <InputDomain setData={setDomainSetting} data={domainSetting} />;
      case stepConstants.INSTALL_DNS_RECORDS:
        return (
          <InstallDNSRecords
            data={domainSetting.dnsRecords?.cname}
            setCurrentStep={setCurrentStep}
            setActiveNextBtn={setActiveNextBtn}
          />
        );
    }
  };

  const handleCreateDomains = async () => {
    const payload = {
      domain: domainSetting.domain,
    };

    try {
      setLoading(true);
      const { data } = await createDomainAuthenticate(payload);
      setDomainSetting({ ...domainSetting, dnsRecords: data?.dnsRecords });
      setLoading(false);
    } catch {
      setLoading(false);
      notification.error({
        message: 'Some thing went wrongs',
      });
    }
  };

  const handleNextPage = async () => {
    if (currentStep == stepConstants.INPUT_DOMAIN) {
      await handleCreateDomains();
    }
    setCurrentStep(currentStep + 1);
  };

  const handleNextParentStep = () => {
    setParentStep(parentStep + 1);
  };

  return (
    <div className="bg-white text-gray-700">
      {stepRenderer(currentStep)}
      <div className="flex justify-between mt-10 w-full">
        {currentStep > 1 ? (
          <Button
            onClick={() => setCurrentStep(currentStep - 1)}
            icon={<LeftOutlined />}
            className="bg-white"
          >
            Back
          </Button>
        ) : (
          <div />
        )}
        <div className="flex space-x-4">
          {currentStep < 3 && (
            <Button
              onClick={() => handleNextPage()}
              type="primary"
              icon={<RightOutlined />}
              loading={loading}
              disabled={
                currentStep == stepConstants.INPUT_DOMAIN &&
                domainSetting.domain == '' &&
                !domainSetting.domain
              }
            >
              <span className="text-xs">Continue</span>
            </Button>
          )}
          {currentStep == 3 && activeNextBtn && (
            <Button
              onClick={() => handleNextParentStep()}
              type="primary"
              icon={<RightOutlined />}
              loading={loading}
              disabled={
                currentStep == stepConstants.INPUT_DOMAIN &&
                domainSetting.domain == '' &&
                !domainSetting.domain
              }
            >
              <span className="text-xs">Continue</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DomainAuthentication;
