import { Radio, Select } from 'antd';
import styled from 'styled-components';

const ReStyleSelect = styled(Select)`
  .ant-select-selector {
    border-radius: 0rem !important;
    border-top-width: 0 !important;
    border-left-width: 0 !important;
    border-right-width: 0 !important;
  }
`;
const dnsHostOptions = [
  { label: 'GoDaddy', value: 'godaddy' },
  { label: 'Namecheap', value: 'namecheap' },
  { label: 'Cloudflare', value: 'cloudflare' },
  { label: 'Google Domains', value: 'google_domains' },
  { label: 'AWS Route 53', value: 'aws_route_53' },
  { label: 'Bluehost', value: 'bluehost' },
  { label: 'HostGator', value: 'hostgator' },
  { label: 'DreamHost', value: 'dreamhost' },
  { label: '1&1 IONOS', value: 'ionos' },
  { label: 'Dynadot', value: 'dynadot' },
  { label: 'Hover', value: 'hover' },
  { label: 'Name.com', value: 'name_com' },
  { label: 'Network Solutions', value: 'network_solutions' },
  { label: 'Other', value: 'other' },
];

const ProviderSelection = () => {
  return (
    <div className="bg-white text-gray-700">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <p className=" mb-6">
          To make this easier, please answer the following questions.
        </p>

        <form>
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <div className="text-sm flex-shrink-0 w-6 h-6 border border-gray-400 rounded-full  text-gray-600 font-semibold flex items-center justify-center select-none mr-2">
                1
              </div>
              <p className=" font-semibold text-gray-900 max-w-[600px]">
                Which Domain Name Server (DNS) host do you use?
              </p>
            </div>
            <p className="text-sm font-medium text-gray-500 mb-3 max-w-[600px]">
              Your DNS host is often — but not always — the same place you host
              your website.
            </p>
            <label
              for="dns-host"
              className="block text-sm font-medium text-gray-600 mb-1"
            >
              DNS host
            </label>
            <ReStyleSelect
              className="w-full"
              placeholder="Select..."
              options={dnsHostOptions}
            />
          </div>

          <div className="mb-8">
            <div className="flex items-center mb-1">
              <div className="text-sm flex-shrink-0 w-6 h-6 border border-gray-400 rounded-full  text-gray-600 font-semibold flex items-center justify-center select-none mr-2">
                2
              </div>
              <p className=" font-semibold text-gray-900 max-w-[600px]">
                Would you also like to brand the links for this domain?
              </p>
            </div>
            <p className="text-sm font-medium text-gray-400 mb-3 max-w-[600px]">
              This will rewrite all tracking links to use the domain you choose
              — not sendgrid.net
            </p>

            <fieldset className="space-y-1 max-w-[600px]">
              <div className="flex items-center space-x-2">
                <Radio.Group
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 8,
                  }}
                  defaultValue={false}
                  //   onChange={onChange}
                  //   value={value}
                  options={[
                    {
                      value: true,
                      label: 'Yes',
                      style: {
                        color: '#111827',
                        fontWeight: '500',
                      },
                    },
                    {
                      value: false,
                      label: 'No',
                      style: {
                        color: '#111827',
                        fontWeight: '500',
                      },
                    },
                  ]}
                />
              </div>
            </fieldset>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProviderSelection;
