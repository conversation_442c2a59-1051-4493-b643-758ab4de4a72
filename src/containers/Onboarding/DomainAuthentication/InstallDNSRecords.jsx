import { Button, Checkbox, Input, Tabs, notification } from 'antd';
import { verifyDomainAuthenticate } from '../../../services/users';
import { useState } from 'react';

const InstallDNSRecords = (props) => {
  const { data, setCurrentStep, setActiveNextBtn } = props;
  const [loading, setLoading] = useState(false)

  const handleVerifyDns = async() => {
    try {
      setLoading(true)
      const {data} = await verifyDomainAuthenticate()
      setLoading(false)
      if (data) {
        notification.success({
          message: "Verify Domain Authenticate success"
        })
        setActiveNextBtn(true)
      }
    } catch (e) {
      setLoading(true)
      notification.error(({
        message: "Verify Domain Authenticate Fail"
      }))
    }
  }

  const items = [
    {
      key: '1',
      label: 'Manual Setup',
      children: (
        <article>
          <h2 className="text-lg font-semibold mb-6 select-none">
            Instructions
          </h2>

          <ol className="space-y-10">
            <li>
              <div className="flex items-center space-x-4 mb-4 select-none">
                <div className="flex-shrink-0 w-6 h-6 rounded-full border border-gray-300 text-gray-600 text-xs font-semibold flex items-center justify-center mt-1 select-none">
                  1
                </div>
                <p className="font-semibold text-sm text-slate-900 mt-0.5">
                  Add all of these records to GoDaddy's DNS section.
                </p>
              </div>

              <table className="w-full border border-gray-200 text-sm text-gray-700 mb-6">
                <thead className="bg-gray-50 select-none">
                  <tr>
                    <th className="text-left font-semibold px-6 py-3 border-b border-gray-200 w-[10%]">
                      TYPE
                    </th>
                    <th className="text-left font-semibold px-6 py-3 border-b border-gray-200 w-[40%]">
                      HOST
                    </th>
                    <th className="w-[10%] border-b border-gray-200"></th>
                    <th className="text-left font-semibold px-6 py-3 border-b border-gray-200 w-[30%]">
                      POINTS TO
                    </th>
                    <th className="w-[10%] border-b border-gray-200"></th>
                  </tr>
                </thead>
                <tbody>
                  {data?.map((item) => {
                    return (
                      <tr className="border-t border-gray-100">
                        <td className="px-6 py-4 border-r border-gray-100 font-medium">
                          CNAME
                        </td>
                        <td className="px-6 py-4 border-r border-gray-100">
                          {item?.host}
                        </td>
                        <td className="px-4 py-4 border-r border-gray-100">
                          <button
                            type="button"
                            className="text-sm font-semibold text-gray-900 border border-gray-300 rounded px-3 py-1 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-600"
                            onClick={async () => {
                              await navigator.clipboard.writeText(item?.host)
                            }}
                          >
                            Copy
                          </button>
                        </td>
                        <td className="px-6 py-4 border-r border-gray-100">
                          {item?.data}
                        </td>
                        <td className="px-4 py-4">
                          <button
                            type="button"
                            className="text-sm font-semibold text-gray-900 border border-gray-300 rounded px-3 py-1 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-600"
                            onClick={async () => {
                              await navigator.clipboard.writeText(item?.data)
                            }}
                          >
                            Copy
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </li>

            <li>
              <div className="flex items-center space-x-4 select-none">
                <div className="flex-shrink-0 w-6 h-6 rounded-full border border-gray-300 text-gray-600 text-xs font-semibold flex items-center justify-center mt-1">
                  2
                </div>
                <p className="font-semibold text-sm text-slate-900 mt-0.5">
                  Once added, press <span className="font-bold">Verify.</span>
                </p>
              </div>
            </li>
          </ol>

          <form className="mt-6 flex items-center justify-end space-x-3 select-none">
            <Checkbox>I've added these records.</Checkbox>
            <Button loading={loading} onClick={() => handleVerifyDns()} className="text-white bg-cyan-600 text-sm font-semibold rounded select-none">
              <span className="px-2">Verify</span>
            </Button>
          </form>
        </article>
      ),
    },
    {
      key: '2',
      label: 'Send To A Coworker',
      children: (
        <article>
          <h3 className="font-semibold mb-6 select-none">
            Enter a coworker’s email address and we'll send them everything they
            need to install these records.
          </h3>

          <div className="flex flex-col items-center gap-3">
            <Input
              placeholder="Enter email address"
              className="border border-gray-300 rounded px-4 py-2 w-full"
            />
            <Input.TextArea
              rows={7}
              placeholder="Enter message"
              className="border border-gray-300 rounded px-4 py-2 w-full"
            />
            <Button className="text-white bg-cyan-600 text-sm font-semibold rounded select-none">
              Send
            </Button>
          </div>
        </article>
      ),
    },
  ];
  return (
    <div className="max-w-6xl mx-auto p-6 sm:p-10">
      <section>
        <p className="text-cyan-700 text-[13px] font-semibold mb-1 select-none">
          Sender Authentication
        </p>
        <h1 className="text-3xl font-extrabold text-slate-900 mb-6 select-none">
          Install DNS Records
        </h1>
        <p className="text-sm text-slate-800 mb-10 select-none max-w-2xl">
          You will need to install the following records to complete the
          process.
        </p>

        <Tabs defaultActiveKey="1" items={items} />
      </section>
    </div>
  );
};

export default InstallDNSRecords;
