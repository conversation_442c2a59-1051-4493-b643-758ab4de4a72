import { UserOutlined } from '@ant-design/icons';
import { Checkbox, Input } from 'antd';
import styled from 'styled-components';

const RestyledInput = styled(Input)`
  border-radius: 0rem !important;
  border-top-width: 0 !important;
  border-left-width: 0 !important;
  border-right-width: 0 !important;
`;

const InputDomain = (props) => {
  const {setData, data} = props
  return (
    <div className="max-w-7xl mx-auto px-4 py-10 flex flex-col md:flex-row md:space-x-20 justify-center items-center">
      <div className="flex-1 max-w-md">
        <p className="text-sm text-gray-700 mb-6 text-start">
          Enter your domain and edit any additional settings.
        </p>
        <div className="mb-6">
          <label className="block text-xs font-semibold text-gray-700 mb-1 text-start">
            Domain You Send From
          </label>
          <label
            className="block text-xs font-semibold text-gray-700 mb-1 text-start"
            htmlFor="from-domain"
          >
            From Domain
            <span className="text-red-600">*</span>
          </label>
          <RestyledInput
            className="w-full"
            placeholder="Enter your domain Eg. pearsoncarter.com "
            type="text"
            id="from-domain"
            name="from-domain"
            value={data?.domain}
            onChange={(e) => {
              setData({...data, domain: e.target.value})
            }}
          />
        </div>
        <div>
          <button
            aria-controls="advanced-settings"
            aria-expanded="false"
            className="text-xs font-semibold text-gray-600 flex items-center space-x-1 mb-3 justify-center"
            id="advanced-settings-button"
            type="button"
          >
            <span>Advanced Settings</span>
            <i className="fas fa-chevron-down text-gray-600 text-[10px]"></i>
          </button>
          <div className="space-y-3" id="advanced-settings">
            <Checkbox.Group
              className="w-full flex flex-col space-y-3 font-medium text-gray-900"
              options={[
                {
                  label: 'Use automated security',
                  value: 'use_automated_security',
                },
                {
                  label: 'Use custom return path',
                  value: 'use_custom_return_path',
                },
                {
                  label: 'Use a custom DKIM selector',
                  value: 'use_a_custom_dkim_selector',
                },
              ]}
            />
          </div>
        </div>
      </div>
      <div className="flex-1 mt-10 md:mt-0 max-w-md shadow-sm border border-gray-200 rounded bg-white">
        <div className="p-4 border-b border-gray-200 flex items-start space-x-3 justify-start">
          <UserOutlined className=" text-[#0288d1] font-bold text-2xl object-contain" />
          <div className="text-xs text-gray-700 text-start">
            <span className="font-semibold text-gray-900">James More</span>
            <span className="text-gray-400 ml-1">
              &lt;<EMAIL>&gt;
            </span>
            <div className="text-gray-400">to me →</div>
          </div>
        </div>
        <div className="p-6 space-y-3 relative">
          <div className="space-y-3 relative">
            <div className="absolute top-1/2 left-14 w-3/4 bg-white py-3 px-3 flex items-center justify-center text-xs text-gray-400 text-center tracking-widest font-semibold ">
              WHAT RECIPIENTS SEE
            </div>
            <div className="h-3 bg-gray-200 rounded py-2 w-1/3" />
            <div className="h-3 bg-gray-200 rounded w-full mx-auto py-2" />
            <div className="h-3 bg-gray-200 rounded w-full mx-auto py-2" />
            <div className="h-3 bg-gray-200 rounded w-full mx-auto py-2" />
            <div className="h-3 bg-gray-200 rounded w-full mx-auto py-2" />
            <div className="h-3 bg-gray-200 rounded w-full mx-auto py-2" />
          </div>
          <div className="w-1/3 mt-6 bg-green-500 hover:bg-green-600 transition-colors text-white text-xs font-semibold rounded px-8 py-4 block mx-auto" />
        </div>
      </div>
    </div>
  );
};

export default InputDomain;
