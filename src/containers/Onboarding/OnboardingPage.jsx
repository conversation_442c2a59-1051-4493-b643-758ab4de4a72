import React, { useEffect, useState } from 'react';
import { Button, Image, message, notification } from 'antd';
import {
  AimOutlined,
  ApiOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CreditCardOutlined,
  LeftOutlined,
  RightOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { completeOnboarding } from '../../services/onboarding';
import zileo<PERSON>ogo from '../../assets/img/welcome/logo.png';
import Integration from './Integration';
import UserInformation from './UserInformation';
import DomainAuthentication from './DomainAuthentication';
import Payment from './Payment';
import { useAuth } from '../../store/auth';
import { useOnboarding } from '../../store/onboarding';
import { userRole } from '../../constants/common.constant';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useViewAs } from '../../store/viewAs';

const stepConstants = {
  PERSONAL_INFO: 1,
  PAYMENT: 2,
  AUTHENTICATE_DOMAIN: 3,
  INTEGRATIONS: 4,
};

const stepInformation = {
  [stepConstants.PERSONAL_INFO]: {
    title: 'Personal Information',
    description: 'Your name and Password setup',
  },
  [stepConstants.PAYMENT]: {
    title: 'Payment',
    description:
      'You can add your payment information to get access to features and integrations',
  },
  [stepConstants.AUTHENTICATE_DOMAIN]: {
    title: 'Authenticate your domain',
    description:
      'Set up DNS records to verify domain ownership and improve email deliverability with Zileo.',
  },
  [stepConstants.INTEGRATIONS]: {
    title: 'Setup Integrations (optional)',
    description:
      'Once clients added you may setup specific integration for them, you can select available integrations from the list',
  },
};

const statusStep = {
  COMPLETED: 'COMPLETED',
  CURRENT: 'CURRENT',
  UPCOMING: 'UPCOMING',
};

const statusStepIcon = {
  COMPLETED: (
    <div className="w-9 h-16 rounded-full bg-green-600 flex items-center justify-center text-white">
      <CheckOutlined className="font-bold text-white text-lg" />
    </div>
  ),
  CURRENT: (
    <div className="w-9 h-16 rounded-full bg-cyan-600 flex items-center justify-center text-white">
      <AimOutlined className="font-bold text-white text-lg" />
    </div>
  ),
  UPCOMING: '',
};

const OnboardingPage = () => {
  const { clearAuth } = useAuth();
  const queryClient = useQueryClient();
  const { clearViewAs } = useViewAs();

  const [currentStep, setCurrentStep] = useState(stepConstants.PERSONAL_INFO);
  const [disableStep, setDisableStep] = useState(stepConstants.PERSONAL_INFO);
  const [integrationStep, setIntegrationStep] = useState(null);
  const [visibleSteps, setVisibleSteps] = useState([
    stepConstants.PERSONAL_INFO,
    stepConstants.PAYMENT,
    stepConstants.AUTHENTICATE_DOMAIN,
    stepConstants.INTEGRATIONS,
  ]);
  const { profile } = useAuth();
  const {
    onboardingRequired,
    setOnboardingRequired,
    setCurrentStep: setReduxCurrentStep,
    currentStep: reduxCurrentStep,
    setPersonalInfo,
    personalInfo,
    resetOnboardingData,
  } = useOnboarding();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const onLogout = async () => {
    clearViewAs();
    clearAuth();
    queryClient.setQueryData(['CURRENT_USER'], null);
    window.location.reload(); // reload the page to clear the cache
  };

  const handleCompleteOnboarding = async () => {
    try {
      setLoading(true);

      // Get the personal information
      if (!personalInfo.password) {
        notification.error({
          message: 'Password is required',
          description:
            'Please go back to the Personal Information step and set your password',
        });
        setLoading(false);
        return;
      }

      // Prepare the data for the API
      const data = {
        initialPassword: personalInfo.password,
        newPassword: personalInfo.password,
        profileInfo: {
          fullName: personalInfo.fullName || profile?.user?.fullName || '',
          jobTitle: personalInfo.jobTitle || profile?.user?.jobTitle || '',
          linkedinUrl:
            personalInfo.linkedinUrl || profile?.user?.linkedinUrl || '',
          // BE đang tạm không cho update
          // email: personalInfo.email || profile?.user?.email || '',
          // username: personalInfo.username || profile?.user?.username || '',
          timezone:
            personalInfo.timezone ||
            Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
      };

      await completeOnboarding(data);

      // Reset
      setOnboardingRequired(false);
      resetOnboardingData();

      message.success('Onboarding completed successfully');
      navigate('/dashboard');
      window.location.reload(); // reload the page to clear the cache
    } catch (error) {
      notification.error({
        message: 'Failed to complete onboarding',
        description: error?.response?.data?.message || 'Something went wrong',
      });
    } finally {
      setLoading(false);
    }
  };

  // Initialize the current step from Redux or set to default
  useEffect(() => {
    if (reduxCurrentStep > 0) {
      setCurrentStep(reduxCurrentStep);
    } else {
      setReduxCurrentStep(stepConstants.PERSONAL_INFO);
    }
  }, [reduxCurrentStep, setReduxCurrentStep]);

  // Update Redux when current step changes
  useEffect(() => {
    setReduxCurrentStep(currentStep);
  }, [currentStep, setReduxCurrentStep]);

  useEffect(() => {
    // Check if user is logged in
    if (!profile?.user) {
      navigate('/login');
      return;
    }

    // Check if onboarding is required
    if (!onboardingRequired) {
      // If onboarding is not required, redirect to dashboard
      navigate('/dashboard');
      return;
    }

    const userRoleKeycode = profile?.user?.role?.keyCode;
    console.log('🚀 ~ useEffect ~ userRoleKeycode:', userRoleKeycode);

    // Set visible steps based on user role
    if (userRoleKeycode === userRole.ADMIN) {
      // Admin sees all steps
      setVisibleSteps([
        stepConstants.PERSONAL_INFO,
        stepConstants.PAYMENT,
        stepConstants.AUTHENTICATE_DOMAIN,
        stepConstants.INTEGRATIONS,
      ]);
    } else if (
      userRoleKeycode === userRole.MANAGEMENT ||
      userRoleKeycode === userRole.BASIC_USER
    ) {
      setVisibleSteps([
        stepConstants.PERSONAL_INFO,
        stepConstants.INTEGRATIONS,
      ]);
    }
  }, [profile, navigate, onboardingRequired]);

  const getStepStatus = (step) => {
    if (!visibleSteps.includes(step)) {
      return null;
    }

    if (step < currentStep) {
      return statusStep.COMPLETED;
    } else if (step === currentStep) {
      return statusStep.CURRENT;
    } else {
      return statusStep.UPCOMING;
    }
  };

  const stepRenderer = (step) => {
    switch (step) {
      case stepConstants.PERSONAL_INFO:
        return <UserInformation setDisableStep={setDisableStep} />;
      case stepConstants.PAYMENT:
        return <Payment />;
      case stepConstants.AUTHENTICATE_DOMAIN:
        return (
          <DomainAuthentication
            parentStep={currentStep}
            setParentStep={setCurrentStep}
          />
        );
      case stepConstants.INTEGRATIONS:
        return (
          <Integration setStep={setIntegrationStep} step={integrationStep} />
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white text-gray-700 grid grid-cols-10 justify-center items-center">
      <aside className="hidden md:flex flex-col bg-gray-50 col-span-2 p-8 relative h-full">
        <div className="flex items-center justify-center">
          <Image preview={false} width={200} src={zileoLogo} />
        </div>
        <p className="text-center text-sm text-gray-600 mb-10">
          Complete following steps to setup your profile
        </p>
        <nav className="flex flex-col space-y-8 text-sm">
          {visibleSteps.includes(stepConstants.PERSONAL_INFO) && (
            <div
              className="flex space-x-4 transition-transform duration-300 hover:scale-105 cursor-pointer"
              onClick={() => setCurrentStep(stepConstants.PERSONAL_INFO)}
            >
              <div className="flex flex-col items-center">
                {statusStepIcon[getStepStatus(stepConstants.PERSONAL_INFO)] || (
                  <div className="w-9 h-16 flex items-center justify-center">
                    <UserOutlined className="font-bold text-gray-600 text-2xl" />
                  </div>
                )}
                <div className="w-px h-full bg-gray-300 mt-1"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-900">
                  Personal information
                </p>
                <p className="text-gray-500 text-xs mt-1">
                  Your name and Password setup
                </p>
              </div>
            </div>
          )}

          {visibleSteps.includes(stepConstants.PAYMENT) && (
            <div
              className="flex space-x-4 transition-transform duration-300 hover:scale-105 cursor-pointer"
              onClick={() => setCurrentStep(stepConstants.PAYMENT)}
            >
              <div className="flex flex-col items-center">
                {statusStepIcon[getStepStatus(stepConstants.PAYMENT)] || (
                  <div className="w-9 h-16 flex items-center justify-center">
                    <CreditCardOutlined className="font-bold text-gray-600 text-2xl" />
                  </div>
                )}
                <div className="w-px h-full bg-gray-300 mt-1"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-900">Payment</p>
                <p className="text-gray-500 text-xs mt-1">
                  You can add your payment information to get access to features
                  and integrations
                </p>
              </div>
            </div>
          )}

          {visibleSteps.includes(stepConstants.AUTHENTICATE_DOMAIN) && (
            <div
              className="flex space-x-4 transition-transform duration-300 hover:scale-105 cursor-pointer"
              onClick={() => setCurrentStep(stepConstants.AUTHENTICATE_DOMAIN)}
            >
              <div className="flex flex-col items-center">
                {statusStepIcon[
                  getStepStatus(stepConstants.AUTHENTICATE_DOMAIN)
                ] || (
                  <div className="w-9 h-16 flex items-center justify-center">
                    <AuditOutlined className="font-bold text-gray-600 text-2xl" />
                  </div>
                )}
                <div className="w-px h-full bg-gray-300 mt-1"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-900">
                  Authenticate your domain
                </p>
                <p className="text-gray-500 text-xs mt-1">
                  Set up DNS records to verify domain ownership and improve
                  email deliverability with Zileo.
                </p>
              </div>
            </div>
          )}

          {visibleSteps.includes(stepConstants.INTEGRATIONS) && (
            <div
              className="flex space-x-4 transition-transform duration-300 hover:scale-105 cursor-pointer"
              onClick={() => setCurrentStep(stepConstants.INTEGRATIONS)}
            >
              <div className="flex flex-col items-center">
                {statusStepIcon[getStepStatus(stepConstants.INTEGRATIONS)] || (
                  <div className="w-9 h-16 flex items-center justify-center">
                    <ApiOutlined className="font-bold text-gray-600 text-2xl" />
                  </div>
                )}
                <div className="w-px h-full bg-gray-300 mt-1"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-900">
                  Setup Integrations
                  <span className="font-normal">(optional)</span>
                </p>
                <p className="text-gray-500 text-xs mt-1">
                  Once clients added you may setup specific integration for
                  them, you can select available integrations from the list
                </p>
              </div>
            </div>
          )}
        </nav>
        <button
          className="absolute bottom-8 left-1/2 -translate-x-1/2 flex items-center space-x-2 border border-gray-300 rounded-full px-4 py-1 text-sm text-gray-700 bg-white hover:bg-gray-100"
          type="button"
          onClick={onLogout}
          aria-label="Log out"
          title="Log out"
        >
          Log out
        </button>
      </aside>
      <main className="flex-1 p-8 mx-auto min-h-screen col-span-8 h-full w-full">
        <div className="flex items-center justify-between mb-6">
          <div>
            <p className="text-cyan-600 font-semibold text-sm mb-2">
              {`Step ${currentStep} of ${Object.keys(stepConstants).length}`}
            </p>
            <h1 className="text-2xl font-extrabold text-gray-900 mb-2">
              {stepInformation[currentStep].title}
            </h1>
            <p className="text-gray-600 max-w-xl">
              {stepInformation[currentStep].description}
            </p>
          </div>
          {currentStep < 4 &&
            visibleSteps.indexOf(currentStep) < visibleSteps.length - 1 && (
              <Button
                onClick={() => {
                  const currentIndex = visibleSteps.indexOf(currentStep);
                  if (currentIndex < visibleSteps.length - 1) {
                    setCurrentStep(visibleSteps[currentIndex + 1]);
                  }
                }}
                type="dashed"
                className="bg-white"
              >
                <span className="text-xs">Skip</span>
              </Button>
            )}
        </div>
        <div className="min-w-full">{stepRenderer(currentStep)}</div>
        {currentStep !== stepConstants.AUTHENTICATE_DOMAIN && (
          <div className="flex justify-between mt-10 w-full">
            {currentStep > 1 && visibleSteps.indexOf(currentStep) > 0 ? (
              <Button
                onClick={() => {
                  if (integrationStep) {
                    setIntegrationStep(null);
                  } else {
                    const currentIndex = visibleSteps.indexOf(currentStep);
                    if (currentIndex > 0) {
                      setCurrentStep(visibleSteps[currentIndex - 1]);
                    }
                  }
                }}
                type="primary"
                icon={<LeftOutlined />}
                className="bg-white"
              >
                Back
              </Button>
            ) : (
              <div />
            )}
            <div className="flex space-x-4">
              {currentStep === visibleSteps[visibleSteps.length - 1] ? (
                <Button
                  onClick={handleCompleteOnboarding}
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  loading={loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <span className="text-xs">Complete</span>
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    if (currentStep === stepConstants.PERSONAL_INFO) {
                      const formValues =
                        document.querySelectorAll('input, select');
                      const personalInfo = {};

                      formValues.forEach((field) => {
                        if (field.id && field.value) {
                          personalInfo[field.id] = field.value;
                        }
                      });

                      setPersonalInfo(personalInfo);
                    }

                    const currentIndex = visibleSteps.indexOf(currentStep);
                    if (currentIndex < visibleSteps.length - 1) {
                      setCurrentStep(visibleSteps[currentIndex + 1]);
                    }
                  }}
                  type="primary"
                  disabled={disableStep == currentStep}
                  icon={<RightOutlined />}
                >
                  <span className="text-xs">Continue</span>
                </Button>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default OnboardingPage;
