/* eslint-disable no-unused-vars */
/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useState, forwardRef } from 'react';
import GridLayout from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { MinusIcon } from '@heroicons/react/24/solid';
import ManualSyncedLeadCount from './components/ManualSyncedLeadCount';
import EachLeadStatusCount from './components/EachLeadStatusCount';
import NewLead from './components/NewLead';
import PotentialLeadReturn from './components/PotentialLeadReturn';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getUserDetail } from '../../services/users';
import { Button } from 'antd';
import { LeftOutlined } from '@ant-design/icons';

const WidgetContainer = forwardRef((props, ref) => {
  return (
    <div className="relative" ref={ref} {...props}>
      {props.children}

      {props.mode === 'removeable' ? (
        <button
          onClick={() => props.onRemove()}
          className="absolute h-4 w-4 flex items-center justify-center top-0 right-0 bg-red-500 rounded-full text-white"
        >
          <MinusIcon className="w-4 h-4"></MinusIcon>
        </button>
      ) : null}
    </div>
  );
});

const StaffPerformanceDetail = () => {
  const navigate = useNavigate();
  let { id } = useParams();
  const [state, setState] = useState({
    a: true,
    b: false,
    c: true,
  });

  const { data } = useQuery(
    ['STAFF_DETAIL'],
    async () => {
      const { data } = await getUserDetail(id);
      return data;
    },
    {
      enabled: true,
    }
  );

  const [mode, setMode] = useState('default'); //removeable, editable, default

  return (
    <div className="flex mt-5">
      <div className="h-screen w-full relative  p-5">
        <div className="px-3 flex gap-4">
          <Button
            className="bg-white"
            onClick={() => {
              navigate(-1);
            }}
            icon={<LeftOutlined />}
          >
            Back
          </Button>
          <p className="text-xl font-semibold">{data?.email}</p>
        </div>

        <GridLayout
          className="mt-5"
          isResizable={mode === 'editable'}
          isDraggable={mode === 'editable'}
          cols={12}
          rowHeight={80}
          width={1000}
          margin={[10, 20]}
        >
          {state['a'] ? (
            <WidgetContainer
              onRemove={() => setState((prev) => ({ ...prev, a: false }))}
              mode={mode}
              key="a"
              data-grid={{ x: 0, y: 0, w: 6, h: 1 }}
            >
              <PotentialLeadReturn id={id} />
            </WidgetContainer>
          ) : null}
          <WidgetContainer
            // TODO @ibnu
            // onRemove={() => setState((prev) => ({ ...prev, d: false }))}
            mode={mode}
            key="d"
            data-grid={{ x: 0, y: 0, w: 6, h: 1 }}
          >
            <ManualSyncedLeadCount id={id} />
          </WidgetContainer>
          <WidgetContainer
            // TODO @ibnu
            // onRemove={() => setState((prev) => ({ ...prev, d: false }))}
            mode={mode}
            key="e"
            data-grid={{ x: 0, y: 0, w: 6, h: 1 }}
          >
            <EachLeadStatusCount id={id} />
          </WidgetContainer>
          <WidgetContainer
            // TODO @ibnu
            // onRemove={() => setState((prev) => ({ ...prev, a: false }))}
            mode={mode}
            key="f"
            data-grid={{ x: 6, y: 0, w: 6, h: 1 }}
          >
            <NewLead id={id} />
          </WidgetContainer>
        </GridLayout>
      </div>
    </div>
  );
};
export default StaffPerformanceDetail;
