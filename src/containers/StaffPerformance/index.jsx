import React from 'react';
import StaffPerformanceTable from '../../components/StaffPerformance/StaffPerformanceTable';
import { useQuery } from '@tanstack/react-query';
import { getStaff } from '../../services/staff';
import { useUserRole } from '../../hooks/useUserRole';
import { useViewAs } from '../../store/viewAs';
import { userRole } from '../../constants/common.constant';
function StaffPerformance() {
  const { profileUser, viewerAsUser } = useViewAs();
  const { isOrganizationalAdmin } = useUserRole();

  const { data } = useQuery(
    ['STAFF_PERFORMANCE'],
    async () => {
      const { data } = await getStaff();
      return data.result;
    },
    {
      enabled: isOrganizationalAdmin,
    }
  );

  return (
    <div className="py-10">
      {isOrganizationalAdmin ||
      (viewerAsUser && profileUser?.role?.keyCode === userRole.ADMIN) ? (
        <StaffPerformanceTable staffs={data} />
      ) : (
        <div className="flex justify-center py-16 items-center text-gray-700">
          <p>You do not have permission to access this feature.</p>
        </div>
      )}
    </div>
  );
}

export default StaffPerformance;
