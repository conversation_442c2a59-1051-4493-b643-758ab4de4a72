/* eslint-disable react/prop-types */
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { IoDocuments } from 'react-icons/io5';
import Widget from '../../../components/Widget/Widget';
import { getPotentialLeadReturnById } from '../../../services/jobLeads';

const PotentialLeadReturn = ({ id }) => {
  const {
    data: { minRange, maxRange },
  } = useQuery(['GET_POTENTIAL_LEAD_RETURN_BY_ID'], {
    queryFn: async () => {
      const { data } = await getPotentialLeadReturnById(id);
      return data.result;
    },
    initialData: { minRange: 0, maxRange: 0 },
  });
  return (
    <Widget
      icon={<IoDocuments className="h-6 w-6" />}
      title={'My Leads Potential Value'}
      subtitle={`£${minRange} - £${maxRange}`}
    />
  );
};

export default PotentialLeadReturn;
