/* eslint-disable react/prop-types */
import React from 'react';
import { MdPersonAdd, MdSync } from 'react-icons/md';
import Widget from '../../../components/Widget/Widget';
import { useQuery } from '@tanstack/react-query';
import { getLeadCountsById } from '../../../services/jobLeads';

const ManualSyncedLeadCount = ({ id }) => {
  const { data } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT_MANUAL_SYNCED'],
    async () => {
      const { data } = await getLeadCountsById(id);
      return data;
    },
    {
      enabled: true,
      initialData: { result: { manualLeadCount: 0, syncedLeadCount: 0 } },
    }
  );

  const {
    result: { manualLeadCount, syncedLeadCount },
  } = data;

  return (
    <div className="grid grid-cols-2 bg-white rounded-[20px] shadow-3xl shadow-shadow-500">
      <Widget
        icon={<MdPersonAdd className="h-7 w-7" />}
        title={'Manual Leads'}
        subtitle={manualLeadCount}
        extra="shadow-none"
      />
      <Widget
        icon={<MdSync className="h-7 w-7" />}
        title={'Synced Leads'}
        subtitle={syncedLeadCount}
        extra="shadow-none"
      />
    </div>
  );
};

export default ManualSyncedLeadCount;
