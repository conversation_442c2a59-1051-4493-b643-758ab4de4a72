/* eslint-disable react/prop-types */
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from '../../../utils/axios';
import Widget from '../../../components/Widget/Widget';
import { MdBarChart } from 'react-icons/md';

const NewLead = ({ id }) => {
  const { data = [] } = useQuery(
    ['EACH_LEAD_STATUS_COUNT_BY_ID'],
    async () => {
      const { data } = await axios.get(
        `/job-lead/count-for-each-lead-status/${id}`
      );
      return data.result;
    },
    {
      enabled: true,
    }
  );

  const count =
    data?.reduce(
      (min, current) =>
        current.order_of_display < min.order_of_display ? current : min,
      { count: 0 }
    )?.count || 0;

  return (
    <Widget
      icon={<MdBarChart className="h-7 w-7" />}
      title={'New Lead'}
      subtitle={count}
    />
  );
};

export default NewLead;
