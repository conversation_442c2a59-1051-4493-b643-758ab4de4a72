/* eslint-disable react/prop-types */
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getLeadCountsForEachStatusById } from '../../../services/jobLeads';
import PieChartCard from '../../../components/Widget/PieChartCard';

const EachLeadStatusCount = ({ id }) => {
  const { data } = useQuery(
    ['EACH_LEAD_STATUS_COUNT_BY_ID'],
    async () => {
      const { data } = await getLeadCountsForEachStatusById(id);
      return data.result;
    },
    {
      enabled: true,
    }
  );

  const leadLabels = data?.map((item) => item.name) || [];
  const leadCount = data?.map((item) => Number(item.count)) || [];

  return (
    <PieChartCard
      title="Lead Status"
      pieLabels={leadLabels}
      pieData={leadCount}
    />
  );
};

export default EachLeadStatusCount;
