import React from 'react';
import { Col, message, Row } from 'antd';
import { useNavigate } from 'react-router-dom';
import LoginForm from '../../components/Login/LoginForm';
import { WelcomeBanner } from '../../components/WelcomeBanner/WelcomeBanner';
import { useLogin } from '../../hooks/useUserDetails';

const LoginPage = () => {
  // const { loading, handleLogin } = useLogin(signIn);
  const navigate = useNavigate();
  const login = useLogin();
  const onSubmit = async (formData) => {
    try {
      const result = await login.mutateAsync(formData);
      console.log('🚀 ~ onSubmit ~ result:', result);
      message.success('Login success');

      // Check if password change is required
      const passwordChangeRequired =
        result?.result?.user?.passwordChangeRequired;

      if (passwordChangeRequired) {
        navigate('/onboarding');
      } else {
        navigate('/dashboard');
        window.location.reload(); // reload the page to clear the cache
      }
    } catch (error) {
      message.error('Login failed');
    }
  };

  return (
    <div>
      <LoginForm onSubmit={onSubmit} />
    </div>
  );
};

export default LoginPage;
