import {
  BuildOutlined,
  ExclamationCircleFilled,
  LeftCircleOutlined,
  LeftOutlined,
  LeftSquareTwoTone,
  RightOutlined,
  SafetyCertificateOutlined,
  SolutionOutlined,
  TeamOutlined,
  WalletOutlined,
} from '@ant-design/icons';
import { Button, Modal, Tooltip } from 'antd';
import clsx from 'clsx';
import CompanyForm from '../CompanyManagement/CompanyForm';
import { useState } from 'react';
import { MODE } from '../CompanyManagement';
import EmployeesOnboarding from '../EmployeesOnboarding';
import { licenseTypeConstant, totalCredits } from './LicensePackageSelection';
import ReviewForm from './ReviewForm';
import { searchCorporateEmployees } from '../../services/bullhorn';
import { v4 } from 'uuid';
import { userRole } from '../../constants/common.constant';
import {
  generateRandomPassword,
  generateRandomUsername,
} from '../../helpers/util';
import dayjs from 'dayjs';
import { perPaymentOptions } from '../../components/Stripe';

const { confirm } = Modal;

const stepConstant = {
  COMPANY_DETAILS: 1,
  ONBOARDING_EMPLOYEES: 2,
  // PAYMENT: 3,
  REVIEW: 3,
};

const BHConfigurationFieds = [
  'bhClientId',
  'bhClientSecret',
  'bhUsername',
  'bhPassword',
];

const InformationCollection = ({ licenseType, paymentPackage, back }) => {
  const [step, setStep] = useState(stepConstant.COMPANY_DETAILS);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [payPer, setPayPer] = useState(perPaymentOptions.monthly);
  const [onboardingEmployees, setOnboardingEmployees] = useState([]);
  const [employeeLoading, setEmployeeLoading] = useState(false);
  const [totalOriginalCredits, setTotalOriginalCredits] = useState(
    totalCredits[paymentPackage]
  );

  const fullyBHData = BHConfigurationFieds.every((key) =>
    selectedCompany?.[key]?.trim()
  );

  const isNotAllowConfigure =
    licenseType === licenseTypeConstant.CONNECTED && !fullyBHData;

  const handleGetBHEmployees = async (payload) => {
    setEmployeeLoading(true);
    try {
      const { data } = await searchCorporateEmployees(payload);

      if (data?.result?.length > 0) {
        const validEmployees = data?.result?.filter(
          (employee) =>
            employee?.email?.trim() &&
            /\S+@\S+\.\S+/.test(employee?.email?.trim())
        );

        const creditsPerEmployee = totalCredits[paymentPackage];

        const mappedEmployees = validEmployees?.map((employee) => {
          const userName = employee?.email || generateRandomUsername();
          const id = v4();
          const newData = {
            id,
            key: id,
            email: employee?.email,
            role: userRole.BASIC_USER,
            userName,
            password: generateRandomPassword(),
            credits: creditsPerEmployee,
            createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
            updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
          };
          return newData;
        });

        setEmployeeLoading(false);

        return [...mappedEmployees];
        // setOnboardingEmployees([...mappedEmployees]);
      }
      setEmployeeLoading(false);
      return [];
    } catch (error) {
      setEmployeeLoading(false);
      return [];
    }
  };

  const handleSumitCompanyDetail = async (payload) => {
    const employeeCap = parseInt(payload?.numberOfLicenses) || 1;
    const adminEmail = payload?.companyEmail?.trim();

    const creditsPerEmployee = totalCredits[paymentPackage];
    let adminData = null;
    if (adminEmail) {
      const adminId = v4();
      adminData = {
        id: adminId,
        key: adminId,
        email: adminEmail,
        role: userRole.ADMIN,
        userName: adminEmail,
        password: generateRandomPassword(),
        credits: creditsPerEmployee,
        createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
        updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      };
    }

    setSelectedCompany(payload);
    const isFullyBHData = BHConfigurationFieds.every((key) =>
      payload?.[key]?.trim()
    );
    const bhInfor = {
      bhClientId: payload?.bhClientId,
      bhClientSecret: payload?.bhClientSecret,
      bhUsername: payload?.bhUsername,
      bhPassword: payload?.bhPassword,
    };
    const employees = await handleGetBHEmployees(bhInfor);

    if (
      licenseType === licenseTypeConstant.CONNECTED &&
      isFullyBHData &&
      employees?.length > 0
    ) {
      // If BH data is fully configured and employees are fetched
      const conditionCap = adminEmail ? employeeCap - 1 : employeeCap;
      if (employees?.length === conditionCap) {
        setOnboardingEmployees([
          ...(adminEmail ? [adminData] : []),
          ...employees,
        ]);
      } else if (employees?.length < conditionCap) {
        const extraNeeded = employeeCap - employees?.length;
        const mappedEmployees = Array.from({ length: extraNeeded }, (_, i) => {
          const id = v4();
          const isAdmin = i === 0;
          const email = `example${i + 1}@domain.com`;
          const newData = {
            id,
            key: id,
            email,
            role: isAdmin ? userRole.ADMIN : userRole.BASIC_USER,
            userName: email,
            password: generateRandomPassword(),
            credits: creditsPerEmployee,
            createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
            updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
          };
          return newData;
        });
        setOnboardingEmployees([
          ...(adminEmail ? [adminData] : []),
          ...employees,
          ...mappedEmployees,
        ]);
      } else if (employees?.length > conditionCap) {
        confirm({
          title: `The company has more users than the number of licenses. Would you like to add more to licenses?`,
          icon: <ExclamationCircleFilled />,
          content: ' ',
          onOk() {
            return;
          },
          onCancel() {
            setOnboardingEmployees([
              ...(adminEmail ? [adminData] : []),
              ...employees,
            ]);
            setStep(stepConstant.ONBOARDING_EMPLOYEES);
            return;
          },
        });

        return;
      }
    } else {
      const creditsPerEmployee = totalCredits[paymentPackage];
      const mappedEmployees = Array.from({ length: employeeCap }, (_, i) => {
        const id = v4();
        const isAdmin = i === 0;
        const email = `example${i + 1}@domain.com`;
        const newData = {
          id,
          key: id,
          email,
          role: isAdmin ? userRole.ADMIN : userRole.BASIC_USER,
          userName: email,
          password: generateRandomPassword(),
          credits: creditsPerEmployee,
          createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
          updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
        };
        return newData;
      });

      if (adminEmail) {
        mappedEmployees[0] = adminData;
      }

      setOnboardingEmployees([...mappedEmployees]);
    }

    console.log('Company details submitted:', payload);
    setStep(stepConstant.ONBOARDING_EMPLOYEES);
  };

  const handleSumitEmployeesOnboarding = async (data) => {
    // setSelectedCompany(payload);
    // {dataSource, totalOriginalCredits}
    const employees = data?.dataSource;
    const creditsCap =
      data?.totalOriginalCredits || totalCredits[paymentPackage];
    const payPer = data?.payPer || perPaymentOptions.monthly;
    console.log('Company Employees Onboarding submitted:', data);
    setOnboardingEmployees([...employees]);
    setTotalOriginalCredits(creditsCap);
    setPayPer(payPer);
    setStep(stepConstant.REVIEW);
  };

  return (
    <div className=" min-h-[40rem] w-[80vw] flex bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="w-1/4 bg-[#fafafa] p-8 border-r border-gray-200">
        {/* <div>
          <Button onClick={back} type="text" className="flex items-center">
            <LeftOutlined />
          </Button>
        </div> */}
        <div className="flex items-center mb-8">
          <div className="w-8 h-8 bg-cyan-700 rounded-full mr-2"></div>
          <div className="flex flex-col">
            <Tooltip title="Package">
              <div className="font-medium  flex items-center gap-1">
                {paymentPackage?.toLowerCase()?.replace('_', ' ')?.capitalize()}
                <SafetyCertificateOutlined />
              </div>
            </Tooltip>
            <Tooltip title="License Type">
              <div className="text-sm text-gray-500">
                {licenseType?.toLowerCase()?.replace('_', ' ')?.capitalize()}
              </div>
            </Tooltip>
          </div>
        </div>
        <nav className="space-y-4">
          <div
            // onClick={() => setStep(stepConstant.COMPANY_DETAILS)}
            className={clsx(
              'flex items-center font-medium cursor-pointer',
              step < stepConstant.COMPANY_DETAILS && 'text-gray-500'
            )}
            href="#"
          >
            <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
              <BuildOutlined />
            </div>
            Company details
          </div>
          <div
            // onClick={() => setStep(stepConstant.ONBOARDING_EMPLOYEES)}
            className={clsx(
              'flex items-center font-medium cursor-pointer',
              step < stepConstant.ONBOARDING_EMPLOYEES && 'text-gray-500'
            )}
            href="#"
          >
            <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
              <TeamOutlined />
            </div>
            Licenses
          </div>
          {/* <a className="flex items-center text-gray-500" href="#">
            <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
              <WalletOutlined />
            </div>
            Payment
          </a> */}
          <div
            // onClick={() => setStep(stepConstant.REVIEW)}
            className={clsx(
              'flex items-center font-medium cursor-pointer',
              step < stepConstant.REVIEW && 'text-gray-500'
            )}
            href="#"
          >
            <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
              <SolutionOutlined />
            </div>
            Review
          </div>
        </nav>
      </div>

      {step === stepConstant.COMPANY_DETAILS && (
        <div className="flex-1 p-8">
          <div className="flex justify-between items-center mb-8">
            <div>
              <div className="flex items-center font-medium cursor-pointer">
                <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
                  <BuildOutlined className="text-xl" />
                </div>
                <div className="flex flex-col">
                  <h3 className="font-semibold">Company details</h3>
                  <span className="text-sm opacity-60">
                    Let's get your account up and running.
                  </span>
                </div>
              </div>
            </div>
            {/* <span className="text-green-500 font-medium">Changes saved</span> */}
            <Button
              onClick={back}
              type="dashed"
              className="flex items-center gap-1 bg-white"
              icon={<LeftCircleOutlined />}
            >
              Back
            </Button>
          </div>
          <div className="space-y-6 h-5/6">
            <CompanyForm
              company={selectedCompany}
              mode={MODE.EDIT}
              handleSumit={handleSumitCompanyDetail}
              employeeLoading={employeeLoading}
              showBHForm={licenseType === licenseTypeConstant.CONNECTED}
            />
          </div>
        </div>
      )}

      {step === stepConstant.ONBOARDING_EMPLOYEES && (
        <div className="flex-1 p-8 h-full w-full">
          <div className="flex justify-between items-center mb-8">
            <div>
              <div className="flex items-center font-medium cursor-pointer">
                <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
                  <TeamOutlined className="text-xl" />
                </div>
                <div className="flex flex-col">
                  <h3 className="font-semibold">Licenses</h3>
                  <span className="text-sm opacity-60">
                    Let's configure your employees.
                  </span>
                </div>
              </div>
            </div>
            {/* <span className="text-green-500 font-medium">Changes saved</span> */}
            <Button
              onClick={() => setStep(stepConstant.COMPANY_DETAILS)}
              type="dashed"
              className="flex items-center gap-1 bg-white"
              icon={<LeftCircleOutlined />}
            >
              Back
            </Button>
          </div>
          <div className="space-y-6 h-full w-full">
            <EmployeesOnboarding
              onboardingEmployees={onboardingEmployees}
              employeeCap={parseInt(selectedCompany?.numberOfLicenses)}
              licenseType={licenseType}
              paymentPackage={paymentPackage}
              skipStep={() => setStep(stepConstant.REVIEW)}
              handleSubmit={handleSumitEmployeesOnboarding}
              isNotAllowConfigure={isNotAllowConfigure}
            />
          </div>
        </div>
      )}

      {step === stepConstant.REVIEW && (
        <div className="flex-1 pt-8 px-8 h-full w-full">
          <div className="flex justify-between items-center mb-8">
            <div>
              <div className="flex items-center font-medium cursor-pointer">
                <div className="px-2 py-1 bg-white rounded-md shadow-sm mr-2 border">
                  <SolutionOutlined className="text-xl" />
                </div>
                <div className="flex flex-col">
                  <h3 className="font-semibold">Review</h3>
                  <span className="text-sm opacity-60">
                    Review your information before proceeding.
                  </span>
                </div>
              </div>
            </div>

            <Button
              onClick={() => setStep(stepConstant.ONBOARDING_EMPLOYEES)}
              type="dashed"
              className="flex items-center gap-1 bg-white"
              icon={<LeftCircleOutlined />}
            >
              Back
            </Button>
          </div>

          <ReviewForm
            employees={onboardingEmployees}
            company={selectedCompany}
            licenseType={licenseType}
            paymentPackage={paymentPackage}
            totalOriginalCredits={totalOriginalCredits}
            payPer={payPer}
          />
        </div>
      )}
    </div>
  );
};

export default InformationCollection;
