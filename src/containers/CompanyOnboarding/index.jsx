import { useEffect, useState } from 'react';
import LicensePackageSelection, {
  licenseTypeConstant,
  paymentPackageConstant,
} from './LicensePackageSelection';
import InformationCollection from './InformationCollection';
import { Button } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const initialState = {
  licenseType: licenseTypeConstant.STANDARD,
  paymentPackage: paymentPackageConstant.BASIC,
};

const stepConstant = {
  LICENSE_PACKAGE_SELECTION: 1,
  INFORMATION_COLLECTION: 2,
};

const CompanyOnboardingPage = () => {
  const navigate = useNavigate();
  const [state, setState] = useState({
    ...initialState,
  });
  const [step, setStep] = useState(stepConstant.LICENSE_PACKAGE_SELECTION);

  const handleStateChange = (newState) => {
    setState((prevState) => ({ ...prevState, ...newState }));
  };

  const handleSubmitLicensePackageSelection = () => {
    // Handle form submission logic here
    console.log('Form submitted with state:', state);
    // Reset state if needed
    setStep(stepConstant.INFORMATION_COLLECTION);
  };

  const backToLicensePackageSelection = () => {
    setStep(stepConstant.LICENSE_PACKAGE_SELECTION);
  };

  return (
    <div class="bg-gray-100 flex items-center justify-center relative h-screen">
      <Button
        onClick={() => navigate(-1)}
        className="bg-white absolute top-10 left-10"
        icon={<LeftOutlined />}
      >
        Back
      </Button>
      {step === stepConstant.LICENSE_PACKAGE_SELECTION && (
        <LicensePackageSelection
          licenseType={state?.licenseType}
          paymentPackage={state?.paymentPackage}
          handleStateChange={handleStateChange}
          handleSubmit={handleSubmitLicensePackageSelection}
        />
      )}
      {step === stepConstant.INFORMATION_COLLECTION && (
        <InformationCollection
          licenseType={state?.licenseType}
          paymentPackage={state?.paymentPackage}
          back={backToLicensePackageSelection}
        />
      )}
    </div>
  );
};

export default CompanyOnboardingPage;
