import { Button, Checkbox, Image, Radio } from 'antd';
import clsx from 'clsx';

// import zileoLogo from '../../assets/img/welcome/logo.png';
import { useState } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Zap } from 'lucide-react';

export const licenseTypeConstant = {
  CONNECTED: 'CONNECTED',
  STANDARD: 'STANDARD',
};
export const paymentPackageConstant = {
  BASIC: 'BASIC',
  PROFESSIONAL: 'PROFESSIONAL',
  PROFESSIONAL_PLUS: 'PROFESSIONAL_PLUS',
  ENTERPRISE: 'ENTERPRISE',
};

export const planIdConstant = {
  BASIC: 'basic',
  PROFESSIONAL: 'pro',
  PROFESSIONAL_PLUS: 'pro_plus',
  ENTERPRISE: 'enterprise',
};

export const planTextConstant = {
  basic: 'Basic',
  pro: 'Professional',
  pro_plus: 'Professional Plus',
  enterprise: 'Enterprise',
};

export const planIdPriceConstant = {
  basic: 135,
  pro: 175,
  pro_plus: 220,
  enterprise: 265,
};

export const planPriceConstant = {
  BASIC: 135,
  PROFESSIONAL: 175,
  PROFESSIONAL_PLUS: 220,
  ENTERPRISE: 265,
};

export const totalCredits = {
  BASIC: 15000,
  PROFESSIONAL: 50000,
  PROFESSIONAL_PLUS: 90000,
  ENTERPRISE: 120000,
};

const stepConstant = {
  LICENSE_SELECTION: 1,
  PACKAGE_SELECTION: 2,
};

const LicensePackageSelection = ({
  licenseType,
  paymentPackage,
  handleStateChange,
  handleSubmit,
}) => {
  const [step, setStep] = useState(stepConstant.LICENSE_SELECTION);

  const licenseOptions = Object.entries(licenseTypeConstant).map(
    ([_key, value]) => ({
      label: value?.toLowerCase()?.capitalize(),
      value,
    })
  );

  const paymentPackageOptions = Object.entries(paymentPackageConstant).map(
    ([_key, value]) => ({
      label: value?.toLowerCase()?.capitalize()?.replace('_', ' '),
      value,
    })
  );

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-4xl flex">
      <div className="text-white p-8 flex-1 relative bg-gray-200">
        <div className="absolute inset-0 opacity-50 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="relative group">
              <div className="w-28 h-28 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-cyan-500/25 transition-all duration-300">
                <Zap className="w-24 h-24 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <div className="!text-5xl font-bold gradient-text">ZILEO</div>
          </div>
        </div>
      </div>
      <div className="p-8 flex-1 min-h-[30rem] ">
        <div className="mb-8">
          <h2 className="text-xl font-semibold">Company set up</h2>
          <div className="flex items-center space-x-2 mt-2">
            <div
              className={clsx(
                'w-1/2 h-1 ',
                step > stepConstant.LICENSE_SELECTION
                  ? 'bg-cyan-600'
                  : 'bg-gray-300'
              )}
            ></div>
            <div className="w-1/2 h-1 bg-gray-300"></div>
          </div>
        </div>

        {step === stepConstant.LICENSE_SELECTION && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold">
              Let's Begin! Which is your License Type?
              <span className="text-yellow-500">🧐</span>
            </h3>
            <div className="mt-4 space-y-4 min-h-[10rem]">
              <Radio.Group
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 8,
                  fontWeight: 500,
                  fontSize: 16,
                }}
                value={licenseType}
                options={licenseOptions}
                onChange={(e) => {
                  handleStateChange({ licenseType: e.target.value });
                }}
              />
            </div>
          </div>
        )}

        {step === stepConstant.PACKAGE_SELECTION && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold">
              Let's Begin! Which is your Package?
              <span className="text-yellow-500">🧐</span>
            </h3>
            <div className="mt-4 space-y-4 min-h-[10rem]">
              <Radio.Group
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 8,
                  fontWeight: 500,
                  fontSize: 16,
                }}
                value={paymentPackage}
                options={paymentPackageOptions}
                onChange={(e) => {
                  handleStateChange({ paymentPackage: e.target.value });
                }}
              />
            </div>
          </div>
        )}
        <div className="flex justify-between items-center font-medium">
          {step > stepConstant.LICENSE_SELECTION ? (
            <Button
              onClick={() => {
                setStep(stepConstant.LICENSE_SELECTION);
              }}
              type="text"
              className="text-gray-500 bg-white"
              icon={<LeftOutlined />}
            >
              Back
            </Button>
          ) : (
            <div />
          )}
          <Button
            onClick={() => {
              if (step === stepConstant.LICENSE_SELECTION) {
                setStep(stepConstant.PACKAGE_SELECTION);
              } else {
                handleSubmit();
              }
            }}
            type="text"
            className="flex items-center justify-center gap-1"
          >
            <span>Next</span>
            <RightOutlined />
          </Button>
        </div>
        <div className="mt-4 text-center">
          <p className="text-gray-500">
            Need Some Help?
            <a className="text-cyan-600 pl-2" href="#">
              Click here
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LicensePackageSelection;
