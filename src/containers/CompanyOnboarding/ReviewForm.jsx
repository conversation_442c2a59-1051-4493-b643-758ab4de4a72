import { Button, Col, Image, notification, Table } from 'antd';
import { TableWrapper } from '../CRM/styled';
import { DollarOutlined, ExportOutlined } from '@ant-design/icons';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { useRef, useState } from 'react';
import { toPng } from 'html-to-image';
import { onboardingCompany } from '../../services/users';
import { useNavigate } from 'react-router-dom';
import { planIdConstant } from './LicensePackageSelection';

const ReviewForm = ({
  employees,
  company,
  licenseType,
  paymentPackage,
  totalOriginalCredits,
  payPer,
}) => {
  const navigate = useNavigate();

  const invoiceRef = useRef(null);
  const [exportLoading, setExportLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const back = () => {
    if (window?.history?.length > 1) {
      navigate(-1);
    } else {
      navigate('/user-management/company');
    }
  };

  const handleSubmit = async () => {
    setSubmitLoading(true);
    const {
      name,
      bhClientId,
      bhClientSecret,
      bhUsername,
      bhPassword,
      companySize,
      companyWebsite,
      companyOwner,
      companyTypes,
      tags,
      address,
      phone,
      companyEmail,
      companyAvatar,
      overview,
      note,
      numberOfLicenses,
    } = company;
    const newEmployees = employees.map(
      ({ email, role, userName, password, credits }) => ({
        email,
        role,
        username: userName,
        password,
        originalCredits: credits,
      })
    );
    // {
    //     "company": {
    //         "name": "Olver zZ",
    //         "companyAvatar": "",
    //         "companyWebsite": "",
    //         "companyIndustries": [],
    //         "companyTypes": [
    //             null
    //         ],
    //         "companyAdmins": [],
    //         "license": "STANDARD",
    //         "package": "BASIC",
    //         "totalOriginalCredits": "15000"
    //     },
    //     "employees": [
    //         {
    //             "email": "<EMAIL>",
    //             "role": "ADMIN",
    //             "username": "oka34",
    //             "password": "*FU(06,1DY&Z",
    //             "originalCredits": 15000
    //         }
    //     ],
    //     "subscription": {
    //         "planId": "basic",
    //         "licenseCount": 10,
    //         "billingCycle": "monthly"
    //     }
    // }
    const payload = {
      company: {
        name,
        bhClientId,
        bhClientSecret,
        bhUsername,
        bhPassword,
        companySize: companySize?.toString(),
        companyAvatar,
        companyWebsite,
        companyOwner,
        companyIndustries: [],
        companyTypes,
        companyAdmins: [],
        tags,
        address,
        phone,
        license: licenseType,
        package: paymentPackage,
        companyEmail,
        overview,
        note,
        totalOriginalCredits: totalOriginalCredits?.toString(),
      },
      employees: newEmployees,
      subscription: {
        planId: planIdConstant[paymentPackage],
        licenseCount: numberOfLicenses,
        billingCycle: payPer,
      },
    };

    try {
      const { data } = await onboardingCompany(payload);

      notification.success({
        message: 'Company Onboarding successfully',
      });
      back();
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);

      notification.error({
        message:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
    }
  };

  const columns = [
    {
      title: 'Invitation Email',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      render: (email) => {
        return <div>{email}</div>;
      },
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      width: '15%',
      render: (role, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {role?.toLowerCase()?.replace('_', ' ')?.capitalize() || '-'}
          </div>
        );
      },
    },
    {
      title: 'Username',
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      render: (userName, record) => {
        return (
          <div className="line-clamp-1" title={userName}>
            {userName}
          </div>
        );
      },
    },
    {
      title: 'Password',
      dataIndex: 'password',
      key: 'password',
      width: '25%',
      render: (password, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {password || '-'}
          </div>
        );
      },
    },
    {
      title: 'Credits',
      dataIndex: 'credits',
      key: 'credits',
      width: '5%',
      render: (credits, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {credits || '0'}
          </div>
        );
      },
    },
  ];

  const handleExportInvoice = async () => {
    setExportLoading(true);
    toPng(invoiceRef.current, { cacheBust: true })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `${company?.name || 'invoice'}.png`;
        link.href = dataUrl;
        link.click();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setExportLoading(false));
  };

  return (
    <div className="w-full mx-auto bg-white rounded-lg shadow-md mb-5">
      <div className="bg-gray-50 p-6 rounded-lg shadow-inner">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
            <Image src={zileoLogo} width={100} preview={false} />
            <div>
              <h2 className="text-xl font-bold flex items-center gap-2">
                {paymentPackage?.toLowerCase()?.replace('_', ' ')?.capitalize()}
              </h2>
              <p className="text-gray-500">
                {licenseType?.toLowerCase()?.replace('_', ' ')?.capitalize()}
              </p>
            </div>
          </div>
          <div className="flex space-x-4">
            <Button
              loading={exportLoading}
              onClick={handleExportInvoice}
              type="primary"
              icon={<ExportOutlined />}
            >
              Export
            </Button>
          </div>
        </div>
        <div
          className="bg-white p-6 rounded-lg shadow-md mb-6 max-h-[40rem] overflow-auto"
          ref={invoiceRef}
        >
          <h3 className="text-lg font-bold mb-4">Company Detail Information</h3>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-gray-500">Company Name</p>
              <p>{company?.name}</p>
            </div>
            <div>
              <p className="text-gray-500">Company Size</p>
              <p>{company?.companySize}</p>
            </div>
            <div>
              <p className="text-gray-500">Address</p>
              <p>{company?.address}</p>
            </div>
            <div>
              <p className="text-gray-500">Owner Name</p>
              <p>{company?.companyOwner}</p>
            </div>
            <div>
              <p className="text-gray-500">Number of Licenses</p>
              <p>{company?.numberOfLicenses}</p>
            </div>
            <div>
              <p className="text-gray-500">Type Of Company</p>
              <p>{company?.companyTypes?.[0]}</p>
            </div>
            <div>
              <p className="text-gray-500">Admin Email</p>
              <p>{company?.companyEmail}</p>
            </div>
            <div>
              <p className="text-gray-500">Phone</p>
              <p>{company?.phone}</p>
            </div>
            <div>
              <p className="text-gray-500">Website</p>
              <p>
                {company?.companyWebsite
                  ? `https://${company?.companyWebsite}`
                  : ''}
              </p>
            </div>
          </div>
          <Col xs={24}>
            <div className="contact-table">
              <TableWrapper className="table-responsive text-gray-800">
                <Table
                  className="customized-style-pagination w-full"
                  rowClassName="editable-row"
                  dataSource={employees}
                  columns={columns}
                  pagination={false}
                />
              </TableWrapper>
            </div>
          </Col>
        </div>
        <div className="mt-6 flex justify-between w-full gap-2 items-center">
          <div className='text-sm font-medium text-cyan-700 flex items-center gap-1'><DollarOutlined/> Payment per <span className='font-semibold'>{payPer}</span></div>
          <Button loading={submitLoading} onClick={handleSubmit} type="primary">
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReviewForm;
