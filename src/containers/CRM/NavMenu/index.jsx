import Icon from '@ant-design/icons';
import { Menu } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { CRMRoutes } from '..';
import { v4 as uuid } from 'uuid';
import InforNavMenu from '../../../components/CRM/InforNavMenu';
import { getCRMStats } from '../../../services/crm/stats';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { CRMSettingRoutes } from '../Setting';

const NavMenu = ({ location }) => {
  const { pathname } = location;
  const isInSettingPage = pathname?.includes('setting');

  const initialNavMenuItems = isInSettingPage
    ? [
        {
          label: 'Home',
          key: uuid(),
          description: 'Dashboard',
          to: `/crm`,
        },
        ...Object.entries(CRMSettingRoutes).map(([key, value]) => ({
          label: value.title,
          key: uuid(),
          description: '',
          to: value.path,
        })),
      ]
    : [
        {
          label: 'Home',
          key: uuid(),
          description: 'Dashboard',
          to: `/crm`,
        },
        ...Object.entries(CRMRoutes).map(([key, value]) => ({
          label: value.title,
          key: uuid(),
          description: value?.title === 'Settings' ? 'Settings' : '',
          to: value.path,
        })),
      ];

  const [navMenuItems, setNavMenuItems] = useState(initialNavMenuItems);

  const getData = async () => {
    try {
      const { data } = await getCRMStats();
      if (data?.result && !isInSettingPage) {
        const newNavMenuItems = [...navMenuItems];
        newNavMenuItems[1].description = `${data?.result?.companyCount || 0} in total`;
        newNavMenuItems[2].description = `${data?.result?.contactCount || 0} in total`;
        newNavMenuItems[3].description = `${data?.result?.leadCount || 0} in total`;
        // newNavMenuItems[4].description = `${data?.result?.tagCount || 0} in total`;
        setNavMenuItems(newNavMenuItems);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <div className="py-5">
      <InforNavMenu
        items={navMenuItems}
        selectedItemKey={pathname}
        className={clsx('w-1/4')}
      />
    </div>
  );
};

export default NavMenu;
