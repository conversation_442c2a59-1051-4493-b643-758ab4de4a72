import {
  ArrowLeftOutlined,
  AuditOutlined,
  BankOutlined,
  LeftOutlined,
  SettingOutlined,
  TagsOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';

const DetailBar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const splittedLocation = location?.pathname?.split('/');
  const currentTab = splittedLocation?.[2]?.capitalize();
  const currentRoute = splittedLocation?.[3]?.capitalize();

  const back = () => {
    if (window?.history?.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm');
    }
  };

  const ICONS = {
    LEADS: <AuditOutlined className="font-bold" />,
    CONTACTS: <UserOutlined className="font-bold" />,
    COMPANIES: <BankOutlined className="font-bold" />,
    TAGS: <TagsOutlined className="font-bold" />,
  };

  return (
    <div className="w-full flex items-center justify-between px-2 py-3 rounded-md bg-white shadow-md mb-2">
      <div className="flex items-center gap-1 text-xl font-bold text-cyan-700">
        <Button
          onClick={back}
          icon={<LeftOutlined />}
          className="bg-white"
        >
          Back
        </Button>

        <div className="flex items-center gap-2">
          {ICONS[currentTab]} {currentTab} / {currentRoute}
        </div>
      </div>
      <Button icon={<SettingOutlined />} />
    </div>
  );
};

export default DetailBar;
