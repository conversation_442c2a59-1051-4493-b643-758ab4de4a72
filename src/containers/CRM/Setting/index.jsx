import zileLogo from '../../../assets/img/welcome/logo.png';
import { Button, Image } from 'antd';
import '.././crm.scss';
import ContainerCard from '../../../components/CRM/ContainerCard';
import {
  LeftOutlined,
  TagsOutlined,
  ThunderboltOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

export const CRMSettingRoutes = {
  tags: {
    path: '/crm/setting/tags',
    title: 'Tags',
    description: 'Manage your tags',
    icon: <TagsOutlined className="text-4xl font-bold text-cyan-600" />,
  },
  industries: {
    path: '/crm/setting/industries',
    title: 'Industries',
    description: 'Manage your industries',
    icon: <ToolOutlined className="text-4xl font-bold text-cyan-600" />,
  },
  skills: {
    path: '/crm/setting/skills',
    title: 'Skills',
    description: 'Manage your skills',
    icon: <ThunderboltOutlined className="text-4xl font-bold text-cyan-600" />,
  },
};

const CRMSettingPage = ({
  isInSettings = false, // This prop can be used to conditionally render settings-related UI
}) => {
  const navigate = useNavigate();
  return (
    <div className="w-full h-full rounded-md shadow-md rounded-md p-4 flex flex-col gap-16 items-center justify-center relative">
      {!isInSettings && (
        <Button
          className="absolute top-4 left-4"
          type="primary"
          onClick={() => navigate('/crm')}
          icon={<LeftOutlined />}
        >
          Go to CRM home
        </Button>
      )}
      <div className="text-4xl font-bold flex items-center justify-center gap-2">
        <Image preview={false} src={zileLogo} width={120} /> CRM Settings
      </div>
      <div className="flex items-center justify-center gap-10">
        {Object.entries(CRMSettingRoutes).map(([key, value]) => (
          <ContainerCard
            icon={value.icon}
            title={value.title}
            description={value.description}
            url={value.path}
          />
        ))}
      </div>
    </div>
  );
};

export default CRMSettingPage;
