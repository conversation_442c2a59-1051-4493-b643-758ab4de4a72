import { SettingOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { Link } from 'react-router-dom';

const SettingsButton = () => {
  return (
    <Tooltip
      title="Go to CRM Settings"
      placement="left"
      color="rgb(38, 166, 191)"
    >
      <Link
        to="/crm/setting"
        className="cursor-pointer fixed flex items-center justify-center top-3/4 right-0 bg-cyan-600 text-white p-3 rounded-tl-md rounded-bl-md shadow-xl z-50 "
      >
        <SettingOutlined
          className="text-xl font-semibold delay-300"
          spin
          style={{ animationDuration: '2s' }} // Adjust the duration to slow down the spin
        />
      </Link>
    </Tooltip>
  );
};

export default SettingsButton;
