import { Suspense, useEffect, useState } from 'react';
import { getContactById } from '../../../services/crm/contacts';
import { Button, Col, notification, Row, Skeleton } from 'antd';
import { NavLink, useNavigate, useParams } from 'react-router-dom';
import clsx from 'clsx';
import { Main, SettingWrapper } from '../styled';
import { Cards } from '../../../components/Cards';
import UserCards from '../../../components/CRM/UserCard';
import UserBio from '../../../components/CRM/UserBio';
import CoverSection from '../../../components/CRM/CoverSection';
import Activity from '../../../components/CRM/Activity';
import { LeftOutlined } from '@ant-design/icons';
import Loading from '../../HotList/Loading';
import DetailBar from '../NavMenu/DetailBar';
import Notes from '../../../components/CRM/Notes';
import { RESOURCE_NAME } from '../crm.constant';
import Files from '../../../components/CRM/Files';
import Emails from '../../../components/CRM/Emails';
import Sequences from '../../../components/CRM/Sequences';

const tabs = [
  // 'Overview',
  // 'Activity',
  'Notes',
  'Files',
  'Emails',
  'Sequences',
];

const ViewContact = () => {
  const navigate = useNavigate();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/contacts');
    }
  };
  const [selectedTab, setSelectedTab] = useState('Notes');
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id } = useParams();

  const getContactDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getContactById(id);
      if (data?.result) {
        const Contact = data.result;
        setItem({ ...Contact });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to fetch Contact details',
      });
    }
  };

  useEffect(() => {
    getContactDetail();
  }, [id]);

  const tabRenderer = {
    Notes: <Notes resourceName={RESOURCE_NAME.CONTACT} resourceId={id} />,
    Files: <Files resourceId={id} resourceName={RESOURCE_NAME.CONTACT} />,
    Emails: <Emails resourceId={id} resourceName={RESOURCE_NAME.CONTACT} />,
    Sequences: (
      <Sequences resourceId={id} resourceName={RESOURCE_NAME.CONTACT} />
    ),
  };

  return (
    <div>
      <DetailBar />
      {!loading && item && (
        <Main>
          <Row gutter={25}>
            <Col xxl={6} lg={8} md={10} xs={24}>
              <Suspense
                fallback={
                  <Cards headless>
                    <Skeleton avatar active paragraph={{ rows: 3 }} />
                  </Cards>
                }
              >
                <UserBio
                  name={
                    `${item?.firstName} ${item?.middleName} ${item?.surName}` ||
                    'N/A'
                  }
                  bio={''}
                  email={item?.email || 'N/A'}
                  telephone={item?.telephone || 'N/A'}
                  industries={item?.industries}
                  tags={item?.tags}
                  address={
                    (item?.address && `${item?.address?.country}`) || 'N/A'
                  }
                  linkedinUrl={item?.linkedinUrl || 'N/A'}
                />
              </Suspense>
              <Suspense
                fallback={
                  <Cards headless>
                    <Skeleton active paragraph={{ rows: 10 }} />
                  </Cards>
                }
              >
                <UserBio
                  cardTitle="Company Info"
                  companyName={item?.company?.name || 'N/A'}
                  companyWebsite={item?.company?.website || 'N/A'}
                  telephone={item?.company?.telephone || 'N/A'}
                  industries={item?.company?.industries || []}
                  tags={item?.company?.tags || []}
                  address={
                    (item?.company?.address &&
                      `${item?.company?.address?.country}`) ||
                    'N/A'
                  }
                />
              </Suspense>
            </Col>
            <Col xxl={18} lg={16} md={14} xs={24}>
              <SettingWrapper>
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <div className="coverWrapper">
                    <nav className="profileTab-menu">
                      <div className="flex items-center gap-5 text-sm font-medium">
                        {tabs.map((tab) => (
                          <div
                            className={clsx(
                              'py-4 hover:text-cyan-800 cursor-pointer',
                              selectedTab === tab &&
                                'text-cyan-600 border-b border-b-2 border-cyan-600'
                            )}
                            onClick={() => setSelectedTab(tab)}
                          >
                            {tab}
                          </div>
                        ))}
                      </div>
                    </nav>
                  </div>
                </Suspense>
                {tabRenderer[selectedTab]}
              </SettingWrapper>
            </Col>
          </Row>
        </Main>
      )}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <Loading />
        </div>
      )}
      {!loading && !item && (
        <div className="flex items-center justify-center h-64">
          Contact not found or deleted by another user.
        </div>
      )}
    </div>
  );
};

export default ViewContact;
