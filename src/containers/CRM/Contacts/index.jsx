import NavMenu from '../NavMenu';
import {
  AutoComplete,
  Badge,
  Button,
  Checkbox,
  Col,
  Dropdown,
  Form,
  Modal,
  notification,
  Popconfirm,
  Popover,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import {
  CardToolbox,
  ContactPageheaderStyle,
  Main,
  TableWrapper,
  UserTableStyleWrapper,
} from '../styled';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import {
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  ExclamationCircleFilled,
  EyeOutlined,
  FilterOutlined,
  LinkedinOutlined,
  MoreOutlined,
  PlusOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import Search from 'antd/es/input/Search';
import { Cards } from '../../../components/Cards';
import { useEffect, useState } from 'react';
import FiltersBox from '../../../components/CRM/FiltersBox';
import { deleteContact, getAllContacts } from '../../../services/crm/contacts';
import { paramsToObj } from '../../SyncSearchJobListV2';
import ConsultantRow from './ConsultantRow';
import { getDisplayColumns, updateDisplayColumns } from '../Companies';
import { UserSettingEnum } from '../../../services/users';

const { confirm } = Modal;

const defaultDisplayColumns = [
  'Name',
  'Job Title',
  'Company',
  'Industry',
  'Telephone',
  'Email',
  'Action',
];

const initialPagination = {
  page: 1,
  limit: 10,
  total: 0,
};

const CRMContactsPage = () => {
  let [searchParams, setSearchParams] = useSearchParams();
  const searchParamsObj = paramsToObj(searchParams);
  const navigate = useNavigate();
  const [pagination, setPagination] = useState(initialPagination);
  const [dataSource, setDataSource] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(true);

  const [displayColummns, setDisplayColumns] = useState(defaultDisplayColumns);
  const [filters, setFilters] = useState([]);
  const [openFilters, setOpenFilters] = useState(false);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  const showFilters = () => setOpenFilters(true);
  const closeFilters = () => setOpenFilters(false);

  const [state, setState] = useState({
    selectedRowKeys: [],
    selectedRows: 0,
    visible: false,
    editVisible: false,
    modalType: 'primary',
    url: null,
    update: {},
  });

  const handleDelete = async (id) => {
    try {
      const { data } = await deleteContact(id);
      notification.success({
        description: 'Company deleted successfully',
      });
      const newDataSource = dataSource.filter((item) => item?.id !== id);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('error', error);
      notification.error({
        description: 'Failed to delete company',
      });
    }
  };

  const handleBulkDelete = async () => {
    setBulkActionLoading(true);
    try {
      const selectedIds = state?.selectedRowKeys;
      if (selectedIds?.length === 0) return;
      const data = await Promise.all(
        selectedIds.map(async (id) => await deleteContact(id))
      );
      notification.success({
        description: 'Contacts deleted successfully',
      });
      const newDataSource = dataSource.filter(
        (item) => !selectedIds?.includes(item?.id)
      );
      setDataSource([...newDataSource]);
      setPagination(initialPagination);
      setState({
        ...state,
        selectedRowKeys: [],
        selectedRows: 0,
      });
      setBulkActionLoading(false);
    } catch (error) {
      console.log('error', error);
      setBulkActionLoading(false);
      notification.error({
        description: 'Failed to delete selected contacts',
      });
    }
  };

  const usersTableColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => {
        const firstName = record?.firstName || '';
        const middleName = record?.middleName || '';
        const lastName = record?.surName || '';
        const name = `${firstName} ${middleName} ${lastName}`;
        return (
          <div
            className="font-semibold text-cyan-600 flex items-center gap-1 line-clamp-1 max-w-xs"
            title={name}
          >
            <UserOutlined /> {name}
          </div>
        );
      },
    },
    {
      title: 'Company',
      dataIndex: 'company',
      key: 'company',
      render: (company, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {company?.name || '-'}
          </div>
        );
      },
    },
    {
      title: 'Job Title',
      dataIndex: 'jobTitle',
      key: 'jobTitle',
      render: (jobTitle, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {jobTitle || '-'}
          </div>
        );
      },
    },
    {
      title: 'Linkedin',
      dataIndex: 'linkedinUrl',
      key: 'linkedinUrl',
      render: (linkedinUrl, record) => {
        return (
          linkedinUrl && (
            <Tooltip title={linkedinUrl}>
              <a
                className="flex justify-start w-full items-center gap-1"
                href={linkedinUrl}
                target="_blank"
              >
                <LinkedinOutlined
                  style={{
                    color: '#0288d1',
                    fontSize: '20px',
                    cursor: 'pointer',
                  }}
                />
                <p className={`line-clamp-1`}>{record?.firstName}</p>
              </a>
            </Tooltip>
          )
        );
      },
    },
    {
      title: 'Consultant',
      dataIndex: 'consultantId',
      key: 'consultantId',
      render: (consultantId, record) => {
        return (
          consultantId?.trim() && <ConsultantRow consultantId={consultantId} />
        );
      },
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      render: (address, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {address?.country || '-'}
          </div>
        );
      },
    },
    {
      title: 'Industry',
      dataIndex: 'industries',
      key: 'industries',
      render: (industries, record) => {
        console.log('industries', industries);
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {industries?.length > 0 &&
              industries?.map((industry) => (
                <Tag color="cyan">{industry?.name}</Tag>
              ))}
          </div>
        );
      },
    },
    {
      title: 'Telephone',
      dataIndex: 'telephone',
      align: 'start',
      key: 'telephone',
      render: (telephone, record) => {
        return <div className="font-semibold text-cyan-600"> {telephone}</div>;
      },
    },
    {
      title: 'Email',
      dataIndex: 'email',
      align: 'start',
      key: 'email',
      render: (email, record) => {
        return <div className="font-semibold text-cyan-600">{email}</div>;
      },
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {tags?.length > 0 &&
              tags.map((tag) => <Tag color="cyan">{tag?.name}</Tag>)}
          </div>
        );
      },
    },
    // {
    //   title: 'Emails & Sequence',
    //   dataIndex: 'emailSequence',
    //   key: 'emailSequence',
    // },
    // {
    //   title: 'Files',
    //   dataIndex: 'files',
    //   key: 'files',
    // },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: '50px',
      align: 'center',
      render: (_text, record) => {
        const items = [
          {
            key: 'view',
            label: 'View',
            icon: <EyeOutlined />,
          },
          {
            key: 'edit',
            label: 'Edit',
            icon: <EditOutlined />,
          },
          // {
          //   key: 'clone',
          //   label: 'Clone',
          //   icon: <CopyOutlined />,
          // },
        ];
        const handleMenuClick = ({ key }) => {
          switch (key) {
            case 'view':
              navigate(`/crm/contacts/view/${record?.id}`);
              break;
            case 'edit':
              navigate(`/crm/contacts/edit/${record?.id}`);
              break;
            case 'clone':
              // navigate(`/crm/contacts/${record?.id}`);
              break;
          }
        };

        const firstName = record?.firstName || '';
        const middleName = record?.middleName || '';
        const lastName = record?.surName || '';
        const name = `${firstName} ${middleName} ${lastName}`;

        return (
          <div
            onClick={(e) => e.stopPropagation()}
            className="font-semibold w-full flex justify-center items-center gap-2"
          >
            <Popconfirm
              title={'Confirmation'}
              description={
                <div>
                  Are you sure to delete{' '}
                  <span className="font-semibold">{name}</span> Contact?
                </div>
              }
              onConfirm={async () => await handleDelete(record?.id)}
              // onCancel={cancel}
              okText="Delete"
              cancelText="Cancel"
              placement="topLeft"
            >
              <Button danger icon={<DeleteOutlined />}></Button>
            </Popconfirm>
            <Dropdown
              menu={{
                items,
                onClick: handleMenuClick,
              }}
              placement="bottomRight"
              arrow
            >
              <Button icon={<MoreOutlined />} />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setState({ ...state, selectedRowKeys, selectedRows });
    },
  };

  const tableColumnTitle = usersTableColumns.map((col) => col.title);

  const tableSettings = (
    <div>
      <Checkbox.Group
        className="flex flex-col gap-2"
        value={displayColummns}
        options={tableColumnTitle.map((text) => ({
          label: text,
          value: text,
        }))}
        onChange={(value) => {
          setDisplayColumns([...value]);
          updateDisplayColumns(UserSettingEnum.CRM_CONTACT, value);
        }}
      />
    </div>
  );

  const newUsersTableColumns = usersTableColumns.filter((item) =>
    displayColummns.includes(item.title)
  );

  const filterFactors = usersTableColumns
    .map((item) => ({
      label: item.title,
      value: item.dataIndex,
    }))
    .filter((item) => item.value !== 'action');

  const handleSubmitFilters = ({ filters }) => {
    console.log('Received values of form:', filters);
    const newFilters = [...filters];
    const companyId =
      newFilters.find((item) => item.label === 'Company')?.value || '';

    setFilters([...newFilters]);
    if (companyId) {
      handleSearch('', [companyId]);
    }
    if (newFilters.length === 0) {
      handleSearch();
    }
  };

  const filterContent = (
    <div className="min-w-[35rem]">
      <FiltersBox
        defaultFilters={filters}
        fieldItems={filterFactors}
        closeFilterModal={closeFilters}
        handleSubmit={handleSubmitFilters}
      />
    </div>
  );

  const handleSearch = async (
    value = '',
    companyIds = [],
    submitPagination = null
  ) => {
    setLoading(true);
    const text = value?.trim() || searchText?.trim();
    try {
      const payload = {
        ...(text && { keyword: text }),
        ...(companyIds?.length > 0 && { companyIds }),
        page: submitPagination?.page || pagination.page,
        limit: submitPagination?.limit || pagination.limit,
      };
      const { data } = await getAllContacts(payload);
      if (data?.result?.data?.length > 0) {
        const { data: newData, pagination } = data?.result;
        setDataSource([...newData]);
        setPagination({
          ...pagination,
          total: pagination?.total,
        });
      } else {
        setDataSource([]);
        setPagination(initialPagination);
      }
      console.log('data', data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  const handlePageChange = (page, pageSize) => {
    console.log('page', page);
    console.log('pageSize', pageSize);
    setPagination({
      ...pagination,
      page,
    });
    handleSearch('', [], { page, pageSize });
  };
  const handleGetDisplayColumns = async () => {
    const displayColumns = await getDisplayColumns(UserSettingEnum.CRM_CONTACT);
    setDisplayColumns([...displayColumns]);
  };

  useEffect(() => {
    handleSearch();
    handleGetDisplayColumns();
  }, []);

  useEffect(() => {
    const companyId = searchParamsObj?.ci || '';
    if (companyId) {
      handleSubmitFilters({
        filters: [{ value: companyId, label: 'Company' }],
      });
    }
  }, [searchParamsObj?.ci]);

  return (
    <>
      <NavMenu location={window?.location} />
      <CardToolbox>
        <ContactPageheaderStyle>
          <div className="w-full h-full flex justify-between items-center">
            <div className="flex items-center gap-4 justify-center">
              <Button
                onClick={() => navigate('/crm/contacts/create')}
                type="primary"
                icon={<PlusOutlined />}
              >
                Add Contact
              </Button>
              <Search
                loading={loading}
                placeholder="Search by name"
                enterButton="Search"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={(keyword) => {
                  handleSearch(keyword);
                }}
              />
            </div>
            <div className="flex items-center gap-2">
              {state.selectedRowKeys?.length > 0 && (
                <div>
                  <Dropdown
                    className="animated fadeInDownBig"
                    placement="bottom"
                    arrow
                    menu={{
                      items: [
                        {
                          key: 'delete-selected',
                          label: (
                            <a
                              className="flex gap-2 items-center"
                              onClick={(e) => {
                                e.preventDefault();
                                confirm({
                                  title:
                                    'Do you want to delete all selected Contacts?',
                                  icon: <ExclamationCircleFilled />,
                                  content: ' ',
                                  onOk() {
                                    handleBulkDelete();
                                  },
                                  onCancel() {
                                    return;
                                  },
                                });
                              }}
                            >
                              <span>Delete</span>
                            </a>
                          ),
                          icon: <DeleteOutlined />,
                        },
                      ],
                    }}
                  >
                    <Space>
                      <Button
                        loading={bulkActionLoading}
                        type="primary"
                        className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                      >
                        <p className="Montserrat">
                          {`${state.selectedRowKeys?.length} Selected`}
                        </p>
                        <DownOutlined />
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              )}
              {/* <Popover
                content={filterContent}
                destroyTooltipOnHide
                action="click"
                open={openFilters}
                onOpenChange={(visible) => setOpenFilters(visible)}
                placement="left"
              >
                <Badge count={filters.length}>
                  <Button
                    type="default"
                    className="bg-white font-medium"
                    icon={
                      <FilterOutlined className="font-semibold text-cyan-600" />
                    }
                  >
                    Filters
                  </Button>
                </Badge>
              </Popover> */}
              <Popover
                content={tableSettings}
                title="Settings"
                placement="bottomLeft"
              >
                <Button type="dashed" icon={<SettingOutlined />}></Button>
              </Popover>
            </div>
          </div>
        </ContactPageheaderStyle>
      </CardToolbox>
      <Main>
        <Row gutter={15}>
          <Col xs={24}>
            <Cards headless>
              <UserTableStyleWrapper>
                <div className="contact-table">
                  <TableWrapper className="table-responsive text-gray-800">
                    <Table
                      className="customized-style-pagination w-full"
                      rowKey={(record) => record?.id}
                      loading={loading}
                      rowSelection={rowSelection}
                      dataSource={dataSource}
                      columns={newUsersTableColumns}
                      onRow={(record) => {
                        return {
                          onClick: () => {
                            state.selectedRowKeys?.length === 0 &&
                              navigate(`/crm/contacts/view/${record?.id}`);
                          },
                          style: { cursor: 'pointer' },
                        };
                      }}
                      pagination={{
                        defaultPageSize: pagination.limit,
                        total: pagination.total,
                        showTotal: (total, range) =>
                          `${range[0]}-${range[1]} of ${total} items`,
                        showSizeChanger: false,
                        onChange: handlePageChange,
                      }}
                    />
                  </TableWrapper>
                </div>
              </UserTableStyleWrapper>
            </Cards>
          </Col>
        </Row>
      </Main>
    </>
  );
};

export default CRMContactsPage;
