import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  notification,
  Row,
  Select,
  Space,
} from 'antd';
import { Cards } from '../../../components/Cards';
import BaseForm from '../../../components/CRM/BaseForm';
import { BasicFormWrapper, HorizontalFormStyleWrap } from '../styled';
import { tagRender } from '../../../components/SearchDetailV2/NewSearchFilterComponent';
import { countries } from 'country-list-json';
import { getAllIndustries } from '../../../services/crm/industries';
import { useEffect, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { getAllCompanies } from '../../../services/crm/companies';
import { getAllSkills } from '../../../services/crm/skills';
import {
  CONSULTANT_SOURCE,
  MODE_CONSTANTS,
} from '../../../constants/common.constant';
import { getUsers } from '../../../services/users';

const { Option } = Select;

const ContactForm = ({
  handleSubmit,
  back,
  contact = null,
  mode = MODE_CONSTANTS.CREATE,
}) => {
  const [options, setOptions] = useState({
    industry: [],
    company: [],
    skills: [],
    consultant: [],
  });
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const onFinish = async (values) => {
    const {
      name,
      address,
      company,
      email,
      industries,
      notes,
      telephone,
      tags,
      jobTitle,
      consultantId,
      linkedinUrl,
    } = values;
    const { firstName, surName, middleName } = name;
    console.log(values);
    setLoading(true);
    const payload = {
      firstName,
      surName,
      middleName,
      telephone,
      email,
      address,
      companyId: company,
      industries,
      notes,
      tags,
      jobTitle,
      consultantId,
      consultantType: CONSULTANT_SOURCE.ZILEO,
      linkedinUrl,
    };
    await handleSubmit(payload);
    setLoading(false);
  };

  const handleCancel = () => {
    back();
  };

  const selectDialCode = (
    <Select showSearch defaultValue={'+44'}>
      {[...new Set(countries.map((item) => item.dial_code))].map(
        (dial_code) => (
          <Option value={dial_code}>{dial_code}</Option>
        )
      )}
    </Select>
  );

  const getIndustryOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllIndustries();

      if (data?.result?.length > 0) {
        result = data.result.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getCompanyOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllCompanies({
        keyword: '',
        page: 1,
        limit: 100,
      });

      if (data?.result?.data?.length > 0) {
        result = data?.result?.data?.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getConsultantOptions = async () => {
    let result = [];
    try {
      const { data } = await getUsers();
      console.log('getConsultantOptions:', data);
      if (data?.length > 0) {
        result = data?.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.fullName || item?.email,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getData = async () => {
    const [industry, company, consultant] = await Promise.all([
      getIndustryOptions(),
      getCompanyOptions(),
      getConsultantOptions(),
    ]);

    setOptions({
      ...options,
      industry,
      company,
      consultant,
    });
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (!contact) return;
    const { industries, company, tags } = contact;
    const newIndustries =
      industries?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];

    const newTags =
      tags?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];

    form.setFieldsValue({
      ...contact,
      industries: newIndustries,
      company: company?.id,
      name: {
        firstName: contact?.firstName,
        middleName: contact?.middleName,
        surName: contact?.surName,
      },
      tags: newTags,
    });
    console.log(contact);
  }, [contact]);

  return (
    <>
      <BasicFormWrapper>
        <HorizontalFormStyleWrap>
          <Cards
            headless
            title="Contact"
            caption={'Please fill your Contact details'}
          >
            <div className="w-full pb-10 border-b" />
            <Form
              form={form}
              onFinish={onFinish}
              name="horizontal-icno-from"
              layout="horizontal"
              className="mt-4 flex flex-col gap-4"
            >
              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="name">
                    <div className="flex flex-col">
                      <span>Contact Name</span>
                      <span className="text-xs opacity-60">
                        Enter the official name of the Contact.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <div className="grid grid-cols-3 gap-3">
                    <Form.Item
                      name={['name', 'firstName']}
                      rules={[
                        { required: true, message: 'First Name is required' },
                      ]}
                    >
                      <Input placeholder="First Name" />
                    </Form.Item>
                    <Form.Item
                      name={['name', 'middleName']}
                      rules={[{ required: false }]}
                    >
                      <Input placeholder="Middle Name" />
                    </Form.Item>
                    <Form.Item
                      name={['name', 'surName']}
                      rules={[
                        { required: true, message: 'Surname is required' },
                      ]}
                    >
                      <Input placeholder="Surname" />
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="jobTitle">
                    <div className="flex flex-col">
                      <span>Job Title</span>
                      <span className="text-xs opacity-60">
                        Enter the position name or title for the role.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="jobTitle">
                    <Input placeholder="Input job title of contact..." />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="jobTitle">
                    <div className="flex flex-col">
                      <span>Linkedin</span>
                      <span className="text-xs opacity-60">
                        Enter the Linkedin profile link of the Contact.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="linkedinUrl">
                    <Input placeholder="Input the linkedin profile link of contact..." />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="company">
                    <div className="flex flex-col">
                      <span>Company</span>
                      <span className="text-xs opacity-60">
                        Select the Company the Contact is associated with.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item
                    name="company"
                    rules={[{ required: true, message: 'Company is required' }]}
                  >
                    <Select
                      options={options.company}
                      optionFilterProp="label"
                      showSearch
                      placeholder="Select the company"
                      dropdownRender={(menu) => (
                        <>
                          {menu}
                          <Divider style={{ margin: '8px 0' }} />
                          <Space
                            style={{ padding: '0 8px 4px' }}
                            className="flex items-center justify-center"
                          >
                            you don't see the Company you are looking for?
                            &nbsp;
                            <Button
                              type="primary"
                              className="w-full"
                              icon={<PlusOutlined />}
                              onClick={() =>
                                window.open('/crm/companies/create', '_blank')
                              }
                            >
                              Add new Company
                            </Button>
                          </Space>
                        </>
                      )}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="industries">
                    <div className="flex flex-col">
                      <span>Industry</span>
                      <span className="text-xs opacity-60">
                        Specify the sector or field the Contact operates in.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="industries">
                    <Select
                      options={options.industry}
                      optionFilterProp="label"
                      showSearch
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-purple' })
                      }
                      mode="multiple"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Add Contact industries..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="consultantId">
                    <div className="flex flex-col">
                      <span>Consultant</span>
                      <span className="text-xs opacity-60">
                        Specify the consultant's name or area of expertise.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="consultantId">
                    <Select
                      options={options.consultant}
                      optionFilterProp="label"
                      showSearch
                      placeholder="Select the consultant..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="telephone">
                    <div className="flex flex-col">
                      <span>Telephone</span>
                      <span className="text-xs opacity-60">
                        Add the primary contact number for the Contact.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="telephone">
                    <Input
                      addonBefore={selectDialCode}
                      placeholder="Set the Contact phone number..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="email">
                    <div className="flex flex-col">
                      <span>Email</span>
                      <span className="text-xs opacity-60">
                        Enter Email address of the Contact.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="email" rules={[{ required: true }]}>
                    <Input placeholder="Set email address..." />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="address">
                    <div className="flex flex-col">
                      <span>Address</span>
                      <span className="text-xs opacity-60">
                        Input the physical or mailing address of the Contact.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <div className="grid grid-cols-9 gap-3">
                    <Form.Item
                      name={['address', 'address1']}
                      className="col-span-6 !mb-0"
                    >
                      <Input placeholder="Main address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'address2']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Secondary address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'city']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="City" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'county']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="County" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'postCode']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Post Code" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'country']}
                      className="col-span-9 !mb-0"
                    >
                      <Input placeholder="Country" />
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              <BaseForm mode={mode} />
              <Row>
                <Col
                  lg={{ span: 16, offset: 8 }}
                  md={{ span: 15, offset: 9 }}
                  xs={{ span: 24, offset: 0 }}
                >
                  <div className="sDash_form-action flex justify-end">
                    <Button
                      onClick={handleCancel}
                      className="btn-signin"
                      type="dashed"
                      size="large"
                    >
                      Cancel
                    </Button>
                    <Button
                      htmlType="submit"
                      className="btn-signin"
                      type="primary"
                      size="large"
                      loading={loading}
                    >
                      Save
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          </Cards>
        </HorizontalFormStyleWrap>
      </BasicFormWrapper>
    </>
  );
};

export default ContactForm;
