import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Loading from '../../HotList/Loading';
import {
  getContactById,
  updateContactById,
} from '../../../services/crm/contacts';
import ContactForm from './ContactForm';
import { MODE_CONSTANTS } from '../../../constants/common.constant';
import DetailBar from '../NavMenu/DetailBar';

const EditContact = () => {
  const [contact, setContact] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/contacts');
    }
  };

  const handleSavingContact = async (payload) => {
    try {
      const { data } = await updateContactById({ ...payload, id });
      notification.success({
        description: 'Contact updated successfully',
      });
      back();
    } catch (error) {
      console.log(error);
      notification.error({
        description: 'Failed to update Contact',
      });
    }
  };

  const getContactDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getContactById(id);
      if (data?.result) {
        const Contact = data.result;
        setContact({ ...Contact });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to fetch Contact details',
      });
    }
  };

  useEffect(() => {
    getContactDetail();
  }, [id]);

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        {!loading && contact && (
          <ContactForm
            contact={contact}
            handleSubmit={handleSavingContact}
            back={back}
            mode={MODE_CONSTANTS.EDIT}
          />
        )}
        {!loading && !contact && (
          <div className="flex justify-center items-center h-64">
            <div>Contact not found or deleted by another user.</div>
          </div>
        )}
        {loading && (
          <div className="flex justify-center items-center h-64">
            <Loading />
          </div>
        )}
      </div>
    </div>
  );
};

export default EditContact;
