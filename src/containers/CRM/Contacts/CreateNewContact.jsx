import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate } from 'react-router-dom';
import ContactForm from './ContactForm';
import { createNewCRMContact } from '../../../services/crm/contacts';
import DetailBar from '../NavMenu/DetailBar';

const CreateNewContact = () => {
  const navigate = useNavigate();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/contacts');
    }
  };

  const handleSubmit = async (payload) => {
    try {
      const { data } = await createNewCRMContact(payload);
      notification.success({
        description: 'Contact created successfully',
      });
      back();
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Failed to create contact',
      });
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        <ContactForm handleSubmit={handleSubmit} back={back} />
      </div>
    </div>
  );
};

export default CreateNewContact;
