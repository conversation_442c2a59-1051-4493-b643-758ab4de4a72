import { useEffect, useState } from 'react';
import { getUserById } from '../../../services/auth';

const ConsultantRow = ({ consultantId }) => {
  const [consultant, setConsultant] = useState(null);
  const getUser = async () => {
    try {
      const { data } = await getUserById(consultantId);
      if (!data) return;
      setConsultant({ ...data });
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getUser();
  }, [consultantId]);
  return (
    <div className="font-semibold text-cyan-600">
      {consultant?.fullName || consultant?.email}
    </div>
  );
};

export default ConsultantRow;
