import Styled from 'styled-components';

const RecentDealsWrapper = Styled.div`
    min-height: 314px;
    .recent-deals-table{
        .dealing-author{
            display: flex;
            align-items: center;
            img{
                max-width: 36px;
                border-radius: 50%;
            }
            .dealing-author-info{
                ${({ theme }) => (theme.rtl ? 'margin-right' : 'margin-left')}: 12px;
                h4{
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 2px;
                }
                p{
                    font-size: 13px;
                    color: #9297AF;
                    margin-bottom: 0;
                }
            }
        }
        .deal-amount{
            font-size: 14px;
            font-weight: 600;
            color: ${({ theme }) => theme['dark-color']};
        }
        .ant-table-tbody > tr > td{
            padding: 7px 25px;
        }
    }
`;

export { RecentDealsWrapper };
