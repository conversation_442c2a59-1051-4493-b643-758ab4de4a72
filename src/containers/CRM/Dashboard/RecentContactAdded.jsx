import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Table } from 'antd';
import { Cards } from '../../../components/Cards';
import { RecentDealsWrapper } from './style';
import moment from 'moment';
import { getAllContacts } from '../../../services/crm/contacts';
import { BankOutlined, ContactsOutlined, MailOutlined, UserOutlined } from '@ant-design/icons';

const RecentContactAdded = () => {
  const navigate = useNavigate();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(true);

  const columns = [
    {
      title: '',
      dataIndex: 'id',
      key: 'id',
      render: (text, record) => {
        const name =
          `${record?.firstName || ''} ${record?.surName || ''}` || 'N/A';
        const email = record?.email || 'N/A';
        const jobTitle = record?.jobTitle || 'N/A';
        const companyName = record?.company?.name || 'N/A';
        return (
          <div className="flex flex-col gap-1 items-start">
            <div className="font-medium text-cyan-600"><UserOutlined className="mr-1" /> {name}</div>
            <div className="text-sm font-medium opacity-70 grid grid-cols-2 w-full">
              <div className="line-clamp-1" title={jobTitle}>
                <ContactsOutlined  className="mr-1" />
                {jobTitle}
              </div>
              <div className="line-clamp-1" title={companyName}>
                <BankOutlined className="mr-1" />
                {companyName}
              </div>
            </div>
          </div>
        );
      },
    },
  ];

  const handleGetData = async () => {
    setLoading(true);
    try {
      const payload = {
        page: 1,
        limit: 10,
      };
      const { data } = await getAllContacts(payload);
      if (data?.result?.data?.length > 0) {
        const { data: newData, pagination } = data?.result;
        setDataSource([...newData]);
      } else {
        setDataSource([]);
      }
      console.log('data', data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  useEffect(() => {
    handleGetData();
  }, []);

  return (
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/50 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <Cards
        isbutton={<div className="card-nav"></div>}
        title="Recent Contacts Added"
        size="large"
        bodypadding="0px"
      >
        <RecentDealsWrapper>
          <div className="table-bordered recent-deals-table table-responsive max-h-[25rem] overflow-y-auto">
            <Table
              loading={loading}
              columns={columns}
              dataSource={dataSource}
              pagination={false}
              showHeader={false}
              onRow={(record) => {
                return {
                  onClick: () => {
                    navigate(`/crm/contacts/view/${record?.id}`);
                  },
                  style: { cursor: 'pointer' },
                };
              }}
            />
          </div>
        </RecentDealsWrapper>
      </Cards>
    </div>
  );
};

export default RecentContactAdded;
