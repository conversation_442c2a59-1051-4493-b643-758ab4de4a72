import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Table } from 'antd';
import { Cards } from '../../../components/Cards';
import { RecentDealsWrapper } from './style';
import moment from 'moment';
import { getAllCompanies } from '../../../services/crm/companies';
import {
  BankOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
} from '@ant-design/icons';

const RecentCompanyAdded = () => {
  const navigate = useNavigate();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(true);

  const columns = [
    {
      title: '',
      dataIndex: 'id',
      key: 'id',
      render: (_text, record) => {
        const telephone = record?.telephone || 'N/A';
        const country = record?.address?.country || 'N/A';
        const companyName = record?.name || 'N/A';
        return (
          <div className="flex flex-col gap-1 items-start">
            <div className="font-medium text-cyan-600"><BankOutlined className='mr-1' /> {companyName}</div>
            <div className="text-sm font-medium opacity-70 grid grid-cols-2 w-full">
              <div className="line-clamp-1" title={country}>
                <EnvironmentOutlined className="mr-1" />
                {country}
              </div>
              <div className="line-clamp-1" title={telephone}>
                <PhoneOutlined className="mr-1" />
                {telephone}
              </div>
            </div>
          </div>
        );
      },
    },
  ];

  const handleGetComapnies = async () => {
    setLoading(true);
    try {
      const payload = {
        page: 1,
        limit: 10,
      };
      const { data } = await getAllCompanies(payload);
      if (data?.result?.data?.length > 0) {
        const { data: newData } = data?.result;
        setDataSource([...newData]);
      } else {
        setDataSource([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  useEffect(() => {
    handleGetComapnies();
  }, []);

  return (
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/50 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <Cards
        isbutton={<div className="card-nav"></div>}
        title="Recent Companies Added"
        size="large"
        bodypadding="0px"
      >
        <RecentDealsWrapper>
          <div className="table-bordered recent-deals-table table-responsive max-h-[25rem] overflow-y-auto">
            <Table
              loading={loading}
              columns={columns}
              dataSource={dataSource}
              pagination={false}
              showHeader={false}
              onRow={(record) => {
                return {
                  onClick: () => {
                    navigate(`/crm/companies/view/${record?.id}`);
                  },
                  style: { cursor: 'pointer' },
                };
              }}
            />
          </div>
        </RecentDealsWrapper>
      </Cards>
    </div>
  );
};

export default RecentCompanyAdded;
