import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Table } from 'antd';
import { Cards } from '../../../components/Cards';
import { RecentDealsWrapper } from './style';
import moment from 'moment';
import { getAllLeads } from '../../../services/crm/leads';
import { currencyFormatter } from '../../../helpers/util';
import { isNumber } from 'lodash';
import { AuditOutlined } from '@ant-design/icons';

const RecentLeadSynced = () => {
  const navigate = useNavigate();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(true);

  const sellingColumns = [
    {
      title: '',
      dataIndex: 'jobTitle',
      key: 'name',
      render: (jobTitle, record) => (
        <div className="flex flex-col gap-1 items-start">
          <div
            className="font-medium text-cyan-600 line-clamp-1"
            title={jobTitle}
          >
            <AuditOutlined className='mr-1' /> {jobTitle}
          </div>
          <div className="text-sm font-medium opacity-70">
            {moment(record?.createdAt).format('DD-MM-YYYY')}
          </div>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'salary',
      key: 'salary',
      render: (salary, record) => {
        const currency = record?.currencyUnit || 'GBP';
        const displaySalary = currencyFormatter(salary, currency);

        return <div>{displaySalary}</div>;
      },
    },
  ];

  const handleGetLeads = async () => {
    setLoading(true);
    try {
      const payload = {
        page: 1,
        limit: 10,
      };
      const { data } = await getAllLeads(payload);
      if (data?.result?.data?.length > 0) {
        const { data: newData } = data?.result;
        setDataSource([...newData]);
      } else {
        setDataSource([]);
      }
      console.log('data', data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  useEffect(() => {
    handleGetLeads();
  }, []);

  return (
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/50 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <Cards
        isbutton={<div className="card-nav"></div>}
        title="Recent Leads Synced"
        size="large"
        bodypadding="0px"
      >
        <RecentDealsWrapper>
          <div className="table-bordered recent-deals-table table-responsive max-h-[25rem] overflow-y-auto">
            <Table
              loading={loading}
              columns={sellingColumns}
              dataSource={dataSource}
              pagination={false}
              showHeader={false}
              onRow={(record) => {
                return {
                  onClick: () => {
                    navigate(`/crm/leads/view/${record?.id}`);
                  },
                  style: { cursor: 'pointer' },
                };
              }}
            />
          </div>
        </RecentDealsWrapper>
      </Cards>
    </div>
  );
};

export default RecentLeadSynced;
