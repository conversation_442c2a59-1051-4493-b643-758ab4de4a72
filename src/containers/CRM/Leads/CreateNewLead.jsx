import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate } from 'react-router-dom';
import LeadForm from './LeadForm';
import { createNewCRMLead } from '../../../services/crm/leads';
import DetailBar from '../NavMenu/DetailBar';

const CreateNewLead = () => {
  const navigate = useNavigate();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/leads');
    }
  };

  const handleSubmit = async (payload) => {
    try {
      const { data } = await createNewCRMLead(payload);
      notification.success({
        description: 'Lead created successfully',
      });
      back();
    } catch (error) {
      notification.error({
        description: 'Failed to create Lead',
      });
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        <LeadForm handleSubmit={handleSubmit} back={back} />
      </div>
    </div>
  );
};

export default CreateNewLead;
