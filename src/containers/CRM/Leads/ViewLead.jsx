import { Suspense, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getLeadById } from '../../../services/crm/leads';
import { Button, Dropdown, notification, Spin, Tag } from 'antd';
import Loading from '../../HotList/Loading';
import {
  BankOutlined,
  DownloadOutlined,
  EditOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  LeftOutlined,
  MoreOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import CopyToClipboard from 'react-copy-to-clipboard';
import { currencyFormatter } from '../../../helpers/util';
import { v4 as uuid } from 'uuid';
import { fileLogo } from '../../../constants/common.constant';
import handleRenderTime from '../../../function/handleRenderTime';
import { Cards } from '../../../components/Cards';
import Notes from '../../../components/CRM/Notes';
import Emails from '../../../components/CRM/Emails';
import FileListCard from '../../../components/CRM/FileListCard';
import clsx from 'clsx';
import { TaskLists } from '../styled';
import DetailBar from '../NavMenu/DetailBar';
import Sequences from '../../../components/CRM/Sequences';
import { RESOURCE_NAME } from '../crm.constant';
import Files from '../../../components/CRM/Files';

const Tabs = [
  'Files',
  'Notes',
  'Emails',
  'Sequences',
  // 'Contacts',
];

const ViewLead = () => {
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('Files');

  const navigate = useNavigate();
  const { id } = useParams();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/leads');
    }
  };
  const getLeadDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getLeadById(id);
      if (data?.result) {
        const lead = data.result;
        setLead({ ...lead });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLeadDetail();
  }, [id]);

  const TabRenderer = {
    Notes: <Notes resourceName={RESOURCE_NAME.LEAD} resourceId={id} />,
    Emails: <Emails resourceName={RESOURCE_NAME.LEAD} resourceId={id} />,
    Sequences: <Sequences resourceName={RESOURCE_NAME.LEAD} resourceId={id} />,
    // Contacts: (
    //   <div className="project-users-wrapper p-[25px] min-h-[435px]">
    //     Contacts coming soon...
    //   </div>
    // ),
    Files: <Files resourceId={id} resourceName={RESOURCE_NAME.LEAD} />,
  };

  return (
    <div className=" ">
      <DetailBar />
      {!loading && lead && (
        <div className="w-full grid grid-cols-10 gap-3 mt-4">
          <div className="col-span-10 flex flex-col gap-2 bg-white p-5 rounded-md shadow-sm">
            <div className="w-full flex items-center justify-between">
              <div className="text-xl font-semibold">{lead?.jobTitle}</div>
              <div className="flex items-center gap-2">
                <Button
                  icon={<EditOutlined />}
                  onClick={() => window.open(`/crm/leads/edit/${id}`)}
                  type="primary"
                >
                  Edit
                </Button>
                <CopyToClipboard
                  onCopy={() =>
                    notification.success({ description: 'Link copied' })
                  }
                  text={`${window.location.origin}/crm/leads/view/${id}`}
                >
                  <Button icon={<ShareAltOutlined />} type="primary">
                    Share
                  </Button>
                </CopyToClipboard>
              </div>
            </div>
            <div className="flex items-center gap-5">
              {lead?.company && (
                <Tag
                  color="cyan"
                  icon={<BankOutlined />}
                  className="font-semibold"
                >
                  {lead?.company?.name}
                </Tag>
              )}
              <Tag color="cyan" className="font-semibold">
                {lead?.jobType}
              </Tag>
              <Tag color="cyan" className="font-semibold">
                {currencyFormatter(lead?.salary)}
              </Tag>
              {lead?.address && (
                <Tag className="font-semibold flex items-center gap-1">
                  <EnvironmentOutlined />
                  {lead?.address?.country}
                </Tag>
              )}
            </div>
            <div className="whitespace-pre-line h-[24rem] p-4 border rounded-md overflow-y-auto">
              {lead?.description}
            </div>
            <div>
              <div className="font-semibold pb-2">Tags</div>
              <div className="flex items-center gap-2">
                {lead?.tags?.map((tag) => (
                  <Tag key={tag?.id} color="cyan" className="font-semibold">
                    {tag?.name}
                  </Tag>
                ))}
              </div>
            </div>
          </div>
          <div className="col-span-10">
            <Cards
              title={
                <div className="flex items-center gap-5 text-sm font-medium">
                  {Tabs.map((tab) => (
                    <div
                      className={clsx(
                        'pb-2 hover:text-cyan-800 cursor-pointer',
                        selectedTab === tab &&
                          'text-cyan-600 border-b border-b-2 border-cyan-600'
                      )}
                      onClick={() => setSelectedTab(tab)}
                    >
                      {tab}
                    </div>
                  ))}
                </div>
              }
            >
              <Suspense
                fallback={
                  <div className="spin">
                    <Spin />
                  </div>
                }
              >
                {TabRenderer[selectedTab]}
              </Suspense>
            </Cards>
          </div>
        </div>
      )}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <Loading />
        </div>
      )}
      {!loading && !lead && (
        <div className="flex items-center justify-center h-64">
          Lead not found or deleted by another user.
        </div>
      )}
    </div>
  );
};

export default ViewLead;
