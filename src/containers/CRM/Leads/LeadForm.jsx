import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  notification,
  Row,
  Select,
  Space,
} from 'antd';
import { Cards } from '../../../components/Cards';
import { BasicFormWrapper, HorizontalFormStyleWrap } from '../styled';
import { tagRender } from '../../../components/SearchDetailV2/NewSearchFilterComponent';
import { getAllIndustries } from '../../../services/crm/industries';
import { useEffect, useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { getAllCompanies } from '../../../services/crm/companies';
import { getAllSkills } from '../../../services/crm/skills';
import { MODE_CONSTANTS } from '../../../constants/common.constant';
import { getCRMTagsByUser } from '../../../services/crm/tag';
import {
  createNewLeadSheet,
  getLeadSheetsByType,
  LEAD_SHEET_TYPE,
} from '../../../services/leadSheet';

const { Option } = Select;

const LeadForm = ({
  handleSubmit = null,
  formState = null,
  setFormState = null,
  back,
  lead = {},
  mode = MODE_CONSTANTS.CREATE,
}) => {
  const [options, setOptions] = useState({
    industry: [],
    company: [],
    jobType: [
      { key: 'Permanent', value: 'Permanent', label: 'Permanent' },
      { key: 'Contract', value: 'Contract', label: 'Contract' },
      { key: 'Temporary', value: 'Temporary', label: 'Temporary' },
      { key: 'Fixed Term', value: 'Fixed Term', label: 'Fixed Term' },
    ],
    salaryRate: [
      { key: 'Hourly', value: 'Hourly', label: 'Hourly' },
      { key: 'Monthly', value: 'Monthly', label: 'Monthly' },
      { key: 'Yearly', value: 'Yearly', label: 'Yearly' },
    ],
    skill: [],
    tag: [],
  });
  const [loading, setLoading] = useState(false);

  // Lead Sheet Area
  const [leadSheets, setLeadSheets] = useState([]);
  const [newLeadSheetName, setNewLeadSheetName] = useState('');
  const [leadSheetLoading, setLeadSheetLoading] = useState(true);
  const inputLeadSheetRef = useRef(null);

  const onNewLeadSheetNameChange = (event) => {
    setNewLeadSheetName(event.target.value);
  };

  const addNewLeadSheet = async (e) => {
    if (!newLeadSheetName?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }
    e.preventDefault();
    try {
      const payload = {
        name: `LEAD: ${newLeadSheetName}`,
        leadStatusType: LEAD_SHEET_TYPE.LEAD,
      };
      setLeadSheetLoading(true);
      const { data } = await createNewLeadSheet(payload);

      if (data?.result) {
        setLeadSheets([{ ...data?.result }, ...leadSheets]);
        setNewLeadSheetName('');
      }

      setTimeout(() => {
        inputLeadSheetRef.current?.focus();
      }, 0);
      setLeadSheetLoading(false);
    } catch (error) {
      notification.error({
        description:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadSheet: ', error);
      setLeadSheetLoading(false);
    }
  };

  const onChangeLeadSheet = (value) => {
    setFormState('leadSheetId', value);
  };

  const getLeadSheetOptions = async () => {
    try {
      const { data } = await getLeadSheetsByType(LEAD_SHEET_TYPE.LEAD);
      if (data?.result?.length > 0) {
        setLeadSheets([...data?.result]);
      }
      setLeadSheetLoading(false);
    } catch (error) {
      console.log('Error in getLeadSheetOptions: ', error);
      setLeadSheetLoading(false);
    }
  };

  useEffect(() => {
    getLeadSheetOptions();
  }, []);

  // End Lead Sheet Area

  const [form] = Form.useForm();

  const onFinish = async (values) => {
    const {
      jobTitle,
      companyId,
      description,
      website,
      address,
      jobType,
      salary,
      industries,
      skills,
      rate,
      tags,
      currencyUnit,
    } = values;
    setLoading(true);
    const payload = {
      jobTitle,
      companyId,
      description,
      website,
      address,
      jobType,
      salary,
      industries,
      skills,
      tags,
      currencyUnit,
    };

    await handleSubmit(payload);
  };

  const handleCancel = () => {
    back();
  };

  const getIndustryOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllIndustries();

      if (data?.result?.length > 0) {
        result = data.result.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getTagOptions = async () => {
    let result = [];
    try {
      const { data } = await getCRMTagsByUser();
      if (data?.result?.length > 0) {
        result = data?.result?.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));
      }
      return result;
    } catch (error) {
      console.log('error: ', error);
      return result;
    }
  };

  const getCompanyOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllCompanies({
        keyword: '',
        page: 1,
        limit: 100,
      });

      if (data?.result?.data?.length > 0) {
        result = data?.result?.data?.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getSkillOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllSkills();

      if (data?.result?.length > 0) {
        result = data?.result?.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getData = async () => {
    try {
      setLoading(true);
      const [industry, company, skill, tag] = await Promise.all([
        getIndustryOptions(),
        getCompanyOptions(),
        getSkillOptions(),
        getTagOptions(),
      ]);

      setOptions({
        ...options,
        industry,
        company,
        skill,
        tag,
      });
      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (!lead) return;
    const { industries, company, skills, tags } = lead;
    const newIndustries =
      industries?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];
    const newSkills =
      skills?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];

    const newTags =
      tags?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];

    console.log('formState: ', formState);
    form.setFieldsValue({
      ...(formState || lead || {}),
      industries: newIndustries,
      companyId: company?.id || formState?.companyId,
      skills: newSkills,
      tags: newTags,
    });
  }, [lead]);

  return (
    <>
      <BasicFormWrapper>
        <HorizontalFormStyleWrap>
          <Cards
            headless
            title="Lead"
            caption={'Please fill your Lead details'}
          >
            <div className="w-full pb-5 border-b" />
            <Form
              onValuesChange={(_changedValues, allValues) => {
                if (setFormState) {
                  Object.entries(allValues).forEach(([key, value]) => {
                    setFormState(key, value);
                  });
                }
              }}
              form={form}
              onFinish={onFinish}
              name="horizontal-icno-from"
              layout="horizontal"
              className="mt-4 flex flex-col gap-4"
            >
              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="jobTitle">
                    <div className="flex flex-col">
                      <span>Lead Name</span>
                      {/* <span className="text-xs opacity-60">
                        Enter the official name of the Lead (Job Title).
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item
                    name={'jobTitle'}
                    rules={[
                      { required: true, message: 'Job Title is required' },
                    ]}
                  >
                    <Input placeholder="Job Title" />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="description">
                    <div className="flex flex-col">
                      <span>Description</span>
                      {/* <span className="text-xs opacity-60">
                        Provide a brief overview of the job
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item name={'description'}>
                    <Input.TextArea rows={6} placeholder="Job Description" />
                  </Form.Item>
                </Col>
              </Row>

              {mode === MODE_CONSTANTS.SYNC && (
                <Row align="middle" className="pb-4">
                  <Col lg={24} md={24} xs={24}>
                    <label htmlFor="jobTitle">
                      <div className="flex flex-col">
                        <span>Lead Sheet</span>
                      </div>
                    </label>
                  </Col>
                  <Col lg={24} md={24} xs={24}>
                    <Form.Item
                      name={'leadSheetId'}
                      rules={[
                        { required: true, message: 'Lead Sheet is required' },
                      ]}
                    >
                      <Select
                        defaultValue={{
                          label: leadSheets?.find(
                            (item) => item?.id === formState?.leadSheetId
                          )?.name,
                          value: formState?.leadSheetId,
                        }}
                        loading={leadSheetLoading}
                        placeholder="Select a Lead Sheet"
                        dropdownRender={(menu) => (
                          <div className="w-full">
                            <div className="grid grid-cols-10 gap-4 py-2 px-3">
                              <Input
                                addonBefore={'LEAD :'}
                                className="col-span-7"
                                placeholder="Please enter sheet name"
                                ref={inputLeadSheetRef}
                                value={newLeadSheetName}
                                onChange={onNewLeadSheetNameChange}
                                onKeyDown={(e) => e.stopPropagation()}
                              />
                              <Button
                                loading={leadSheetLoading}
                                className="col-span-3 font-medium"
                                type="default"
                                icon={<PlusOutlined />}
                                onClick={addNewLeadSheet}
                              >
                                Add New Sheet
                              </Button>
                            </div>
                            <Divider
                              style={{
                                margin: '8px 0',
                              }}
                            />
                            {menu}
                          </div>
                        )}
                        options={leadSheets.map((item) => ({
                          label: item?.name,
                          value: item?.id,
                        }))}
                        onChange={onChangeLeadSheet}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="companyId">
                    <div className="flex flex-col">
                      <span>Company</span>
                      {/* <span className="text-xs opacity-60">
                        Select the Company the Lead is belonged to.
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item
                    name="companyId"
                    rules={[{ required: true, message: 'Company is required' }]}
                  >
                    <Select
                      loading={loading}
                      disabled={loading}
                      options={options.company}
                      optionFilterProp="label"
                      showSearch
                      placeholder="Select the company"
                      dropdownRender={(menu) => (
                        <>
                          {menu}
                          <Divider style={{ margin: '8px 0' }} />
                          <Space
                            style={{ padding: '0 8px 4px' }}
                            className="flex items-center justify-center"
                          >
                            you don't see the Company you are looking for?
                            &nbsp;
                            <Button
                              type="primary"
                              className="w-full"
                              icon={<PlusOutlined />}
                              onClick={() =>
                                window.open('/crm/companies/create', '_blank')
                              }
                            >
                              Add new Company
                            </Button>
                          </Space>
                        </>
                      )}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="jobType">
                    <div className="flex flex-col">
                      <span>Job Type</span>
                      {/* <span className="text-xs opacity-60">
                        Specify the employment type (Permanent, Contact,
                        Temporary, Fixed Term).
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item
                    name="jobType"
                    rules={[
                      { required: true, message: 'Job Type is required' },
                    ]}
                  >
                    <Select
                      options={options.jobType}
                      optionFilterProp="label"
                      showSearch
                      placeholder="Select the Job Type"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="salary">
                    <div className="flex flex-col">
                      <span>Salary</span>
                      {/* <span className="text-xs opacity-60">
                        Enter the compensation details, including salary range
                        or hourly rate.
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <div className="grid grid-cols-5 gap-3">
                    <Form.Item
                      className="col-span-3"
                      name="salary"
                      rules={[
                        { required: true, message: 'Salary is required' },
                      ]}
                    >
                      <Input placeholder="Set the Salary" />
                    </Form.Item>
                    <Form.Item name="currencyUnit">
                      <Select
                        options={[
                          { label: 'Dollar (USD - $)', value: 'USD' },
                          { label: 'Pound (GBP - £)', value: 'GBP' },
                          // { label: 'Euro (EUR)', value: 'EUR' },
                          // { label: 'Japanese Yen (JPY)', value: 'JPY' },
                          // { label: 'Australian Dollar (AUD)', value: 'AUD' },
                          // { label: 'Canadian Dollar (CAD)', value: 'CAD' },
                          // { label: 'Swiss Franc (CHF)', value: 'CHF' },
                          // { label: 'Chinese Yuan (CNY)', value: 'CNY' },
                          // { label: 'Indian Rupee (INR)', value: 'INR' },
                          // { label: 'Brazilian Real (BRL)', value: 'BRL' },
                          // { label: 'South African Rand (ZAR)', value: 'ZAR' },
                          // { label: 'Russian Ruble (RUB)', value: 'RUB' },
                          // { label: 'Singapore Dollar (SGD)', value: 'SGD' },
                          // { label: 'New Zealand Dollar (NZD)', value: 'NZD' },
                          // { label: 'Mexican Peso (MXN)', value: 'MXN' },
                        ]}
                        optionFilterProp="label"
                        showSearch
                        placeholder="Select the Salary Unit"
                      />
                    </Form.Item>
                    <Form.Item
                      name="rate"
                      // rules={[
                      //   { required: true, message: 'Salary Rate is required' },
                      // ]}
                    >
                      <Select
                        options={options.salaryRate}
                        optionFilterProp="label"
                        showSearch
                        placeholder="Select the Salary Rate"
                      />
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="industries">
                    <div className="flex flex-col">
                      <span>Industry</span>
                      {/* <span className="text-xs opacity-60">
                        Specify the sector or field the Lead operates in.
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item name="industries">
                    <Select
                      loading={loading}
                      disabled={loading}
                      options={options.industry}
                      optionFilterProp="label"
                      showSearch
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-purple' })
                      }
                      mode="multiple"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Add Lead industries..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="address">
                    <div className="flex flex-col">
                      <span>Address</span>
                      {/* <span className="text-xs opacity-60">
                        Input the physical or mailing address of the Lead.
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <div className="grid grid-cols-9 gap-3">
                    <Form.Item
                      name={['address', 'address1']}
                      className="col-span-6 !mb-0"
                    >
                      <Input placeholder="Main address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'address2']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Secondary address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'city']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="City" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'county']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="County" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'postCode']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Post Code" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'country']}
                      className="col-span-9 !mb-0"
                    >
                      <Input placeholder="Country" />
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              <Row align="middle" className="pb-4">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="skills">
                    <div className="flex flex-col">
                      <span>Skills</span>
                      {/* <span className="text-xs opacity-60">
                        List the key skills required for the role.
                      </span> */}
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item name="skills">
                    <Select
                      loading={loading}
                      disabled={loading}
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-blue' })
                      }
                      optionFilterProp="label"
                      options={options.skill}
                      //   size="large"
                      mode="multiple"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Add your skills..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={24} md={24} xs={24}>
                  <label htmlFor="tags">
                    <div className="flex flex-col">
                      <span>Tags</span>
                    </div>
                  </label>
                </Col>
                <Col lg={24} md={24} xs={24}>
                  <Form.Item name="tags">
                    <Select
                      loading={loading}
                      disabled={loading}
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-cyan' })
                      }
                      optionFilterProp="label"
                      options={options.tag}
                      //   size="large"
                      mode="multiple"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Add your tags..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              {handleSubmit && (
                <Row>
                  <Col
                    lg={{ span: 16, offset: 8 }}
                    md={{ span: 15, offset: 9 }}
                    xs={{ span: 24, offset: 0 }}
                  >
                    <div className="sDash_form-action flex justify-end">
                      <Button
                        onClick={handleCancel}
                        className="btn-signin"
                        type="dashed"
                        size="large"
                      >
                        Cancel
                      </Button>
                      <Button
                        htmlType="submit"
                        className="btn-signin"
                        type="primary"
                        size="large"
                        loading={loading}
                      >
                        Save
                      </Button>
                    </div>
                  </Col>
                </Row>
              )}
            </Form>
          </Cards>
        </HorizontalFormStyleWrap>
      </BasicFormWrapper>
    </>
  );
};

export default LeadForm;
