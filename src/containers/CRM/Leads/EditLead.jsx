import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Loading from '../../HotList/Loading';
import { getLeadById, updateLeadById } from '../../../services/crm/leads';
import LeadForm from './LeadForm';
import { MODE_CONSTANTS } from '../../../constants/common.constant';
import DetailBar from '../NavMenu/DetailBar';

const EditLead = () => {
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(true);

  const navigate = useNavigate();
  const { id } = useParams();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/leads');
    }
  };

  const handleSavingLead = async (payload) => {
    try {
      const { data } = await updateLeadById({ ...payload, id });
      notification.success({
        description: 'Lead updated successfully',
      });
      back();
    } catch (error) {
      console.log(error);
      notification.error({
        description: 'Failed to update Lead',
      });
    }
  };

  const getLeadDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getLeadById(id);
      if (data?.result) {
        const lead = data.result;
        setLead({ ...lead });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to fetch Lead details',
      });
    }
  };

  useEffect(() => {
    getLeadDetail();
  }, [id]);

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        {!loading && lead && (
          <LeadForm
            lead={lead}
            handleSubmit={handleSavingLead}
            back={back}
            mode={MODE_CONSTANTS.EDIT}
          />
        )}
        {!loading && !lead && (
          <div className="flex justify-center items-center h-64">
            <div>Lead not found or deleted by another user.</div>
          </div>
        )}
        {loading && (
          <div className="flex justify-center items-center h-64">
            <Loading />
          </div>
        )}
      </div>
    </div>
  );
};

export default EditLead;
