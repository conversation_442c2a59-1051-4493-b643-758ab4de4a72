import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import CompanyForm from './CompanyForm';
import { useEffect, useState } from 'react';
import {
  getCompanyById,
  updateCompanyById,
} from '../../../services/crm/companies';
import Loading from '../../HotList/Loading';
import { MODE_CONSTANTS } from '../../../constants/common.constant';
import DetailBar from '../NavMenu/DetailBar';

const EditCompany = () => {
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/companies');
    }
  };

  const handleSavingCompany = async (payload) => {
    try {
      const { data } = await updateCompanyById({ ...payload, id });
      notification.success({
        description: 'Company updated successfully',
      });
      back();
    } catch (error) {
      notification.error({
        description: 'Failed to update company',
      });
    }
  };

  const getCompanyDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getCompanyById(id);
      if (data?.result) {
        const company = data.result;
        setCompany({ ...company });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to fetch company details',
      });
    }
  };

  useEffect(() => {
    getCompanyDetail();
  }, [id]);

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        {!loading && company && (
          <CompanyForm
            company={company}
            handleSubmitCompany={handleSavingCompany}
            back={back}
            mode={MODE_CONSTANTS.EDIT}
          />
        )}
        {!loading && !company && (
          <div className="flex justify-center items-center h-64">
            <div>Company not found or deleted by another user.</div>
          </div>
        )}
        {loading && (
          <div className="flex justify-center items-center h-64">
            <Loading />
          </div>
        )}
      </div>
    </div>
  );
};

export default EditCompany;
