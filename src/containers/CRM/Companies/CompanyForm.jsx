import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  notification,
  Row,
  Select,
} from 'antd';
import { Cards } from '../../../components/Cards';
import BaseForm from '../../../components/CRM/BaseForm';
import { BasicFormWrapper, HorizontalFormStyleWrap } from '../styled';
import { tagRender } from '../../../components/SearchDetailV2/NewSearchFilterComponent';
import { countries } from 'country-list-json';
import { getAllIndustries } from '../../../services/crm/industries';
import { useEffect, useState } from 'react';
import { MODE_CONSTANTS } from '../../../constants/common.constant';

const { Option } = Select;

const CompanyForm = ({
  handleSubmitCompany,
  back,
  company = null,
  mode = MODE_CONSTANTS.CREATE,
}) => {
  const [options, setOptions] = useState({
    industry: [],
  });
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const onFinish = async (values) => {
    console.log(values);
    setLoading(true);
    const payload = { ...values };
    await handleSubmitCompany(payload);
  };

  const handleCancel = () => {
    back();
  };

  const selectBefore = (
    <Select defaultValue="http://">
      <Option value="http://">http://</Option>
      <Option value="https://">https://</Option>
    </Select>
  );

  const selectDialCode = (
    <Select showSearch defaultValue={'+44'}>
      {[...new Set(countries.map((item) => item.dial_code))].map(
        (dial_code) => (
          <Option value={dial_code}>{dial_code}</Option>
        )
      )}
    </Select>
  );

  const getIndustryOptions = async () => {
    let result = [];
    try {
      const { data } = await getAllIndustries();

      if (data?.result?.length > 0) {
        result = data.result.map((item) => ({
          key: item?.id,
          value: item?.id,
          label: item?.name,
        }));
      }
      return result;
    } catch (error) {
      return result;
    }
  };

  const getData = async () => {
    const [industry] = await Promise.all([getIndustryOptions()]);

    setOptions({
      industry,
    });
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (!company) return;
    console.log(company);
    const { industries } = company;
    const newIndustries =
      industries?.map((item) => ({
        key: item?.id,
        value: item?.id,
        label: item?.name,
      })) || [];
    form.setFieldsValue({
      ...company,
      industries: newIndustries,
    });
  }, [company]);

  return (
    <>
      <BasicFormWrapper>
        <HorizontalFormStyleWrap>
          <Cards
            headless
            title="Company"
            caption={'Please fill your company details'}
          >
            <div className="w-full pb-10 border-b" />
            <Form
              form={form}
              onFinish={onFinish}
              name="horizontal-icno-from"
              layout="horizontal"
              className="mt-4 flex flex-col gap-4"
            >
              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="name">
                    <div className="flex flex-col">
                      <span>Company Name</span>
                      <span className="text-xs opacity-60">
                        Enter the official name of the company.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="name" rules={[{ required: true }]}>
                    <Input placeholder="Set the company name..." />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="industries">
                    <div className="flex flex-col">
                      <span>Industry</span>
                      <span className="text-xs opacity-60">
                        Specify the sector or field the company operates in.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="industries">
                    <Select
                      // defaultValue={form.getFieldValue('industries')}
                      options={options.industry}
                      optionFilterProp="label"
                      showSearch
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-purple' })
                      }
                      mode="multiple"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Add company industries..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="description">
                    <div className="flex flex-col">
                      <span>Company Description</span>
                      <span className="text-xs opacity-60">
                        Provide a brief overview of the company's mission,
                        services, or products.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="description">
                    <Input.TextArea
                      rows={8}
                      placeholder="Enter company description..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="website">
                    <div className="flex flex-col">
                      <span>Website</span>
                      <span className="text-xs opacity-60">
                        Enter the company's official website URL.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="website">
                    <Input
                      // addonBefore={selectBefore}
                      placeholder="Set the company website..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="telephone">
                    <div className="flex flex-col">
                      <span>Telephone</span>
                      <span className="text-xs opacity-60">
                        Add the primary contact number for the company.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <Form.Item name="telephone">
                    <Input
                      addonBefore={selectDialCode}
                      placeholder="Set the company phone number..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row align="middle" className="pb-4 border-b">
                <Col lg={8} md={9} xs={24}>
                  <label htmlFor="address">
                    <div className="flex flex-col">
                      <span>Address</span>
                      <span className="text-xs opacity-60">
                        Input the physical or mailing address of the company.
                      </span>
                    </div>
                  </label>
                </Col>
                <Col lg={16} md={15} xs={24}>
                  <div className="grid grid-cols-9 gap-3">
                    <Form.Item
                      name={['address', 'address1']}
                      className="col-span-6 !mb-0"
                    >
                      <Input placeholder="Main address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'address2']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Secondary address" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'city']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="City" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'county']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="County" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'postCode']}
                      className="col-span-3 !mb-0"
                    >
                      <Input placeholder="Post Code" />
                    </Form.Item>
                    <Form.Item
                      name={['address', 'country']}
                      className="col-span-9 !mb-0"
                    >
                      <Input placeholder="Country" />
                    </Form.Item>
                  </div>
                </Col>
              </Row>

              <BaseForm mode={mode} />
              <Row>
                <Col
                  lg={{ span: 16, offset: 8 }}
                  md={{ span: 15, offset: 9 }}
                  xs={{ span: 24, offset: 0 }}
                >
                  <div className="sDash_form-action flex justify-end">
                    <Button
                      onClick={handleCancel}
                      className="btn-signin"
                      type="dashed"
                      size="large"
                    >
                      Cancel
                    </Button>
                    <Button
                      htmlType="submit"
                      className="btn-signin"
                      type="primary"
                      size="large"
                      loading={loading}
                    >
                      Save
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          </Cards>
        </HorizontalFormStyleWrap>
      </BasicFormWrapper>
    </>
  );
};

export default CompanyForm;
