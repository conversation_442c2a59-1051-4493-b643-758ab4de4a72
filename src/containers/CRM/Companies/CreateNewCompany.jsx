import { LeftOutlined } from '@ant-design/icons';
import { Button, notification } from 'antd';
import { useNavigate } from 'react-router-dom';
import CompanyForm from './CompanyForm';
import { createNewCompany } from '../../../services/crm/companies';
import DetailBar from '../NavMenu/DetailBar';

const CreateNewCompany = () => {
  const navigate = useNavigate();

  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm/companies');
    }
  };

  const handleSubmitCompany = async (payload) => {
    try {
      const { data } = await createNewCompany(payload);
      notification.success({
        description: 'Company created successfully',
      });
      back();
    } catch (error) {
      notification.error({
        description: 'Failed to create company',
      });
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <DetailBar />
      <div className="w-full">
        <CompanyForm handleSubmitCompany={handleSubmitCompany} back={back} />
      </div>
    </div>
  );
};

export default CreateNewCompany;
