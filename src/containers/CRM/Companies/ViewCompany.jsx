import { Link, NavLink, Route, useNavigate, useParams } from 'react-router-dom';
import {
  ActivitiesWrapper,
  ContactPageheaderStyle,
  Main,
  ProjectDetailsWrapper,
  TaskLists,
} from '../styled';
import Heading from '../../../components/Heading';
import { Button, Col, notification, Row, Spin, Switch, Table, Tag } from 'antd';
import { Cards } from '../../../components/Cards';
import { lazy, Suspense, useEffect, useState } from 'react';
import {
  BankOutlined,
  EditOutlined,
  EnvironmentOutlined,
  ExportOutlined,
  FileDoneOutlined,
  FileSearchOutlined,
  LeftSquareOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusCircleOutlined,
  PoundCircleOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import clsx from 'clsx';
import { getCompanyById } from '../../../services/crm/companies';
import Loading from '../../HotList/Loading';
import FileListCard from '../../../components/CRM/FileListCard';
import { getAllContacts } from '../../../services/crm/contacts';
import InforNavMenu from '../../../components/CRM/InforNavMenu';
import DetailBar from '../NavMenu/DetailBar';
import { getCRMCompanyStats } from '../../../services/crm/stats';
import ConsultantRow from '../Contacts/ConsultantRow';
import Files from '../../../components/CRM/Files';
import { RESOURCE_NAME } from '../crm.constant';

const Activities = lazy(() => import('../../../components/CRM/Activities'));
const Leads = lazy(() => import('../../../components/CRM/Leads'));
const Notes = lazy(() => import('../../../components/CRM/Notes'));
const Emails = lazy(() => import('../../../components/CRM/Emails'));
const Sequences = lazy(() => import('../../../components/CRM/Sequences'));
const Tabs = [
  'Leads',
  // 'Activities',
  'Notes',
  'Emails',
  'Sequences',
  'Contacts',
  'Files',
];

const contactColumns = [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    render: (_name, item) => {
      return (
        <div className="flex items-center gap-1 font-semibold text-cyan-600 w-full hover:underline cursor-pointer">
          <UserOutlined />
          {`${item?.firstName} ${item?.middleName} ${item?.surName}`}
        </div>
      );
    },
  },
  {
    title: 'Job Title',
    dataIndex: 'jobTitle',
    key: 'jobTitle',
  },
  {
    title: 'Work Email',
    dataIndex: 'email',
    key: 'email',
    render: (email, record) => {
      return (
        <Tag color="cyan" icon={<MailOutlined />} className="w-fit">
          {email}
        </Tag>
      );
    },
  },
  {
    title: 'Work Phone',
    dataIndex: 'telephone',
    key: 'telephone',
    render: (telephone, record) => {
      return (
        <Tag color="cyan" icon={<PhoneOutlined />} className="w-fit">
          {telephone}
        </Tag>
      );
    },
  },
  {
    title: 'Company',
    dataIndex: 'company',
    key: 'company',
    render: (company, record) => {
      return (
        <Tag color="cyan" icon={<BankOutlined />} className="w-fit">
          {company?.name}
        </Tag>
      );
    },
  },
  {
    title: 'Consultant',
    dataIndex: 'consultantId',
    key: 'consultantId',
    render: (consultantId, record) => {
      return consultantId && <ConsultantRow consultantId={consultantId} />;
    },
  },
  {
    title: 'Address',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: 'Last Note',
    notes: 'notes',
    key: 'notes',
  },
  {
    title: 'Date Added',
    notes: 'addedAt',
    key: 'addedAt',
  },
];

const ViewCompany = () => {
  const initialNavMenuItems = [
    {
      label: '',
      key: 'Leads',
      description: 'Leads',
      to: '#',
    },
    {
      label: '',
      key: 'Notes',
      description: 'Notes',
      to: '#',
    },
    {
      label: '',
      key: 'Emails',
      description: 'Emails',
      to: '#',
    },
    {
      label: '',
      key: 'Sequences',
      description: 'Sequences',
      to: '#',
    },
    {
      label: '',
      key: 'Contacts',
      description: 'Contacts',
      to: '#',
    },
    {
      label: '',
      key: 'Files',
      description: 'Files',
      to: '#',
    },
  ];
  const navigate = useNavigate();
  const [contacts, setContacts] = useState([]);
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('Leads');
  const [navMenuItems, setNavMenuItems] = useState(initialNavMenuItems);
  const { id } = useParams();

  const getCompanyDetail = async () => {
    setLoading(true);
    try {
      const { data } = await getCompanyById(id);
      if (data?.result) {
        const company = data.result;
        setItem({ ...company });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to fetch company details',
      });
    }
  };

  const getContactsInCompany = async () => {
    try {
      const { data } = await getAllContacts({
        companyIds: [id],
        limit: 10,
        page: 1,
      });
      if (data?.result?.data?.length > 0) {
        setContacts([...data?.result?.data]);
      }
      console.log('getContactsInCompany: ', data);
    } catch (error) {
      console.log(error);
    }
  };

  const getCompanyStats = async () => {
    try {
      const { data } = await getCRMCompanyStats(id);
      if (data?.result) {
        const newNavMenuItems = [...navMenuItems];
        newNavMenuItems[0].label = data?.result?.totalLeads || 0;
        newNavMenuItems[1].label = data?.result?.totalNotes || 0;
        newNavMenuItems[2].label = data?.result?.totalEmails || 0;
        newNavMenuItems[3].label = data?.result?.totalSequence || 0;
        newNavMenuItems[4].label = data?.result?.totalContacts || 0;
        newNavMenuItems[5].label = data?.result?.totalFile || 0;
        setNavMenuItems(newNavMenuItems);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCompanyDetail();
    getContactsInCompany();
    getCompanyStats();
  }, [id]);

  const TabRenderer = {
    Leads: <Leads companyId={id} />,
    // Activities: <Activities />,
    Notes: <Notes resourceId={id} resourceName={RESOURCE_NAME.COMPANY} />,
    Emails: <Emails resourceId={id} resourceName={RESOURCE_NAME.COMPANY} />,
    Sequences: (
      <Sequences resourceId={id} resourceName={RESOURCE_NAME.COMPANY} />
    ),
    Contacts: (
      <ActivitiesWrapper>
        <div className="project-users">
          <div className="flex w-full justify-end">
            <div
              onClick={() => window.open(`/crm/contacts?ci=${id}`, '_blank')}
              className="flex items-center gap-2 font-semibold text-cyan-600 w-fit hover:underline cursor-pointer"
            >
              View all contacts
              <ExportOutlined />
            </div>
          </div>
          <div className="search-table-new-design-container">
            <Table
              size="small"
              dataSource={contacts}
              columns={contactColumns}
              onRow={(record, rowIndex) => {
                return {
                  onClick: () => {
                    navigate(`/crm/contacts/view/${record?.id}`);
                  },
                  style: { cursor: 'pointer' },
                };
              }}
            />
          </div>
        </div>
      </ActivitiesWrapper>
    ),
    Files: <Files resourceId={id} resourceName={RESOURCE_NAME.COMPANY} />,
  };

  return (
    <>
      <DetailBar />
      {!loading && item && (
        <ProjectDetailsWrapper>
          <div className="flex items-center justify-between sticky py-3">
            <div key="1" className="project-header">
              <Heading as="h2">{item?.name}</Heading>
              <Button
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={() => window.open(`/crm/leads/create`, '_blank')}
              >
                Add New Lead
              </Button>
            </div>
            <div>
              <InforNavMenu
                items={navMenuItems}
                setSelectedItemKey={setSelectedTab}
              />
            </div>
            <div key="1" className="project-action">
              <Link
                key={1}
                to={`/crm/companies/edit/${id}`}
                className="project-edit"
              >
                <EditOutlined />
                Edit
              </Link>
            </div>
          </div>
          <Main>
            <Row gutter={25}>
              <Col xxl={24} xl={24} xs={24}>
                <div className="about-project-wrapper">
                  <Cards title="Company Overview">
                    <div className="w-full h-full flex flex-col gap-5">
                      <div className="about-content">
                        <p>{item?.description}</p>
                      </div>
                      <div className="about-project w-full h-full flex gap-5">
                        <div className="flex flex-col items-start justify-start">
                          <span>Website</span>
                          <a href={`https://${item?.website}`} target="_blank">
                            {item?.website || 'N/A'}
                          </a>
                        </div>
                        <div>
                          <span>Telephone</span>
                          <a href={`tel:${item?.telephone}`}>
                            {item?.telephone || 'N/A'}
                          </a>
                        </div>
                        <div>
                          <span>Address</span>
                          <span>
                            {`${item?.address?.address1 || item?.address?.address2 || 'N/A'}, ${item?.address?.city || 'N/A'}, ${item?.address?.county || 'N/A'}, ${item?.address?.country || 'N/A'}`}
                          </span>
                        </div>
                      </div>
                      <div className="about-project">
                        <div>
                          <span>Industries</span>
                          <div className="flex items-center gap-2">
                            {item?.industries?.length > 0 &&
                              item?.industries?.map((industry) => (
                                <Tag
                                  key={industry?.id}
                                  color="cyan"
                                  className="w-fit"
                                >
                                  {industry?.name}
                                </Tag>
                              ))}
                          </div>
                        </div>
                      </div>
                      <div className="about-project">
                        <div>
                          <span>Tags</span>
                          <div className="flex items-center gap-2">
                            {item?.tags?.length > 0 &&
                              item?.tags?.map((tag) => (
                                <Tag
                                  key={tag?.id}
                                  color="cyan"
                                  className="w-fit"
                                >
                                  {tag?.name}
                                </Tag>
                              ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Cards>
                </div>
              </Col>
              <Col xxl={24} lg={24} xs={24}>
                <TaskLists>
                  <Cards
                    title={
                      <div className="flex items-center gap-5 text-sm font-medium">
                        {Tabs.map((tab) => (
                          <div
                            className={clsx(
                              'pb-2 hover:text-cyan-800 cursor-pointer',
                              selectedTab === tab &&
                                'text-cyan-600 border-b border-b-2 border-cyan-600'
                            )}
                            onClick={() => setSelectedTab(tab)}
                          >
                            {tab}
                          </div>
                        ))}
                      </div>
                    }
                  >
                    <Suspense
                      fallback={
                        <div className="spin">
                          <Spin />
                        </div>
                      }
                    >
                      {TabRenderer[selectedTab]}
                    </Suspense>
                  </Cards>
                </TaskLists>
              </Col>
            </Row>
          </Main>
        </ProjectDetailsWrapper>
      )}
      {loading && (
        <div className="flex justify-center items-center h-64">
          <Loading />
        </div>
      )}
      {!loading && !item && (
        <div className="flex justify-center items-center h-64">
          Company not found or deleted by another user.
        </div>
      )}
    </>
  );
};

export default ViewCompany;
