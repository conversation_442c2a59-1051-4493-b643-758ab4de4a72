import {
  Button,
  Col,
  Dropdown,
  Form,
  Input,
  notification,
  Popconfirm,
  Row,
  Space,
  Table,
  Tooltip,
} from 'antd';
import NavMenu from '../NavMenu';
import {
  CardToolbox,
  ContactPageheaderStyle,
  Main,
  TableWrapper,
  UserTableStyleWrapper,
} from '../styled';
import {
  CheckOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  MoreOutlined,
  PlusOutlined,
  StopOutlined,
  TagOutlined,
} from '@ant-design/icons';
import Search from 'antd/es/input/Search';
import { Cards } from '../../../components/Cards';
import { useEffect, useState } from 'react';
import { initialPagination } from '../Leads';
import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import {
  createTag,
  deleteTag,
  editTag,
  getCRMTagsByOrg,
  getCRMTagsByUser,
} from '../../../services/crm/tag';
import { useViewAs } from '../../../store/viewAs';
import { useAuth } from '../../../store/auth';

const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const inputNode =
    dataIndex === 'description' ? <Input.TextArea rows={2} /> : <Input />;
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{
            margin: 0,
          }}
          rules={[
            {
              required: title === 'Title' ? true : false,
              message: `Please Input ${title}!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const CRMTagsPage = ({ isInSettings = false }) => {
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const userToSet = profileUser || profileUserAuth?.user;
  const organization = userToSet?.organization || null;
  const organizationId = userToSet?.organization?.id || '';

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState(initialPagination);
  const [dataSource, setDataSource] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');

  const getData = async () => {
    setLoading(true);
    try {
      // const { data } = await getCRMTagsByOrg(organizationId);
      const { data } = await getCRMTagsByUser();
      if (data?.result?.length > 0) {
        const newData = data?.result?.map((item) => ({
          ...item,
          key: item?.id,
        }));
        setDataSource([...newData]);
      } else {
        setDataSource([]);
      }
      setLoading(false);
      console.log('data: ', data);
    } catch (error) {
      setLoading(false);
      console.log('error: ', error);
    }
  };

  const isEditing = (record) => record?.key === editingKey;

  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      setActionLoading(true);
      const newData = [...dataSource];

      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        let newItem = {
          ...item,
          ...row,
        };
        if (item?.id) {
          // update tag
          const { data } = await editTag({ ...newItem });
        } else {
          const { data } = await createTag({
            ...newItem,
            organization: organizationId,
          });
          if (data?.result) {
            newItem = data?.result;
          }
          // create new tag
        }
        newData.splice(index, 1, newItem);
        setDataSource(newData);
        setEditingKey('');
        // getData();
      } else {
        newData.push(row);
        setDataSource(newData);
        setEditingKey('');
      }
      setActionLoading(false);
      form.resetFields();
      notification.success({
        message: 'Tag saved successfully!',
      });
    } catch (errInfo) {
      setActionLoading(false);
      console.log('Validate Failed:', errInfo);
      const message =
        errInfo?.response?.data?.message ||
        'Something went wrong! Please try again later.';
      notification.error({
        message,
      });
    }
  };

  const cancelAdd = () => {
    const newDataSource = [...dataSource];
    newDataSource.shift();
    setDataSource([...newDataSource]);
    setEditingKey('');
  };

  const handleAdd = () => {
    const id = uuid();
    const newData = {
      key: id,
      title: '',
      description: '',
      createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
    };
    setDataSource([newData, ...dataSource]);
    setEditingKey(newData?.key);
  };

  const handleDelete = async (id) => {
    try {
      const { data } = await deleteTag(id);

      const newDataSource = dataSource.filter((item) => item?.id !== id);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        message: 'Something went wrong!',
      });
    }
  };

  const tagColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
      render: (id) => {
        const formattedId = `${id?.slice(0, 5)}...${id?.slice(-5)}`;
        return id ? (
          <Tooltip
            rootClassName="min-w-[17.5rem]"
            title={<div className="min-w-[17.5rem]">{id}</div>}
          >
            <div className="font-semibold w-full flex justify-start items-center gap-1">
              {formattedId}
            </div>
          </Tooltip>
        ) : (
          <div className="font-medium opacity-60">Auto generating</div>
        );
      },
    },
    {
      title: 'Title',
      dataIndex: 'name',
      key: 'name',
      editable: true,
      width: '30%',
      render: (title, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            <TagOutlined className="font-semibold" />
            {title || '-'}
          </div>
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '30%',
      editable: true,
      render: (description, record) => {
        return description?.trim() ? (
          <div
            className="line-clamp-1 max-w-xs w-full gap-1"
            title={description}
          >
            {description}
          </div>
        ) : (
          <div>
            <span className="font-medium italic opacity-60">
              No description
            </span>
          </div>
        );
      },
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '20%',
      render: (createdAt, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {dayjs(createdAt).format('DD/MM/YYYY') || '-'}
          </div>
        );
      },
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: '20%',
      render: (updatedAt, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {dayjs(updatedAt).format('DD/MM/YYYY') || '-'}
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: '10%',
      align: 'center',
      render: (_action, record) => {
        const editable = isEditing(record);
        return (
          <div className="flex items-center justify-center w-full font-semibold ">
            {editable ? (
              <div className="flex items-center gap-2">
                <Button
                  loading={actionLoading}
                  type="dashed"
                  className="flex gap-1 items-center"
                  onClick={() => save(record?.key)}
                  icon={<CheckOutlined />}
                >
                  Save
                </Button>
                <Popconfirm
                  title="Sure to cancel?"
                  onConfirm={record?.id ? cancel : cancelAdd}
                >
                  <Button
                    type="text"
                    danger
                    className="flex gap-1 items-center"
                    icon={<StopOutlined />}
                  >
                    <span>Cancel</span>
                  </Button>
                </Popconfirm>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  type="primary"
                  disabled={editingKey}
                  onClick={() => edit(record)}
                  icon={<EditOutlined />}
                />
                <Popconfirm
                  title={<div>Confirmation</div>}
                  description={
                    <div>
                      Are you sure to delete <strong>{record?.name}</strong>{' '}
                      Tag?
                    </div>
                  }
                  onCancel={cancel}
                  onConfirm={() => handleDelete(record?.id)}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    danger
                    disabled={editingKey}
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm>
              </div>
            )}
          </div>
        );
      },
    },
  ];

  const mergedColumns = tagColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <>
      {!isInSettings && <NavMenu location={window?.location} />}
      <CardToolbox>
        <ContactPageheaderStyle>
          <div className="w-full h-full flex justify-between items-center">
            <div className="flex items-center gap-4 justify-center">
              <Button
                disabled={editingKey || loading}
                onClick={handleAdd}
                type="primary"
                icon={<PlusOutlined />}
              >
                Add Tag
              </Button>
              {/* <Search
                allowClear
                // loading={loading}
                placeholder="Search by name"
                enterButton="Search"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                // onSearch={(keyword) => {
                //   handleSearch(keyword);
                // }}
              /> */}
            </div>
            <div>
              {selectedRowKeys?.length > 0 && (
                <div>
                  <Dropdown
                    className="animated fadeInDownBig"
                    placement="bottom"
                    arrow
                    menu={{
                      items: [
                        {
                          key: 'delete-selected',
                          label: (
                            <a
                              className="flex gap-2 items-center"
                              onClick={(e) => {
                                e.preventDefault();
                              }}
                            >
                              <span>Delete</span>
                            </a>
                          ),
                          icon: <DeleteOutlined />,
                        },
                      ],
                    }}
                  >
                    <Space>
                      <Button
                        type="primary"
                        className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                      >
                        <p className="Montserrat">
                          {`${selectedRowKeys?.length} Selected`}
                        </p>
                        <DownOutlined />
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              )}
            </div>
          </div>
        </ContactPageheaderStyle>
      </CardToolbox>
      <Main>
        <Row gutter={15}>
          <Col xs={24}>
            <Cards headless>
              <UserTableStyleWrapper>
                <div className="contact-table">
                  <TableWrapper className="table-responsive text-gray-800">
                    <Form form={form} component={false}>
                      <Table
                        loading={loading}
                        className="customized-style-pagination w-full"
                        components={{
                          body: {
                            cell: EditableCell,
                          },
                        }}
                        rowClassName="editable-row"
                        dataSource={dataSource}
                        columns={mergedColumns}
                        rowSelection={editingKey ? null : rowSelection}
                        rowKey={(record) => record.id}
                        pagination={false}
                      />
                    </Form>
                  </TableWrapper>
                </div>
              </UserTableStyleWrapper>
            </Cards>
          </Col>
        </Row>
      </Main>
    </>
  );
};

export default CRMTagsPage;
