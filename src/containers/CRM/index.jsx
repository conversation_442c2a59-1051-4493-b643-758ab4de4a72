import { useParams } from 'react-router-dom';
import zileLogo from '../../assets/img/welcome/logo.png';
import { Button, Col, Image, Row, Skeleton } from 'antd';
import './crm.scss';
import ContainerCard from '../../components/CRM/ContainerCard';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  AuditOutlined,
  BankOutlined,
  ExportOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import NavMenu from './NavMenu';
import Heading from '../../components/Heading';
import clsx from 'clsx';
import { lazy, Suspense, useState } from 'react';
import {
  ChartjsBarChartTransparent,
  chartOptions,
} from '../../components/Charts';
import { Cards } from '../../components/Cards';
import { CardBarChart2, EChartCard } from '../Dashboard/style';
import { numberWithCommas } from '../../utils/common';
import { useQuery } from '@tanstack/react-query';
import moment from 'moment';
import { chartLinearGradient } from '../../utils/chart.utils';
import SettingsButton from './Setting/SettingsButton';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';
import { SUPER_ADMIN, userRole } from '../../constants/common.constant';

const MinMaxLeadsValue = lazy(() => import('../Dashboard/MinMaxLeadsValue'));
const RecentLeadSynced = lazy(() => import('./Dashboard/RecentLeadSynced'));
const RecentContactAdded = lazy(() => import('./Dashboard/RecentContactAdded'));
const RecentCompanyAdded = lazy(() => import('./Dashboard/RecentCompanyAdded'));

export const CRMRoutes = {
  companies: {
    path: '/crm/companies',
    title: 'Companies',
    description: 'Manage your companies',
    icon: <BankOutlined className="text-4xl font-bold text-cyan-600" />,
  },
  contacts: {
    path: '/crm/contacts',
    title: 'Contacts',
    description: 'Manage your contacts',
    icon: <UserOutlined className="text-4xl font-bold text-cyan-600" />,
  },
  leads: {
    path: '/crm/leads',
    title: 'Leads',
    description: 'Manage your leads',
    icon: <AuditOutlined className="text-4xl font-bold text-cyan-600" />,
  },
  // settings: {
  //   path: '/crm/setting',
  //   title: 'Settings',
  //   description: 'Settings for CRM',
  //   icon: <SettingOutlined className="text-4xl font-bold text-cyan-600" />,
  // },
};

const CRMDashboardPage = () => {
  const [chartLabels, setChartLabels] = useState([]);
  const currentUserRole = getUserViewAsRole();
  const isAllowSettings =
    currentUserRole === userRole.ADMIN || currentUserRole === SUPER_ADMIN;

  const {
    data,
    isFetching,
    refetch: refetchLeadStats,
  } = useQuery(
    ['CRM_STATS'],
    async () => {
      let initialChartBarDataSet = [
        {
          key: 'companyCount',
          name: 'Total Companies',
          total: 12,
          isIncrease: true,
          range: `4%`,
          data: [
            {
              data: [20, 60, 50, 45, 50, 60, 70, 40, 45, 35, 25, 30],
              backgroundColor: '#EFEFFE',
              hoverBackgroundColor: '#5F63F2',
              label: 'item(s)',
              barPercentage: 1,
            },
          ],
        },
        {
          key: 'contactCount',
          name: 'Total Contacts',
          total: 123,
          isIncrease: true,
          range: `53%`,
          data: [
            {
              data: [20, 60, 50, 45, 50, 60, 70, 40, 45, 35, 25, 30],
              backgroundColor: '#FFF0F6',
              hoverBackgroundColor: '#FF69A5',
              label: 'contact(s)',
              barPercentage: 1,
            },
          ],
        },
        {
          key: 'leadCount',
          name: 'Total Leads',
          total: 454,
          isIncrease: true,
          range: `434%`,
          data: [
            {
              data: [20, 60, 50, 45, 50, 60, 70, 40, 45, 35, 25, 30],
              backgroundColor: '#E9F5FF',
              hoverBackgroundColor: '#2C99FF',
              label: 'lead(s)',
              barPercentage: 1,
            },
          ],
        },
        {
          key: 'tagCount',
          name: 'Total Tags',
          total: 454,
          isIncrease: true,
          range: `434%`,
          data: [
            {
              data: [20, 60, 50, 45, 50, 60, 70, 40, 45, 35, 25, 30],
              backgroundColor: '#E9F5FF',
              hoverBackgroundColor: '#2C99FF',
              label: 'tag(s)',
              barPercentage: 1,
            },
          ],
        },
      ];

      setChartLabels([
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ]);

      return initialChartBarDataSet;
    },
    {
      enabled: true,
      initialData: [],
    }
  );

  const { data: potentialLeadValue, isFetching: isFetchingPotentialLeads } =
    useQuery(['CRM_GET_POTENTIAL_LEAD_RETURN'], {
      queryFn: async () => {
        const labels = [];

        for (let i = 1; i <= 12; i += 1) {
          let monthName = moment(new Date())
            .subtract(i, 'month')
            .startOf('month')
            .format('MMM');
          labels.push(monthName);
        }

        const performanceDatasets = [
          {
            data: Array.from({ length: 12 }, () =>
              Math.floor(Math.random() * 1000)
            ),
            borderColor: '#5F63F2',
            borderWidth: 4,
            fill: true,
            backgroundColor: () => {
              const performanceEl = document?.getElementById('performance');
              return chartLinearGradient(performanceEl, 300, {
                start: '#5F63F230',
                end: '#ffffff05',
              });
            },
            label: 'Max. Leads Value',
            pointStyle: 'circle',
            pointRadius: '0',
            hoverRadius: '9',
            pointBorderColor: '#fff',
            pointBackgroundColor: '#5F63F2',
            hoverBorderWidth: 5,
            amount: `£1,735,800`,
            amountClass: 'current-amount',
          },
          {
            data: Array.from({ length: 12 }, () =>
              Math.floor(Math.random() * 1000)
            ),
            borderColor: '#C6D0DC',
            borderWidth: 2,
            fill: false,
            backgroundColor: '#00173750',
            label: 'Min. Leads Value',
            borderDash: [3, 3],
            pointRadius: '0',
            hoverRadius: '0',
            amount: `£1,284,075`,
            amountClass: 'prev-amount',
          },
        ];
        return { performanceDatasets, labels: labels.reverse() };
      },
      initialData: { label: [], performanceDatasets: [] },
    });

  return (
    <div className="w-full h-full rounded-md">
      <NavMenu location={window?.location} />
      <div className="relative w-full h-full">
        {isAllowSettings && <SettingsButton />}
        <div className="flex justify-between items-center px-3 pb-4 font-Inter">
          <div></div>
          <div className="flex items-center gap-2">
            <Button
              type="primary"
              icon={<ExportOutlined />}
              className="text-cyan-600"
            >
              Export
            </Button>
          </div>
        </div>

        {/* {isFetching && (
          <div className="flex gap-3 p-4">
            {Array.from({
              length: 4,
            }).map((_, i) => (
              <Skeleton active />
            ))}
          </div>
        )} */}

        <div
          className={clsx(
            'grid grid-cols-1 gap-5 lg:grid-cols-2 2xl:grid-cols-4'
          )}
        >
          {data?.map((dataSet) => (
            <Col className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/50 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300" key={dataSet?.key}>
              <Cards headless>
                <EChartCard style={{ minHeight: '130px' }}>
                  <div className="card-chunk">
                    <CardBarChart2>
                      <Heading as="h1" className="font-Inter">
                        {numberWithCommas(dataSet.total ?? 0)}
                      </Heading>
                      <span
                        className="font-Inter line-clamp-1"
                        title={dataSet.name}
                      >
                        {dataSet.name}
                      </span>
                      <div>
                        <p>
                          {dataSet.range !== '0%' &&
                            !dataSet.range.includes('Infinity') && (
                              <span
                                className={`font-Inter ${dataSet.isIncrease ? 'growth-upward' : 'growth-downward'}`}
                              >
                                {dataSet.isIncrease ? (
                                  <ArrowUpOutlined />
                                ) : (
                                  <ArrowDownOutlined />
                                )}{' '}
                                {dataSet.range === 'NaN%'
                                  ? `0%`
                                  : dataSet.range}
                              </span>
                            )}
                          {dataSet.range == '0%' && (
                            <span
                              className={`font-Inter ${dataSet.isIncrease ? 'growth-upward' : 'growth-downward'}`}
                            >
                              {dataSet.isIncrease ? (
                                <ArrowUpOutlined />
                              ) : (
                                <ArrowDownOutlined />
                              )}{' '}
                              {dataSet.range === 'NaN%' ? `0%` : dataSet.range}
                            </span>
                          )}
                          {dataSet.range === 'Infinity%' && (
                            <span
                              style={{
                                height: '40px',
                              }}
                            ></span>
                          )}
                          <span
                            className={`font-Inter`}
                            style={{ width: '100%' }}
                          >
                            Since last month
                          </span>
                        </p>
                      </div>
                    </CardBarChart2>
                  </div>
                  <div className="card-chunk">
                    <ChartjsBarChartTransparent
                      labels={chartLabels}
                      datasets={dataSet.data}
                      options={chartOptions}
                    />
                  </div>
                </EChartCard>
              </Cards>
            </Col>
          ))}
        </div>
        <Row gutter={25}>
          <Col xxl={24} xs={24} className='py-5'>
            <Suspense
              fallback={
                <Cards headless>
                  <Skeleton active />
                </Cards>
              }
            >
              <MinMaxLeadsValue
                title="Total Leads Value"
                preIsLoading={isFetchingPotentialLeads}
                potentialLeadValue={potentialLeadValue}
              />
            </Suspense>
          </Col>

          <Col xxl={8} xs={24} className="flex">
            <Suspense
              fallback={
                <Cards headless>
                  <Skeleton active />
                </Cards>
              }
            >
              <RecentLeadSynced />
            </Suspense>
          </Col>
          <Col xxl={8} xs={24} className="flex">
            <Suspense
              fallback={
                <Cards headless>
                  <Skeleton active />
                </Cards>
              }
            >
              <RecentContactAdded />
            </Suspense>
          </Col>
          <Col xxl={8} xs={24} className="flex">
            <Suspense
              fallback={
                <Cards headless>
                  <Skeleton active />
                </Cards>
              }
            >
              <RecentCompanyAdded />
            </Suspense>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default CRMDashboardPage;
