import { userRole } from '../../../constants/common.constant';

const UserSummaryCards = ({ onRoleClick, selectedRole, users = [] }) => {
  const totalUser = users?.length;

  const totalAdmin = users?.filter(
    (user) =>
      user?.role?.keyCode === userRole.ADMIN || user?.role === userRole.ADMIN
  ).length;

  const totalManagement = users?.filter(
    (user) =>
      user?.role?.keyCode === userRole.MANAGEMENT ||
      user?.role === userRole.MANAGEMENT
  ).length;

  const totalBasicUser = users?.filter(
    (user) =>
      user?.role?.keyCode === userRole.BASIC_USER ||
      user?.role === userRole.BASIC_USER
  ).length;

  const stats = [
    {
      title: 'Total Users',
      value: totalUser,
      icon: <UsersIcon />,
      color: 'teal',
      description: 'All active users',
      role: 'All',
    },
    {
      title: 'Admin',
      value: totalAdmin,
      icon: <ShieldIcon />,
      color: 'purple',
      description: 'Administrator access',
      role: 'Admin',
    },
    {
      title: 'Management',
      value: totalManagement,
      icon: <CrownIcon />,
      color: 'blue',
      description: 'Management level',
      role: 'Management',
    },
    {
      title: 'Basic User',
      value: totalBasicUser,
      icon: <UserIcon />,
      color: 'green',
      description: 'Standard access',
      role: 'Basic User',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          {...stat}
          onRoleClick={onRoleClick}
          selectedRole={selectedRole}
        />
      ))}
    </div>
  );
};

export default UserSummaryCards;

function StatCard({
  title,
  value,
  icon,
  color,
  description,
  role,
  onRoleClick,
  selectedRole,
}) {
  const colorClasses = {
    teal: 'bg-teal-100 text-teal-600 border-teal-200',
    purple: 'bg-purple-100 text-purple-600 border-purple-200',
    blue: 'bg-blue-100 text-blue-600 border-blue-200',
    green: 'bg-green-100 text-green-600 border-green-200',
  };

  const isSelected = selectedRole === role;

  return (
    <div
      onClick={() => onRoleClick(role)}
      className={`bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-200 transform hover:scale-105 hover:shadow-lg ${
        isSelected
          ? 'border-teal-500 shadow-lg ring-2 ring-teal-200'
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>{icon}</div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{description}</div>
        </div>
      </div>
      <h3
        className={`text-lg font-semibold ${isSelected ? 'text-teal-600' : 'text-gray-900'}`}
      >
        {title}
      </h3>
      {isSelected && (
        <div className="mt-2 text-xs text-teal-600 font-medium">
          <svg
            className="inline w-3 h-3 mr-1"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          Filtered
        </div>
      )}
    </div>
  );
}

function UsersIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function ShieldIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    </svg>
  );
}

function CrownIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14" />
    </svg>
  );
}

function UserIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );
}
