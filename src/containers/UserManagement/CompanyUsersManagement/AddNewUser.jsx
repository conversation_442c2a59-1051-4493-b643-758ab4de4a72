import React, { useEffect, useState } from 'react';
import clsx from 'clsx';
import { Alert<PERSON>riangle, CreditCard, ShoppingCart } from 'lucide-react';
import { Button, notification, Skeleton, Tooltip } from 'antd';
import { RestyledModal } from '../../page/CreditManagementCompany/PurchaseCreditSection';
import PurchaseLicenseSection from './PurchaseLicenseSection';
import { addNewUser, getOrgLicenseInfor } from '../../../services/users';

export function AddUserModal({ onClose }) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(true);
  const [addLoading, setAddLoading] = useState(false);
  const [role, setRole] = useState('BASIC_USER');
  const [licenseInfor, setLicenseInfor] = useState(null);
  const licenseInfor 
  // Purchase license function can be implemented here
  const [openPurchaseLicense, setOpenPurchaseLicense] = useState(false);

  const showPurchaseLicenseModal = () => setOpenPurchaseLicense(true);
  const closePurchaseLicenseModal = () => setOpenPurchaseLicense(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setAddLoading(true);
    try {
      // Handle adding user here
      const { data } = await addNewUser({ email, role });
      notification.success({
        message: 'User Added',
        description: `User with email ${email} has been added successfully.`,
      });
      console.log('Adding user:', { email, role });
      setAddLoading(false);
      onClose();
    } catch (error) {
      setAddLoading(false);
      notification.error({
        message: 'Error',
        description:
          error?.message || 'Failed to add user. Please try again later.',
      });
      console.error('Error submitting form:', error);
    }
  };

  const getCompanyLicenseDetail = async () => {
    try {
      setLoading(true);
      const { data } = await getOrgLicenseInfor();
      if (data?.result) {
        // setLicenseInfor({ ...data?.result });
      }
      setLoading(false);
      console.log('Company License Data:', data);
    } catch (error) {
      setLoading(false);
      console.error('Error fetching company license details:', error);
    }
  };

  useEffect(() => {
    getCompanyLicenseDetail();
  }, []);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              Add New User
            </h2>
            <p className="text-sm text-gray-500">
              Invite a new team member to your organization
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        </div>

        <div
          className={clsx(
            'rounded-lg border bg-card text-card-foreground shadow-sm mx-4',
            licenseInfor?.licenses?.available > 0
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          )}
        >
          <div className="flex flex-col space-y-1.5 p-6 pb-3">
            <div className="font-semibold leading-none tracking-tight text-sm flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              License Status
            </div>
          </div>
          {!loading && (
            <div className="p-6 pt-0">
              {licenseInfor?.licenses?.available > 0 ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Available Licenses:
                    </span>
                    <div className="inline-flex items-center rounded-full border px-6 py-1 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 bg-green-100 text-green-800">
                      {licenseInfor?.licenses?.available} remaining
                    </div>
                  </div>
                  <p className="text-xs text-green-600">
                    ✓ Licenses successfully added! You can now invite new users.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-red-600">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      No licenses available
                    </span>
                  </div>
                  <p className="text-xs text-gray-600">
                    You need to purchase additional licenses to invite new
                    users.
                  </p>
                  <Button
                    size="sm"
                    className="w-full bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium justify-center h-10"
                    onClick={showPurchaseLicenseModal}
                    icon={<ShoppingCart className="w-4 h-4" />}
                  >
                    Purchase Licenses
                  </Button>
                </div>
              )}
            </div>
          )}
          {loading && (
            <div className="p-6 pt-0">
              <Skeleton active paragraph={{ rows: 4 }} />
            </div>
          )}
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter user's email address"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                required
              />
            </div>

            <div>
              <label
                htmlFor="role"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Role
              </label>
              <select
                id="role"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="BASIC_USER">Basic User</option>
                <option value="MANAGEMENT">Management</option>
                <option value="ADMIN">Admin</option>
              </select>
            </div>

            {/* Licensing Notice */}
            {/* <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg
                  className="w-5 h-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-amber-800 mb-1">
                    License Information
                  </h4>
                  <p className="text-sm text-amber-700">
                    Please note that adding a new user may require purchasing an
                    additional license, which will result in additional charges
                    to your account. You will be notified of any costs before
                    they are applied.
                  </p>
                </div>
              </div>
            </div> */}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 mt-6 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <Tooltip
              title={
                licenseInfor?.licenses?.available <= 0
                  ? 'Need to purchase license first!'
                  : 'Send invitation'
              }
              placement="top"
            >
              {' '}
              <Button
                loading={addLoading}
                htmlType="submit"
                disabled={!email || licenseInfor?.licenses?.available <= 0}
                className="h-10 bg-teal-500 text-white rounded-lg hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                Send Invitation
              </Button>
            </Tooltip>
          </div>
        </form>
      </div>
      {/* Purchase License Modal */}
      <RestyledModal
        title={
          <div className="bg-gray-100 py-3 px-4 flex items-center justify-between">
            <h3 className="text-lg font-medium">Select Licenses to Purchase</h3>
          </div>
        }
        className="mt-10"
        open={openPurchaseLicense}
        onCancel={closePurchaseLicenseModal}
        footer={null}
      >
        <div className="p-6">
          <PurchaseLicenseSection
            close={closePurchaseLicenseModal}
            onPurchaseSuccess={() => {
              // Refresh license information after successful purchase
              getCompanyLicenseDetail();
            }}
          />
        </div>
      </RestyledModal>
    </div>
  );
}
