import { <PERSON><PERSON>, Button, message, Modal, notification, Popconfirm, Upload } from 'antd';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { getAvatarColor } from './UserTable';
import dayjs from 'dayjs';
import { updateUserPassword, uploadImage } from '../../../services/users';
import { UploadCloud } from 'lucide-react';
import { CloseOutlined, EyeInvisibleOutlined, EyeTwoTone, FormOutlined, InfoCircleOutlined, UploadOutlined } from '@ant-design/icons';
import useEditUser from '../../../hooks/useEditUser';
import MailBox from '../../Settings/MailBox';
import Sequence from '../../Settings/Sequence';
import LinkedIn from '../../Settings/LinkedIn';
import Password from 'antd/es/input/Password';

const timezones = [
  { value: '', label: 'Select Timezone' },
  { value: 'UTC', label: 'UTC - Coordinated Universal Time' },
  { value: 'GMT', label: 'GMT - Greenwich Mean Time' },
  { value: 'EST', label: 'EST - Eastern Standard Time (UTC-5)' },
  { value: 'CST', label: 'CST - Central Standard Time (UTC-6)' },
  { value: 'MST', label: 'MST - Mountain Standard Time (UTC-7)' },
  { value: 'PST', label: 'PST - Pacific Standard Time (UTC-8)' },
  { value: 'AKST', label: 'AKST - Alaska Standard Time (UTC-9)' },
  { value: 'HST', label: 'HST - Hawaii Standard Time (UTC-10)' },
  { value: 'AST', label: 'AST - Atlantic Standard Time (UTC-4)' },
  { value: 'NST', label: 'NST - Newfoundland Standard Time (UTC-3:30)' },
  { value: 'CET', label: 'CET - Central European Time (UTC+1)' },
  { value: 'EET', label: 'EET - Eastern European Time (UTC+2)' },
  { value: 'MSK', label: 'MSK - Moscow Time (UTC+3)' },
  { value: 'GST', label: 'GST - Gulf Standard Time (UTC+4)' },
  { value: 'PKT', label: 'PKT - Pakistan Standard Time (UTC+5)' },
  { value: 'IST', label: 'IST - India Standard Time (UTC+5:30)' },
  { value: 'BST', label: 'BST - Bangladesh Standard Time (UTC+6)' },
  { value: 'ICT', label: 'ICT - Indochina Time (UTC+7)' },
  { value: 'CST_CHINA', label: 'CST - China Standard Time (UTC+8)' },
  { value: 'JST', label: 'JST - Japan Standard Time (UTC+9)' },
  { value: 'AEST', label: 'AEST - Australian Eastern Standard Time (UTC+10)' },
  { value: 'NZST', label: 'NZST - New Zealand Standard Time (UTC+12)' },
  { value: 'CAT', label: 'CAT - Central Africa Time (UTC+2)' },
  { value: 'WAT', label: 'WAT - West Africa Time (UTC+1)' },
  { value: 'EAT', label: 'EAT - East Africa Time (UTC+3)' },
  { value: 'SAST', label: 'SAST - South Africa Standard Time (UTC+2)' },
  { value: 'BRT', label: 'BRT - Brasilia Time (UTC-3)' },
  { value: 'ART', label: 'ART - Argentina Time (UTC-3)' },
  { value: 'CLT', label: 'CLT - Chile Standard Time (UTC-4)' },
  { value: 'COT', label: 'COT - Colombia Time (UTC-5)' },
];

export function UserDetailsPage({ user, back }) {
  const { setUser, onSubmit, setValue, loading } = useEditUser(user?.id);

  const [activeTab, setActiveTab] = useState('profile');
  const [linkedInStep, setLinkedInStep] = useState(1);
  const [linkedInData, setLinkedInData] = useState({
    country: '',
    credentials: { username: '', password: '' },
    cookies: '',
  });
  const [formData, setFormData] = useState({
    ...user,
    fullName: user?.fullName || '',
    email: user?.email,
    jobTitle: user?.jobTitle || '',
    organization: user?.organization?.name || '',
    role: user?.role?.name,
    username: user?.username || user?.email?.split('@')[0],
    timezone: user?.timezone || '',
    linkedinUrl: user?.linkedinUrl || '',
    licenseType: user?.licenseType,
    isDisabled: user?.isDisabled || false,
    password: '',
  });
  const [password, setPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [opendPasswordEditModal, setOpendPasswordEditModal] = useState(false);

  const showPasswordEditModal = () => {
    setPassword('');
    setOpendPasswordEditModal(true);
  };
  const closePasswordEditModal = () => setOpendPasswordEditModal(false);

  const handleUpdatePassword = async () => {
    if (!user?.id) return;
    setPasswordLoading(true);

    await updateUserPassword(user?.id, password)
      .then((res) => {
        notification.success({
          description: 'Password changed!',
        });
        closePasswordEditModal();
      })
      .catch((err) => {
        notification.error({
          description: 'Password update failed!',
        });
      })
      .finally(() => {
        setPasswordLoading(false);
      });

    console.log('new password: ', password);
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: <UserIcon /> },
    { id: 'mailbox', label: 'Mailbox', icon: <MailIcon /> },
    { id: 'sequence', label: 'Sequence', icon: <ListIcon /> },
    { id: 'linkedin', label: 'LinkedIn', icon: <LinkedInIcon /> },
  ];

  const handleInputChange = (field, value) => {
    setUser((prev) => ({ ...prev, [field]: value }));
    setFormData((prev) => ({ ...prev, [field]: value }));
    setValue(field, value);
  };

  const renderLinkedInStep = () => {
    switch (linkedInStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <span className="text-red-500">*</span> Your Country
              </label>
              <input
                type="text"
                value={linkedInData.country}
                onChange={(e) =>
                  setLinkedInData({ ...linkedInData, country: e.target.value })
                }
                placeholder="Enter your Region name"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LinkedIn Username/Email
              </label>
              <input
                type="text"
                value={linkedInData.credentials.username}
                onChange={(e) =>
                  setLinkedInData({
                    ...linkedInData,
                    credentials: {
                      ...linkedInData.credentials,
                      username: e.target.value,
                    },
                  })
                }
                placeholder="Enter your LinkedIn username or email"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LinkedIn Password
              </label>
              <input
                type="password"
                value={linkedInData.credentials.password}
                onChange={(e) =>
                  setLinkedInData({
                    ...linkedInData,
                    credentials: {
                      ...linkedInData.credentials,
                      password: e.target.value,
                    },
                  })
                }
                placeholder="Enter your LinkedIn password"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cookies <span className="text-gray-500">(Optional)</span>
              </label>
              <textarea
                value={linkedInData.cookies}
                onChange={(e) =>
                  setLinkedInData({ ...linkedInData, cookies: e.target.value })
                }
                placeholder="Paste LinkedIn cookies here (optional)"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
          </div>
        );
      case 4:
        return (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-800 mb-2">
                Setup Complete
              </h4>
              <p className="text-sm text-green-700">
                Your LinkedIn integration has been configured successfully. You
                can now use LinkedIn features within the platform.
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Country:</span>
                <span className="font-medium">{linkedInData.country}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Username:</span>
                <span className="font-medium">
                  {linkedInData.credentials.username}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Cookies:</span>
                <span className="font-medium">
                  {linkedInData.cookies ? 'Provided' : 'Not provided'}
                </span>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const props = {
    name: 'file',
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        let formData = new FormData();
        formData.append('avatar', file);
        const res = await uploadImage(formData);
        handleInputChange('avatarUrl', res?.data?.result?.data?.url);
        handleInputChange('avatarId', res?.data?.result?.data?.imgId);
        onSuccess('ok');
      } catch (error) {
        console.log('error upload image: ', error);
        onError(error);
      }
    },
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className=" mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            type="text"
            onClick={back}
            className="inline-flex items-center text-sm text-teal-600 hover:text-teal-800 mb-4 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-1"
            >
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
            Back to User Management
          </Button>

          <div className="flex items-center gap-4">
            {user?.avatarUrl && (
              <Avatar size={150} src={user?.avatarUrl} className=" mr-4" />
            )}
            {!user?.avatarUrl && (
              <div
                className={`w-20 h-20 rounded-full ${getAvatarColor(user?.avatar || user?.email)} flex items-center justify-center text-white font-medium mr-4`}
              >
                {user?.fullName?.charAt(0) || user?.email?.charAt(0)}
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {user?.fullName || '-'}
              </h1>
              <p className="text-gray-500">{user?.email || '-'}</p>
              <div className="flex items-center gap-2 mt-1">
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${
                    user?.role?.name === 'Admin'
                      ? 'bg-purple-100 text-purple-800 border-purple-200'
                      : user?.role?.name === 'Management'
                        ? 'bg-blue-100 text-blue-800 border-blue-200'
                        : 'bg-green-100 text-green-800 border-green-200'
                  }`}
                >
                  {user?.role?.name}
                </span>
                <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                  {user?.status}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-4 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon}
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'profile' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Profile Picture Section */}
                <div className="lg:col-span-1">
                  <div className="flex flex-col gap-3 items-center justify-center">
                    {formData?.avatarUrl && (
                      <Avatar
                        size={150}
                        src={formData?.avatarUrl}
                        className=" mr-4"
                      />
                    )}
                    {!formData?.avatarUrl && (
                      <div
                        className={`w-20 h-20 rounded-full ${getAvatarColor(user?.avatar || user?.email)} flex items-center justify-center text-white font-medium mr-4`}
                      >
                        {user?.fullName?.charAt(0) || user?.email?.charAt(0)}
                      </div>
                    )}
                    <Upload
                      {...props}
                      rootClassName="customized-upload-component width-inherit"
                    >
                      <div className="w-full flex items-center justify-center">
                        <Button
                          type="primary"
                          className="mt-3"
                          icon={<UploadOutlined />}
                        >
                          Upload
                        </Button>
                      </div>
                    </Upload>
                    <div className="mt-4 text-sm text-gray-500">
                      <p>
                        Last active:{' '}
                        {dayjs(user?.lastActivity)?.format(
                          'DD-MM-YYYY HH:mm:ss'
                        )}
                      </p>
                      <p>Company: {user?.organization?.name}</p>
                    </div>
                  </div>
                </div>

                {/* Form Section */}
                <div className="lg:col-span-2 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={formData.fullName}
                        onChange={(e) =>
                          handleInputChange('fullName', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange('email', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Job Title
                      </label>
                      <input
                        type="text"
                        value={formData.jobTitle}
                        onChange={(e) =>
                          handleInputChange('jobTitle', e.target.value)
                        }
                        placeholder="e.g. Sales Manager"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Organization
                      </label>
                      <div className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                        {formData.organization || 'Not Assigned'}
                      </div>
                      {/* <input
                        type="text"
                        value={formData.organization}
                        onChange={(e) =>
                          handleInputChange('organization', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      /> */}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Role
                      </label>
                      <select
                        value={formData.role}
                        onChange={(e) =>
                          handleInputChange('role', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      >
                        <option value="Admin">Admin</option>
                        <option value="Management">Management</option>
                        <option value="Basic User">Basic User</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Username
                      </label>
                      <input
                        type="text"
                        value={formData.username}
                        onChange={(e) =>
                          handleInputChange('username', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Time Zone
                      </label>
                      <select
                        value={formData.timezone}
                        onChange={(e) =>
                          handleInputChange('timezone', e.target.value)
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      >
                        {timezones.map((tz) => (
                          <option key={tz.value} value={tz.value}>
                            {tz.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        LinkedIn
                      </label>
                      <input
                        type="url"
                        value={formData.linkedinUrl}
                        onChange={(e) =>
                          handleInputChange('linkedinUrl', e.target.value)
                        }
                        placeholder="https://www.linkedin.com/in/username"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      License Type
                    </label>
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                      {formData.licenseType || 'Not Assigned'}
                    </div>
                    {/* <select
                      value={formData.licenseType}
                      onChange={(e) =>
                        handleInputChange('licenseType', e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="CONNECTED">CONNECTED</option>
                      <option value="STANDARD">STANDARD</option>
                    </select> */}
                  </div>

                  {/* Update Password Section */}
                  <div className="border-t border-gray-200 pt-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">
                      Security Settings
                    </h4>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Button
                        onClick={showPasswordEditModal}
                        type="primary"
                        className="col-span-4 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      >
                        Click here to update your password
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        Disable Account
                      </h4>
                      <p className="text-sm text-gray-500">
                        Prevent the user from signing in
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleInputChange('isDisabled', false)}
                        className={`px-3 py-1 text-xs rounded-md transition-colors ${
                          !formData.isDisabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        No
                      </button>
                      <button
                        onClick={() => handleInputChange('isDisabled', true)}
                        className={`px-3 py-1 text-xs rounded-md transition-colors ${
                          formData.isDisabled
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'mailbox' && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg
                    className="w-16 h-16 mx-auto"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-10 5L2 7" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Mailbox
                </h3>
                <MailBox userIdProp={user?.id} />
              </div>
            )}

            {activeTab === 'sequence' && (
              <div className="w-full items-center justify-center ">
                <Sequence userIdProp={user?.id} />
              </div>
            )}

            {activeTab === 'linkedin' && (
              <div className="w-full items-center justify-center ">
                <LinkedIn userIdProp={user?.id} />
              </div>
            )}
          </div>

          {/* Footer */}
          {activeTab === 'profile' && (
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-end gap-3">
              <Button
                onClick={onSubmit}
                loading={loading}
                className="bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors"
              >
                Save Changes
              </Button>
            </div>
          )}
        </div>
      </div>
      {/* Password modal */}
      {opendPasswordEditModal && (
        <Modal
          title={
            <div className="flex items-center gap-1">
              <span>Update New Password</span>
              <Popconfirm
                rootClassName="hide-popconfirm-buttons"
                placement="top"
                title={''}
                description={
                  'Password should be between 8-12 letters and should include 1 number and 1 special character'
                }
                // okText="Yes"
                // cancelText="No"
              >
                <Button type="text" icon={<InfoCircleOutlined />} />
              </Popconfirm>
            </div>
          }
          open={opendPasswordEditModal}
          // onOk={closePasswordEditModal}
          onCancel={closePasswordEditModal}
          footer={
            <div className="flex gap-1 w-full justify-end">
              <Button
                loading={passwordLoading}
                className="Montserrat  flex items-center "
                type="primary"
                onClick={handleUpdatePassword}
              >
                <FormOutlined /> <span>Save</span>
              </Button>
              <Button
                loading={passwordLoading}
                className="Montserrat  flex items-center "
                onClick={closePasswordEditModal}
              >
                <CloseOutlined /> <span>Cancel</span>
              </Button>
            </div>
          }
        >
          <div className="py-2">
            <Password
              className="py-2"
              placeholder="Input new password"
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
              value={password}
              onChange={(ev) => setPassword(ev.target.value)}
            />
          </div>
        </Modal>
      )}
    </div>
  );
}

function UserIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );
}

function MailIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="20" height="16" x="2" y="4" rx="2" />
      <path d="m22 7-10 5L2 7" />
    </svg>
  );
}

function ListIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="8" x2="21" y1="6" y2="6" />
      <line x1="8" x2="21" y1="12" y2="12" />
      <line x1="8" x2="21" y1="18" y2="18" />
      <line x1="3" x2="3.01" y1="6" y2="6" />
      <line x1="3" x2="3.01" y1="12" y2="12" />
      <line x1="3" x2="3.01" y1="18" y2="18" />
    </svg>
  );
}

function LinkedInIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
      <rect width="4" height="12" x="2" y="9" />
      <circle cx="4" cy="4" r="2" />
    </svg>
  );
}
