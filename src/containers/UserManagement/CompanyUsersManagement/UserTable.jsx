import { Avatar, notification } from 'antd';
import dayjs from 'dayjs';
import { useState, useEffect } from 'react';
import UserTableActions from '../../../components/UserManagementV2/UserTableActions';

export const getAvatarColor = (name) => {
  const colors = [
    'bg-red-500',
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-yellow-500',
    'bg-pink-500',
  ];
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};
const UserTable = ({
  selectedRoleFilter,
  users = [],
  rawUsers = [],
  setUsers,
  onUserClick,
  populateUsers,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState(rawUsers);

  useEffect(() => {
    let filtered = users;
    if (searchQuery) {
      filtered = filtered.filter(
        (user) =>
          user?.fullName?.toLowerCase()?.includes(searchQuery.toLowerCase()) ||
          user?.email?.toLowerCase()?.includes(searchQuery.toLowerCase())
        // ||
        // user.company.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (selectedRoleFilter !== 'All') {
      filtered = filtered.filter(
        (user) => user?.role?.name === selectedRoleFilter
      );
    }

    setFilteredUsers(filtered);
  }, [searchQuery, selectedRoleFilter]);

  const handleUserClick = (userId) => {
    const selectedUser = filteredUsers.find((user) => user.id === userId);
    if (!selectedUser) {
      notification.warning({
        message: 'User Not Found',
        description: 'The selected user does not exist.',
      });
      return;
    }

    onUserClick(selectedUser);
    // router.push(`/user/${userId}`);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'Admin':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Management':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Basic User':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm">
      {/* Table Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {selectedRoleFilter === 'All'
                ? 'All Users'
                : `${selectedRoleFilter} Users`}
            </h2>
            <p className="text-sm text-gray-500">
              {selectedRoleFilter === 'All'
                ? 'Manage your team members and their access levels'
                : `Showing ${filteredUsers.length} ${selectedRoleFilter.toLowerCase()} user${filteredUsers.length !== 1 ? 's' : ''}`}
            </p>
          </div>
          <div className="flex items-center gap-3">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search users..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 w-64 text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto max-h-96 overflow-y-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-200">
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Linked User
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Active
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <tr
                  key={user?.id}
                  onClick={() => handleUserClick(user?.id)}
                  className="hover:bg-gray-50 transition-colors cursor-pointer"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {user?.avatarUrl && (
                        <Avatar
                          size={50}
                          src={user?.avatarUrl}
                          className=" mr-4"
                        />
                      )}
                      {!user?.avatarUrl && (
                        <div
                          className={`w-12 h-12 rounded-full ${getAvatarColor(user?.avatar || user?.email)} flex items-center justify-center text-white font-medium mr-4`}
                        >
                          {user?.fullName?.charAt(0) || user?.email?.charAt(0)}
                        </div>
                      )}

                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user?.fullName || user?.username || user?.email}
                        </div>
                        <div className="text-sm text-gray-500">
                          {user?.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getRoleColor(user?.role?.name || user?.role)}`}
                    >
                      {user?.role?.name || user?.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user?.organization?.name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user?.consultantName || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user?.lastActivity
                      ? dayjs(user?.lastActivity).format('DD-MM-YYYY HH:mm:ss')
                      : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                      {user?.status}
                    </span>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <UserTableActions
                      setLoading={() => {}}
                      user={user}
                      reloadUserList={populateUsers}
                      setSelectedUserId={() => {}}
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={6}
                  className="px-6 py-12 text-center text-gray-500"
                >
                  <div className="flex flex-col items-center">
                    <svg
                      className="w-12 h-12 text-gray-300 mb-4"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                    <p className="text-lg font-medium">No users found</p>
                    <p className="text-sm">
                      Try adjusting your search or filter criteria
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UserTable;
