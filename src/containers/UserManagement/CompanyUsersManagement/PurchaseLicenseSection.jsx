import { Button } from 'antd';
import { CreditCard } from 'lucide-react';
import { useState } from 'react';

const PurchaseLicenseSection = ({ close }) => {
  const [selectedPlan, setSelectedPlan] = useState('1');
  return (
    <div className="sm:max-w-lg">
      <div className="space-y-6 flex flex-col justify-between">
        {/* License Options Grid */}
        <div className="grid grid-cols-2 gap-4">
          {/* 1 License */}
          <div
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedPlan === '1'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedPlan('1')}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium">1 License</h3>
              <span className="font-semibold">$15</span>
            </div>
            <p className="text-gray-600 text-sm">1 user license</p>
          </div>

          {/* 5 Licenses */}
          <div
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedPlan === '5'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedPlan('5')}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium">5 Licenses</h3>
              <span className="font-semibold">$65</span>
            </div>
            <p className="text-gray-600 text-sm">5 user licenses</p>
          </div>

          {/* 10 Licenses */}
          <div
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedPlan === '10'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedPlan('10')}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium">10 Licenses</h3>
              <span className="font-semibold">$120</span>
            </div>
            <p className="text-gray-600 text-sm">10 user licenses</p>
          </div>

          {/* 15 Licenses */}
          <div
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedPlan === '15'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedPlan('15')}
          >
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium">15 Licenses</h3>
              <span className="font-semibold">$170</span>
            </div>
            <p className="text-gray-600 text-sm">15 user licenses</p>
          </div>
        </div>

        {/* Information Note */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
            <p className="text-sm text-gray-700">
              Purchased licenses will be added to your company's license pool.
              When admins invite new users, they will automatically use licenses
              from this pool.
            </p>
          </div>
        </div>

        {/* Payment Method Section */}
        <div className="space-y-3">
          <h3 className="font-medium">Payment Method</h3>
          <div className="p-4 border border-gray-200 rounded-lg bg-white">
            <div className="flex items-center gap-3">
              <CreditCard className="w-5 h-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium">•••• •••• •••• 4242</p>
                <p className="text-xs text-gray-600">Expires 12/26</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t h-full items-center">
          <Button
            // variant="outline"
            onClick={close}
          >
            Cancel
          </Button>
          <Button
            className="bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium justify-center h-8"
            // onClick={handlePurchaseComplete}
            icon={<CreditCard className="w-4 h-4" />}
          >
            Purchase
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PurchaseLicenseSection;
