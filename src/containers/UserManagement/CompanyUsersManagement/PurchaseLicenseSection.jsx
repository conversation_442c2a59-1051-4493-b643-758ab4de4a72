import React, { useState, useEffect } from 'react';
import { Button, notification, Skeleton, Select } from 'antd';
import { CreditCard } from 'lucide-react';
import { previewLicenseFees, addLicenses } from '../../../services/users';
import { getPaymentMethods } from '../../../services/payment';

const PurchaseLicenseSection = ({ close, onPurchaseSuccess }) => {
  const [selectedPlan, setSelectedPlan] = useState('1');
  const [previewData, setPreviewData] = useState({});
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [purchaseLoading, setPurchaseLoading] = useState(false);

  // License options
  const licenseOptions = [
    { count: 1, label: '1 License' },
    { count: 5, label: '5 Licenses' },
    { count: 10, label: '10 Licenses' },
    { count: 15, label: '15 Licenses' },
  ];

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      const { data } = await getPaymentMethods();
      if (data?.result?.paymentMethods?.length > 0) {
        setPaymentMethods(data.result.paymentMethods);
        setSelectedPaymentMethod(data.result.paymentMethods[0]);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load payment methods.',
      });
    }
  };

  // Preview license fees for all options
  const fetchAllPreviewFees = async () => {
    setPreviewLoading(true);
    try {
      const promises = licenseOptions.map(async (option) => {
        const { data } = await previewLicenseFees(option.count);
        return { count: option.count, data: data?.result };
      });

      const results = await Promise.all(promises);
      const previewMap = {};
      results.forEach(({ count, data }) => {
        previewMap[count] = data;
      });
      setPreviewData(previewMap);
    } catch (error) {
      console.error('Error fetching preview fees:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load pricing information.',
      });
    } finally {
      setPreviewLoading(false);
    }
  };

  // Handle purchase
  const handlePurchase = async () => {
    if (!selectedPaymentMethod || !selectedPlan) return;

    setPurchaseLoading(true);
    try {
      const payload = {
        additionalLicenseCount: parseInt(selectedPlan),
        paymentMethodId: selectedPaymentMethod.id,
      };

      await addLicenses(payload);

      notification.success({
        message: 'Purchase Successful',
        description: `Successfully added ${selectedPlan} license(s) to your organization.`,
      });

      onPurchaseSuccess?.();
      close();
    } catch (error) {
      console.error('Error purchasing licenses:', error);
      notification.error({
        message: 'Purchase Failed',
        description: error?.response?.data?.message || 'Failed to purchase licenses. Please try again.',
      });
    } finally {
      setPurchaseLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await Promise.all([
        fetchPaymentMethods(),
        fetchAllPreviewFees()
      ]);
      setLoading(false);
    };

    initializeData();
  }, []);

  if (loading) {
    return (
      <div className="sm:max-w-lg p-6">
        <Skeleton active paragraph={{ rows: 6 }} />
      </div>
    );
  }

  return (
    <div className="sm:max-w-lg">
      <div className="space-y-6 flex flex-col justify-between">
        {/* License Options Grid */}
        <div className="grid grid-cols-2 gap-4">
          {licenseOptions.map((option) => {
            const preview = previewData[option.count];
            const price = preview?.pricing?.total ? (preview.pricing.total / 100).toFixed(2) : '...';

            return (
              <div
                key={option.count}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedPlan === option.count.toString()
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPlan(option.count.toString())}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">{option.label}</h3>
                  <span className="font-semibold">
                    {previewLoading ? '...' : `$${price}`}
                  </span>
                </div>
                <p className="text-gray-600 text-sm">
                  {option.count} user license{option.count > 1 ? 's' : ''}
                </p>
                {preview?.creditAllocation && (
                  <p className="text-xs text-blue-600 mt-1">
                    {preview.creditAllocation.totalCreditsToAllocate?.toLocaleString()} credits
                  </p>
                )}
              </div>
            );
          })}
        </div>

        {/* Information Note */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
            <p className="text-sm text-gray-700">
              Purchased licenses will be added to your company's license pool.
              When admins invite new users, they will automatically use licenses
              from this pool.
            </p>
          </div>
        </div>

        {/* Payment Method Section */}
        <div className="space-y-3">
          <h3 className="font-medium">Payment Method</h3>
          {paymentMethods.length > 0 ? (
            <div className="space-y-2">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedPaymentMethod?.id === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedPaymentMethod(method)}
                >
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="text-sm font-medium">
                        •••• •••• •••• {method.card?.last4}
                        {method.card?.brand && ` (${method.card.brand.toUpperCase()})`}
                      </p>
                      {method.card?.exp_month && method.card?.exp_year && (
                        <p className="text-xs text-gray-600">
                          Expires {method.card.exp_month}/{method.card.exp_year}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 border border-red-200 rounded-lg bg-red-50">
              <p className="text-sm text-red-600">
                No payment methods available. Please add a payment method first.
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t h-full items-center">
          <Button onClick={close}>
            Cancel
          </Button>
          <Button
            className="bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium justify-center h-8"
            onClick={handlePurchase}
            loading={purchaseLoading}
            disabled={!selectedPaymentMethod || !selectedPlan || previewLoading}
            icon={<CreditCard className="w-4 h-4" />}
          >
            Purchase {selectedPlan} License{selectedPlan !== '1' ? 's' : ''}
            {previewData[selectedPlan]?.pricing?.total &&
              ` - $${(previewData[selectedPlan].pricing.total / 100).toFixed(2)}`
            }
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PurchaseLicenseSection;
