import React, { useState, useEffect } from 'react';
import { Button, notification, Skeleton, Select } from 'antd';
import { CreditCard } from 'lucide-react';
import { previewLicenseFees, addLicenses } from '../../../services/users';
import { getPaymentMethods } from '../../../services/payment';

const PurchaseLicenseSection = ({ close, onPurchaseSuccess }) => {
  const [selectedPlan, setSelectedPlan] = useState('1');
  const [previewData, setPreviewData] = useState({});
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [purchaseLoading, setPurchaseLoading] = useState(false);

  // License options
  const licenseOptions = [
    { count: 1, label: '1 License' },
    { count: 5, label: '5 Licenses' },
    { count: 10, label: '10 Licenses' },
    { count: 15, label: '15 Licenses' },
  ];

  // Fetch payment methods
  const fetchPaymentMethods = async () => {
    try {
      const { data } = await getPaymentMethods();
      console.log('Payment methods response:', data);
      if (data?.result?.paymentMethods?.length > 0) {
        setPaymentMethods(data.result.paymentMethods);
        setSelectedPaymentMethod(data.result.paymentMethods[0]);
        console.log('Set payment methods:', data.result.paymentMethods);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load payment methods.',
      });
    }
  };

  // Preview license fees for selected plan
  const fetchPreviewFees = async (count) => {
    if (!count || previewData[count]) return; // Skip if already loaded

    setPreviewLoading(true);
    try {
      const { data } = await previewLicenseFees(count);
      console.log(`Preview for ${count} licenses:`, data);

      if (data?.result) {
        setPreviewData(prev => ({
          ...prev,
          [count]: data.result
        }));
      }
    } catch (error) {
      console.error('Error fetching preview fees:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load pricing information.',
      });
    } finally {
      setPreviewLoading(false);
    }
  };



  // Handle purchase
  const handlePurchase = async () => {
    if (!selectedPaymentMethod || !selectedPlan) return;

    setPurchaseLoading(true);
    try {
      const payload = {
        additionalLicenseCount: parseInt(selectedPlan),
        paymentMethodId: selectedPaymentMethod.id,
      };

      await addLicenses(payload);

      notification.success({
        message: 'Purchase Successful',
        description: `Successfully added ${selectedPlan} license(s) to your organization.`,
      });

      onPurchaseSuccess?.();
      close();
    } catch (error) {
      console.error('Error purchasing licenses:', error);
      notification.error({
        message: 'Purchase Failed',
        description: error?.response?.data?.message || 'Failed to purchase licenses. Please try again.',
      });
    } finally {
      setPurchaseLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await fetchPaymentMethods();
      // Fetch preview for default selected plan (1 license)
      await fetchPreviewFees(1);
      setLoading(false);
    };

    initializeData();
  }, []);

  if (loading) {
    return (
      <div className="sm:max-w-lg p-6">
        <Skeleton active paragraph={{ rows: 6 }} />
      </div>
    );
  }

  return (
    <div className="sm:max-w-lg">
      <div className="space-y-6 flex flex-col justify-between">


        {/* License Count Selection */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of Licenses
            </label>
            <div className="grid grid-cols-2 gap-3">
              {licenseOptions.map((option) => (
                <button
                  key={option.count}
                  type="button"
                  className={`p-3 border rounded-lg text-left transition-all ${
                    selectedPlan === option.count.toString()
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => {
                    setSelectedPlan(option.count.toString());
                    fetchPreviewFees(option.count);
                  }}
                >
                  <div className="font-medium">{option.label}</div>
                  <div className="text-sm text-gray-600">
                    {option.count} user license{option.count > 1 ? 's' : ''}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Pricing Display */}
          {selectedPlan && (
            <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h3 className="font-medium text-gray-900 mb-3">Pricing Details</h3>
              {previewLoading ? (
                <div className="text-sm text-gray-600">Loading pricing...</div>
              ) : previewData[selectedPlan] ? (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">License Count:</span>
                    <span className="font-medium">{selectedPlan}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Price:</span>
                    <span className="font-medium text-lg">
                      ${(previewData[selectedPlan].pricing?.total / 100).toFixed(2)}
                    </span>
                  </div>
                  {previewData[selectedPlan].creditAllocation && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Credits per License:</span>
                        <span className="font-medium">
                          {previewData[selectedPlan].creditAllocation.creditsPerLicense?.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Credits:</span>
                        <span className="font-medium">
                          {previewData[selectedPlan].creditAllocation.totalCreditsToAllocate?.toLocaleString()}
                        </span>
                      </div>
                    </>
                  )}
                  {previewData[selectedPlan].proRataDetails && (
                    <div className="pt-2 border-t border-gray-300">
                      <p className="text-xs text-gray-500">
                        {previewData[selectedPlan].proRataDetails.description}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-red-600">
                  Failed to load pricing. Please try again.
                </div>
              )}
            </div>
          )}
        </div>

        {/* Information Note */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
            <p className="text-sm text-gray-700">
              Purchased licenses will be added to your company's license pool.
              When admins invite new users, they will automatically use licenses
              from this pool.
            </p>
          </div>
        </div>

        {/* Payment Method Section */}
        <div className="space-y-3">
          <h3 className="font-medium">Payment Method</h3>
          {paymentMethods.length > 0 ? (
            <div className="space-y-2">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedPaymentMethod?.id === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedPaymentMethod(method)}
                >
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="text-sm font-medium">
                        •••• •••• •••• {method.last4}
                        {method.brand && ` (${method.brand.toUpperCase()})`}
                      </p>
                      {method.expiryMonth && method.expiryYear && (
                        <p className="text-xs text-gray-600">
                          Expires {method.expiryMonth}/{method.expiryYear}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 border border-red-200 rounded-lg bg-red-50">
              <p className="text-sm text-red-600">
                No payment methods available. Please add a payment method first.
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t h-full items-center">
          <Button onClick={close}>
            Cancel
          </Button>
          <Button
            className="bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium justify-center h-8"
            onClick={handlePurchase}
            loading={purchaseLoading}
            disabled={!selectedPaymentMethod || !selectedPlan || previewLoading}
            icon={<CreditCard className="w-4 h-4" />}
          >
            Purchase {selectedPlan} License{selectedPlan !== '1' ? 's' : ''}
            {previewData[selectedPlan]?.pricing?.total &&
              ` - $${(previewData[selectedPlan].pricing.total / 100).toFixed(2)}`
            }
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PurchaseLicenseSection;
