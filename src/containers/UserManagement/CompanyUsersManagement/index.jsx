import { useState } from 'react';
import UserSummaryCards from './UserSummaryCards';
import UserTable from './UserTable';
import { useUsers } from '..';
import { Button, Skeleton } from 'antd';
import { UserDetailsPage } from './UserDetail';
import { AddUserModal } from './AddNewUser';
import { RestyledModal } from '../../page/CreditManagementCompany/PurchaseCreditSection';

const CompanyUsersManagement = () => {
  const {
    users,
    populateUsers,
    setSearchText,
    loading,
    searchValue,
    setSearchValue,
    rawUsers,
    setRawUsers,
    setUsers,
  } = useUsers();

  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedRoleFilter, setSelectedRoleFilter] = useState('All');
  const [openAddNewUser, setOpenAddNewUser] = useState(false);

  const showAddNewUserModal = () => setOpenAddNewUser(true);
  const closeAddNewUserModal = () => setOpenAddNewUser(false);

  const handleUserClick = (user) => {
    setSelectedUser(user);
  };

  const handleRoleCardClick = (role) => {
    setSelectedRoleFilter(role);
  };
  return (
    <div className="h-full bg-gray-50 p-6">
      {!selectedUser && (
        <div className="mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <span className="p-1 bg-teal-100 rounded text-teal-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </span>
                  User Management
                </h1>
                <p className="text-gray-500 mt-1">
                  Manage users, roles, and permissions across your organization
                </p>
              </div>
              <Button
                onClick={showAddNewUserModal}
                className="bg-teal-500 text-white rounded-lg hover:bg-teal-600 flex items-center transition-colors"
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="16" />
                    <line x1="8" y1="12" x2="16" y2="12" />
                  </svg>
                }
              >
                Add User
              </Button>
            </div>
          </div>

          {/* Summary Cards */}
          {!loading && (
            <UserSummaryCards
              onRoleClick={handleRoleCardClick}
              selectedRole={selectedRoleFilter}
              users={rawUsers}
            />
          )}
          {loading && (
            <Skeleton active paragraph={{ rows: 2 }} className="mt-4" />
          )}

          {/* User Table */}
          <div className="mt-8">
            {!loading && (
              <UserTable
                selectedRoleFilter={selectedRoleFilter}
                onUserClick={handleUserClick}
                users={users}
                rawUsers={rawUsers}
                setUsers={setUsers}
                populateUsers={populateUsers}
              />
            )}
            {loading && (
              <Skeleton active paragraph={{ rows: 10 }} className="mt-4" />
            )}
          </div>
        </div>
      )}
      {selectedUser && (
        <UserDetailsPage
          user={selectedUser}
          back={() => handleUserClick(null)}
        />
      )}
      {/* Add New User Modal */}
      <RestyledModal
        open={openAddNewUser}
        onCancel={closeAddNewUserModal}
        footer={null}
      >
        <AddUserModal onClose={closeAddNewUserModal} />
      </RestyledModal>
    </div>
  );
};

export default CompanyUsersManagement;
