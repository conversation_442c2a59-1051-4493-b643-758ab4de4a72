import {
  AppstoreAddOutlined,
  BankOutlined,
  QuestionOutlined,
  TeamOutlined,
  UserOutlined,
  UserSwitchOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { getMetrics } from '../../services/users';
import { useEffect, useState } from 'react';
import { Skeleton, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { SALES, SUPER_ADMIN, userRole } from '../../constants/common.constant';
import { USER_STATUS } from '../CompanyManagement/ManageEmployees';
import clsx from 'clsx';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';

const UserStats = ({
  users,
  handleChangeUserRoleView,
  pendingCompanies = [],
  selectedCompany = null,
}) => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [roleStats, setRoleStats] = useState([]);
  const [selectedKeyCode, setSelectedKeyCode] = useState('');

  const currentUserRole = getUserViewAsRole();
  const isSuperAdmin =
    currentUserRole === SUPER_ADMIN || currentUserRole === SALES; // Sales and Super Admin can see all users

  const {
    data: { organizationsCount, usersCount },
    isFetching: isFetchingUsersMetrics,
  } = useQuery(['USERS_METRICS'], {
    queryFn: async () => {
      const { data } = await getMetrics();
      setCompanies([...data.organizations]);
      return data;
    },
    initialData: { organizationsCount: 0, usersCount: 0 },
  });

  useEffect(() => {
    if (users?.length > 0) {
      const lengthFilteredByField = (fieldName) =>
        users?.filter((user) => user?.role?.name === fieldName).length;

      const pendingUsers = users?.filter(
        (user) => user?.status === USER_STATUS.PENDING
      ).length;

      const roleStatsTemp = [
        ...(isSuperAdmin
          ? [
              {
                keyCode: SALES,
                roleName: 'Sales',
                total: lengthFilteredByField('Sales'),
                color: '#006769',
                icon: (
                  <UserSwitchOutlined className="text-[#006769] mb-4 text-2xl font-bold" />
                ),
              },
            ]
          : []),
        {
          keyCode: userRole.ADMIN,
          roleName: 'Admin',
          total: lengthFilteredByField('Admin'),
          color: '#40A578',
          icon: (
            <UsergroupAddOutlined className="text-[#40A578] mb-4 text-2xl font-bold" />
          ),
        },
        {
          keyCode: userRole.MANAGEMENT,
          roleName: 'Management',
          total: lengthFilteredByField('Management'),
          color: '#41b4b6',
          icon: (
            <TeamOutlined className="text-[#41b4b6] mb-4 text-2xl font-bold" />
          ),
        },
        {
          keyCode: userRole.BASIC_USER,
          roleName: 'Basic User',
          total: lengthFilteredByField('Basic User'),
          color: '#9DDE8B',
          icon: (
            <UserOutlined className="text-[#9DDE8B] mb-4 text-2xl font-bold" />
          ),
        },
      ];
      setRoleStats([...roleStatsTemp]);
    }
  }, [users]);

  return (
    <div
      className={clsx(
        'w-full pb-4 grid  ',
        isSuperAdmin ? 'grid-cols-7 gap-3' : 'grid-cols-4 gap-6'
      )}
    >
      {isFetchingUsersMetrics && (
        <div>
          <Skeleton active />
        </div>
      )}
      {!isFetchingUsersMetrics && (
        <>
          <div className=" bg-white col-span-1 p-4 rounded-md shadow-md transition-transform duration-300 hover:scale-105">
            <AppstoreAddOutlined className="text-[#1b254b] mb-4 text-2xl font-bold" />

            <div className="">
              <div className="flex flex-col gap-2">
                <span className="text-[#a3a3a3] md:text-base text-sm line-clamp-1 font-medium">
                  Total Users
                </span>
                <span className="text-[#1b254b] text-4xl font-semibold">
                  {usersCount}
                </span>
              </div>
            </div>
          </div>

          {isSuperAdmin && (
            <Tooltip title="Total active companies">
              <div
                onClick={() => navigate(`/user-management/company`)}
                className="cursor-pointer bg-white col-span-1 p-4 rounded-md shadow-md transition-transform duration-300 hover:scale-105"
              >
                <BankOutlined className="text-[#1b254b] mb-4 text-2xl font-bold" />

                <div className="">
                  <div className="flex flex-col gap-2">
                    <span className="text-[#a3a3a3] md:text-base text-sm line-clamp-1 font-medium">
                      Companies
                    </span>
                    <span className="text-[#1b254b] text-4xl font-semibold">
                      {organizationsCount}
                    </span>
                  </div>
                </div>
              </div>
            </Tooltip>
          )}
        </>
      )}
      {roleStats?.length === 0 &&
        Array.from({
          length: isSuperAdmin ? 4 : 4,
        }).map((i) => <Skeleton active />)}
      {roleStats?.length > 0 &&
        roleStats?.map((role) => (
          <div
            onClick={() => {
              // if (!selectedCompany && isSuperAdmin) return;
              if (selectedKeyCode === role.keyCode) {
                setSelectedKeyCode('');
                handleChangeUserRoleView('ALL');
              } else {
                setSelectedKeyCode(role.keyCode);
                handleChangeUserRoleView(role.keyCode);
              }
            }}
            className={clsx(
              ' bg-white col-span-1 p-4 rounded-md shadow-md cursor-pointer transition-transform duration-300 hover:scale-105',
              selectedKeyCode === role.keyCode && `border-2`
            )}
            style={
              selectedKeyCode === role.keyCode
                ? {
                    borderColor: role.color,
                  }
                : {}
            }
            key={role.roleName}
          >
            {role.icon}
            <div className="">
              <div className="flex flex-col gap-2">
                <span
                  className={`text-[${role.color}] md:text-base text-sm line-clamp-1 font-medium`}
                >
                  {role.roleName}
                </span>
                <span className={`text-[${role.color}] text-4xl font-semibold`}>
                  {role.total}
                </span>
              </div>
            </div>
          </div>
        ))}

      {isSuperAdmin && (
        <Tooltip title="Total pending companies">
          <div
            onClick={() => navigate(`/user-management/company?status=pending`)}
            className="bg-white col-span-1 p-4 rounded-md shadow-md transition-transform duration-300 cursor-pointer hover:scale-105"
          >
            <QuestionOutlined className="text-[#FFA55D] mb-4 text-2xl font-bold" />

            <div className="">
              <div className="flex flex-col gap-2">
                <span className="text-[#FFA55D] md:text-base text-sm line-clamp-1 font-medium">
                  Pending
                </span>
                <span className="text-[#FFA55D] text-4xl font-semibold">
                  {pendingCompanies?.length}
                </span>
              </div>
            </div>
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default UserStats;
