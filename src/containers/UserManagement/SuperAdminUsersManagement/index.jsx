import { useState } from 'react';
import { Router } from 'react-router-dom';
import SuperAdminSummaryCards from './SuperAdminSummaryCards';
import { CompaniesTable } from './CompaniesTable';

const SuperAdminUsersManagement = () => {
  //   const router = Router();
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const handleCardClick = (filter) => {
    setSelectedFilter(filter);
    // Here you would implement the actual filtering logic
    console.log('Filtering by:', filter);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleCompaniesListView = () => {
    // router.push('/companies');
  };
  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              User Management
              <svg
                className="w-6 h-6 text-teal-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </h1>
            {selectedFilter !== 'all' && (
              <p className="text-sm text-gray-500 mt-1">
                Showing results filtered by:{' '}
                <span className="font-medium text-teal-600">
                  {selectedFilter}
                </span>
              </p>
            )}
          </div>
          <button
            onClick={handleCompaniesListView}
            className="px-3 py-1.5 bg-teal-500 text-white rounded-md hover:bg-teal-600 transition-colors text-sm"
          >
            Companies List
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <SuperAdminSummaryCards
        onCardClick={handleCardClick}
        selectedFilter={selectedFilter}
      />

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative max-w-md">
          <input
            type="text"
            placeholder="Search company by name..."
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 w-full"
            value={searchQuery}
            onChange={handleSearch}
          />
          <svg
            className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <button className="absolute right-3 top-2 px-3 py-1 bg-teal-500 text-white text-xs rounded hover:bg-teal-600 transition-colors">
            Search
          </button>
        </div>
      </div>

      {/* Companies Table */}
      <CompaniesTable
        searchQuery={searchQuery}
        selectedFilter={selectedFilter}
      />
    </div>
  );
};

export default SuperAdminUsersManagement;
