import React from 'react';
import { useState } from 'react';

const companies = [
  {
    id: 1,
    name: 'test',
    website: '-',
    owner: '-',
    telephone: '-',
    companySize: 'N/A Employees',
    status: 'Approved',
    logo: 'T',
    logoColor: 'bg-gray-500',
    industry: 'Technology',
    sizeCategory: 'Under 100',
  },
  {
    id: 2,
    name: 'Boeing',
    website: 'http://www.boeing.com',
    owner: '-',
    telephone: '******-473-2016',
    companySize: '171000 Employees',
    status: 'Approved',
    logo: 'B',
    logoColor: 'bg-blue-600',
    industry: 'Technology',
    sizeCategory: '5000+',
  },
  {
    id: 3,
    name: 'Business Insider',
    website: 'http://www.businessinsider.com',
    owner: 'Bosch Viet Nam',
    telephone: '******-376-6113',
    companySize: '9700 Employees',
    status: 'Approved',
    logo: 'B<PERSON>',
    logoColor: 'bg-blue-500',
    industry: 'Entertainment',
    sizeCategory: '5000+',
  },
  {
    id: 4,
    name: '<PERSON> x WH',
    website: '-',
    owner: '-',
    telephone: '-',
    companySize: 'N/A Employees',
    status: 'Approved',
    logo: 'O',
    logoColor: 'bg-yellow-500',
    industry: 'Agency',
    sizeCategory: 'Under 100',
  },
  {
    id: 5,
    name: 'NAB Innovation Centre Vietnam',
    website: 'http://www.myworkdayjobs.com',
    owner: 'Bosch Viet Nam',
    telephone: '******-536-3015',
    companySize: '1400 Employees',
    status: 'Approved',
    logo: 'N',
    logoColor: 'bg-red-600',
    industry: 'Finance',
    sizeCategory: '1000 - 5000',
  },
  {
    id: 6,
    name: 'Unilever',
    website: 'http://www.unilever.com',
    owner: '777',
    telephone: '+44 20 1894 2790',
    companySize: '125000 Employees',
    status: 'Approved',
    logo: 'U',
    logoColor: 'bg-blue-700',
    industry: 'Cosmetics',
    sizeCategory: '5000+',
  },
  {
    id: 7,
    name: 'Wealthsimple',
    website: 'http://www.wealthsimple.com',
    owner: '777',
    telephone: '******-649-3193',
    companySize: '1600 Employees',
    status: 'Approved',
    logo: 'W',
    logoColor: 'bg-gray-700',
    industry: 'Finance',
    sizeCategory: '1000 - 5000',
  },
  {
    id: 8,
    name: 'Bosch Global Software Technologies',
    website: 'https://www.linkedin.com/company/bosch-...',
    owner: 'Bosch Viet Nam',
    telephone: '03357941140',
    companySize: '45555 Employees',
    status: 'Approved',
    logo: 'B',
    logoColor: 'bg-teal-600',
    industry: 'Technology',
    sizeCategory: '5000+',
  },
];

export function CompaniesTable({
  searchQuery,
  selectedIndustry,
  selectedSize,
}) {
  //   const router = useRouter();
  const [filteredCompanies, setFilteredCompanies] = useState(companies);

  const handleCompanyClick = (companyId) => {
    // router.push(`/company/${companyId}`);
  };

  const handleViewAllUsers = (e, companyId) => {
    e.stopPropagation();
    // router.push(`/company/${companyId}/users`);
  };

  // Filter companies based on all criteria
  React.useEffect(() => {
    let filtered = companies;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (company) =>
          company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          company.owner.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply industry filter
    if (selectedIndustry && selectedIndustry !== 'All Industries') {
      filtered = filtered.filter(
        (company) => company.industry === selectedIndustry
      );
    }

    // Apply size filter
    if (selectedSize && selectedSize !== 'All Sizes') {
      filtered = filtered.filter(
        (company) => company.sizeCategory === selectedSize
      );
    }

    setFilteredCompanies(filtered);
  }, [searchQuery, selectedIndustry, selectedSize]);

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="bg-gray-50 border-b border-gray-200">
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Company Name
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Owner
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Telephone
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Company Size
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              More
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {filteredCompanies.length > 0 ? (
            filteredCompanies.map((company) => (
              <tr
                key={company.id}
                onClick={() => handleCompanyClick(company.id)}
                className="hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div
                      className={`w-10 h-10 rounded-full ${company.logoColor} flex items-center justify-center text-white font-medium mr-4`}
                    >
                      {company.logo}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {company.name}
                      </div>
                      {company.website !== '-' && (
                        <div className="text-xs text-gray-500 truncate max-w-xs">
                          {company.website}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {company.owner}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {company.telephone}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-teal-600 font-medium">
                  <div className="flex items-center">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {company.companySize}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
                    {company.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <button
                    onClick={(e) => handleViewAllUsers(e, company.id)}
                    className="text-teal-600 hover:text-teal-800 border border-teal-600 hover:border-teal-800 px-3 py-1 rounded-md text-xs font-medium transition-colors"
                  >
                    View all Users →
                  </button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={6} className="px-6 py-12 text-center text-gray-500">
                <div className="flex flex-col items-center">
                  <svg
                    className="w-12 h-12 text-gray-300 mb-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"
                    />
                  </svg>
                  <p className="text-lg font-medium">No companies found</p>
                  <p className="text-sm">
                    Try adjusting your search or filter criteria
                  </p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
}
