import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getUserViewAsRole } from '../../../helpers/getUserViewAs';
import {
  SALES,
  SUPER_ADMIN,
  userRole,
} from '../../../constants/common.constant';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from 'antd';
import { getMetrics } from '../../../services/users';

const SuperAdminSummaryCards = ({
  onCardClick,
  selectedFilter,
  users,
  pendingCompanies,
  selectedCompany,
}) => {
  const totalUsers = users?.length || 0;
  const totalPending = pendingCompanies?.length || 0;
  const totalSales =
    users?.filter((user) => user?.role?.keyCode === SALES)?.length || 0;
  const totalAdmins =
    users?.filter((user) => user?.role?.keyCode === userRole.ADMIN)?.length ||
    0;
  const totalManagement =
    users?.filter((user) => user?.role?.keyCode === userRole.MANAGEMENT)
      ?.length || 0;
  const totalBasicUser =
    users?.filter((user) => user?.role?.keyCode === userRole.BASIC_USER)
      ?.length || 0;

  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [roleStats, setRoleStats] = useState([]);
  const [selectedKeyCode, setSelectedKeyCode] = useState('');

  const {
    data: { organizationsCount, usersCount },
    isFetching: isFetchingUsersMetrics,
  } = useQuery(['USERS_METRICS'], {
    queryFn: async () => {
      const { data } = await getMetrics();
      setCompanies([...data.organizations]);
      return data;
    },
    initialData: { organizationsCount: 0, usersCount: 0 },
  });

  const stats = [
    {
      title: 'Total Users',
      value: totalUsers,
      icon: <GridIcon />,
      color: 'gray',
      description: 'All users across companies',
      filter: 'ALL_USERS',
    },
    {
      title: 'Companies',
      value: organizationsCount,
      icon: <BuildingIcon />,
      color: 'blue',
      description: 'Active companies',
      filter: 'ALL',
    },
    {
      title: 'Sales',
      value: totalSales,
      icon: <SalesIcon />,
      color: 'teal',
      description: 'Sales team members',
      filter: SALES,
    },
    {
      title: 'Admin',
      value: totalAdmins,
      icon: <AdminIcon />,
      color: 'green',
      description: 'Administrator access',
      filter: userRole.ADMIN,
    },
    {
      title: 'Management',
      value: totalManagement,
      icon: <ManagementIcon />,
      color: 'cyan',
      description: 'Management level',
      filter: userRole.MANAGEMENT,
    },
    {
      title: 'Basic User',
      value: totalBasicUser,
      icon: <UserIcon />,
      color: 'lime',
      description: 'Standard access',
      filter: userRole.BASIC_USER,
    },
    {
      title: 'Pending',
      value: totalPending,
      icon: <PendingIcon />,
      color: 'orange',
      description: 'Pending approvals',
      filter: 'pending',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-4 mb-8">
      {!isFetchingUsersMetrics &&
        stats.map((stat, index) => (
          <StatCard
            key={index}
            {...stat}
            onCardClick={onCardClick}
            selectedFilter={selectedFilter}
          />
        ))}
      {isFetchingUsersMetrics && (
        <Skeleton
          active
          paragraph={{ rows: 4 }}
          title={false}
          className="w-full col-span-1 md:col-span-3 lg:col-span-7"
        />
      )}
    </div>
  );
};

export default SuperAdminSummaryCards;

function StatCard({
  title,
  value,
  icon,
  color,
  description,
  filter,
  onCardClick,
  selectedFilter,
}) {
  const colorClasses = {
    gray: 'text-gray-600 hover:text-gray-700',
    blue: 'text-blue-600 hover:text-blue-700',
    teal: 'text-teal-600 hover:text-teal-700',
    green: 'text-green-600 hover:text-green-700',
    cyan: 'text-cyan-600 hover:text-cyan-700',
    lime: 'text-lime-600 hover:text-lime-700',
    orange: 'text-orange-600 hover:text-orange-700',
  };

  const isSelected =
    selectedFilter === filter || (filter === 'ALL' && !selectedFilter);

  return (
    <div
      onClick={() => onCardClick(filter)}
      className={`bg-white rounded-lg border p-4 cursor-pointer transition-all duration-200 transform hover:scale-105 hover:shadow-lg hover:border-gray-300 ${
        isSelected
          ? 'border-teal-500 shadow-lg ring-2 ring-teal-200 bg-teal-50'
          : 'border-gray-200'
      }`}
    >
      <div className="flex flex-col items-center text-center">
        <div className={`mb-2 transition-colors ${colorClasses[color]}`}>
          {icon}
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
        <div
          className={`text-sm font-medium mb-1 transition-colors ${isSelected ? 'text-teal-600' : 'text-gray-900 hover:text-gray-700'}`}
        >
          {title}
        </div>
        <div className="text-xs text-gray-500">{description}</div>
        {isSelected && (
          <div className="mt-2 text-xs text-teal-600 font-medium">
            <svg
              className="inline w-3 h-3 mr-1"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
            Active Filter
          </div>
        )}
      </div>
    </div>
  );
}

function GridIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <rect x="3" y="3" width="7" height="7" />
      <rect x="14" y="3" width="7" height="7" />
      <rect x="14" y="14" width="7" height="7" />
      <rect x="3" y="14" width="7" height="7" />
    </svg>
  );
}

function BuildingIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z" />
      <path d="M6 12H4a2 2 0 0 0-2 2v8h20v-8a2 2 0 0 0-2-2h-2" />
    </svg>
  );
}

function SalesIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function AdminIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function ManagementIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function UserIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );
}

function PendingIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );
}
