/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import UserManagementTable from '../../components/UserManagementV2/UserManagementTable';
import UserManagementInvitation from '../../components/UserManagementV2/UserManagementInvitation';
import {
  getCompanies,
  getUsers,
  OrganizationStatusEnum,
} from '../../services/users';
import { HttpStatusCode } from 'axios';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import UserStats from './UserStats';
import './index.css';
import { Avatar, Button, notification, Table, Tag } from 'antd';
import { getLinkS3 } from '../../services/aws';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';
import {
  alphabetColors,
  SALES,
  SUPER_ADMIN,
} from '../../constants/common.constant';
import {
  PicRightOutlined,
  RightOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { UserGroupIcon } from '../../components/Sidebar/consts';
import { STATUS_COLOR } from '../CompanyManagement';
import Search from 'antd/es/input/Search';
import SuperAdminSummaryCards from './SuperAdminUsersManagement/SuperAdminSummaryCards';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { UserDetailsPage } from './CompanyUsersManagement/UserDetail';

const RestyledTable = styled(Table)`
  .ant-table-wrapper .ant-table-thead tr th::before {
    display: none !important;
  }
  .ant-table-cell {
    border-left: none !important;
    border-right: none !important;
  }
  .ant-table-wrapper .ant-table-thead {
    background-color: #f9fafb !important;
  }

  th.ant-table-cell {
    color: #6b7280 !important;
    background-color: #f9fafb !important;
  }
  .ant-table-cell {
    background-color: #f9fafb !important;
  }
  .ant-table-thead .ant-table-cell {
    background-color: #f9fafb !important;
    font-size: 0.75rem;
    line-height: 1rem;
  }
  .ant-table-thead .ant-table-cell::before {
    display: none !important;
  }
`;

export const useUsers = () => {
  const { viewerAsUser } = useViewAs();
  const [users, setUsers] = useState([]);
  const [rawUsers, setRawUsers] = useState([]);
  const [searchText, setSearchText] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const queryClient = useQueryClient();
  const { clearAuth } = useAuth();
  const [searchValue, setSearchValue] = useState('');

  const populateUsers = async () => {
    try {
      setLoading(true);
      const { data } = await getUsers(searchText, searchValue);
      if (data?.length > 0) {
        const dataWithAvatarUrl = await Promise.all(
          data?.map(async (user) => {
            if (!user?.avatarId) return { ...user };
            const { data } = await getLinkS3(user?.avatarId);
            return { ...user, avatarUrl: data };
          })
        );
        setLoading(false);
        setUsers([...dataWithAvatarUrl]);
        setRawUsers([...dataWithAvatarUrl]);
      } else {
        setLoading(false);
        setUsers(data);
        setRawUsers([...data]);
      }
    } catch (error) {
      const statusCode = error.response.status;
      if (statusCode === HttpStatusCode.Unauthorized) {
        queryClient.setQueryData(['CURRENT_USER'], null);
        clearAuth();
      }
      notification.error({
        description: 'Please try again later!',
      });
      setLoading(false);
    }
  };
  useEffect(() => {
    // Fetch users from API
    populateUsers();
  }, [viewerAsUser, searchText, searchValue]);

  return {
    users,
    loading,
    error,
    populateUsers,
    setSearchText,
    setSearchValue,
    searchValue,
    rawUsers,
    setRawUsers,
    setUsers,
  };
};

function UserManagement() {
  const {
    users,
    populateUsers,
    setSearchText,
    loading,
    searchValue,
    setSearchValue,
    rawUsers,
    setUsers
  } = useUsers();
  const navigate = useNavigate();
  const isSuperAdmin =
    getUserViewAsRole() === SUPER_ADMIN || getUserViewAsRole() === SALES;

  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [pendingCompanies, setPendingCompanies] = useState([]);
  const [activeCompanies, setActiveCompanies] = useState([]);
  const [selectedKeyCode, setSelectedKeyCode] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  const handleChangeUserRoleView = (key) => {
    if (key === 'ALL') {
      setSelectedKeyCode(null);
      setUsers([...rawUsers]);
    } else if (key === 'pending') {
      navigate('/user-management/company?status=pending');
    } else if (key === 'ALL_USERS') {
      populateUsers();
      setSelectedKeyCode(key);
    } else {
      const filteredUsers = rawUsers.filter(
        (user) => user?.role?.keyCode === key || user?.status === key
      );
      setSelectedKeyCode(key);
      setUsers([...filteredUsers]);
    }
  };

  const { data: rawActiveCompanies, isFetching } = useQuery(
    ['GET_ALL_COMPANIES'],
    {
      queryFn: async () => {
        try {
          const { data } = await getCompanies();
          if (data?.result?.length > 0) {
            const dataWithAvatarUrl = await Promise.all(
              data?.result?.map(async (comp) => {
                if (!comp?.companyAvatar) return { ...comp };
                const { data } = await getLinkS3(comp?.companyAvatar);
                return { ...comp, avatarUrl: data };
              })
            );
            const pendingCompaniesTemp = dataWithAvatarUrl?.filter(
              (company) => company?.status === OrganizationStatusEnum.PENDING
            );
            setPendingCompanies([...pendingCompaniesTemp]);
            const activeCompanies = dataWithAvatarUrl?.filter(
              (company) => company?.status === OrganizationStatusEnum.APPROVED
            );

            setActiveCompanies([...activeCompanies]);
            return [...activeCompanies];
          } else {
            setActiveCompanies([]);
            return [];
          }
        } catch (error) {
          console.log('Error fetching companies:', error);
          return [];
        }
      },
      initialData: [],
    }
  );

  const companyColumns = [
    {
      title: 'Company Name'.toUpperCase(),
      dataIndex: 'name',
      key: 'name',
      width: '35%',
      render: (text, record) => {
        return (
          <div className="flex items-center gap-2">
            {record?.avatarUrl && <Avatar size={40} src={record?.avatarUrl} />}
            {!record?.avatarUrl && (
              <Avatar
                size={40}
                style={{ backgroundColor: alphabetColors[text?.charAt(0)] }}
              >
                {text?.charAt(0)}
              </Avatar>
            )}
            <div className="flex flex-col">
              <span
                className="font-semibold text-sm line-clamp-1 text-gray-900"
                title={text || '-'}
              >
                {text || '-'}
              </span>
              {/* <span className="text-sm italic">{record?.website || '-'}</span> */}
              <a
                className="text-xs line-clamp-1 font-semibold text-gray-500"
                href={record?.companyWebsite || '#'}
                title={record?.companyWebsite || '-'}
                target="_blank"
              >
                {record?.companyWebsite || '-'}
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Owner'.toUpperCase(),
      dataIndex: 'companyOwner',
      key: 'companyOwner',
      width: '15%',
      render: (text, record) => (
        <span className="text-gray-500 text-sm font-semibold">
          {text || '-'}
        </span>
      ),
    },
    {
      title: 'Telephone'.toUpperCase(),
      dataIndex: 'phone',
      key: 'phone',
      width: '15%',
      render: (phone, record) => {
        return (
          <div className="font-semibold text-sm text-gray-500">{phone}</div>
        );
      },
    },
    {
      title: 'Company Size'.toUpperCase(),
      dataIndex: 'companySize',
      key: 'companySize',
      width: '15%',
      render: (text, record) => (
        <div className="flex gap-1 items-center text-cyan-600 font-medium">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {` ${text || 'N/A'} `}
          <span className="text-sm">Employees</span>
        </div>
      ),
    },
    {
      title: 'Status'.toUpperCase(),
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '15%',
      render: (status, record) => {
        return (
          <div className="w-full flex items-center justify-center">
            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 border border-green-200">
              {status?.toLowerCase()?.capitalize()}
            </span>
          </div>
        );
      },
    },
    {
      title: 'More'.toUpperCase(),
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      width: '15%',
      render: (status, record) => {
        return (
          <div className="w-full flex items-center justify-center hover:animate-wiggle text-gr">
            <Button type="primary" className="flex items-center">
              <span className="text-xs">View all Users</span>
              <RightOutlined />
            </Button>
          </div>
        );
      },
    },
  ];

  const backToSelectCompany = () => {
    setSelectedCompany(null);
  };

  const handleSearch = (e) => {
    const searchText = e.target.value;
    setSearchQuery(searchText);
    if (!searchText) {
      setActiveCompanies([...rawActiveCompanies]);
      return;
    }
    const newCompanies = activeCompanies.filter((company) =>
      company?.name.toLowerCase().includes(searchText?.toLowerCase())
    );
    setActiveCompanies([...newCompanies]);
  };

  return selectedUser ? (
    <div>
      <UserDetailsPage back={() => setSelectedUser(null)} user={selectedUser} />
    </div>
  ) : (
    <div className="relative">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              User Management
              <svg
                className="w-6 h-6 text-teal-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </h1>
            {/* {selectedFilter !== 'all' && (
              <p className="text-sm text-gray-500 mt-1">
                Showing results filtered by:{' '}
                <span className="font-medium text-teal-600">
                  {selectedFilter}
                </span>
              </p>
            )} */}
          </div>
          <button
            onClick={() => navigate(`/user-management/company`)}
            className="px-3 py-1.5 bg-teal-500 text-white rounded-md hover:bg-teal-600 transition-colors text-sm"
          >
            Companies List
          </button>
        </div>
      </div>
      <SuperAdminSummaryCards
        onCardClick={handleChangeUserRoleView}
        selectedFilter={selectedKeyCode}
        users={rawUsers}
        pendingCompanies={pendingCompanies}
        selectedCompany={selectedCompany}
      />
      <div className="">
        {!isSuperAdmin && (
          <>
            <UserManagementInvitation
              rePopulateUsers={populateUsers}
              setSearchText={setSearchText}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
              loading={loading}
            />
            <UserManagementTable
              users={users}
              reloadUserList={populateUsers}
              loadingTable={loading}
              setSelectedUser={setSelectedUser}
            />
          </>
        )}
        {isSuperAdmin &&
          (selectedCompany ||
          selectedKeyCode ||
          selectedKeyCode === 'ALL_USERS' ? (
            <>
              <UserManagementInvitation
                rePopulateUsers={populateUsers}
                setSearchText={setSearchText}
                searchValue={searchValue}
                setSearchValue={setSearchValue}
                loading={loading}
                selectedCompany={selectedCompany}
                backToSelectCompany={backToSelectCompany}
              />
              <UserManagementTable
                users={users}
                reloadUserList={populateUsers}
                loadingTable={loading}
                setSelectedUser={setSelectedUser}
              />
            </>
          ) : (
            <div
            // className="search-table-new-design-container"
            >
              <div className="mb-6">
                <div className="relative max-w-md">
                  <input
                    type="text"
                    placeholder="Search company by name..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 w-full"
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                  <svg
                    className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                  <button className="absolute right-3 top-2 px-3 py-1 bg-teal-500 text-white text-xs rounded hover:bg-teal-600 transition-colors">
                    Search
                  </button>
                </div>
              </div>
              {/* <Search
                className="customize-search-container"
                allowClear
                placeholder="Search company by name..."
                enterButton={
                  <div className="flex items-center gap-2">
                    Search <SearchOutlined />
                  </div>
                }
                size="middle"
                onSearch={(searchText) => {
                  if (!searchText) {
                    setActiveCompanies([...rawActiveCompanies]);
                    return;
                  }
                  const newCompanies = activeCompanies.filter((company) =>
                    company?.name
                      .toLowerCase()
                      .includes(searchText?.toLowerCase())
                  );
                  setActiveCompanies([...newCompanies]);
                }}
              /> */}
              <RestyledTable
                dataSource={activeCompanies}
                columns={companyColumns}
                loading={isFetching}
                onRow={(record) => {
                  return {
                    onClick: () => {
                      setSelectedCompany({ ...record });
                    },
                    style: { cursor: 'pointer' },
                  };
                }}
              />
            </div>
          ))}
      </div>
    </div>
  );
}

export default UserManagement;
