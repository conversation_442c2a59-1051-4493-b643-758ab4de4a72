import {
  CameraOutlined,
  CaretRightOutlined,
  CloseOutlined,
  DoubleRightOutlined,
  FileGifOutlined,
  LoadingOutlined,
  PlusOutlined,
  SaveOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  AutoComplete,
  Avatar,
  Button,
  Collapse,
  Form,
  Image,
  Input,
  InputNumber,
  Select,
  Spin,
  Upload,
  message,
  notification,
} from 'antd';
import { Controller, useForm } from 'react-hook-form';
import { MODE, companyTypes } from '.';
import { useCallback, useEffect, useState } from 'react';
import {
  createNewCompany,
  updateCompany,
  uploadImage,
} from '../../services/users';
import { getLinkS3 } from '../../services/aws';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { getRoleName } from '../../components/UserManagementV2/UserManagementTable';
import { debounce, isArray } from 'lodash';
import {
  getCompanyDetail,
  getListCompanyLinkedin,
} from '../../services/linkedInFinder';
import { convertToBase64 } from '../../services/signatures';
import { urlToFile } from '../../helpers/util';
import { getListCompanies } from '../../services/employee';
import { getDetailCompanyById } from '../../services/companyFinder';

const stepConstant = {
  INPUT_COMPANY_NAME: 1,
  INPUT_DETAIL: 2,
};

const sourceConstant = {
  APOLLO: 'apollo',
  LINKEDIN: 'linkedIn',
};

const CompanyForm = ({
  closeModal,
  company,
  mode,
  refetch,
  handleSumit,
  showBHForm,
  employeeLoading,
}) => {
  const { handleSubmit, control, getValues, setValue, watch } = useForm();

  const [website, setWebsite] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(stepConstant.INPUT_COMPANY_NAME);

  const [imageUrl, setImageUrl] = useState('');
  const [imageId, setImageId] = useState('');
  const [staffAvatars, setStaffAvatars] = useState([]);
  const [suggestionLoading, setSuggestionLoading] = useState(false);
  const [overviewLoading, setOverviewLoading] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState([]);
  const [companySuggestionsRaw, setCompanySuggestionsRaw] = useState([]);

  const getCompaniesFromLinkedin = async (keywords) => {
    try {
      const payload = {
        type: 'classic',
        category: 'companies',
        keywords,
        pageSize: 5,
      };
      const { data } = await getListCompanyLinkedin(payload);
      if (data?.result?.data?.items?.length > 0 && !suggestionLoading) {
        const suggestions = data?.result?.data?.items?.map((item) => ({
          ...item,
          source: sourceConstant.LINKEDIN,
        }));

        return suggestions;
      } else {
        return [];
      }
    } catch (error) {
      console.log('error get company suggestions: ', error);
      return [];
    }
  };

  const getCompaniesFromApollo = async (keywords) => {
    try {
      const { data } = await getListCompanies({
        page: 1,
        limit: 5,
        searchText: keywords,
      });
      // setValue('address', selectedCompany?.location);
      if (data?.organizations?.length > 0 && !suggestionLoading) {
        const suggestions = data?.organizations?.map((item) => ({
          ...item,
          logo: item?.logo_url || '',
          profile_url: item?.website_url || '',
          industry: item?.industries?.[0] || '',
          location: item?.raw_address || '',
          companySize: item?.estimated_num_employees,
          source: sourceConstant.APOLLO,
        }));

        return suggestions;
      } else {
        return [];
      }
    } catch (error) {
      console.log('error get company suggestions: ', error);
      return [];
    }
  };

  const handleGetCompanySuggestions = async (keywords) => {
    setSuggestionLoading(true);
    try {
      const [apolloSuggestions, linkedInSuggestions] = await Promise.all([
        getCompaniesFromApollo(keywords),
        getCompaniesFromLinkedin(keywords),
      ]);
      let rawSuggestions = [];

      if (linkedInSuggestions?.length > 0) {
        rawSuggestions = [...linkedInSuggestions];
      } else {
        rawSuggestions = [...apolloSuggestions];
      }

      if (rawSuggestions?.length > 0) {
        setCompanySuggestionsRaw([...rawSuggestions]);
        const options = rawSuggestions.map((item) => ({
          value: item?.id,
          label: item?.name,
        }));
        setCompanySuggestions([...options]);
      } else {
        setCompanySuggestions([]);
      }

      setSuggestionLoading(false);
    } catch (error) {
      setSuggestionLoading(false);
      console.log('error get company suggestions: ', error);
    }
  };

  // Memoize the debounced function
  const debouncedGetCompanySuggestions = useCallback(
    debounce((text) => handleGetCompanySuggestions(text), 300), // Adjust debounce duration as needed
    []
  );

  const uploadFile = async (file) => {
    let formData = new FormData();
    formData.append('avatar', file);
    return await uploadImage(formData);
  };

  const props = {
    name: 'file',
    multiple: false,
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        console.log('file: ', file);
        const res = await uploadFile(file);

        setImageId(res?.data?.result?.data?.imgId);
        setImageUrl(res?.data?.result?.data?.url);
        onSuccess('ok');
      } catch (error) {
        console.log('error upload image: ', error);
        onError(error);
      }
    },
  };

  const fetchImageUrl = async (avatarId) => {
    // if (avatarId?.includes('http')) {
    //   setImageUrl(avatarId);
    //   return;
    // }
    try {
      const { data } = await getLinkS3(avatarId);
      setImageUrl(data);
    } catch (error) {
      setImageUrl(zileoLogo);
    }
  };

  useEffect(() => {
    if (!company?.companyAvatar) return;
    fetchImageUrl(company?.companyAvatar);
  }, [company?.companyAvatar]);

  const create = async (payload) => {
    await createNewCompany(payload)
      .then((res) => {
        notification.success({
          description: 'Company created!',
        });
        if (refetch) {
          refetch();
        }
        if (closeModal) {
          closeModal();
        }
        console.log('create new company: ', res);
      })
      .catch((err) => {
        notification.error({
          description: 'Company failed!',
        });

        console.log('create new company err: ', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const update = async (payload) => {
    await updateCompany(payload, company?.id)
      .then((res) => {
        notification.success({
          description: 'Company create updated!',
        });

        refetch();
        closeModal();
        console.log('Update company: ', res);
      })
      .catch((err) => {
        notification.error({
          description: 'Company update failed!',
        });
        console.log('Update company err: ', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = async (values) => {
    console.log('values: ', values);
    if (!values?.name?.trim()) {
      notification.error({
        description: 'Company Name is required!',
      });
      return;
    }

    setLoading(true);
    const payload = {
      ...values,
      companyTypes: [values?.companyTypes],
      companyWebsite: website || '',
      companyAvatar: imageId || '',
      companyAvatarUrl: imageUrl || '',
      // companyAdmins: getValues('companyAdmins')?.join(',') || '',
      // tags: getValues('tags')?.join(',') || '',
    };
    if (handleSumit) {
      setLoading(false);
      await handleSumit(payload);
      return;
    }
    if (!values?.id) {
      create(payload);
    } else {
      update(payload);
    }
  };

  const getStaffAvatars = async () => {
    if (company?.users?.length > 0) {
      const staffAvatarIdList = company?.users
        ?.map((user) => user?.avatarId || '')
        .filter((id) => id);
      console.log('staffAvatarIdList: ', staffAvatarIdList);
      const staffAvatarUrlList = Promise.allSettled(
        staffAvatarIdList?.map(async (avtId) => {
          const { data } = await getLinkS3(avtId);
          return { avtId, url: data || '' };
        })
      );
      setStaffAvatars([
        ...(await staffAvatarUrlList).map((item) => item.value),
      ]);
    }
  };

  useEffect(() => {
    if (!company) return;
    setStep(stepConstant.INPUT_DETAIL);
    for (const property in company) {
      setValue(property, company[property]);

      if (property === 'companyWebsite') {
        setWebsite(company[property]);
      }
      if (property === 'tags' || property === 'companyAdmins') {
        setValue(
          property,
          isArray(company[property])
            ? company[property]
            : company[property]?.split(',') || []
        );
      }
      if (property === 'companyAvatar') {
        setImageId(company[property]);
      }
      // if (property === 'companyAvatar' && property['companyAvatar'] ) {
      //   setImageId(company[property]);
      // }
      getStaffAvatars();
    }
  }, [company]);

  const uploadButton = (
    <button className="flex flex-col justify-center items-center py-4 border border-dashed w-full rounded-md">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div className="mt-2 font-Montserrat">Upload</div>
    </button>
  );

  const items = [
    {
      key: 'staff-list',
      label: (
        <div className="text-lg font-semibold py-3">
          Company Staff(s) on Zileo
        </div>
      ),
      children: (
        <div className="font-Montserrat flex flex-col w-full w-fit">
          {company?.users?.map((user) => (
            <a
              onClick={(e) => e.stopPropagation()}
              href={`#`}
              target="_blank"
              className="py-2 border-b flex flex-col gap-1"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Avatar
                    src={
                      staffAvatars.find((item) => item.avtId === user?.avatarId)
                        ?.url || ''
                    }
                  >
                    {Array.from(user?.username || 'a')[0]}
                  </Avatar>
                  <div className="flex flex-col">
                    <span className="text-sm font-semibold">
                      {user?.email || '-'}
                    </span>
                    <span className="text-xs italic">{user?.fullName}</span>
                  </div>
                </div>
                {getRoleName(user?.role?.name)}
              </div>
            </a>
          ))}
        </div>
      ),
    },
  ];

  const handleSaveCompanyAvt = async (url) => {
    try {
      const { data } = await convertToBase64(url, true);
      setImageId(data?.result);

      return url;
    } catch (error) {
      // notification.error({
      //   description: 'Error when retriving company avatar!',
      // });
      console.error('handleSaveCompanyAvt', error);
      return '';
    }
  };

  const handleGetCompanyDetail = async (
    organizationId,
    type = sourceConstant.APOLLO
  ) => {
    setOverviewLoading(true);
    try {
      const { data } =
        type === sourceConstant.LINKEDIN
          ? await getCompanyDetail(organizationId)
          : await getDetailCompanyById({
              organizationId,
            });

      const company = data?.result?.data || data?.organization;

      setOverviewLoading(false);
      return company;
    } catch (error) {
      setOverviewLoading(false);
      return null;
    }
  };

  const renderOption = (option) => {
    const selectedCompany = companySuggestionsRaw.find(
      (item) => item?.id === option.value
    );

    return (
      <div className="grid p-2">
        <div className="flex justify-between">
          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
            {selectedCompany?.name}
            <br />
            <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
              {selectedCompany?.industry || '-'}
            </span>
          </span>

          <Avatar
            className="w-10 h-10"
            src={selectedCompany?.logo ? `${selectedCompany?.logo}` : ''}
            shape="square"
          ></Avatar>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-[#f9fafb] w-full mb-5 h-full">
      {step === stepConstant.INPUT_COMPANY_NAME && (
        <div className="w-full flex flex-col gap-10 justify-center items-center h-full">
          <Image src={zileoLogo} preview={false} width={300} />
          <div className="flex items-center justify-center gap-2 w-full">
            <AutoComplete
              onSelect={async (value, label) => {
                const selectedCompany = companySuggestionsRaw.find(
                  (item) => item?.id === value
                );
                setValue('name', selectedCompany?.name);
                setValue('address', selectedCompany?.location);
                setValue('tags', [selectedCompany?.industry]);
                if (selectedCompany?.companySize) {
                  setValue('companySize', selectedCompany?.companySize);
                }
                if (selectedCompany?.phone) {
                  setValue('phone', selectedCompany?.phone);
                }

                if (selectedCompany?.summary) {
                  setValue('overview', selectedCompany?.summary);
                }

                setWebsite(selectedCompany?.profile_url || '');

                const companyAvtUrl = await handleSaveCompanyAvt(
                  selectedCompany?.logo
                );
                setImageUrl(companyAvtUrl);
                const companyDetail = await handleGetCompanyDetail(
                  selectedCompany?.id,
                  selectedCompany?.source
                );

                const companyOverview =
                  companyDetail?.short_description ||
                  companyDetail?.description ||
                  '';
                setValue('overview', companyOverview);
              }}
              suffixIcon={suggestionLoading ? <Spin size="small" /> : <></>}
              options={companySuggestions}
              onSearch={debouncedGetCompanySuggestions}
              optionRender={renderOption}
              onChange={(value) => {
                setValue('name', value);
              }}
              value={watch('name')}
              className="w-1/3"
              placeholder="Please input your company Name"
            />
            <Button
              disabled={suggestionLoading || !watch('name')?.trim()}
              onClick={() => setStep(stepConstant.INPUT_DETAIL)}
              className="bg-white"
              type="dashed"
              icon={<DoubleRightOutlined />}
            >
              Next
            </Button>
          </div>
        </div>
      )}
      {step === stepConstant.INPUT_DETAIL && (
        <div className="grid grid-cols-5 gap-5">
          <div className="col-span-3 bg-white border">
            <div className="flex justify-center">
              <Form
                onFinish={handleSubmit(onSubmit)}
                layout="vertical"
                className="font-Montserrat grid gap-3 w-11/12 p-4"
              >
                {/* <div className="border-b py-3 text-lg font-semibold">
                Company Detail
              </div> */}
                {/* <div className="grid grid-rows-2 gap-3 w-11/12"> */}
                <div className="grid grid-cols-10 items-center  max-h-[50rem] overflow-y-auto pr-2">
                  <div className="flex flex-col gap-2 pb-5 col-span-4">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Company Name</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <AutoComplete
                            onSelect={async (value, label) => {
                              const selectedCompany =
                                companySuggestionsRaw.find(
                                  (item) => item?.id === value
                                );
                              setValue('name', selectedCompany?.name);
                              setValue('address', selectedCompany?.location);
                              setValue('tags', [selectedCompany?.industry]);
                              if (selectedCompany?.companySize) {
                                setValue(
                                  'companySize',
                                  selectedCompany?.companySize
                                );
                              }
                              if (selectedCompany?.phone) {
                                setValue('phone', selectedCompany?.phone);
                              }

                              if (selectedCompany?.summary) {
                                setValue('overview', selectedCompany?.summary);
                              }

                              setWebsite(selectedCompany?.profile_url || '');
                              const companyAvtUrl = await handleSaveCompanyAvt(
                                selectedCompany?.logo
                              );
                              setImageUrl(companyAvtUrl);
                              const companyDetail =
                                await handleGetCompanyDetail(
                                  selectedCompany?.id,
                                  selectedCompany?.source
                                );

                              const companyOverview =
                                companyDetail?.short_description ||
                                companyDetail?.description ||
                                '';
                              setValue('overview', companyOverview);
                            }}
                            suffixIcon={
                              suggestionLoading ? <Spin size="small" /> : <></>
                            }
                            options={companySuggestions}
                            onSearch={debouncedGetCompanySuggestions}
                            optionRender={renderOption}
                            placeholder="Company Name"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // // required
                          />
                        )}
                        name="name"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 pb-5 col-span-4">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Owner Name</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            placeholder="Owner Name"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="companyOwner"
                        control={control}
                      />
                    </span>
                  </div>

                  <div className="flex flex-col gap-2 pb-5 col-span-2">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Company Size</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <InputNumber
                            min={1}
                            placeholder="Size"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="companySize"
                        control={control}
                      />
                    </span>
                  </div>

                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">
                        Company Overview{' '}
                        {overviewLoading && (
                          <Spin className="ml-2" size="small" />
                        )}
                      </span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input.TextArea
                            disabled={overviewLoading}
                            min={1}
                            rows={4}
                            placeholder="Company Overview"
                            {...field}
                            // required
                          />
                        )}
                        name="overview"
                        control={control}
                      />
                    </span>
                  </div>

                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Note</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input.TextArea
                            min={1}
                            rows={2}
                            placeholder="Input some notes to super admin here"
                            {...field}
                            // required
                          />
                        )}
                        name="note"
                        control={control}
                      />
                    </span>
                  </div>
                  {/* <div className="flex flex-col gap-2 pb-5 col-span-3">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">License</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            placeholder="License"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="license"
                        control={control}
                      />
                    </span>
                  </div> */}
                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Type of company</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Select
                            placeholder="Company Type"
                            defaultValue={company?.companyTypes}
                            {...field}
                            disabled={mode === MODE.VIEW}
                            options={companyTypes}
                            className="w-full font-Montserrat"
                          />
                        )}
                        name="companyTypes"
                        control={control}
                      />
                    </span>
                  </div>

                  <div className="flex flex-col gap-2 pb-5 col-span-7">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Admin Email</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            placeholder="Admin Email"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="companyEmail"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 pb-5 col-span-3">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Phone</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            placeholder="Phone"
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="phone"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Number of Licenses</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <InputNumber
                            min={1}
                            placeholder="Number of Licenses"
                            type="number"
                            defaultValue={company?.numberOfLicenses || 1}
                            {...field}
                            className="w-full font-Montserrat"
                          />
                        )}
                        name="numberOfLicenses"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Address</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            placeholder="Address"
                            defaultValue={company?.address}
                            {...field}
                            disabled={mode === MODE.VIEW}
                            className="w-full font-Montserrat"
                          />
                        )}
                        name="address"
                        control={control}
                      />
                    </span>
                  </div>

                  {/* <div className="flex flex-col gap-2 pb-5 col-span-10">
                  <div className="flex gap-2 items-center">
                    <span className="text-sm">Admin Emails</span>
                  </div>
                  <span className="text-[#4d6276]">
                    <Controller
                      render={({ field }) => (
                        <Select
                          {...field}
                          mode="tags"
                          style={{
                            width: '100%',
                          }}
                          className="w-full font-Montserrat"
                          placeholder="Company Admins"
                          disabled={mode === MODE.VIEW}
                        />
                      )}
                      name="companyAdmins"
                      control={control}
                    />
                  </span>
                </div> */}

                  <div className="flex flex-col gap-2 pb-5 col-span-10">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Tags</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          // <Select
                          //   defaultValue={company?.companyTypes}
                          //   {...field}
                          //   disabled={mode === MODE.VIEW}
                          //   options={companyTypes}
                          //   className="w-full font-Montserrat"
                          // />
                          <Select
                            {...field}
                            mode="tags"
                            style={{
                              width: '100%',
                            }}
                            className="w-full font-Montserrat"
                            placeholder="Company Industries"
                            disabled={mode === MODE.VIEW}
                          />
                        )}
                        name="tags"
                        control={control}
                      />
                    </span>
                  </div>

                  {/* <div className="border-b py-3 text-lg font-semibold col-span-10">
                  Business Information
                </div> */}
                  {/* <div className="col-span-10 zileo-staff-container pb-7">
                  <Collapse
                    items={items}
                    bordered={false}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined rotate={isActive ? 90 : 0} />
                    )}
                    expandIconPosition="end"
                    defaultActiveKey={['staff-list']}
                  />
                </div> */}
                  <div className="flex w-full justify-end col-span-10 gap-4">
                    {mode !== MODE.VIEW && (
                      <Button
                        htmlType="submit"
                        type="primary"
                        className="Montserrat  flex items-center "
                        loading={loading || employeeLoading}
                        // onClick={showCompanyForm}
                      >
                        <SaveOutlined /> <span>Save & Next</span>
                      </Button>
                    )}
                    {/* <Button
                    htmlType="button"
                    className="Montserrat  flex items-center "
                    disabled={loading}
                    onClick={closeModal}
                  >
                    <CloseOutlined /> <span>Close</span>
                  </Button> */}
                  </div>
                </div>
              </Form>
            </div>
          </div>

          <div className="col-span-2 bg-white border">
            {/* <div className="border-b py-3 mx-6 text-lg font-semibold">
            Company Avatar
          </div> */}
            <div className="flex flex-col justify-center p-4 font-Montserrat gap-5">
              <div className="w-full">
                <div className="flex items-center w-full justify-start gap-4 mb-4">
                  <CameraOutlined />
                  <div className="flex flex-col">
                    <span className="text-base font-semibold">
                      The Logo of company
                    </span>
                    <span className="text-xs">Less than 4mb</span>
                  </div>
                </div>

                <div className="max-w-sm w-full">
                  <Upload
                    {...props}
                    className="h-10 "
                    rootClassName="customized-upload-component max-w-[10rem]"
                    disabled={mode === MODE.VIEW}
                  >
                    {imageUrl ? (
                      <div
                        className={` ${mode !== MODE.VIEW && 'avatar-container'} relative`}
                      >
                        <img
                          className="image"
                          src={imageUrl}
                          alt="avatar"
                          style={{ width: '100%' }}
                        />
                        <div className="hover-content">
                          <span className="font-Montserrat">
                            click to change
                          </span>
                        </div>
                      </div>
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </div>
              </div>

              <div className="flex flex-col gap-2">
                <div className="flex gap-2 items-center">
                  <span className="text-sm">Company Website</span>
                </div>
                <span className="text-[#4d6276]">
                  <Input
                    disabled={mode === MODE.VIEW}
                    // addonBefore={selectBefore}
                    className="w-full font-Montserrat"
                    value={website}
                    onChange={(ev) => setWebsite(ev.target.value)}
                  />
                </span>
              </div>

              {showBHForm && (
                <div className="grid grid-cols-10">
                  <div className="border-b py-3 text-lg font-semibold col-span-10">
                    Bullhorn Information
                  </div>
                  <div className="flex flex-col gap-2 col-span-5 py-5">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Client ID</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="bhClientId"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 col-span-5 py-5">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Client Secret</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input.Password
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="bhClientSecret"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 col-span-5 pb-5">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Username</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="bhUsername"
                        control={control}
                      />
                    </span>
                  </div>
                  <div className="flex flex-col gap-2 col-span-5 pb-5">
                    <div className="flex gap-2 items-center">
                      <span className="text-sm">Password</span>
                    </div>
                    <span className="text-[#4d6276]">
                      <Controller
                        render={({ field }) => (
                          <Input.Password
                            disabled={mode === MODE.VIEW}
                            className="w-11/12 font-Montserrat"
                            {...field}
                            // required
                          />
                        )}
                        name="bhPassword"
                        control={control}
                      />
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyForm;
