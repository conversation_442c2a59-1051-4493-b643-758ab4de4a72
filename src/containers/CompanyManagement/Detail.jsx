import {
  BankOutlined,
  CameraOutlined,
  CaretRightOutlined,
  CheckOutlined,
  CloseOutlined,
  CreditCardOutlined,
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  LeftOutlined,
  LoadingOutlined,
  PlusOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  Avatar,
  Button,
  Checkbox,
  Collapse,
  Dropdown,
  Image,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Spin,
  Table,
  Tag,
  Tooltip,
  Upload,
  message,
  notification,
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import zileoLogo from '../../assets/img/welcome/logo.png';
import './index.css';
import { getLinkS3 } from '../../services/aws';
import {
  getCompanyDetail,
  OrganizationStatusEnum,
  updateCompany,
  updateOnboardingCompanyStatus,
  uploadImage,
} from '../../services/users';
import { getRoleName } from '../../components/UserManagementV2/UserManagementTable';
import { useForm } from 'react-hook-form';
import {
  getUserViewAs,
  getUserViewAsLicense,
  getUserViewAsRole,
} from '../../helpers/getUserViewAs';
import { licenseType, SUPER_ADMIN } from '../../constants/common.constant';
import { STATUS_COLOR } from '.';
import ManageEmployees from './ManageEmployees';
import { HiStatusOffline, HiStatusOnline } from 'react-icons/hi';
import { tagRender } from '../../components/SearchDetailV2/NewSearchFilterComponent';
import CompanyDocuments from './CompanyDocuments';

export const companyTypes = [
  {
    label: 'Agriculture',
    value: 'Agriculture',
  },
  {
    label: 'B2B',
    value: 'B2B',
  },
  {
    label: 'Cosmetics',
    value: 'Cosmetics',
  },
  {
    label: 'Agency',
    value: 'Agency',
  },
  {
    label: 'Services',
    value: 'Services',
  },
  {
    label: 'Real Estate',
    value: 'Real Estate',
  },
  {
    label: 'Food & Beverages',
    value: 'Food & Beverages',
  },
  {
    label: 'Entertainment',
    value: 'Entertainment',
  },
  {
    label: 'Daily Used',
    value: 'Daily Used',
  },
  {
    label: 'Games & Recreation',
    value: 'Games & Recreation',
  },
  {
    label: 'Aeroplane',
    value: 'Aeroplane',
  },
  {
    label: 'Transportation',
    value: 'Transportation',
  },
];

const tabs = {
  COMPANY_DETAIL: 'COMPANY_DETAIL',
  EMPLOYEES_DETAIL: 'EMPLOYEES_DETAIL',
};

const googleKey = import.meta.env.VITE_KEY_GOOGLE;
const DetailCompany = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  const [showContent, setShowMoreContent] = useState(false);
  const [showPeople, setShowMorePeople] = useState(false);
  const [tab, setTab] = useState(tabs.COMPANY_DETAIL);
  const [isFetching, setIsFetching] = useState(false);
  const [dataDetail, setDataDetail] = useState();
  const [imageUrl, setImageUrl] = useState(zileoLogo);
  const [staffAvatars, setStaffAvatars] = useState([]);
  const [editingStatus, setEditingStatus] = useState(false);
  const [disable, setDisable] = useState(true);
  const [loadingEdit, setLoadingEdit] = useState(false);
  const [imageId, setImageId] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  const { setValue, getValues, watch } = useForm();

  const isStandardCompnany = getValues('license') === licenseType.STANDARD;

  const currentUserRole = getUserViewAsRole();
  const currentUserId = getUserViewAs();
  const isSuperAdmin = currentUserRole === SUPER_ADMIN;

  const getStaffAvatars = async () => {
    if (dataDetail?.users?.length > 0) {
      const staffAvatarIdList = dataDetail?.users
        ?.map((user) => user?.avatarId || '')
        .filter((id) => id);
      const staffAvatarUrlList = Promise.allSettled(
        staffAvatarIdList?.map(async (avtId) => {
          const { data } = await getLinkS3(avtId);
          return { avtId, url: data || '' };
        })
      );
      setStaffAvatars([
        ...(await staffAvatarUrlList).map((item) => item.value),
      ]);
    }
  };

  const customProps = {
    name: 'file',
    multiple: false,
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        console.log('file: ', file);
        let formData = new FormData();
        formData.append('avatar', file);
        const res = await uploadImage(formData);
        // console.log('res: ', res?.data?.result?.data?.imgId);
        setImageId(res?.data?.result?.data?.imgId);
        setImageUrl(res?.data?.result?.data?.url);
        onSuccess('ok');
      } catch (error) {
        console.log('error upload image: ', error);
        onError(error);
      }
    },
  };

  const update = async (payload) => {
    setLoadingEdit(true);
    await updateCompany(payload, dataDetail?.id)
      .then((res) => {
        notification.success({
          description: 'Company updated!',
        });

        handleGetDetailData();
        setEditingStatus(false);
        setDisable(true);
        setLoadingEdit(false);
      })
      .catch((err) => {
        setLoadingEdit(false);
        notification.error({
          description: 'Company update failed!',
        });
      });
  };

  const handleSaveData = async () => {
    try {
      const payload = {
        name: getValues('name'),
        companySize: getValues('companySize'),
        companyOwner: getValues('companyOwner'),
        licenseType: getValues('license'),
        companyTypes: getValues('companyTypes'),
        companyEmail: getValues('companyEmail'),
        phone: getValues('phone'),
        address: getValues('address'),
        companyAdmins: getValues('companyAdmins'),
        tags: getValues('tags'),
        bhClientId: getValues('bhClientId'),
        bhClientSecret: getValues('bhClientSecret'),
        bhUsername: getValues('bhUsername'),
        bhPassword: getValues('bhPassword'),
        id: dataDetail?.id,
        bhToken: getValues('bhToken'),
        companyWebsite: getValues('companyWebsite'),
        companyIndustries: getValues('companyIndustries'),
        companyAvatar: imageId ?? getValues('companyAvatar'),
      };

      const data = await update(payload);
    } catch (err) {
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const handleGetDetailData = async () => {
    try {
      setIsFetching(true);
      const { data } = await getCompanyDetail(id);
      console.log('data: ', data);
      setDataDetail(data?.result);
      setValue('id', data?.result?.id);
      setValue('name', data?.result?.name);
      setValue('bhClientId', data?.result?.bhClientId);
      setValue('bhClientSecret', data?.result?.bhClientSecret);
      setValue('bhUsername', data?.result?.bhUsername);
      setValue('bhPassword', data?.result?.bhPassword);
      setValue('bhToken', data?.result?.bhToken);
      setValue('companySize', data?.result?.companySize);
      setValue('companyOwner', data?.result?.companyOwner);
      setValue('companyWebsite', data?.result?.companyWebsite);
      setValue('companyIndustries', data?.result?.companyIndustries);
      setValue('companyTypes', [data?.result?.companyTypes]);
      setValue('companyAdmins', data?.result?.companyAdmins?.split(',') || []);
      setValue('companyAvatar', data?.result?.companyAvatar);
      setValue('address', data?.result?.address);
      setValue('phone', data?.result?.phone);
      setValue('tags', data?.result?.tags?.split(',') || []);
      setValue('license', data?.result?.license);
      setValue('companyEmail', data?.result?.companyEmail);
      setValue('deletedAt', data?.result?.deletedAt);
      setValue('overview', data?.result?.overview);
      setValue('note', data?.result?.note);
      setValue('status', data?.result?.status);
      setValue('documents', data?.result?.documents);
      setIsFetching(false);
    } catch (err) {
      setIsFetching(false);
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const fetchImageUrl = async (avatarId) => {
    try {
      const { data } = await getLinkS3(avatarId);
      setImageUrl(data);
    } catch (error) {
      setImageUrl(zileoLogo);
    }
  };

  const selectBefore = (
    <Select defaultValue="http://">
      <Option value="http://">http://</Option>
      <Option value="https://">https://</Option>
    </Select>
  );

  useEffect(() => {
    if (!dataDetail?.companyAvatar) return;
    fetchImageUrl(dataDetail?.companyAvatar);
  }, [dataDetail?.companyAvatar]);

  useEffect(() => {
    handleGetDetailData();
    getStaffAvatars();
  }, [id]);

  const handleReset = () => {
    setValue('id', dataDetail?.id);
    setValue('name', dataDetail?.name);
    setValue('bhClientId', dataDetail?.bhClientId);
    setValue('bhClientSecret', dataDetail?.bhClientSecret);
    setValue('bhUsername', dataDetail?.bhUsername);
    setValue('bhPassword', dataDetail?.bhPassword);
    setValue('bhToken', dataDetail?.bhToken);
    setValue('companySize', dataDetail?.companySize);
    setValue('companyOwner', dataDetail?.companyOwner);
    setValue('companyWebsite', dataDetail?.companyWebsite);
    setValue('companyIndustries', dataDetail?.companyIndustries);
    setValue('companyTypes', [dataDetail?.companyTypes]);
    setValue('companyAdmins', dataDetail?.companyAdmins?.split(',') || []);
    setValue('companyAvatar', dataDetail?.companyAvatar);
    setValue('address', dataDetail?.address);
    setValue('phone', dataDetail?.phone);
    setValue('tags', dataDetail?.tags?.split(',') || []);
    setValue('license', dataDetail?.license);
    setValue('companyEmail', dataDetail?.companyEmail);
    setValue('deletedAt', dataDetail?.deletedAt);
  };

  const uploadButton = (
    <button className="flex flex-col justify-center items-center py-4 border border-dashed w-full rounded-md">
      {loadingEdit ? <LoadingOutlined /> : <PlusOutlined />}
      <div className="mt-2 font-Montserrat">Upload</div>
    </button>
  );

  const handleUpdateStatus = async (companyId, status) => {
    setActionLoading(true);
    try {
      const payload = {
        status,
        companyId,
      };
      const { data } = await updateOnboardingCompanyStatus(payload);
      notification.success({
        description: `Status updated to ${status?.toLowerCase()?.capitalize()}!`,
      });

      handleGetDetailData();
      setActionLoading(false);
    } catch (error) {
      console.error('Error updating status:', error);
      notification.error({
        description: 'Failed to update status!',
      });
      setActionLoading(false);
    }
  };

  const items = [
    {
      key: 'staff-list',
      label: (
        <div className="text-lg font-semibold py-3">
          Company Staff(s) on Zileo
        </div>
      ),
      children: (
        <div className="font-Montserrat flex flex-col w-full w-fit">
          {dataDetail?.users?.map((user) => (
            <a
              onClick={(e) => e.stopPropagation()}
              href={`/user-management/${user?.id}/edit?companyId=${dataDetail?.id}`}
              target="_blank"
              className="py-2 border-b flex flex-col gap-1"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Avatar
                    src={
                      staffAvatars.find((item) => item.avtId === user?.avatarId)
                        ?.url || ''
                    }
                  >
                    {Array.from(user?.username || 'a')[0]}
                  </Avatar>
                  <div className="flex flex-col">
                    <span className="text-sm font-semibold">
                      {user?.email || '-'}
                    </span>
                    <span className=" italic">{user?.fullName}</span>
                  </div>
                </div>
                {getRoleName(user?.role?.name)}
              </div>
            </a>
          ))}
        </div>
      ),
    },
  ];

  const employees =
    dataDetail?.users?.map((user) => ({
      id: user?.id,
      key: user?.id,
      name: user?.fullName,
      email: user?.email,
      phone: user?.phone,
      role: user?.role?.keyCode,
      status: user?.status,
      userName: user?.username,
      password: user?.initialPassword,
      credits: user?.originalAmount || 0,
    })) || [];

  return (
    <div className="flex flex-col gap-5 p-4 company-detail-container">
      {tab === tabs.COMPANY_DETAIL && (
        <>
          <div>
            <div className="flex justify-between text-2xl font-semibold">
              <div className="flex gap-2 items-center">
                <Button
                  type="primary"
                  disabled={isFetching}
                  onClick={() => {
                    navigate(-1);
                  }}
                >
                  <div className='px-2 flex items-center justify-center'>
                    <LeftOutlined />
                  </div>
                </Button>
                <span className="Montserrat">Company Detail</span>
              </div>
              <div className="flex gap-3">
                {!editingStatus && (
                  <div className="flex items-center gap-2">
                    <Button
                      type="primary"
                      className="Montserrat  flex items-center "
                      disabled={isFetching}
                      onClick={() => {
                        setDisable(false);
                        setEditingStatus(true);
                        // setMode(MODE.EDIT);
                        // setSelectedCompany(initialCompany);
                        // showCompanyForm();
                      }}
                      icon={<BankOutlined />}
                    >
                      Edit Company
                    </Button>
                    {/* <Button
                      type="primary"
                      className="flex items-center "
                      disabled={isFetching}
                      onClick={() => {
                        setTab(tabs.EMPLOYEES_DETAIL);
                      }}
                      icon={<TeamOutlined />}
                    >
                      Onboard Employees
                    </Button> */}
                  </div>
                )}
                {editingStatus && (
                  <div style={{ display: 'flex' }}>
                    <Button
                      type="primary"
                      className="Montserrat  flex items-center "
                      disabled={loadingEdit}
                      loading={loadingEdit}
                      onClick={() => {
                        // setDisable(true);
                        // setEditingStatus(false);
                        handleReset();
                        setEditingStatus(false);
                        setDisable(true);
                        setLoadingEdit(false);
                      }}
                    >
                      <EditOutlined /> <span>Cancel</span>
                    </Button>
                    <Button
                      className="Montserrat  flex items-center "
                      disabled={loadingEdit}
                      loading={loadingEdit}
                      style={{ marginLeft: '10px' }}
                      onClick={() => {
                        // setDisable(true);
                        // setEditingStatus(false);
                        handleSaveData();
                      }}
                    >
                      <EditOutlined /> <span>Save</span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="col-span-1">
            {isFetching && (
              <div className="col-span-10 h-20 items-center justify-center flex">
                <Spin />
              </div>
            )}
            {!isFetching && (
              <main className="max-w-full mb-20">
                <section
                  className="bg-white rounded-lg shadow-md p-6 flex flex-col sm:flex-row sm:items-center sm:space-x-6"
                  style={{ boxShadow: '0 1px 3px rgb(0 0 0 / 0.1)' }}
                >
                  <div className="flex-shrink-0 mb-4 sm:mb-0 min-w-[11rem]">
                    <Upload
                      {...customProps}
                      className="h-10 "
                      rootClassName="customized-upload-component"
                      disabled={disable}
                    >
                      {imageUrl ? (
                        <div className={`relative`}>
                          <Image
                            preview={false}
                            alt={getValues('name')}
                            className="w-12 h-12"
                            height={100}
                            src={imageUrl}
                          />
                          <div className="hover-content">
                            <span className="font-Montserrat">
                              click to change
                            </span>
                          </div>
                        </div>
                      ) : (
                        uploadButton
                      )}
                    </Upload>
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <h1 className="text-xl font-semibold text-gray-900 leading-tight flex items-center gap-2">
                        {disable ? (
                          getValues('name')
                        ) : (
                          <Input
                            className="w-full font-Montserrat"
                            value={watch('name')}
                            onChange={(e) => setValue('name', e.target.value)}
                          />
                        )}
                        <Tag
                          className="px-2 py-1 flex items-center gap-1"
                          color={STATUS_COLOR[getValues('status')]}
                        >
                          {getValues('status') ===
                          OrganizationStatusEnum.APPROVED ? (
                            <HiStatusOnline />
                          ) : (
                            <HiStatusOffline />
                          )}
                          {getValues('status')?.toLowerCase()?.capitalize()}
                        </Tag>
                        <Button
                          onClick={() =>
                            navigate(
                              `/credit-management/company/${getValues('id')}`
                            )
                          }
                          type="primary"
                          icon={<CreditCardOutlined />}
                        >
                          <span className="text-xs">Manage Credits</span>
                        </Button>
                      </h1>
                      {isSuperAdmin &&
                        getValues('status') ===
                          OrganizationStatusEnum.PENDING && (
                          <div className="flex gap-2 items-center font-medium">
                            <Tooltip title="Approve this request">
                              <Button
                                disabled={actionLoading}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleUpdateStatus(
                                    id,
                                    OrganizationStatusEnum.APPROVED
                                  );
                                }}
                                type="primary"
                                icon={
                                  <CheckOutlined className="text-cyan-600 font-medium" />
                                }
                              >
                                Approve
                              </Button>
                            </Tooltip>
                            <Tooltip title="Reject this request">
                              <Button
                                type="dashed"
                                disabled={actionLoading}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleUpdateStatus(
                                    id,
                                    OrganizationStatusEnum.REJECTED
                                  );
                                }}
                                icon={
                                  <CloseOutlined className="text-red-600 font-medium" />
                                }
                              >
                                Reject
                              </Button>
                            </Tooltip>
                          </div>
                        )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      <Tooltip title="Company Type">
                        {disable ? (
                          getValues('companyTypes')?.[0] || (
                            <span className="italic opacity-75 text-sm">
                              Company type not set
                            </span>
                          )
                        ) : (
                          <Select
                            placeholder="Select company type"
                            value={watch('companyTypes')}
                            options={companyTypes}
                            className="w-[20rem] font-Montserrat"
                            onChange={(e) => {
                              console.log(e);
                              setValue('companyTypes', e);
                            }}
                          />
                        )}
                      </Tooltip>
                    </p>
                    <div className="mt-4 grid grid-cols-2 2xl:grid-cols-5 gap-x-6 gap-y-2 text-gray-700 font-semibold">
                      <div>
                        <div className="text-gray-900 font-bold ">Phone</div>
                        <div className="text-cyan-600 underline cursor-pointer">
                          {disable ? (
                            getValues('phone') || '-'
                          ) : (
                            <Input
                              placeholder="Please input company phone"
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('phone', e.target.value)
                              }
                              value={watch('phone')}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-900 mt-0.5">License</div>
                        <div className="text-cyan-600 font-bold">
                          {getValues('license') || '-'}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-900 mt-0.5">Owner</div>
                        <div className="text-gray-900 font-bold ">
                          {disable ? (
                            getValues('companyOwner') || '-'
                          ) : (
                            <Input
                              placeholder="Please input company owner"
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('companyOwner', e.target.value)
                              }
                              value={watch('companyOwner')}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-900 mt-0.5">Email</div>
                        <div className="text-gray-900 font-bold ">
                          {disable ? (
                            getValues('companyEmail') || '-'
                          ) : (
                            <Input
                              placeholder="Please input company email"
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('companyEmail', e.target.value)
                              }
                              value={watch('companyEmail')}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-900 font-bold ">
                          Company Size
                        </div>
                        <div className="text-gray-900  mt-0.5">
                          {disable ? (
                            getValues('companySize')?.replace(
                              /(.)(?=(\d{3})+$)/g,
                              '$1,'
                            ) || '-'
                          ) : (
                            <InputNumber
                              min={0}
                              placeholder="Please input company size"
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('companySize', e.target.value)
                              }
                              value={watch('companySize')}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-900 font-bold mt-0.5">
                          Website
                        </div>
                        <div className="text-cyan-600 underline cursor-pointer">
                          {disable ? (
                            getValues('companyWebsite') || '-'
                          ) : (
                            <Input
                              className="font-Montserrat"
                              placeholder="Please input company website"
                              onChange={(e) =>
                                setValue('companyWebsite', e.target.value)
                              }
                              value={watch('companyWebsite')}
                            />
                          )}
                        </div>
                      </div>
                      {/* <div className="col-span-1 xl:col-span-2">
                        <div className="text-gray-900 font-bold mt-0.5">
                          Industry
                        </div>
                        <div className="text-gray-900 mt-0.5">
                          {disable ? (
                            getValues('tags') || '-'
                          ) : (
                            <Select
                              mode="tags"
                              className="font-Montserrat w-full"
                              placeholder="Company Industries"
                              value={watch('tags')}
                              onChange={(e) => setValue('tags', e)}
                              tagRender={(props) =>
                                tagRender({ ...props, tagColor: 'tag-cyan' })
                              }
                            />
                          )}
                        </div>
                      </div> */}

                      <div className="col-span-1 xl:col-span-2">
                        <div className="text-gray-900 font-bold mt-0.5">
                          Tags
                        </div>
                        <div className="text-gray-900 mt-0.5">
                          {disable ? (
                            getValues('tags') || '-'
                          ) : (
                            <Select
                              mode="tags"
                              className="font-Montserrat w-full"
                              placeholder="Company Industries"
                              value={watch('tags')}
                              onChange={(e) => setValue('tags', e)}
                              tagRender={(props) =>
                                tagRender({ ...props, tagColor: 'tag-cyan' })
                              }
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
                <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-2 space-y-8">
                    <section className="p-4 bg-white rounded-lg shadow-sm">
                      <h2 className="text-sm font-semibold text-gray-900 mb-2">
                        {`Note in ${getValues('name')}`}
                      </h2>
                      <p className="text-gray-700">
                        {getValues('note') || (
                          <span className="opacity-75 italic">
                            There are no note
                          </span>
                        )}
                      </p>
                    </section>
                    <section className="p-4 bg-white rounded-lg shadow-sm">
                      <h2 className="text-sm font-semibold text-gray-900 mb-2">
                        {`About ${getValues('name')}`}
                      </h2>
                      <p className=" text-gray-700 ">
                        {disable ? (
                          getValues('overview')?.length > 500 ? (
                            <>
                              {!showContent
                                ? `${getValues('overview')?.slice(0, 500)}...`
                                : getValues('overview') || (
                                    <span className="italic opacity-60">
                                      No overview found
                                    </span>
                                  )}
                              {getValues('overview')?.length > 500 && (
                                <span
                                  onClick={() =>
                                    setShowMoreContent(!showContent)
                                  }
                                  className="text-cyan-600 font-semibold hover:underline pl-2 text-sm cursor-pointer"
                                >
                                  {showContent ? ' Show Less' : ' Show More'}
                                </span>
                              )}
                            </>
                          ) : (
                            getValues('overview') || (
                              <span className="italic opacity-60">
                                No information found
                              </span>
                            )
                          )
                        ) : (
                          <Input.TextArea
                            className="font-Montserrat"
                            rows={6}
                            onChange={(e) =>
                              setValue('overview', e.target.value)
                            }
                            value={watch('overview')}
                          />
                        )}
                      </p>
                    </section>
                    <section>
                      <h3 className="font-semibold text-gray-900 mb-3">
                        Location
                      </h3>
                      <div className="bg-white rounded-lg p-4 max-w-full space-y-3">
                        <div className="flex items-center space-x-2  text-gray-700 font-semibold">
                          <EnvironmentOutlined />
                          <span className="w-full">
                            {disable ? (
                              getValues('address') || 'Location not set'
                            ) : (
                              <Input
                                className="w-full"
                                required
                                onChange={(e) =>
                                  setValue('address', e.target.value)
                                }
                                value={watch('address')}
                              />
                            )}
                          </span>
                        </div>
                        <iframe
                          src={`https://www.google.com/maps/embed/v1/place?key=${googleKey}&q=${`${getValues('address') || ''}`}`}
                          height="450"
                          frameborder="0"
                          width={'100%'}
                          style={{ border: 0 }}
                          allowfullscreen=""
                          aria-hidden="false"
                          tabindex="0"
                          className="mt-5"
                        />
                        <div className="flex items-center justify-between  text-gray-700 font-semibold">
                          <a
                            aria-label="View map"
                            className="text-cyan-600 border border-cyan-600 rounded-md px-3 py-1 hover:bg-cyan-50 font-semibold"
                            href={`https://www.google.com/maps?q=${`${getValues('address') || ''}`}`}
                            target="_blank"
                          >
                            View Map
                          </a>
                        </div>
                      </div>
                    </section>
                  </div>
                  <aside className="space-y-8">
                    <section className="bg-white rounded-lg px-7 py-4 w-full shadow-sm">
                      <div className="flex items-center justify-between pb-2">
                        <h3 className="font-semibold text-gray-900 mb-3">
                          {`Users in ${getValues('name')}`}
                        </h3>
                        <Button
                          onClick={() => setTab(tabs.EMPLOYEES_DETAIL)}
                          type="primary"
                          icon={<PlusOutlined />}
                        >
                          <span className="text-xs">Add</span>
                        </Button>
                      </div>
                      <ul className="space-y-4">
                        <div className="font-Montserrat flex flex-col w-full w-fit gap-2">
                          {dataDetail?.users?.slice(0, 5)?.map((user) => (
                            <a
                              onClick={(e) => e.stopPropagation()}
                              href={`/user-management/${user?.id}/edit?companyId=${dataDetail?.id}`}
                              target="_blank"
                              className="flex flex-col gap-1 border rounded-md p-2 hover:bg-gray-50 "
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-2 grid grid-cols-10 w-full">
                                  <div className="col-span-2 flex items-center justify-center">
                                    <Avatar
                                      src={
                                        staffAvatars.find(
                                          (item) =>
                                            item.avtId === user?.avatarId
                                        )?.url || ''
                                      }
                                      size={50}
                                    >
                                      {
                                        Array.from(
                                          user?.fullName ||
                                            user?.username ||
                                            'a'
                                        )[0]
                                      }
                                    </Avatar>
                                  </div>
                                  <div className="flex flex-col col-span-5">
                                    <span
                                      className=" font-semibold line-clamp-1"
                                      title={user?.fullName}
                                    >
                                      {user?.fullName || user?.username || '-'}
                                    </span>
                                    <span
                                      className="text-cyan-600 font-semibold line-clamp-1"
                                      title={user?.email}
                                    >
                                      {user?.email || '-'}
                                    </span>
                                  </div>
                                  <span className="font-semibold text-gray-500 col-span-3 flex justify-end">
                                    {getRoleName(user?.role?.name)}
                                  </span>
                                </div>
                              </div>
                            </a>
                          ))}
                        </div>
                      </ul>
                      {dataDetail?.users?.length > 5 && (
                        <button
                          onClick={() => setShowMorePeople(!showPeople)}
                          aria-label={`Show all`}
                          className="mt-4 w-full border border-cyan-600 text-cyan-600  font-semibold rounded-md py-1 hover:bg-cyan-50"
                        >
                          {showPeople ? `View less` : `View all`}
                        </button>
                      )}
                    </section>
                    <section className="bg-white rounded-lg p-2 shadow-sm flex flex-col">
                      <CompanyDocuments
                        documents={getValues('documents')}
                        organizationId={getValues('id')}
                      />
                    </section>
                    {!isStandardCompnany && (
                      <section className="bg-white rounded-lg p-4 shadow-sm flex flex-col">
                        <h3 className="text-sm font-semibold text-gray-900 mb-3">
                          Bullhorn Configuration
                        </h3>
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2 items-center">
                            <span className="text-sm">Client Id</span>
                          </div>
                          <span className="text-[#4d6276]">
                            <Input
                              disabled={disable || isStandardCompnany}
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('bhClientId', e.target.value)
                              }
                              value={watch()?.bhClientId}
                            />
                          </span>
                        </div>
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2 items-center">
                            <span className="text-sm">Client Secret</span>
                          </div>
                          <span className="text-[#4d6276]">
                            <Input.Password
                              disabled={disable || isStandardCompnany}
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('bhClientSecret', e.target.value)
                              }
                              value={watch()?.bhClientSecret}
                            />
                          </span>
                        </div>
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2 items-center">
                            <span className="text-sm">Username</span>
                          </div>
                          <span className="text-[#4d6276]">
                            <Input
                              disabled={disable || isStandardCompnany}
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('bhUsername', e.target.value)
                              }
                              value={watch()?.bhUsername}
                            />
                          </span>
                        </div>
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2 items-center">
                            <span className="text-sm">Password</span>
                          </div>
                          <span className="text-[#4d6276]">
                            <Input.Password
                              disabled={disable || isStandardCompnany}
                              className="font-Montserrat"
                              required
                              onChange={(e) =>
                                setValue('bhPassword', e.target.value)
                              }
                              value={watch()?.bhPassword}
                            />
                          </span>
                        </div>
                      </section>
                    )}
                  </aside>
                </div>
              </main>
            )}
          </div>
        </>
      )}
      {tab === tabs.EMPLOYEES_DETAIL && (
        <>
          <div>
            <div className="flex justify-between text-2xl font-semibold">
              <div className="flex gap-2 items-center">
                <Button
                  type="primary"
                  onClick={() => {
                    setTab(tabs.COMPANY_DETAIL);
                    handleGetDetailData();
                  }}
                  icon={<LeftOutlined />}
                ></Button>
                <span className="Montserrat">Manage Employees</span>
              </div>
              <div className="flex gap-3">
                <Button
                  type="primary"
                  className="flex items-center "
                  disabled={isFetching}
                  onClick={() => {
                    navigate('/user-management/company');
                  }}
                  icon={<CloseOutlined />}
                >
                  Exit
                </Button>
                {editingStatus && (
                  <div style={{ display: 'flex' }}>
                    <Button
                      type="primary"
                      className="Montserrat  flex items-center "
                      disabled={loadingEdit}
                      loading={loadingEdit}
                      onClick={() => {
                        // setDisable(true);
                        // setEditingStatus(false);
                        handleReset();
                        setEditingStatus(false);
                        setDisable(true);
                        setLoadingEdit(false);
                      }}
                    >
                      <EditOutlined /> <span>Cancel</span>
                    </Button>
                    <Button
                      className="Montserrat  flex items-center "
                      disabled={loadingEdit}
                      loading={loadingEdit}
                      style={{ marginLeft: '10px' }}
                      onClick={() => {
                        // setDisable(true);
                        // setEditingStatus(false);
                        handleSaveData();
                      }}
                    >
                      <EditOutlined /> <span>Save</span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="font-Montserrat bg-white col-span-1 rounded-md shadow-md p-4">
            <ManageEmployees
              companyCredits={dataDetail?.totalOriginalCredits} // Company total original credits
              onboardingEmployees={employees} // Company employees
              organizationId={dataDetail?.id} // Company ID
              companyStatus={dataDetail?.status} // Company status
            />
          </div>
        </>
      )}
    </div>
  );
};

export default DetailCompany;
