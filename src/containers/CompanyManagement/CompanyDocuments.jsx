import { Button, message, notification, Spin, Upload } from 'antd';
// import FileListCard from './FileListCard';
import { UploadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { ActivitiesWrapper } from '../CRM/styled';
import FileListCard from '../../components/CRM/FileListCard';
import { LIMIT_ATTACHMENTS_SIZE } from '../../components/BullHorn/BullhornSendEmailModal';
import {
  createOrganizationDocument,
  deleteOrganizationDocument,
  uploadFile,
} from '../../services/users';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';
import { SALES, SUPER_ADMIN } from '../../constants/common.constant';

const CompanyDocuments = ({ documents, organizationId }) => {
  const currentUserRole = getUserViewAsRole();
  const isSuperAdmin =
    currentUserRole === SUPER_ADMIN || currentUserRole === SALES; // Sales and Super Admin can see all users

  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  const props = {
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file?.response, info.fileList);
      }
      if (info.file.status === 'done') {
        setFileList([info.file?.response, ...fileList]);
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      setUploadLoading(true);
      const isAllow = file.size < LIMIT_ATTACHMENTS_SIZE;
      if (!isAllow) {
        const uploadedFile = {
          uid: uuid(),
          name: `${uuid()}${file?.name}`,
          status: 'failed',
        };
        onError(
          {
            event: { status: 500 },
          },
          { ...uploadedFile }
        );
        message.error('Reaching size limitation (20mb)!');
        return;
      }
      try {
        let formData = new FormData();

        formData.append('file', file);
        const { data } = await uploadFile(formData);
        const responsFile = data?.result?.data || null;

        if (responsFile) {
          const uploadedFileNote = {
            name: file?.name,
            status: 'done',
            url: responsFile?.url,
            fileId: responsFile?.fileId,
            size: file?.size,
            type: file?.type,
          };

          const payload = {
            fileId: responsFile?.fileId,
            note: JSON.stringify(uploadedFileNote),
          };
          const { data: newOrgDocument } = await createOrganizationDocument(
            organizationId,
            payload
          );
          onSuccess({
            ...payload,
          });
        } else {
          onError({ event: { status: 500 } });
        }
        setUploadLoading(false);
      } catch (error) {
        setUploadLoading(false);
        onError({ event: error });
      }
    },
  };

  const getData = async () => {
    console.log('documents', documents);
    if (documents?.length > 0) {
      setFileList([...documents]);
    } else {
      setFileList([]);
    }
  };

  const handleDeleteDocument = async (fileId) => {
    try {
      const { data } = await deleteOrganizationDocument(organizationId, fileId);
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    getData();
  }, [documents]);

  return (
    <ActivitiesWrapper>
      <div className="flex h-full w-full justify-between items-center">
        <div className="font-semibold text-gray-900">Documents</div>
        {isSuperAdmin && (
          <Upload disabled={loading} showUploadList={false} {...props}>
            <Button
              loading={uploadLoading}
              type="primary"
              icon={<UploadOutlined />}
            >
              <span className="text-xs">Upload</span>
            </Button>
          </Upload>
        )}
      </div>
      {loading ? (
        <div className="w-full flex items-center justify-center italic opacity-60">
          <Spin spinning />
        </div>
      ) : fileList?.length > 0 ? (
        <div className="file-list">
          <FileListCard
            fileList={fileList}
            setFileList={setFileList}
            deleteFromSource={handleDeleteDocument}
            allowDelete={isSuperAdmin}
          />
        </div>
      ) : (
        <div className="w-full h-full min-h-[10rem] flex items-center justify-center italic opacity-60">
          No documents found
        </div>
      )}
    </ActivitiesWrapper>
  );
};
export default CompanyDocuments;
