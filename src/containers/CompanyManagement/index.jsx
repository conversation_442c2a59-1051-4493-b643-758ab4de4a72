import {
  AppstoreOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  EyeOutlined,
  LeftOutlined,
  PlusOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  Avatar,
  Button,
  Checkbox,
  Dropdown,
  Image,
  Input,
  Modal,
  Popconfirm,
  Spin,
  Table,
  Tag,
  Tooltip,
  notification,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  deleteCompany,
  getCompanies,
  OrganizationStatusEnum,
  updateOnboardingCompanyStatus,
} from '../../services/users';
import { faUserGroup } from '@fortawesome/free-solid-svg-icons';
import { UserGroupIcon } from '../../components/Sidebar/consts';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { useEffect, useState } from 'react';

import './index.css';
import { getLinkS3 } from '../../services/aws';
import { getUserViewAs, getUserViewAsRole } from '../../helpers/getUserViewAs';
import { SUPER_ADMIN } from '../../constants/common.constant';
import clsx from 'clsx';
import Search from 'antd/es/input/Search';

export const companyTypes = [
  {
    label: 'Agriculture',
    value: 'Agriculture',
  },
  {
    label: 'B2B',
    value: 'B2B',
  },
  {
    label: 'Cosmetics',
    value: 'Cosmetics',
  },
  {
    label: 'Agency',
    value: 'Agency',
  },
  {
    label: 'Services',
    value: 'Services',
  },
  {
    label: 'Real Estate',
    value: 'Real Estate',
  },
  {
    label: 'Food & Beverages',
    value: 'Food & Beverages',
  },
  {
    label: 'Entertainment',
    value: 'Entertainment',
  },
  {
    label: 'Daily Used',
    value: 'Daily Used',
  },
  {
    label: 'Games & Recreation',
    value: 'Games & Recreation',
  },
  {
    label: 'Aeroplane',
    value: 'Aeroplane',
  },
  {
    label: 'Transportation',
    value: 'Transportation',
  },
];

const initialCompany = {
  name: '',
  bhClientId: '',
  bhClientSecret: '',
  bhUsername: '',
  bhPassword: '',
  bhToken: null,
  companySize: 0,
  companyOwner: '',
  companyWebsite: '',
  // companyIndustries: null,
  companyTypes: [],
  companyAvatar: '',
};

export const MODE = {
  VIEW: 'VIEW',
  EDIT: 'EDIT',
};

export const STATUS_COLOR = {
  APPROVED: 'success',
  PENDING: 'warning',
  REJECTED: 'error',
};

const CompanyManagement = () => {
  const navigate = useNavigate();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  // const [selectedCompany, setSelectedCompany] = useState(initialCompany);
  const [companies, setCompanies] = useState([]);
  const [filter, setFilter] = useState({
    companySize: [],
    companyType: [],
    text: '',
  });
  const [actionLoading, setActionLoading] = useState(false);

  const currentUserRole = getUserViewAsRole();
  const currentUserId = getUserViewAs();
  const isSuperAdmin = currentUserRole === SUPER_ADMIN;
  const queryParams = new URLSearchParams(location.search);
  const statusQueryParam = queryParams.get('status');

  // const [openCompanyForm, setOpenCompanyForm] = useState(false);
  // const [mode, setMode] = useState(MODE.VIEW);

  // const showCompanyForm = () => setOpenCompanyForm(true);
  // const closeCompanyForm = () => setOpenCompanyForm(false);

  const {
    data: companyList = [],
    isFetching,
    refetch,
  } = useQuery(['COMPANY_LIST'], {
    queryFn: async () => {
      const { data } = await getCompanies();
      if (data?.result?.length > 0) {
        const companiesTemp = statusQueryParam
          ? [...data?.result]?.filter(
              (company) => company?.status?.toLowerCase() === statusQueryParam
            )
          : [...data?.result];
        const dataWithAvatarUrl = await Promise.all(
          companiesTemp?.map(async (comp) => {
            if (!comp?.companyAvatar) return { ...comp };
            const { data } = await getLinkS3(comp?.companyAvatar);
            return { ...comp, avatarUrl: data };
          })
        );
        setCompanies([...dataWithAvatarUrl]);
        return [...dataWithAvatarUrl];
      } else {
        setCompanies([...data?.result]);
        return data?.result;
      }
    },
  });

  const handleDelete = async (compnayId) => {
    await deleteCompany(compnayId)
      .then((res) => {
        notification.success({
          description: 'Deleted!',
        });
      })
      .catch((err) => {
        notification.error({
          description: 'Delete Failed!',
        });
      })
      .finally(() => {
        refetch();
      });
  };

  const companySizeList = [
    { label: 'Under 100 Employees', value: '0-100' },
    { label: '100 - 200 Employees', value: '100-200' },
    { label: '200 - 300 Employees', value: '200-300' },
    { label: '300 - 400 Employees', value: '300-400' },
    { label: '500 + Employees', value: '500-9999999' },
  ];

  const handleUpdateStatus = async (companyId, status) => {
    setActionLoading(true);
    try {
      const payload = {
        status,
        companyId,
      };
      const { data } = await updateOnboardingCompanyStatus(payload);
      notification.success({
        description: `Status updated to ${status?.toLowerCase()?.capitalize()}!`,
      });

      refetch();
      setActionLoading(false);
    } catch (error) {
      console.error('Error updating status:', error);
      notification.error({
        description: 'Failed to update status!',
      });
      setActionLoading(false);
    }
  };

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (text, record) => {
        return (
          <div className="flex items-center gap-2">
            {record?.avatarUrl && <Avatar size={50} src={record?.avatarUrl} />}
            {!record?.avatarUrl && <Avatar size={50} icon={<UserOutlined />} />}
            <div className="flex flex-col">
              <span
                className="font-semibold text-sm line-clamp-1 text-cyan-600"
                title={text || '-'}
              >
                {text || '-'}
              </span>
              {/* <span className="text-sm italic">{record?.website || '-'}</span> */}
              <a
                className="text-xs line-clamp-1 font-semibold"
                href={record?.companyWebsite || '#'}
                title={record?.companyWebsite || '-'}
                target="_blank"
              >
                {record?.companyWebsite || '-'}
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Owner',
      dataIndex: 'companyOwner',
      key: 'companyOwner',
      render: (text, record) => (
        <span className="text-cyan-600 text-sm font-semibold">
          {text || '-'}
        </span>
      ),
    },
    {
      title: 'Telephone',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone, record) => {
        return <div className="font-semibold text-sm ">{phone}</div>;
      },
    },
    {
      title: 'Company Size',
      dataIndex: 'companySize',
      key: 'companySize',
      render: (text, record) => (
        <div className="flex gap-1 items-center text-cyan-600 font-semibold">
          <UserGroupIcon className="w-4 h-4" />
          {` ${text || 'N/A'} `}
          <span className="text-sm">Employees</span>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status, record) => {
        return (
          <div className="w-full flex items-center justify-center">
            <Tag
              color={STATUS_COLOR[status]}
              className="flex items-center px-5 py-1 text-sm"
            >
              <span className=" text-sm">
                {status?.toLowerCase()?.capitalize()}
              </span>
            </Tag>
          </div>
        );
      },
    },
    {
      title: 'More',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      render: (id, record) => {
        const allowToDelete =
          isSuperAdmin || record?.createdBy === currentUserId;
        return (
          <div
            className="flex gap-1 justify-center w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {isSuperAdmin &&
              record?.status === OrganizationStatusEnum.PENDING && (
                <>
                  <Tooltip title="Approve this request">
                    <Button
                      disabled={actionLoading}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpdateStatus(id, OrganizationStatusEnum.APPROVED);
                      }}
                      icon={
                        <CheckOutlined className="text-green-600 font-medium text-sm " />
                      }
                    ></Button>
                  </Tooltip>
                  <Tooltip title="Reject this request">
                    <Button
                      disabled={actionLoading}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpdateStatus(id, OrganizationStatusEnum.REJECTED);
                      }}
                      icon={
                        <CloseOutlined className="text-red-600 font-medium text-sm " />
                      }
                    ></Button>
                  </Tooltip>
                </>
              )}
            <Popconfirm
              title="Delete the company"
              description={`Are you sure to delete ${record?.name} Company?`}
              onConfirm={() => handleDelete(record?.id)}
              // onCancel={cancel}
              okText="Yes"
              cancelText="No"
            >
              <Button
                disabled={actionLoading || !allowToDelete}
                title={
                  allowToDelete
                    ? 'Delete'
                    : 'You are not allowed to delete this company'
                }
                danger
                className="w-full text-sm "
                type="text"
                icon={<DeleteOutlined />}
              ></Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleChangeCompanySizeFilter = (e) => {
    const { value } = e.target;
    const { checked } = e.target;
    if (checked) {
      setFilter({
        ...filter,
        companySize: [value],
      });
    } else {
      setFilter({
        ...filter,
        companySize: [],
      });
    }
  };

  const handleChangeCompanyTypeFilter = (e) => {
    const { value } = e.target;
    const { checked } = e.target;
    if (checked) {
      const currFilterCompanyType = [...filter.companyType];
      const newFilterCompanyType = [...currFilterCompanyType, value];
      setFilter({
        ...filter,
        companyType: [...newFilterCompanyType],
      });
    } else {
      const currFilterCompanyType = [...filter.companyType];
      const newFilterCompanyType = currFilterCompanyType.filter(
        (item) => item !== value
      );
      setFilter({
        ...filter,
        companyType: [...newFilterCompanyType],
      });
    }
  };
  useEffect(() => {
    let currCompanies = [...companyList];

    if (filter.companyType.length > 0) {
      currCompanies = currCompanies.filter(
        (company) =>
          company?.companyTypes &&
          filter?.companyType?.includes(company?.companyTypes)
      );
    }

    if (filter.companySize.length > 0) {
      const range = filter.companySize[0].split('-');
      currCompanies = currCompanies.filter(
        (company) =>
          parseInt(company.companySize) > parseInt(range[0]) &&
          parseInt(company.companySize) < parseInt(range[1])
      );
    }

    if (filter.text) {
      currCompanies = currCompanies.filter((company) =>
        company?.name?.toLowerCase()?.includes(filter?.text?.toLowerCase())
      );
    }

    setCompanies([...currCompanies]);
  }, [filter]);

  return (
    <div className="flex flex-col gap-5 p-4">
      <div>
        <div className="flex justify-between text-2xl font-semibold">
          <span className="">Companies List</span>
          <div className="flex gap-3">
            <Input
              disabled={isFetching}
              value={filter?.text}
              allowClear
              placeholder="Search by company name..."
              className="w-72"
              onChange={(e) => {
                setFilter({
                  ...filter,
                  text: e.target.value,
                });
              }}
              prefix={<SearchOutlined className="text-[#97979e]" />}
            />
            <Button
              type="primary"
              className="  flex items-center "
              disabled={isFetching}
              onClick={() => {
                navigate('/company/onboarding');
                // setMode(MODE.EDIT);
                // setSelectedCompany(initialCompany);
                // showCompanyForm();
              }}
            >
              <PlusOutlined /> <span>Add Company</span>
            </Button>
            <Button
              className="bg-white flex items-center "
              disabled={isFetching}
              onClick={() => {
                navigate(-1);
              }}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
          </div>
        </div>
      </div>
      <div className="font- col-span-1 p-4 grid grid-cols-10 gap-3">
        {isFetching && (
          <div className="col-span-10 h-20 items-center justify-center flex">
            <Spin />
          </div>
        )}
        {!isFetching && (
          <>
            <div className="col-span-2">
              <div className="flex flex-col gap-4 grid grid-cols-5 pb-2">
                <div className=" flex flex-col gap-2 col-span-5">
                  {companyTypes.map((sizeOption) => (
                    <Checkbox
                      value={sizeOption.value}
                      onChange={handleChangeCompanyTypeFilter}
                      rootClassName={clsx(
                        'w-full bg-white border rounded-md shadow-sm px-5 py-2 gap-2 flex items-center font-medium !text-[#5e768d] cursor-pointer',
                        filter.companyType?.includes(sizeOption.value) &&
                          'border-cyan-600 !text-cyan-600 !font-semibold'
                      )}
                      // rootClassName="font-medium text-[#97979e]"
                    >
                      {sizeOption.label}
                    </Checkbox>
                  ))}
                </div>
              </div>
              <div className="flex flex-col gap-4 pt-2 border-t border-t-2 grid grid-cols-5">
                <div className=" flex flex-col gap-2 col-span-5">
                  {companySizeList.map((sizeOption) => (
                    <Checkbox
                      checked={filter.companySize.includes(sizeOption.value)}
                      onChange={handleChangeCompanySizeFilter}
                      value={sizeOption.value}
                      rootClassName={clsx(
                        'w-full bg-white border rounded-md shadow-sm px-5 py-2 gap-2 flex items-center font-medium !text-[#5e768d] cursor-pointer',
                        filter.companySize?.includes(sizeOption.value) &&
                          'border-cyan-600 !text-cyan-600 !font-semibold'
                      )}
                    >
                      {sizeOption.label}
                    </Checkbox>
                  ))}
                </div>
              </div>
            </div>
            <div className="col-span-8 search-table-new-design-container p-2 bg-white rounded-lg shadow-md">
              <Table
                dataSource={companies}
                columns={columns}
                loading={isFetching}
                rowSelection={rowSelection}
                rowKey={(record) => record.id}
                onRow={(record, rowIndex) => {
                  return {
                    onClick: () => {
                      navigate(`/user-management/company/${record?.id}`);
                    },
                    style: { cursor: 'pointer' },
                  };
                }}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CompanyManagement;
