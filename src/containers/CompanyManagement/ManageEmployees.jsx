import {
  Button,
  Col,
  Dropdown,
  Form,
  Input,
  InputNumber,
  notification,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import {
  CheckOutlined,
  CiCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import {
  CardToolbox,
  ContactPageheaderStyle,
  TableWrapper,
  UserTableStyleWrapper,
} from '../CRM/styled';
import { DefaultRoleIds, userRole } from '../../constants/common.constant';
import {
  generateRandomPassword,
  generateRandomUsername,
} from '../../helpers/util';
import clsx from 'clsx';
import styled from 'styled-components';
import {
  deleteUser,
  OrganizationStatusEnum,
  singleOnboardingEmployee,
  updatePermissions,
} from '../../services/users';

const RestyledSelect = styled(Select)`
  .ant-select-selection-item {
    font-weight: 500;
  }
`;

export const USER_STATUS = {
  ACTIVE: 'ACTIVE',
  PENDING: 'PENDING',
  DEACTIVATED: 'DEACTIVATED',
};

export const USER_STATUS_COLOR = {
  ACTIVE: 'success',
  PENDING: 'warning',
  DEACTIVATED: 'error',
};

const getElementByField = (field, _value, disabled = false, onChange) => {
  const roleOptions = Object.entries(userRole).map(([_key, value]) => ({
    label: value?.toLowerCase()?.capitalize()?.replace('_', ' '),
    value,
  }));

  switch (field) {
    case 'role':
      return <RestyledSelect disabled={disabled} options={roleOptions} />;
    case 'password':
      return <Input.Password disabled={disabled} />;
    case 'credits':
      return <InputNumber min={0} disabled={disabled} />;
    case 'email':
      return <Input disabled={disabled} onChange={onChange} />;
    default:
      return <Input disabled={disabled} />;
  }
};

const ManageEmployees = ({
  companyCredits = null,
  onboardingEmployees,
  organizationId,
  companyStatus,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [actionLoading, setActionLoading] = useState(false);

  const [totalOriginalCredits, setTotalOriginalCredits] = useState(0);
  const [isEditCreditsCap, setEditCreditsCap] = useState(false);

  const toggleEditCreditsCap = () => setEditCreditsCap(!isEditCreditsCap);

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');

  const isEditing = (record) => record?.key === editingKey;

  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const isDisabled =
      record?.id &&
      record?.status !== USER_STATUS.ACTIVE &&
      (dataIndex === 'credits' || dataIndex === 'password');

    const onChange = (e) => {
      if (dataIndex === 'email') {
        const newEmail = e.target.value;
        form.setFieldsValue({
          ...record,
          email: newEmail,
          userName: newEmail,
        });
      }
    };

    const inputNode = getElementByField(
      dataIndex,
      record?.[dataIndex],
      isDisabled,
      onChange
    );
    return (
      <td {...restProps}>
        {editing && record?.status !== USER_STATUS.ACTIVE ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={
              dataIndex === 'credits'
                ? []
                : [
                    {
                      required: true,
                      message:
                        dataIndex === 'email'
                          ? `${title} is invalid`
                          : `${title} is required`,
                      pattern:
                        dataIndex === 'email' ? /\S+@\S+\.\S+/ : undefined,
                    },
                  ]
            }
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const cancel = () => {
    // handleDelete([editingKey]);
    form.resetFields();
    setEditingKey('');
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      setActionLoading(true);
      const newData = [...dataSource];

      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];

        let newItem = {
          ...item,
          ...row,
        };

        newData.splice(index, 1, newItem);
        // const newTotal = newData?.reduce((acc, item) => {
        //   return acc + item?.credits;
        // }, 0);

        // if (newTotal > totalOriginalCredits) {
        //   notification.error({
        //     message: 'You have exceeded the available credits!',
        //   });
        //   setActionLoading(false);
        //   return;
        // }

        const payload = {
          email: newItem?.email,
          role: newItem?.role,
          username: newItem?.userName,
          password: newItem?.password,
          originalCredits: newItem?.credits,
          organizationId,
        };

        if (item?.id) {
          // update user
          const editedUserPayload = {
            email: newItem?.email,
            username: newItem?.userName,
            roleId: DefaultRoleIds[newItem?.role],
            id: item?.id,
          };
          const { data } = await updatePermissions({
            userId: item?.id,
            permissionsPayload: editedUserPayload,
          });
          notification.success({
            message: 'Employee updated successfully!',
          });
        } else {
          // create new user
          const { data } = await singleOnboardingEmployee(payload);
          if (data?.result) {
            newItem.id = data?.result?.id;
            newItem.key = data?.result?.id;
            newItem.status = data?.result?.status;
          }
          notification.success({
            message: 'Employee onboarded successfully!',
          });
        }

        setDataSource(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setDataSource(newData);
        setEditingKey('');
      }

      setActionLoading(false);
      form.resetFields();
      //   notification.success({
      //     message: 'Employee submitted successfully!',
      //   });
    } catch (errInfo) {
      setActionLoading(false);
      console.log('Validate Failed:', errInfo);
      const message =
        errInfo?.response?.data?.message ||
        'Please make sure all fields are filled correctly!';
      notification.error({
        message,
      });
    }
  };

  const cancelAdd = () => {
    form.resetFields();
    setEditingKey('');
    const newDataSource = [...dataSource];
    const selectedItem = newDataSource.find((item) => item.key === editingKey);
    if (!selectedItem?.id) {
      newDataSource.shift();
      setDataSource([...newDataSource]);
    }
  };

  const handleAdd = () => {
    const id = uuid();
    const newData = {
      key: id,
      email: '<EMAIL>',
      role: userRole.BASIC_USER,
      userName: generateRandomUsername(),
      password: generateRandomPassword(),
      credits: 0,
      createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      status: USER_STATUS.PENDING,
    };
    setDataSource([newData, ...dataSource]);
    edit(newData);
  };

  const handleDelete = async (ids) => {
    try {
      const newDataSource = dataSource.filter(
        (item) => !ids?.includes(item?.key)
      );
      setDataSource([...newDataSource]);
      const data = await Promise.all(
        ids?.map(async (id) => await deleteUser(id))
      );
      notification.success({
        message: 'Success!',
      });
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        message: 'Something went wrong!',
      });
    }
  };

  const columns = [
    {
      title: 'Invitation Email',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      editable: true,
      render: (email) => {
        return <div>{email}</div>;
      },
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      editable: true,
      width: '15%',
      render: (role, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {role?.toLowerCase()?.capitalize()?.replace('_', ' ') || '-'}
          </div>
        );
      },
    },
    {
      title: 'Username',
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      editable: true,
      render: (userName, record) => {
        return (
          <div className="line-clamp-1" title={userName}>
            {userName}
          </div>
        );
      },
    },
    {
      title: 'Initial Password',
      dataIndex: 'password',
      key: 'password',
      width: '25%',
      editable: true,
      render: (password, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {password || '-'}
          </div>
        );
      },
    },

    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '25%',
      // editable: true,
      render: (status, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            <Tag color={USER_STATUS_COLOR[status]}>{status}</Tag>
          </div>
        );
      },
    },
    {
      title: 'Credits',
      dataIndex: 'credits',
      key: 'credits',
      editable: true,
      width: '5%',
      render: (credits, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {credits || '0'}
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: '5%',
      align: 'center',
      render: (_action, record) => {
        const editable = isEditing(record);
        return (
          <div className="flex items-center justify-center w-full font-semibold ">
            {editable ? (
              <div className="flex items-center gap-2">
                <Button
                  loading={actionLoading}
                  type="dashed"
                  // className="flex gap-1 items-center"
                  onClick={() => save(record?.key)}
                  icon={<CheckOutlined />}
                ></Button>
                <Popconfirm title="Sure to cancel?" onConfirm={cancelAdd}>
                  <Button
                    type="text"
                    danger
                    // className="flex gap-1 items-center"
                    icon={<StopOutlined />}
                  ></Button>
                </Popconfirm>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  type="primary"
                  disabled={editingKey || record?.status === USER_STATUS.ACTIVE}
                  title={
                    record?.status === USER_STATUS.ACTIVE &&
                    'This user have already actived! '
                  }
                  onClick={() => edit(record)}
                  icon={<EditOutlined />}
                />
                <Popconfirm
                  title={<div>Confirmation</div>}
                  description={
                    <div>
                      Are you sure to delete <strong>{record?.name}</strong>{' '}
                      Employee?
                    </div>
                  }
                  onCancel={cancel}
                  onConfirm={() => handleDelete([record?.key])}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    danger
                    title={
                      record?.status === USER_STATUS.ACTIVE &&
                      'This user have already actived! '
                    }
                    disabled={
                      editingKey || record?.status === USER_STATUS.ACTIVE
                    }
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm>
              </div>
            )}
          </div>
        );
      },
    },
  ];

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const costCredits = dataSource?.reduce((acc, item) => {
    return acc + item?.credits;
  }, 0);

  useEffect(() => {
    if (onboardingEmployees?.length > 0) {
      const newDataSource = onboardingEmployees?.map((item) => ({
        ...item,
        key: item?.id,
      }));
      setDataSource([...newDataSource]);
    }
  }, [onboardingEmployees]);

  useEffect(() => {
    setTotalOriginalCredits(+companyCredits || 0);
  }, []);

  return (
    <div className="flex flex-col gap-5 w-full">
      <CardToolbox>
        <ContactPageheaderStyle>
          <div className="w-full h-full flex justify-between items-center">
            <div className="flex items-center gap-4 justify-center">
              <Button
                disabled={editingKey}
                onClick={handleAdd}
                type="primary"
                icon={<PlusOutlined />}
              >
                Add Employee
              </Button>

              <div>
                {selectedRowKeys?.length > 0 &&
                  companyStatus !== OrganizationStatusEnum.APPROVED && (
                    <div>
                      {/* <Dropdown
                        className="animated fadeInDownBig"
                        placement="bottom"
                        arrow
                        menu={{
                          items: [
                            {
                              key: 'delete-selected',
                              label: (
                                <a
                                  className="flex gap-2 items-center"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    handleDelete(selectedRowKeys);
                                    setSelectedRowKeys([]);
                                  }}
                                >
                                  <span>Delete</span>
                                </a>
                              ),
                              icon: <DeleteOutlined />,
                            },
                          ],
                        }}
                      >
                        <Space> */}
                          <Button
                            type="primary"
                            className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                          >
                            <p className="Montserrat">
                              {`${selectedRowKeys?.length} Selected`}
                            </p>
                            {/* <DownOutlined /> */}
                          </Button>
                        {/* </Space>
                      </Dropdown> */}
                    </div>
                  )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* <Button
                disabled={editingKey || dataSource?.length === 0}
                onClick={handleEqualize}
                type="primary"
                icon={<CiCircleOutlined />}
              >
                Equalize
              </Button> */}
              <div className="text-sm flex items-center gap-2">
                Available Credits:{' '}
                {!isEditCreditsCap && (
                  <span
                    className={clsx(
                      'font-semibold flex items-center gap-1',
                      +costCredits > totalOriginalCredits
                        ? 'text-red-500'
                        : 'text-cyan-600'
                    )}
                  >
                    <CiCircleOutlined />
                    {`${costCredits} / ${totalOriginalCredits}`}
                  </span>
                )}
                {isEditCreditsCap && (
                  <InputNumber
                    value={totalOriginalCredits}
                    onChange={(value) => setTotalOriginalCredits(value)}
                  />
                )}
                {/* <Tooltip title="Edit credits capacity">
                  <Button
                    onClick={toggleEditCreditsCap}
                    type="text"
                    icon={
                      isEditCreditsCap ? <SaveOutlined /> : <EditOutlined />
                    }
                  />
                </Tooltip> */}
              </div>
            </div>
          </div>
        </ContactPageheaderStyle>
      </CardToolbox>
      <Row gutter={15}>
        <Col xs={24}>
          <UserTableStyleWrapper>
            <div className="contact-table">
              <TableWrapper className="table-responsive text-gray-800">
                <Form form={form} component={false}>
                  <Table
                    className="customized-style-pagination w-full"
                    components={{
                      body: {
                        cell: EditableCell,
                      },
                    }}
                    rowClassName="editable-row"
                    dataSource={dataSource}
                    columns={mergedColumns}
                    rowSelection={editingKey ? null : rowSelection}
                    rowKey={(record) => record.id}
                    pagination={false}
                  />
                </Form>
              </TableWrapper>
            </div>
          </UserTableStyleWrapper>
        </Col>
      </Row>
    </div>
  );
};

export default ManageEmployees;
