.customize-modal .ant-modal-content {
  padding: 0px !important;
  border-radius: 0 !important;
  /* background-color: #f9fafb; */
}
.ant-upload-drag {
  background: white !important;
}

.customize-modal .ant-upload-drag {
  padding-top: 1.4rem;
  padding-bottom: 1.4rem;
}

.customized-upload-component .ant-upload {
  width: 100% !important;
}

.image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  transition: 0.5s ease;
  backface-visibility: hidden;
}

.hover-content {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
  z-index: 99;
}

.avatar-container:hover .image {
  opacity: 0.2;
}

.avatar-container:hover .hover-content {
  opacity: 1;
}

.text {
  background-color: #04aa6d;
  color: white;
  font-size: 16px;
  padding: 16px 32px;
}

.customized-upload-component .ant-upload {
  width: 100% !important;
  max-width: 11rem;
  display: flex;
  place-self: center;
}

.image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  transition: 0.5s ease;
  backface-visibility: hidden;
}

.hover-content {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}

.avatar-container:hover .image {
  opacity: 0.2;
}

.avatar-container:hover .hover-content {
  opacity: 1;
}

.text {
  background-color: #04aa6d;
  color: white;
  font-size: 16px;
  padding: 16px 32px;
}

.customized-invitation-tabs .ant-tabs-tab-btn {
  font-family: 'Montserrat';
  font-weight: 600;
}
.customized-invitation-tabs .ant-select-selector {
  border-radius: 0px !important;
  font-family: 'Montserrat';
}

.customized-invitation-tabs .ant-tabs-nav-wrap {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
}

/* .customized-invitation-tabs .ant-tabs-nav-list {
  padding: 16px 8px;
  border: 1px solid #cccccc;
  border-radius: 10px;
} */

.zileo-staff-container .ant-collapse-header {
  padding: 0 !important;
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.zileo-staff-container .ant-collapse-content-active {
  @apply overflow-y-auto max-h-44;
}
