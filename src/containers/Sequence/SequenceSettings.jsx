import {
  CalendarOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  notification,
  Popconfirm,
  Select,
  Spin,
  TimePicker,
  Form,
  Input,
} from 'antd';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import {
  getSequenceTimeSettings,
  updateSequenceTimeSettings,
} from '../../services/users';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { capFirst } from '../../utils/common';
import { getTimezoneList } from '../../services/nylas';
import { DateRangePickerOne } from '../../components/DatePicker';
import { Popover } from '../../components/Popup';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { addDays } from 'date-fns';

const weekdaysValue = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

const SequenceSettings = ({ closeSettingTab = null }) => {
  const [schedule, setSchedule] = useState({
    timezone: null,
    weekdays: [],
    deliveryAt: [],
    nonWorkingDays: [],
  });
  const [loading, setLoading] = useState(true);
  const [savingLoading, setSavingLoading] = useState(false);
  const [tzOptions, setTZOptions] = useState([]);

  const [openPopup, setOpenPopup] = useState(-1);
  const toggleWeekday = (value) => {
    const isExisting = schedule?.weekdays?.includes(value);
    let newWeekdays = [...schedule?.weekdays];
    if (isExisting) {
      newWeekdays = newWeekdays.filter((item) => item !== value);
    } else {
      newWeekdays.push(value);
    }
    setSchedule({
      ...schedule,
      weekdays: [...newWeekdays],
    });
  };

  const handleSaveSchedule = async () => {
    if (
      !schedule?.timezone ||
      schedule?.weekdays?.length === 0 ||
      !schedule?.weekdays ||
      !schedule?.deliveryAt ||
      schedule?.deliveryAt?.length === 0
    ) {
      notification.warning({
        description: 'Invalid time settings. Please check again before saving.',
      });
      return;
    }

    try {
      setSavingLoading(true);
      const nonWorkingDays =
        schedule?.nonWorkingDays?.length > 0
          ? schedule?.nonWorkingDays
              ?.filter((item) => item?.length > 0)
              ?.map((rangeDate) => ({
                start: rangeDate?.[0]
                  ? dayjs(rangeDate?.[0]).format('YYYY-MM-DD 00:00:00')
                  : '',
                end: rangeDate?.[1]
                  ? dayjs(rangeDate?.[1]).format('YYYY-MM-DD 23:59:59')
                  : '',
                name: '',
              }))
          : null;

      const payload = {
        timezone: schedule.timezone,
        workingHours: schedule.weekdays
          ?.map((day) => day.toUpperCase())
          .reduce(
            (a, v) => ({
              ...a,
              [v]: {
                startTime: schedule.deliveryAt?.[0],
                endTime: schedule.deliveryAt?.[1],
              },
            }),
            {}
          ),
        nonWorkingDays,
      };
      const { data } = await updateSequenceTimeSettings(payload);
      notification.success({
        description: 'New settings applied!',
      });
      setSavingLoading(false);

      console.log('payload: ', data);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Something went wrong, Try again later!',
      });
      setSavingLoading(false);
    }
  };

  const handleGetScheduleSetting = async () => {
    try {
      const { data } = await getSequenceTimeSettings();
      const userSettings = data?.result || {};
      if (
        !isEmpty(userSettings?.timezone) &&
        !isEmpty(userSettings?.workingHours)
      ) {
        let deliveryAt = [];
        const weekDaysSettings = Object.keys(userSettings?.workingHours).map(
          (key) => {
            deliveryAt = [
              userSettings?.workingHours[key]?.startTime,
              userSettings?.workingHours[key]?.endTime,
            ];
            return capFirst(key.toLowerCase());
          }
        );

        const nonWorkingDays =
          userSettings?.nonWorkingDays?.length > 0
            ? userSettings?.nonWorkingDays?.map((item) => {
                return [
                  dayjs(item?.start, 'YYYY-MM-DD hh:mm:ss'),
                  dayjs(item?.end, 'YYYY-MM-DD hh:mm:ss'),
                ];
              })
            : [];
        const newSchedule = {
          timezone: userSettings?.timezone,
          weekdays: [...weekDaysSettings],
          nonWorkingDays,
          deliveryAt,
        };
        console.log('newSchedule: ', newSchedule);
        setSchedule({ ...newSchedule });
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const getTimezonesList = async () => {
    try {
      const { data } = await getTimezoneList();

      if (data?.result?.length > 0) {
        const tzList = data?.result?.map((item) => ({
          label: item.text,
          value: item.timezone,
          order: item.order,
        }));

        setTZOptions([...tzList]);
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const getData = async () => {
    try {
      setLoading(true);
      await Promise.all([handleGetScheduleSetting(), getTimezonesList()]);

      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <div className="flex flex-col gap-4">
      {closeSettingTab && (
        <>
          <div>
            <Breadcrumb
              items={[
                {
                  title: (
                    <div
                      style={{ cursor: 'pointer' }}
                      onClick={closeSettingTab}
                    >
                      Sequence
                    </div>
                  ),
                },
                {
                  title: <>Settings</>,
                },
              ]}
            />
          </div>
          <div>
            <Button
              className="bg-white flex items-center "
              onClick={closeSettingTab}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
          </div>
        </>
      )}
      <div className="grid grid-cols-2 gap-2">
        <div className="col-span-1 bg-white rounded-md shadow-md p-5 flex flex-col gap-4">
          {loading && (
            <div className="w-full h-full flex justify-center items-center">
              <Spin />
            </div>
          )}

          {!loading && (
            <>
              <div className="text-cyan-600 font-semibold text-lg">
                Sending Options
              </div>

              <div className="mt-2 text-sm text-[#9eadc0] font-medium flex items-center justify-between gap-2">
                <div className="pr-9">Timezone</div>
                <Select
                  disabled={savingLoading}
                  optionFilterProp="label"
                  style={{ width: '100%' }}
                  prefixCls="Timezone"
                  showSearch
                  defaultValue={schedule.timezone}
                  placeholder="Time Zone"
                  onChange={(timezone) => {
                    setSchedule({ ...schedule, timezone });
                  }}
                  options={tzOptions.sort((a, b) =>
                    a.order < b.order ? -1 : 1
                  )}
                />
              </div>

              <div>
                <div className="mt-2 text-sm text-[#9eadc0] font-medium flex items-center gap-2">
                  <div>Working Hours</div>
                  <TimePicker.RangePicker
                    defaultValue={schedule?.deliveryAt?.map((time) =>
                      dayjs(time, 'HH:mm')
                    )}
                    disabled={savingLoading}
                    className="max-w-fit"
                    format={'HH:mm'}
                    onChange={(value) =>
                      setSchedule({
                        ...schedule,
                        deliveryAt: value?.map((i) => dayjs(i).format('HH:mm')),
                      })
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-7 gap-5">
                {weekdaysValue.map((weekdayText) => (
                  <div
                    className={clsx(
                      'rounded-md cursor-pointer border min-w-[5rem]'
                    )}
                    onClick={() => toggleWeekday(weekdayText)}
                  >
                    <div
                      className={clsx(
                        'w-11/12 font-medium px-3 py-2 rounded-tl-md rounded-bl-md',
                        (weekdayText === 'Sun' || weekdayText === 'Sat') &&
                          'text-red-400',
                        schedule.weekdays.includes(weekdayText) &&
                          'bg-cyan-600 text-white'
                      )}
                    >
                      {weekdayText}
                    </div>
                  </div>
                ))}
              </div>

              <div>
                <div className="mt-2 text-sm text-[#9eadc0] font-medium flex items-center gap-2">
                  <div className="flex gap-2 items-center">
                    <span>Holidays</span>

                    <Popconfirm
                      rootClassName="customize-tooltip-widget"
                      placement="bottom"
                      title="Inactive sequence range date"
                      description="Your sequences will not active on this range of date."
                    >
                      <Button
                        type="text"
                        icon={<InfoCircleOutlined className="text-xs" />}
                      />
                    </Popconfirm>

                    <div className="flex flex-wrap gap-1 items-center">
                      {schedule?.nonWorkingDays?.map((range, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 px-2"
                        >
                          <Popover
                            placement="bottomRight"
                            title="Selecting a range date"
                            openStatus={openPopup === index}
                            content={
                              <DateRangePickerOne
                                handleSelectDateRange={(dateRange) => {
                                  if (dateRange) {
                                    let [fromDate, toDate] = dateRange
                                      .split(' - ')
                                      .map((dateStr) => dayjs(dateStr));
                                    fromDate = fromDate.startOf('day');
                                    toDate = toDate.endOf('day');
                                    fromDate = fromDate.format(
                                      'YYYY-MM-DD HH:mm:ss'
                                    );
                                    toDate = toDate.format(
                                      'YYYY-MM-DD HH:mm:ss'
                                    );

                                    const newScheduleExcclude = [
                                      ...(schedule?.nonWorkingDays || []),
                                    ];
                                    newScheduleExcclude[index] = [
                                      fromDate,
                                      toDate,
                                    ];

                                    setSchedule({
                                      ...schedule,
                                      nonWorkingDays: [...newScheduleExcclude],
                                    });
                                    setOpenPopup(-1);
                                  }
                                }}
                                defaultStartDate={
                                  range?.[0] ? new Date(range?.[0]) : new Date()
                                }
                                defaultEndDate={
                                  range?.[1]
                                    ? new Date(range?.[1])
                                    : addDays(new Date(), 7)
                                }
                                setOpenPopup={(isOpen) => {
                                  setOpenPopup(-1);
                                }}
                              />
                            }
                            action="click"
                          >
                            <Button
                              onClick={(e) => {
                                setOpenPopup(index);
                                e.stopPropagation();
                              }}
                              className="gap-2 py-3 flex items-center bg-[#fff] font-medium hover:text-[#2684c7] text-[#5a5f7d]"
                            >
                              <CalendarOutlined className="text-[#2684c7]" />
                              <p className="font-Inter">
                                {range?.length > 0
                                  ? `${dayjs(range?.[0]).format('DD/MM/YYYY')} - ${dayjs(range?.[1]).format('DD/MM/YYYY')}`
                                  : 'Not set yet'}
                              </p>
                            </Button>
                          </Popover>
                          <MinusCircleOutlined
                            disabled={openPopup > -1}
                            className={clsx(
                              'dynamic-delete-button',
                              openPopup > -1 && '!cursor-not-allowed'
                            )}
                            onClick={() => {
                              if (openPopup > -1) return;
                              const newScheduleExcclude = [
                                ...schedule?.nonWorkingDays,
                              ];
                              newScheduleExcclude.splice(index, 1);

                              setSchedule({
                                ...schedule,
                                nonWorkingDays: [...newScheduleExcclude],
                              });
                            }}
                          />
                        </div>
                      ))}
                      <div className="px-2">
                        <Button
                          type="dashed"
                          disabled={openPopup > -1}
                          onClick={() => {
                            const newScheduleExcclude = [
                              ...(schedule?.nonWorkingDays || []),
                            ];
                            newScheduleExcclude.push([]);

                            setSchedule({
                              ...schedule,
                              nonWorkingDays: [...newScheduleExcclude],
                            });
                          }}
                          icon={<PlusOutlined />}
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  loading={savingLoading}
                  onClick={handleSaveSchedule}
                  type="primary"
                  icon={<SaveOutlined />}
                >
                  Save
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SequenceSettings;
