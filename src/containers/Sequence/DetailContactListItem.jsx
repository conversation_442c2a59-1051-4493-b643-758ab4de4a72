import {
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  <PERSON>lip<PERSON>Outlined,
  FireOutlined,
  LinkedinOutlined,
  MailOutlined,
  PhoneOutlined,
  QuestionCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Dropdown,
  Input,
  Modal,
  Popconfirm,
  message,
  notification,
} from 'antd';
import {
  deleteContactFromList,
  updateContactFromList,
} from '../../services/contactList';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { EditIcon, LocationIcon } from '@nylas/react';

const DetailContactListItem = (props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { item, currentDetail, handleGetDetailData, listName, view } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const { setValue, getValues } = useForm();
  const handleDeleteContact = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await deleteContactFromList(currentDetail?.id, item?.id);
      notification.success({ message: 'Delete Contact Success' });
      messageApi.destroy();
      handleGetDetailData();
    } catch (errors) {
      messageApi.destroy();
      notification.error({ message: 'Something went wrong' });
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = async () => {
    if (!getValues('email') || !getValues('name')) {
      notification.error({ message: 'Please fill in all fields' });
      return;
    }
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    const payload = {
      email: getValues('email'),
      name: getValues('name'),
      phone: getValues('phone'),
      contactTitle: getValues('contactTitle'),
      contactLocation: getValues('contactLocation'),
      linkedInProfileUrl: getValues('linkedInProfileUrl')
    };

    try {
      const { data } = await updateContactFromList(
        currentDetail?.id,
        item?.id,
        payload
      );
      notification.success({ message: 'Update Contact Success' });
      messageApi.destroy();
      handleGetDetailData();
      setIsModalOpen(false);
    } catch (errors) {
      messageApi.destroy();
      notification.error({ message: 'Something went wrong' });
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    setValue('name', item?.name);
    setValue('email', item?.email);
    setValue('phone', item?.phone);
    setValue('contactTitle', item?.contactTitle);
    setValue('contactLocation', item?.contactLocation);
  }, [item]);

  console.log('item: ', item);
  return (
    <>
      {contextHolder}
      <div className="relative bg-white rounded-lg p-4 overflow-hidden shadow-md">
        <div className="flex flex-col gap-1 items-center justify-center p-3 border-b font-Montserrat">
          <Avatar src={item?.avatarUrl || ''} size={120}>
            {Array.from(item?.name || 'a')[0]}
          </Avatar>
          <span className="text-lg font-semibold pt-3">
            {!isModalOpen && (item?.name || '-')}
            {isModalOpen && (
              <Input
                className="font-Montserrat"
                defaultValue={item?.name}
                onChange={(e) => setValue('name', e.target.value)}
              />
            )}
          </span>
          <span className="text-sm">
            {!isModalOpen && (item?.contactTitle || '-')}
            {isModalOpen && (
              <Input
                className="font-Montserrat"
                defaultValue={item?.contactTitle}
                onChange={(e) => setValue('contactTitle', e.target.value)}
              />
            )}
          </span>
        </div>
        <div className="p-3 flex items-center flex-col gap-3 font-Montserrat text-[#8288A4]">
          <div className="flex items-center justify-center gap-1">
            <PhoneOutlined />{' '}
            {!isModalOpen && (
              <a href={`tel:${item?.phone}`}>{item?.phone || '-'}</a>
            )}
            {isModalOpen && (
              <Input
                className="font-Montserrat"
                defaultValue={item?.phone}
                onChange={(e) => setValue('phone', e.target.value)}
              />
            )}
          </div>
          <div className="flex items-center justify-center gap-2">
            <MailOutlined />{' '}
            {!isModalOpen && (
              <a
                href={`mailto:${item?.email}`}
                className="line-clamp-1 max-w-xs"
                title={item?.email || '-'}
              >
                {item?.email || '-'}
              </a>
            )}
            {isModalOpen && (
              <Input
                className="font-Montserrat"
                defaultValue={item?.email}
                onChange={(e) => setValue('email', e.target.value)}
              />
            )}
          </div>
          <div className="flex items-center justify-center gap-1">
          <LinkedinOutlined style={{marginRight: "3px"}}/>
            <span>
              {!isModalOpen && (item?.linkedInProfileUrl || '-')}
              {isModalOpen && (
                <Input
                  className="font-Montserrat"
                  defaultValue={item?.linkedInProfileUrl}
                  onChange={(e) => setValue('linkedInProfileUrl', e.target.value)}
                />
              )}
            </span>
          </div>
          <div className="flex items-center justify-center gap-1">
            <LocationIcon />{' '}
            <span>
              {!isModalOpen && (item?.contactLocation || '-')}
              {isModalOpen && (
                <Input
                  className="font-Montserrat"
                  defaultValue={item?.contactLocation}
                  onChange={(e) => setValue('contactLocation', e.target.value)}
                />
              )}
            </span>
          </div>
        </div>

        {/* action button */}

        {!view &&
          (!isModalOpen ? (
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'edit-contact',
                    label: (
                      <div
                        onClick={showModal}
                        className="flex items-center cursor-pointer gap-2 font-medium font-Montserrat"
                      >
                        {/* {isModalOpen ? (
                      <CheckOutlined onClick={handleOk} />
                    ) : ( */}
                        <EditIcon /> <span>Edit Contact</span>
                        {/* )} */}
                      </div>
                    ),
                  },
                  {
                    key: 'delete-contact',
                    label: (
                      <Popconfirm
                        title="Delete Contact"
                        description="Are you sure to delete this contact?"
                        onConfirm={(e) => {
                          e.stopPropagation(), handleDeleteContact(item?.id);
                        }}
                        onCancel={(e) => {
                          // e.stopPropagation(), setIsModalOpenDetail(false);
                        }}
                        okText="Yes"
                        cancelText="No"
                        icon={
                          <QuestionCircleOutlined style={{ color: 'red' }} />
                        }
                      >
                        <div className="flex items-center cursor-pointer gap-2 font-medium font-Montserrat">
                          <DeleteOutlined /> <span>Remove Contact</span>
                        </div>
                      </Popconfirm>
                    ),
                  },
                ],
              }}
              trigger={'click'}
              placement="bottom"
              arrow
            >
              <Button
                className="absolute top-4 left-6 text-xl font-bold items-center flex items-center justify-center"
                type="text"
                icon={<EllipsisOutlined />}
              ></Button>
            </Dropdown>
          ) : (
            <Button
              className="absolute top-4 left-6 text-xl font-bold items-center flex items-center justify-center"
              type="text"
              onClick={handleOk}
              icon={<CheckOutlined />}
            ></Button>
          ))}

        {/* <span className="absolute top-4 left-6 text-base px-3 py-2 border rounded-lg flex">
          <FireOutlined />
          <span
            className="pl-2 line-clamp-1 max-w-[50px]"
            title={`This contact belong to ${listName} Contact.`}
          >
            {listName}
          </span>
        </span> */}
      </div>
      {/* <Modal
        title="Edit Contact Info"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div style={{ marginTop: '20px' }}></div>
        <label>Name</label>
        <Input
          defaultValue={item?.name}
          onChange={(e) => setValue('name', e.target.value)}
        />
        <div style={{ marginTop: '10px' }}></div>
        <label>Email</label>
        <Input
          defaultValue={item?.email}
          onChange={(e) => setValue('email', e.target.value)}
        />
        <div style={{ marginTop: '10px' }}></div>
        <label>Phone</label>
        <Input
          defaultValue={item?.phone}
          onChange={(e) => setValue('phone', e.target.value)}
        />
      </Modal> */}
    </>
  );
};

export default DetailContactListItem;
