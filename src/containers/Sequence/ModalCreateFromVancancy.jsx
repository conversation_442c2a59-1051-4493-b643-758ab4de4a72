import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Drawer,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
} from 'antd';
import {
  EnvironmentOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  LinkOutlined,
  MoreOutlined,
  PhoneOutlined,
  UserOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import Email4Logo from '../../assets/img/vacancy.png';
import { useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { generateAIAssistantMessage } from '../../services/search';
import JobDetail from '../../components/JobDetail';
import { AnimatedBeam, Circle } from '../../components/MagicUI/AnimatedBeam';
import zileoLogo from '../../assets/img/welcome/logo.png';
import bhlogo from '/logo_bull.webp';
import { textContent } from '../../helpers/util';

function ModalCreateFromVancancy({
  closeCreateSequenceModal,
  isShowModal = false,
  backTo = () => {},
}) {
  const [open, setOpen] = useState(false);
  const zileoRef = useRef(null);
  const bullhornRef = useRef(null);
  const containerRef = useRef(null);

  const { handleSubmit, control, getValues, setValue, watch, reset } =
    useForm();
  const [companyId, setCompanyId] = useState();
  const [countLeads, setCountLeads] = useState(0);
  const [searchText, setSearchText] = useState();
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [existingContacts, setExistingContacts] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState(null);
  const [leadByCompanyId, setLeadByCompanyId] = useState();
  const [loadingContact, setLoadingContact] = useState(false);
  const [loadingFindJob, setLoadingFindJob] = useState(false);
  const [loadingFindVacancy, setLoadingFindVacancy] = useState(false);
  const [loadingDataEmail, setLoadingDataEmail] = useState(false);

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const {
    options: companyOptionsJobOrder,
    setOptions: companySetOptionsJobOrder,
    handleScrollPopup: handleCompanyScrollJobOrder,
    handleSearch: handleCompanySearchJobOrder,
    isLoading: isLoadingCompanyJobOrder,
    setLoading: setIsLoadingCompanyJobOrder,
    valueNotFound: valueNotFoundCompanyJobOrder,
    setStart: companySetStartJobOrder,
    isLoadingScroll: isLoadingScrollCompanyJobOrder,
    setLoadingScroll: setIsLoadingScrollCompanyJobOrder,
  } = useInfiniteScrollWithSearch(searchBullhornData('JobOrder'));

  const timeoutRef = useRef(null);

  const updateExistingContacts = async () => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'ClientContact',
      null,
      null,
      null,
      companyId
    );
    setExistingContacts(response?.data?.result || []);
    setLoadingContact(false);
  };

  useEffect(() => {
    if (getValues('leadSelected')) {
      setOpenSendEmail(true);
      updateExistingContacts();
      // getDataEmail();
    }
  }, [getValues('leadSelected')]);

  useEffect(() => {
    handleCompanySearch(' ');
  }, []);

  useEffect(() => {
    if (!openModalSendEmail) {
      setValue('sendMail.content', null);
      setValue('sendMail.subject', null);
    }
  }, [openModalSendEmail]);

  const getDataEmail = async () => {
    const { data } = await getEmailConfigInJobBoard(
      getValues('leadSelected').job_id ||
        getValues('leadSelected').job_board_id ||
        getValues('leadSelected')?.id,
      true
    );
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          key: index + 1,
          status: item?.status,
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
    }
    setOpenSendEmail(true);
  };

  return (
    <>
      {!isShowModal && (
        <div
          // onClick={() => setOpen(true)}
          className="w-full flex items-center justify-start gap-3 p-1"
        >
          <Image style={{ height: '43px' }} src={Email4Logo} preview={false} />
          <div>
            <div className="font-semibold ">Vacancy</div>
            <div className="font-medium text-xs opacity-70">
              Create a new sequence from vacancy.
            </div>
          </div>
        </div>
      )}

      {isShowModal && (
        <div className="relative sequence-background-container py-3 px-6 bg-white rounded-md shadow-md overflow-hidden">
          <div className="flex items-center justify-center pb-2 border-b relative min-w-[50rem]">
            <Button
              className="absolute left-3"
              onClick={backTo}
              type="primary"
              icon={<LeftOutlined />}
            >
              Back
            </Button>
            <div className="w-fit flex flex-col items-center gap-1 justify-center text-cyan-600">
              <div className="text-base font-semibold">Start From Vacancy</div>
              <div className="text-xs opacity-80">
                Customize this sequence’s name, permission settings, sending
                schedule, and more.
              </div>
            </div>
            {/* <div /> */}
          </div>
          <div className="flex flex-col items-center justify-start min-h-[40rem] gap-4">
            <div
              className="relative flex w-full max-w-[20rem] items-center justify-center min-h-[9rem] mt-32"
              ref={containerRef}
            >
              <div className="flex w-full justify-between gap-10">
                <div className="flex items-center gap-3 w-full">
                  <div
                    ref={zileoRef}
                    className="z-10 flex items-center justify-center rounded-md border bg-white p-3 h-full h-[7rem] w-[7rem]"
                  >
                    <Image width={80} src={zileoLogo} preview={false} />
                  </div>
                  <LinkOutlined className="text-xl opacity-60" />
                  <div
                    ref={bullhornRef}
                    className="z-10 flex items-center justify-center rounded-md border bg-white p-3 h-[7rem] w-[7rem]"
                  >
                    <Image
                      className="rounded-full"
                      width={80}
                      src={bhlogo}
                      preview={false}
                    />
                  </div>
                </div>
              </div>

              {/* <AnimatedBeam
                containerRef={containerRef}
                fromRef={zileoRef}
                toRef={bullhornRef}
                // startYOffset={-50}
                // endYOffset={-50}
                // curvature={20}
                duration={3}
              /> */}
              {/* <AnimatedBeam
                duration={10}
                containerRef={containerRef}
                fromRef={zileoRef}
                toRef={bullhornRef}
                startYOffset={-10}
                endYOffset={-10}
                curvature={20}
                reverse
              /> */}
            </div>
            <div className="text-cyan-600 font-semibold flex items-center gap-2">
              <InfoCircleOutlined />
              Search for a company and a vacancy to start a new sequence.
            </div>
            <div className="custom_input_sequence">
              <Select
                optionFilterProp="label"
                showSearch
                loading={isLoadingCompany}
                onPopupScroll={(e) =>
                  handleCompanyScroll(e, 'ClientCorporation')
                }
                filterOption={(input, option) => {
                  const elementHtml = textContent(option?.label);
                  return elementHtml
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                disabled={
                  loadingFindVacancy ||
                  loadingContact ||
                  openModalSendEmail ||
                  isLoadingScrollCompanyJobOrder
                }
                options={companyOptions?.map((option) => ({
                  id: option.id,
                  value: option.id,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base text-cyan-600 font-medium">
                            {option.id} - {option.name}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                              <PhoneOutlined className="font-semibold" />{' '}
                              {option.phone ? option.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                              <InfoCircleOutlined className="font-semibold" />{' '}
                              {option.status ? option.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                              <EnvironmentOutlined className="font-bold" />
                              {option.address &&
                              option.address.city &&
                              option.address.state
                                ? `${option.address.city}, ${option.address.state}`
                                : option.address && option.address.city
                                  ? option.address.city
                                  : option.address && option.address.state
                                    ? option.address.state
                                    : '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
                style={{ width: '500px', height: '50px', color: '#fff' }}
                onSelect={(value, option) => {
                  setCompanyId(option?.id);
                  handleCompanySearchJobOrder('', option?.id);
                  setLeadByCompanyId(null);
                  setValue(
                    'currentCp',
                    companyOptions?.find((item) => item.id === option.id)?.name
                  );
                  setValue(
                    'companyDetail',
                    companyOptions?.find((item) => item.id === option.id)
                  );
                  setValue('companyId', option?.id);
                  setValue('leadSelected', null);
                  setOpenSendEmail(false);
                }}
                onSearch={(value) => {
                  setValue('currentCp', value);
                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    setLoadingFindJob(true);
                    if (value) companySetStart(0);
                    handleCompanySearch(value);
                    setLoadingFindJob(false);
                  }, 500);
                }}
                placeholder="Search Company"
                notFoundContent={
                  isLoadingCompany && (
                    <div className="w-full flex justify-center py-4">
                      <Spin size="default" />
                    </div>
                  )
                }
              />
            </div>
            {loadingFindJob && <Spin />}
            <div className="custom_input_sequence">
              <Select
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.valueName ?? '')
                    ?.toLowerCase()
                    ?.includes(input.toLowerCase())
                }
                onPopupScroll={(e) =>
                  handleCompanyScrollJobOrder(e, 'JobOrder', companyId)
                }
                loading={isLoadingCompanyJobOrder}
                notFoundContent={
                  isLoadingCompanyJobOrder && (
                    <div className="w-full flex justify-center py-4">
                      <Spin size="default" />
                    </div>
                  )
                }
                disabled={openModalSendEmail || companyId ? false : true}
                // loading={isLoadingScrollCompanyJobOrder}
                style={{ width: '500px', height: '50px' }}
                value={getValues('leadSelected')?.title}
                onSelect={async (value, option) => {
                  const leadSelect = companyOptionsJobOrder?.find(
                    (item) => item.id === option.id
                  );
                  const prompt = `Extract the 5 highlight skills from this vacancy description: ${leadSelect?.description}
  Note: Return an array of text only.\n
  No yapping!`;
                  const messages = [
                    {
                      role: 'user',
                      content: prompt,
                    },
                  ];

                  const payload = {
                    messages,
                    numOfChoice: 1,
                  };

                  setValue('leadSelected', {
                    ...leadSelect,
                    joblocationcity:
                      leadSelect?.address?.address1 ||
                      leadSelect?.address?.city,
                    company: getValues('currentCp'),
                  });
                  setValue(
                    'joblocationcity',
                    leadSelect?.address?.address1 || leadSelect?.address?.city
                  );
                  setValue('address1', leadSelect?.address?.address1);
                  setValue('jobtitle', leadSelect?.title);
                  setLeadByCompanyId(value);
                  setOpenSendEmail(true);

                  const { data } = await generateAIAssistantMessage(payload);

                  const { choices } = data;
                  const results = choices.map(
                    (result) => result?.message?.content || ''
                  );

                  const skills = JSON.parse(results);
                  setValue('skills', skills);
                }}
                placeholder="Search Vacancy"
                options={companyOptionsJobOrder?.map((item) => ({
                  value: item.id,
                  id: item.id,
                  valueName: item?.title,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base">
                            {item?.title}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex">
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <PhoneOutlined /> {item?.phone ? item.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <InfoCircleOutlined />{' '}
                              {item?.status ? item.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <EnvironmentOutlined />
                              {item?.max_address_city ?? '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
              />
            </div>
            {(loadingContact || loadingFindVacancy) && <Spin />}
          </div>
          {!loadingContact && !loadingDataEmail && openModalSendEmail && (
            <Drawer
              maskClosable={false}
              className="sequence-background-container"
              width={'100%'}
              getContainer={false}
              // style={{ overflowY: 'auto', top: 10 }}
              // title={
              //   <div
              //     style={{ display: 'flex', justifyContent: 'space-between' }}
              //   >
              //     <div>Create a new Sequence From Vacancy</div>
              //   </div>
              // }
              open={openModalSendEmail}
              onClose={() => {
                setValue('sendMail.listEmailSend', []);
                setValue('sendMail.content', null);
                setValue('sendMail.subject', null);
                setValue('sendMail.mailStepParentMailTo', []);
                setValue('sequenceData.name', null);
                setInputNumberStep([]);
                setOpenSendEmail(false);
                reset();
                setValue('companyId', companyId);
              }}
              footer={false}
            >
              <div className="grid grid-cols-10 overflow-y-auto">
                <div className="h-full overflow-scroll p-3 col-span-6 border rounded-md max-h-[38rem]">
                  <JobDetail job={getValues('leadSelected')} />
                </div>
                <div
                  key={getValues('leadSelected')?.toString()}
                  className="sequence-detail col-span-4 create-sequence-from-vacancy-container"
                >
                  <Form layout="vertical">
                    <BullhornSendEmail
                      watch={watch}
                      control={control}
                      setValue={setValue}
                      getValues={getValues}
                      sendToEmail={getValues()?.email}
                      mailTitle={getValues()?.jobtitle}
                      openModalSendEmail={openModalSendEmail}
                      setOpenSendEmail={setOpenSendEmail}
                      listAddContactSelected={[]}
                      setNumberStep={setNumberStep}
                      numberStep={numberStep}
                      inputNumberStep={inputNumberStep}
                      setInputNumberStep={setInputNumberStep}
                      job={getValues('leadSelected')}
                      setEmailConfigData={setEmailConfigData}
                      emailConfigData={emailConfigData}
                      fromSequenseEmail={true}
                      loadingDataEmail={loadingDataEmail}
                      newUpdatedSequence={true}
                      fromCreateByVacancy={true}
                      notLoadingData={true}
                      closeCreateSequenceModal={() => {
                        setOpen(false);
                        closeCreateSequenceModal();
                      }}
                    />
                  </Form>
                </div>
              </div>
            </Drawer>
          )}
        </div>
      )}
    </>
  );
}

export default ModalCreateFromVancancy;
