import { useEffect, useState } from 'react';
import { Button, Table, Tooltip } from 'antd';
import { searchBullhornData } from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { COMMON_STRINGS } from '../../constants/common.constant';
import Search from 'antd/es/input/Search';
import { CloseCircleOutlined, SearchOutlined } from '@ant-design/icons';

const columns = [
  {
    title: COMMON_STRINGS.NAME,
    key: 'name',
    dataIndex: 'name',
    // align: 'center',
    minWidth: '70%',
    render: (text, record) => (
      <div className="flex gap-4">
        <div className="w-full">
          <div
            className="text-sm font-semibold line-clamp-1"
            title={record?.name}
          >
            {record?.name}
          </div>
        </div>
      </div>
    ),
  },
  {
    title: 'Count',
    key: 'clientContacts',
    dataIndex: 'clientContacts',
    align: 'center',
    render: (clientContacts, record) => {
      const count =
        (+record?.clientContactCount > 0 && +record?.clientContacts?.total) ||
        (+record?.candidateCount > 0 && +record?.candidates?.total) ||
        0;
      return (
        <div className="w-full flex items-center justify-center font-medium">
          {count}
        </div>
      );
    },
  },
];

const HotListModal = ({
  submitList,
  hotList,
  setParticipantActiveKey,
  disabled,
}) => {
  const [selectedHotList, setSelectedHotList] = useState([]);
  // useState are
  const [hotListData, setHotListData] = useState([]);
  const [rawHotListData, setRawHotListData] = useState([]);
  const [loadingHotList, setLoadingHotList] = useState(true);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedHotList(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedHotList,
    onChange: onSelectChange,
    type: 'checkbox',
  };

  const handleHotListSearch = async () => {
    try {
      const { data } = await searchBullhornData('Tearsheet')(0, 1000);
      setLoadingHotList(false);
      if (data?.result?.length > 0) {
        const dataTemp = [...data?.result];
        setHotListData([...dataTemp]);
        setRawHotListData([...dataTemp]);
      }
    } catch (error) {
      setLoadingHotList(false);
    }
  };

  useEffect(() => {
    handleHotListSearch('');
  }, []);

  useEffect(() => {
    if (hotList?.length > 0) {
      const selectedRowKeysTemp = hotList?.map((list) => list?.id || list);
      setSelectedHotList([...selectedRowKeysTemp]);
    }
  }, [hotList]);

  return (
    <div>
      <div className="flex justify-start w-full">
        <div className="flex items-center gap-3 w-full">
          <Search
            allowClear
            // className="customize-search-container"
            placeholder="Search..."
            loading={loadingHotList}
            onSearch={(text) => {
              const newHotListData = rawHotListData?.filter(
                (item) =>
                  item?.name
                    ?.trim()
                    .toLowerCase()
                    ?.includes(text.trim().toLowerCase()) ||
                  item?.description
                    ?.trim()
                    .toLowerCase()
                    ?.includes(text.trim().toLowerCase())
              );
              setHotListData([...newHotListData]);
            }}
            enterButton={<SearchOutlined />}
            size="middle"
          />
        </div>
      </div>
      <div className="search-table-new-design-container">
        <Table
          size="small"
          rowSelection={rowSelection}
          rowKey={(record) => record?.id}
          loading={loadingHotList}
          dataSource={hotListData}
          columns={columns}
          className="custom-table"
          pagination={{
            pageSize: 5,
            showSizeChanger: false,
          }}
          onRow={(record, rowIndex) => {
            return {
              onClick: () => {
                // navigate(`/contact_list/${record.id}`);
                const currSelectedHotList = [...selectedHotList];
                if (currSelectedHotList?.includes(record?.id)) {
                  const removedHotList = currSelectedHotList?.filter(
                    (listId) => listId !== record?.id
                  );
                  onSelectChange([...removedHotList]);
                } else {
                  onSelectChange([...currSelectedHotList, record.id]);
                }
              },
              style: { cursor: 'pointer' },
            };
          }}
        />
      </div>
      <div className="flex justify-end gap-1 pt-5">
        {selectedHotList?.length > 0 && (
          <Tooltip title={'Remove selection.'}>
            <Button
              type="text"
              onClick={() => setSelectedHotList([])}
              icon={<CloseCircleOutlined />}
            />
          </Tooltip>
        )}
        <Button
          disabled={loadingHotList || disabled}
          type="primary"
          onClick={() => {
            const listHotList = hotListData?.filter((list) =>
              selectedHotList?.includes(list?.id)
            );
            submitList(listHotList);
            setParticipantActiveKey([]);
          }}
        >
          {`Select ${selectedHotList?.length > 0 ? `(${selectedHotList?.length})` : ''}`}
        </Button>
      </div>
    </div>
  );
};

export default HotListModal;
