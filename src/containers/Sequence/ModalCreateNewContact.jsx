import { Button, Input, Modal, notification } from 'antd';
import { useForm } from 'react-hook-form';
import { addUserToList } from '../../services/contactList';
import { useState } from 'react';

function ModalCreateNewContact(props) {
  const { openCreateNew, setOpenCreateNew, currentDetail, handleGetDetailData } = props;
  const [loadingCreateNew, setLoadingCreateNew] = useState(false)
  const handleClose = () => {
    setOpenCreateNew(false);
  };

  const { setValue, getValues } = useForm();

  const handleSuccess = async() => {
    console.log(getValues('firstName'), getValues())
    try {
      if (!getValues('firstName')) {
        notification.error({
          message: 'First name is required',
        });
        return;
      }
      if (!getValues('lastName')) {
        notification.error({
          message: 'Last name is required',
        });
        return;
      }
      if (!getValues('email')) {
        notification.error({
          message: 'Email is required',
        });
        return;
      }

      setLoadingCreateNew(true)

      const payload = {
        firstName: getValues('firstName'),
        lastName: getValues('lastName'),
        email: getValues('email'),
        phone: getValues('phone'),
        listIds: [currentDetail?.id],
        linkedinUrl: getValues("linkedinUrl")
      }

      const {data} = await addUserToList(payload)

      if (data) {
        notification.success({
          message: 'Create new contact success',
        });
        setOpenCreateNew(false);
        handleGetDetailData()
      }
      setLoadingCreateNew(false)
    } catch (error) {
      notification.error({
        message: 'Something went wrong',
      });
    }
    setLoadingCreateNew(false)
  };

  return (
    <Modal
      onCancel={() => handleClose()}
      open={openCreateNew}
      width={700}
      title={'Create new Contact'}
      footer={
        <div style={{ marginTop: '20px' }}>
          <Button>Cancel</Button>
          <Button type="primary" onClick={() => handleSuccess()} loading={loadingCreateNew}>
            Save
          </Button>
        </div>
      }
    >
      <div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ width: '150px' }}>
            First name <span style={{ color: 'red' }}>*</span>:
          </div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => { console.log(e.target.value); setValue('firstName', e.target.value)}}
              placeholder="Input First Name"
            />
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '150px' }}>
            Last Name <span style={{ color: 'red' }}>*</span>:
          </div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => setValue('lastName', e.target.value)}
              placeholder="Input Last Name"
            />
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '150px' }}>Job Title:</div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => setValue('jobTitle', e.target.value)}
              placeholder="Input JobTitle"
            />
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '150px' }}>Phone Number:</div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => setValue('phone', e.target.value)}
              placeholder="Phone Number"
            />
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '150px' }}>
            Email <span style={{ color: 'red' }}>*</span>:
          </div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => setValue('email', e.target.value)}
              placeholder="Email"
            />
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '150px' }}>Linkedin Profile Url:</div>
          <div style={{ width: '550px' }}>
            <Input
              onChange={(e) => setValue('linkedinUrl', e.target.value)}
              placeholder="Linkedin Profile Url"
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}

export default ModalCreateNewContact;
