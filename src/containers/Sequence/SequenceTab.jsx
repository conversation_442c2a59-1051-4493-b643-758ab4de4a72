import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Input,
  Menu,
  Modal,
  Pagination,
  Progress,
  Rate,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  Tag,
  Tooltip,
  notification,
} from 'antd';
import EditableNumber from '../../components/EditableNumberComponent/EditableNumber';
import LeadStatusCRUD from '../../components/JobsLeads/LeadStatusCRUD';
import LeadsDragDrop from '../../components/JobsLeads/LeadsDragDrop';
import useJobLeads from '../../hooks/useJobLeads';
import { bulkUpdateLeadStatus } from '../../services/jobLeadStatuses';

import { useDispatch } from 'react-redux';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { saveAllStatusLead } from '../../store/common';
import Mailbox from '../../components/Mailbox';
import {
  countLeadMailBox,
  getLeadsByCompany,
  getMyLeadCompany,
} from '../../services/myLead';
import {
  getUserViewAs,
  getUserViewAsLicenseType,
} from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import { MdOutlineMoreHoriz } from 'react-icons/md';
import {
  CaretDownOutlined,
  CoffeeOutlined,
  ContactsOutlined,
  CrownOutlined,
  DownOutlined,
  EnvironmentOutlined,
  FileProtectOutlined,
  FireFilled,
  HeartOutlined,
  InfoCircleOutlined,
  MailOutlined,
  MoreOutlined,
  PhoneOutlined,
  SearchOutlined,
  UserOutlined,
  CalendarOutlined,
  DeleteOutlined,
  SettingOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { getValue } from '@mui/system';
import SequenceItem from './SequenceItem';
import ModalCreateListSequence from './ModalCreateListSequence';
import {
  bulkStopSequence,
  deleteManySequence,
  getAllEmails,
  updateMarkCompleted,
} from '../../services/search';
import SequenceDetail from './SequenceDetail';
import dayjs from 'dayjs';
import TagStatus from '../../components/TagStatus';
import { conicColors } from '../SequenceTemplate/constant';
import { Popover } from '../../components/Popup';
import { DateRangePickerOne } from '../../components/DatePicker';
import clsx from 'clsx';

import _ from 'lodash';
import SequenceSettings from './SequenceSettings';
import ModalCreateFromVancancy from './ModalCreateFromVancancy';
import ModalCreateFromVClone from './ModalCreateFromClone';
import ModalCreateFromCandidate from './ModalCreateFromCandidate';
import ModalCreateFromTemplateInPage from './ModalCreateFromTemplateInPage';
import { useSearchParams } from 'react-router-dom';
import { licenseType } from '../../constants/common.constant';

const STATUS_LABEL = {
  ALL: 'All status',
  LIVE: 'Live',
  STOP: 'Stopped',
  COMPLETED: 'Completed',
  DRAFT: 'Draft',
};

const FROM_RESOURCE_LABEL = {
  ALL: 'All Recipient Source',
  VACANCY: 'Vacancy',
  HOT_LIST: 'CRM List',
  CONTACT_LIST: 'Zileo Contact List',
  OPPORTUNITY: 'Zileo Opportunity',
  LEAD: 'Zileo Leads',
};

export const SEQUENCE_CREATING_TYPES = {
  FROM_VACANCY: 'FROM_VACANCY',
  FROM_CANDIDATE: 'FROM_CANDIDATE',
  FROM_TEMPLATE: 'FROM_TEMPLATE',
  FROM_SCRATCH: 'FROM_SCRATCH',
};

function SequencesTab({ tabDetail, setTabDetail }) {
  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const [searchParams] = useSearchParams();
  const statusParam = searchParams.get('status');
  const [liveStatus, setLiveStatus] = useState(false);
  const [stoppedStatus, setStoppedStatus] = useState(false);
  const [completedStatus, setCompletedStatus] = useState(false);

  const [dataEmailsSequence, setDataEmailSequence] = useState([]);
  // const [tabDetail, setTabDetail] = useState(false);
  const [pending, setPending] = useState(false);
  const [selectedValue, setSelectedValue] = useState(false);
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [deleteSequenceList, setDeleteSequenceList] = useState([]);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [listDeletedId, setListDeletedId] = useState([]);
  const [openPopup, setOpenPopup] = useState(false);
  // add startDate and endDate
  const [sequenceChange, setSequenceChange] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [sequenceFilter, setSequenceFilter] = useState({
    statusSelect: 'ALL',
    createFromSource: 'ALL',
    startDate: '',
    endDate: '',
    searchText: '',
  });

  // Settings tab
  const [openSettingsTab, setOpenSettingsTab] = useState(false);
  const showSettingTab = () => setOpenSettingsTab(true);
  const closeSettingTab = () => setOpenSettingsTab(false);

  // Selected Creating Sequence Types
  const [selectedCreatingType, setSelectedCreatingType] = useState(null);

  const queryParams = new URLSearchParams(location.search);

  const seqId = queryParams.get('seqId');

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const handleSelectDateRange = (dateRange) => {
    if (!dateRange) {
      setSequenceFilter({
        ...sequenceFilter,
        startDate: '',
        endDate: '',
      });
      setOpenPopup(false);
      return;
    }
    if (dateRange) {
      const [fromDate, toDate] = dateRange
        .split(' - ')
        .map((dateStr) => dayjs(dateStr).format('YYYY-MM-DD'));
      setSequenceFilter({
        ...sequenceFilter,
        startDate: fromDate,
        endDate: toDate,
      });
      setOpenPopup(false);
    }
  };

  const handleSelectDelete = (id) => {
    const index = deleteSequenceList.indexOf(id);
    if (index === -1) {
      setDeleteSequenceList([...deleteSequenceList, id]);
    } else {
      const newItems = [...deleteSequenceList];
      newItems.splice(index, 1);
      setDeleteSequenceList(newItems);
    }
  };

  const handleDateSequence = async (
    statusProp = null,
    pageProp = null,
    limit = 10,
    createdFromProp = null,
    searchTextProp = null,
    fromDateProp = null,
    toDateProp = null
  ) => {
    setPending(true);
    const status =
      statusProp ||
      (sequenceFilter?.statusSelect === 'ALL'
        ? ''
        : sequenceFilter?.statusSelect);
    const queryPage = pageProp || 1;
    const search = searchTextProp || sequenceFilter?.searchText;
    const fromDate = fromDateProp || sequenceFilter?.startDate;
    const toDate = toDateProp || sequenceFilter?.endDate;
    const createdFrom =
      createdFromProp ||
      (sequenceFilter?.createFromSource === 'ALL'
        ? ''
        : sequenceFilter?.createFromSource);

    const { data } = await getAllEmails(
      status,
      queryPage,
      limit,
      null,
      createdFrom,
      search,
      fromDate,
      toDate
    );
    setDataEmailSequence(data?.result?.mails);
    setCountPage(data?.result?.count);
    setPending(false);
  };

  const debouncedSearchText = _.debounce(async (e) => {
    setSequenceFilter({ ...sequenceFilter, searchText: e?.target?.value });
  }, 500);

  useEffect(() => {
    setDataEmailSequence(
      dataEmailsSequence?.filter((item) => !listDeletedId.includes(item.id))
    );
  }, [listDeletedId]);

  useEffect(() => {
    if (!sequenceFilter) return;
    setPage(1);
    handleDateSequence();
  }, [sequenceFilter]);

  useEffect(() => {
    setSequenceFilter({
      ...sequenceFilter,
      statusSelect: statusParam || 'ALL',
    });
  }, [statusParam]);

  const handlePagination = (page) => {
    setPage(page);
    handleDateSequence(null, page);
  };

  const handleDeleteMany = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await deleteManySequence({
        sequenceIds: deleteSequenceList,
      });
      if (data) {
        setLoadingDelete(false);
        notification.success({
          message: 'Deleted successfully',
        });
        setDeleteSequenceList([]);
        handleDateSequence();
      }
    } catch (e) {
      notification.error({
        message: 'Something went wrong happened',
      });
    }
  };

  const updateMarkCompletedHandle = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await updateMarkCompleted({
        sequenceIds: deleteSequenceList,
      });
      if (data) {
        setLoadingDelete(false);
        notification.success({
          message: 'Mark Complete successfully',
        });
        setDeleteSequenceList([]);
        handleDateSequence();
      }
    } catch (e) {
      notification.error({
        message: 'Something went wrong happened',
      });
    }
  };

  const handleStopSequence = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await bulkStopSequence({
        sequenceIds: deleteSequenceList,
      });
      if (data) {
        setLoadingDelete(false);
        notification.success({
          message: 'Bulk stop sequences successfully',
        });
        setDeleteSequenceList([]);
        handleDateSequence();
      }
    } catch (e) {
      notification.error({
        message: 'Something went wrong happened',
      });
    }
  };

  useEffect(() => {
    if (seqId) {
      setTabDetail(true);
    }
  }, [seqId]);

  const itemStatus = [
    {
      label: (
        <div
          className="font-Montserrat px-2 py-1 border-[#e6e9ed] bg-[#eff1f3] text-[#674677] text-center"
          onClick={async () => {
            setStoppedStatus(!stoppedStatus);
            setLiveStatus(false);
          }}
        >
          All Status
        </div>
      ),
      key: 'ALL',
    },
    {
      label: (
        <div
          className="font-Montserrat px-2 py-1 border-[#9ce9cf] bg-[#e6fff8] text-[#00996d] text-center"
          onClick={async () => {
            setLiveStatus(!liveStatus);
            setStoppedStatus(false);
          }}
        >
          Live
        </div>
      ),
      key: 'LIVE',
    },
    {
      label: (
        <div
          className="font-Montserrat px-2 py-1 border-[#eb4d4d] bg-[#f8aeae] text-[#b63838] text-center"
          onClick={async () => {
            setStoppedStatus(!stoppedStatus);
            setLiveStatus(false);
          }}
        >
          Stopped
        </div>
      ),
      key: 'STOP',
    },
    {
      label: (
        <div
          className="font-Montserrat px-2 py-1 border-[#eb4d4d] bg-[#5c98fa] text-[#f7f7f7] text-center"
          onClick={async () => {
            setCompletedStatus(!completedStatus);
            setLiveStatus(false);
          }}
        >
          Completed
        </div>
      ),
      key: 'COMPLETED',
    },
    {
      label: (
        <div
          className="font-Montserrat px-2 py-1 border-[#eb4d4d] bg-[#1A1A19] text-white text-center"
          onClick={async () => {
            setCompletedStatus(!completedStatus);
            setLiveStatus(false);
          }}
        >
          Draft
        </div>
      ),
      key: 'DRAFT',
    },
  ];

  const deleteItems = [
    {
      key: '1',
      label: (
        <div
          className="font-Montserrat"
          onClick={() => {
            handleDeleteMany();
          }}
        >
          Delete
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div
          className="font-Montserrat"
          onClick={() => {
            updateMarkCompletedHandle();
          }}
        >
          Mark as Completed
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <div
          className="font-Montserrat"
          onClick={() => {
            handleStopSequence();
          }}
        >
          Stop
        </div>
      ),
    },
  ];

  const itemRecipientSource = [
    {
      label: <div>All Recipient Source</div>,
      key: 'ALL',
    },
    {
      label: <div>Vacancy</div>,
      key: 'VACANCY',
    },
    {
      label: <div>CRM List</div>,
      key: 'HOT_LIST',
    },
    {
      label: <div>Zileo contact list</div>,
      key: 'CONTACT_LIST',
    },
    {
      label: <div>Opportunity</div>,
      key: 'OPPORTUNITY',
    },
    {
      label: <div>Lead</div>,
      key: 'LEAD',
    },
  ];

  const columns = [
    {
      title: 'Status',
      key: 'status',
      dataIndex: 'status',
      // align: 'center',
      render: (text, record) => (
        <div className="flex gap-4 ">
          <TagStatus record={record} sequenceChange={sequenceChange} />
        </div>
      ),
    },
    {
      title: 'Name',
      key: 'name',
      dataIndex: 'name',
      // align: 'center',
      render: (text, record) => (
        <div className="flex gap-4 ">
          <div className="w-full">
            <div
              style={{
                fontWeight: '600',
                // cursor: 'pointer',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '400px',
              }}
            >
              {record?.name}
            </div>
            <div className="flex items-center gap-3">
              <span style={{ color: 'blue', fontSize: '12px' }}>
                {record?.sender}
              </span>
              <span style={{ fontSize: '12px' }}>{record?.stepCount} step</span>
              {record?.hasChildren && (
                <Tooltip title="Sequence has already started the new sequence.">
                  <Tag color="magenta">Extended</Tag>
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Open',
      key: 'openRate',
      dataIndex: 'openRate',
      align: 'center',
      render: (text, record) => {
        const { openRate } = record;
        const displayingData = isNaN(openRate)
          ? '0'
          : parseInt(text) > 100
            ? '100'
            : parseInt(text);
        return (
          <div className="w-full flex items-center justify-center">
            <Progress
              size={45}
              type="circle"
              percent={displayingData}
              strokeColor={conicColors}
              status={'active'}
            />
          </div>
        );
      },
    },
    {
      title: 'Response',
      key: 'replyRate',
      dataIndex: 'replyRate',
      align: 'center',
      render: (text, record) => {
        const { replyRate } = record;
        const displayingData = isNaN(replyRate)
          ? '0'
          : parseInt(text) > 100
            ? '100'
            : parseInt(text);
        return (
          <div className="w-full flex items-center justify-center">
            <Progress
              size={45}
              type="circle"
              percent={displayingData}
              strokeColor={conicColors}
              status={'active'}
            />
          </div>
        );
      },
    },
    {
      title: 'Sent',
      key: 'sentCount',
      dataIndex: 'sentCount',
      align: 'center',

      render: (text) => {
        return (
          <Tooltip title={`${text} email(s) sent`}>
            <div className="w-full flex items-center justify-center">
              <span className="h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700">
                {text}
              </span>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Link Clicks',
      key: 'linkClicked',
      dataIndex: 'linkClicked',
      align: 'center',
      render: (text) => {
        return (
          <Tooltip title={`${text} link(s) clicked`}>
            <div className="w-full flex items-center justify-center">
              <span className="h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700">
                {text}
              </span>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Date Created',
      key: 'createdAt',
      dataIndex: 'createdAt',
      align: 'center',
      render: (text, record) => (
        <div className="font-medium">
          {record?.recreatedAt
            ? dayjs(record?.recreatedAt).format('DD/MM/YYYY hh:mm A')
            : dayjs(text).format('DD/MM/YYYY hh:mm A') || ''}
        </div>
      ),
    },
    {
      title: 'Opened',
      key: 'opened',
      dataIndex: 'opened',
      align: 'center',
      render: (text) => {
        return (
          <Tooltip title={`${text} email(s) opened`}>
            <div className="w-full flex items-center justify-center">
              <span className="h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700">
                {text}
              </span>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: ''.toUpperCase(),
      key: 'action',
      dataIndex: 'action',
      align: 'center',
      render: (text, record) => (
        <div onClick={(e) => e.stopPropagation()}>
          <SequenceItem
            mailItem={record}
            tabDetail={tabDetail}
            setTabDetail={setTabDetail}
            setSelectedValue={setSelectedValue}
            handleDateSequence={handleDateSequence}
            selectedValue={selectedValue}
            setLoadingStatus={setLoadingStatus}
            setDeleteSequenceList={setDeleteSequenceList}
            handleSelectDelete={handleSelectDelete}
            deleteSequenceList={deleteSequenceList}
            setListDeletedId={setListDeletedId}
            listDeletedId={listDeletedId}
            setSequenceChange={setSequenceChange}
            sequenceChange={sequenceChange}
            setDataEmailSequence={setDataEmailSequence}
            dataEmailsSequence={dataEmailsSequence}
          />
        </div>
      ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setDeleteSequenceList(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: deleteSequenceList,
    onChange: onSelectChange,
  };

  const backTo = () => {
    setSelectedCreatingType(null);
  };

  const CREATING_SEQUENCE = {
    FROM_SCRATCH: (
      <ModalCreateFromVClone
        backTo={backTo}
        isShowModal={true}
        closeCreateSequenceModal={backTo}
      />
    ),
    FROM_TEMPLATE: (
      <ModalCreateFromTemplateInPage
        backTo={backTo}
        closeCreateSequenceModal={backTo}
      />
    ),
    ...(isStandardUser
      ? {}
      : {
          FROM_VACANCY: (
            <ModalCreateFromVancancy
              backTo={backTo}
              isShowModal={true}
              closeCreateSequenceModal={backTo}
            />
          ),
        }),
    // FROM_CANDIDATE: (
    //   <ModalCreateFromCandidate
    //     backTo={backTo}
    //     isShowModal={true}
    //     closeCreateSequenceModal={backTo}
    //   />
    // ),
  };

  const showClearAll =
    sequenceFilter?.statusSelect !== 'ALL' ||
    sequenceFilter?.createFromSource !== 'ALL' ||
    sequenceFilter?.startDate ||
    sequenceFilter?.endDate ||
    sequenceFilter?.searchText;

  return (
    <div>
      {!selectedCreatingType &&
        (tabDetail ? (
          <>
            <SequenceDetail
              tabDetail={tabDetail}
              handleDateSequence={handleDateSequence}
              setTabDetail={setTabDetail}
              setSelectedValue={setSelectedValue}
              selectedValue={selectedValue}
              page={page}
              fromDate={sequenceFilter.startDate}
              toDate={sequenceFilter.endDate}
              seqId={seqId}
            />
          </>
        ) : openSettingsTab ? (
          <SequenceSettings closeSettingTab={closeSettingTab} />
        ) : (
          <div>
            <>
              <div className="flex justify-between pb-5">
                <div className="flex gap-2 items-center">
                  <div>
                    <ModalCreateListSequence
                      reloadData={handleDateSequence}
                      setSelectedCreatingType={setSelectedCreatingType}
                    />
                  </div>

                  <div>
                    <>
                      <Dropdown
                        trigger={'click'}
                        menu={{
                          items: deleteItems,
                        }}
                        disabled={deleteSequenceList?.length === 0}
                        placement="bottomLeft"
                        arrow
                      >
                        <Button
                          loading={loadingDelete}
                          type="primary"
                          className="font-Montserrat bg-white  font-medium flex items-center justify-center"
                        >
                          <span className="mr-1">
                            {deleteSequenceList?.length}
                          </span>
                          <span>SELECTED</span>
                          <DownOutlined />
                        </Button>
                      </Dropdown>
                    </>
                    {/* )} */}
                  </div>

                  <div>
                    <Button
                      onClick={showSettingTab}
                      icon={<SettingOutlined />}
                      type="primary"
                      className=" !border-[#b2b8be] font-medium flex items-center justify-center"
                    >
                      Settings
                    </Button>
                  </div>
                </div>

                <div className="flex gap-2 ">
                  {showClearAll && (
                    <Button
                      danger
                      icon={<CloseOutlined />}
                      onClick={() => {
                        setSequenceFilter({
                          ...sequenceFilter,
                          statusSelect: 'ALL',
                          createFromSource: 'ALL',
                          startDate: '',
                          endDate: '',
                          searchText: '',
                        });
                        setOpenPopup(false);
                      }}
                      className="bg-white font-medium flex items-center justify-center"
                    >
                      Clear all
                    </Button>
                  )}
                  <Dropdown
                    menu={{
                      items: itemStatus,
                      onClick: (option) => {
                        setSequenceFilter({
                          ...sequenceFilter,
                          statusSelect: option.key,
                        });
                      },
                    }}
                    trigger={['click']}
                  >
                    <Button
                      className="font-Montserrat bg-white font-medium flex items-center justify-center"
                      // icon={<DownOutlined />}
                    >
                      <CrownOutlined />
                      <span>
                        {STATUS_LABEL[sequenceFilter?.statusSelect] ??
                          'All Status'}
                      </span>
                      {/* <CaretDownOutlined /> */}
                    </Button>
                  </Dropdown>
                  <Dropdown
                    menu={{
                      items: itemRecipientSource,
                      onClick: (option) => {
                        setSequenceFilter({
                          ...sequenceFilter,
                          createFromSource: option?.key,
                        });
                      },
                    }}
                    trigger={['click']}
                  >
                    <Button
                      // icon={<DownOutlined />}
                      className="font-Montserrat bg-white font-medium flex items-center justify-center"
                    >
                      <FileProtectOutlined />
                      <span>
                        {FROM_RESOURCE_LABEL[
                          sequenceFilter?.createFromSource
                        ] ?? 'All Recipient Source'}
                      </span>
                    </Button>
                  </Dropdown>
                  <Popover
                    placement="bottomRight"
                    title="Search by Calendar"
                    openStatus={openPopup}
                    setOpenStatus={setOpenPopup}
                    content={
                      <DateRangePickerOne
                        handleSelectDateRange={handleSelectDateRange}
                        setOpenPopup={setOpenPopup}
                      />
                    }
                    action="click"
                  >
                    <Button
                      onClick={(e) => {
                        e.stopPropagation(), setOpenPopup(true);
                      }}
                      // type="primary"
                      className="gap-2 py-3 flex items-center bg-[#fff] font-medium hover:text-[#2684c7] text-[#5a5f7d]"
                    >
                      <CalendarOutlined className="text-[#2684c7]" />
                      <p className="font-Inter">
                        {sequenceFilter.startDate && sequenceFilter.endDate
                          ? `${dayjs(sequenceFilter.startDate).format('DD/MM/YYYY')} - ${dayjs(sequenceFilter.endDate).format('DD/MM/YYYY')}`
                          : 'All Time'}
                      </p>
                    </Button>
                  </Popover>
                  {sequenceFilter.startDate && sequenceFilter.endDate && (
                    <DeleteOutlined
                      onClick={() => {
                        setSequenceFilter({
                          ...sequenceFilter,
                          startDate: '',
                          endDate: '',
                        });
                      }}
                    />
                  )}

                  <Input
                    style={{
                      width: '300px',
                      height: '32px',
                    }}
                    className="font-Montserrat  search-input"
                    onChange={debouncedSearchText}
                    placeholder="Sequence name, company, contact, email"
                  />
                </div>
              </div>

              {pending ? (
                <div className="w-full flex justify-center h-20 items-center">
                  <Spin></Spin>
                </div>
              ) : (
                <div>
                  <div className="search-table-new-design-container">
                    <Table
                      rowSelection={rowSelection}
                      rowKey={(record) => record.id}
                      loading={pending}
                      dataSource={dataEmailsSequence}
                      columns={columns}
                      className="custom-table"
                      onRow={(record, rowIndex) => {
                        return {
                          onClick: () => {
                            setTabDetail(true);
                            setSelectedValue(record);
                          },
                          style: { cursor: 'pointer' },
                        };
                      }}
                      pagination={false}
                      // rowClassName="custom-row"
                      // bordered
                    />
                  </div>
                  <div style={{ marginTop: '30px' }}>
                    <Pagination
                      defaultCurrent={page}
                      defaultPageSize={10}
                      total={countPage}
                      showSizeChanger={false}
                      onChange={handlePagination}
                    />
                  </div>
                </div>
              )}
            </>
          </div>
        ))}
      {selectedCreatingType && CREATING_SEQUENCE[selectedCreatingType]}
    </div>
  );
}

export default SequencesTab;
