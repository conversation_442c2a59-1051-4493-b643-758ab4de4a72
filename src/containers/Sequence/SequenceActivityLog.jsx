import React, { useEffect, useState } from 'react';
import { getActivityLog } from '../../services/search';
import {
  Avatar,
  Button,
  Modal,
  notification,
  Spin,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { transFormDateFormat } from '../../utils/date';
import { PropTypes } from 'prop-types';
import { UserOutlined } from '@ant-design/icons';
import handleRenderTime from '../../function/handleRenderTime';
import { v4 as uuid } from 'uuid';
import { Spinner } from '@material-tailwind/react';

const mappingTypes = {
  LINKEDIN_CONNECTION_REQUEST: 'LinkedIn Connection Request',
  EMAIL: 'Email',
  NOTE: 'Note',
  TASK: 'Task',
};

const mappingLinkedInTypes = {
  INVITATION: 'Connection Request',
  INMAIL: 'InMail',
  NORMAL_MESSAGE: 'Normal Message',
};

const eventDescription = {
  deferred: {
    message: 'Email step $number was deferred',
    name: 'DEFERRED',
    color: 'volcano',
  },
  bounce: {
    message: 'Email step $number was bounced by <b>$email</b></div>',
    name: 'BOUNCE',
    color: 'volcano',
  },
  dropped: {
    message: 'Email step $number was dropped',
    name: 'DROPPED',
    color: 'error',
  },
  spamreport: {
    message: 'Email step $number was reported as spam',
    name: 'SPAM',
    color: 'error',
  },
  delivered: {
    message: 'Email step $number was delivered',
    name: 'DELIVERED',
    color: 'green',
  },
  sent: {
    message: '$type step $number was sent',
    name: 'SENT',
    color: 'green',
  },
  updated: {
    message: 'Sequence was updated',
    name: 'UPDATED',
    color: 'geekblue',
  },
  created: {
    message: 'Sequence was created',
    name: 'CREATED',
    color: 'geekblue',
  },
  replied: {
    // message: '<div>Mail number $number was replied by <b>$email</b> with message $message</div>',
    message: '<div>Email step $number was replied by <b>$email</b></div>',
    name: 'REPLIED',
    color: 'green',
  },
  replied_by_hotlist: {
    message:
      'Email step $number was replied by email $email coming from hot list',
    name: 'REPLIED_BY_HOTLIST',
    color: 'green',
  },
  stopped: {
    message: 'Sequence was stopped by <b>$recipient</b> replied',
    name: 'STOPPED',
    color: 'error',
  },
  lived: {
    message: 'Sequence was marked as LIVE',
    name: 'LIVE',
    color: 'green',
  },
  blocked: {
    message: 'Email step $number to email <b>$email</b> was blocked due to reason: <i>$reason</i>',
    name: 'BLOCKED',
    color: 'error',
  },
  off: {
    message: 'Sequence was stopped by user',
    name: 'STOPPED BY USER',
    color: 'error',
  },
  opened: {
    message: 'Email step $number was opened by <b>$email</b>',
    name: 'OPENED',
    color: 'blue',
  },
  failed: {
    message:
      '$type step $number was sent $recipients but failed. Error: <i>$error</i>',
    name: 'FAILED',
    color: 'error',
  },
  skipped: {
    message:
      '$type step $number is not sent to contact <b>$email</b> because it will be <i>$skipType</i>',
    name: 'NOT SENT',
    color: 'volcano',
  },
  auto_replied: {
    message:
      '<div>$type Email step $number was auto replied by <b>$recipient</b></div>',
    name: 'AUTO REPLIED',
    color: 'blue',
  },
  linkedin_invitation_accepted: {
    message: '<div>LinkedIn Invitation was accepted by <b>$recipient</b></div>',
    name: 'INVITATION ACCEPTED',
    color: 'blue',
  },
  link_clicked: {
    message:
      '<div style="margin-left: 55px"> <b><a href=$url_click target="_blank">Link </a><span style="color: #1677ff">( $url_click_title )</span></b> clicked by <b>$email</b></div>',
    name: 'LINK CLICKED',
    color: 'blue',
  },
};

const SequenceActivityLog = (props) => {
  const { sequenceData, seqId } = props;
  const [data, setData] = useState([]);
  const [triggeredData, setTriggeredData] = useState([]);
  const [rawData, setRawData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  // Email popup
  const [openContent, setOpenContent] = useState(false);
  const [selectedMail, setSelectedMail] = useState(null);

  const openModal = () => setOpenContent(true);
  const closeModal = () => setOpenContent(false);

  const columnData = [
    {
      title: 'Description',
      dataIndex: 'description',
      width: '60%',
      render: (desc, record) => {
        if (
          ['replied', 'bounce', 'auto_replied', 'replied_by_hotlist'].includes(
            record.action
          )
        ) {
          return (
            <Tooltip
              placement="top"
              title={"Click to see full email's content"}
            >
              <div
                onClick={(ev) => {
                  ev.stopPropagation();
                  ev.preventDefault();
                  setSelectedMail({ ...record });
                  openModal();
                }}
                dangerouslySetInnerHTML={{ __html: desc }}
                className={`cursor-pointer ${record?.className || ''}`}
                style={{
                  width: '700px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              />
            </Tooltip>
          );
        } else if (record.linkedInDescriptions) {
          // add linkedInDescriptions is custom field for custom data for linkedIn Step sent
          return (
            <div
              className={`${record?.className || ''}`}
              dangerouslySetInnerHTML={{ __html: record.linkedInDescriptions }}
            />
          );
        } else {
          return (
            <div
              className={`${record?.className || ''}`}
              dangerouslySetInnerHTML={{ __html: desc }}
            />
          );
        }
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      align: 'center',
      render: (tag) =>
        eventDescription?.[tag]?.name && (
          <Tag color={eventDescription?.[tag]?.color} key={tag}>
            {eventDescription?.[tag]?.name}
          </Tag>
        ),
      filters: Object.keys(eventDescription).map((key) => ({
        text: eventDescription?.[key].name,
        value: key,
      })),
      filteredValue: filteredInfo.action,
      onFilter: (value, record) => {
        return record.action.includes(value);
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      align: 'center',
      sorter: (a, b) => {
        return new Date(a.date) - new Date(b.date);
      },
      render: (value) => value && <Tag>{transFormDateFormat(value)}</Tag>,
    },
  ];

  const handleChange = (filters, sorter) => {
    console.log('filter', filters);
    console.log('sorter', sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  };

  const fetchData = async () => {
    setLoading(true);
    let page = 1;
    let limit = 1000;
    let lastResult = [];
    do {
      try {
        const { data } = await getActivityLog(seqId || sequenceData.id, {
          page,
          limit,
        });
        lastResult = data;
        const newData = data?.result?.items;
        setTriggeredData([...newData]);
        page++;
      } catch (error) {
        setLoading(false);
        console.log('error', error);
      }
    } while (lastResult?.result?.length > 0);
    setLoading(false);
  };

  const handleMappingData = async (newData = []) => {
    try {
      const dataTable = newData?.map((item, index) => {
        const recipients = item?.content?.recipients
          ?.map(
            (rep) =>
              (item.content?.type === 'LINKEDIN_CONNECTION_REQUEST'
                ? rep?.name || rep?.email
                : rep?.email) || rep
          )
          .join(', ');

        const mappingVars = {
          $number: (item?.sequenceStep?.stepIndex ?? 0) + 1,
          $name:
            item?.sequenceStep?.name ||
            `Step ${(item?.sequenceStep?.stepIndex ?? 0) + 1}`,
          $recipients: recipients ? `to <b>${recipients}</b>` : '',
          $recipient: item?.content?.recipient,
          $message: item.content?.email?.content,
          $type: mappingTypes[item.content?.type || item?.sequenceStep?.type],
          $email: item?.content?.email || item?.content?.emails?.join(', '),
          $skipType:
            item?.content?.reason === 'bounce'
              ? 'bounced'
              : item?.content?.reason,
          $reason: item?.content?.reason || 'N/A',
          $error:
            typeof item.content?.reason === 'string'
              ? item.content?.reason
              : item.content?.reason?.providerError?.error?.message ||
                item.content?.reason?.message ||
                JSON.stringify(item.content?.reason),
        };
        if (
          item.content?.type === 'LINKEDIN_CONNECTION_REQUEST' &&
          item.content?.recipients?.length
        ) {
          mappingVars['$email'] = item.content?.recipients
            ?.map((rep) => rep?.name || rep?.email || rep)
            .join(', ');
        }

        if (
          item.type === 'replied' ||
          item.type === 'replied_by_hotlist' ||
          item.type === 'bounce'
        ) {
          mappingVars['$email'] = item.content?.recipient;
          mappingVars['$message'] = item.content?.email?.content;
        }

        if (['opened', 'blocked'].includes(item.type)) {
          mappingVars['$email'] = item.content?.email?.content;
        }

        if (item.type === 'link_clicked') {
          mappingVars['$email'] = item.content?.email?.content;
          mappingVars['$url_click'] =
            item.content?.link_onclick || item.content.urlClick;
          mappingVars['$url_click_title'] =
            item.content?.link_onclick || item.content.urlClick;
        }

        let linkedInDescriptions = null;

        let description = Object.keys(mappingVars).reduce((desc, key) => {
          return desc.replace(key, mappingVars[key] ?? '');
        }, eventDescription?.[item.type]?.message || '');

        if (item.type === 'sent' && item.content?.reason) {
          description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
          description = `${description} but failed. Error: <i>${item.content?.reason}</i>`;
        }

        if (
          item.type === 'sent' &&
          !item.content.linkedInType &&
          item.content?.recipients?.length
        ) {
          description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
          description = `${description} to <b>${item.content?.recipients?.join(', ')}</b>`;
        }

        if (
          ['message', 'sent'].includes(item.type) &&
          item.content.linkedInType &&
          item.content?.recipients?.length
        ) {
          description = `${item.content.linkedInType} ${description}`;
          linkedInDescriptions = `${mappingLinkedInTypes[item.content.linkedInType] || item.content.linkedInType} sent`;
          description = `${description} to  <b>${item.content?.recipients?.join(', ')}</b>`;
          linkedInDescriptions = `${linkedInDescriptions} to <b>${item.content?.recipients?.join(', ')}</b>`;
        }

        if (item.type === 'failed' && item.content.linkedInType) {
          description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
        }

        if (item.type === 'stopped' && item.content?.reason) {
          description = `Sequence is stopped because <i>${item.content?.reason}</i>`;
        }

        if (item.content?.type === 'TASK' && item.type === 'created') {
          const displayVars = {
            '{{recipient}}':
              mappingVars['$recipient']?.email || mappingVars['$recipient'],
            ...mappingVars,
          };
          description = Object.keys(displayVars).reduce((desc, key) => {
            return desc.replace(key, displayVars[key]);
          }, '$type step $number was created for <b>{{recipient}}</b>');
        }

        if (item.content?.displayText) {
          const displayVars = {
            '{{step_number}}': mappingVars['$number'],
            '{{contact_name}}': mappingVars['$recipients'],
          };
          description = Object.keys(displayVars).reduce((desc, key) => {
            return desc.replace(key, displayVars[key]);
          }, item.content.displayText);
          linkedInDescriptions = description;
        }

        return {
          key: index,
          description,
          action: item?.type,
          date: item.occurredAt,
          recipient: item.recipient,
          content: item?.content,
          groupedByStep:
            description.includes(`${mappingVars['$type']}`) ||
            item.type === 'linkedin_invitation_accepted' ||
            item.type === 'link_clicked' ||
            item.content?.displayText ||
            item.content?.type
              ? `[${mappingVars['$type']}] ${mappingVars['$name'] || 'UNKNOWN'}`.toUpperCase()
              : '',
          grouptedByType:
            eventDescription[item?.type]?.name?.toUpperCase() ||
            item?.type?.toUpperCase() ||
            'UNKNOWN',
          linkedInDescriptions,
        };
        console.log(
          'iteeeeeeem',
          '#',
          x.groupedByStep,
          '#',
          x.grouptedByType,
          item,
          linkedInDescriptions,
          description
        );
        return x;
      });

      console.log('mappingVars', dataTable);

      const groupedLogsByStep = dataTable.filter((item) => item.groupedByStep);

      const groupedLogsByType = dataTable.filter((item) => !item.groupedByStep);

      const nonGroupedLogs = [];

      const groupedDataByStep = Object.groupBy(
        groupedLogsByStep,
        ({ groupedByStep }) => groupedByStep
      );

      // Process to grouping data by step
      for (const [key, value] of Object.entries(groupedDataByStep)) {
        // Process to grouping data by type in grouping step
        const childrenGroupedByType = [];

        const groupedChildrenData = Object.groupBy(
          [...value],
          ({ grouptedByType }) => grouptedByType
        );

        for (const [childKey, childValue] of Object.entries(
          groupedChildrenData
        )) {
          childrenGroupedByType.unshift({
            key: uuid(),
            description: childKey,
            children: [...childValue],
            className: 'group-name-type-text',
          });
        }
        nonGroupedLogs.push({
          key: uuid(),
          description: key,
          children: [...childrenGroupedByType],
          className: 'group-name-text',
        });
      }

      // Process to grouping other data by type

      const groupedDataByType = Object.groupBy(
        groupedLogsByType,
        ({ grouptedByType }) => grouptedByType
      );

      for (const [key = 'UNKNOWN', value] of Object.entries(
        groupedDataByType
      )) {
        // Process to grouping data by type
        nonGroupedLogs.push({
          key: uuid(),
          description: `${key}`,
          children: [...value],
          className: 'group-name-type-text',
        });
      }
      const finalData = [...nonGroupedLogs];
      setData([...finalData]);
      console.log('finalData', finalData);
    } catch (error) {
      // setLoading(false);
      notification.error({
        message: 'Fail to fetch data',
        duration: 3,
      });
    }

    // setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (triggeredData.length === 0) return;
    const newData = [...rawData, ...triggeredData];
    setRawData(newData);
    handleMappingData(newData);
  }, [triggeredData]);

  return (
    <>
      <div className="search-table-new-design-container activity-logs-table-container">
        {loading && (
          <div className="w-full flex items-center justify-start p-2">
            <Spin />
          </div>
        )}
        <Table
          pagination={false}
          columns={columnData}
          dataSource={data}
          onChange={handleChange}
        />
      </div>

      {/* Preview Email Content modal */}
      <Modal
        // title="Preview Content"
        open={openContent}
        onCancel={closeModal}
        footer={<Button onClick={closeModal}>Close</Button>}
        destroyOnClose={true}
        closable={false}
        width={650}
      >
        <div className="p-2">
          <div className="flex justify-between items-center p-3">
            <div className="flex items-center gap-2">
              <Avatar icon={<UserOutlined />} />
              <span
                title={selectedMail?.content?.recipient}
                className="font-semibold max-w-[15rem] line-clamp-1"
              >
                {selectedMail?.content?.recipient || '-'}
              </span>
            </div>
            <div className="text-[#bbbbbb] italic text-sm">
              {selectedMail?.date ? handleRenderTime(selectedMail?.date) : ''}
            </div>
          </div>
          <div
            className=" border rounded-md p-3 overflow-hidden"
            dangerouslySetInnerHTML={{
              __html: selectedMail?.content?.email?.content,
            }}
          />
        </div>
      </Modal>
    </>
  );
};

SequenceActivityLog.propTypes = {
  sequenceData: PropTypes.any,
};

export default SequenceActivityLog;
