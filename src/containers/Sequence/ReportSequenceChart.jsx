import {
  FireOutlined,
  InfoCircleOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>confirm, Spin } from 'antd';
import ReactDOM from 'react-dom';
import {
  CartesianGrid,
  <PERSON><PERSON>hart,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  ReferenceLine,
  AreaChart,
  Area,
  Line,
} from 'recharts';
import { useLayoutEffect, useRef, useState } from 'react';
import { getSequenceReport } from '../../services/search';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { toPng } from 'html-to-image';

const getStatusColor = (statusName) => {
  switch (statusName) {
    case 'LIVE':
      return 'border-[#9ce9cf] bg-[#e6fff8] text-[#00996d]';
    case 'STOP':
      return 'border-[#fff2f0] bg-[#fff2f0] text-[#ff4d4f]';
    default:
      return 'border-[#fafafa] bg-[#fafafa] text-[#000]';
  }
};

const dataStats = [
  {
    key: 'bounceRate',
    title: 'Bounce Rate',
    value: 0,
    description:
      'Bounce Rate is defined as the percentage of total NOT existing recipient address that leave a email without approaching.',
    addonAfter: '%',
  },
  // {
  //   key: 'openRate',
  //   title: 'Open Rate',
  //   value: 0,
  //   description:
  //     'Email open rate is the percentage of subscribers who open a specific email out of your total number of subscribers.',
  //   addonAfter: '%',
  // },
  {
    key: 'clickRate',
    title: 'Click Rate',
    value: 0,
    description:
      'The click-through rate represents the percentage of people who not only opened the email but also clicked on the link or ad in the email',
    addonAfter: '%',
  },
  {
    key: 'replyRate',
    title: 'Reply Rate',
    value: 0,
    description:
      'Email response rate is an email marketing metric that measures the percentage of emails sent that get a response',
    addonAfter: '%',
  },
];

const ReportSequenceChart = ({ sequenceData, seqId }) => {
  const currentChart = useRef(null);
  const [exportLoading, setExportLoading] = useState(false);

  const [state, setState] = useState({
    responsive: 0,
  });

  const [reportData, setReportData] = useState({
    data: [],
    total: {
      replyRate: '0',
      openRate: '0',
      clickRate: '0',
      bounceRate: '0',
    },
  });
  const [loading, setLoading] = useState(true);

  const getSequenceReportData = async (seqId) => {
    const { data } = await getSequenceReport(seqId);
    if (data?.result) {
      const { data: timeSeriData, rate } = data?.result;

      const chartData = timeSeriData?.map((item) => ({
        name: dayjs(item?.dateTime).format('MMMM D'),
        'Bounce Action': item?.bounceRate || 0,
        'Reply Action': item?.replyRate || 0,
        'Open Action': item?.openRate || 0,
        'Click Action': item?.clickRate || 0,
      }));
      setReportData({ data: [...chartData], total: {...rate} });
    }
    setLoading(false);
  };

  const handleExportChart = async () => {
    setExportLoading(true);
    toPng(currentChart.current, { cacheBust: true })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `${seqId || sequenceData?.id}.png`;
        link.href = dataUrl;
        link.click();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setExportLoading(false));
  };

  useEffect(() => {
    if (!(seqId || sequenceData?.id)) return;
    getSequenceReportData(seqId || sequenceData?.id);
  }, [sequenceData, seqId]);

  useLayoutEffect(() => {
    if (!document || !window) return;
    function updateSize() {
      const element = document?.querySelector('.recharts-wrapper');
      const width =
        element !== null
          ? element?.closest('.ant-card-body')?.clientWidth
          : document?.querySelector('.ant-card-body')?.clientWidth;
      setState({ responsive: 800 });
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <div className="py-5 bg-white rounded-md px-3 flex flex-col gap-7">
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div>Status: </div>
          <div
            className={`flex items-center gap-2 py-2 px-2 rounded-md font-semibold ${getStatusColor(sequenceData?.status)}`}
          >
            <FireOutlined />
            <div>{sequenceData?.status}</div>
          </div>
        </div>
        <Button
          loading={exportLoading}
          disabled={loading}
          onClick={handleExportChart}
          icon={<ShareAltOutlined />}
        >
          Export
        </Button>
      </div>
      {!loading && (
        <div id="sequence-report" ref={currentChart}>
          <div className="grid grid-cols-4 gap-5">
            {dataStats.map((item) => (
              <div className="flex flex-col justify-center items-center border rounded-md py-5 gap-4">
                <div className="flex items-center">
                  <div className="text-lg font-semibold text-[#686D76]">
                    {item?.title}
                  </div>
                  <Popconfirm
                    rootClassName="customize-tooltip-widget"
                    placement="right"
                    title={item?.title}
                    description={item?.description}
                  >
                    <Button
                      type="text"
                      icon={<InfoCircleOutlined className="text-[#848891]" />}
                    />
                  </Popconfirm>
                </div>
                <div className="text-6xl font-bold">
                  {reportData?.total[item?.key] || 0} {item.addonAfter}
                </div>
              </div>
            ))}
          </div>
          <div className="py-5 px-3 ant-card-body ">
            <LineChart
              id="currentChart"
              // ref={(chart) => (currentChart.current = chart)}
              width={1650}
              height={400}
              data={reportData.data}
              margin={{
                top: 5,
                right: window.innerWidth <= 375 ? 34 : 40,
                left: window.innerWidth <= 375 ? -16 : 0,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="Bounce Action"
                stroke="#8884d8"
                activeDot={{ r: 8 }}
              />
              <Line type="monotone" dataKey="Open Action" stroke="#afca9d" />
              <Line type="monotone" dataKey="Click Action" stroke="#34ca9d" />
              <Line type="monotone" dataKey="Reply Action" stroke="#75ca9d" />
            </LineChart>
          </div>
        </div>
      )}
      {loading && (
        <div className="w-full h-10 flex justify-center items-center">
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default ReportSequenceChart;
