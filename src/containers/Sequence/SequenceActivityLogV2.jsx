import { useEffect, useState } from 'react';
import { getActivityLog, getActivityLogsStats } from '../../services/search';
import { notification, Select, Table, Tag, Tooltip } from 'antd';
import { transFormDateFormat } from '../../utils/date';
import { v4 as uuid } from 'uuid';
import {
  MailOutlined,
  EyeOutlined,
  LinkOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  PauseCircleOutlined,
  EditOutlined,
  ClockCircleOutlined,
  UserAddOutlined,
  FileSyncOutlined,
  AppstoreOutlined,
  HeartOutlined,
  BranchesOutlined,
} from '@ant-design/icons';
import Search from 'antd/es/input/Search';
import {
  ADD_STEP_TYPE,
  ADD_STEP_TYPE_NAME,
  ADD_STEP_TYPE_ICON,
} from '../../components/BullHorn/EmailtriggerStep';
import { arrayUniqueByKey } from '../../utils/common';
import clsx from 'clsx';
import dayjs from 'dayjs';

const initialPagination = {
  current: 1,
  pageSize: 10,
  total: 0,
};

const mappingTypes = {
  LINKEDIN_CONNECTION_REQUEST: 'LinkedIn Connection Request',
  EMAIL: 'Email',
  NOTE: 'Note',
  TASK: 'Task',
};

const SequenceActivityType = {
  // DEFERRED: 'deferred',
  BOUNCE: 'bounce',
  DROPPED: 'dropped',
  // SPAM: 'spamreport',
  DELIVERED: 'delivered',
  SENT: 'sent',
  UPDATED: 'updated',
  CREATED: 'created',
  REPLIED: 'replied',
  STOPPED: 'stopped',
  LIVED: 'live',
  BLOCKED: 'blocked',
  STOPPED_BY_USER: 'off',
  REPLIED_BY_HOTLIST: 'replied_by_hotlist',
  OPENED: 'opened',
  LINK_CLICKED: 'link_clicked',
  FAILED: 'failed',
  SKIPPED: 'skipped',
  AUTO_REPLIED: 'auto_replied',
  LINKEDIN_INVITATION_ACCEPTED: 'linkedin_invitation_accepted',
  MESSAGE: 'message',
};

const SequenceActivityTypeIcon = {
  DEFERRED: <ClockCircleOutlined />,
  BOUNCE: <CloseCircleOutlined />,
  DROPPED: <CloseCircleOutlined />,
  SPAM: <WarningOutlined />,
  DELIVERED: <MailOutlined />,
  SENT: <MailOutlined />,
  UPDATED: <EditOutlined />,
  CREATED: <HeartOutlined />,
  REPLIED: <FileSyncOutlined />,
  STOPPED: <PauseCircleOutlined />,
  LIVED: <EditOutlined />,
  BLOCKED: <CloseCircleOutlined />,
  STOPPED_BY_USER: <PauseCircleOutlined />,
  REPLIED_BY_HOTLIST: <FileSyncOutlined />,
  OPENED: <EyeOutlined />,
  LINK_CLICKED: <LinkOutlined />,
  FAILED: <CloseCircleOutlined />,
  SKIPPED: <PauseCircleOutlined />,
  AUTO_REPLIED: <FileSyncOutlined />,
  LINKEDIN_INVITATION_ACCEPTED: <UserAddOutlined />,
  MESSAGE: <EditOutlined />,
};

const eventDescription = {
  deferred: {
    message: 'Email step $number was deferred',
    name: 'DEFERRED',
    color: 'volcano',
  },
  bounce: {
    message: 'Email step $number was bounced by <b>$email</b></div>',
    name: 'BOUNCE',
    color: 'volcano',
  },
  dropped: {
    message: 'Email step $number was dropped',
    name: 'DROPPED',
    color: 'error',
  },
  spamreport: {
    message: 'Email step $number was reported as spam',
    name: 'SPAM',
    color: 'error',
  },
  delivered: {
    message: 'Email step $number was delivered',
    name: 'DELIVERED',
    color: 'green',
  },
  sent: {
    message: '$type step $number was sent',
    name: 'SENT',
    color: 'green',
  },
  updated: {
    message: 'Sequence was updated',
    name: 'UPDATED',
    color: 'geekblue',
  },
  created: {
    message: 'Sequence was created',
    name: 'CREATED',
    color: 'geekblue',
  },
  replied: {
    // message: '<div>Mail number $number was replied by <b>$email</b> with message $message</div>',
    message: '<div>Email step $number was replied by <b>$email</b></div>',
    name: 'REPLIED',
    color: 'green',
  },
  replied_by_hotlist: {
    message:
      'Email step $number was replied by email $email coming from hot list',
    name: 'REPLIED_BY_HOTLIST',
    color: 'green',
  },
  stopped: {
    message: 'Sequence was stopped by <b>$recipient</b> replied',
    name: 'STOPPED',
    color: 'error',
  },
  lived: {
    message: 'Sequence was marked as LIVE',
    name: 'LIVE',
    color: 'green',
  },
  blocked: {
    message: 'Email step $number was blocked',
    name: 'BLOCKED',
    color: 'error',
  },
  off: {
    message: 'Sequence was stopped by user',
    name: 'STOPPED BY USER',
    color: 'error',
  },
  opened: {
    message: 'Email step $number was opened by <b>$email</b>',
    name: 'OPENED',
    color: 'blue',
  },
  failed: {
    message:
      '$type step $number was sent $recipients but failed. Error: <i>$error</i>',
    name: 'FAILED',
    color: 'error',
  },
  skipped: {
    message:
      '$type step $number is not sent to contact <b>$email</b> because it will be <i>$skipType</i>',
    name: 'NOT SENT',
    color: 'volcano',
  },
  auto_replied: {
    message:
      '<div>$type Email step $number was auto replied by <b>$recipient</b></div>',
    name: 'AUTO REPLIED',
    color: 'blue',
  },
  linkedin_invitation_accepted: {
    message: '<div>LinkedIn Invitation was accepted by <b>$recipient</b></div>',
    name: 'INVITATION ACCEPTED',
    color: 'blue',
  },
  link_clicked: {
    message:
      '<div style="margin-left: 55px"> <b><a href=$url_click target="_blank">Link </a><span style="color: #1677ff">( $url_click_title )</span></b> clicked by <b>$email</b></div>',
    name: 'LINK CLICKED',
    color: 'blue',
  },
};

const handleMappingData = async (newData = []) => {
  try {
    const dataTable = newData?.map((item, index) => {
      const recipients = item?.content?.recipients
        ?.map(
          (rep) =>
            (item.content?.type === 'LINKEDIN_CONNECTION_REQUEST'
              ? rep?.name || rep?.email
              : rep?.email) || rep
        )
        .join(', ');

      const mappingVars = {
        $number: (item?.sequenceStep?.stepIndex ?? 0) + 1,
        $name:
          item?.sequenceStep?.name ||
          `Step ${(item?.sequenceStep?.stepIndex ?? 0) + 1}`,
        $recipients: recipients ? `to <b>${recipients}</b>` : '',
        $recipient: item?.content?.recipient,
        $message: item.content?.email?.content,
        $type: mappingTypes[item.content?.type || item?.sequenceStep?.type],
        $email: item?.content?.email || item?.content?.emails?.join(', '),
        $skipType:
          item?.content?.reason === 'bounce'
            ? 'bounced'
            : item?.content?.reason,
        $error:
          typeof item.content?.reason === 'string'
            ? item.content?.reason
            : item.content?.reason?.providerError?.error?.message ||
              item.content?.reason?.message ||
              JSON.stringify(item.content?.reason),
      };
      if (
        item.content?.type === 'LINKEDIN_CONNECTION_REQUEST' &&
        item.content?.recipients?.length
      ) {
        mappingVars['$email'] = item.content?.recipients
          ?.map((rep) => rep?.name || rep?.email || rep)
          .join(', ');
      }

      if (
        item.type === 'replied' ||
        item.type === 'replied_by_hotlist' ||
        item.type === 'bounce'
      ) {
        mappingVars['$email'] = item.content?.recipient;
        mappingVars['$message'] = item.content?.email?.content;
      }

      if (item.type === 'opened') {
        mappingVars['$email'] = item.content?.email?.content;
      }

      if (item.type === 'link_clicked') {
        mappingVars['$email'] = item.content?.email?.content;
        mappingVars['$url_click'] =
          item.content?.link_onclick || item.content.urlClick;
        mappingVars['$url_click_title'] =
          item.content?.link_onclick || item.content.urlClick;
      }

      let linkedInDescriptions = null;

      let description = Object.keys(mappingVars).reduce((desc, key) => {
        return desc.replace(key, mappingVars[key] ?? '');
      }, eventDescription?.[item.type]?.message || '');

      if (item.type === 'sent' && item.content?.reason) {
        description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
        description = `${description} but failed. Error: <i>${item.content?.reason}</i>`;
      }

      if (
        item.type === 'sent' &&
        !item.content.linkedInType &&
        item.content?.recipients?.length
      ) {
        description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
        description = `${description} to <b>${item.content?.recipients?.join(', ')}</b>`;
      }

      if (
        ['message', 'sent'].includes(item.type) &&
        item.content.linkedInType &&
        item.content?.recipients?.length
      ) {
        description = `${item.content.linkedInType} ${description}`;
        linkedInDescriptions = `${mappingLinkedInTypes[item.content.linkedInType] || item.content.linkedInType} sent`;
        description = `${description} to  <b>${item.content?.recipients?.join(', ')}</b>`;
        linkedInDescriptions = `${linkedInDescriptions} to <b>${item.content?.recipients?.join(', ')}</b>`;
      }

      if (item.type === 'failed' && item.content.linkedInType) {
        description = `${item.content.linkedInType ? `${item.content.linkedInType} ` : ''}${description}`;
      }

      if (item.type === 'stopped' && item.content?.reason) {
        description = `Sequence is stopped because <i>${item.content?.reason}</i>`;
      }

      if (item.content?.type === 'TASK' && item.type === 'created') {
        const displayVars = {
          '{{recipient}}':
            mappingVars['$recipient']?.email || mappingVars['$recipient'],
          ...mappingVars,
        };
        description = Object.keys(displayVars).reduce((desc, key) => {
          return desc.replace(key, displayVars[key]);
        }, '$type step $number was created for <b>{{recipient}}</b>');
      }

      if (item.content?.displayText) {
        const displayVars = {
          '{{step_number}}': mappingVars['$number'],
          '{{contact_name}}': mappingVars['$recipients'],
        };
        description = Object.keys(displayVars).reduce((desc, key) => {
          return desc.replace(key, displayVars[key]);
        }, item.content.displayText);
        linkedInDescriptions = description;
      }

      return {
        id: item?.id,
        description,
        action: item?.type,
        date: item.occurredAt,
        recipient: item.recipient,
        content: item?.content,
        groupedByStep:
          description.includes(`${mappingVars['$type']}`) ||
          item.type === 'linkedin_invitation_accepted' ||
          item.type === 'link_clicked' ||
          item.content?.displayText ||
          item.content?.type
            ? `[${mappingVars['$type']}] ${mappingVars['$name'] || 'UNKNOWN'}`.toUpperCase()
            : '',
        grouptedByType:
          eventDescription[item?.type]?.name?.toUpperCase() ||
          item?.type?.toUpperCase() ||
          'UNKNOWN',
        linkedInDescriptions,
      };
    });

    const finalData = [...dataTable];
    return [...finalData];
  } catch (error) {
    console.log('error', error);
    return [];
  }
};

const SequenceActivityLogV2 = ({ sequenceData, seqId, inputNumberStep }) => {
  const [dataSource, setDataSource] = useState([]);
  const [actionTypeStats, setActionTypeStats] = useState(null);
  const [dataWithText, setDataWithText] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState(initialPagination);
  const [stepOptions, setStepOPtions] = useState([]);
  const [filters, setFilters] = useState({
    stepId: null,
    search: '',
    actionType: null,
    stepType: null,
  });
  const [optionLoading, setOptionLoading] = useState(true);

  const columns = [
    {
      title: '',
      dataIndex: 'id',
      key: 'id',
      width: '100%',
      render: (id, record) => {
        const itemWithText = dataWithText?.find((item) => item?.id === id);
        const recipients = record?.content?.recipients || [];
        const stepContentType =
          record?.content?.type || record?.sequenceStep?.type || ''; // EMAIL, LINKEDIN,...
        const actionType = record?.type || ''; // DEFERRED, BOUNCE, DROPPED,...
        const subject = itemWithText?.description || '';
        const stepName = record?.sequenceStep?.name || '';
        const updatedAt = record?.updatedAt || '';
        return (
          <div className="w-full grid grid-cols-10 gap-3">
            <div className="w-full flex items-center justify-start text-base font-medium">
              {ADD_STEP_TYPE_NAME[stepContentType] ? (
                <Tag
                  icon={ADD_STEP_TYPE_ICON[stepContentType]}
                  color={'cyan'}
                  key={uuid()}
                >
                  {ADD_STEP_TYPE_NAME[stepContentType]}
                </Tag>
              ) : (
                <Tag icon={<BranchesOutlined />} color={'cyan'} key={uuid()}>
                  Sequence
                </Tag>
              )}
            </div>
            <div class="flex items-center gap-3 col-span-2">
              <p
                class="text-[#475569] font-medium flex gap-1 text-sm items-start justify-center"
                title={recipients?.[0]?.email || recipients?.[0]?.toString()}
              >
                To:{' '}
                <a
                  href={
                    recipients?.length > 0
                      ? `mailTo:${recipients?.[0]?.email || recipients?.[0]?.toString()}`
                      : '#'
                  }
                  class="text-cyan-600 hover:underline line-clamp-1 max-w-[13rem]"
                >
                  {recipients?.length > 0 ? (
                    recipients?.[0]?.email || recipients?.[0]?.toString()
                  ) : (
                    <span className="text-gray-900 italic">
                      All participants
                    </span>
                  )}
                </a>
              </p>
            </div>
            <div className="col-span-3 flex items-center justify-start">
              <p
                class="text-sm text-[#111827] line-clamp-1 max-w-full"
                dangerouslySetInnerHTML={{
                  __html: subject,
                }}
              ></p>
            </div>
            <div class="flex flex-col items-end justify-center gap-1 text-[#475569] text-sm col-span-3">
              <p class="text-cyan-600 text-sm">
                {/* <a href="#" class="hover:underline">
                  Eugen Esanu
                </a>
                <span class="mx-2 text-[#94a3b8]">·</span> */}
                <span class="text-[#94a3b8]">{stepName} </span>
                <span class="mx-2 text-[#94a3b8]">·</span>
                <span class="text-[#94a3b8]">
                  {dayjs(updatedAt).format('HH:MM:ss a - DD MMM YYYY')}
                </span>
              </p>
            </div>
            <div className="w-full flex items-center justify-end text-base font-medium">
              <Tag color={eventDescription?.[actionType]?.color} key={uuid()}>
                {eventDescription?.[actionType]?.name}
              </Tag>
            </div>
          </div>
        );
      },
    },
  ];

  const fetchData = async (paginationTemp, filtersTemp = {}) => {
    setLoading(true);
    try {
      const page = paginationTemp
        ? paginationTemp?.current
        : pagination?.current;
      const limit = paginationTemp
        ? paginationTemp?.pageSize
        : pagination?.pageSize;

      const officialFilters = {
        ...filters,
        ...filtersTemp,
      };
      const { data } = await getActivityLog(seqId || sequenceData.id, {
        page,
        limit,
        ...officialFilters,
      });
      const newData = data?.result?.items || [];
      setDataSource([...newData]);

      // Transform data to add description
      const transformedData = await handleMappingData(newData);
      setDataWithText([...transformedData]);

      const total = data?.result?.total || 0;
      setPagination({
        pageSize: limit,
        current: page,
        total,
      });
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  const getSequenceSteps = async () => {
    setOptionLoading(true);
    try {
      const sequenceStepsTemp = inputNumberStep?.filter((item) => !item?.delay);
      const uniqueSteps = arrayUniqueByKey(sequenceStepsTemp, 'id');
      const stepOptions = uniqueSteps?.map((step) => ({
        label: step?.name,
        value: step?.id,
      }));
      setStepOPtions([...stepOptions]);
      setOptionLoading(false);
    } catch (error) {
      setOptionLoading(false);
      console.log('error', error);
    }
  };

  const getActionTypeCounts = async () => {
    try {
      const { data } = await getActivityLogsStats(seqId || sequenceData.id);
      const actionCountsTemp = data?.result;
      const total = Object.entries(actionCountsTemp).reduce(
        (acc, [key, value]) => {
          return acc + value;
        },
        0
      );
      const actionCounts = { ...actionCountsTemp, total };
      setActionTypeStats({ ...actionCounts });
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    fetchData(pagination, filters);
  }, [filters]);

  useEffect(() => {
    getSequenceSteps();
    getActionTypeCounts();
  }, []);

  return (
    <div className="bg-[#f9fafc] font-sans text-sm text-[#475569] flex justify-center py-6 px-4">
      <div className="w-full flex gap-6 grid grid-cols-10">
        <aside className="flex flex-col text-sm text-[#475569] col-span-2 select-none">
          <div className="mb-4">
            <p className="font-semibold mb-2">View by Action</p>
            <ul className="space-y-2">
              <li
                className={clsx(
                  'flex items-center gap-3   hover:scale-105 transition-all duration-200 ease-in-out',
                  !filters?.actionType ? 'text-cyan-600' : 'text-[#94a3b8]',
                  loading ? 'cursor-not-allowed' : 'cursor-pointer'
                )}
                onClick={() => {
                  if (loading) return;
                  if (filters?.actionType) {
                    setPagination({ ...pagination, current: 1 });
                    setFilters({ ...filters, actionType: null });
                  }
                }}
              >
                <AppstoreOutlined />
                <span className="font-semibold">All</span>
                <span className="ml-auto text-sm ">
                  {actionTypeStats?.['total'] || 0}
                </span>
              </li>
              {Object.entries(SequenceActivityType).map(
                ([key, value], index) => (
                  <li
                    className={clsx(
                      'flex items-center gap-3 hover:scale-105 transition-all duration-200 ease-in-out',
                      filters?.actionType === value
                        ? 'text-cyan-600'
                        : 'text-[#94a3b8]',
                      loading ? 'cursor-not-allowed' : 'cursor-pointer'
                    )}
                    onClick={() => {
                      if (loading) return;
                      if (filters?.actionType !== value) {
                        setPagination({ ...pagination, current: 1 });
                        setFilters({ ...filters, actionType: value });
                      }
                    }}
                  >
                    {SequenceActivityTypeIcon[key]}
                    <span className="font-semibold">
                      {value.replaceAll('_', ' ')?.capitalize()}
                    </span>
                    <span className="ml-auto text-sm font-medium">
                      {actionTypeStats?.[value] || 0}
                    </span>
                  </li>
                )
              )}
            </ul>
          </div>
        </aside>
        <main className="flex-1 col-span-8">
          <div className="grid grid-cols-6 items-center gap-3 mb-4">
            <Select
              disabled={loading}
              loading={optionLoading}
              options={stepOptions}
              placeholder="Select a step"
              className="col-span-1text-sm text-[#475569] focus:outline-none focus:ring-1 focus:ring-cyan-600 focus:border-cyan-600"
              aria-label="Select filter"
              allowClear
              onChange={(value) => {
                if (filters?.stepId !== value) {
                  setPagination({ ...pagination, current: 1 });
                  setFilters({ ...filters, stepId: value });
                }
              }}
            />
            <Select
              disabled={loading}
              options={Object.entries(ADD_STEP_TYPE_NAME)
                .map(([key, value]) => ({
                  label: value,
                  value: key,
                }))
                .filter((item) => item.value !== 'ADD_WAIT')}
              placeholder="Select step type"
              className="col-span-1 text-sm text-[#475569] focus:outline-none focus:ring-1 focus:ring-cyan-600 focus:border-cyan-600"
              aria-label="Select filter"
              allowClear
              onChange={(value) => {
                if (filters?.stepType !== value) {
                  setPagination({ ...pagination, current: 1 });
                  setFilters({ ...filters, stepType: value });
                }
              }}
            />
            <div className="relative flex-1 col-span-4">
              <Search
                className="w-full "
                placeholder="Search by email address, name, or content"
                disabled={loading}
                allowClear
                onSearch={(value) => {
                  if (filters?.search !== value) {
                    setPagination({ ...pagination, current: 1 });
                    setFilters({ ...filters, search: value });
                  }
                }}
              />
            </div>
          </div>
          <div className="task-table-new-design-container">
            <Table
              showHeader={false}
              className="customized-style-pagination w-full"
              dataSource={dataSource}
              columns={columns}
              loading={loading}
              rowKey={(record) => record?.id}
              pagination={{
                pageSize: pagination?.pageSize,
                current: pagination?.current,
                total: pagination?.total,
                onChange: (current, pageSize) => {
                  const newPagination = {
                    ...pagination,
                    current,
                    pageSize,
                  };
                  setPagination({ ...newPagination });
                  fetchData(newPagination);
                },
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
                showSizeChanger: true,
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default SequenceActivityLogV2;
