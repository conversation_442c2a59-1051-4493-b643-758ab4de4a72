import { But<PERSON>, Spin, Table, Tag } from 'antd';
import ValidatingContactLoading from '../../components/ValidatingContactLoading';
import { useEffect, useState } from 'react';
import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteColumnOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  MailOutlined,
  UserOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

const StyledTable = styled(Table)`
  .ant-table-cell {
    padding: 0;
  }
`;

export const VALIDATE_TYPES = {
  CONTACT_LIST: 'CONTACT_LIST',
  HOT_LIST: 'HOT_LIST',
  SELECT_ALL: 'SELECT_ALL',
};

export const VALIDATE_TYPES_TEXT = {
  CONTACT_LIST: 'Contact List',
  HOT_LIST: 'Hot List',
  SELECT_ALL: 'Contacts',
};

export const CLOSE_TYPES = {
  CLOSE_POPUP: 'CLOSE_POPUP', // Close the modal only
  CLOSE_AND_SAVE: 'CLOSE_AND_SAVE', // Close the modal and save the data
};

const ValidatingEmailProcess = ({
  emails = [],
  rawList,
  loading = true,
  closeModal,
  type = VALIDATE_TYPES.CONTACT_LIST,
  skippedEmails = [],
  remove,
  revert,
}) => {
  const [counter, setCounter] = useState(0);
  const [showInvalidContacts, setShowInvalidContacts] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [isExand, setExpand] = useState(false);

  const toggleExpand = () => setExpand(!isExand);
  const invalidEmails =
    emails?.filter((email) =>
      type === VALIDATE_TYPES.SELECT_ALL
        ? email?.emailStatus === 'Invalid' || !email?.emailStatus
        : email?.status === 'Invalid'
    ) || [];
  const validEmails =
    emails?.filter((email) =>
      type === VALIDATE_TYPES.SELECT_ALL
        ? email?.emailStatus === 'Valid'
        : email?.status === 'Valid'
    ) || [];

  const getTotal = () => {
    switch (type) {
      case VALIDATE_TYPES.CONTACT_LIST:
        return emails?.length;
      case VALIDATE_TYPES.HOT_LIST:
        const total = rawList?.reduce(
          (acc, curr) =>
            +curr?.candidateCount > 0
              ? acc + parseInt(curr?.candidates?.total)
              : acc + parseInt(curr?.clientContacts?.total),
          0
        );
        return total;
      case VALIDATE_TYPES.SELECT_ALL:
        return emails?.length;
      default:
        return rawList?.length;
    }
  };

  useEffect(() => {
    //Implementing the setInterval method
    const limit = getTotal();
    const interval = setInterval(() => {
      if (parseInt(counter) === parseInt(limit - 1)) {
        clearInterval(interval);
        return;
      }
      setCounter(counter + 1);
    }, 500);

    if (!loading) {
      setCounter(limit);
      clearInterval(interval);
    }

    //Clearing the interval
    return () => {
      clearInterval(interval);
    };
  }, [counter, loading]);

  useEffect(() => {
    if ((loading && emails?.length === 0) || !emails) return;
    const ids = emails?.map((email) => email?.id);
    setSelectedRowKeys(ids);
    return () => {
      setSelectedRowKeys([]);
      setCounter(0);
    };
  }, [loading]);

  const columns = [
    {
      title: '',
      dataIndex: 'name',
      key: 'name',
      width: '75%',
      render: (name, item) => (
        <div
          className="col-span-7 line-clamp-1 py-2 border-b flex flex-col items-start justify-start gap-1 text-xs"
          title={name}
        >
          <div className="flex items-center gap-1">
            <UserOutlined />
            {name || '-'}
          </div>
          <div
            className="line-clamp-1 opacity-60 italic flex items-center gap-1 max-w-[10rem]"
            title={item?.email || 'Not found'}
          >
            <MailOutlined />
            {item?.email || 'Not found'}
          </div>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (email, item) => {
        return (
          <div>
            {(!item?.email?.trim() ||
              (item?.email && skippedEmails?.includes(item?.email))) && (
              <Tag color="error" icon={<CheckOutlined />}>
                Removed
              </Tag>
            )}
          </div>
        );
      },
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <div className="flex flex-col items-center justify-center h-full gap-3 email-validator-container py-1">
      <div className="flex text-xs items-center justify-start w-full h-full gap-1">
        {!loading && getTotal() === 10 ? (
          <div className="font-medium">We're preparing..</div>
        ) : (
          <div className="font-medium">
            {loading ? "We're validating" : 'Validation completed'}{' '}
            <Tag color="cyan" className="font-medium mx-1">
              {counter}
              {getTotal() === 0 && loading ? (
                <Spin className="pl-2" size="small" />
              ) : (
                ` / ${getTotal()}`
              )}
            </Tag>
            <span className="pl-1">
              emails from {VALIDATE_TYPES_TEXT[type]}
            </span>
          </div>
        )}
        {!loading && (
          <span>
            <span
              className="text-cyan-500 font-semibold cursor-pointer hover:underline"
              onClick={toggleExpand}
            >
              {!isExand ? `View` : `Hide`}
            </span>
          </span>
        )}
      </div>
      {!loading && isExand && (
        <div className="w-full flex flex-col justify-center items-center border p-3 rounded-md shadow-sm text-xs">
          <div className="w-full flex flex-col gap-2 max-w-xs">
            <div className="w-full grid grid-cols-10 gap-2 font-medium items-center">
              <div className="col-span-7">
                Total <Tag color="success">Valid</Tag> Contacts:{' '}
              </div>
              <div className="col-span-3">
                <Tag color="success" className="font-semibold">
                  {validEmails?.length}
                </Tag>
              </div>
              <div className="col-span-7">
                Total <Tag color="error">Invalid</Tag> Contacts:{' '}
              </div>
              <div className="col-span-3 flex items-center gap-1">
                <Tag color="error" className="font-semibold">
                  {invalidEmails?.length}
                </Tag>
                <Button
                  onClick={() => setShowInvalidContacts(!showInvalidContacts)}
                  type="text"
                  icon={
                    showInvalidContacts ? (
                      <EyeInvisibleOutlined />
                    ) : (
                      <EyeOutlined />
                    )
                  }
                ></Button>
              </div>
              {showInvalidContacts && (
                <div className="col-span-10 search-table-new-design-container max-h-60 overflow-y-auto w-full pr-2">
                  <StyledTable
                    showHeader={false}
                    size="small"
                    rowSelection={rowSelection}
                    rowKey={(record) => record?.id}
                    dataSource={invalidEmails}
                    pagination={false}
                    columns={columns}
                    className="custom-table"
                    onRow={(record, rowIndex) => {
                      return {
                        onClick: () => {
                          if (selectedRowKeys?.includes(record?.id)) {
                            revert(record);
                            if (!record?.email?.trim()) return;
                            const newSelectedRowKeys = selectedRowKeys?.filter(
                              (listId) => listId !== record?.id
                            );
                            onSelectChange([...newSelectedRowKeys]);
                          } else {
                            remove(record);
                            if (!record?.email?.trim()) return;
                            onSelectChange([...selectedRowKeys, record?.id]);
                          }
                        },
                        style: { cursor: 'pointer' },
                      };
                    }}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="flex justify-end gap-3 w-full pt-2">
            <Button
              size="small"
              // type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => {
                const unticked = rawList?.filter(
                  (item) =>
                    !selectedRowKeys?.includes(item?.id) &&
                    (item?.emailStatus === 'Invalid' || !item?.emailStatus)
                );
                unticked?.forEach((item) => {
                  revert(item);
                });
                closeModal(CLOSE_TYPES.CLOSE_AND_SAVE);
              }}
              className="flex items-center justify-center"
            >
              <span className="text-xs font-medium">Save result</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ValidatingEmailProcess;
