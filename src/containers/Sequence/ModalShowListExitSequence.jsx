import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Pagination,
  Radio,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  notification,
} from 'antd';
import _debounce from 'lodash/debounce';
import Email3Logo from '../../assets/img/files.png';
import Search from 'antd/es/input/Search';
import { getAllEmails, getListEmailFromSequence } from '../../services/search';
import { useForm } from 'react-hook-form';
import { addContactToMail } from '../../services/emailFinder';

function ModalShowListExitSequence(props) {
  const { open, setOpen, currentContact, onClose = false } = props;
  const { handleSubmit, control, getValues, setValue, watch } = useForm();

  const [tabDetail, setTabDetail] = useState(false);
  const [dataEmailsSequence, setDataEmailSequence] = useState([]);
  const [dataListSequence, setDataListEmailSequence] = useState([]);
  const [chooseItem, setChooseItem] = useState([]);
  const [tabState, setTabState] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [existingContacts, setExistingContacts] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [pending, setPending] = useState(false);

  const handleDateSequence = async (status = null, page = 1, limit = 10) => {
    setPending(true);
    const { data } = await getAllEmails(
      status,
      page,
      limit,
      getValues('searchText')
    );
    setDataEmailSequence(data?.result?.mails);
    setCountPage(data?.result?.count);
    setPending(false);
  };

  const handleGetListData = async () => {
    const { data } = await getListEmailFromSequence(chooseItem);
    const newValueArr = data?.result?.mails?.map((item, index) =>
      index === data?.result?.mails.length - 1
        ? { ...item, delay: index + 1 }
        : item
    );
    const newData = newValueArr?.slice(1).map((item, index) => {
      return {
        delay: item.delay,
        subject: item.subject,
        content: item.content,
        key: index + 1,
        status: item?.status,
      };
    });
    setInputNumberStep(newData);
    setDataListEmailSequence(data?.result?.mails);
    setEmailConfigData(data?.result?.mails);
    setValue(
      `sendMail.mailStepParentMailTo`,
      data?.result?.mails?.[0]?.recipients ?? []
    );
    setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
    setValue(
      `sendMail.mailStepParentContent`,
      data?.result?.mails?.[0]?.content
    );
    setValue(
      `sendMail.mailStepParentSubject`,
      data?.result?.mails?.[0]?.subject
    );
    setNumberStep(newData?.length);
  };

  useEffect(() => {
    if (!onClose) {
      handleDateSequence();
    }
  }, []);

  const handlePagination = (page) => {
    setPage(page);
    handleDateSequence(null, page, 10);
  };

  const handleOke = async () => {
    if (chooseItem?.length == 0) {
      notification.error({ message: 'Please choose a Sequence' });
      return;
    }
    const payload = {
      emailSeqIds: chooseItem,
      recipients: [
        {
          email: currentContact?.email,
          name: currentContact?.name,
          firstName: currentContact?.firstName,
        },
      ],
    };
    try {
      const { data } = await addContactToMail(payload);
      if (data) {
        notification.success({ message: 'Add to sequences success' });
        setOpen(false);
      }
    } catch (e) {
      notification.error({ message: 'Add to sequences Fail' });
    }
  };

  const handleChooseItem = (item) => {
    if (!chooseItem?.includes(item)) {
      setChooseItem([...chooseItem, item]);
    } else {
      setChooseItem(chooseItem.filter((it) => it !== item));
    }
  };

  useEffect(() => {
    if (chooseItem && tabState) {
      handleGetListData();
    }
  }, [chooseItem, tabState]);

  useEffect(() => {
    if (!open) {
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      setTabState(false);
      setChooseItem([]);
    }
  }, [open]);

  return (
    <>
      <Modal
        title="Select exited sequence"
        centered
        open={open}
        footer={
          !tabState ? (
            <>
              <div>
                <Button onClick={() => setOpen(false)}>Cancel</Button>
                <Button type="primary" onClick={() => handleOke()}>
                  Choose
                </Button>
              </div>
            </>
          ) : (
            false
          )
        }
        onOk={() => handleOke()}
        onCancel={() => setOpen(false)}
        width={700}
      >
        <div>
          <div style={{ marginTop: '20px' }}>
            <Search
              onSearch={() => handleDateSequence(null, page, 10)}
              placeholder="input search text"
              style={{ width: 200 }}
              onChange={(e) => setValue('searchText', e.target.value)}
            />
          </div>
          {pending ? (
            <>
              <Spin />
            </>
          ) : (
            <>
              <div style={{ marginTop: '30px' }}>
                {dataEmailsSequence?.map((item, index) => (
                  <div
                    onClick={() => handleChooseItem(item?.id)}
                    key={item?.id}
                    style={{ marginTop: '5px', cursor: 'pointer' }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        border: `1px solid ${chooseItem?.includes(item?.id) ? '#fff' : '#ccc'}`,
                        borderRadius: '5px',
                        padding: '30px 10px',
                        height: '50px',
                        alignItems: 'center',
                        background: `${chooseItem?.includes(item?.id) ? '#7dd3fc' : '#fff'}`,
                      }}
                    >
                      <div
                        style={{
                          marginLeft: '20px',
                          width: '70%',
                          borderRight: '0.5px solid #ccc',
                        }}
                      >
                        <div
                          style={{
                            fontWeight: '600',
                            cursor: 'pointer',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: '250px',
                          }}
                        >
                          {item?.name}
                        </div>
                        <div>
                          <span style={{ color: 'blue', fontSize: '12px' }}>
                            {item?.sender}
                          </span>
                          <span
                            style={{ marginLeft: '20px', fontSize: '12px' }}
                          >
                            {item?.stepCount} step
                          </span>
                        </div>
                      </div>
                      <div
                        style={{
                          display: 'flex',
                          marginLeft: '10px',
                          paddingRight: '10px',
                          borderRight: '0.5px solid #ccc',
                          width: '40%',
                        }}
                      >
                        <div style={{ textAlign: 'center', width: '25%' }}>
                          <div>{item?.pendingCount}</div>
                          <div style={{ color: '#ccc', fontSize: '10px' }}>
                            Pending
                          </div>
                        </div>
                        <div style={{ textAlign: 'center', width: '25%' }}>
                          <div>{item?.sentCount}</div>
                          <div style={{ color: '#ccc', fontSize: '10px' }}>
                            Sent
                          </div>
                        </div>
                        <div style={{ textAlign: 'center', width: '25%' }}>
                          <div>{item?.stopCount}</div>
                          <div style={{ color: '#ccc', fontSize: '10px' }}>
                            Stop
                          </div>
                        </div>
                        <div style={{ textAlign: 'center', width: '25%' }}>
                          <div>{item?.repliedCount}</div>
                          <div style={{ color: '#ccc', fontSize: '10px' }}>
                            Replied
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div style={{ marginTop: '30px' }}>
                <Pagination
                  defaultCurrent={page}
                  defaultPageSize={10}
                  total={countPage}
                  showSizeChanger={false}
                  onChange={handlePagination}
                />
              </div>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}

export default ModalShowListExitSequence;
