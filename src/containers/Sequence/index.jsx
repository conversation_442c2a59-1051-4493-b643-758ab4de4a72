import React, { useEffect, useState } from 'react';

import { useSearchParams } from 'react-router-dom';
import { Tabs } from 'antd';

import SequencesTab from './SequenceTab';
import SequenceTemplate from '../SequenceTemplate';
import UserListTab from './UserListTab';
import SequenceHotListTab from '../SequenceHotListTab';
import { getUserViewAsLicenseType } from '../../helpers/getUserViewAs';
import { licenseType } from '../../constants/common.constant';

export const SEQUENCE_TAB_KEY = 'sequence-tab';
export const TEMPLATE_TAB_KEY = 'template-tab';
export const USER_LIST_TAB_KEY = 'user-list-tab';
export const HOT_LIST_TAB_KEY = 'hot-list-tab';

const Sequences = () => {
  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const [tabDetail, setTabDetail] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const activeKey = searchParams.get('activeKey');

  const items = [
    {
      key: SEQUENCE_TAB_KEY,
      label: 'Sequence',
      children: (
        <SequencesTab tabDetail={tabDetail} setTabDetail={setTabDetail} />
      ),
    },
    {
      key: TEMPLATE_TAB_KEY,
      label: 'Template',
      children: <SequenceTemplate />,
    },
    {
      key: USER_LIST_TAB_KEY,
      label: 'Contact List',
      children: <UserListTab />,
    },
    ...(isStandardUser
      ? []
      : [
          {
            key: HOT_LIST_TAB_KEY,
            label: 'CRM List',
            children: <SequenceHotListTab />,
          },
        ]),
  ];

  useEffect(() => {
    if (!document) return;
    const tabsEl = document.getElementsByClassName('ant-tabs-nav')[0];
    if (tabDetail) {
      searchParams.delete('activeKey');
      setSearchParams(searchParams);
      tabsEl.classList.add('!hidden');
    } else {
      tabsEl.classList.remove('!hidden');
    }
  }, [tabDetail]);

  useEffect(() => {
    if (activeKey === SEQUENCE_TAB_KEY) {
      setTabDetail(false);
    }
    if (isStandardUser && activeKey === HOT_LIST_TAB_KEY) {
      searchParams.set('activeKey', SEQUENCE_TAB_KEY);
      setSearchParams(searchParams);
    }
  }, [activeKey]);

  return (
    <div>
      <Tabs
        className="cusomized-tabs "
        activeKey={activeKey || SEQUENCE_TAB_KEY}
        items={items}
        destroyInactiveTabPane={true}
        onChange={(activeKey) => {
          searchParams.set('activeKey', activeKey);
          setSearchParams(searchParams);
        }}
      />
    </div>
  );
};

export default Sequences;
