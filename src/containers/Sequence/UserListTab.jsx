import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  Avatar,
  Button,
  Divider,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Spin,
  Tooltip,
  message,
  notification,
  Image,
  Table,
  Segmented,
} from 'antd';
import _debounce from 'lodash/debounce';
import SequenceItem from './SequenceItem';
import ModalCreateListSequence from './ModalCreateListSequence';
import { getAllEmails } from '../../services/search';
import SequenceDetail from './SequenceDetail';
import {
  createNewContactList,
  deleteBulkList,
  deleteContactList,
  getAllContact,
  getAllContactLists,
  getNewContactList,
} from '../../services/contactList';
import FormCreateNewList from './FormCreateNewList';
import {
  AntDesignOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  EllipsisOutlined,
  EyeOutlined,
  FireOutlined,
  GroupOutlined,
  MailOutlined,
  PhoneOutlined,
  QuestionCircleOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import DetailContactList from './DetailContactList';
import DetailEditContactList from './EditDetailContactList';
import { LocationIcon } from '@nylas/react';
import handleRenderTime from '../../function/handleRenderTime';
import moment from 'moment';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { COMMON_STRINGS } from '../../constants/common.constant';
import { useNavigate } from 'react-router-dom';

const QUERY_CONTACTLIST_TYPES = {
  MY_CONTACTLIST: 'MY_CONTACTLIST',
  COMPANY_CONTACTLIST: 'COMPANY_CONTACTLIST',
};

const initialPagination = {
  current: 1,
  pageSize: 10,
  // total: 0,
};

function UserListTab() {
  const navigate = useNavigate();
  const [dataListTab, setDataListTab] = useState([]);
  const [rawDataListTab, setRawDataListTab] = useState();
  const [pagination, setPagination] = useState(initialPagination);
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpenDetail, setIsModalOpenDetail] = useState(false);
  const [isModalOpenEditDetail, setIsModalOpenEditDetail] = useState(false);
  const [currentDetail, setCurrentDetail] = useState();
  const [selectedContactList, setSelectedContactList] = useState([]);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [loading, setLoading] = useState(true);

  const [type, setType] = useState(QUERY_CONTACTLIST_TYPES.MY_CONTACTLIST);

  const [rangeLabel, setRangeLabel] = useState('');

  const handleGetData = async (submitType) => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    setLoading(true);
    try {
      const queryType = submitType || type;
      const { data: dataListTemp } = await getAllContactLists(queryType);

      setDataListTab(dataListTemp?.result);
      setRawDataListTab(dataListTemp?.result);
      setLoading(false);
      messageApi.destroy();
    } catch (error) {
      setLoading(false);
      messageApi.destroy();
    }
  };

  useEffect(() => {
    handleGetData();
  }, []);

  const handleShowDetail = (item) => {
    setCurrentDetail(item);
    setIsModalOpenDetail(true);
  };

  const handleEditDetail = (item) => {
    setCurrentDetail(item);
    setIsModalOpenEditDetail(true);
  };

  const handleDeleteList = async (id) => {
    setIsModalOpenDetail(false);
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await deleteContactList(id);
      if (data) {
        handleGetData();
        messageApi.destroy();
        notification.success({
          message: 'Delete Contact List Success',
        });
      }
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleBulkDelete = async () => {
    setDeleteLoading(true);
    await deleteBulkList({ ids: selectedContactList })
      .then(() => {
        notification.success({
          message: 'Contact Lists deleted!',
        });
        const currDataListTab = [...rawDataListTab];
        const deletedDataListTab = currDataListTab.filter(
          (list) => !selectedContactList.includes(list?.id)
        );
        setDataListTab([...deletedDataListTab]);
        setRawDataListTab([...deletedDataListTab]);
        setSelectedContactList([]);
      })
      .catch((ex) => {
        notification.error({
          message: 'Something went wrong',
        });
      })
      .finally(() => setDeleteLoading(false));
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedContactList(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedContactList,
    onChange: onSelectChange,
  };

  const handleFilterRangeClick = (ev) => {
    const { key } = ev;
    console.log('ev: ', ev);
    if (key === 'all') {
      setDataListTab([...rawDataListTab]);
      return;
    }
    const filteringRange = key?.split('-');
    const min = parseInt(filteringRange[0]);
    const max = parseInt(filteringRange[1]);
    const currContactsList = [...rawDataListTab];
    const filterdContactsList = currContactsList.filter(
      (item) =>
        parseInt(item?.contacts?.length || 0) >= min &&
        parseInt(item?.contacts?.length || 0) < max
    );
    setDataListTab([...filterdContactsList]);
  };

  const handleSearch = (e) => {
    const searchText = e.target.value?.toLowerCase()?.trim();
    const currContactsList = [...rawDataListTab];
    const filterdContactsList = currContactsList.filter(
      (item) =>
        item?.name?.toLowerCase()?.includes(searchText) ||
        item?.contactListDescription?.toLowerCase()?.includes(searchText)
    );
    setDataListTab([...filterdContactsList]);
  };

  const columns = [
    {
      title: COMMON_STRINGS.NAME,
      key: 'name',
      dataIndex: 'name',
      // align: 'center',
      width: '30%',
      render: (text, record) => (
        <div className="flex gap-4">
          <div className="w-full">
            <div
              className="text-lg font-semibold line-clamp-1"
              title={record?.name}
            >
              {record?.name}
            </div>
            <div>
              <span
                className="text-xs font-medium text-[#b6b2b2] line-clamp-2"
                title={record?.contactListDescription}
              >
                {record?.contactListDescription}
              </span>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Count',
      key: 'contactCount',
      dataIndex: 'contactCount',
      align: 'center',
      render: (text, record) => {
        return (
          <Tooltip title={`${text} contact(s)`}>
            <div className="w-full flex items-center justify-center">
              <span className="h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700">
                {record?.contacts?.length || 0}
              </span>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Updated At',
      key: 'updatedAt',
      dataIndex: 'updatedAt',
      align: 'center',
      render: (text) => {
        return (
          <Tooltip title={`${handleRenderTime(text)}`}>
            <div className="w-full flex items-center justify-center font-medium">
              {handleRenderTime(text)}
            </div>
          </Tooltip>
        );
      },
    },
    // {
    //   title: 'LOCATION',
    //   key: 'location',
    //   dataIndex: 'location',
    //   align: 'center',
    //   render: (text) => {
    //     return (
    //       <Tooltip title={text}>
    //         <div className="w-full flex items-center justify-center">
    //           {text}
    //         </div>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      title: 'Contacts',
      key: 'contacts',
      icon: <GroupOutlined />,
      dataIndex: 'contacts',
      align: 'center',
      render: (contacts) => {
        const otherItem = {
          name:
            contacts?.length > 5
              ? `+ ${contacts?.length - 5} contact(s) more`
              : 0,
        };
        const handledContacts = [...contacts?.slice(0, 4), otherItem];
        return (
          <div>
            {contacts?.length > 0 ? (
              <Avatar.Group
                max={{
                  count: 5,
                  style: {
                    color: '#f56a00',
                    backgroundColor: '#fde3cf',
                  },
                }}
              >
                {contacts?.length > 5 &&
                  handledContacts?.map((user, index) => (
                    <Tooltip title={`${user?.name}`} placement="top">
                      <Avatar>{Array.from(user?.name || 'a')[0]}</Avatar>
                    </Tooltip>
                  ))}

                {contacts?.length <= 5 &&
                  contacts?.map((user, index) => (
                    <Tooltip title={`${user?.name}`} placement="top">
                      <Avatar>{Array.from(user?.name || 'a')[0]}</Avatar>
                    </Tooltip>
                  ))}
              </Avatar.Group>
            ) : (
              <div className="py-1 italic text-[#9299B8]">
                There are no user found on this list
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: ''.toUpperCase(),
      key: 'action',
      dataIndex: 'action',
      align: 'center',
      width: '5%',
      render: (text, item) => (
        <div onClick={(e) => e.stopPropagation()}>
          <Dropdown
            onClick={(e) => {
              e.stopPropagation();
            }}
            menu={{
              items: [
                {
                  key: 'view-contact-list',
                  label: (
                    <div
                      className="font-Montserrat flex gap-2 items-center justify-start"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/contact_list/${item.id}`);
                      }}
                    >
                      <EyeOutlined /> <span>View Contact List</span>
                    </div>
                  ),
                },
                {
                  key: 'edit-contact-list',
                  label: (
                    <div
                      className="font-Montserrat flex gap-2 items-center justify-start"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditDetail(item);
                      }}
                    >
                      <EditOutlined /> <span>Edit Contact List</span>
                    </div>
                  ),
                },
                {
                  key: 'delete-contact-list',
                  label: (
                    <Popconfirm
                      title="Delete Contact"
                      description="Are you sure to delete this contact?"
                      onConfirm={(e) => {
                        e.stopPropagation();
                        handleDeleteList(item?.id);
                      }}
                      okText="Yes"
                      cancelText="No"
                      icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
                    >
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        className="font-Montserrat flex gap-2 items-center justify-start"
                      >
                        <DeleteOutlined /> <span>Delete Contact List</span>
                      </div>
                    </Popconfirm>
                  ),
                },
              ],
            }}
            placement="bottom"
            arrow
          >
            <Button
              className="text-xl font-bold items-center flex items-center justify-center"
              type="text"
              icon={<EllipsisOutlined />}
              onClick={(e) => e.stopPropagation()}
            ></Button>
          </Dropdown>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <FormCreateNewList handleLoadData={handleGetData} />
          <Dropdown
            trigger={'click'}
            menu={{
              items: [
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: <div onClick={handleBulkDelete}>Delete</div>,
                },
              ],
            }}
            disabled={selectedContactList?.length === 0}
            placement="bottomLeft"
            arrow
          >
            <Button
              loading={deleteLoading}
              // type="primary"
              className="bg-white font-medium flex items-center justify-center"
            >
              <span className="mr-1">{selectedContactList?.length}</span>
              <span>SELECTED</span>
              <DownOutlined />
            </Button>
          </Dropdown>
        </div>
        <Segmented
          disabled={loading}
          className="customized-segmented-sequence-template"
          value={type}
          onChange={(value) => {
            setType(value);
            // setPage(1);
            handleGetData(value);
            setPagination(initialPagination);
            // getData(searchText, 1, limit, value);
          }}
          options={[
            {
              label: 'My Contact Lists',
              value: QUERY_CONTACTLIST_TYPES.MY_CONTACTLIST,
            },
            {
              label: 'Company Contact Lists',
              value: QUERY_CONTACTLIST_TYPES.COMPANY_CONTACTLIST,
            },
          ]}
        />

        <div className="flex items-center gap-3">
          <Dropdown
            disabled={loading}
            menu={{
              items: [
                {
                  label: (
                    <div onClick={() => setRangeLabel('All Range')}>
                      All Range
                    </div>
                  ),
                  key: 'all',
                  value: 'All range',
                },
                {
                  label: (
                    <div onClick={() => setRangeLabel('Under 50 contacts')}>
                      Under 50 contacts
                    </div>
                  ),
                  key: '0-50',
                },
                {
                  label: (
                    <div onClick={() => setRangeLabel('50 - 100 Contacts')}>
                      50 - 100 Contacts
                    </div>
                  ),
                  key: '50-100',
                },
                {
                  label: (
                    <div onClick={() => setRangeLabel('100 - 150 Contacts')}>
                      100 - 150 Contacts
                    </div>
                  ),
                  key: '100-150',
                },
                {
                  label: (
                    <div onClick={() => setRangeLabel('150+ Contacts')}>
                      {' '}
                      150+ Contacts
                    </div>
                  ),
                  key: '150-999999',
                },
              ],
              onClick: handleFilterRangeClick,
            }}
            trigger={['click']}
          >
            <Button
              className="font-Montserrat bg-white font-medium flex items-center justify-center"
              // icon={<DownOutlined />}
            >
              <TeamOutlined />
              <span>{rangeLabel || 'All Range'}</span>
              {/* <CaretDownOutlined /> */}
            </Button>
          </Dropdown>
          <Input
            disabled={loading}
            style={{
              width: '300px',
              height: '32px',
            }}
            className="!rounded-none search-input"
            onChange={handleSearch}
            placeholder="Search by contacts name, description,..."
          />
        </div>
      </div>
      {contextHolder}
      <div className="search-table-new-design-container mt-5">
        <Table
          rowSelection={rowSelection}
          rowKey={(record) => record?.id}
          loading={loading}
          dataSource={dataListTab}
          columns={columns}
          className="custom-table"
          pagination={{
            current: pagination.current,
            // pageSize: pagination.pageSize,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize });
            },
          }}
          onRow={(record, rowIndex) => {
            return {
              onClick: () => {
                navigate(`/contact_list/${record.id}`);
              },
              style: { cursor: 'pointer' },
            };
          }}
          // pagination={false}
        />
      </div>
      <DetailContactList
        isModalOpen={isModalOpenDetail}
        setIsModalOpen={setIsModalOpenDetail}
        currentDetail={currentDetail}
      />
      <DetailEditContactList
        isModalOpen={isModalOpenEditDetail}
        setIsModalOpen={setIsModalOpenEditDetail}
        currentDetail={currentDetail}
        handleLoadData={handleGetData}
      />
    </div>
  );
}

export default UserListTab;
