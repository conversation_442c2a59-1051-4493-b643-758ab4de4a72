import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  Alert,
  Button,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Space,
  Spin,
  Upload,
  notification,
} from 'antd';
import _debounce from 'lodash/debounce';
import SequenceItem from './SequenceItem';
import ModalCreateListSequence from './ModalCreateListSequence';
import { getAllEmails } from '../../services/search';
import SequenceDetail from './SequenceDetail';
import {
  createNewContactList,
  downLoadErrorFile,
  getExampleFile,
} from '../../services/contactList';
import {
  DownOutlined,
  PlusOutlined,
  UploadOutlined,
  UserAddOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import { COMMON_STRINGS } from '../../constants/common.constant';

function FormCreateNewList(props) {
  const { handleLoadData } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nameList, setNameList] = useState();

  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [loadingDownloadError, setLoadingDownloadError] = useState(false);
  const [errorData, setErrorData] = useState([]);
  const [dataFileInsert, setDataFileInsert] = useState([]);
  const [createdId, setCreatedId] = useState()

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setNameList("")
    setDescription("")
    setFileList([])
    setErrorData([])
    setIsModalOpen(false);
  };

  const handleSave = async () => {
    setErrorData([])
    if (!nameList) {
      notification.error({
        message: 'Please enter a name',
      });
      return;
    }

    let formData = new FormData();
    formData.append('name', nameList);
    formData.append('contactListDescription', description);
    if(fileList?.length > 0) {
      formData.append('csvFile', fileList[0]);
    }

    try {
      setLoading(true);
      const { data } = await createNewContactList(formData);
      if (data) {
        setCreatedId(data?.result?.dataCreate?.id)
        setDataFileInsert(data?.result?.fullData ?? [])
        if (data?.result?.errors?.length > 0) {
          notification.warning({
            message: 'Error on Upload CSV',
          });
          setErrorData(data?.result?.errors);
          setLoading(false);
          // return;
        }

        if(data?.result?.data?.length > 0) {
          notification.success({
            message: `List ${nameList}  is created, ${data?.result?.data?.length} / ${data?.result?.fullData?.length} are added`,
          });
        } else {
          notification.success({
            message: `Create List Successfully`,
          });
        }
        // setIsModalOpen(false);
        handleLoadData();
      }
      setLoading(false);
    } catch (e) {
      notification.error({
        message: e?.response?.data?.message
      });
      setLoading(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file) => {
      return false;
    },
    onChange({ file }) {
      if (fileList?.uid === file.uid) {
        setFileList([]);
      } else {
        setFileList([file]);
      }
    },
  };

  const handleDownloadExampleFile = async () => {
    setLoadingDownload(true);
    const response = await getExampleFile();
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'example.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    setLoadingDownload(false);
  };

  const handleDownLoadErrorFile = async () => {
    setLoadingDownloadError(true);
    const response = await downLoadErrorFile({data: errorData});
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'errors.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    setLoadingDownloadError(false);
  };

  return (
    <div>
      <Button
        className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
        type="primary"
        onClick={showModal}
      >
        <PlusOutlined />
        <span className="font-Montserrat">{COMMON_STRINGS.CREATE_NEW_LIST}</span>
      </Button>

      {/* <Dropdown
        className="mb-4"
        placement="bottom"
        arrow
        menu={{
          items: [
            {
              key: 'create-list',
              label: (
                <a
                  className="Montserrat flex gap-2 items-center py-2"
                  onClick={(e) => {
                    e.preventDefault();
                    showModal()
                  }}
                >
                  <UsergroupAddOutlined />
                  <span>Create New List</span>
                </a>
              ),
            },
            {
              key: 'create-contact',
              label: (
                <a
                  className="Montserrat flex gap-2 items-center py-2"
                  onClick={(e) => {
                    e.preventDefault();
                  }}
                >
                  <UserAddOutlined />
                  <span>Create New Contact</span>
                </a>
              ),
            },
          ],
        }}
      >
        <Space>
          <Button
            type="primary"
            className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
          >
            <p className="Montserrat">Create New</p>
            <DownOutlined />
          </Button>
        </Space>
      </Dropdown> */}
      <Modal
        title={<span className="font-Montserrat">Create New List</span>}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={false}
      >
        {/* <div style={{ marginTop: '20px' }}></div>
        <label>Name List</label>
        <Input
          onChange={(e) => setNameList(e.target.value)}
          placeholder="name"
        ></Input>
        <Button
          onClick={() => handleSave()}
          type="primary"
          className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
        >
          Save
        </Button> */}

        <div className="flex flex-col p-4 rounded-md border gap-5">
          <div className="grid grid-cols-4 gap-4 font-Montserrat">
            <label>Name:</label>
            <Input
              className="col-span-3 Montserrat"
              onChange={(e) => setNameList(e.target.value)}
              placeholder="Name"
              value={nameList}
            ></Input>
            <label>Description:</label>
            <Input.TextArea
              className="col-span-3 Montserrat"
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description"
              value={description}
            ></Input.TextArea>
            <label>Import csv:</label>
            <div className="col-span-3 Montserrat">
              <Upload {...uploadProps} fileList={fileList}>
                <Button icon={<UploadOutlined />}>Upload</Button>
              </Upload>
            </div>
          </div>
          <div
            onClick={() => {
              !loadingDownload && handleDownloadExampleFile();
            }}
            style={{
              cursor: 'pointer',
              textDecoration: 'underline',
              color: 'blue',
            }}
          >
            Download example file {loadingDownload ? <Spin /> : ''}
          </div>
          <div>
            {errorData?.length > 0 ? (
              <div>
                <Alert message={`There are  ${errorData?.length} / ${dataFileInsert?.length} contacts with errors, please download the file for more information.`} type="warning" showIcon closable />
                <div
                  onClick={() => {
                    !loadingDownloadError && handleDownLoadErrorFile();
                  }}
                  style={{
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    color: 'red',
                    marginTop: "10px"
                  }}
                >
                  Download errors file {loadingDownloadError ? <Spin /> : ''}
                </div>
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="w-full flex justify-end">
            {createdId && (
              <Button
              loading={loading}
              onClick={() => (window.location.href = `/contact_list/${createdId}`)}
              type="primary"
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-Montserrat"
            >
              View Detail Contact list
            </Button>
            )}
            
            <Button
              loading={loading}
              onClick={() => {
                handleSave();
              }}
              type="primary"
              style={{marginLeft: "20px"}}
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-Montserrat"
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default FormCreateNewList;
