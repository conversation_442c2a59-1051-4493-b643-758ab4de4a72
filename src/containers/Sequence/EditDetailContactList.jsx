import { Button, Input, Modal, Select, message, notification } from 'antd';
import { useEffect, useState } from 'react';
import {
  editContactList,
} from '../../services/contactList';

function DetailEditContactList(props) {
  const { isModalOpen, setIsModalOpen, currentDetail, handleLoadData } = props;

  const [messageApi, contextHolder] = message.useMessage();
  const [nameList, setNameList] = useState('');
  const [description, setDescription] = useState('');

  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (isModalOpen && currentDetail) {
      setNameList(currentDetail?.name);
      setDescription(currentDetail?.contactListDescription);
    }
  }, [isModalOpen]);

  const handleSave = async () => {
    if (!nameList) {
      notification.error({
        message: 'Please enter a name',
      });
    }
    try {
      setLoading(true);
      const { data } = await editContactList(currentDetail?.id, {
        name: nameList,
        contactListDescription: description,
      });

      if (data) {
        notification.success({
          message: 'Update ContactList successfully',
        });
        setIsModalOpen(false);
        handleLoadData();
      }

      setLoading(false);
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
      setLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <Modal
        title={<span className="font-Montserrat">Edit a Group</span>}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={false}
      >
        {/* <div style={{ marginTop: '20px' }}></div> */}
        <div className="flex flex-col p-4 rounded-md border gap-5">
          <div className="grid grid-cols-4 gap-4 font-Montserrat">
            <label>Name:</label>
            <Input
              className="col-span-3 Montserrat"
              onChange={(e) => setNameList(e.target.value)}
              placeholder="Name"
              value={nameList}
            ></Input>
            <label>Description:</label>
            <Input.TextArea
              className="col-span-3 Montserrat"
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description"
              value={description}
            ></Input.TextArea>
          </div>
          <div className="w-full flex justify-end">
            <Button
              loading={loading}
              onClick={() => handleSave()}
              type="primary"
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-Montserrat"
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default DetailEditContactList;
