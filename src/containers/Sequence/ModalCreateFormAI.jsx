import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
  notification,
} from 'antd';
import {
  EnvironmentOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import SequenseEmail from '../../containers/SequenseEmail';
import { Controller, useForm } from 'react-hook-form';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import Email1Logo from '../../assets/img/ai.png';
import TextArea from 'antd/es/input/TextArea';
import {
  generateFreeContent,
  generateFreeSubject,
} from '../../services/search';
import { v4 as uuid } from 'uuid';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import BullhornSendEmailModal from '../../components/BullHorn/EmailtriggerStep';
import { sendEmail } from '../../services/notification';
import { getSignatureDefault } from '../../services/signatures';

function ModalCreateFormAI() {
  const [open, setOpen] = useState(false);
  const idUserViewAs = getUserViewAs();
  const [openModal, setOpenModal] = useState(false);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [loading, setLoading] = useState(false);
  const [notDiscard, setNotDiscard] = useState(true);
  const [notConfirmStep, setConfirmStep] = useState(false);
  const [emailSeqId, setEmailSeqId] = useState('');
  const [openReGenerateAiModal, setOpenReGenerateAiModal] = useState(false);
  const [regenerateAIStatus, setRegenerateAIStatus] = useState('');
  const [contactListSelected, setContactListSelected] = useState([]);

  const { handleSubmit, control, getValues, setValue, watch } = useForm();

  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  const replaceImagePlaceholders = (text, imgMetadata) => {
    let counter = 1;

    imgMetadata?.forEach(function (imgData) {
        text = text.replace(/<img[^>]*?src="(cid:[^"]*?|{{IMAGE_\d+}})"[^>]*?>/g, function(match) {
            const replacedMatch = match.replace(/<img/g, function(imgMatch) {
                return imgMatch + ' id="create-by-code-' + counter + '"';
            });
            counter++;
            return replacedMatch;
        });
        const regex = new RegExp('src="(cid:' + imgData.imgId + '|{{IMAGE_' + (counter - 1) + '}})"', 'g');
        text = text.replace(regex, 'src="' + imgData.link + '"');

        const crossoriginRegex = new RegExp('crossorigin="use-credentials"', 'g');
        text = text.replace(crossoriginRegex, '');
    });
    return text;
};

  const handleGetSignatureData = async () => {
    try {
      const { data } = await getSignatureDefault();
      if (data) {
        const newUrl = data?.result?.sigContent || "";
        return newUrl;
      }
      return ""
    } catch (error) {
      return ""
    }
  };

  const handleGenerateEmail = async () => {
    setInputNumberStep([])
    if (!getValues('location') || getValues('location') == '') {
      notification.error({
        message: 'Please enter a location',
      });
      return;
    }
    if (!getValues('type') || getValues('type') == '') {
      notification.error({
        message: 'Please enter a type',
      });
      return;
    }
    if (!getValues('position') || getValues('position') == '') {
      notification.error({
        message: 'Please enter a position',
      });
      return;
    }
    if (!getValues('description') || getValues('description') == '') {
      notification.error({
        message: 'Please enter a description',
      });
      return;
    }
    setLoading(true);
    const payload = {
      location: getValues('location'),
      type: getValues('type'),
      position: getValues('position'),
      description: getValues('description'),
    };
    try {
      const [contentsResponse, subjectResponse] = await Promise.all([
        generateFreeContent(payload),
        generateFreeSubject(payload),
      ]);
      setLoading(false);
      const contents = contentsResponse?.data?.result.content ?? [];
      const subject = subjectResponse?.data?.result.subject ?? [];

      const dataSignature = await handleGetSignatureData()

      const mapStep = contents
        ?.map((email, index, array) => {
          const key1 = uuid();
          const result = [
            {
              key: key1,
              subject: subject[index],
              content: email + dataSignature,
            },
          ];
          if (index < array.length - 1) {
            const key2 = uuid();
            result.push({
              key: key2,
              delay: 3,
            });
          }
          return result;
        })
        .flat();
      setInputNumberStep(mapStep);
      setOpenModal(true);
    } catch (error) {
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleSendEmail = async () => {
    const payload = {
      emailSeqId,
      createdFrom: getValues('sendEmail.createFrom') ?? [],
      recipients: getValues('sendMail.listEmailSend') ?? [],
      isNotSendSequence: false,
      primaryMail: {
        subject: inputNumberStep[0]?.subject,
        content: inputNumberStep[0]?.content,
        delay: getValues('sendMail.mailStepParent')
          ? getValues('sendMail.mailStepParent')
          : 1,
        unit: getValues('firstStepUnit') ? getValues('firstStepUnit') : 'DAY',
      },
    };

    try {
      // call API emails/send
      const { data } = await sendEmail(payload);

      if (data?.result?.emailSeqId) {
        setEmailSeqId(data?.result?.emailSeqId);
      }

      if (data?.result?.isRegistered) {
        notification.success({
          message: 'Success',
          description: 'Email has been sent!',
        });
      } else {
        notification.warning({
          message: 'Warning',
          description:
            'Email has been sent, but seem your email has not been granted permission to send sequence email. We sent you a guideline to your email. Please follow it then try again',
          duration: 10,
        });
      }
    } catch (e) {
      if ((e?.response?.data?.message || '').includes('should not be empty')) {
        notification.error({
          message: 'Error!',
          description: 'Can not send to inactive contact',
        });
      } else {
        notification.error({
          message: 'Error!',
          description: e?.response?.data?.message,
        });
      }
    }
  };

  return (
    <>
      <div
        onClick={() => setOpen(true)}
        style={{
          width: '45%',
          border: '1px solid #ccc',
          padding: '10px',
          cursor: 'pointer',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Image style={{ width: '83px', height: '72px' }} src={Email1Logo} />
        </div>
        <div
          style={{
            textAlign: 'center',
            marginTop: '20px',
            fontSize: '20px',
            fontWeight: '600',
          }}
        >
          AI Wizard
        </div>
        <div style={{ textAlign: 'center', marginTop: '10px' }}>
          Create a simple outbound sequence with one click.
        </div>
      </div>
      <Modal
        title="Create a new Sequence Using AI"
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        footer={
          <div style={{ marginTop: '50px' }}>
              <Tooltip
                title={!emailSeqId ? 'Please click Save before Send Email' : ''}
                placement="bottom"
              >
                <Button
                  htmlType="button"
                  onClick={handleSendEmail}
                  type="primary"
                  style={{ marginLeft: '10px' }}
                  disabled={emailSeqId ? false : true}
                  // loading={sendEmailStatus}
                >
                  Send Email
                </Button>
              </Tooltip>
            <Button
              disabled={loading}
              onClick={() => {
                setOpen(false);
                setInputNumberStep([]);
                setOpenModal(false);
              }}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              onClick={() => {
                !emailSeqId ?  handleGenerateEmail() : setOpenReGenerateAiModal(true)
              }}
              disabled={loading}
              loading={loading}
            >
              Generate AI
            </Button>
          </div>
        }
        width={900}
      >
        <div>
          Use AI to generate a complete campaign with sequential contact points
          to engage target audiences at scale.
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ width: '45%' }}>
            <div style={{ marginTop: '10px' }}>
              Location <span style={{ color: 'red' }}>*</span>
            </div>
            <div>
              <Form.Item name="location">
                <div style={{ width: '100%' }}>
                  <Controller
                    render={({ field }) => (
                      <div>
                        <Input
                          style={{ width: '100%' }}
                          onChange={(e) => setValue('location', e.target.value)}
                        />
                      </div>
                    )}
                    name="location"
                    control={control}
                  />
                </div>
              </Form.Item>
            </div>
            <div style={{ marginTop: '10px' }}>
              Type <span style={{ color: 'red' }}>*</span>
            </div>
            <div>
              <Form.Item name="type">
                <div style={{ width: '100%' }}>
                  <Controller
                    render={({ field }) => (
                      <div>
                        <Input
                          style={{ width: '100%' }}
                          onChange={(e) => setValue('type', e.target.value)}
                        />
                      </div>
                    )}
                    name="type"
                    control={control}
                  />
                </div>
              </Form.Item>
            </div>
            <div style={{ marginTop: '10px' }}>
              Position <span style={{ color: 'red' }}>*</span>
            </div>
            <div>
              <Form.Item name="position">
                <div style={{ width: '100%' }}>
                  <Controller
                    render={({ field }) => (
                      <div>
                        <Input
                          style={{ width: '100%' }}
                          onChange={(e) => setValue('position', e.target.value)}
                        />
                      </div>
                    )}
                    name="position"
                    control={control}
                  />
                </div>
              </Form.Item>
            </div>
            <div style={{ marginTop: '10px' }}>
              Description <span style={{ color: 'red' }}>*</span>
            </div>
            <div>
              <Form.Item name="description">
                <div style={{ width: '100%' }}>
                  <Controller
                    render={({ field }) => (
                      <div>
                        <TextArea
                          showCount
                          maxLength={100}
                          style={{ height: 120, resize: 'none' }}
                          onChange={(e) =>
                            setValue('description', e.target.value)
                          }
                        />
                      </div>
                    )}
                    name="description"
                    control={control}
                  />
                </div>
              </Form.Item>
            </div>
          </div>
          <div style={{ width: '53%' }}>
            {openModal && !loading && (
              <BullhornSendEmailModal
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModal}
                setOpenSendEmail={setOpenModal}
                listAddContactSelected={[]}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                setConfirmStep={setConfirmStep}
                // job={job}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                // fromSequenseEmail={true}
                newUpdatedSequence={true}
                // seqId={selectedValue?.id}
                // loadingDataEmail={loadingDataEmail}
                fromSequenceAI={true}
                setNotDiscard={setNotDiscard}
                notDiscard={notDiscard}
                onHandleSeqId={handleEmailSeqId}
                emailSeqId={regenerateAIStatus == "CREATE_NEW" ? null : emailSeqId}
                setContactListSelected={setContactListSelected}
                contactListSelected={contactListSelected}
              />
            )}
            {loading && (
              <div
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Spin size="large" />
              </div>
            )}
          </div>
        </div>
      </Modal>
      <Modal title="Generate AI" open={openReGenerateAiModal} footer={false} onOk={() => setOpenReGenerateAiModal(false)} onCancel={() => setOpenReGenerateAiModal(false)}>
        <div>
          <div>Do you want to create a new sequence or update this sequence?</div>
          <div style={{
            display: "flex",
            justifyContent: "center",
            marginTop: "20px"
          }}>
            <Button
              onClick={() => {
                setRegenerateAIStatus("CREATE_NEW");
                setEmailSeqId(null)
                handleGenerateEmail()
                setOpenReGenerateAiModal(false)
              }}
            >Create a new sequence</Button>
            <Button
              style={{marginLeft: "20px"}}
              onClick={() => {
                setRegenerateAIStatus("UPDATE");
                handleGenerateEmail()
                setOpenReGenerateAiModal(false)
              }}
            >Update this sequence</Button>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default ModalCreateFormAI;
