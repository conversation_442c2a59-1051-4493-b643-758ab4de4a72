import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Radio,
  Rate,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  message,
  notification,
} from 'antd';
import {
  EnvironmentOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import zileoIcon from '../../assets/img/zileoIcon.png';
import logo from '/logo_bull.webp';
import SequenseEmail from '../SequenseEmail';
import { useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { getDetailContactList, getNewContactList } from '../../services/contactList';
import Search from 'antd/es/input/Search';
import moment from 'moment';

function ModalCreateFromHotList() {
  const [open, setOpen] = useState(false);

  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [dataListTab, setDataListTab] = useState();
  const [messageApi, contextHolder] = message.useMessage();
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [existingContacts, setExistingContacts] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState(null);
  const [loadingContact, setLoadingContact] = useState(false);
  const [loadingDataEmail, setLoadingDataEmail] = useState(false);
  const [listSelect, setListSelect] = useState()
  const [loading, setLoading] = useState(true);
  const [chooseItem, setChooseItem] = useState(null);
  const selectedHotList = [];
  const [hotListData, setHotListData] = useState([]);

  const {
    options: data,
    handleSearch: setHotListSearchText,
    handleScrollPopup: handleHotListScroll,
    handleSearch: setHotListText,
    isLoading: loadingHotList,
  } = useInfiniteScrollWithSearch(
    () => searchBullhornData('Tearsheet')(0, 1000)
  );

  useEffect(() => {
    setLoading(true)
    setHotListText('')
    setLoading(false)
  }, []);

  const handleChoseList =async () => {
    if(!chooseItem) {
      notification.error({
        message: "Please select a list"
      })
      return
    }
    const listLabel = `Contact from ${chooseItem?.[0].name}`;
    const item = {
      name: listLabel,
      label: listLabel,
      value: chooseItem?.[0].id,
      hotListId: chooseItem?.[0].id,
    };

    setHotListData([item]);
    setValue(`sendMail.hotList`, [item]);
    setOpenSendEmail(true);
  }

  const columns = [
    {
      title: 'Item',
      dataIndex: 'Item',
      width: '90%',
      render: (_, record) => (
        <div className="flex flex-col gap-1">
          <div className="flex justify-between">
            <span className="text-base font-semibold">{record.name}</span>
            <span className="text-xs text-gray-800">
              Date Added: {moment(record.dateAdded).format('MM/DD/YYYY')}
            </span>
          </div>
          <div className="text-xs text-gray-800">
            Client Contacts: {record.clientContacts?.total || 0}
          </div>
        </div>
      ),
    },
  ];

  const rowSelection = {
    onChange: (_selectedRowKeys, selectedRows) => {
      setChooseItem(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: !record.clientContacts?.total,
      name: record.name,
    }),
  };

  return (
    <>
      {contextHolder}
      <div
        onClick={() => setOpen(true)}
        style={{
          width: '30%',
          border: '1px solid #ccc',
          padding: '10px',
          cursor: 'pointer',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Image
            style={{ width: '83px', height: '72px' }}
            preview={false}
            src={logo}
          />
        </div>
        <div
          style={{
            textAlign: 'center',
            marginTop: '20px',
            fontSize: '20px',
            fontWeight: '600',
          }}
        >
          Bullhorn hot list
        </div>
        <div style={{ textAlign: 'center', marginTop: '10px' }}>
          Create a new sequence from Bullhorn hot list.
        </div>
      </div>
      <Modal
        title="Create a new Sequence From Bullhorn hot list"
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        footer={false}
        width={550}
      >
        <div>Select a Bullhorn hot list</div>
        <div style={{ marginTop: '10px' }}>
        <div style={{ marginTop: '20px', marginBottom: '20px' }}>
            <Search onSearch={(searchText) => setHotListSearchText(searchText)} placeholder="input search text" style={{ width: 200 }} onChange={(e) => setValue("searchText", e.target.value)} />
          </div>
          {loading && (
            <div className="w-full flex justify-center h-96 items-center">
              <Spin size="large" />
            </div>
          )}
          {!loading && (
            <Table
              columns={columns}
              dataSource={data.filter(({ id }) => id).map(hotList => ({ ...hotList, key: hotList.id }))}
              showHeader={false}
              rowSelection={{
                type: 'radio',
                ...rowSelection,
              }}
              selectedRowKeys={selectedHotList.map(({ value }) => value)}
              // onScroll={(e) =>
              //   handleHotListScroll(e, 'Tearsheet')
              // }
              pagination={{ pageSize: 5 }}
              loading={loadingHotList}
            />
          )}
          <div style={{marginTop: "30px", float: "right"}}>
            <Button onClick={() => setOpen(false)}>Cancel</Button>
            <Button type="primary" style={{marginLeft: "10px" }} onClick={handleChoseList}>Choose</Button>
          </div>
          <div style={{clear: "both"}}></div>
        </div>
        {!loadingContact && !loadingDataEmail && openModalSendEmail && (
          <Modal
            width={800}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>New Email</div>
              </div>
            }
            open={openModalSendEmail}
            onCancel={() => {
              setValue('sendMail.content', null);
              setValue('sendMail.subject', null);
              setOpenSendEmail(false);
            }}
            footer={false}
          >
            <div>
              <Form layout="vertical">
                <BullhornSendEmail
                  watch={watch}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  sendToEmail={getValues()?.email}
                  mailTitle={getValues()?.jobtitle}
                  openModalSendEmail={openModalSendEmail}
                  setOpenSendEmail={setOpenSendEmail}
                  listAddContactSelected={existingContacts}
                  setNumberStep={setNumberStep}
                  numberStep={numberStep}
                  inputNumberStep={inputNumberStep}
                  setInputNumberStep={setInputNumberStep}
                  job={getValues('leadSelected')}
                  setEmailConfigData={setEmailConfigData}
                  emailConfigData={emailConfigData}
                  fromSequenseEmail={true}
                  loadingDataEmail={loadingDataEmail}
                  isFromVacancy={true}
                  hostListOther={hotListData}
                />
              </Form>
            </div>
          </Modal>
        )}
      </Modal>
    </>
  );
}

export default ModalCreateFromHotList;
