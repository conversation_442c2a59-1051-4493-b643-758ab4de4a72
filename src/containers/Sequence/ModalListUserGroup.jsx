import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';
import { v4 as uuid } from 'uuid';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Pagination,
  Radio,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  Tag,
  message,
  notification,
} from 'antd';
import _debounce from 'lodash/debounce';
import Email3Logo from '../../assets/img/files.png';
import Search from 'antd/es/input/Search';
import { getAllEmails, getListEmailFromSequence } from '../../services/search';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import {
  addUserToList,
  bulkAddUserToList,
  createNewContactList,
  getNewContactList,
} from '../../services/contactList';
import { COMMON_STRINGS } from '../../constants/common.constant';

function ModalListUserGroup(props) {
  const { open, setOpen, currentContact, currentContacts, enrichFields } =
    props;
  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [dataListTab, setDataListTab] = useState();
  const [messageApi, contextHolder] = message.useMessage();
  const [dataEmailsSequence, setDataEmailSequence] = useState([]);
  const [chooseItem, setChooseItem] = useState(null);
  const [tabState, setTabState] = useState(false);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [pending, setPending] = useState(false);
  const [valueSelected, setValueSelected] = useState([]);
  const [modalCreateNewList, setModalCreateNewList] = useState(false);
  const [nameList, setNameList] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const handleGetData = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await getNewContactList(currentContact?.id);
      setDataListTab(data?.result);
      messageApi.destroy();
    } catch (error) {
      console.log('error', error);
      messageApi.destroy();
    }
  };

  useEffect(() => {
    if (open) {
      handleGetData();
    }
  }, [open]);

  const handleOke = async () => {
    if (valueSelected?.length === 0) {
      notification.error({
        message: 'Please select a group',
      });
      return;
    }
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading ...',
        duration: 0,
      });
      if (currentContacts?.length) {
        const bulkAddContactsPayload = currentContacts.map((item) => ({
          name: item?.name,
          email: item?.email,
          phone: item?.phone ?? null,
          contactApolloId: item?.id,
          linkedinUrl: item?.linkedin_url,
          contactTitle: item?.title,
          companyName: item?.organization_name ?? null,
        }));

        const { data } = await bulkAddUserToList({
          listIds: valueSelected,
          contacts: bulkAddContactsPayload,
          requestId: uuid(),
          enrichFields,
        });
      } else {
        const { data } = await addUserToList({
          name: currentContact?.name,
          email: currentContact?.email,
          phone: currentContact?.phone ?? null,
          contactApolloId: currentContact?.id,
          listIds: valueSelected,
          linkedinUrl: currentContact?.linkedin_url,
          contactTitle: currentContact?.title,
          companyName: currentContact?.organization_name ?? null,
          enrichFields,
        });
      }
      setOpen(false);
      messageApi.destroy();
      notification.success({
        message: 'Success',
        description: (
          <div>
            You have successfully added{' '}
            <Tag className="text-xl font-medium px-1" color="cyan">
              {currentContacts?.length}
            </Tag>{' '}
            contacts to Contact List(s)
          </div>
        ),
      });
    } catch (error) {
      notification.error({
        message: 'Error creating',
        description:
          error?.response?.data?.message ||
          error?.message ||
          'Some things went wrong',
      });
    }
  };

  const plainOptions = dataListTab?.map((item) => {
    return {
      label: (
        <div
          style={{
            width: '600px',
            boxShadow: '5px 8px 20px 0px #ccc',
            padding: '10px',
            marginTop: '10px',
            borderRadius: '10px',
          }}
        >
          <div>{item?.name}</div>
          <div>
            <span style={{ fontSize: '12px' }}>Users in list: </span>
            {item?.contactCount}
          </div>
        </div>
      ),
      value: item?.id,
    };
  });

  const onChange = (checkedValues) => {
    setValueSelected(checkedValues);
  };

  const handleSave = async () => {
    if (!nameList) {
      notification.error({
        message: 'Please enter a name',
      });
      return;
    }

    let formData = new FormData();
    formData.append('name', nameList);
    formData.append('contactListDescription', description);
    try {
      setLoading(true);
      const { data } = await createNewContactList(formData);
      if (data) {
        notification.success({
          message: `Create List Successfully`,
        });
        handleGetData();
        setModalCreateNewList(false);
      }
      setLoading(false);
    } catch (e) {
      notification.error({
        message: e?.response?.data?.message,
      });
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        title={COMMON_STRINGS.BULK_ADD_TO_CONTACT_LIST}
        centered
        open={open}
        footer={
          !tabState ? (
            <>
              <div style={{ marginTop: '20px' }}>
                <Button onClick={() => setModalCreateNewList(true)}>
                  Create new list
                </Button>
                <Button onClick={() => setOpen(false)}>Cancel</Button>
                <Button type="primary" onClick={() => handleOke()}>
                  Choose
                </Button>
              </div>
            </>
          ) : (
            false
          )
        }
        onOk={() => handleOke()}
        onCancel={() => setOpen(false)}
        width={700}
      >
        <div>
          {contextHolder}
          <div style={{ marginTop: '20px' }}>
            {/* <Search
            //   onSearch={() => handleDateSequence(null, page, 10)}
              placeholder="input search text"
              style={{ width: 200 }}
              onChange={(e) => setValue('searchText', e.target.value)}
            /> */}
          </div>
          <Checkbox.Group
            options={plainOptions}
            defaultValue={['Apple']}
            onChange={onChange}
          />
        </div>
      </Modal>
      <Modal
        title={<span className="font-Montserrat">Create New List</span>}
        open={modalCreateNewList}
        onOk={() => {}}
        onCancel={() => setModalCreateNewList(false)}
        footer={false}
      >
        <div className="flex flex-col p-4 rounded-md border gap-5">
          <div className="grid grid-cols-4 gap-4 font-Montserrat">
            <label>Name:</label>
            <Input
              className="col-span-3 Montserrat"
              onChange={(e) => setNameList(e.target.value)}
              placeholder="Name"
              value={nameList}
            ></Input>
            <label>Description:</label>
            <Input.TextArea
              className="col-span-3 Montserrat"
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description"
              value={description}
            ></Input.TextArea>
          </div>
          <div className="w-full flex justify-end">
            <Button
              loading={loading}
              onClick={() => {
                handleSave();
              }}
              type="primary"
              style={{ marginLeft: '20px' }}
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-Montserrat"
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default ModalListUserGroup;
