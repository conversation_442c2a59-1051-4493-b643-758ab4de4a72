import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Breadcrumb,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  Tabs,
  Tooltip,
} from 'antd';
import {
  AppstoreOutlined,
  BarChartOutlined,
  EditOutlined,
  FieldTimeOutlined,
  FileSearchOutlined,
  LeftOutlined,
  LogoutOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { useForm } from 'react-hook-form';
import { getListEmailFromSequence } from '../../services/search';
import ReportSequenceChart from './ReportSequenceChart';
import SequenceActivityLog from './SequenceActivityLog';
import JobDetail from '../../components/JobDetail';
import { v4 as uuid } from 'uuid';
import { ADD_STEP_TYPE } from '../../components/BullHorn/EmailtriggerStep';
import { getContactList } from '../../services/contactList';
import { getShortlistById, searchCommonBulhorn } from '../../services/bullhorn';
import { parseBHQuery } from '../../utils/common';
import { findProp } from '../../helpers/util';
import ModalEditNameSequence from '../../components/BullHorn/ModalEditNameSequence';
import SequenceActivityLogV2 from './SequenceActivityLogV2';

export const getContactLists = async (contactListIds) => {
  let result = contactListIds;

  try {
    const { data } = await getContactList(contactListIds);
    if (data?.result?.length > 0) {
      result = [...data?.result];
    }
    return result;
  } catch (error) {
    return result;
  }
};

export const getHotLists = async (hotListIds) => {
  const entity = 'Tearsheet';
  if (!hotListIds || hotListIds?.length === 0) return hotListIds;
  let result = hotListIds;
  const query = parseBHQuery(hotListIds);
  try {
    const { data } = await searchCommonBulhorn(
      entity,
      query,
      ['id', 'name'],
      0,
      100
    );
    if (data?.result?.data?.length > 0) {
      result = [...data?.result?.data];
    }
    return result;
  } catch (error) {
    return result;
  }
};

export const getShortLists = async (shortListIds) => {
  if (!shortListIds || shortListIds?.length === 0) return shortListIds;
  let result = shortListIds;
  try {
    const listShortListRes = await Promise.all(
      shortListIds?.map((id) => getShortlistById(id))
    );
    result = listShortListRes?.flatMap((item) => {
      const shortListData = item?.data?.result?.[0]?.jobOrder || [];

      return { ...shortListData, name: shortListData?.title };
    });
    return result;
  } catch (error) {
    return result;
  }
};

function SequenceDetail(props) {
  const {
    tabDetail,
    setTabDetail,
    selectedValue,
    setSelectedValue,
    handleDateSequence,
    page,
    fromDate,
    toDate,
    seqId,
  } = props;
  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [emailConfigData, setEmailConfigData] = useState();
  const [emailSendTo, setEmailSendTo] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [showDetail, setShowDetail] = useState(false);
  const [openEditNameModal, setOpenEditNameModal] = useState(false);
  const [job, setJob] = useState(null);

  const handleGetListData = async () => {
    const { data } = await getListEmailFromSequence(selectedValue?.id || seqId);
    console.log('data: ', data);

    const child = data?.result?.sequence?.child || null;
    const latestSequence = data?.result?.sequence || selectedValue;

    const jobTemp = data?.result?.job;
    setValue(
      'companyDetail',
      data?.result?.recipients?.[0]?.recipients?.[0]?.companyDetail
    );
    if (jobTemp) {
      setValue(
        'companyId',
        data?.result?.sequence?.companyId || data?.result?.job?.companyBhId
      );
      setValue(
        'companySequenceContactId',
        data?.result?.sequence?.companyId || data?.result?.job?.companyBhId
      );
      setJob(jobTemp);
    }

    const stepsTemp = data?.result?.mails;
    // Mapping participants in Linkedin Step
    const linkedinStepParticipantsTemp =
      getValues('linkedinStepParticipants') || [];
    const linkedinStepParticipants = [];
    const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
      if (
        step?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
        (step?.recipients?.length > 0 ||
          step?.contactListIds?.length > 0 ||
          step?.hotlistIds?.length > 0)
      ) {
        const linkedinStepParticipantsObject = {
          id: step?.id || uuid(),
          key: step?.key || uuid(),
          contactListIds: step?.contactListIds || [],
          hotlistIds: step?.hotlistIds || [],
          recipients: step?.recipients || [],
        };
        linkedinStepParticipants.push(linkedinStepParticipantsObject);
      }
      return [
        ...step?.delays.map((delayItem) => ({
          ...delayItem,
          unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
          key: uuid(),
        })),
        { ...step, key: step?.id || uuid() },
      ];
    });
    setValue('linkedinStepParticipants', [
      ...linkedinStepParticipants,
      ...linkedinStepParticipantsTemp,
    ]);

    const participantsTemp =
      data?.result?.sequence?.participants ||
      data?.result?.recipients[0] ||
      null;
    const recipients = participantsTemp?.recipients || [];
    const mergedRecipients = [...recipients];

    if (participantsTemp) {
      const {
        contactListIds,
        hotlistIds,
        recipients: sendToList,
        shortListIds = [],
      } = participantsTemp;

      const list = await Promise.all([
        await getContactLists(contactListIds),
        await getHotLists(hotlistIds),
        await getShortLists(shortListIds),
      ]);

      setValue('participants', {
        contactList: list[0],
        hotList: list[1],
        shortList: list[2],
        sendTo: sendToList,
      });
    }

    const childList = findProp(data?.result?.sequence, 'child') || [];
    const newSelectedValue = {
      ...latestSequence,
      currentNumberStepCount: 1,
      ignoreParticipantsIds: [],
    };
    if (childList?.length > 0) {
      const ignoreParticipantsIds = childList?.flatMap((child) => {
        const { contactListIds, hotlistIds, recipients } =
          child?.sequence?.participants;
        const ignoreIds = [
          ...(contactListIds || []),
          ...(hotlistIds || []),
          ...(recipients?.map((contact) => contact?.id)?.filter((id) => id) ||
            []),
        ];
        return ignoreIds;
      });
      newSelectedValue.ignoreParticipantsIds = ignoreParticipantsIds;
      newSelectedValue.currentNumberStepCount = childList?.length + 1;
    }

    if (child) {
      newSelectedValue.child = child;
    }
    setSelectedValue({ ...newSelectedValue });

    console.log('newSelectedValue: ', newSelectedValue);

    setValue('sequenceData', data?.result?.sequence);
    setValue('sequenceObject', inputNumberStepTemp);
    setEmailSendTo(mergedRecipients);
    setInputNumberStep([...inputNumberStepTemp]);
    setEmailConfigData(data?.result?.mails);
    setValue(`sendMail.mailStepParentMailTo`, mergedRecipients ?? []);
    setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
    setValue(
      `sendMail.mailStepParentContent`,
      data?.result?.mails?.[0]?.content
    );
    setValue(
      `sendMail.mailStepParentSubject`,
      data?.result?.mails?.[0]?.subject
    );
    setValue(
      'sendMail.listEmail',
      mergedRecipients?.map((item) => item.email)
    );
    setValue(
      'sendMail.listEmailSend',
      mergedRecipients?.map((item) => item.email)
    );
    setNumberStep(inputNumberStepTemp?.inputNumberStep);
    setShowDetail(true);
  };

  useEffect(() => {
    if ((selectedValue && tabDetail) || seqId) {
      handleGetListData();
    }
    return () => {};
  }, []);

  const items = [
    {
      key: '1',
      label: (
        <>
          <div>
            <span style={{ marginRight: '10px' }}>
              <AppstoreOutlined />
            </span>
            Overview
          </div>
        </>
      ),
      children: (
        <>
          {showDetail ? (
            <div
              style={{ width: '100%' }}
              className="grid border shadow-sm bg-[#f1f5f8] grid-cols-10 p-4 rounded-md sequence-detail-container"
            >
              {job && (
                <div className="h-full overflow-scroll pt-3 px-3 col-span-5">
                  <JobDetail job={job} />
                </div>
              )}
              <div
                className="col-span-5 sequence-detail w-full"
                style={{ height: '53vh', overflowY: 'scroll' }}
              >
                <Form layout="vertical">
                  <BullhornSendEmail
                    watch={watch}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    sendToEmail={getValues()?.email}
                    mailTitle={getValues()?.jobtitle}
                    openModalSendEmail={tabDetail}
                    setOpenSendEmail={setTabDetail}
                    listAddContactSelected={emailSendTo}
                    setNumberStep={setNumberStep}
                    numberStep={numberStep}
                    inputNumberStep={inputNumberStep}
                    setInputNumberStep={setInputNumberStep}
                    job={job}
                    setEmailConfigData={setEmailConfigData}
                    emailConfigData={emailConfigData}
                    fromSequenseEmail={true}
                    fromSequenseDetail={true}
                    seqId={selectedValue?.id || seqId}
                    selectedSequenceValue={selectedValue}
                    // loadingDataEmail={loadingDataEmail}
                    notLoadingData={true}
                    newUpdatedSequence={true}
                    reloadSequenceDetail={handleGetListData}
                  />
                </Form>
              </div>
            </div>
          ) : (
            <Spin />
          )}
        </>
      ),
    },
    {
      key: '2',
      label: (
        <>
          <div>
            <span style={{ marginRight: '10px' }}>
              <FieldTimeOutlined />
            </span>
            Activity Logs
          </div>
        </>
      ),
      children: (
        <>
          <SequenceActivityLogV2
            sequenceData={selectedValue}
            seqId={seqId}
            inputNumberStep={inputNumberStep}
          />
        </>
      ),
      disabled: !showDetail,
    },
    // {
    //   key: '3',
    //   label: (
    //     <>
    //       <div>
    //         <span style={{ marginRight: '10px' }}>
    //           <BarChartOutlined />
    //         </span>
    //         Report
    //       </div>
    //     </>
    //   ),
    //   children: (
    //     <>
    //       <ReportSequenceChart sequenceData={selectedValue} seqId={seqId} />
    //     </>
    //   ),
    // },
    // {
    //   key: '4',
    //   label: (
    //     <>
    //       <div>
    //         <span style={{ marginRight: '10px' }}>
    //           <FileSearchOutlined />
    //         </span>
    //         Templates
    //       </div>
    //     </>
    //   ),
    //   children: <SequenceTemplate />,
    // },
  ];
  return (
    <>
      <div>
        <Breadcrumb
          items={[
            {
              title: (
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() => setTabDetail(false)}
                >
                  Sequence
                </div>
              ),
            },
            {
              title: <>{selectedValue?.name}</>,
            },
          ]}
        />
      </div>
      <div style={{ marginTop: '10px' }}>
        <Button
          className="bg-white  flex items-center "
          onClick={() => {
            setTabDetail(false),
              setSelectedValue(null),
              handleDateSequence(null, page, 10, null, null, fromDate, toDate);
          }}
          icon={<LeftOutlined />}
        >
          Back
        </Button>
      </div>
      <div style={{ marginTop: '30px', fontSize: '20px' }} className="max-w-md">
        {!openEditNameModal && (
          <div>
            <span>{watch('sequenceData')?.name || selectedValue?.name}</span>
            <Tooltip title="Click to edit Sequence name.">
              <Button
                type="text"
                className="font-semibold hover:text-[#2684c7] text-[#5a5f7d]"
                onClick={(e) => {
                  setOpenEditNameModal(!openEditNameModal);
                }}
                icon={<EditOutlined className="text-cyan-600" />}
              ></Button>
            </Tooltip>
          </div>
        )}
        {openEditNameModal && (
          <ModalEditNameSequence
            getValues={getValues}
            setValue={setValue}
            setOpenEditNameModal={setOpenEditNameModal}
            openEditNameModal={openEditNameModal}
            sequenceId={selectedValue?.id || seqId}
            isDependency
          />
        )}
      </div>
      <div style={{ marginTop: '10px' }} className="sequence-detail-page">
        <Tabs defaultActiveKey="1" items={items} />
      </div>
    </>
  );
}

export default SequenceDetail;
