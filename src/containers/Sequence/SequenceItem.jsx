import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';
import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Input,
  Menu,
  Modal,
  Rate,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Table,
  notification,
} from 'antd';
import EditableNumber from '../../components/EditableNumberComponent/EditableNumber';
import LeadStatusCRUD from '../../components/JobsLeads/LeadStatusCRUD';
import LeadsDragDrop from '../../components/JobsLeads/LeadsDragDrop';
import useJobLeads from '../../hooks/useJobLeads';
import { bulkUpdateLeadStatus } from '../../services/jobLeadStatuses';

import { useDispatch } from 'react-redux';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { saveAllStatusLead } from '../../store/common';
import Mailbox from '../../components/Mailbox';
import {
  countLeadMailBox,
  getLeadsByCompany,
  getMyLeadCompany,
} from '../../services/myLead';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import { MdOutlineMoreHoriz } from 'react-icons/md';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  EnvironmentOutlined,
  FlagOutlined,
  HeartOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  PhoneOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { getValue } from '@mui/system';
import {
  deleteEmailFromSequence,
  getListEmailFromSequence,
  updateMarkCompleted,
  updateSequence,
} from '../../services/search';
import SequenceTemplateForm from '../SequenceTemplate/SequenceTemplateForm';
import { initialSequenceTemplate } from '../SequenceTemplate/constant';
import { createSequenceTemplate } from '../../services/sequenceTemplate';
import { useViewAs } from '../../store/viewAs';

function SequenceItem(props) {
  const {
    tabDetail,
    setTabDetail,
    mailItem,
    setSelectedValue,
    setLoadingStatus,
    loadingStatus,
    handleDateSequence,
    handleSelectDelete,
    deleteSequenceList,
    setListDeletedId,
    listDeletedId,
    setSequenceChange,
    sequenceChange,
    setDataEmailSequence,
    dataEmailsSequence,
  } = props;
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [emailConfigData, setEmailConfigData] = useState();
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [openMailClone, setOpenMailClone] = useState(false);
  const [openMailEdit, setOpenMailEdit] = useState(false);
  const [loadingUpdateStatus, setLoadingUpdateStatus] = useState(false);
  const [loadingSaveAsASequence, setLoadingSaveAsASequence] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);

  // save as a template functions
  const [selectedSequenceTemplate, setSelectedSequenceTemplate] =
    useState(null);
  const [openTemplateForm, setOpenTemplateForm] = useState(false);

  const showTemplateModal = () => setOpenTemplateForm(true);
  const closeTemplateModal = () => setOpenTemplateForm(false);
  const handleStarToggle = async (sequenceId, isStared) => {
    try {
      setLoadingSaveAsASequence(true);
      const { data } = await updateSequence(sequenceId, {
        isStared: !isStared,
      });
      if (data) {
        const newEmailsSequence = [...dataEmailsSequence];
        const updatedItemIndex = newEmailsSequence.findIndex(
          (seq) => seq?.id === sequenceId
        );
        newEmailsSequence[updatedItemIndex] = {
          ...newEmailsSequence[updatedItemIndex],
          isStared: !isStared,
        };
        setDataEmailSequence([...newEmailsSequence]);
        setLoadingSaveAsASequence(false);
        notification.success({
          message: `${!isStared ? 'Add' : 'Remove'} star complete successfully`,
        });
      }
    } catch (e) {
      setLoadingSaveAsASequence(false);
      notification.error({ message: 'Something went wrong happened' });
    }
  };
  const handleMarkSequence = async (id) => {
    try {
      setLoadingSaveAsASequence(true);
      const { data } = await updateMarkCompleted({
        sequenceIds: [id],
      });
      if (data) {
        setLoadingSaveAsASequence(false);
        notification.success({
          message: 'Mark Complete successfully',
        });
        handleDateSequence();
      }
    } catch (e) {
      setLoadingSaveAsASequence(false);
      notification.error({
        message: 'Something went wrong happened',
      });
    }
  };

  const handleSaveAsTemplate = (template) => {
    setLoadingSaveAsASequence(true);
    closeTemplateModal();
    const creatingTemplate = {
      ...template,
      organizationId:
        userToSet?.user?.organizationId || userToSet?.organization?.id,
      modifiedBy: userToSet?.user?.id || userToSet?.id,
      createdBy: userToSet?.user?.id || userToSet?.id,
    };
    createSequenceTemplate(creatingTemplate)
      .then((res) => {
        if (res?.data?.result) {
          notification.success({
            message: 'Sucess',
            description: 'The template is added!',
          });
        }
      })
      // .catch((err) => {
      //   notification.error({
      //     message: 'Error',
      //     description: 'Please try again later',
      //   });
      // })
      .finally(() => {
        setLoadingSaveAsASequence(false);
      });
  };

  const handleGetListData = async () => {
    const { data } = await getListEmailFromSequence(mailItem?.id);
    const stepsTemp = data?.result?.mails;

    if (data?.result?.mails?.length == 0) {
      notification.error({ message: 'Cannot get data from sequence' });
    }
    setValue(
      `sendMail.mailStepParentMailTo`,
      data?.result?.mails?.[0]?.recipients ?? []
    );
    setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
    setValue(
      `sendMail.mailStepParentContent`,
      data?.result?.mails?.[0]?.content
    );
    setValue(
      `sendMail.mailStepParentSubject`,
      data?.result?.mails?.[0]?.subject
    );

    // In case open save as a template modal

    const contentTemp = {
      mailDefine: {},
      triggerItem: {
        rawSequence: [...stepsTemp],
      },
      rawSequence: [...stepsTemp],
    };

    const templateObjTemp = {
      name: data?.result?.mails?.[0]?.subject,
      organizationId: '',
      content:
        JSON.stringify(contentTemp) || '{"mailDefine":{},"triggerItem":{}}',
      modifiedBy: '',
      createdBy: '',
    };
    setSelectedSequenceTemplate({ ...templateObjTemp });
  };

  // useEffect(() => {
  //   if (selectedValue && tabDetail) {
  //     handleGetListData();
  //   }
  // }, [selectedValue, tabDetail]);

  const items = [
    // {
    //   label: 'Edit',
    //   key: 'edit',
    //   icon: <EditOutlined />,
    // },
    // {
    //   label: 'Clone',
    //   key: 'create_clone',
    //   icon: <CopyOutlined />,
    // },
    {
      label: 'Save as a template',
      key: 'save_as_a_template',
      icon: <CopyOutlined />,
    },
    {
      label: 'Mark as Completed',
      key: 'mask_completed',
      icon: <FlagOutlined />,
    },
    {
      label: 'Delete',
      key: 'delete',
      icon: <DeleteOutlined />,
    },
  ];

  const handleMenuClick = (e) => {
    const { key } = e;
    if (key === 'delete') {
      setOpenDeleteModal(true);
      return
    }
    setLoadingSaveAsASequence(true);
    handleGetListData()
      .then(() => {
        switch (key) {
          case 'edit':
            // handleOpenSequenceModal();
            setOpenMailEdit(true);
            break;

          case 'create_clone':
            // handleOpenTemplateModal();
            setOpenMailClone(true);
            break;
          case 'save_as_a_template':
            showTemplateModal();
            break;
          case 'mask_completed':
            handleMarkSequence(mailItem?.id);
            break;
          // case 'delete':
          //   setOpenDeleteModal(true);
          //   break;
        }
      })
      .finally(() => {
        setLoadingSaveAsASequence(false);
      });
  };

  const handleUpdateStatus = async () => {
    const foundItem = sequenceChange.find(
      (item) => item.seqId === mailItem?.id
    );
    const body = {
      status:
        (foundItem?.status || mailItem?.status) === 'LIVE' ? 'STOP' : 'LIVE',
    };
    try {
      setLoadingUpdateStatus(true);
      const { data } = await updateSequence(mailItem?.id, body);

      setSequenceChange((prevChanges) => {
        const existingChange = prevChanges.find(
          (change) => change?.seqId === mailItem?.id
        );

        if (existingChange) {
          return prevChanges.map((change) =>
            change.seqId === mailItem?.id
              ? { ...change, status: body.status }
              : change
          );
        } else {
          return [...prevChanges, { seqId: mailItem?.id, status: body.status }];
        }
      });
      notification.success({ message: 'Sequence updated successfully' });
      setLoadingUpdateStatus(false);
      // handleDateSequence()
    } catch (e) {
      notification.error({ message: 'Something went wrong happened' });
    }
  };

  const handleDeleteSequence = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await deleteEmailFromSequence(mailItem?.id);
      setListDeletedId((prev) => [...prev, mailItem?.id]);
      setOpenDeleteModal(false);
      notification.success({ message: 'Delete sequence Success' });
      setLoadingDelete(false);
      // handleDateSequence();
    } catch (e) {
      setOpenDeleteModal(false);
      notification.error({ message: 'Something went wrong happened' });
    }
  };

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };
  return (
    <>
      <div
      // onClick={() => {
      //   setTabDetail(true), setSelectedValue(mailItem);
      // }}
      // key={mailItem?.item}
      // style={{ marginTop: '5px', cursor: 'pointer' }}
      >
        {/* <div
          style={{
            display: 'flex',
            border: '1px solid #ccc',
            borderRadius: '5px',
            padding: '30px 10px',
            height: '50px',
            alignItems: 'center',
          }}
        > */}
        {/* <div>
            <Checkbox checked={deleteSequenceList?.includes(mailItem?.id)} onClick={(e) => {e.stopPropagation(), handleSelectDelete(mailItem?.id)}}/>
          </div>
          <div
            style={{
              marginLeft: '20px',
              width: '20%',
              borderRight: '0.5px solid #ccc',
            }}
          >
            <div
              onClick={() => {
                setTabDetail(true), setSelectedValue(mailItem);
              }}
              style={{
                fontWeight: '600',
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '250px',
              }}
            >
              {mailItem?.name}
            </div>
            <div>
              <span style={{ color: 'blue', fontSize: '12px' }}>
                {mailItem?.sender}
              </span>
              <span style={{ marginLeft: '20px', fontSize: '12px' }}>
                {mailItem?.stepCount} step
              </span>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              marginLeft: '10px',
              paddingRight: '10px',
              borderRight: '0.5px solid #ccc',
              width: '34%',
            }}
          >
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Active</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Paused</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Not Sent</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Bounced</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>
                Spam Blocked
              </div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>0</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Finished</div>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              marginLeft: '10px',
              paddingRight: '10px',
              borderRight: '0.5px solid #ccc',
              width: '27%',
            }}
          >
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>{mailItem?.pendingCount}</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Pending</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>{mailItem?.sentCount}</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Sent</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>{mailItem?.stopCount}</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Stop</div>
            </div>
            <div style={{ textAlign: 'center', width: '20%' }}>
              <div>{mailItem?.repliedCount}</div>
              <div style={{ color: '#ccc', fontSize: '10px' }}>Reply</div>
            </div>
          </div> */}

        <div className="flex gap-2 items-center justify-center">
          <div
            style={{ marginLeft: '10px' }}
            onClick={(e) => e.stopPropagation()}
          >
            <Switch
              defaultChecked={mailItem?.status === 'LIVE' ? true : false}
              onClick={(e) => handleUpdateStatus()}
              loading={loadingUpdateStatus}
            />
          </div>
          <div style={{ marginLeft: '20px' }}>
            <Rate
              value={mailItem?.isStared}
              onClick={(e) => {
                e.stopPropagation();
                handleStarToggle(mailItem?.id, mailItem?.isStared);
              }}
              count={1}
              disabled={loadingUpdateStatus}
            />
          </div>
          <div
            style={{ marginLeft: '20px' }}
            onClick={(e) => e.stopPropagation()}
          >
            <Dropdown
              rootClassName="font-Montserrat"
              menu={menuProps}
              disabled={loadingSaveAsASequence}
            >
              <Button>
                <Space>
                  {loadingSaveAsASequence ? (
                    <Spin />
                  ) : (
                    <MoreOutlined
                      style={{ fontSize: '20px', cursor: 'pointer' }}
                    />
                  )}
                </Space>
              </Button>
            </Dropdown>
          </div>
        </div>
        {/* <div style={{
            marginLeft: '20px',
            height: "60px",
            width: "10px",
            background: mailItem?.status === 'LIVE' ? "green" : (mailItem?.isMarkedAsCompleted ? "gray" :"red"),
            position: "absolute",
            right: "1px",
            borderTopRightRadius: "5px",
            borderBottomRightRadius: "5px"
            }}></div> */}
        {/* </div> */}
      </div>
      <div>
        {getValues('sendMail.mailStepParentContent') && (
          <Modal
            title="Create a new Clone Sequence"
            centered
            open={openMailClone}
            onOk={() => setOpenMailClone(false)}
            onCancel={() => setOpenMailClone(false)}
            width={700}
            footer={false}
          >
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openMailClone}
                setOpenSendEmail={setOpenMailClone}
                listAddContactSelected={emailConfigData?.[0]?.recipients}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                // job={getValues('leadSelected')}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromSequenseEmail={true}
                // seqId={mailItem?.id}
                // onlyEdit={true}
                // loadingDataEmail={loadingDataEmail}
              />
            </Form>
          </Modal>
        )}
      </div>
      <div>
        {getValues('sendMail.mailStepParentContent') && (
          <Modal
            title="Edit Sequence"
            centered
            open={openMailEdit}
            onOk={() => setOpenMailEdit(false)}
            onCancel={() => setOpenMailEdit(false)}
            width={860}
            footer={false}
          >
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openMailEdit}
                setOpenSendEmail={setOpenMailEdit}
                listAddContactSelected={emailConfigData?.[0]?.recipients}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                // job={getValues('leadSelected')}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromSequenseEmail={true}
                onlyEdit={true}
                seqId={mailItem?.id}
                // loadingDataEmail={loadingDataEmail}
              />
            </Form>
          </Modal>
        )}
      </div>
      <div>
        <Modal
          title="Confirm Delete"
          open={openDeleteModal}
          // onOk={(e) => {e.stopPropagation(), handleDeleteSequence()}}
          // onCancel={() => {
          //   setOpenDeleteModal(false);
          // }}
          footer={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div></div>
              <div style={{ display: 'flex' }}>
                <Button onClick={() => setOpenDeleteModal(false)}>
                  Cancel
                </Button>
                <Button
                  loading={loadingDelete}
                  onClick={(e) => {
                    e.stopPropagation(), handleDeleteSequence();
                  }}
                  type="primary"
                >
                  Delete
                </Button>
              </div>
            </div>
          }
        >
          Do you want to delete this sequence?
        </Modal>
      </div>

      {openTemplateForm && (
        <SequenceTemplateForm
          handleCancel={closeTemplateModal}
          handleOk={handleSaveAsTemplate}
          open={openTemplateForm}
          sequenceTemplate={selectedSequenceTemplate}
        />
      )}
    </>
  );
}

export default SequenceItem;
