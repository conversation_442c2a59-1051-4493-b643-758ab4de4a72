import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Radio,
  Rate,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  message,
  notification,
} from 'antd';
import {
  EnvironmentOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import zileoIcon from '../../assets/img/zileoIcon.png';
import SequenseEmail from '../SequenseEmail';
import { useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { getDetailContactList, getNewContactList } from '../../services/contactList';

function ModalCreateFromSaveList() {
  const [open, setOpen] = useState(false);

  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [dataListTab, setDataListTab] = useState();
  const [messageApi, contextHolder] = message.useMessage();
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [existingContacts, setExistingContacts] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState(null);
  const [loadingContact, setLoadingContact] = useState(false);
  const [loadingDataEmail, setLoadingDataEmail] = useState(false);
  const [listSelect, setListSelect] = useState()

  const handleGetData = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    const { data } = await getNewContactList();
    setDataListTab(data?.result);
    messageApi.destroy();
  };

  useEffect(() => {
    if (open) {
      handleGetData();
    }
  }, [open]);

  const onChange = (e) => {
    setListSelect(e.target.value)
  };

  const handleChoseList =async () => {
    if(!listSelect) {
      notification.error({
        message: "Please select a list"
      })
      return
    }
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    const { data } = await getDetailContactList(listSelect);
    setValue(
      `sendMail.mailStepParentMailTo`,
      data?.result ?? []
    );
    setExistingContacts(data?.result)
    setOpenSendEmail(true);
    messageApi.destroy();
  }

  return (
    <>
      {contextHolder}
      <div
        onClick={() => setOpen(true)}
        style={{
          width: '30%',
          border: '1px solid #ccc',
          padding: '10px',
          cursor: 'pointer',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Image
            style={{ width: '83px', height: '72px' }}
            preview={false}
            src={zileoIcon}
          />
        </div>
        <div
          style={{
            textAlign: 'center',
            marginTop: '20px',
            fontSize: '20px',
            fontWeight: '600',
          }}
        >
          Zileo contact saved list
        </div>
        <div style={{ textAlign: 'center', marginTop: '10px' }}>
          Create a new sequence from Zileo contact saved list.
        </div>
      </div>
      <Modal
        title="Create a new Sequence From Zileo contact saved list."
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        footer={false}
        width={550}
      >
        <div>Select a Zileo contact saved list</div>
        <div style={{ marginTop: '10px' }}>
          <Radio.Group onChange={onChange}>
            <Space direction="vertical">
              {dataListTab?.map((item) => (
                <Radio style={{width: "500px"}} value={item?.id}>
                  <div style={{
                     padding: "10px",
                     width: "450px",
                     boxShadow: '5px 8px 24px 5px #ccc',
                     borderRadius: "7px"
                  }}>
                     <div style={{fontSize: "15px", fontWeight: "700"}}>{item?.name}</div>
                     <div><span>User in list: </span>{item?.contactCount}</div>
                  </div>
                </Radio>
              ))}
            </Space>
          </Radio.Group>
          <div style={{marginTop: "30px", float: "right"}}>
            <Button onClick={() => setOpen(false)}>Cancel</Button>
            <Button type="primary" style={{marginLeft: "10px" }} onClick={handleChoseList}>Choose</Button>
          </div>
          <div style={{clear: "both"}}></div>
        </div>
        {!loadingContact && !loadingDataEmail && openModalSendEmail && (
          <Modal
            width={800}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>New Email</div>
              </div>
            }
            open={openModalSendEmail}
            onCancel={() => {
              setValue('sendMail.content', null);
              setValue('sendMail.subject', null);
              setOpenSendEmail(false);
            }}
            footer={false}
          >
            <div>
              <Form layout="vertical">
                <BullhornSendEmail
                  watch={watch}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  sendToEmail={getValues()?.email}
                  mailTitle={getValues()?.jobtitle}
                  openModalSendEmail={openModalSendEmail}
                  setOpenSendEmail={setOpenSendEmail}
                  listAddContactSelected={existingContacts}
                  setNumberStep={setNumberStep}
                  numberStep={numberStep}
                  inputNumberStep={inputNumberStep}
                  setInputNumberStep={setInputNumberStep}
                  job={getValues('leadSelected')}
                  setEmailConfigData={setEmailConfigData}
                  emailConfigData={emailConfigData}
                  fromSequenseEmail={true}
                  loadingDataEmail={loadingDataEmail}
                  isFromVacancy={true}
                />
              </Form>
            </div>
          </Modal>
        )}
      </Modal>
    </>
  );
}

export default ModalCreateFromSaveList;
