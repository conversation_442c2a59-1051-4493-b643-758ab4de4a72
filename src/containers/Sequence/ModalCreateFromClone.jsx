import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Pagination,
  Radio,
  Rate,
  Row,
  Select,
  Spin,
  Switch,
  notification,
} from 'antd';
import _debounce from 'lodash/debounce';
import Email3Logo from '../../assets/img/rocket.png';
import Search from 'antd/es/input/Search';
import { getAllEmails, getListEmailFromSequence } from '../../services/search';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
// import selectStepIllustration from '../../assets/img/select-step.png';
import { ArrowRightOutlined, LeftOutlined } from '@ant-design/icons';
import { SEQUENCE_GUIDE, StyledSteps } from './ModalCreateFromCandidate';

function ModalCreateFromVClone({
  closeCreateSequenceModal,
  isShowModal = false,
  backTo = () => {},
}) {
  const [open, setOpen] = useState(false);
  const { handleSubmit, control, getValues, setValue, watch, reset } =
    useForm();

  const [tabDetail, setTabDetail] = useState(false);
  const [dataEmailsSequence, setDataEmailSequence] = useState([]);
  const [dataListSequence, setDataListEmailSequence] = useState([]);
  const [chooseItem, setChooseItem] = useState(null);
  const [tabState, setTabState] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [existingContacts, setExistingContacts] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [pending, setPending] = useState(false);

  const handleDateSequence = async (status = null, page = 1, limit = 10) => {
    setPending(true);
    const { data } = await getAllEmails(
      status,
      page,
      limit,
      getValues('searchText')
    );
    setDataEmailSequence(data?.result?.mails);
    setCountPage(data?.result?.count);
    setPending(false);
  };

  const handleGetListData = async () => {
    const { data } = await getListEmailFromSequence(chooseItem);
    const newValueArr = data?.result?.mails?.map((item, index) =>
      index === data?.result?.mails.length - 1
        ? { ...item, delay: index + 1 }
        : item
    );
    const newData = newValueArr?.slice(1).map((item, index) => {
      return {
        delay: item.delay,
        subject: item.subject,
        content: item.content,
        key: index + 1,
        status: item?.status,
      };
    });
    setInputNumberStep(newData);
    setDataListEmailSequence(data?.result?.mails);
    setEmailConfigData(data?.result?.mails);
    setValue(
      `sendMail.mailStepParentMailTo`,
      data?.result?.mails?.[0]?.recipients ?? []
    );
    setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
    setValue(
      `sendMail.mailStepParentContent`,
      data?.result?.mails?.[0]?.content
    );
    setValue(
      `sendMail.mailStepParentSubject`,
      data?.result?.mails?.[0]?.subject
    );
    setNumberStep(newData?.length);
  };

  useEffect(() => {
    handleDateSequence();
  }, []);

  const handlePagination = (page) => {
    setPage(page);
    handleDateSequence(null, page, 10);
  };

  const handleOke = () => {
    if (!chooseItem) {
      notification.error({ message: 'Please choose a Sequence' });
      return;
    }
    setTabState(true);
  };

  useEffect(() => {
    if (chooseItem && tabState) {
      handleGetListData();
    }
  }, [chooseItem, tabState]);

  useEffect(() => {
    if (!open) {
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      setTabState(false);
      setChooseItem(null);
    }
  }, [open]);

  return (
    <>
      {!isShowModal && (
        <div
          // onClick={() => setOpen(true)}
          className="w-full flex items-center justify-start gap-3 p-1"
        >
          <Image style={{ height: '43px' }} src={Email3Logo} preview={false} />
          <div>
            <div className="font-semibold ">Start from scratch</div>
            <div className="font-medium text-xs opacity-70">
              Start from scratch build the perfect sequence.
            </div>
          </div>
        </div>
      )}
      {isShowModal && (
        <div className="sequence-background-container py-3 px-6 bg-white rounded-md shadow-md overflow-hidden">
          <div className="flex items-center justify-between pb-2 border-b">
            <div>
              <Button
                onClick={backTo}
                className="bg-white"
                icon={<LeftOutlined />}
              >
                Back
              </Button>
            </div>
            <div className="flex flex-col items-center gap-1 justify-center text-cyan-600">
              <div className="text-base font-semibold">Start From Scratch</div>
              <div className="text-xs opacity-80">
                Start by defining your sequence goals (e.g., nurture leads on
                linkedin, or engage candidates).
              </div>
            </div>
            <div />
          </div>
          <div className="grid grid-cols-10 min-h-[38rem]">
            <div className="w-full col-span-6 flex flex-col gap-2 justify-center items-center h-full">
              <div className="max-w-md flex-wrap opacity-60">
                <StyledSteps
                  progressDot
                  current={SEQUENCE_GUIDE?.length}
                  direction="vertical"
                  items={SEQUENCE_GUIDE.map((item) => ({
                    title: item,
                  }))}
                  className="!text-xs"
                />
              </div>
            </div>
            <div className="col-span-4 w-full">
              <Form
                layout="vertical"
                className="start-from-sratch-container py-5 flex justify-center items-center w-full"
              >
                <BullhornSendEmail
                  watch={watch}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  sendToEmail={getValues()?.email}
                  mailTitle={getValues()?.jobtitle}
                  listAddContactSelected={
                    emailConfigData?.[0]?.recipients ?? []
                  }
                  setNumberStep={setNumberStep}
                  numberStep={numberStep}
                  inputNumberStep={inputNumberStep}
                  setInputNumberStep={setInputNumberStep}
                  setEmailConfigData={setEmailConfigData}
                  fromSequenseEmail={true}
                  fromCreatingScratchSequence={true}
                  newUpdatedSequence={false}
                  closeCreateSequenceModal={() => {
                    setOpen(false);
                    closeCreateSequenceModal();
                  }}
                />
              </Form>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default ModalCreateFromVClone;
