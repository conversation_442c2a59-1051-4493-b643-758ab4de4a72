import React, { useEffect, useState } from 'react';
import {
  Button,
  Image,
  Modal,
  notification,
  Spin,
  Input,
  Table,
  Pagination,
  Space,
  Form,
  Tag,
  Drawer,
  Segmented,
} from 'antd';
import {
  ArrowLeftOutlined,
  BranchesOutlined,
  EyeOutlined,
  FieldTimeOutlined,
  LeftOutlined,
  LinkedinOutlined,
  MailOutlined,
  SnippetsOutlined,
  UserOutlined,
  SearchOutlined,
  CalendarOutlined,
  FundViewOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import Email2Logo from '../../assets/img/template.png';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import {
  getSequenceTemplates,
  getSingleSequenceTemplate,
} from '../../services/sequenceTemplate';
import moment from 'moment';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData, searchBullhorn } from '../../services/bullhorn';
import { v4 as uuid } from 'uuid';
import Search from 'antd/es/input/Search';
import clsx from 'clsx';
import rocketImage from '../../assets/img/pricing-plan-enterprise.png';
import { Spinner } from '@material-tailwind/react';
import { getSignatureDefault } from '../../services/signatures';
import {
  ADD_STEP_TYPE,
  replaceImagePlaceholders,
} from '../../components/BullHorn/EmailtriggerStep';
import { replaceSignaturePlaceholder } from '../../utils/common';
// import selectStepIllustration from '../../assets/img/select-step.png';
import { ArrowRightOutlined } from '@ant-design/icons';
import handleRenderTime from '../../function/handleRenderTime';
import { useViewAs } from '../../store/viewAs';
import { SEQUENCE_GUIDE, StyledSteps } from './ModalCreateFromCandidate';
import { QUERY_TEMPLATE_TYPES } from '../SequenceTemplate';

export const getIconAvtTemplate = {
  EMAIL: <MailOutlined className="text-3xl font-extrabold text-[#5a6d83] " />,
  ADD_WAIT: (
    <FieldTimeOutlined className="text-3xl font-extrabold text-[#5a6d83]" />
  ),
  // ADD_TASK: 'ADD_TASK',
  ADD_NOTE: (
    <SnippetsOutlined className="text-3xl font-extrabold text-[#5a6d83]" />
  ),
  // DUPLICATE_MAIL: 'DUPLICATE_MAIL',
  LINKEDIN_CONNECTION_REQUEST: (
    <LinkedinOutlined className="text-3xl font-extrabold text-[#5a6d83]" />
  ),
};

const ModalCreateFromTemplate = ({
  fromBullhornSendEmailModal = false,
  onChooseTemplate = null,
  handleCloseTemplateOptions = null,
  closeCreateSequenceModal = null,
}) => {
  const { handleSubmit, control, getValues, setValue, watch, reset } =
    useForm();

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [data, setData] = useState([]);
  const [rawData, setRawData] = useState([]);
  const [chooseItem, setChooseItem] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [tabState, setTabState] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [existingContacts, setExistingContacts] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [listAddContactSelected, setlistAddContactSelected] = useState([]);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [limit, setLimit] = useState(11);
  const [searchText, setSearchText] = useState('');
  const [type, setType] = useState(QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE);
  const [loadingButton, setLoadingButton] = useState(false);
  const [duration, setDuration] = useState({
    days: 0,
    hours: 0,
  });

  const [isInputSearchVisible, setIsInputSearchVisible] = useState(true);
  const [loadingContent, setLoadingContent] = useState(false);
  const [dataSignature, setDataSignature] = useState('');
  const [loadingSignature, setLoadingSignature] = useState(true);
  const handlePagination = (page) => {
    setPage(page);
  };

  const { profile: profileUserAuth } = useAuth();
  const { profileUser } = useViewAs();

  const userToSet = profileUser || profileUserAuth;
  const userToSetId = userToSet?.user?.id || userToSet?.id;

  const isLinkedInStepBlocked =
    (userToSet &&
      !userToSet?.user?.unipileAccountId &&
      !userToSet?.unipileAccountId) ||
    userToSet?.user?.unipileAccountStatus === 'DISCONNECTED' ||
    userToSet?.unipileAccountStatus === 'DISCONNECTED';

  const handleSearch = async () => {
    await getData(setSearchLoading);
  };

  const handleOke = async (record = null) => {
    console.log('chooseItem: ', chooseItem);
    setLoadingButton(true);
    if ((!chooseItem || chooseItem.length === 0) && !record) {
      notification.error({ message: 'Please choose a Template' });
      return;
    }
    const templateId = record?.id || chooseItem[0]?.id;
    if (fromBullhornSendEmailModal && onChooseTemplate) {
      getSingleSequenceTemplate(templateId)
        .then(async (res) => {
          // Set content to chooseItem[0]

          const itemTemp = { ...chooseItem[0] };
          itemTemp.content = res?.data?.result?.content;
          await handleBindingData(itemTemp);
          setLoadingButton(false);
        })
        .catch((err) => {
          console.log('getSingleSequenceTemplate err: ', err);
          notification.error({
            message: 'Error',
            description: 'Please try again later',
          });
          setLoadingButton(false);
        });
    } else {
      try {
        const { data } = await getSingleSequenceTemplate(templateId);
        console.log(data?.result?.content);
        const currentRecord = { ...record };
        currentRecord.content = data?.result?.content;
        await handleBindingData(currentRecord);

        setIsInputSearchVisible(false);
        setLoadingButton(false);
      } catch (e) {
        setLoadingButton(false);
        console.error('err: ', err);
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      }
    }
  };

  const handleCloseModal = () => setOpen(false);

  const handleSearchAll = (value) => {
    setSearchText(value);
    getData();
    // const currentData = [...rawData];
    // const filteredData = currentData.filter((item) =>
    //   item?.name?.includes(value)
    // );
    // setData([...filteredData]);
  };

  const getData = async (setLoadingFunc = setLoading, submitType) => {
    setLoadingFunc(true);
    const viewAsParams = {
      viewAs: getUserViewAs(),
      page: page || 1,
      limit: 5,
      search: searchText || '',
      type: submitType || type,
    };
    getSequenceTemplates(viewAsParams)
      .then((res) => {
        if (res?.data?.result) {
          setCountPage(res?.data?.result?.count);
          const dataTemp = [...res?.data?.result?.items].map((item) => ({
            ...item,
            key: item.id,
          }));
          // .splice(0, 5);
          setData(dataTemp);
          setRawData(dataTemp);
        }
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        setLoadingFunc(false);
      });
  };

  useEffect(() => {
    getData();
  }, [page]);

  const columns = [
    {
      title: 'Infor',
      dataIndex: 'onfor',
      width: '90%',
      render: (_, record) => {
        const { totalOpened, totalSent, totalReply } = record;
        const percentageOpened = totalSent
          ? Number((totalOpened / totalSent) * 100)
          : 0;
        const percentageReply = totalSent
          ? Number((totalReply / totalSent) * 100)
          : 0;
        return (
          <div className="flex flex-col gap-1">
            <div className="flex justify-between">
              <div
                className="flex items-center gap-2 line-clamp-1 max-w-md"
                title={record?.name}
              >
                <span className="text-base font-semibold">{record?.name}</span>
              </div>
              <span className="text-[#9ca3af] flex items-center gap-1">
                <CalendarOutlined className="text-base" />
                <span className="text-xs font-medium">
                  {handleRenderTime(record?.updatedAt)}
                </span>
              </span>
            </div>
            <div className="flex items-center gap-4 pt-1">
              {type === QUERY_TEMPLATE_TYPES.MY_TEMPLATE && (
                <Tag icon={<UserOutlined />} color="cyan">
                  {record?.createdBy === userToSetId
                    ? 'Updated by you'
                    : record?.modifiedFullName}
                </Tag>
              )}
              {type === QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE && (
                <Tag icon={<UserOutlined />} color="cyan">
                  {`Created by ${record?.createdFullName}`}
                </Tag>
              )}

              <Tag icon={<FundViewOutlined />} color="blue">
                {`Opened: ${isNaN(percentageOpened) ? 0 : percentageOpened.toFixed(0)}%`}
              </Tag>
              <Tag icon={<MessageOutlined />} color="geekblue">
                {`Response: ${isNaN(percentageReply) ? 0 : percentageReply.toFixed(0)}%`}
              </Tag>
              {/* {record?.createdBy === userToSetId && (
                <Tag color="cyan" icon={<UserOutlined />}>
                  Created by you
                </Tag>
              )} */}
            </div>
          </div>
        );
      },
    },
    // {
    //   title: 'Preview',
    //   dataIndex: 'preview',
    //   width: '10%',
    //   render: (_, record) => (
    //     <Space size="middle">
    //       <Button
    //         title="Preview Template"
    //         shape="circle"
    //         icon={<EyeOutlined />}
    //         //   onClick={() => onPreview(record)}
    //       />
    //     </Space>
    //   ),
    // },
  ];

  const rowSelection = {
    onChange: (_selectedRowKeys, selectedRows) => {
      setChooseItem(selectedRows);
    },
    selectedRowKeys: [chooseItem?.[0]?.id],
    getCheckboxProps: (record) => ({
      disabled: record?.name === 'Disabled User',
      // Column configuration not to be checked
      name: record?.name,
    }),
  };

  const handleBindingData = async (record = null) => {
    const parsedContent = JSON.parse(record?.content || chooseItem[0]?.content);

    if (parsedContent?.triggerItem) {
      const stepsTemp = parsedContent?.triggerItem?.rawSequence;
      let totalHours = 0;
      let totalDays = 0;

      const inputNumberStepTemp = stepsTemp
        ?.map((step) => [
          step?.delays && [
            ...step?.delays?.flatMap((delayItem, index) => {
              if (delayItem?.unit === 'HOUR') {
                const { delay } = delayItem;
                totalHours += parseInt(delay);
              } else {
                const { delay } = delayItem;
                totalDays += parseInt(delay);
              }
              return {
                ...delayItem,
                unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
                key: uuid(),
                // type: ADD_STEP_TYPE.ADD_WAIT
              };
            }),
          ],
          {
            ...step,
            id: uuid(),
            subject: step?.subject,
            content: step?.content,
            type: step?.type || '',
            key: uuid(),
            rawKey: step?.key,
            rawId: step?.id,
          },
        ])
        .flat(Infinity)
        .filter((item) => item?.key);

      const updatedThreadingInputNumberStep = inputNumberStepTemp?.map(
        (item) => {
          const contentWithSig =
            item?.type === ADD_STEP_TYPE.SEND_MAIL
              ? replaceSignaturePlaceholder(item?.content, dataSignature)
              : item?.content;
          if (!item?.threadId) return { ...item, content: contentWithSig };

          const mainEmail = inputNumberStepTemp.find(
            (main) =>
              main?.rawId === item?.threadId || main?.rawKey === item?.threadId
          );
          const newThreadingId = mainEmail?.id;
          return {
            ...item,
            threadId: newThreadingId,
            content: contentWithSig,
          };
        }
      );

      const newTriggerItem = {
        rawSequence: [...updatedThreadingInputNumberStep],
      };

      const isHaveLinkedinStep = updatedThreadingInputNumberStep?.some(
        (step) => step?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST
      );
      if (isHaveLinkedinStep && isLinkedInStepBlocked) {
        notification.warning({
          description: 'Please link your Linkedin Account first!',
        });
        return;
      }
      if (onChooseTemplate) {
        onChooseTemplate(record);
        handleCloseTemplateOptions();
        setOpen(false);
        return;
      }
      setValue('triggerItem', newTriggerItem);
      if (!record) return;
      setChooseItem([record]);
    }
  };
  const handleGetSignature = async () => {
    try {
      const { data } = await getSignatureDefault();
      if (data) {
        // const signatureEl = `<span class="signature-container"><span>${data?.result?.sigContent}</span></span>`;
        const signatureEl = data?.result?.sigContent;
        setDataSignature(signatureEl ?? '');
        setLoadingSignature(false);
      }
    } catch (error) {
      console.log('newUrl error: ', error);
    }
  };

  useEffect(() => {
    handleGetSignature();
  }, []);

  useEffect(() => {
    if (!open) {
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      setTabState(false);
      setChooseItem(null);
    }
  }, [open]);

  useEffect(() => {
    setOpen(fromBullhornSendEmailModal);
  }, [fromBullhornSendEmailModal]);

  return (
    <>
      {!fromBullhornSendEmailModal && (
        <div
          onClick={() => setOpen(true)}
          className="w-full flex items-center justify-start gap-3 p-1"
        >
          <Image style={{ height: '43px' }} src={Email2Logo} preview={false} />
          <div>
            <div className="font-semibold ">Template</div>
            <div className="font-medium text-xs opacity-70">
              Start with one of our sequence templates.
            </div>
          </div>
        </div>
      )}
      {open && (
        <Drawer
          maskClosable={false}
          className="create-sequence-detail sequence-template-container"
          title={
            fromBullhornSendEmailModal && onChooseTemplate
              ? 'Select a Template to Continue'
              : ''
          }
          centered
          open={open}
          // show search input
          footer={
            !tabState && fromBullhornSendEmailModal ? (
              <>
                <div>
                  <Button
                    disabled={!chooseItem && true}
                    type="primary"
                    onClick={() => handleOke()}
                    loading={loadingButton}
                  >
                    Choose
                  </Button>
                </div>
              </>
            ) : (
              false
            )
          }
          // onOk={() => handleOke()}
          onClose={() => {
            if (fromBullhornSendEmailModal) {
              handleCloseTemplateOptions();
            }
            setOpen(false);
            setIsInputSearchVisible(true);
            reset();
          }}
          getContainer={false}
          width={'50%'}
        >
          {loading && (
            <div className="w-full flex justify-center h-96 items-center">
              <Spin size="large" />
            </div>
          )}
          {!loading &&
            (fromBullhornSendEmailModal ? (
              <>
                <div className="flex items-center justify-between">
                  {isInputSearchVisible && (
                    <Input.Search
                      placeholder="Search..."
                      value={searchText}
                      // set hidden search input
                      onChange={(text) => setSearchText(text.target.value)}
                      onSearch={() => {
                        setPage(1);
                        handleSearch();
                      }}
                      loading={searchLoading}
                      style={{ marginBottom: '10px' }}
                      className="mt-5 max-w-[20rem]"
                    />
                  )}
                  <Segmented
                    disabled={loading}
                    className="customized-segmented-sequence-template"
                    value={type}
                    onChange={(value) => {
                      setType(value);
                      setPage(1);
                      getData(setLoading, value);
                    }}
                    options={[
                      {
                        label: 'My Templates',
                        value: QUERY_TEMPLATE_TYPES.MY_TEMPLATE,
                      },
                      {
                        label: 'Company Templates',
                        value: QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE,
                      },
                    ]}
                  />
                </div>
                <div className="pt-3 hide-selection-container">
                  <Table
                    className="customized-style-pagination w-full"
                    columns={columns}
                    dataSource={data}
                    showHeader={false}
                    rowSelection={{
                      type: 'radio',
                      ...rowSelection,
                    }}
                    rowKey={(record) => record.id}
                    pagination={{
                      current: page,
                      showSizeChanger: false,
                      onChange: handlePagination,
                      defaultPageSize: 5,
                      total: countPage,
                    }}
                    onRow={(record, rowIndex) => {
                      return {
                        onClick: () => {
                          setChooseItem([record]);
                        },
                        style: { cursor: 'pointer' },
                      };
                    }}
                  />
                </div>
              </>
            ) : (
              <div className="grid grid-cols-10 gap-3">
                {!(chooseItem?.length > 0) && (
                  <div className="col-span-5 flex flex-col gap-4 bg-white p-5 rounded-lg">
                    <div className="text-2xl font-semibold">Template List</div>
                    <Search
                      className="customize-search-container pb-3"
                      allowClear
                      placeholder="Search your template..."
                      enterButton="Search"
                      size="large"
                      loading={searchLoading}
                      onChange={(text) => setSearchText(text.target.value)}
                      value={searchText}
                      onSearch={handleSearchAll}
                      onKeyPress={(event) => {
                        if (event.key === 'Enter') {
                          handleSearch(searchText);
                        }
                      }}
                    />
                    <div className="text-lg font-semibold flex items-center justify-between">
                      <span>Start with a pre-made sequence</span>
                      {(loadingButton || loadingSignature) && (
                        <div>
                          <Spin />
                        </div>
                      )}
                    </div>
                    <div className="max-h-[33rem] overflow-y-auto flex flex-col gap-3 pr-3 cursor-pointer">
                      {data?.map((record, index) => {
                        const { totalOpened, totalSent, totalReply } = record;
                        const percentageOpened = totalSent
                          ? Number((totalOpened / totalSent) * 100)
                          : 0;
                        const percentageReply = totalSent
                          ? Number((totalReply / totalSent) * 100)
                          : 0;

                        const sequenceItemData = JSON.parse(
                          record?.content ||
                            '{"triggerItem":{"rawSequence":{}}}'
                        );

                        const { rawSequence } = sequenceItemData?.triggerItem;

                        return (
                          <div className="flex flex-col justify-center">
                            <div
                              onClick={() => {
                                handleOke(record);
                                // setChooseItem([record]);
                              }}
                              className={clsx(
                                'grid grid-cols-10 gap-3 p-4 border-2 rounded-md hover:border-blue-400 min-h-[9rem]',
                                chooseItem?.length > 0 &&
                                  chooseItem[0]?.id === record?.id &&
                                  'border-cyan-500'
                              )}
                            >
                              <div className="col-span-3 p-6 bg-[#f1f5f8] rounded-lg flex items-center justify-center">
                                {getIconAvtTemplate[rawSequence[0]?.type] || (
                                  <BranchesOutlined className="text-3xl font-extrabold text-[#5a6d83]" />
                                )}
                              </div>
                              <div className="col-span-7 py-3">
                                <div
                                  className="text-lg font-semibold line-clamp-1 mb-3"
                                  title={record?.name}
                                >
                                  {record?.name}
                                </div>
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <UserOutlined />
                                    <span className="text-xs italic text-gray-800 font-medium">
                                      Updated By {record?.modifiedFullName}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div className="text-xs italic text-gray-800 font-medium">
                                      Opened:{' '}
                                      {`${isNaN(percentageOpened) ? 0 : percentageOpened.toFixed(0)}%`}
                                    </div>
                                    <div className="text-xs italic text-gray-800 font-medium">
                                      Response:{' '}
                                      {`${isNaN(percentageReply) ? 0 : percentageReply.toFixed(0)}%`}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            {/* {index === data?.length - 1 && visibleLoadMore && (
                              <div>
                                <Button icon={<ExpandOutlined />}>
                                  Load More
                                </Button>
                              </div>
                            )} */}
                          </div>
                        );
                      })}
                    </div>
                    <div style={{ marginTop: '30px' }}>
                      <Pagination
                        defaultCurrent={page}
                        defaultPageSize={10}
                        total={countPage}
                        showSizeChanger={false}
                        onChange={handlePagination}
                      />
                    </div>
                  </div>
                )}
                <div
                  className={clsx(
                    ' bg-[#f1f5f8] pt-5',
                    chooseItem?.length > 0 ? 'col-span-10' : 'col-span-5 px-10'
                  )}
                >
                  <div className="flex items-center pb-3 justify-between">
                    {/* <div className="text-2xl font-semibold">Preview</div> */}
                    {chooseItem?.length > 0 && (
                      <Button
                        type="primary"
                        onClick={(e) => setChooseItem([])}
                        className="w-fit bg-white border"
                      >
                        <div
                          className="flex items-center gap-2 bg-white"
                          icon={<LeftOutlined />}
                        >
                          Back to Template List
                        </div>
                      </Button>
                    )}
                  </div>
                  {/* {chooseItem?.length > 0 && (
                    <div className="font-medium text-gray-700 pb-3">
                      <span>
                        {getValues('triggerItem.rawSequence')?.length} steps
                      </span>
                      <span
                        className={clsx(
                          `label-dot mx-2 text-gray-600 font-bold`
                        )}
                      >
                        -
                      </span>
                      <span>
                        {duration?.days} day(s) and {duration?.hours} hour(s) to
                        complete
                      </span>
                    </div>
                  )} */}
                  {chooseItem?.length > 0 ? (
                    <div className="grid grid-cols-10">
                      <div className="w-full col-span-6 flex flex-col gap-2 justify-center items-center h-full">
                        <div className="max-w-md flex-wrap opacity-60">
                          <StyledSteps
                            progressDot
                            current={SEQUENCE_GUIDE?.length}
                            direction="vertical"
                            items={SEQUENCE_GUIDE.map((item) => ({
                              title: item,
                            }))}
                            className="!text-xs"
                          />
                        </div>
                      </div>
                      <div className="col-span-4 w-full">
                        <Form
                          layout="vertical"
                          className="py-5 flex justify-center items-center w-full"
                        >
                          <BullhornSendEmail
                            watch={watch}
                            control={control}
                            setValue={setValue}
                            getValues={getValues}
                            sendToEmail={getValues()?.email}
                            mailTitle={getValues()?.jobtitle}
                            openModalSendEmail={tabState}
                            setOpenSendEmail={setTabState}
                            listAddContactSelected={listAddContactSelected}
                            setNumberStep={setNumberStep}
                            numberStep={numberStep}
                            inputNumberStep={inputNumberStep}
                            setInputNumberStep={setInputNumberStep}
                            setEmailConfigData={setEmailConfigData}
                            emailConfigData={emailConfigData}
                            fromSequenseEmail={true}
                            newUpdatedSequence={true}
                            fromCreateFromTemplateModal={true}
                            closeAllModal={() => {}}
                            closeCreateSequenceModal={() => {
                              setOpen(false);
                              closeCreateSequenceModal();
                            }}
                          />
                        </Form>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center gap-4 w-full h-2/3 justify-center text-xl font-semibold italic">
                      <Image
                        preview={false}
                        src={rocketImage}
                        height={'20rem'}
                      />
                      <div className="flex gap-2 items-center">
                        <ArrowLeftOutlined />
                        <span>Select a template to start</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
        </Drawer>
      )}
    </>
  );
};

export default ModalCreateFromTemplate;
