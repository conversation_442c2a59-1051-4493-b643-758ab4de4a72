import { useState } from 'react';
import { getNewContactList } from '../../services/contactList';
import { Button, Table, Tooltip } from 'antd';
import { useEffect } from 'react';
import { COMMON_STRINGS } from '../../constants/common.constant';
import { CloseCircleOutlined } from '@ant-design/icons';

const columns = [
  {
    title: COMMON_STRINGS.NAME,
    key: 'name',
    dataIndex: 'name',
    // align: 'center',
    width: '70%',
    render: (text, record) => (
      <div className="flex gap-4">
        <div className="w-full">
          <div
            className="text-sm font-semibold line-clamp-1"
            title={record?.name}
          >
            {record?.name}
          </div>
        </div>
      </div>
    ),
  },
  {
    title: 'Count',
    key: 'contactCount',
    dataIndex: 'contactCount',
    align: 'center',
    render: (contactCount) => {
      return (
        <div className="w-full flex items-center justify-center font-medium">
          {contactCount || 0}
        </div>
      );
    },
  },
];

const ContactListModal = ({
  submitList,
  contactList,
  setParticipantActiveKey,
  disabled
}) => {
  const [dataListTab, setDataListTab] = useState([]);
  const [selectedContactList, setSelectedContactList] = useState([]);
  const [loading, setLoading] = useState(true);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedContactList(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedContactList,
    onChange: onSelectChange,
    type: 'checkbox',
  };

  const handleGetData = async () => {
    setLoading(true);
    try {
      const { data: dataListTemp } = await getNewContactList();

      setDataListTab(dataListTemp?.result);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGetData();
  }, []);

  useEffect(() => {
    if (contactList?.length > 0) {
      const selectedRowKeysTemp = contactList?.map((list) => list?.id || list);
      setSelectedContactList([...selectedRowKeysTemp]);
    }
  }, [contactList]);

  return (
    <div>
      <div className="search-table-new-design-container mt-5">
        <Table
          size="small"
          rowSelection={rowSelection}
          rowKey={(record) => record?.id}
          loading={loading}
          dataSource={dataListTab}
          pagination={{
            pageSize: 5,
          }}
          columns={columns}
          className="custom-table"
          onRow={(record, rowIndex) => {
            return {
              onClick: () => {
                // navigate(`/contact_list/${record.id}`);
                const currSelectedContactList = [...selectedContactList];
                if (currSelectedContactList?.includes(record?.id)) {
                  const removedContactList = currSelectedContactList?.filter(
                    (listId) => listId !== record?.id
                  );
                  onSelectChange([...removedContactList]);
                } else {
                  onSelectChange([...selectedContactList, record.id]);
                }
              },
              style: { cursor: 'pointer' },
            };
          }}
        />
      </div>
      <div className="flex justify-end gap-1 pt-5">
        {selectedContactList?.length > 0 && (
          <Tooltip title={'Remove selection.'}>
            <Button
              type="text"
              onClick={() => setSelectedContactList([])}
              icon={<CloseCircleOutlined />}
            />
          </Tooltip>
        )}

        <Button
          disabled={loading || disabled}
          type="primary"
          onClick={() => {
            const listContactList = dataListTab?.filter((list) =>
              selectedContactList?.includes(list?.id)
            );
            submitList(listContactList);
            setParticipantActiveKey([]);
          }}
        >
           {`Select ${selectedContactList?.length > 0 ? `(${selectedContactList?.length})` : ''}`}
        </Button>
      </div>
    </div>
  );
};

export default ContactListModal;
