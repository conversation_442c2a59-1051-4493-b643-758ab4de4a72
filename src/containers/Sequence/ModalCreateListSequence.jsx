import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Rate,
  Row,
  Select,
  notification,
  Space,
} from 'antd';
import _debounce from 'lodash/debounce';
import Email1Logo from '../../assets/img/ai.png';
import ModalCreateFromVancancy from './ModalCreateFromVancancy';
import ModalCreateFromVClone from './ModalCreateFromClone';
import { useAuth } from '../../store/auth';
import { createSequenceTemplate } from '../../services/sequenceTemplate';
import SequenceTemplateForm from '../SequenceTemplate/SequenceTemplateForm';
import ModalCreateFromTemplate from './ModalCreateFromTemplate';
import _ from 'lodash';
import ModalCreateFromSaveList from './ModalCreateFromSaveList';
import ModalCreateFromHotList from './ModalCreateFromHotList';
import ModalCreateFormAI from './ModalCreateFormAI';
import ModalCreateFromCandidate from './ModalCreateFromCandidate';

import { PlusOutlined } from '@ant-design/icons';
import { useViewAs } from '../../store/viewAs';
import { SEQUENCE_CREATING_TYPES } from './SequenceTab';
import { getUserViewAsLicenseType } from '../../helpers/getUserViewAs';
import { licenseType } from '../../constants/common.constant';

function ModalCreateListSequence({
  reloadData = null,
  setSelectedCreatingType,
}) {
  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const [open, setOpen] = useState(false);
  const [openTemplateModal, setOpenTemplateModal] = useState(false);

  const handleCloseTemplateModal = () => setOpenTemplateModal(false);

  const handleOk = (template) => {
    if (_.isEmpty(template?.name)) {
      notification.warning({
        message: 'Warning',
        description: 'Template Name can not empty!',
      });
      return;
    }

    const creatingTemplate = {
      ...template,
      organizationId:
        userToSet?.user?.organizationId || userToSet?.organization?.id,
      modifiedBy: userToSet?.user?.id || userToSet?.id,
      createdBy: userToSet?.user?.id || userToSet?.id,
    };

    createSequenceTemplate(creatingTemplate)
      .then((res) => {
        if (res?.data?.result) {
          notification.success({
            message: 'Sucess',
            description: 'The template is added!',
          });

          handleCloseTemplateModal();
        }
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      });
  };

  const closeCreateSequenceModal = () => {
    if (reloadData) {
      reloadData();
    }
    setOpen(false);
  };

  const items = [
    {
      key: SEQUENCE_CREATING_TYPES.FROM_SCRATCH,
      label: (
        <ModalCreateFromVClone
          closeCreateSequenceModal={closeCreateSequenceModal}
        />
      ),
    },
    {
      key: SEQUENCE_CREATING_TYPES.FROM_TEMPLATE,
      label: (
        <ModalCreateFromTemplate
          closeCreateSequenceModal={closeCreateSequenceModal}
        />
      ),
    },
    ...(isStandardUser
      ? []
      : [
          {
            key: SEQUENCE_CREATING_TYPES.FROM_VACANCY,
            label: (
              <ModalCreateFromVancancy
                closeCreateSequenceModal={closeCreateSequenceModal}
              />
            ),
          },
        ]),

    // {
    //   key: SEQUENCE_CREATING_TYPES.FROM_CANDIDATE,
    //   label: (
    //     <ModalCreateFromCandidate
    //       closeCreateSequenceModal={closeCreateSequenceModal}
    //     />
    //   ),
    // },

    ,
  ];

  const handleMenuClick = (e) => {
    setSelectedCreatingType(e.key);
  };

  return (
    <>
      <Dropdown
        menu={{
          items,
          onClick: handleMenuClick,
        }}
        placement="bottomLeft"
        arrow
        trigger={['click']}
      >
        <Button
          type="primary"
          className="!border-[#b2b8be] flex items-center justify-center text-[#fff]"
          // onClick={() => setOpen(true)}
        >
          <PlusOutlined />{' '}
          <span className="font-Montserrat">Add New Sequence</span>
        </Button>
      </Dropdown>
      {openTemplateModal && (
        <SequenceTemplateForm
          handleCancel={handleCloseTemplateModal}
          handleOk={handleOk}
          open={openTemplateModal}
          sequenceTemplate={null}
        />
      )}
    </>
  );
}

export default ModalCreateListSequence;
