import {
  <PERSON><PERSON>,
  But<PERSON>,
  Modal,
  Spin,
  Upload,
  message,
  notification,
} from 'antd';
import { useEffect, useState } from 'react';
import {
  downLoadErrorFile,
  getDetailContactList,
  insertContactToList,
} from '../../services/contactList';
import {
  MailOutlined,
  PhoneOutlined,
  UploadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import DetailContactListItem from './DetailContactListItem';
import ModalCreateNewContact from './ModalCreateNewContact';

function DetailContactList(props) {
  const { isModalOpen, setIsModalOpen, currentDetail } = props;
  const [dataDetail, setDataDetail] = useState([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [fileList, setFileList] = useState([]);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [loadingDownloadError, setLoadingDownloadError] = useState(false);
  const [errorData, setErrorData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dataFileInsert, setDataFileInsert] = useState([]);
  const [openCreateNew, setOpenCreateNew] = useState(false)

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleGetDetailData = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    const { data } = await getDetailContactList(currentDetail?.id);
    setDataDetail(data?.result);
    messageApi.destroy();
  };

  useEffect(() => {
    if (isModalOpen) {
      setDataDetail([]);
      handleGetDetailData();
    }
  }, [isModalOpen]);

  const uploadProps = {
    beforeUpload: (file) => {
      return false;
    },
    onChange({ file }) {
      if (fileList?.uid === file.uid) {
        setFileList([]);
      } else {
        setFileList([file]);
      }
    },
  };

  const handleSave = async () => {
    setErrorData([]);
    let formData = new FormData();
    formData.append('id', currentDetail?.id);
    if (fileList?.length > 0) {
      formData.append('csvFile', fileList[0]);
    }

    try {
      setLoading(true);
      const { data } = await insertContactToList(formData);
      if (data) {
        setDataFileInsert(data?.result?.fullData ?? []);
        if (data?.result?.errors?.length > 0) {
          notification.warning({
            message: 'Error on Upload CSV',
          });
          setErrorData(data?.result?.errors);
          setLoading(false);
          // return;
        }

        if (data?.result?.data?.length > 0) {
          notification.success({
            message: `${data?.result?.data?.length} / ${data?.result?.fullData?.length} are added`,
          });
        }
        // setIsModalOpen(false);
      }
      handleGetDetailData();
      setLoading(false);
    } catch (e) {
      console.log(e);
      notification.error({
        message: e?.response?.data?.message,
      });
      setLoading(false);
    }
  };

  const handleDownLoadErrorFile = async () => {
    setLoadingDownloadError(true);
    const response = await downLoadErrorFile({ data: errorData });
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'errors.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    setLoadingDownloadError(false);
  };

  return (
    <>
      {contextHolder}
      <Modal
        width={1000}
        title={
          <div className="w-full">
            <div className="font-Montserrat px-10 border-b pb-3 w-full">
              {currentDetail?.name}
            </div>
          </div>
        }
        open={isModalOpen}
        // onOk={handleOk}
        onCancel={handleCancel}
        footer={
          <div className="w-full flex justify-end">
            <Button
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
              type="primary"
              onClick={handleCancel}
            >
              <span className="font-Montserrat">Close</span>
            </Button>
          </div>
        }
      >
        <div style={{ display: 'flex' }}>
          <div style={{ fontWeight: '600' }}>
            <Button type="primary" onClick={() => {setOpenCreateNew(true)}}>Create new Contact</Button>
          </div>
          <div style={{ marginLeft: '20px' }}>
            <Upload {...uploadProps} fileList={fileList}>
              <Button icon={<UploadOutlined />}>Insert CSV</Button>
            </Upload>
          </div>
          {fileList.length > 0 && (
            <div style={{ marginLeft: '10px' }}>
              <Button
                loading={loading}
                onClick={() => handleSave()}
                type="primary"
              >
                Save
              </Button>
            </div>
          )}
        </div>
        <div>
          {errorData?.length > 0 ? (
            <div>
              <Alert
                style={{ marginTop: '20px' }}
                message={`There are  ${errorData?.length} / ${dataFileInsert?.length} contacts with errors, please download the file for more information.`}
                type="warning"
                showIcon
                closable
              />
              <div
                onClick={() => {
                  !loadingDownloadError && handleDownLoadErrorFile();
                }}
                style={{
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  color: 'red',
                  marginTop: '10px',
                }}
              >
                Download errors file {loadingDownloadError ? <Spin /> : ''}
              </div>
            </div>
          ) : (
            <></>
          )}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 pt-5">
          {dataDetail?.map((item) => (
            <DetailContactListItem
              listName={currentDetail?.name || '-'}
              item={item}
              currentDetail={currentDetail}
              handleGetDetailData={handleGetDetailData}
            />
          ))}
        </div>
      </Modal>
      <ModalCreateNewContact openCreateNew={openCreateNew} setOpenCreateNew={setOpenCreateNew} currentDetail={currentDetail} handleGetDetailData={handleGetDetailData}/>
    </>
  );
}

export default DetailContactList;
