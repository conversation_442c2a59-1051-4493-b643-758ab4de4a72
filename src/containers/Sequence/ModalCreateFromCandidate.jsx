import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Divider,
  Dropdown,
  Form,
  Image,
  Input,
  Menu,
  Modal,
  Pagination,
  Radio,
  Row,
  Select,
  Spin,
  Steps,
  Switch,
  notification,
} from 'antd';
import _debounce from 'lodash/debounce';
import CandidateLogo from '../../assets/img/candidate.png';
import { getAllEmails, getListEmailFromSequence } from '../../services/search';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import {
  ArrowRightOutlined,
  LeftOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

export const StyledSteps = styled(Steps)`
  .ant-steps-item-title {
    font-size: 12px;
    font-weight: 500;
    color: #0891b2 !important ;
  }
`;

export const SEQUENCE_GUIDE = [
  <div>Name your sequence</div>,
  <div>Select a time to schedule your sequence or send Immediately</div>,
  <div>
    Add your Participants to the sequence you can select as many Participants as
    you wish
  </div>,
  <div className="inline">
    <span>Start building your sequence press the</span>
    <PlusCircleOutlined className="text-[#7CF5FF] mx-1" />
    <span>icon and choose where to start example first step email</span>
  </div>,
  <div>
    Outreach to all your Participants on LinkedIn also by choosing a LinkedIn
    step we advise selecting three Participants for this step so you keep within
    your limits on LinkedIn
  </div>,
  <div>
    You can add a task in your sequence this will then send the tasks to the
    task page
  </div>,
  <div>
    We are connected directly to Bullhorn so a note will be updated on each
    contact you send to
  </div>,
  <div>
    Once you have built press the start button and start enjoying your results
  </div>,
];

function ModalCreateFromCandidate({
  closeCreateSequenceModal,
  isShowModal = false,
  backTo = () => {},
}) {
  const [open, setOpen] = useState(false);
  const { handleSubmit, control, getValues, setValue, watch, reset } =
    useForm();

  const [tabDetail, setTabDetail] = useState(false);
  const [dataEmailsSequence, setDataEmailSequence] = useState([]);
  const [dataListSequence, setDataListEmailSequence] = useState([]);
  const [chooseItem, setChooseItem] = useState(null);
  const [tabState, setTabState] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [existingContacts, setExistingContacts] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [pending, setPending] = useState(false);

  const handleDateSequence = async (status = null, page = 1, limit = 10) => {
    setPending(true);
    const { data } = await getAllEmails(
      status,
      page,
      limit,
      getValues('searchText')
    );
    setDataEmailSequence(data?.result?.mails);
    setCountPage(data?.result?.count);
    setPending(false);
  };

  const handleGetListData = async () => {
    const { data } = await getListEmailFromSequence(chooseItem);
    const newValueArr = data?.result?.mails?.map((item, index) =>
      index === data?.result?.mails.length - 1
        ? { ...item, delay: index + 1 }
        : item
    );
    const newData = newValueArr?.slice(1).map((item, index) => {
      return {
        delay: item.delay,
        subject: item.subject,
        content: item.content,
        key: index + 1,
        status: item?.status,
      };
    });
    setInputNumberStep(newData);
    setDataListEmailSequence(data?.result?.mails);
    setEmailConfigData(data?.result?.mails);
    setValue(
      `sendMail.mailStepParentMailTo`,
      data?.result?.mails?.[0]?.recipients ?? []
    );
    setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
    setValue(
      `sendMail.mailStepParentContent`,
      data?.result?.mails?.[0]?.content
    );
    setValue(
      `sendMail.mailStepParentSubject`,
      data?.result?.mails?.[0]?.subject
    );
    setNumberStep(newData?.length);
  };

  useEffect(() => {
    handleDateSequence();
  }, []);

  const handlePagination = (page) => {
    setPage(page);
    handleDateSequence(null, page, 10);
  };

  const handleOke = () => {
    if (!chooseItem) {
      notification.error({ message: 'Please choose a Sequence' });
      return;
    }
    setTabState(true);
  };

  useEffect(() => {
    if (chooseItem && tabState) {
      handleGetListData();
    }
  }, [chooseItem, tabState]);

  useEffect(() => {
    if (!open) {
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      setTabState(false);
      setChooseItem(null);
    }
  }, [open]);

  return (
    <>
      {!isShowModal && (
        <div
          onClick={() => setOpen(true)}
          className="w-full flex items-center justify-start gap-3 p-1 create-new-sequence-button-container"
        >
          <Image
            style={{ height: '43px' }}
            src={CandidateLogo}
            preview={false}
          />
          <div>
            <div className="font-semibold ">Candidate</div>
            <div className="font-medium text-xs opacity-70">
              Create a sequence from Candidate.
            </div>
          </div>
        </div>
      )}
      {isShowModal && (
        <div className="relative sequence-background-container py-3 px-6 bg-white rounded-md shadow-md overflow-hidden">
          <div className="flex items-center justify-between pb-2 border-b">
            <Button onClick={backTo} className='bg-white' icon={<LeftOutlined />}>
              Back
            </Button>
            <div className="flex flex-col items-center gap-1 justify-center text-cyan-600">
              <div className="text-base font-semibold">
                Candidate
              </div>
              <div className="text-xs opacity-80">
                Adjust the sequence steps based on the candidate’s stage in the
                hiring process.
              </div>
            </div>
            <div />
          </div>{' '}
          <div className="grid grid-cols-10 min-h-[38rem]">
            <div className="w-full col-span-6 flex flex-col gap-2 justify-center items-center h-full">
              <div className="max-w-md flex-wrap opacity-60">
                <StyledSteps
                  progressDot
                  current={SEQUENCE_GUIDE?.length}
                  direction="vertical"
                  items={SEQUENCE_GUIDE.map((item) => ({
                    title: item,
                  }))}
                  className="!text-xs"
                />
              </div>
            </div>
            <div className="col-span-4 w-full">
              <Form
                layout="vertical"
                className="start-from-sratch-container py-5 flex justify-center items-center w-full"
              >
                <BullhornSendEmail
                  watch={watch}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  sendToEmail={getValues()?.email}
                  mailTitle={getValues()?.jobtitle}
                  listAddContactSelected={
                    emailConfigData?.[0]?.recipients ?? []
                  }
                  setNumberStep={setNumberStep}
                  numberStep={numberStep}
                  inputNumberStep={inputNumberStep}
                  setInputNumberStep={setInputNumberStep}
                  setEmailConfigData={setEmailConfigData}
                  fromSequenseEmail={true}
                  fromCreatingScratchSequence={true}
                  fromCandidateSequence={true}
                  newUpdatedSequence={false}
                  closeCreateSequenceModal={() => {
                    setOpen(false);
                    closeCreateSequenceModal();
                  }}
                />
              </Form>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default ModalCreateFromCandidate;
