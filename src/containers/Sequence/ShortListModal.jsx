import { useRef, useState } from 'react';
import { getNewContactList } from '../../services/contactList';
import { Button, Select, Spin, Table, Tooltip } from 'antd';
import { useEffect } from 'react';
import { COMMON_STRINGS } from '../../constants/common.constant';
import {
  CloseCircleOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import {
  searchBullhornData,
  searchShortlistByCompanyId,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';

const columns = [
  {
    title: COMMON_STRINGS.NAME,
    key: 'name',
    dataIndex: 'name',
    // align: 'center',
    width: '70%',
    render: (text, record) => (
      <div className="flex gap-4">
        <div className="w-full">
          <div
            className="text-sm font-semibold line-clamp-1"
            title={record?.name}
          >
            {record?.name}
          </div>
        </div>
      </div>
    ),
  },
  {
    title: 'Count',
    key: 'candidatesCount',
    dataIndex: 'candidatesCount',
    align: 'center',
    render: (contactCount) => {
      return (
        <div className="w-full flex items-center justify-center font-medium">
          {contactCount || 0}
        </div>
      );
    },
  },
];

const ShortListModal = ({ submitList, shortList, setParticipantActiveKey }) => {
  const [dataListTab, setDataListTab] = useState([]);
  const [selectedShortList, setSelectedShortList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [companyId, setCompanyId] = useState('');

  const timeoutRef = useRef(null);
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedShortList(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedShortList,
    onChange: onSelectChange,
    type: 'checkbox',
  };

  const handleGetData = async () => {
    setLoading(true);
    try {
      const { data: dataListTemp } = await getNewContactList();

      setDataListTab(dataListTemp?.result);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleSearchShortlist = async (companyId) => {
    setSelectedShortList([])
    setLoading(true);
    try {
      const { data } = await searchShortlistByCompanyId(companyId);
      console.log('data: ', data);
      if (data?.result?.length > 0) {
        const rawShortlist = [...data?.result];
        const newShortlistData = rawShortlist?.map((item) => ({
          id: item?.id,
          name: item?.title,
          candidatesCount: item?.submissions?.total,
        }));
        setDataListTab([...newShortlistData]);
      }
      //
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const {
    options: companyOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  useEffect(() => {
    // handleGetData();
    handleCompanySearch(' ');
  }, []);

  // useEffect(() => {
  //   if (shortList?.length > 0) {
  //     const selectedRowKeysTemp = shortList?.map((list) => list?.id || list);
  //     setSelectedShortList([...selectedRowKeysTemp]);
  //   }
  // }, [shortList]);

  return (
    <div>
      <div className="flex justify-start w-full">
        <div className="flex items-center gap-3 w-full">
          <Select
            showSearch
            loading={isLoadingCompany}
            disabled={loading}
            onPopupScroll={(e) => handleCompanyScroll(e, 'ClientCorporation')}
            options={companyOptions?.map((option) => ({
              ids: option.id,
              value: option.name,
              label: (
                <>
                  <div className="grid">
                    <div className="flex justify-between">
                      <span className="text-base font-base">
                        {option.id} - {option.name}
                      </span>
                    </div>
                    <div className="contact-details">
                      <div className="flex">
                        <span className="text-gray-500 text-xs min-w-[200px]">
                          <PhoneOutlined /> {option.phone ? option.phone : '-'}
                        </span>
                        <span className="text-gray-500 text-xs min-w-[200px]">
                          <InfoCircleOutlined />{' '}
                          {option.status ? option.status : '-'}
                        </span>
                        <span className="text-gray-500 text-xs min-w-[200px]">
                          <EnvironmentOutlined />
                          {option.address &&
                          option.address.city &&
                          option.address.state
                            ? `${option.address.city}, ${option.address.state}`
                            : option.address && option.address.city
                              ? option.address.city
                              : option.address && option.address.state
                                ? option.address.state
                                : '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                </>
              ),
            }))}
            className="w-full"
            // style={{ width: '500px', height: '50px', color: '#fff' }}
            onSelect={(_value, option) => handleSearchShortlist(option?.ids)}
            onSearch={(value) => {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }
              timeoutRef.current = setTimeout(() => {
                if (value) companySetStart(0);
                handleCompanySearch(value);
              }, 500);
            }}
            placeholder="Search Company"
            notFoundContent={
              isLoadingCompany && (
                <div className="w-full flex justify-center py-4">
                  <Spin size="default" />
                </div>
              )
            }
          />
        </div>
      </div>
      <div className="search-table-new-design-container mt-5">
        <Table
          size="small"
          rowSelection={rowSelection}
          rowKey={(record) => record?.id}
          loading={loading}
          dataSource={dataListTab}
          pagination={{
            pageSize: 5,
          }}
          columns={columns}
          className="custom-table"
          onRow={(record, rowIndex) => {
            return {
              onClick: () => {
                // navigate(`/contact_list/${record.id}`);
                const currSelectedShortList = [...selectedShortList];
                if (currSelectedShortList?.includes(record?.id)) {
                  const removedShortList = currSelectedShortList?.filter(
                    (listId) => listId !== record?.id
                  );
                  onSelectChange([...removedShortList]);
                } else {
                  onSelectChange([...selectedShortList, record.id]);
                }
              },
              style: { cursor: 'pointer' },
            };
          }}
        />
      </div>
      <div className="flex justify-end gap-1 pt-5">
        {selectedShortList?.length > 0 && (
          <Tooltip title={'Remove selection.'}>
            <Button
              type="text"
              onClick={() => setSelectedShortList([])}
              icon={<CloseCircleOutlined />}
            />
          </Tooltip>
        )}

        <Button
          disabled={loading}
          type="primary"
          onClick={() => {
            const listShortList = dataListTab?.filter((list) =>
              selectedShortList?.includes(list?.id)
            );
            submitList(listShortList);
            setParticipantActiveKey([]);
            setSelectedShortList([])
          }}
        >
          {`Add ${selectedShortList?.length > 0 ? `(${selectedShortList?.length})` : ''}`}
        </Button>
      </div>
    </div>
  );
};

export default ShortListModal;
