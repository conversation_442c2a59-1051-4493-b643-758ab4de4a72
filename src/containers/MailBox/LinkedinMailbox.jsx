import { useEffect, useRef, useState } from 'react';
import {
  getLinkedinMessages,
  getRetrieveOwnProfile,
  syncLinkedInMessageData,
} from '../../services/mailBox';
import {
  Alert,
  Avatar,
  Badge,
  Button,
  Image,
  Input,
  Row,
  Spin,
  Table,
  Tag,
  Tooltip,
  message,
  notification,
} from 'antd';
import {
  AntDesignOutlined,
  ApiOutlined,
  ClockCircleOutlined,
  CommentOutlined,
  LeftOutlined,
  LeftSquareOutlined,
  LinkedinOutlined,
  LinkOutlined,
  RedoOutlined,
  RightOutlined,
  SyncOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { v4 as uuid } from 'uuid';
import ChatWindow from './ChatWindow';
import defaultUser from '../../assets/img/avatars/user.png';
import handleRenderTime from '../../function/handleRenderTime';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { useNavigate } from 'react-router-dom';
import zileoIcon from '../../assets/img/zileoIcon.png';
import clsx from 'clsx';
import { Card, CardContent } from '../../components/RadixCard';
import {
  Clock,
  Crown,
  Linkedin,
  MoreVertical,
  Paperclip,
  Send,
} from 'lucide-react';

const LinkedinMailbox = () => {
  const navigate = useNavigate();
  const linkedinScrollRef = useRef(null);

  const [data, setData] = useState([]);
  const [defaultData, setDefaultData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loadingSyncData, setLoadingSyncData] = useState(false);
  const [linkedInProfile, setLinkedInProfile] = useState();
  const [showToast, setShowToast] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [nextPageTokenArr, setNextPageTokenArr] = useState([null]);
  const [nextPageToken, setNextPageToken] = useState();
  const [loadingDataPage, setLoadingDataPage] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const handleGetProfileAccount = async () => {
    try {
      const { data: profileLinkedIn } = await getRetrieveOwnProfile();
      setLinkedInProfile(profileLinkedIn?.result);
    } catch (e) {
      notification.error({ message: 'Something went wrong' });
    }
  };

  useEffect(() => {
    handleGetProfileAccount();
  }, []);

  const userToSet = profileUser || profileUserAuth;

  const isNotSetLinkedin =
    userToSet &&
    !userToSet?.user?.unipileAccountId &&
    !userToSet?.unipileAccountId;

  const columns = [
    {
      title: <div className="w-full flex justify-center text-lg">Contact</div>,
      dataIndex: 'author',
      width: 150,
      key: 'author',
      render: (author, record) => {
        const listItem = record?.list_item || [];
        const latesMessage =
          listItem?.length > 0 ? listItem[listItem?.length - 1] : '';
        return (
          <div className="flex items-center gap-2">
            {record?.isGroupChat ? (
              <div style={{ width: '80px' }}>
                <Avatar.Group>
                  {record?.userInChat?.map((itemProfile) => (
                    <Avatar
                      key={itemProfile?.profileUrl}
                      src={itemProfile?.profileUrl}
                      onError={(e) => {
                        e.target.src = zileoIcon;
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (itemProfile?.linkedinUrl) {
                          window.open(
                            itemProfile?.linkedinUrl,
                            '_blank',
                            'noopener,noreferrer'
                          );
                        }
                      }}
                    />
                  ))}
                </Avatar.Group>
              </div>
            ) : (
              <div>
                {author?.profileUrl !== 'undefined' ? (
                  <Image
                    className="rounded-full"
                    src={author?.profileUrl}
                    width={'60px'}
                    onError={(e) => {
                      e.target.src = defaultUser;
                    }}
                    preview={false}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (author?.linkedinUrl) {
                        window.open(
                          author?.linkedinUrl,
                          '_blank',
                          'noopener,noreferrer'
                        );
                      }
                    }}
                  />
                ) : (
                  <Image
                    className="rounded-full"
                    src={defaultUser}
                    width={'50px'}
                    onError={(e) => {
                      e.target.src = defaultUser;
                    }}
                    preview={false}
                  />
                )}
              </div>
            )}
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <span className="font-semibold">
                  {record?.isGroupChat ? record?.title : author?.name}
                </span>
                <Tag color="geekblue" icon={<ClockCircleOutlined />}>
                  {handleRenderTime(listItem?.[0]?.createdAt)}
                </Tag>
                {/* <span className='text-xs italic text-gray-700'>{handleRenderTime(latesMessage?.createdAt)}</span> */}
              </div>
              <span className="text-sm text-gray-700 font-medium">
                {listItem?.[0]?.text?.slice(0, 20)}...
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: <div className="w-full flex justify-center text-lg">Headline</div>,
      dataIndex: 'author',
      key: 'author',
      width: '60%',
      render: (author, record) => {
        const totalUnread = record?.seen;
        return (
          <div>
            {record?.inmail ? (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tag color="processing">InMail</Tag>
              </div>
            ) : (
              <div className="flex items-center gap-2 ">
                <ApiOutlined className="text-[#0288d1]" size={40} />
                <span className="line-clamp-1" title={author?.headline}>
                  {author?.headline}
                </span>
              </div>
            )}
            {totalUnread > 0 && (
              <div>
                <div
                  style={{
                    position: 'absolute',
                    width: '25px',
                    height: '25px',
                    backgroundColor: '#0a66c2',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '50%',
                    right: '-10px',
                    zIndex: '9999',
                    marginTop: '-20px',
                    color: '#fff',
                    fontWeight: '600',
                  }}
                >
                  {totalUnread}
                </div>
                <div
                  style={{
                    position: 'absolute',
                    width: '5px',
                    height: '100%',
                    backgroundColor: '#0a66c2',
                    top: 0,
                    left: '-66%',
                  }}
                ></div>
              </div>
            )}
          </div>
        );
      },
    },
  ];

  const handleSearch = (searchName) => {
    const searchData = defaultData.filter((item) =>
      item?.author?.name
        ?.toString()
        .toLocaleLowerCase()
        .includes(searchName.toLocaleLowerCase())
    );

    setData(searchData);
  };

  const getData = async (cursor) => {
    setLoadingDataPage(true);
    try {
      const { data } = await getLinkedinMessages(cursor);
      if (data?.result?.cursorIndex) {
        setNextPageToken(data?.result?.cursorIndex);
        setNextPageTokenArr([...nextPageTokenArr, data?.result?.cursorIndex]);
      } else {
        setNextPageToken(null);
      }
      if (data?.result?.items?.length > 0) {
        const messageList = [...data?.result?.items].map((item) => ({
          ...item,
          id: uuid(),
        }));
        setData((prev) => [...prev, ...messageList]);
        setDefaultData((prev) => [...prev, ...messageList]);
      }
      setLoadingDataPage(false);
      setLoading(false);
    } catch (error) {
      console.log('message err: ', error);
      setLoading(false);
      setLoadingDataPage(false);
    }
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const selection = {
    selectedRowKeys,
    onChange: onSelectChange,
    type: 'radio',
  };

  const openLinkedinSetting = () => navigate('/settings?tab=linkedin');

  useEffect(() => {
    getData();
  }, []);

  const handleSyncData = async () => {
    try {
      setLoadingSyncData(true);
      const { data } = await syncLinkedInMessageData();
      setLoadingSyncData(false);
      setShowToast(true);
      message.success(
        'Data synced successfully, please wait a while for the data to be synchronized.'
      );
    } catch {
      setLoadingSyncData(false);
      notification.error({ message: 'Sync data Failed' });
    }
  };

  const handleReSyncData = async () => {
    const { data } = await syncLinkedInMessageData();
  };

  useEffect(() => {
    handleReSyncData();
  }, []);

  const handleGetNextPage = async () => {
    setCurrentPage(+currentPage + 1);
    await getData(nextPageToken);
  };

  const handleGetPrevPage = async () => {
    const page = +currentPage - 1;
    setCurrentPage(page);
    const newArr = nextPageTokenArr.slice(0, nextPageTokenArr.length - 1);
    setNextPageTokenArr(newArr);
    await getData(nextPageTokenArr[page]);
  };

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollTop + clientHeight >= scrollHeight - 20) {
      if (nextPageToken) {
        handleGetNextPage();
      }
    }
  };

  return (
    <div className="w-full h-full flex items-center justify-center overflow-hidden rounded-xl shadow-sm bg-white min-h-[60vh] relative">
      {loading && <Spin />}
      {!loading &&
        (isNotSetLinkedin ? (
          <div className="p-4">
            <div className="flex flex-col justify-center items-center py-40 gap-3">
              <LinkedinOutlined className="text-[#0288d1] text-[5rem]" />
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3">
                No Access
              </div>
              <p className="text-lg md:text-xl lg:text-xl flex items-center gap-2">
                <span>Your account has not linked Linkedin account yet.</span>
              </p>
              <Button
                type="primary"
                icon={<LinkOutlined />}
                onClick={openLinkedinSetting}
              >
                Connect Linkedin Account
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* LinkedIn List Pane */}
            <div className="w-2/5 border-r border-slate-200 flex flex-col">
              {showToast && (
                <Alert
                  description="Please reload in few minutes"
                  type="warning"
                  showIcon
                  closable
                  style={{ marginTop: '10px' }}
                  onClose={() => setShowToast(false)}
                />
              )}
              <div className="p-4 border-b border-slate-200 bg-slate-50/70">
                <Button
                  loading={loadingSyncData}
                  onClick={handleSyncData}
                  className="w-full bg-blue-600 hover:bg-blue-700 mb-3 h-9 text-xs text-white font-medium"
                  icon={<SyncOutlined className="w-3.5 h-3.5" />}
                >
                  Sync LinkedIn Messages
                </Button>
                {/* LinkedIn Search Input Removed Here */}
              </div>
              <Row
                className="flex-1 max-h-[58vh] overflow-y-auto"
                ref={linkedinScrollRef}
                onScroll={handleScroll}
              >
                <div className="p-3 space-y-2.5 w-full">
                  {data?.map((message) => (
                    <Card
                      key={message?.id}
                      className={clsx(
                        `cursor-pointer transition-all duration-150 hover:shadow-md border-l-2 w-full`,
                        message?.inmail
                          ? 'border-l-amber-400 bg-amber-50/30'
                          : 'border-l-blue-400 bg-blue-50/30',
                        selectedMessage?.id === message.id &&
                          'ring-1 ring-blue-500 shadow-sm'
                      )}
                      onClick={() => setSelectedMessage(message)}
                    >
                      <CardContent className="!p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <Avatar
                              src={message?.author?.profileUrl}
                              className="w-10 h-10 ring-1 ring-white shadow-xs"
                            >
                              {message?.author?.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </Avatar>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5 mb-1">
                              <h3 className="font-semibold text-slate-800 truncate text-xs">
                                {message?.author?.name}
                              </h3>
                              {message?.inmail && (
                                <div className="inline-flex items-center gap-1 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-amber-100 text-amber-800 border-amber-200 text-[10px] px-1.5 py-0.5">
                                  <Crown className="w-2.5 h-2.5" />
                                  InMail
                                </div>
                              )}
                              {message?.seen > 0 && (
                                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full ml-auto"></div>
                              )}
                            </div>
                            <p
                              className="text-xs text-slate-500 truncate mb-1 line-clamp-1 max-w-full"
                              title={message?.author?.headline}
                            >
                              {message?.author?.headline}
                            </p>
                            <p className="text-xs text-slate-600 line-clamp-2 mb-1.5 leading-relaxed">
                              {message?.list_item[0]?.text?.length > 0 &&
                                `${message?.list_item[0]?.text}...`}
                            </p>
                            <div className="flex items-center text-[11px] text-slate-400">
                              <Clock className="w-2.5 h-2.5 mr-1" />
                              {handleRenderTime(
                                message?.list_item?.[0]?.createdAt
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  {loadingDataPage && (
                    <div className="p-4 text-center text-xs text-slate-500 flex items-center gap-1">
                      Loading more messages... <Spin size="small" />
                    </div>
                  )}
                  {!nextPageToken && data?.length > 0 && (
                    <div className="p-4 text-center text-xs text-slate-400">
                      No more messages
                    </div>
                  )}
                </div>
              </Row>
              <div className="p-3 border-t border-slate-200 bg-slate-50/70 flex items-center justify-between text-xs">
                <span>Showing {data?.length} messages</span>
                {loadingDataPage && <Spin size="small" />}
              </div>
            </div>
            {/* LinkedIn Detail Pane */}
            <div className="flex-1 flex flex-col overflow-hidden h-full">
              {selectedMessage ? (
                <>
                  <ChatWindow
                    reloadData={getData}
                    backTo={() => setSelectedMessage(null)}
                    setData={setData}
                    data={data}
                    linkedInProfile={linkedInProfile}
                    author={selectedMessage?.author}
                    chatInfo={selectedMessage}
                    listItem={selectedMessage?.list_item}
                  />
                  {/*
                  <Row className="flex-1 p-5 bg-slate-50 max-h-[58vh] overflow-y-auto">
                    
                    <div className="space-y-4">
                      {selectedLinkedinMessage.fullContent
                        .split('\n\n')
                        .map((paragraph, index) => {
                          const isUserReply =
                            paragraph.startsWith('[User Reply]');
                          const messageText = isUserReply
                            ? paragraph.replace('[User Reply] ', '')
                            : paragraph.startsWith('[Their Reply] ')
                              ? paragraph.replace('[Their Reply] ', '')
                              : paragraph;
                          const senderName = isUserReply
                            ? 'You'
                            : selectedLinkedinMessage.name;
                          const avatarSrc = isUserReply
                            ? '/placeholder.svg?height=32&width=32'
                            : selectedLinkedinMessage.avatar ||
                              '/placeholder.svg?height=32&width=32';
                          const avatarFallback = isUserReply
                            ? 'YOU'
                            : selectedLinkedinMessage.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('');

                          const messageSender = paragraph.startsWith(
                            '[User Reply]'
                          )
                            ? 'user'
                            : paragraph.startsWith('[Their Reply]')
                              ? 'contact'
                              : index % 2 === 0
                                ? 'contact'
                                : 'user';

                          return (
                            <div
                              key={index}
                              className={`flex items-end gap-2 ${
                                messageSender === 'user'
                                  ? 'justify-end'
                                  : 'justify-start'
                              }`}
                            >
                              {messageSender === 'contact' && (
                                <Avatar className="w-8 h-8 self-start">
                                  <AvatarImage
                                    src={avatarSrc || '/placeholder.svg'}
                                  />
                                  <AvatarFallback className="text-xs">
                                    {avatarFallback}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              <div
                                className={`max-w-[70%] p-3 rounded-xl text-xs ${
                                  messageSender === 'user'
                                    ? 'bg-blue-600 text-white rounded-br-none'
                                    : 'bg-white text-slate-700 border border-slate-200 rounded-bl-none shadow-sm'
                                }`}
                              >
                                <p>{messageText}</p>
                                <p
                                  className={`text-[10px] mt-1 ${messageSender === 'user' ? 'text-blue-200' : 'text-slate-400'} ${messageSender === 'user' ? 'text-right' : 'text-left'}`}
                                >
                                  {new Date().toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </p>
                              </div>
                              {messageSender === 'user' && (
                                <Avatar className="w-8 h-8 self-start">
                                  <AvatarImage
                                    src={avatarSrc || '/placeholder.svg'}
                                  />
                                  <AvatarFallback className="text-xs bg-slate-700 text-white">
                                    {avatarFallback}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  </Row>*/}
                </>
              ) : (
                <div className="h-full flex items-center justify-center text-slate-500">
                  <div className="text-center">
                    <Linkedin className="w-12 h-12 mx-auto mb-3 text-slate-300" />
                    <p className="text-sm">Select a LinkedIn message to view</p>
                  </div>
                </div>
              )}
            </div>
          </>
          // <div style={{ minHeight: '800px' }} className="w-full">
          //   <div>
          //     <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          //       <Tooltip title="Click here to sync chat data">
          //         <Button
          //           onClick={handleSyncData}
          //           type="primary"
          //           style={{ width: '120px' }}
          //           loading={loadingSyncData}
          //           icon={<SyncOutlined />}
          //         >
          //           Sync
          //         </Button>
          //       </Tooltip>
          //       <div>
          //         {/* <Input
          //           onChange={(e) => handleSearch(e.target.value)}
          //           placeholder="Search by name"
          //           className="font-Montserrat  search-input"
          //           style={{ width: '455px' }}
          //         ></Input> */}
          //       </div>
          //     </div>
          // {showToast && (
          //   <Alert
          //     description="Please reload in few minutes"
          //     type="warning"
          //     showIcon
          //     closable
          //     style={{ marginTop: '10px' }}
          //     onClose={() => setShowToast(false)}
          //   />
          // )}
          //     {/* <Tooltip title="Click here to reconnect account">
          //       <Button onClick={handleSyncData} type="primary" style={{width: "200px", marginLeft: "20px"}} loading={loadingSyncData} icon={<RedoOutlined />}>Reconnect account</Button>
          //     </Tooltip> */}
          //   </div>
          //   <div>
          //     <div className="w-full h-full search-table-new-design-container"
          //       style={{
          //         display: "flex",
          //         width: "100%",
          //         justifyContent: "space-between"
          //       }}
          //     >
          //       <div
          //         onScroll={handleScroll}
          //         style={{ height: '700px', overflowY: 'auto', width:"68%" }}
          //       >
          //         <Table
          //           className="col-span-3 custom-table"
          //           columns={columns}
          //           dataSource={data}
          //           pagination={false}
          //           loading={loadingDataPage}
          //           // pagination={{
          //           //   pageSize: 10,
          //           // }}
          //           style={{ minHeight: '500px', width: '90%' }}
          //           rowKey={(record) => record?.id}
          //           rowSelection={selection}
          //           onRow={(record, rowIndex) => {
          //             return {
          //               onClick: () => {
          //                 if (selectedRowKeys?.includes(record?.id)) {
          //                   setSelectedRowKeys([]);
          //                 } else {
          //                   setSelectedRowKeys([record?.id]);
          //                 }
          //               },
          //               style: { cursor: 'pointer' },
          //             };
          //           }}
          //         />
          //       </div>

          //       {selectedRowKeys?.length > 0 ? (
          //         <div className="col-span-2" style={{
          //           width: "30%"
          //         }}>
          //           <ChatWindow
          //             reloadData={getData}
          //             backTo={() => setSelectedRowKeys([])}
          //             setData={setData}
          //             data={data}
          //             linkedInProfile={linkedInProfile}
          //             author={
          //               data?.find((item) =>
          //                 selectedRowKeys?.includes(item?.id)
          //               )?.author
          //             }
          //             chatInfo={data?.find((item) =>
          //               selectedRowKeys?.includes(item?.id)
          //             )}
          //             listItem={
          //               data?.find((item) =>
          //                 selectedRowKeys?.includes(item?.id)
          //               )?.list_item
          //             }
          //           />
          //         </div>
          //       ) : (
          //         <div
          //           className="flex items-center justify-center gap-5 flex-col col-span-2 rounded-md border h-[30rem]"
          //           style={{ width: '30%', marginTop: '50px' }}
          //         >
          //           <CommentOutlined className="text-[5rem] text-cyan-400" />
          //           <div className="flex items-center gap-2 font-medium text-[#9CA3AF]">
          //             <LeftSquareOutlined />
          //             <span>Please select to open mailbox</span>
          //           </div>
          //         </div>
          //       )}
          //     </div>
          //     {/* <div
          //       style={{
          //         marginTop: '20px',
          //         display: 'flex',
          //         alignItems: 'center',
          //         justifyContent: 'center',
          //       }}
          //     >
          //       <Button
          //         disabled={currentPage == 0 ? true : false}
          //         onClick={() => handleGetPrevPage()}
          //         style={{
          //           width: '50px',
          //           marginLeft: '20px',
          //           backgroundColor: currentPage == 0 ? '#F2F2F2' : '#0288D1',
          //           borderRadius: '0px !important',
          //           display: 'flex',
          //           justifyContent: 'center',
          //           alignItems: 'center',
          //           color: currentPage == 0 ? '#9CA3AF' : '#ffffff',
          //           cursor: currentPage == 0 ? 'not-allowed' : 'pointer',
          //         }}
          //       >
          //         <LeftOutlined />
          //       </Button>
          //       <Button
          //         style={{
          //           marginLeft: '10px',
          //           width: '50px',
          //           backgroundColor: !nextPageToken ? '#F2F2F2' : '#0288D1',
          //           borderRadius: '0px !important',
          //           display: 'flex',
          //           justifyContent: 'center',
          //           alignItems: 'center',
          //           color: !nextPageToken ? '#9CA3AF' : '#ffffff',
          //         }}
          //         onClick={() => handleGetNextPage()}
          //       >
          //         <RightOutlined />
          //       </Button>
          //     </div> */}
          //   </div>
          // </div>
        ))}
    </div>
  );
};

export default LinkedinMailbox;
