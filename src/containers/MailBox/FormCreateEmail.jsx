import {
  Button,
  Checkbox,
  Image,
  Input,
  Rate,
  message,
  notification,
} from 'antd';
import React, { useState } from 'react';
import Logo from '../../assets/img/files.png';
import dayjs from 'dayjs';
import {
  CloseOutlined,
  FullscreenOutlined,
  MinusOutlined,
} from '@ant-design/icons';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'react-hook-form';
import { handleSendMailBox } from '../../services/mailBox';
import { Send } from 'lucide-react';

const FormCreateEmail = (props) => {
  const { setOpenEmailForm } = props;
  const [miniSize, setMiniSize] = useState(false);
  const { getValues, setValue } = useForm();
  const [messageApi, contextHolder] = message.useMessage();

  const handleSendEmail = async () => {
    if (!getValues('email')) {
      notification.error({
        message: 'Error',
        description: 'Email is required',
      });
      return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(getValues('email'))) {
      notification.error({
        message: 'Error: Invalid email address.',
        description:
          'Please ensure the email is in the correct format, e.g., <EMAIL>.',
      });
      return;
    }
    if (!getValues('subject')) {
      notification.error({
        message: 'Error',
        description: 'Subject is required',
      });
      return;
    }
    if (!getValues('content')) {
      notification.error({
        message: 'Error',
        description: 'Content is required',
      });
      return;
    }
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading ...',
        duration: 0,
      });
      const { data } = await handleSendMailBox({
        recipients: [
          {
            email: getValues('email'),
          },
        ],
        subject: getValues('subject'),
        content: getValues('content'),
      });
      if (data) {
        notification.success({
          message: 'Successfully',
          description: 'Sent mail successfully',
        });
        messageApi.destroy();
        setValue('content', null);
        setValue('subject', null);
        setValue('email', null);
        setOpenEmailForm(false);
      }
    } catch (e) {
      messageApi.destroy();
      notification.error({
        message: e?.response?.data?.message ?? 'Something went wrong',
      });
    }
  };

  return (
    <div
      style={{
        width: miniSize ? '350px' : '550px',
        height: miniSize ? '50px' : '600px',
        background: '#fff',
        position: 'absolute',
        right: '20px',
        bottom: '20px',
        borderRadius: '10px',
        boxShadow: '5px 8px 20px 0px #a3a3a3',
      }}
    >
      <div className="bg-slate-100 py-3">
        {contextHolder}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '10px',
          }}
        >
          <div>New Email</div>
          <div>
            {miniSize && (
              <FullscreenOutlined
                onClick={() => setMiniSize(false)}
                style={{ cursor: 'pointer' }}
              />
            )}
            {!miniSize && (
              <MinusOutlined
                onClick={() => setMiniSize(true)}
                style={{ cursor: 'pointer' }}
              />
            )}
            <CloseOutlined
              onClick={() => setOpenEmailForm(false)}
              style={{ cursor: 'pointer', marginLeft: '15px' }}
            />
          </div>
        </div>
      </div>
      {!miniSize && (
        <div className='flex flex-col p-4'>
          <div style={{ marginTop: '0px' }}>
            <Input
              prefix={'Send to: '}
              style={{
                border: 'none',
                borderBottom: '1px solid #ccc',
                borderRadius: '0',
                height: '40px',
                outline: 'none',
              }}
              onChange={(e) => setValue('email', e.target.value)}
              placeholder="Email"
            />
          </div>
          <div style={{ marginTop: '0px' }}>
            <Input
              prefix={'Subject: '}
              style={{
                border: 'none',
                borderBottom: '1px solid #ccc',
                borderRadius: '0',
                height: '40px',
                outline: 'none',
              }}
              onChange={(e) => setValue('subject', e.target.value)}
              placeholder="subject"
            />
          </div>
          <div style={{ marginTop: '10px' }}>
            <TextArea
              showCount
              count={true}
              onChange={(e) => setValue('content', e.target.value)}
              style={{ height: '370px', resize: 'none', border: 'none' }}
            />
          </div>
          <div style={{ marginTop: '10px' }}>
            <Button
              className="flex items-center bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 shadow-sm h-9 text-xs text-white font-medium"
              onClick={handleSendEmail}
              icon={<Send size={15} />}
            >
              Send
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FormCreateEmail;
