import {
  Avatar,
  Badge,
  Button,
  Image,
  Input,
  notification,
  Spin,
  Upload,
} from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import { useState } from 'react';
import {
  createMessageInChat,
  getAttachmentMessages,
  getLinkedinMessagesInChat,
} from '../../services/mailBox';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import { baseURL } from '../../utils/axios';
import clsx from 'clsx';
import { getBase64 } from '../../utils/common';
import { Crown, MessageCircleOff, Paperclip, Send, User } from 'lucide-react';

const ALLOWED_FILE_TYPE_LIST = [
  'image/jpg',
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/pdf',
];

const ChatWindow = ({
  backTo,
  author,
  chatInfo,
  setData,
  data,
  linkedInProfile,
}) => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [fileList, setFileList] = useState([]);
  const [listItem, setListItem] = useState([]);
  const [loadingGetData, setLoadingGetData] = useState(false);
  const chatWindowRef = useRef(null);

  const handleGetMessageInChat = async (chatId) => {
    if (chatId) {
      setLoadingGetData(true);
      try {
        const { data: dataLinkedinChat } =
          await getLinkedinMessagesInChat(chatId);
        setListItem(dataLinkedinChat?.result?.items);
        const updatedData = data.map((item) =>
          item.chat_id === chatId ? { ...item, seen: 0 } : item
        );
        setData(updatedData);
        setLoadingGetData(false);
      } catch (e) {
        console.log(e);
        setLoadingGetData(false);
        notification.error({ message: 'Something went wrong' });
      }
    }
  };

  useEffect(() => {
    setListItem([]);
    setMessage('');
    handleGetMessageInChat(chatInfo?.chat_id);
  }, [chatInfo?.chat_id]);

  const props = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      console.log('file: ', file);
      if (!ALLOWED_FILE_TYPE_LIST.includes(file?.type)) {
        notification.warning({
          description:
            'Upload not allowed. Upload only files of type: .jpg, .jpeg, .png, .gif, .pdf',
        });
        return false;
      }
      setFileList([file]);
      return false;
    },
    fileList,
  };

  useEffect(() => {
    if (chatWindowRef.current) {
      chatWindowRef.current.scrollTop = chatWindowRef.current.scrollHeight;
    }
  }, [listItem]);

  const scrollToEl = (elementId) => {
    const el = document?.getElementById(elementId);
    el.scrollTop = el.scrollHeight;
  };

  const handleSendMessage = async () => {
    if (!message.trim()) {
      notification.warning({
        description: 'Can not send empty message!',
      });
      return;
    }
    setLoading(true);
    try {
      const formData = new FormData();

      formData.append('content', message);
      formData.append('chatId', chatInfo?.chat_id);

      fileList.forEach((multerFile) => {
        formData.append('files', multerFile);
      });

      const { data: sentMessageData } = createMessageInChat(formData);
      console.log(fileList);
      if (true) {
        // const newMessageItem = { ...sentMessageData?.result };
        // console.log("newMessageItem", newMessageItem)
        // const newData = [...data];
        // const chatIndex = newData.findIndex(
        //   (item) => item?.chat_id === chatInfo?.chat_id
        // );
        // const newListItem = [...(newData[chatIndex]?.list_item || [])];
        // newListItem.push({
        //   id: newMessageItem?.message_id,
        //   seen: 0,
        //   object: 'Message',
        //   file: newMessageItem?.file,
        //   sender: {
        //     name: newMessageItem?.sender?.name,
        //     headline: newMessageItem?.sender?.headline,
        //     profileUrl: newMessageItem?.sender?.profileUrl,
        //   },
        //   createdAt: new Date(),
        //   text: message,
        // });
        // newData[chatIndex] = {
        //   ...newData[chatIndex],
        //   list_item: newListItem,
        // };
        fileList?.map((item) => {
          console.log(URL.createObjectURL(item));
        });
        const newMessageItem = {
          id: 'create_new_id_' + listItem.length,
          seen: 0,
          object: 'Message',
          file: fileList?.map((item) => ({
            url: URL.createObjectURL(item),
            local: true,
            type: item?.type,
          })),
          sender: {
            name:
              linkedInProfile?.first_name + ' ' + linkedInProfile?.last_name,
            headline: linkedInProfile?.occupation,
            profileUrl: linkedInProfile?.profile_picture_url,
          },
          createdAt: new Date(),
          text: message,
        };
        newMessageItem.isSender = 1; // Assuming 1 means the user is the sender
        console.log('newMessageItem', newMessageItem);
        setListItem([...listItem, newMessageItem]);
        setMessage([]);
        setFileList([]);
        setTimeout(() => scrollToEl('chat-window'), 500);
      }
      // console.log('sentMessageData: ', sentMessageData);

      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      setLoading(false);
    }
  };

  return (
    <>
      <div className="p-5 border-b border-slate-200 bg-slate-50/70 h-full">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar
              className="w-10 h-10"
              src={
                chatInfo?.author?.profileUrl ||
                '/placeholder.svg?height=40&width=40&query=avatar'
              }
            >
              {chatInfo?.author?.name
                .split(' ')
                .map((n) => n[0])
                .join('')}
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <h2 className="font-semibold text-slate-800 text-sm">
                  {chatInfo?.isGroupChat ? chatInfo?.title : author?.name}
                </h2>
                {chatInfo?.inmail && (
                  <div className="inline-flex items-center gap-1 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-amber-100 text-amber-800 border-amber-200 text-[10px] px-1.5 py-0.5">
                    <Crown className="w-2.5 h-2.5" />
                    InMail
                  </div>
                )}
              </div>
              <p className="text-xs text-slate-500">
                {chatInfo?.author?.headline}
              </p>
            </div>
          </div>
          <Button
            icon={<MessageCircleOff size={17} />}
            className="flex items-center justify-center text-gray-600"
            onClick={backTo}
          />
        </div>
        {loadingGetData ? (
          <div className="flex justify-center items-center h-full min-h-[61vh]">
            <Spin />
          </div>
        ) : (
          <div>
            {/* <div className="flex items-center gap-3 my-4">
              {chatInfo?.isGroupChat ? (
                <div style={{ width: '80px' }}>
                  <Avatar.Group>
                    {chatInfo?.userInChat?.map((itemProfile) => (
                      <Avatar
                        key={itemProfile?.profileUrl}
                        src={itemProfile?.profileUrl}
                      />
                    ))}
                  </Avatar.Group>
                </div>
              ) : (
                <div>
                  {author?.profileUrl !== 'undefined' ? (
                    <Image
                      className="rounded-full"
                      src={author?.profileUrl}
                      width={'60px'}
                    />
                  ) : (
                    <Image
                      className="rounded-full"
                      src={defaultUser}
                      width={'50px'}
                    />
                  )}
                </div>
              )}
            </div> */}
            <div
              id="chat-window"
              className="overflow-y-auto flex flex-col min-h-96 max-h-[27rem] gap-4 px-2 text-xs py-4 scroll-bar-thin"
              ref={chatWindowRef}
              style={{
                minHeight: '26rem',
              }}
            >
              {listItem
                ?.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
                .map((item, index, array) => {
                  // Lấy ngày hiện tại và ngày trước đó
                  const currentDate = dayjs(item?.createdAt).format(
                    'YYYY-MM-DD'
                  );
                  const prevDate =
                    index > 0
                      ? dayjs(array[index - 1]?.createdAt).format('YYYY-MM-DD')
                      : null;
                  const messageSender =
                    item?.isSender === 1 ? 'user' : 'contact';
                  return (
                    <div className="flex flex-col gap-2" key={item?.id}>
                      {currentDate !== prevDate && (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            margin: '20px 0',
                          }}
                        >
                          <div
                            style={{
                              flex: 1,
                              borderTop: '1px solid #d3d3d3',
                              margin: '0 10px',
                            }}
                          ></div>
                          <span
                            style={{
                              fontWeight: 600,
                              color: '#555',
                              textTransform: 'uppercase',
                            }}
                          >
                            {dayjs(item?.createdAt).format('dddd')}
                          </span>
                          <div
                            style={{
                              flex: 1,
                              borderTop: '1px solid #d3d3d3',
                              margin: '0 10px',
                            }}
                          ></div>
                        </div>
                      )}
                      <div
                        className={clsx(
                          `flex items-end gap-2`,
                          messageSender === 'user'
                            ? 'justify-end'
                            : 'justify-start'
                        )}
                      >
                        <div className="flex items-center gap-2">
                          {messageSender === 'contact' &&
                            (item?.sender?.profileUrl !== 'undefined' ? (
                              <Avatar src={item?.sender?.profileUrl} />
                            ) : (
                              <Avatar icon={<User />} />
                            ))}
                        </div>
                        <div
                          className={clsx(
                            `max-w-[70%] p-3 rounded-xl text-xs min-w-20`,
                            messageSender === 'user'
                              ? 'bg-blue-600 text-white rounded-br-none'
                              : 'bg-white text-slate-700 border border-slate-200 rounded-bl-none shadow-sm'
                          )}
                        >
                          <p>{item?.text}</p>
                          <p
                            className={clsx(
                              `text-[10px] mt-1`,
                              messageSender === 'user'
                                ? 'text-blue-200'
                                : 'text-slate-400',
                              messageSender === 'user'
                                ? 'text-right'
                                : 'text-left'
                            )}
                          >
                            {new Date(item?.createdAt).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit',
                            })}
                          </p>
                        </div>
                        {messageSender === 'user' &&
                          (item?.sender?.profileUrl !== 'undefined' ? (
                            <Avatar src={item?.sender?.profileUrl} />
                          ) : (
                            <Avatar icon={<User />} />
                          ))}
                      </div>
                      <div
                        className={clsx(
                          `flex items-end gap-2`,
                          messageSender === 'user'
                            ? 'justify-end'
                            : 'justify-start'
                        )}
                      >
                        {item?.file?.length > 0 &&
                          item?.file?.map((fileItem) => (
                            <>
                              <div className="flex items-center gap-2">
                                {messageSender === 'contact' &&
                                  (item?.sender?.profileUrl !== 'undefined' ? (
                                    <Avatar src={item?.sender?.profileUrl} />
                                  ) : (
                                    <Avatar icon={<User />} />
                                  ))}
                              </div>
                              {fileItem?.local ? (
                                <>
                                  {fileItem?.type === 'image/png' ||
                                  fileItem?.uid ? (
                                    <div className="grid grid-cols-10 rounded-md w-2/3 max-w-[10rem]">
                                      <div className="col-span-10 flex flex-col justify-center rounded-tr-md rounded-br-md text-xs font-medium text-gray-800 rounded-md">
                                        <Image
                                          className="rounded-md"
                                          preview={false}
                                          src={fileItem?.url}
                                        ></Image>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="grid grid-cols-10 rounded-md border w-1/2">
                                      <div className="col-span-3 flex justify-center items-center bg-cyan-500 p-3 rounded-tl-md rounded-bl-md text-xs font-medium text-gray-800">
                                        Attachments
                                      </div>
                                      <div className="col-span-7 flex flex-col justify-center items-center rounded-tr-md rounded-br-md text-xs font-medium text-gray-800">
                                        <a href={fileItem?.url}>
                                          {fileItem?.id}.pdf
                                        </a>
                                      </div>
                                    </div>
                                  )}
                                </>
                              ) : (
                                <>
                                  {fileItem?.type === 'img' || fileItem?.uid ? (
                                    <div className="grid grid-cols-10 rounded-md w-2/3 max-w-[10rem]">
                                      <div className="col-span-10 flex flex-col justify-center rounded-tr-md rounded-br-md text-xs font-medium text-gray-800 rounded-md">
                                        <Image
                                          className="rounded-md"
                                          preview={false}
                                          src={
                                            fileItem?.uid
                                              ? getBase64(fileItem)
                                              : `${baseURL}/emails/linkedin-message-file/get-view-as/${getUserViewAs()}/message/${item?.id}/attachmentId/${fileItem?.id}?type=${fileItem?.type}`
                                          }
                                        ></Image>
                                      </div>
                                    </div>
                                  ) : fileItem?.type === 'linkedin_post' ? (
                                    <div>
                                      <a
                                        href={fileItem?.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500 underline"
                                      >
                                        {fileItem?.url}
                                      </a>
                                    </div>
                                  ) : (
                                    <div className="grid grid-cols-10 rounded-md border w-1/2">
                                      <div className="col-span-3 flex justify-center items-center bg-cyan-500 p-3 rounded-tl-md rounded-bl-md text-sm font-medium text-gray-800">
                                        Attachments
                                      </div>
                                      <div className="col-span-7 flex flex-col justify-center items-center rounded-tr-md rounded-br-md text-xs font-medium text-gray-800">
                                        <a
                                          href={`${baseURL}/emails/linkedin-message-file/get-view-as/${getUserViewAs()}/message/${item?.id}/attachmentId/${fileItem?.id}?type=${fileItem?.type}`}
                                        >
                                          {`${fileItem?.id} (${fileItem?.type})`}
                                        </a>
                                      </div>
                                    </div>
                                  )}
                                </>
                              )}
                              {messageSender === 'user' &&
                                (item?.sender?.profileUrl !== 'undefined' ? (
                                  <Avatar src={item?.sender?.profileUrl} />
                                ) : (
                                  <Avatar icon={<User />} />
                                ))}
                            </>
                          ))}
                      </div>
                    </div>
                  );
                })}
            </div>

            
            <div className="p-4 border-t border-slate-200 bg-slate-50/70">
              <div className="flex items-center gap-3">
                <Upload
                  maxCount={1}
                  listType="picture-card"
                  {...props}
                  className="max-w-28"
                >
                  {fileList?.length === 0 && (
                    <div className="flex flex-col justify-center items-center gap-2 text-gray-500">
                      <Paperclip size={20} />
                      <div className="text-xs font-medium">
                        Image & PDF Only
                      </div>
                    </div>
                  )}
                </Upload>
                <Input.TextArea
                  value={message}
                  onChange={(ev) => setMessage(ev.target.value)}
                  rows={5}
                  placeholder={`Reply to ${chatInfo?.author?.name}...`}
                  className="flex-1 h-10 text-xs rounded-md w-full"
                />

                <Button
                  loading={loading}
                  onClick={handleSendMessage}
                  icon={<Send className="w-4 h-4" />}
                  className="bg-blue-600 hover:bg-blue-700 text-white h-10 px-4 text-xs rounded-md flex items-center gap-1 font-medium"
                >
                  Send
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChatWindow;
