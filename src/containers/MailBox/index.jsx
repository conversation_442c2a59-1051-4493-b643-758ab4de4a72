import {
  Button,
  Checkbox,
  Input,
  message,
  notification,
  Row,
  Segmented,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import MailBoxItem from './MailBoxItem';
import { deleteMailBox, getListMailBox } from '../../services/mailBox';
import { Spin } from 'antd/lib';
import { LeftOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons';
import DetailMailBox from './DetailMailBox';
import FormCreateEmail from './FormCreateEmail';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import LinkedinMailbox from './LinkedinMailbox';
import MyLeadsMailbox from './MyLeadsMailbox';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../components/RadixTabs';
import {
  Archive,
  Forward,
  Frown,
  Link,
  Mail,
  MoreVertical,
  Paperclip,
  Plus,
  Reply,
  Search,
  Star,
  Trash2,
} from 'lucide-react';
import { ScrollArea } from '../../components/ScrollArea';
import dayjs from 'dayjs';
import clsx from 'clsx';
import { v4 } from 'uuid';

const TABS = {
  MAILBOX: 'Mailbox',
  LINKEDIN: 'Linkedin',
  MYLEADS: 'My Leads',
};

const MailBoxTab = () => {
  const [searchParams] = useSearchParams();
  const tabId = searchParams.get('tab');

  const [tab, setTab] = useState(tabId ? TABS[tabId] : TABS.MAILBOX);
  const [rawDataEmailList, setRawDataEmailList] = useState([]);
  const [dataEmailList, setDataEmailList] = useState([]);
  const [hasMoreEmails, setHasMoreEmails] = useState(true);
  const [nextPageToken, setNextPageToken] = useState();
  const [currentPage, setCurrentPage] = useState(0);
  const [nextPageTokenArr, setNextPageTokenArr] = useState([null]);
  const [loading, setLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();
  const [selectedValue, setSelectedValue] = useState();
  const [openEmailForm, setOpenEmailForm] = useState(false);
  const [linkMailBoxStatus, setLinkMailBoxStatus] = useState(false);
  const [listChecked, setListChecked] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const navigate = useNavigate();

  const emailScrollRef = useRef(null);

  const apiUrl = import.meta.env.VITE_API_URL;

  const handleGetDataEmail = async (nextPage, limit) => {
    try {
      const { data } = await getListMailBox(nextPage, limit);
      if (data) {
        setDataEmailList([...dataEmailList, ...(data?.result?.data || [])]);
        setRawDataEmailList([
          ...rawDataEmailList,
          ...(data?.result?.data || []),
        ]);
        setNextPageToken(data?.result?.nextCursor);
        setNextPageTokenArr([...nextPageTokenArr, data?.result?.nextCursor]);
      }
      if (!data || data?.result?.data?.length === 0) {
        setHasMoreEmails(false);
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
      if (e.response.data.message === 'Not have permission') {
        setLinkMailBoxStatus(true);
        // notification.error({
        //   message: 'Error!',
        //   description:
        //     'You do not have permission, Please link to your Mailbox',
        // });
      }
      messageApi.destroy();
    }
  };

  useEffect(() => {
    setLoading(true);
    handleGetDataEmail(null, 10);
  }, []);

  const handleGetNextPage = async () => {
    setCurrentPage(+currentPage + 1);
    await handleGetDataEmail(nextPageToken, 10);
  };

  const handleGetPrevPage = async () => {
    setCurrentPage(+currentPage - 1);
    const newArr = nextPageTokenArr.slice(0, nextPageTokenArr.length - 1);
    setNextPageTokenArr(newArr);
    await handleGetDataEmail(nextPageTokenArr[+currentPage - 1], 10);
  };

  const handleChecked = () => {
    if (selectAll) {
      setListChecked([]);
    } else {
      const idsList = dataEmailList.map(function (item) {
        return item.id;
      });
      setListChecked(idsList);
    }
    setSelectAll(!selectAll);
  };

  const handleDeleteEmails = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await deleteMailBox({
        threadIds: listChecked,
      });
      if (data) {
        messageApi.destroy();
        setLoading(false);
        handleGetDataEmail(null, 10);
        setListChecked([]);
      }
    } catch (e) {
      if (e.response.data.message === 'Not have permission') {
        setLinkMailBoxStatus(true);
      }
      messageApi.destroy();
    }
  };

  return (
    <div className="w-full h-full flex flex-col gap-5 item-center no-padding">
      {contextHolder}
      {/* Tabs Nav */}
      <div className="bg-white border-b border-slate-200 py-3 px-6">
        <Tabs value={tab} onValueChange={setTab} className="w-full">
          <TabsList className="grid w-fit grid-cols-3 bg-slate-100 p-1 h-10 rounded-lg">
            {[TABS.MAILBOX, TABS.LINKEDIN, TABS.MYLEADS].map((tab) => (
              <TabsTrigger
                key={tab}
                value={tab}
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md px-5 font-medium text-[13.5px] rounded-md h-full"
              >
                {tab}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      <div>
        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden p-1.5 bg-slate-100">
          <Tabs
            value={tab}
            onValueChange={setTab}
            className="w-full h-full flex flex-col"
          >
            {/* Mailbox Tab Content */}
            <TabsContent
              value={TABS.MAILBOX}
              className="flex-1 overflow-hidden m-0"
            >
              {linkMailBoxStatus && (
                <>
                  <div className="w-full h-full flex items-center justify-center min-h-[60vh]">
                    <div>
                      <div
                        style={{
                          fontSize: '25px',
                          textAlign: 'center',
                          fontWeight: '700',
                          marginTop: '20px',
                        }}
                        className="flex items-center justify-center gap-2"
                      >
                        <Frown size={50} />
                        No Access!
                      </div>
                      <div
                        style={{
                          fontSize: '20px',
                          textAlign: 'center',
                          marginTop: '20px',
                        }}
                      >
                        Please click on the Button below to link your account to
                        the Mailbox
                      </div>
                      <div className="flex items-center justify-center mt-10 mb-10">
                        <Button
                          onClick={() =>
                            (window.location.href = `${apiUrl}/emails/auth-mailbox/user/${getUserViewAs()}`)
                          }
                          type="primary"
                          className="flex items-center gap-1"
                          icon={<Link className="w-4 h-4" />}
                        >
                          Link with Mailbox
                        </Button>
                      </div>
                    </div>
                  </div>
                </>
              )}
              {!linkMailBoxStatus && (
                <div className="w-full h-full flex overflow-hidden rounded-xl shadow-sm bg-white min-h-[60vh]">
                  {/* Email List Pane */}
                  <div className="w-2/5 border-r border-slate-200 flex flex-col max-h-[60rem]">
                    <div className="p-4 border-b border-slate-200 bg-slate-50/70">
                      <div className="flex items-center justify-between mb-3">
                        <Button
                          onClick={() => setOpenEmailForm(true)}
                          className="flex items-center bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 shadow-sm h-9 text-xs text-white font-medium"
                        >
                          <Plus className="w-3.5 h-3.5 mr-1.5" /> Compose
                        </Button>
                        <Button
                          onClick={() => navigate(`/settings?mailbox=true`)}
                          type="dashed"
                          className="text-cyan-600 border-cyan-300 hover:bg-cyan-50 h-9 text-xs px-3"
                        >
                          Unlink Mailbox
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <div className="relative flex-1">
                          <Input.Search
                            allowClear
                            onChange={(e) => {
                              const value = e.target.value;
                              if (value) {
                                const filteredEmails = rawDataEmailList.filter(
                                  (email) =>
                                    email?.subject
                                      ?.toLowerCase()
                                      .includes(value.toLowerCase()) ||
                                    email?.snippet
                                      ?.toLowerCase()
                                      .includes(value.toLowerCase())
                                );
                                setDataEmailList(filteredEmails);
                              } else {
                                setDataEmailList(rawDataEmailList);
                              }
                            }}
                            placeholder="Search emails..."
                            className="h-9 text-xs border-slate-300 focus:border-cyan-500 focus:ring-cyan-500 rounded-md"
                            enterButton={
                              <Search className="transform text-slate-400 w-3.5 h-3.5" />
                            }
                          />
                        </div>
                      </div>
                    </div>
                    <Row
                      className="flex-1 max-h-[58vh] overflow-y-auto"
                      ref={emailScrollRef}
                      onScroll={(e) => {
                        const { scrollTop, scrollHeight, clientHeight } =
                          e.currentTarget;
                        // Check if scrolled to the bottom
                        if (scrollTop + clientHeight >= scrollHeight - 5) {
                          if (hasMoreEmails && !loading) {
                            setLoading(true);
                            handleGetNextPage();
                          }
                        }
                      }}
                    >
                      <div className="divide-y divide-slate-100">
                        {dataEmailList.map((email) => (
                          <div
                            key={v4()}
                            className={clsx(
                              `p-4 cursor-pointer hover:bg-slate-50/70 transition-all duration-150`,
                              selectedValue?.id === email?.id &&
                                'bg-cyan-50/50 border-r-2 !border-r-cyan-500'
                            )}
                            onClick={() => setSelectedValue(email)}
                          >
                            <div className="flex items-start justify-between mb-1.5">
                              <div className="flex items-center gap-2">
                                <div
                                  className={`w-2 h-2 rounded-full ${!email.unread ? 'bg-slate-300' : 'bg-cyan-500 shadow-sm'}`}
                                ></div>
                                <span
                                  className={`text-xs font-semibold ${!email.unread ? 'text-slate-600' : 'text-slate-800'}`}
                                >
                                  {email?.latestDraftOrMessage?.from?.[0]?.name}
                                </span>
                                {email?.starred && (
                                  <Star className="w-3.5 h-3.5 text-amber-400 fill-current" />
                                )}
                              </div>
                              <div className="flex items-center gap-1.5">
                                {email?.attachments?.length > 0 && (
                                  <Paperclip className="w-3.5 h-3.5 text-slate-400" />
                                )}
                                <span className="text-[11px] text-slate-500 font-medium">
                                  {dayjs(email?.date).format(
                                    'MMM D, YYYY h:mm A'
                                  )}
                                </span>
                              </div>
                            </div>
                            <h3
                              className={`text-xs mb-1 line-clamp-1 ${!email.unread ? 'text-slate-600' : 'text-slate-800 font-medium'}`}
                            >
                              {email?.subject}
                            </h3>
                            <p className="text-xs text-slate-500 line-clamp-2 leading-relaxed">
                              {email?.snippet}
                            </p>
                          </div>
                        ))}
                        {loading && (
                          <div className="p-4 text-center text-xs text-slate-500 flex items-center gap-2">
                            Loading more emails... <Spin size="small" />
                          </div>
                        )}
                        {!hasMoreEmails && dataEmailList?.length > 0 && (
                          <div className="p-4 text-center text-xs text-slate-400">
                            No more emails
                          </div>
                        )}
                      </div>
                    </Row>
                    <div className="p-3 border-t border-slate-200 bg-slate-50/70 flex items-center justify-between text-xs">
                      <span>Showing {dataEmailList?.length} emails</span>
                      {loading && (
                        <Spin size="small" className="text-slate-500" />
                      )}
                    </div>
                  </div>
                  {/* Email Detail Pane */}
                  <div className="flex-1 bg-white flex flex-col overflow-hidden">
                    {selectedValue ? (
                      <DetailMailBox
                        setSelectedValue={setSelectedValue}
                        selectedValue={selectedValue}
                        dataEmailList={dataEmailList}
                        setDataEmailList={setDataEmailList}
                      />
                    ) : (
                      <div className="h-full flex items-center justify-center text-slate-500">
                        <div className="text-center">
                          <Mail className="w-12 h-12 mx-auto mb-3 text-slate-300" />
                          <p className="text-sm">Select an email to view</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* LinkedIn Tab Content */}
            <TabsContent
              value={TABS.LINKEDIN}
              className="flex-1 overflow-hidden m-0"
            >
              <LinkedinMailbox />
            </TabsContent>
            {/* My Leads Tab Content */}
            <TabsContent
              value={TABS.MYLEADS}
              className="flex-1 overflow-hidden m-0"
            >
              <MyLeadsMailbox />
            </TabsContent>
          </Tabs>
        </div>
        {/* Compose Email Form */}
        {openEmailForm && (
          <FormCreateEmail setOpenEmailForm={setOpenEmailForm} />
        )}
      </div>
    </div>
  );
};

export default MailBoxTab;
