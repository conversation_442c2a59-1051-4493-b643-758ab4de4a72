import {
  Button,
  Checkbox,
  Image,
  Input,
  Rate,
  message,
  notification,
} from 'antd';
import React, { useEffect, useState } from 'react';
import Logo from '../../assets/img/files.png';
import dayjs from 'dayjs';
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  RollbackOutlined,
} from '@ant-design/icons';
import { getDetailMailBox, handleSendMailBox } from '../../services/mailBox';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'react-hook-form';
import {
  Archive,
  Forward,
  Mail,
  MoreVertical,
  Paperclip,
  Plus,
  Reply,
  Search,
  Send,
  Star,
  Trash2,
} from 'lucide-react';
import { ScrollArea } from '../../components/ScrollArea';

const DetailMailBoxItem = ({ item, handleGetDetailEmail, subject }) => {
  const [dataDetailMailBox, setDataDetailMailBox] = useState();
  const [sendLoading, setSendLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [createReplyStatus, setCreateReplyStatus] = useState(false);
  const { getValues, setValue } = useForm();

  const handleSendEmail = async () => {
    if (!getValues('content')) {
      notification.error({
        message: 'Error',
        description: 'Content is required',
      });
      return;
    }
    try {
      setSendLoading(true);
      const { data } = await handleSendMailBox({
        recipients: [
          {
            email: item?.data?.to?.[0]?.email,
          },
        ],
        subject: item?.data?.subject,
        content: getValues('content'),
        replyTo: [
          {
            email: getValues('email') ?? item?.data?.to?.[0]?.email,
          },
        ],
        replyToMessageId: item?.data?.id,
      });
      if (data) {
        notification.success({
          message: 'Successfully',
          description: 'Sent mail successfully',
        });
        handleGetDetailEmail(data?.result?.data?.id);
        messageApi.destroy();
        setValue('content', null);
      }
      setSendLoading(false);
    } catch (e) {
      setSendLoading(false);
      messageApi.destroy();
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  return (
    <>
      <div className="p-5 border-b border-slate-200 bg-slate-50/70">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h2 className="text-base font-bold text-slate-800 mb-1 leading-tight">
              {subject}
            </h2>
            <div className="flex items-center gap-2 text-xs text-slate-500">
              <span className="font-medium text-slate-700">
                {item?.data?.from?.[0]?.email}
              </span>
              <span>•</span>
              <span>
                {dayjs(item?.data?.date).format('MMM D, YYYY h:mm A')}
              </span>
            </div>
          </div>
          {/* <div className="flex items-center gap-1">
            <Button
              type="text"
              className="h-8 w-8 hover:bg-slate-200"
              icon={<Reply className="w-4 h-4" />}
            />
            <Button
              type="text"
              className="h-8 w-8 hover:bg-slate-200"
              icon={<Forward className="w-4 h-4" />}
            />
            <Button
              type="text"
              className="h-8 w-8 hover:bg-slate-200"
              icon={<Archive className="w-4 h-4" />}
            />
            <Button
              type="text"
              className="h-8 w-8 hover:bg-slate-200"
              icon={<Trash2 className="w-4 h-4" />}
            />
          </div> */}
        </div>
      </div>
      <div className="flex-1 p-5 max-h-[50vh] overflow-y-auto">
        <div
          dangerouslySetInnerHTML={{
            __html: item?.data?.body,
          }}
          className="prose prose-sm max-w-none text-slate-700 leading-relaxed whitespace-pre-wrap"
        ></div>
      </div>
      <div className="p-4 border-t border-slate-200 bg-slate-50/70">
        <div className="flex items-center gap-2">
          <TextArea
            rows={2}
            onChange={(e) => setValue('content', e.target.value)}
            placeholder="Type your reply..."
            className="flex-1 h-10 text-xs rounded-md"
          />
          {/* <Button
              type="text"
              variant="ghost"
              size="icon"
              className="h-10 w-10"
            >
              <Paperclip className="w-4 h-4" />
            </Button> */}
          <Button
            loading={sendLoading}
            onClick={() => handleSendEmail()}
            icon={<Send className="w-3.5 h-3.5" />}
            className="flex items-center bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 h-10 px-4 text-xs rounded-md text-white font-medium"
          >
            Send
          </Button>
        </div>
      </div>

      {/* <div key={item?.data?.id}>
        <div
          style={{ marginTop: '20px', display: 'flex', alignItems: 'center' }}
        >
          <div>
            <div
              style={{
                width: '50px',
                height: '50px',
                background: 'blue',
                borderRadius: '50%',
              }}
            ></div>
          </div>
          <div style={{ marginLeft: '20px' }}>
            <div>
              <span style={{ fontSize: '16px', fontWeight: '600' }}>
                {item?.data?.from?.[0]?.name}
              </span>{' '}
              <span
                style={{
                  fontSize: '13px',
                  color: '#a3a3a3',
                  marginLeft: '10px',
                }}
              >{`< ${item?.data?.from?.[0]?.email} >`}</span>
            </div>
            <div>
              <span>To</span>{' '}
              <span
                style={{
                  fontSize: '13px',
                  color: '#a3a3a3',
                  marginLeft: '10px',
                }}
              >{`< ${item?.data?.to?.[0]?.email} >`}</span>
            </div>
          </div>
        </div>
        <div style={{ marginTop: '30px' }}>
          <div
            dangerouslySetInnerHTML={{
              __html: item?.data?.body,
            }}
          />
        </div>
      </div> */}
      {/* {!createReplyStatus && (
        <div
          style={{
            marginTop: '20px',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <div></div>
          <Button
            type="text"
            onClick={() => setCreateReplyStatus(true)}
            icon={<RollbackOutlined />}
          >
            Reply
          </Button>
        </div>
      )}
      {createReplyStatus && (
        <div
          style={{
            marginTop: '50px',
            boxShadow: '5px 8px 20px 0px #a3a3a3',
            padding: '20px',
            borderRadius: '15px',
          }}
        >
          <div>
            <span
              style={{
                fontSize: '14px',
                color: '#000',
                marginLeft: '10px',
              }}
            >
              <Input
                prefix={'Send to: '}
                style={{
                  border: 'none',
                  borderBottom: '1px solid #ccc',
                  borderRadius: '0',
                  height: '40px',
                  outline: 'none',
                }}
                defaultValue={item?.data?.to?.[0]?.email}
                onChange={(e) => setValue('email', e.target.value)}
                placeholder="Email"
              />
            </span>
          </div>
          <div style={{ marginTop: '10px' }}>
            <TextArea
              onChange={(e) => setValue('content', e.target.value)}
              style={{ height: '200px', resize: 'none', border: 'none' }}
            />
          </div>
          <div
            style={{
              marginTop: '20px',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Button onClick={() => handleSendEmail()} type="primary">
              Send
            </Button>
            <DeleteOutlined
              onClick={() => setCreateReplyStatus(false)}
              style={{ fontSize: '16px' }}
            />
          </div>
        </div>
      )} */}
    </>
  );
};

export default DetailMailBoxItem;
