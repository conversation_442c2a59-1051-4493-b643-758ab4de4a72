import { useInfiniteQuery } from '@tanstack/react-query';
import { message, notification, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { getListLeadMailBox } from '../../services/myLead';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import LeadCard from '../../components/JobsLeads/HorizontalLeadCard';
import { useSearchParams } from 'react-router-dom';
import {
  GatewayOutlined,
  GoldOutlined,
  PayCircleFilled,
} from '@ant-design/icons';
import { getJobDetail, getSentJob } from '../../services/jobLeads';
import clsx from 'clsx';
import BullHornJobSubmissionModal from '../../components/BullHorn/BullHornJobSubmissionModal';
import { stripJobDescription } from '../../utils/common';
import { generateJobSkills } from '../../services/search';

const PAGE_LIMIT = 20;

const LEAD_TYPE = {
  EXISTING_LEAD: 'EXISTING_LEAD',
  NEW_LEAD: 'NEW_LEAD',
};

const MyLeadsMailbox = () => {
  const userId = getUserViewAs();
  const [searchParams] = useSearchParams();
  const notiJobBoardId = searchParams.get('leadJobBoardId');
  const inMailType = searchParams.get('inMailType');
  const selectedLeadRef = useRef(null);

  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(
    inMailType || LEAD_TYPE.EXISTING_LEAD
  );
  const [refresh, setRefresh] = useState(false);
  const [isShowInMailbox, setIsShowInMailbox] = useState(!!notiJobBoardId);

  const [messageApi, contextHolder] = message.useMessage();
  const [dataInMailBox, setDataMailBox] = useState([]);
  const [jobToSync, setJobToSync] = useState();
  const [isModalOpen, setModalOpen] = useState(false);
  const [sentJobId, setSentJobId] = useState();
  const [sentJodIdCheck, setSentJodIdCheck] = useState();
  const [actionKey, setActionKey] = useState();

  const { ref, inView } = useInView({
    threshold: 0,
  });

  const { data, fetchNextPage, hasNextPage, isLoading, refetch } =
    useInfiniteQuery(['GET_LIST_LEAD_MAIL_BOX', userId], getListLeadMailBox, {
      getNextPageParam: (lastPage, allPages) => {
        const nextPage =
          lastPage?.data?.result?.items?.length >= PAGE_LIMIT
            ? allPages.length + 1
            : undefined;
        return nextPage;
      },
      // refetchOnWindowFocus: false,
      enabled: !refresh,
    });

  const handleAfterUpdateStatusCompleted = () => {
    // handleUpdateCountLeadAfterChangeStatusToDone();
    refetch();
  };

  const handleSentJob = async () => {
    try {
      setLoading(true);
      const { data } = await getSentJob();
      const descData = [...data?.result?.items].sort(
        (a, b) => new Date(b.dateAdded) - new Date(a.dateAdded)
      );
      setDataMailBox([...descData]);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error', error);
    }
  };

  const handleSyncJob = async (id, key) => {
    try {
      setSentJobId(id);
      messageApi.open({
        type: 'loading',
        content: 'Loading ...',
        duration: 0,
      });
      const { data } = await getJobDetail(id);

      const job = {
        ...data.result.item,
        description: stripJobDescription(data.result.item.description),
      };

      if (data) {
        if (!job?.skills || job?.skills?.length === 0) {
          setModalOpen(true);
          const { data: skillsData } = await generateJobSkills(job?.job_id);
          const jobToSyncWithSkills = {
            ...job,
            skills: skillsData?.result || [],
          };
          setActionKey(key);
          setJobToSync(jobToSyncWithSkills);
        } else {
          setJobToSync(job);
          setModalOpen(true);
          setActionKey(key);
        }
        messageApi.destroy();
      }
    } catch (err) {
      notification.error({
        messages: 'Something went wrong',
      });
      messageApi.destroy();
    }
  };

  const executeScroll = () =>
    selectedLeadRef.current.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });

  useEffect(() => {
    handleSentJob();
  }, []);

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, fetchNextPage]);

  useEffect(() => {
    if (
      dataInMailBox?.length > 0 &&
      notiJobBoardId &&
      selectedLeadRef.current
    ) {
      executeScroll();
    }
  }, [dataInMailBox, notiJobBoardId]);

  return (
    <div
      className="w-full h-full flex justify-center px-10"
      style={{ minHeight: '700px' }}
    >
      {contextHolder}
      {(isLoading || loading) && <Spin />}

      {!isLoading && (
        <div className="w-full flex flex-col gap-5 items-center">
          <div className="flex items-center justify-center gap-5 py-3 px-20 rounded-md bg-white shadow-sm w-fit">
            {/* <div className="text-lg font-semibold">Lead Type</div> */}
            <div
              className={clsx(
                'flex items-center gap-2 cursor-pointer font-medium border rounded-md p-4',
                selectedType === LEAD_TYPE.EXISTING_LEAD && 'border-cyan-400'
              )}
              onClick={() => setSelectedType(LEAD_TYPE.EXISTING_LEAD)}
            >
              <GatewayOutlined className="text-orange-400 text-xl" />
              <span>Existing Lead</span>
            </div>
            <div
              className={clsx(
                'flex items-center gap-2 cursor-pointer font-medium border rounded-md p-4',
                selectedType === LEAD_TYPE.NEW_LEAD && 'border-cyan-400'
              )}
              onClick={() => setSelectedType(LEAD_TYPE.NEW_LEAD)}
            >
              <GoldOutlined className="text-green-600 text-xl" />
              <span>New Lead</span>
            </div>
          </div>
          {/* Existing leads */}
          {selectedType === LEAD_TYPE.EXISTING_LEAD && (
            <div className="w-full">
              <div className="text-[#7c7c7c] font-medium mb-2">
                {'Existing Leads'.toUpperCase()}
              </div>
              <div
                className="existing-leads-container"
                style={{ maxHeight: '700px' }}
              >
                {isLoading && (
                  <Spin
                    style={{
                      float: 'right',
                      marginTop: '-25px',
                      marginRight: '10px',
                    }}
                  />
                )}
                {data?.pages?.map((page) =>
                  page?.data?.result?.items?.map((item) => (
                    <div key={item.id} className="mb-4">
                      <LeadCard
                        setRefresh={setRefresh}
                        refresh={refresh}
                        fromExisting={true}
                        lead={item}
                        isShowInMailbox={isShowInMailbox}
                        handleAfterUpdateStatusCompleted={
                          handleAfterUpdateStatusCompleted
                        }
                        reloadJobLeads={() => refetch()}
                        fromExistingTab={true}
                        horizontal
                      />
                    </div>
                  ))
                )}
                {hasNextPage && (
                  <Row ref={ref}>
                    <Col span={24}>
                      <LoadingAdvanced isSkeleton />
                    </Col>
                  </Row>
                )}
              </div>
            </div>
          )}
          {console.log('dataInMailBox', dataInMailBox)}
          {/* New leads */}
          {selectedType === LEAD_TYPE.NEW_LEAD && (
            <div className="w-full">
              <div className="text-[#7c7c7c] font-medium mb-2">
                {'New Leads'.toUpperCase()}
              </div>
              <div
                className="existing-leads-container"
                style={{ maxHeight: '700px' }}
              >
                {data?.pages?.map((page) =>
                  dataInMailBox?.map((item) => (
                    <div
                      ref={
                        notiJobBoardId === item?.jobBoardId
                          ? selectedLeadRef
                          : null
                      }
                      tabIndex={notiJobBoardId === item?.jobBoardId && 0}
                      autoFocus={notiJobBoardId === item?.jobBoardId}
                      key={item.id}
                      className={clsx(
                        'mb-4 rounded-lg',
                        notiJobBoardId === item?.jobBoardId &&
                          'animate-bounce-short'
                      )}
                    >
                      <LeadCard
                        lead={item}
                        isShowInMailbox={isShowInMailbox}
                        handleAfterUpdateStatusCompleted={
                          handleAfterUpdateStatusCompleted
                        }
                        review={true}
                        onClickSync={handleSyncJob}
                        onClickCheckId={setSentJodIdCheck}
                        reloadJobLeads={() => {
                          handleSentJob();
                        }}
                        horizontal
                      />
                    </div>
                  ))
                )}
                {hasNextPage && (
                  <Row ref={ref}>
                    <Col span={24}>
                      <LoadingAdvanced isSkeleton />
                    </Col>
                  </Row>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {isModalOpen && (
        <BullHornJobSubmissionModal
          job={jobToSync}
          isModalVisible={isModalOpen}
          setIsModalVisible={(value) => {
            setModalOpen(value);
            handleSentJob();
          }}
          sentJobId={sentJodIdCheck}
          actionKey={actionKey}
        />
      )}
    </div>
  );
};

export default MyLeadsMailbox;
