import { Checkbox, Dropdown, Image, Rate, Space, Tooltip, message, notification } from 'antd';
import React, { useState } from 'react';
import Logo from '../../assets/img/files.png';
import dayjs from 'dayjs';
import { deleteMailBox, updateMailBox } from '../../services/mailBox';
import { DeleteOutlined, EllipsisOutlined, MailOutlined } from '@ant-design/icons';

const MailBoxItem = (props) => {
  const { mailBoxItem, setSelectedValue, selectedValue, dataEmailList, setDataEmailList, setListChecked, listChecked, handleGetDataEmail, setLoading } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const handleUpdateStarEmail = async () => {
    const { data } = await updateMailBox(mailBoxItem.id, {
      starred: !mailBoxItem?.starred,
    });

    const itemToUpdate = dataEmailList.find(item => item.id === mailBoxItem?.id);
    if (itemToUpdate) {
      itemToUpdate.starred = !mailBoxItem?.starred
    }
    setDataEmailList([...dataEmailList])
  };

  const handleUpdateUnreadEmail = async () => {
    const { data } = await updateMailBox(mailBoxItem.id, {
      unread: !mailBoxItem?.unread,
    });

    const itemToUpdate = dataEmailList.find(item => item.id === mailBoxItem?.id);
    if (itemToUpdate) {
      itemToUpdate.unread = !mailBoxItem?.unread
    }
    setDataEmailList([...dataEmailList])
  };

  const handelDeleteMailBox = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await deleteMailBox({
        threadIds: [mailBoxItem?.id]
      });
      if (data) {
        messageApi.destroy();
        setLoading(false);
        handleGetDataEmail(null, 10)
        // setListChecked([])
      }
    } catch (e) {
      notification.error({
        message: "Something went wrong",
      })
      messageApi.destroy();
    }
  };

  const handleCheckbox = () => {
    const  index = listChecked.indexOf(mailBoxItem?.id)
    if (index !== -1) {
      listChecked.splice(index, 1);
    } else {
      listChecked.push(mailBoxItem?.id);
    }

    setListChecked([...listChecked])
  }

  const items = [
    {
      label: (
        <Tooltip placement="bottom" title={mailBoxItem?.unread ? "Mark as read" : "Mark as unread"}>
          <div onClick={(e) => { e.stopPropagation(), handleUpdateUnreadEmail()}}>
            <MailOutlined />
          </div>
        </Tooltip>
      ),
      key: '0',
    },
    {
      label: (
        <div onClick={(e) => { e.stopPropagation(), handelDeleteMailBox()}}>
          <DeleteOutlined />
        </div>
      ),
      key: '1',
    },
  ];

  return (
    <div key={mailBoxItem?.id} onClick={() => setSelectedValue(mailBoxItem)}>
      {contextHolder}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          borderLeft: '1px solid #ccc',
          borderRight: '1px solid #ccc',
          borderBottom: '1px solid #ccc',
          padding: '10px',
          background: mailBoxItem?.unread ? "#fff" : '#eeeeee'
        }}
      >
        <div style={{ width: '2%' }}>
          <Checkbox onClick={(e) => { e.stopPropagation(), handleCheckbox()}} checked={listChecked.includes(mailBoxItem?.id)}></Checkbox>
        </div>
        <div style={{ width: '2%' }}>
          <Rate
            onClick={(e) => {
              e.stopPropagation();
              handleUpdateStarEmail();
            }}
            defaultValue={mailBoxItem?.starred ? 1 : null}
            count={1}
          />
        </div>
        {/* <div style={{ width: '5%' }}>
          <Image
            src={Logo}
            preview={false}
            style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              objectFit: 'contain',
              marginLeft: '10px',
            }}
          />
        </div> */}
        <div style={{ width: '15%', fontSize: '14px' }}>
          {mailBoxItem?.latestDraftOrMessage?.from?.[0]?.name}
        </div>
        <div
          style={{
            width: '25%',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            fontSize: '15px',
          }}
        >
          {mailBoxItem?.latestDraftOrMessage?.subject}
        </div>
        <div
          style={{
            width: '40%',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            fontSize: '13px',
            color: '#a3a3a3',
          }}
        >
          {mailBoxItem?.latestDraftOrMessage?.snippet}
        </div>
        <div style={{ width: '10%', fontSize: '13px', color: '#a3a3a3' }}>
          {dayjs
            .unix(mailBoxItem?.latestDraftOrMessage?.createdAt || mailBoxItem?.latestDraftOrMessage?.date)
            .format('HH:mm - YYYY/MM/DD')}
        </div>
        <div style={{ width: '3%', fontSize: '13px', color: '#000' }}>
          <Dropdown menu={{ items }} trigger={['click']}>
            <a onClick={(e) =>  e.stopPropagation()}>
              <EllipsisOutlined />
            </a>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default MailBoxItem;
