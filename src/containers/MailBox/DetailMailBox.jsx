import {
  Button,
  Checkbox,
  Image,
  Rate,
  Spin,
  message,
  notification,
} from 'antd';
import React, { useEffect, useState } from 'react';
import Logo from '../../assets/img/files.png';
import dayjs from 'dayjs';
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  RollbackOutlined,
} from '@ant-design/icons';
import {
  getDetailMailBox,
  handleSendMailBox,
  updateMailBox,
} from '../../services/mailBox';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'react-hook-form';
import DetailMailBoxItem from './DetailMailBoxItem';

const DetailMailBox = (props) => {
  const { setSelectedValue, selectedValue, dataEmailList, setDataEmailList } =
    props;
  const [dataDetailMailBox, setDataDetailMailBox] = useState();
  const [loading, setLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();
  const [createReplyStatus, setCreateReplyStatus] = useState(false);
  const { getValues, setValue } = useForm();

  const handleEffectData = async () => {
    const { data } = await updateMailBox(selectedValue.id, {
      unread: false,
    });
    const itemToUpdate = dataEmailList?.find(
      (item) => item.id === selectedValue?.id
    );
    if (itemToUpdate) {
      itemToUpdate.unread = false;
    }
    setDataEmailList([...dataEmailList]);
  };

  useEffect(() => {
    handleEffectData();
  }, [selectedValue]);

  const handleSendEmail = async () => {
    if (!getValues('content')) {
      notification.error({
        message: 'Error',
        description: 'Content is required',
      });
      return;
    }
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading ...',
        duration: 0,
      });
      const { data } = await handleSendMailBox({
        recipients: [
          {
            email: selectedValue?.latestDraftOrMessage?.to?.[0]?.email,
          },
        ],
        subject: selectedValue?.latestDraftOrMessage?.subject,
        content: getValues('content'),
        replyTo: [
          {
            email: selectedValue?.latestDraftOrMessage?.to?.[0]?.email,
          },
        ],
        replyToMessageId: selectedValue?.id,
      });
      if (data) {
        notification.success({
          message: 'Successfully',
          description: 'Sent mail successfully',
        });
        messageApi.destroy();
        setValue('content', null);
        setCreateReplyStatus(false);
      }
    } catch (e) {
      messageApi.destroy();
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleGetDetailEmail = async (id = null) => {
    setLoading(true);
    try {
      const baseMessages = selectedValue?.messageIds ?? [];
      if (id) {
        baseMessages.push(id);
      }
      const { data } = await getDetailMailBox({
        messageIds: baseMessages,
      });

      setDataDetailMailBox(data?.result?.messages);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching email details:', error);
      setLoading(false);
      notification.error({
        message: 'Error',
        description: error?.message || 'Failed to fetch email details',
      });
    }
  };

  useEffect(() => {
    handleGetDetailEmail();
  }, [selectedValue]);

  return loading ? (
    <div className="flex items-center justify-center h-full">
      <Spin size="large" />
    </div>
  ) : (
    <>
      {dataDetailMailBox?.map((item) => (
        <DetailMailBoxItem
          item={item}
          handleGetDetailEmail={handleGetDetailEmail}
          subject={selectedValue?.subject}
        />
      ))}
    </>
  );
};

export default DetailMailBox;
