import { Modal } from 'antd';
import { useState } from 'react';
import PurchaseCreditModal from './PurchaseCreditModal';
import styled from 'styled-components';

export const RestyledModal = styled(Modal)`
  .ant-modal-content {
    padding: 0;
  }
`;

const PurchaseCreditSection = ({ handlePurchaseSuccess }) => {
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false);
  const showPurchaseModal = () => setIsPurchaseModalOpen(true);
  const closePurchaseModal = () => setIsPurchaseModalOpen(false);

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-8">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Purchase Credits</h2>
      </div>
      <div className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              Need more credits?
            </h3>
            <p className="text-gray-500">
              Purchase additional credits to add to your company's credit pool.
              When users run out of their allocated credits, they will
              automatically use credits from this pool.
            </p>
          </div>
          <button
            onClick={showPurchaseModal}
            className="px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 whitespace-nowrap"
          >
            Purchase Credits
          </button>
        </div>
      </div>
      {/* Purchase Modal */}
      <RestyledModal
        title={
          <div className="bg-gray-100 py-3 px-4 flex items-center justify-between">
            <h3 className="text-lg font-medium">Purchase Credits</h3>
          </div>
        }
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isPurchaseModalOpen}
        onOk={showPurchaseModal}
        onCancel={closePurchaseModal}
        footer={null}
        maskClosable={false}
      >
        <PurchaseCreditModal onClose={closePurchaseModal} handlePurchaseSuccess={handlePurchaseSuccess} />
      </RestyledModal>
    </div>
  );
};

export default PurchaseCreditSection;
