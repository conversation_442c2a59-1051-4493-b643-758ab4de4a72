import { ArrowUpOutlined, UserOutlined } from '@ant-design/icons';
import React from 'react';

const RoleBox = (props) => {
  const { roleName, totalCredits, creditUsed, growth, selected = true } = props;
  return (
    <div
      className={`w-[24.5%] p-4 border ${selected ? 'border-cyan-600' : 'border-gray-300'} rounded-lg flex h-22`}
    >
      <div
        className={`w-10 h-8 border ${selected ? 'border-cyan-600 text-cyan-600' : 'border-gray-300 text-black'} rounded-lg flex items-center justify-center`}
      >
        <UserOutlined />
      </div>
      <div className="ml-2.5 w-[70%]">
        <div className={`text-sm font-medium ${selected ? 'text-cyan-600' : 'text-black'}`}>
          {roleName}
        </div>
        <div className={`text-2xl font-bold ${selected ? 'text-cyan-600' : 'text-black'}`}>
          {creditUsed} / {totalCredits}
        </div>
      </div>
      {/* <div
        className={`mt-9 w-16 h-5 border ${selected ? 'border-cyan-600' : 'border-gray-300'} rounded-md flex justify-center items-center text-green-500 text-xs`}
      >
        <ArrowUpOutlined className="text-xs" />
        <span className="ml-1">{growth}%</span>
      </div> */}
    </div>
  );
};

export default RoleBox;