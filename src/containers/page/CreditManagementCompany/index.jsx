import React, { useEffect, useState } from 'react';
import { Spin, Result, Button } from 'antd';
import { useParams } from 'react-router-dom';
import {
  getCreditManagementDetail,
  getUserOrganizationDetail,
} from '../../../services/users';
import CreditCompanyDetail from './CreditCompanyDetail';
import {
  getUserViewAs,
  getUserViewAsOrganizationId,
  getUserViewAsRole,
} from '../../../helpers/getUserViewAs';
import { SALES, SUPER_ADMIN } from '../../../constants/common.constant';

const CreditManagementCompany = (props) => {
  const { companyId } = props;
  const [loading, setLoading] = useState(true);
  const [initData, setInitData] = useState([]);
  const [errorCheckCompany, setErrorCheckCompany] = useState(false);
  const currentUserRole = getUserViewAsRole();
  const isSuperAdmin =
    currentUserRole === SUPER_ADMIN || currentUserRole === SALES;
  // const companyId = getUserViewAsOrganizationId();
  // let { companyId } = useParams();

  const NoAccess = () => {
    return (
      <Result
        status="403"
        title="403"
        subTitle="Sorry, you are not authorized to access this page."
      />
    );
  };

  const handleGetDetailOrg = async () => {
    try {
      const id = isSuperAdmin ? companyId : getUserViewAsOrganizationId();
      const data = await getCreditManagementDetail(id);
      console.log('data', data);
      setInitData(data?.data?.result);
      if (data) {
        setLoading(false);
      }
    } catch (e) {
      console.log('err handleGetDetailOrg', e);
      setErrorCheckCompany(true);
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGetDetailOrg();
  }, []);

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (errorCheckCompany) {
    return <NoAccess />;
  }

  return (
    <div>
      <CreditCompanyDetail
        initData={initData}
        companyId={isSuperAdmin ? companyId : getUserViewAsOrganizationId()}
        handleRefetchData={handleGetDetailOrg}
      />
    </div>
  );
};

export default CreditManagementCompany;
