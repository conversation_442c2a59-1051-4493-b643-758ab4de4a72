import React, { useEffect, useState } from 'react';
import { Statistic, Row, Col, Segmented, Input, Skeleton } from 'antd';
import {
  ArrowUpOutlined,
  EyeOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import RoleBox from '../RoleBox';
import { DatePicker, Space } from 'antd';
import CreditTable from '../CreditTable';
import PurchaseCreditSection from '../PurchaseCreditSection';
import CreditSummary from '../CreditSummary';
import { useQuery } from '@tanstack/react-query';
import { getUserOrganizationDetail } from '../../../../services/users';

const { RangePicker } = DatePicker;

// Helper function for role colors
const getRoleColor = (role) => {
  switch (role) {
    case 'Admin':
      return 'text-purple-600';
    case 'Management':
      return 'text-blue-600';
    case 'Basic User':
      return 'text-amber-600';
    default:
      return 'text-gray-600';
  }
};

// Helper function for progress bar colors
const getProgressColor = (used, total) => {
  if (total === 0) return 'bg-gray-500';
  const percentage = (used / total) * 100;
  if (percentage < 50) return 'bg-teal-500';
  if (percentage < 75) return 'bg-amber-500';
  return 'bg-red-500';
};

const CreditCompanyDetail = (props) => {
  const { initData, companyId, handleRefetchData } = props;
  const [dataSource, setDataSource] = useState([]);
  const [searchValue, setSearchValue] = useState('');

  const {
    data: organizationData,
    isFetching,
    refetch,
  } = useQuery(
    ['GET_ORGANIZATION_DETAIL', companyId],
    async () => {
      if (!companyId) return;
      const { data } = await getUserOrganizationDetail(companyId);
      return data?.result;
    },
    { enabled: !!companyId }
  );

  useEffect(() => {
    if (initData) {
      const baseData = initData?.staffs
        ?.filter(
          (v) =>
            v?.email
              ?.toString()
              .toLocaleLowerCase()
              .includes(searchValue.toLocaleLowerCase()) ||
            v?.userName
              ?.toString()
              .toLocaleLowerCase()
              .includes(searchValue.toLocaleLowerCase())
        )
        ?.map((item) => ({
          id: item?.id,
          email: item?.email,
          role: item?.role?.name,
          userName: item?.username,
          totalCredits: item?.creditManagement?.originalAmount || 0,
          creditUsed: item?.creditManagement?.remaining || 0,
        }));
      setDataSource(baseData);
    }
  }, [initData, searchValue]);

  return (
    <div className="flex flex-col gap-2 w-full p-2">
      {isFetching && (
        <Skeleton className="mb-3" active paragraph={{ rows: 3 }} />
      )}
      {!isFetching && organizationData && (
        <div className="mb-3">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <span className="p-1 bg-teal-100 rounded text-teal-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect width="20" height="14" x="2" y="5" rx="2" />
                <line x1="2" x2="22" y1="10" y2="10" />
              </svg>
            </span>
            Credit Management
          </h1>
          <div className="text-gray-500 mt-1 text-sm flex items-center gap-2">
            Manage credits for{' '}
            <p className="font-semibold text-cyan-700">
              {organizationData?.name}
            </p>
          </div>
        </div>
      )}
      {!isFetching && <CreditSummary companyId={companyId} />}
      <PurchaseCreditSection handlePurchaseSuccess={(res) => refetch()} />

      {/* Team Members Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h2 className="text-lg font-medium text-gray-900">
                Team Members
              </h2>
              <span className="bg-gray-100 text-gray-700 text-xs font-medium px-2 py-1 rounded-full">
                {dataSource.length}
              </span>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search team members..."
                className="pl-10 pr-4 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 w-56 text-sm"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
              <svg
                className="absolute left-3 top-2 h-4 w-4 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
        <div className="overflow-x-auto max-h-[30rem] overflow-y-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Credits
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {dataSource.length > 0 ? (
                dataSource.map((staff) => (
                  <tr
                    key={staff.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {staff?.name || staff.userName || staff?.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {staff?.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`font-medium ${getRoleColor(staff?.role)}`}
                      >
                        {staff?.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-500">
                            {staff?.usedCredits || 0}
                          </span>
                          <span className="text-xs text-gray-500">
                            {staff?.totalCredits || 0}
                          </span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${getProgressColor(staff?.creditUse, staff?.totalCredits)}`}
                            style={{
                              width: `${(staff?.usedCredits / staff?.totalCredits) * 100}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-6 py-8 text-center text-gray-500"
                  >
                    No team members found matching your search.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      {/* <div style={{background: "#fff", width: "100%", padding: "20px"}}>
      <div className="text-2xl font-semibold">Credit Management</div>
      <div>Here's an overview of your store credits</div>
        <div className="flex mt-5 justify-between w-full relative">
          <RoleBox
            roleName={"Admin's Remaining Credits"}
            totalCredits={initData?.creditManagementByRole?.ADMIN?.totalOriginalAmount || 0}
            creditUsed={initData?.creditManagementByRole?.ADMIN?.totalRemaining || 0}
            growth={51}
            selected={true}
          />
          <RoleBox
            roleName={"Managements' Remaining Credits"}
            totalCredits={initData?.creditManagementByRole?.MANAGEMENT?.totalOriginalAmount || 0}
            creditUsed={initData?.creditManagementByRole?.MANAGEMENT?.totalOriginalAmount || 0}
            growth={51}
            selected={false}
          />
          <RoleBox
            roleName={"Basic Users' Remaining Credits"}
            totalCredits={initData?.creditManagementByRole?.BASIC_USER?.totalOriginalAmount || 0}
            creditUsed={initData?.creditManagementByRole?.BASIC_USER?.totalRemaining || 0}
            growth={51}
            selected={false}
          />
          <RoleBox
            roleName={"Organization Credits"}
            totalCredits={initData?.organizationCredit?.originalAmount || 0}
            creditUsed={initData?.organizationCredit?.remaining || 0}
            growth={51}
            selected={false}
          />
        </div>
      </div>
      <div className="flex justify-between p-5">
        <div className="flex items-center">
          <div className="text-2xl font-bold">Staffs</div>
          <div className="ml-2.5 flex justify-center items-center w-12 h-5 border border-gray-300 rounded text-xs">
            {dataSource?.length}
          </div>
        </div>
        <div>
          <Input placeholder="Search" className="w-96" onChange={(e) => setSearchValue(e.target.value)}/>
        </div>
      </div>
      <div>
        <CreditTable dataSource={dataSource} companyId={companyId} handleRefetchData={handleRefetchData}/>
      </div> */}
    </div>
  );
};

export default CreditCompanyDetail;
