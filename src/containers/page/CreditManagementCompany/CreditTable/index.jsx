import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { TableWrapper } from '../../../CRM/styled';
import {
  Button,
  Divider,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Popover,
  Select,
  Table,
  notification,
} from 'antd';
import {
  CaretDownOutlined,
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  SendOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { userRole } from '../../../../constants/common.constant';
import { upsertCreditManagementDetail } from '../../../../services/users';
import { useViewAs } from '../../../../store/viewAs';
import { useAuth } from '../../../../store/auth';

const CreditTable = (props) => {
  const { dataSource, companyId, handleRefetchData } = props;
  const [editingKey, setEditingKey] = useState('');
  const [creditChange, setCreditChange] = useState();
  const [loading, setLoading] = useState(false);
  const [loadingRecordId, setLoadingRecordId] = useState('');
  const [openKey, setOpenKey] = useState()

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const account = profileUser || profileUserAuth?.user;

  const isEditing = (record) => record?.key === editingKey;
  const [form] = Form.useForm();

  const isAllDigits = (text) => {
    return text == '' || (!isNaN(text) && Number.isInteger(Number(text)));
  };

  const handleEditCreditManagement = async (recordId) => {
    try {
      if (creditChange?.[recordId]) {
        setLoadingRecordId(recordId);
        const payload = {
          userId: recordId,
          totalCredit: creditChange?.[recordId],
        };

        const { data } = await upsertCreditManagementDetail(companyId, payload);
        setLoadingRecordId('');
        if (data) {
          setLoading(true);
          notification.success({ message: `Update credit for user success` });
          await handleRefetchData();
          setLoading(false);
          setOpenKey(null)
        }
      }
    } catch (e) {
      notification.error({message: "Cannot update credit"})
      setLoadingRecordId('');
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      render: (email) => {
        return <div>{email}</div>;
      },
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      width: '15%',
      render: (role, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {role || '-'}
          </div>
        );
      },
    },
    {
      title: 'Full Name',
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      align: "left",
      render: (userName, record) => {
        return (
          <div className="line-clamp-1" title={userName}>
            {userName}
          </div>
        );
      },
    },
    ...(account.role.keyCode === 'SUPER_ADMIN' ||
    account.role.keyCode === 'ADMIN'
      ? [
          {
            title: 'Credits',
            dataIndex: 'credits',
            key: 'credits',
            width: '25%',
            align: 'center',
            render: (credits, record) => {
              return (
                <div className="font-semibold w-full flex justify-center items-center gap-1">
                  <div className="flex items-center">
                    <div className="w-[100px]">
                      {record?.creditUsed || '0'} /{' '}
                      {record?.totalCredits || '0'}
                    </div>
                    <Popover
                      placement="top"
                      title={false}
                      open={openKey == record?.id}
                      content={
                        <div>
                          <div>
                            Update available credits{' '}
                            <span className="font-semibold">
                              {record?.userName}
                            </span>
                          </div>
                          <div className="w-[230px] border border-gray-300 mt-1"></div>
                          <div className="mt-2">
                            <Input
                              onChange={(e) => {
                                if (isAllDigits(e.target.value)) {
                                  setCreditChange({
                                    ...creditChange,
                                    [record.id]: e.target.value,
                                  });
                                }
                              }}
                              defaultValue={record?.creditUsed}
                              value={
                                creditChange?.[record.id]
                              }
                            />
                          </div>
                          <div className="mt-2 flex">
                            <Button
                              className="h-6 w-[90px] flex items-center justify-center"
                              type="primary"
                              onClick={() =>
                                handleEditCreditManagement(record?.id)
                              }
                              loading={
                                loadingRecordId === record?.id ? true : false
                              }
                            >
                              Save
                            </Button>
                            <Button
                              onClick={() => {
                                  setCreditChange({
                                    ...creditChange,
                                    [record.id]: record?.totalCredits,
                                  })
                                  setOpenKey(null)
                                }
                              }
                              disabled={
                                loadingRecordId === record?.id ? true : false
                              }
                              className="h-6 w-[90px] ml-2 flex items-center justify-center"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      }
                    >
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          setOpenKey(record?.id)
                        }}
                        className="rounded-none w-[70px] flex items-center justify-center"
                        loading={loadingRecordId === record?.id ? true : false}
                      >
                        <EditOutlined />
                      </Button>
                    </Popover>
                  </div>
                </div>
              );
            },
          },
        ]
      : []),
  ];

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <div className="contact-table">
      <TableWrapper className="table-responsive text-gray-800">
        <Form form={form} component={false}>
          <Table
            className="customized-style-pagination w-full"
            rowClassName="editable-row"
            dataSource={dataSource}
            columns={mergedColumns}
            pagination={false}
            loading={loading}
          />
        </Form>
      </TableWrapper>
    </div>
  );
};

export default CreditTable;
