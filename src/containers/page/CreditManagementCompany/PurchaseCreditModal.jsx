import { useEffect, useState } from 'react';
import {
  confirmCreditsTopup,
  getCreditPackages,
  purchaseCreditsTopup,
} from '../../../services/users';
import { Button, notification, Skeleton } from 'antd';
import { getPaymentMethods } from '../../../services/payment';

// Credit packages

const PurchaseCreditModal = ({ onClose, handlePurchaseSuccess }) => {
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [creditPackages, setCreditPackages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [purchaseLoading, setPurchaseLoading] = useState(false);

  const getPackagesData = async () => {
    try {
      const { data } = await getCreditPackages();
      if (data?.result?.packages?.length > 0) {
        setCreditPackages([...data.result.packages]);
      }
      console.log('Credit Packages:', data);
    } catch (error) {
      console.error('Error fetching credit packages:', error);
    }
  };

  const getUserPaymentMethods = async () => {
    try {
      const { data } = await getPaymentMethods();
      console.log('Payment Methods:', data);
      if (data?.result?.paymentMethods?.length > 0) {
        setPaymentMethods(data.result.paymentMethods);
        setSelectedPaymentMethod(data?.result?.paymentMethods?.[0]); // Select the first payment method by default
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    }
  };

  const handlePurchase = async () => {
    setPurchaseLoading(true);
    try {
      const payload = {
        packageId: selectedPackage,
        paymentMethodId: selectedPaymentMethod?.id || 'default_card',
      };
      const { data } = await purchaseCreditsTopup(payload);

      if (data?.result?.paymentIntentId) {
        const confirmPayload = {
          paymentIntentId: data.result.paymentIntentId,
        };
        const { data: confirmedData } =
          await confirmCreditsTopup(confirmPayload);

        console.log('Confirmed Purchase Data:', confirmedData);
        setPurchaseLoading(false);
        notification.success({
          message: 'Purchase Successful',
        });
        onClose();
        handlePurchaseSuccess && handlePurchaseSuccess(data?.result);
      } else {
        notification.error({
          message: 'Purchase Failed',
          description:
            data?.message || 'An error occurred during the purchase.',
        });
        setPurchaseLoading(false);
        return;
      }
    } catch (error) {
      setPurchaseLoading(false);
      console.error('Error during purchase:', error);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([getPackagesData(), getUserPaymentMethods()]);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div>
      <div className="p-4">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Credit Package
          </label>
          {!loading && (
            <div className="grid grid-cols-2 gap-3">
              {creditPackages.map((pkg) => (
                <div
                  key={pkg.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedPackage === pkg.id
                      ? 'border-teal-500 bg-teal-50'
                      : 'border-gray-200 hover:border-teal-300'
                  }`}
                  onClick={() => setSelectedPackage(pkg.id)}
                >
                  <div className="flex justify-between items-center mb-1">
                    <span className="font-medium">{pkg.name}</span>
                    <span
                      className={`text-xs px-2 py-0.5 rounded-full ${
                        selectedPackage === pkg.id
                          ? 'bg-teal-100 text-teal-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {pkg?.price ? `$${pkg.price}` : 'Contact Us'}
                    </span>
                  </div>
                  {pkg?.id === 'enterprise' ? (
                    <div className="text-sm text-gray-500">Contact us</div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      {pkg?.credits?.toLocaleString()} credits
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          {loading && (
            <Skeleton
              active
              paragraph={{
                rows: 3,
                className: 'w-full',
              }}
            />
          )}
        </div>

        <div className="mb-6">
          <div className="border rounded-lg p-4 bg-blue-50 border-blue-100">
            <div className="flex items-start">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-500 mr-2 mt-0.5"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
              </svg>
              <p className="text-sm text-blue-800">
                Purchased credits will be added to your company's credit pool.
                When users run out of their allocated credits, they will
                automatically use credits from this pool.
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Payment Method
          </label>
          {!loading &&
            paymentMethods?.length > 0 &&
            paymentMethods?.map((method) => (
              <div
                className="border rounded-lg p-3 border-teal-500 bg-teal-50 hover:bg-teal-100 cursor-pointer mb-3"
                onClick={() => setSelectedPaymentMethod(method)}
                key={method.id}
              >
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-full border border-teal-500 bg-teal-500 mr-2"></div>
                  <div>
                    <span className="font-medium">
                      {method?.brand} {method?.type}
                    </span>
                    <div className="text-sm text-gray-500 mt-1">
                      <span className="flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <rect
                            x="1"
                            y="4"
                            width="22"
                            height="16"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="1" y1="10" x2="23" y2="10"></line>
                        </svg>
                        {method?.brand} ending in {method?.last4}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          {!loading && paymentMethods?.length === 0 && (
            <div className="text-sm text-gray-500">
              No payment methods available. Please add a payment method to
              proceed.
            </div>
          )}
          {loading && (
            <Skeleton
              active
              paragraph={{ rows: 2, className: 'w-full' }}
              className="mb-3"
            />
          )}
        </div>
        {selectedPackage && selectedPackage !== 'enterprise' && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-2">
              Order Summary
            </div>
            <div className="flex justify-between mb-1">
              <span className="text-sm text-gray-500">
                {creditPackages.find((pkg) => pkg.id === selectedPackage)?.name}{' '}
                Package
              </span>
              <span className="text-sm">
                $
                {
                  creditPackages.find((pkg) => pkg.id === selectedPackage)
                    ?.price
                }
              </span>
            </div>
            <div className="flex justify-between mb-3">
              <span className="text-sm text-gray-500">Credits</span>
              <span className="text-sm">
                {creditPackages
                  .find((pkg) => pkg.id === selectedPackage)
                  ?.credits?.toLocaleString()}
              </span>
            </div>
            <div className="border-t border-gray-200 pt-2 flex justify-between font-medium">
              <span>Total</span>
              <span>
                $
                {
                  creditPackages.find((pkg) => pkg.id === selectedPackage)
                    ?.price
                }
              </span>
            </div>
          </div>
        )}
      </div>
      <div className="flex items-center justify-end gap-3 p-4 bg-gray-50 border-t border-gray-200">
        <Button disabled={loading} onClick={onClose}>
          Cancel
        </Button>
        <Button
          disabled={loading || !selectedPackage}
          loading={purchaseLoading}
          onClick={() => {
            if (selectedPackage === 'enterprise') {
              // Handle enterprise package logic here
              console.log('Contacting for enterprise package');
              window.open(
                `mailto:<EMAIL>?subject=Enterprise%20Credit%20Package%20Inquiry&body=Hello,%0D%0A%0D%0AI am interested in the Enterprise credit package. Please contact me with more information.%0D%0A%0D%0AThank you.`,
                '_blank'
              );
              return;
            }
            // Handle purchase logic here
            handlePurchase();
          }}
          className="bg-teal-500 text-white rounded-lg hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {selectedPackage === 'enterprise' ? 'Contact now' : 'Purchase'}
        </Button>
      </div>
    </div>
  );
};

export default PurchaseCreditModal;
