import { useEffect, useState } from 'react';
import { getUserViewAsOrganizationId } from '../../../helpers/getUserViewAs';
import { getCreditStats } from '../../../services/users';

const CreditSummary = ({ companyId }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  const fetchData = async () => {
    const orgId = companyId || getUserViewAsOrganizationId();
    setLoading(true);
    try {
      const { data } = await getCreditStats(orgId);
      if (data?.result?.organizationSummary) {
        setData(data?.result?.organizationSummary);
      }
      console.log('Credit Summary Data:', data);
      // setData(response);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [companyId]);

  return loading ? (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
    </div>
  ) : (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Credit Pool */}
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="p-2 rounded bg-purple-100 text-purple-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M20.2 7.8l-7.7 7.7-4-4-5.7 5.7" />
              <path d="M15 7h6v6" />
            </svg>
          </div>
          <span className="text-xs font-medium px-2 py-1 rounded bg-purple-100 text-purple-600">
            Pool
          </span>
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            {data?.totalCreditsAllocated || 0}
          </h3>
          <p className="text-sm text-gray-500">Credits in shared pool</p>
          <div className="mt-2 text-xs text-gray-500">
            Users draw from this pool when they run out of their allocated
            credits
          </div>
        </div>
      </div>

      {/* Total Credits */}
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="p-2 rounded bg-teal-100 text-teal-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
          </div>
          <span className="text-xs font-medium px-2 py-1 rounded bg-teal-100 text-teal-600">
            Total
          </span>
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            {data?.totalCreditsUsed || 0} / {data?.totalCreditsAllocated || 0}
          </h3>
          <p className="text-sm text-gray-500">Total credits used</p>
          <div className="mt-2 w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-teal-500"
              style={{
                width: `${((data?.totalCreditsUsed || 0) / (data?.totalCreditsAllocated || 0)) * 100}%`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Available Credits */}
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="p-2 rounded bg-green-100 text-green-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
          </div>
          <span className="text-xs font-medium px-2 py-1 rounded bg-green-100 text-green-600">
            Available
          </span>
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            {data?.totalCreditsRemaining || 0}
          </h3>
          <p className="text-sm text-gray-500">Credits available</p>
          <div className="mt-2 w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500"
              style={{
                width: `${((data?.totalCreditsRemaining || 0) / data?.totalCreditsAllocated) * 100}%`,
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* User Credits */}
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="p-2 rounded bg-blue-100 text-blue-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <span className="text-xs font-medium px-2 py-1 rounded bg-blue-100 text-blue-600">
            Users
          </span>
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-900">
            {data?.totalCreditsUsed || 0}/{data?.totalCreditsAllocated || 0}
          </h3>
          <p className="text-sm text-gray-500">User credits used</p>
          <div className="mt-2 w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-500"
              style={{
                width: `${
                  ((data?.totalCreditsUsed || 0) /
                    (data?.totalCreditsAllocated || 0)) *
                  100
                }%`,
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreditSummary;
