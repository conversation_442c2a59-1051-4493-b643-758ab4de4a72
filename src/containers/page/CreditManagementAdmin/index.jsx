import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../store/auth';
import { useViewAs } from '../../../store/viewAs';
import { useEffect, useState } from 'react';
import {
  getCreditManagementOrganizations,
  getUserOrganizations,
} from '../../../services/users';
import CreditManagementAdminTable from './CreditManagementAdminTable';
import { Button, Input } from 'antd';
import CreditManagementCompany from '../CreditManagementCompany';
import { LeftOutlined } from '@ant-design/icons';
import { CompanyTable } from './CompanyTable';
import CreditSummary from './CreditSummary';
import { getUserViewAsRole } from '../../../helpers/getUserViewAs';
import { SALES, SUPER_ADMIN } from '../../../constants/common.constant';

const CreditManagementAdmin = () => {
  const [listCompany, setListCompany] = useState([]);
  const [listOption, setListOption] = useState([]);
  const [searchValue, setSearchValue] = useState('');

  const [loading, setLoading] = useState(false);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const account = profileUser || profileUserAuth;

  const currentUserRole = getUserViewAsRole();
  const isSuperAdmin =
    currentUserRole === SUPER_ADMIN || currentUserRole === SALES;

  const [currentOrgId, setCurrentOrgId] = useState();

  useEffect(() => {
    if (
      account?.user?.role?.keyCode === 'BASIC_USER' ||
      account?.role?.keyCode === 'BASIC_USER'
    ) {
      const orgId = account?.user?.organizationId || account?.organizationId;
      setCurrentOrgId(orgId);
    }
  }, [account]);

  const handleGetListCompany = async () => {
    setLoading(true);
    const { data } = await getCreditManagementOrganizations();
    setListCompany(data?.result);
    setListOption(data?.result);
    setLoading(false);
  };

  useEffect(() => {
    const optionFilters = listCompany.filter(
      (v) =>
        v?.name
          ?.toString()
          .toLocaleLowerCase()
          .includes(searchValue.toLocaleLowerCase()) ||
        v?.id
          ?.toString()
          .toLocaleLowerCase()
          .includes(searchValue.toLocaleLowerCase())
    );
    setListOption(optionFilters);
  }, [searchValue]);

  useEffect(() => {
    if (
      account?.user?.role?.keyCode !== 'BASIC_USER' ||
      account?.role?.keyCode !== 'BASIC_USER'
    ) {
      handleGetListCompany();
    }
  }, [account]);

  return (
    <div>
      {currentOrgId ? (
        <div>
          {isSuperAdmin && (
            <div>
              <Button
                type="text"
                className="inline-flex items-center text-sm text-teal-600 hover:text-teal-800"
                onClick={() => setCurrentOrgId(null)}
                icon={<LeftOutlined />}
              >
                Back
              </Button>
            </div>
          )}
          <CreditManagementCompany companyId={currentOrgId} />
        </div>
      ) : (
        <div className="w-full h-full p-3">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <span className="p-1 bg-teal-100 rounded text-teal-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
              </span>
              Credit Management
            </h1>
            <p className="text-gray-500 mt-1">
              Monitor and manage credits across all companies
            </p>
          </div>

          <CreditSummary />

          <div className="mt-8">
            <CompanyTable
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              data={listOption}
              loading={loading}
              setCurrentOrgId={setCurrentOrgId}
            />
          </div>
        </div>
      )}
    </div>
  );
};
export default CreditManagementAdmin;
