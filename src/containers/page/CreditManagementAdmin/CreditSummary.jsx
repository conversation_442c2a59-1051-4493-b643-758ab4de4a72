import { useEffect, useState } from 'react';
import { getUserViewAsOrganizationId } from '../../../helpers/getUserViewAs';
import { getCreditStats, getSystemCreditStats } from '../../../services/users';

const CreditSummary = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await getSystemCreditStats();
      console.log('Credit getSystemCreditStats Data:', data);
      if (data?.result?.systemSummary) {
        setData(data?.result?.systemSummary);
      }
      // setData(response);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return loading ? (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
    </div>
  ) : (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <SummaryCard
        icon={<CreditCardIcon />}
        label="Total"
        value={data?.totalCreditsAllocated || '0'}
        description="Total credits allocated"
        color="teal"
      />
      <SummaryCard
        icon={<AvailableIcon />}
        label="Available"
        value={data?.totalCreditsRemaining || '0'}
        description="Credits available"
        color="green"
      />
      <SummaryCard
        icon={<ClockIcon />}
        label="Used"
        value={data?.totalCreditsUsed || '0'}
        description="Credits used"
        color="yellow"
      />
      <SummaryCard
        icon={<AlertIcon />}
        label="Low"
        value={data?.lowCreditCompaniesCount || '0'}
        description="Companies with low credits"
        color="red"
      />
    </div>
  );
};

export default CreditSummary;

function SummaryCard({ icon, label, value, description, color }) {
  const bgColors = {
    teal: 'bg-teal-100',
    green: 'bg-green-100',
    yellow: 'bg-amber-100',
    red: 'bg-red-100',
  };

  const textColors = {
    teal: 'text-teal-600',
    green: 'text-green-600',
    yellow: 'text-amber-600',
    red: 'text-red-600',
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-5">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-2 rounded ${bgColors[color]} ${textColors[color]}`}>
          {icon}
        </div>
        <span
          className={`text-xs font-medium px-2 py-1 rounded ${bgColors[color]} ${textColors[color]}`}
        >
          {label}
        </span>
      </div>
      <div>
        <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
    </div>
  );
}

function CreditCardIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="20" height="14" x="2" y="5" rx="2" />
      <line x1="2" x2="22" y1="10" y2="10" />
    </svg>
  );
}

function AvailableIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="20" height="14" x="2" y="5" rx="2" />
      <line x1="2" x2="22" y1="10" y2="10" />
    </svg>
  );
}

function ClockIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );
}

function AlertIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="12" x2="12" y1="8" y2="12" />
      <line x1="12" x2="12.01" y1="16" y2="16" />
    </svg>
  );
}
