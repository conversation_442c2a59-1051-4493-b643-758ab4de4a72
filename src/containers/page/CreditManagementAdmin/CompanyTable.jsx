import { Input, Spin } from 'antd';
import { Search } from 'lucide-react';
import { useState, useEffect } from 'react';

export function CompanyTable({
  searchValue,
  setSearchValue,
  data,
  loading,
  setCurrentOrgId,
}) {
  //   const [filteredCompanies, setFilteredCompanies] = useState(companies);

  const handleRowClick = (companyId) => {
    // router.push(`/company/${companyId}`);
    setCurrentOrgId(companyId);
  };
  console.log('CompanyTable Data:', data);
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Companies</h2>
        <Input
          allowClear
          disabled={loading}
          prefix={<Search size={15} className="text-gray-600" />}
          className="max-w-80"
          placeholder="Search by company name"
          onChange={(e) => setSearchValue(e.target.value)}
          value={searchValue}
          //   onSearch={handleSearch}
        />
      </div>
      <div className="max-h-96 overflow-y-auto pr-2">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company Name
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Address
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Owner
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Users
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                License
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Credits
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          {!loading && (
            <tbody className="divide-y divide-gray-200">
              {data.length > 0 ? (
                data.map((company) => (
                  <tr
                    key={company?.id}
                    className="hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => handleRowClick(company.id)}
                  >
                    <td
                      className="px-4 py-4 whitespace-nowrap"
                      title={company?.name}
                    >
                      <div className="flex items-center">
                        <div
                          className={`w-8 h-8 rounded-full bg-${getInitialColor(company?.name?.charAt(0))}-100 text-${getInitialColor(company?.name?.charAt(0))}-600 flex items-center justify-center font-medium uppercase mr-3`}
                        >
                          {company?.name?.charAt(0)}
                        </div>
                        <div className="font-medium text-gray-900">
                          {company.name}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                      {company?.address ? (
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 text-gray-400 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          {company.address}
                        </div>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {company?.companyOwner}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-gray-400 mr-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        {company?.users?.length || 0}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          company.license === 'CONNECTED'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-purple-100 text-purple-800'
                        }`}
                      >
                        {company.license}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-500">
                            {company?.usedCredits || 0}
                          </span>
                          <span className="text-xs text-gray-500">
                            {company?.totalCredits || 0}
                          </span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${getProgressColor(company?.usedCredits, company?.totalCredits)}`}
                            style={{
                              width: `${(company?.usedCredits / company?.totalCredits) * 100}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(company.status)}`}
                      >
                        {company.status}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={7}
                    className="px-4 py-8 text-center text-gray-500"
                  >
                    No companies found matching your search.
                  </td>
                </tr>
              )}
            </tbody>
          )}
          {loading && (
            <tbody>
              <tr>
                <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                  Loading... <Spin />
                </td>
              </tr>
            </tbody>
          )}
        </table>
      </div>
    </div>
  );
}

function getInitialColor(initial) {
  const colors = {
    t: 'teal',
    b: 'blue',
    o: 'orange',
    n: 'green',
    u: 'purple',
    w: 'cyan',
  };

  return colors[initial.toLowerCase()] || 'gray';
}

function getProgressColor(used, total) {
  const percentage = (used / total) * 100;

  if (percentage < 50) return 'bg-teal-500';
  if (percentage < 75) return 'bg-amber-500';
  return 'bg-red-500';
}

function getStatusColor(status) {
  switch (status?.toLowerCase()) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'warning':
      return 'bg-amber-100 text-amber-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
