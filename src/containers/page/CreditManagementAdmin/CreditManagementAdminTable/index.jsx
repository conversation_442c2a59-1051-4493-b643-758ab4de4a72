import { EyeOutlined } from '@ant-design/icons';
import { Table } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const CreditManagementAdminTable = (props) => {
  const [dataSource, setDataSource] = useState([]);
  const { initData, loading = false, setCurrentOrgId } = props;

  const navigate = useNavigate();

  useEffect(() => {
    if (initData) {
      const baseData = initData?.map((item) => ({
        name: item?.name,
        address: item?.address,
        companyOwner: item?.companyOwner,
        license: item?.license,
        id: item?.id,
        totalUsers: item?.users?.length || 0
      }));
      setDataSource(baseData);
    }
  }, [initData]);

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      width: '15%',
      render: (name) => {
        return <div>{name}</div>;
      },
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      width: '30%',
      render: (address) => {
        return (
          <div className="font-semibold flex justify-start items-center gap-1 text-cyan-600">
            {address || '-'}
          </div>
        );
      },
    },
    {
      title: 'Owner',
      dataIndex: 'companyOwner',
      key: 'companyOwner',
      width: '20%',
      render: (companyOwner) => {
        return (
          <div className="truncate" title={companyOwner}>
            {companyOwner}
          </div>
        );
      },
    },
    {
      title: 'Total Users',
      dataIndex: 'totalUsers',
      key: 'totalUsers',
      width: '20%',
      render: (totalUsers) => {
        return (
          <div className="truncate" title={totalUsers}>
            {totalUsers}
          </div>
        );
      },
    },
    {
      title: 'License',
      dataIndex: 'license',
      key: 'license',
      width: '15%',
      render: (license) => {
        return (
          <div className="font-semibold flex justify-start items-center gap-1">
            {license}
          </div>
        );
      },
    },
    // {
    //   title: 'Access',
    //   width: '15%',
    //   align: 'center',
    //   render: (_, record) => {
    //     return (
    //       <div className="flex justify-center">
    //         <div
    //           className="w-[30px] h-[30px] flex items-center justify-center border border-gray-300 rounded cursor-pointer hover:bg-gray-100"
    //           onClick={() => {
    //             navigate(`/credit-management/company/${record?.id}`);
    //           }}
    //         >
    //           <EyeOutlined />
    //         </div>
    //       </div>
    //     );
    //   },
    // },
  ];

  return (
    <div className="w-full">
      <Table
        rowClassName="hover:bg-gray-50 cursor-pointer"
        dataSource={dataSource}
        columns={columns}
        pagination={true}
        loading={loading}
        className="w-full"
        onRow={(record) => {
          return {
            onClick: () => {
              setCurrentOrgId?.(record?.id)
            }
          };
        }}
      />
    </div>
  );
};

export default CreditManagementAdminTable;