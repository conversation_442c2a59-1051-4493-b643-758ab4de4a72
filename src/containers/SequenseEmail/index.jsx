import React, { useEffect, useRef, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  AutoComplete,
  Button,
  Col,
  Divider,
  Dropdown,
  Form,
  Input,
  Menu,
  Modal,
  Row,
  Select,
  Spin,
} from 'antd';
import EditableNumber from '../../components/EditableNumberComponent/EditableNumber';
import LeadStatusCRUD from '../../components/JobsLeads/LeadStatusCRUD';
import LeadsDragDrop from '../../components/JobsLeads/LeadsDragDrop';
import useJobLeads from '../../hooks/useJobLeads';
import { bulkUpdateLeadStatus } from '../../services/jobLeadStatuses';

import { useDispatch } from 'react-redux';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { saveAllStatusLead } from '../../store/common';
import Mailbox from '../../components/Mailbox';
import {
  countLeadMailBox,
  getLeadsByCompany,
  getMyLeadCompany,
} from '../../services/myLead';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import { MdOutlineMoreHoriz } from 'react-icons/md';
import {
  EnvironmentOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { getValue } from '@mui/system';

function SequenseEmail() {
  const idUserViewAs = getUserViewAs();

  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [companyId, setCompanyId] = useState();
  const [countLeads, setCountLeads] = useState(0);
  const [searchText, setSearchText] = useState();
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [existingContacts, setExistingContacts] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState(null);
  const [leadByCompanyId, setLeadByCompanyId] = useState();
  const [loadingContact, setLoadingContact] = useState(false);
  const [loadingFindJob, setLoadingFindJob] = useState(false);
  const [loadingFindVacancy, setLoadingFindVacancy] = useState(false);
  const [loadingDataEmail, setLoadingDataEmail] = useState(false);

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
    isLoadingScroll: isLoadingScrollCompany,
    setLoadingScroll: setIsLoadingScrollCompany,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const {
    options: companyOptionsJobOrder,
    setOptions: companySetOptionsJobOrder,
    handleScrollPopup: handleCompanyScrollJobOrder,
    handleSearch: handleCompanySearchJobOrder,
    isLoading: isLoadingCompanyJobOrder,
    setLoading: setIsLoadingCompanyJobOrder,
    valueNotFound: valueNotFoundCompanyJobOrder,
    setStart: companySetStartJobOrder,
    isLoadingScroll: isLoadingScrollCompanyJobOrder,
    setLoadingScroll: setIsLoadingScrollCompanyJobOrder,
  } = useInfiniteScrollWithSearch(searchBullhornData('JobOrder'));
  const timeoutRef = useRef(null);

  const updateExistingContacts = async () => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'ClientContact',
      null,
      null,
      null,
      companyId
    );
    setExistingContacts(response?.data?.result || []);
    setLoadingContact(false);
  };

  useEffect(() => {
    if (getValues('leadSelected')) {
      setOpenSendEmail(false);
      updateExistingContacts();
      getDataEmail();
    }
  }, [getValues('leadSelected')]);

  useEffect(() => {
    handleCompanySearch(' ')
  }, [])

  useEffect(() => {
     if(!openModalSendEmail) {
      setValue("sendMail.content", null)
      setValue("sendMail.subject", null)
     }
  }, [openModalSendEmail])

  const getDataEmail = async () => {
    const { data } = await getEmailConfigInJobBoard(
      getValues('leadSelected').job_id || getValues('leadSelected').job_board_id || getValues('leadSelected')?.id, true
    );
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          key: index + 1,
          status: item?.status
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
    }
    setOpenSendEmail(true);
  };

  const debouncedSetSearchText = _debounce(setSearchText, 500);

  return (
    <>
      <Row gutter={[32, 32]}>
        <Col span={24}>
          <div className="flex gap-4 items-center">
            <div className="custom_input">
              <AutoComplete
                onPopupScroll={(e) =>
                  handleCompanyScroll(e, 'ClientCorporation')
                }
                // value={getValues("currentCp")}
                disabled={loadingFindVacancy || loadingContact || openModalSendEmail || isLoadingScrollCompanyJobOrder}
                options={companyOptions?.map((option) => ({
                  ids: option.id,
                  value: option.name,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base">
                            {option.id} - {option.name}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex">
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <PhoneOutlined />{' '}
                              {option.phone ? option.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <InfoCircleOutlined />{' '}
                              {option.status ? option.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <EnvironmentOutlined />
                              {option.address &&
                              option.address.city &&
                              option.address.state
                                ? `${option.address.city}, ${option.address.state}`
                                : option.address && option.address.city
                                  ? option.address.city
                                  : option.address && option.address.state
                                    ? option.address.state
                                    : '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
                style={{ width: '500px', height: '50px', color: '#fff' }}
                onSelect={(value, option) => {
                  setCompanyId(option?.ids);
                  handleCompanySearchJobOrder(option?.ids)
                  setLeadByCompanyId(null);
                  setValue("currentCp", companyOptions?.find(
                    (item) => item.id === option.ids
                  )?.name)
                  setValue("leadSelected", null)
                  setOpenSendEmail(false)
                }}
                onSearch={(value) => {
                  setValue("currentCp", value);
                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    setLoadingFindJob(true);
                    if (value) companySetStart(0);
                    handleCompanySearch(value);
                    setLoadingFindJob(false);
                  }, 500);
                }}
                placeholder="Search Company"
              />
            </div>
            {isLoadingScrollCompany && <Spin />}
            <div className="custom_input_vacancy">
              {companyId && (
                <Select
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.valueName ?? '')?.toLowerCase()?.includes(input.toLowerCase())
                  }
                  onPopupScroll={(e) =>
                    handleCompanyScrollJobOrder(e, 'JobOrder', companyId)
                  }
                  disabled={openModalSendEmail}
                  // loading={isLoadingScrollCompanyJobOrder}
                  style={{ width: '500px', height: '50px' }}
                  value={
                    getValues('leadSelected')?.title
                  }
                  onSelect={(value, option) => {
                    const leadSelect = companyOptionsJobOrder?.find(
                      (item) => item.id === option.id
                    );
                    setValue('leadSelected', leadSelect);
                    setLeadByCompanyId(value);
                    setOpenSendEmail(true);
                  }}
                  placeholder="Search Vacancy"
                  options={companyOptionsJobOrder?.map((item) => ({
                    value: item.id,
                    id: item.id,
                    valueName: item?.title,
                    label: (
                      <>
                        <div className="grid">
                          <div className="flex justify-between">
                            <span className="text-base font-base">
                              {item?.title}
                            </span>
                          </div>
                          <div className="contact-details">
                            <div className="flex">
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <PhoneOutlined />{' '}
                                {item?.phone ? item.phone : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <InfoCircleOutlined />{' '}
                                {item?.status ? item.status : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <EnvironmentOutlined />
                                {item?.max_address_city ?? '-'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </>
                    ),
                  }))}
                />
              )}
            </div>
            {(loadingContact || loadingFindVacancy) && <Spin />}
          </div>
        </Col>
      </Row>
      {(!loadingContact && !loadingDataEmail && openModalSendEmail)&& (
        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
            </div>
          }
          open={openModalSendEmail}
          onCancel={() => {
            setValue("sendMail.content", null)
            setValue("sendMail.subject", null)
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmail}
                setOpenSendEmail={setOpenSendEmail}
                listAddContactSelected={existingContacts}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                job={getValues("leadSelected")}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromSequenseEmail={true}
                loadingDataEmail={loadingDataEmail}
              />
            </Form>
          </div>
        </Modal>
      )}
    </>
  );
}

export default SequenseEmail;
