/* eslint-disable react/no-unknown-property */
/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useRef, useState } from 'react';
import _get from 'lodash/get';
import './style.css';
import CompanyList from './CompanyList';
import {
  useLazyGetJobLeadsCompaniesQuery,
  useLazySearchJobLeadsCompaniesQuery,
} from '../../app/myLeads';
import { Spin } from 'antd';
import { MODE } from '../MyLeads';
import HorizontalCompanyList from './HorizontalCompanyList';
import clsx from 'clsx';

export const getBackgroundColor = (isDraggingOver, isDraggingFrom) => {
  if (isDraggingOver) {
    return '#FFEBE6';
  }
  if (isDraggingFrom) {
    return '#E6FCFF';
  }
  return '#EBECF0';
};

const scrollContainerHeight = 250;

const ListChildDragDrop = ({
  companies,
  title,
  statusId,
  reloadJobLeads,
  isSelectionMode,
  onChangeCheckbox,
  isDisableDnd,
  selectedRowKeys,
  handleDeleteLead,
  companyOverIndex,
  setJobLeads,
  jobLeads,
  setJobLeadsGlobal,
  inColumnLoading,
  mode,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [newCompanies, setNewCompanies] = useState(companies || []);
  const [searchJobLeadsCompaniesQuery] = useLazySearchJobLeadsCompaniesQuery();
  const [loading, setLoading] = useState(false);

  const [getJobLeadsCompanies] = useLazyGetJobLeadsCompaniesQuery();

  const handleScroll = async () => {
    const container = tableContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight - 1) {
        setLoading(true);
        setCurrentPage(currentPage + 1);
        await getCompanies(currentPage + 1);
        setLoading(false);
      }
    }
  };

  const tableContainerRef = useRef(null);

  const getCompanies = async (pageLoad) => {
    const { data } = await getJobLeadsCompanies({
      statusId,
      page: pageLoad,
      search: '',
      limit: 10,
    });

    const dataCompany = data?.result?.items || [];
    const companiesListTemp = await Promise.all(
      dataCompany.map(async (company) => {
        const leadsTemp = await getLeads({
          statusId: statusId,
          companyId: company.company_id,
        });
        if (leadsTemp.length > 0) {
          return {
            ...company,
            leads: leadsTemp,
            company_id: `${company.company_id}/${leadsTemp.id}`,
            companyIdRaw: company.company_id,
          };
        } else {
          return company;
        }
      })
    );

    setJobLeads((prevItems) =>
      prevItems.map((item) =>
        item.id === statusId
          ? { ...item, companies: [...item.companies, ...companiesListTemp] }
          : item
      )
    );
    setJobLeadsGlobal((prevItems) =>
      prevItems.map((item) =>
        item.id === statusId
          ? { ...item, companies: [...item.companies, ...companiesListTemp] }
          : item
      )
    );
    // setNewCompanies(prevCompanies => [...prevCompanies, ...companiesListTemp]);
  };

  const getLeads = async ({ statusId, companyId }) => {
    const companyData = await searchJobLeadsCompaniesQuery({
      statusId,
      companyId,
    });

    const convertData = _get(companyData, 'data.result.items', []);
    return convertData;
  };

  return (
    <div>
      {(loading || inColumnLoading) && (
        <div className="custom-on-loading">
          <Spin />
        </div>
      )}
      <div
        className={clsx(
          `pb-0 transition duration-200 ease-in-out select-none flex flex-col w-full flex items-start flex-col h-full`,
          mode === MODE.VERTICAL && 'gap-4',
          mode === MODE.HORIZONTAL && 'gap-1'
        )}
        // style={{height: "60vh", overflowY: "auto"}}
        onScroll={handleScroll}
        ref={tableContainerRef}
      >
        {companies.length
          ? companies?.map((item, index) => (
              <div
                className="select-none drag-item w-full"
                key={item?.company_id}
              >
                {mode === MODE.VERTICAL && (
                  <CompanyList
                    key={item?.company_id}
                    selectedRowKeys={selectedRowKeys}
                    isSelectionMode={isSelectionMode}
                    onChangeCheckbox={onChangeCheckbox}
                    statusId={statusId}
                    title={title}
                    handleDeleteLead={handleDeleteLead}
                    reloadJobLeads={reloadJobLeads}
                    isDisableDnd={isDisableDnd}
                    isOver={companyOverIndex === index}
                    jobLeads={jobLeads}
                    setJobLeads={setJobLeads}
                    {...item}
                  />
                )}
                {mode === MODE.HORIZONTAL && (
                  <HorizontalCompanyList
                    setJobLeads={setJobLeads}
                    jobLeads={jobLeads}
                    mode={mode}
                    key={item?.company_id}
                    selectedRowKeys={selectedRowKeys}
                    isSelectionMode={isSelectionMode}
                    onChangeCheckbox={onChangeCheckbox}
                    statusId={statusId}
                    title={title}
                    handleDeleteLead={handleDeleteLead}
                    reloadJobLeads={reloadJobLeads}
                    isDisableDnd={isDisableDnd}
                    isOver={companyOverIndex === index}
                    {...item}
                  />
                )}
              </div>
            ))
          : // <div className="p-2 border-dashed border-2 rounded-md h-full w-full font-bold flex items-center justify-center">
            //   Drag lead here
            // </div>
            null}
      </div>
    </div>
  );
};

export default ListChildDragDrop;
