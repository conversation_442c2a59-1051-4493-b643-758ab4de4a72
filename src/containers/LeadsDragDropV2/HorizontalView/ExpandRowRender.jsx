import {
  CalendarOutlined,
  ContainerOutlined,
  EnvironmentOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Table, Tag } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import LeadCard from '../../../components/JobsLeads/LeadCard';
import { formatNumber } from '../../../components/BullHorn/BullHornJobSubmissionForm';
import Styled from 'styled-components';

const ExpandRowRender = ({ record, handleDeleteLead, reloadJobLeads }) => {
  console.log('record', record);

  const ExpandContainer = Styled.div`
.expand-table-new-design-container .ant-table-thead .ant-table-cell{
  background-color: ${record?.color} !important;
  }
  .ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:first-child {
  border-start-start-radius: 0px !important;
  }
  .ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:last-child {
  border-start-end-radius: 0px !important;
  }
`;

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date_added',
      key: 'date_added',
      align: 'start',
      render: (date) => {
        return (
          date && (
            <div className="flex items-center justify-start">
              <Tag icon={<CalendarOutlined />} color="cyan">
                {dayjs(date).format('DD/MM/YYYY')}
              </Tag>
            </div>
          )
        );
      },
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      align: 'start',
      width: '37%',
      render: (title, record) => {
        return (
          title && (
            <div>
              <div className="font-medium">{title}</div>
              <div className="flex gap-4 justify-start items-center text-xs opacity-60">
                <span className="flex items-center gap-1">
                  {' '}
                  <ContainerOutlined />
                  {record?.employment_type}
                </span>
                <span className="flex items-center gap-1">
                  <EnvironmentOutlined />{' '}
                  {record?.address_city || record?.address_country}
                </span>
              </div>
            </div>
          )
        );
      },
    },
    {
      title: 'Salary',
      dataIndex: 'salary',
      key: 'salary',
      align: 'start',
      width: '15%',
      render: (salary, record) => {
        return (
          salary && (
            <div className="font-medium">{`£ ${formatNumber(parseFloat(salary))}`}</div>
          )
        );
      },
    },
    {
      title: 'Contacts',
      dataIndex: 'company_contact_name',
      key: 'company_contact_name',
      //   align: 'center',
      render: (company_contact_name, record) => {
        return (
          company_contact_name && (
            <div className="flex items-center justify-start gap-1 font-medium text-sm">
              <UserOutlined /> <span>{company_contact_name}</span>
            </div>
          )
        );
      },
    },
    {
      title: 'Actions',
      dataIndex: 'lead_id',
      key: 'lead_id',
      align: 'center',
      render: (leadContacts, record) => {
        return (
          record?.title?.trim() && ( // title of lead
            <div className="flex items-center justify-center gap-2">
              <LeadCard
                lead={record}
                reloadJobLeads={reloadJobLeads}
                handleDeleteLead={handleDeleteLead}
                isButtonMode={true}
              />
            </div>
          )
        );
      },
    },
  ];

  return (
    <ExpandContainer className="grid grid-cols-10">
      <div className="expand-table-new-design-container col-span-10 border">
        <Table
          showHeader={false}
          dataSource={record?.leads}
          columns={columns}
          rowKey={(record) => record?.id}
          //   rowSelection={rowSelection}
          // onRow={(record, rowIndex) => {
          //   return {
          //     onClick: () => {
          //       setSelectedTask(record?.sequenceTasks?.[0]);
          //       setSelectedTaskList([...record?.sequenceTasks]);
          //       showModal();
          //     },
          //     style: { cursor: 'pointer' },
          //   };
          // }}
          pagination={false}
        />
      </div>
    </ExpandContainer>
  );
};

export default ExpandRowRender;
