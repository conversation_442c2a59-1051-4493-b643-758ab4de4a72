import { useEffect, useState } from 'react';
import { getLinkS3 } from '../../../services/aws';
import zileoLogo from '../../../assets/img/welcome/logo.png';
import { Image, Spin } from 'antd';

const CompanyCompact = ({ value, row, index }) => {
  const { logoCompany, leads, jl_company_name } = row;
  const [imageUrl, setImageUrl] = useState(null);

  const fetchImageUrl = async () => {
    const logoCompaniesLocalStorage = sessionStorage.getItem('LOGO_COMPANIES');
    const logoCompanies = logoCompaniesLocalStorage
      ? JSON.parse(logoCompaniesLocalStorage)
      : [];

    const existingLogo = logoCompanies.find(
      (item) => item?.companyLogoId === logoCompany
    );

    if (existingLogo) {
      setImageUrl(existingLogo?.data);
    } else {
      try {
        const { data } = await getLinkS3(logoCompany);

        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            { companyLogoId: logoCompany, data },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
        }

        setImageUrl(data);
      } catch (error) {
        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            {
              companyLogoId: logoCompany,
              data:
                document?.location?.origin + '/src/assets/img/welcome/logo.png',
            },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
          setImageUrl(zileoLogo);
        } else {
          setImageUrl(existingLogo);
        }
      }
    }
  };

  useEffect(() => {
    fetchImageUrl();
  }, []);

  return (
    <>
      <div className="flex items-center justify-start gap-4">
        {/* {!imageUrl && <Spin size="small" />} */}
        {/* {imageUrl && ( */}
        <Image
          preview={false}
          loading="lazy"
          width={'50px'}
          src={imageUrl}
          onError={() => setImageUrl(zileoLogo)}
        />
        <div>
          <span
            className="text-cyan-600 line-clamp-1 col-span-4 w-full font-medium"
            title={jl_company_name}
          >
            <span>{jl_company_name}</span>
          </span>
          <div className="flex items-center text-xs text-[#565961]">
            <span>{leads?.length || 0} leads in total</span>
          </div>
        </div>
        {/* )} */}
      </div>
    </>
  );
};

export default CompanyCompact;
