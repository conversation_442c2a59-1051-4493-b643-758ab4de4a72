import {
  CalendarOutlined,
  ClusterOutlined,
  ContainerOutlined,
  DeleteOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  HolderOutlined,
  PayCircleOutlined,
  Pic<PERSON>enterOutlined,
  PoundCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import ExpandRowRender from './ExpandRowRender';
import { formatNumber } from '../../../components/BullHorn/BullHornJobSubmissionForm';
import LeadCard from '../../../components/JobsLeads/LeadCard';
import CompanyCompact from './CompanyCompact';

import { CSS } from '@dnd-kit/utilities';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';

const RowContext = React.createContext({});
const DragHandle = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{
        cursor: 'move',
      }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

const Row = (props) => {
  console.log('Row', props);
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });
  const style = {
    ...props.style,
    transform: CSS?.Translate?.toString(transform),
    transition,
    ...(isDragging
      ? {
          position: 'relative',
          zIndex: 9999,
        }
      : {}),
  };
  const contextValue = useMemo(
    () => ({
      setActivatorNodeRef,
      listeners,
    }),
    [setActivatorNodeRef, listeners]
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

const HorizontalView = ({ jobLeads, handleDeleteLead, reloadJobLeads }) => {
  const [dataSource, setDataSource] = useState([]);
  const [expandList, setExpandList] = useState([]);

  const onDragEnd = ({ active, over }) => {
    console.log('onDragEnd', active);
    if (active.id !== over?.id) {
      setDataSource((prevState) => {
        const activeIndex = prevState.findIndex(
          (record) => record.key === active?.id
        );
        const overIndex = prevState.findIndex(
          (record) => record.key === over?.id
        );
        return arrayMove(prevState, activeIndex, overIndex);
      });
    }
  };

  const columns = [
    {
      title: 'Stage',
      // Lead Status
      key: 'stage-name',
      width: '20%',
      render: (value, row, index) => {
        const obj = {
          children: (
            <div className="flex flex-col gap-1">
              <div className="text-lg font-semibold">{row.stage_name}</div>
              <div className="flex items-center justify-start gap-1">
                <span className="font-medium">{`${row?.total_lead} leads`}</span>
                <span className="font-semibold">-</span>
                <span className="flex items-center justify-start gap-1 font-medium">
                  <PoundCircleOutlined />
                  {formatNumber(parseFloat(row?.total_salary))}
                </span>
              </div>
            </div>
          ),
          props: {},
        };
        if (row?.color) {
          obj.props.style = { backgroundColor: row.color };
        }
        // Calculate rowSpan dynamically
        const rowSpan = dataSource.reduce((acc, item, idx) => {
          if (item.stage_name === row.stage_name) {
            acc.push(idx);
          }
          return acc;
        }, []);

        if (rowSpan[0] === index) {
          const rowCompanyIdList = row?.companies?.map(
            (company) => company?.company_id
          );
          const extraCols = rowCompanyIdList?.filter((companyId) =>
            expandList.includes(companyId)
          ).length;
          obj.props.rowSpan = rowSpan.length + extraCols;
        } else {
          obj.props.rowSpan = 0;
        }

        return obj;
      },
    },
    // {
    //   key: 'sort',
    //   align: 'center',
    //   width: 80,
    //   render: () => <DragHandle />,
    // },
    {
      title: 'Date',
      dataIndex: 'leadCreatedDate',
      key: 'date',
      align: 'center',
      render: (date) => {
        return (
          date && (
            <div className="flex items-center justify-center">
              <Tag icon={<CalendarOutlined />} color="cyan">
                {dayjs(date).format('DD/MM/YYYY')}
              </Tag>
            </div>
          )
        );
      },
    },
    // Table.EXPAND_COLUMN,
    {
      // showSorterTooltip: {
      //   target: 'full-header',
      // },
      // filters: arrayUniqueByKey(
      //   dataSource.map((item) => ({
      //     text: `[${item?.stage_name}] ${item.jl_company_name}`,
      //     value: `${item?.stage_name},${item.jl_company_name}`.trim(),
      //   })),
      //   'value'
      // ),
      title: 'Company Name',
      dataIndex: 'jl_company_name',
      key: 'jl_company_name',
      // onFilter: (value, record) => {
      //   const [stageName, companyName] = value.split(',');
      //   return (
      //     record.stage_name === stageName &&
      //     record.jl_company_name === companyName
      //   );
      // },
      // sorter: (a, b) => a.jl_company_name.length - b.jl_company_name.length,
      // sortDirections: ['descend'],
      render: (value, row, index) => {
        // // Calculate rowSpan dynamically
        // const rowSpan = dataSource.reduce((acc, item, idx) => {
        //   if (item.jl_company_name === row.jl_company_name) {
        //     acc.push(idx);
        //   }
        //   return acc;
        // }, []);

        // if (rowSpan[0] === index) {
        //   obj.props.rowSpan = rowSpan.length;
        // } else {
        //   obj.props.rowSpan = 0;
        // }

        return <CompanyCompact value={value} row={row} index={index} />;
      },
    },
    {
      title: 'Title',
      dataIndex: 'leadTitle',
      key: 'title',
      align: 'start',
      render: (leadTitle, record) => {
        return leadTitle ? (
          <div>
            <div className="font-medium">{leadTitle}</div>
            <div className="flex gap-4 justify-start items-center text-xs opacity-60">
              <span className="flex items-center gap-1">
                {' '}
                <ContainerOutlined />
                {record?.employment_type}
              </span>
              <span className="flex items-center gap-1">
                <EnvironmentOutlined />{' '}
                {record?.address_city || record?.address_country}
              </span>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-start">
            <Tag icon={<ClusterOutlined />} color="magenta">
              {/* {`${record?.leads?.length} leads`} */}
              Grouped
            </Tag>
          </div>
        );
      },
    },
    {
      title: 'Salary',
      dataIndex: 'leadSalary',
      key: 'salary',
      align: 'start',
      render: (leadSalary, record) => {
        return leadSalary ? (
          <div className="font-medium">{`£ ${formatNumber(parseFloat(leadSalary))}`}</div>
        ) : (
          <div className="flex items-center justify-start">
            <Tag icon={<PoundCircleOutlined />} color="geekblue">
              {/* {`${formatNumber(record?.leads?.reduce((a, b) => a + parseInt(b?.salary), 0))} in total`} */}
              Grouped
            </Tag>
          </div>
        );
      },
    },
    {
      title: 'Contacts',
      dataIndex: 'leadContacts',
      key: 'contacts',
      align: 'start',
      render: (leadContacts, record) => {
        return leadContacts ? (
          <div className="flex items-center justify-start gap-1 font-medium text-sm">
            <UserOutlined /> <span>{leadContacts}</span>
          </div>
        ) : (
          <div className="flex items-center justify-start">
            <Tag icon={<UserOutlined />} color="purple">
              {/* {`${record?.leads?.length} contacts`} */}
              Grouped
            </Tag>
          </div>
        );
      },
    },
    {
      title: 'Actions',
      dataIndex: 'lead_id',
      key: 'lead_id',
      align: 'center',
      render: (leadContacts, record) => {
        return (
          record?.title?.trim() && ( // title of lead
            <div className="flex items-center justify-center gap-2">
              <LeadCard
                lead={record}
                reloadJobLeads={reloadJobLeads}
                handleDeleteLead={handleDeleteLead}
                isButtonMode={true}
              />
            </div>
          )
        );
      },
    },
  ];

  useEffect(() => {
    if (jobLeads) {
      const companyList = jobLeads.flatMap((item) =>
        item?.companies?.length > 0
          ? item?.companies?.map((company) => ({
              ...item,
              id: company?.company_id,
              ...company,
              stage_name: item?.name,
              ...(company?.leads?.length === 1 && {
                ...company?.leads?.[0],
                leadTitle: company?.leads[0]?.title,
                leadSalary: company?.leads[0]?.salary,
                leadContacts: company?.leads[0]?.company_contact_name,
                leadCreatedDate: company?.leads[0]?.date_added,
              }),
              //   ...(company?.leads?.length > 1 && {
              //     children: company?.leads?.map((lead) => ({
              //       ...lead,
              //       leadTitle: lead?.title,
              //       leadSalary: lead?.salary,
              //       leadContacts: lead?.company_contact_name,
              //       leadCreatedDate: lead?.date_added,
              //     })),
              //   }),
            }))
          : //   ?.flatMap((comp) =>
            //     comp?.leads?.map((lead) => ({
            //       stage_name: comp?.stage_name,
            //       jl_company_name: comp?.jl_company_name,
            //       ...lead,
            //       leadTitle: lead?.title,
            //       leadSalary: lead?.salary,
            //       leadContacts: lead?.company_contact_name,
            //       leadCreatedDate: lead?.date_added,
            //     }))
            //   )
            {
              stage_name: item?.name,
            }
      );
      const data = [...companyList];
      console.log('companyList', companyList);
      setDataSource(data);
    }
  }, [jobLeads]);

  return (
    <div className="w-full">

      <div className="search-table-new-design-container">
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.company_id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              //   className="customized-style-pagination w-full"
              dataSource={dataSource}
              columns={columns}
              components={{
                body: {
                  row: Row,
                },
              }}
              expandable={{
                expandedRowRender: (record) => {
                  return (
                    <div className="w-full h-[20rem] overflow-y-auto expand-row-container">
                      <ExpandRowRender
                        handleDeleteLead={handleDeleteLead}
                        record={record}
                        reloadJobLeads={reloadJobLeads}
                      />
                    </div>
                  );
                },
                rowExpandable: (record) =>
                  expandList.includes(record?.company_id),
                showExpandColumn: false,
                defaultExpandAllRows: true,
                expandedRowKeys: expandList,
                expandRowByClick: false,
              }}
              rowKey={(record) => record?.company_id}
              //   rowSelection={rowSelection}
              onRow={(record, rowIndex) => {
                return {
                  onClick: (event) => {
                    if (record?.leads?.length > 1) {
                      let newExpandList = [...expandList];
                      if (newExpandList.includes(record?.company_id)) {
                        newExpandList = newExpandList.filter(
                          (item) => item !== record?.company_id
                        );
                      } else {
                        newExpandList.push(record?.company_id);
                      }
                      setExpandList([...newExpandList]);
                    }
                  },
                  style: { cursor: 'pointer' },
                };
              }}
              pagination={false}
            />
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
};

export default HorizontalView;
