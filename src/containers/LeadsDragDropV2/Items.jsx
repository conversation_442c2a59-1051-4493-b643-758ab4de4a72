import { useSortable } from '@dnd-kit/sortable';
import React from 'react';
import { CSS } from '@dnd-kit/utilities';
import clsx from 'clsx';
import { DragOutlined } from '@ant-design/icons';
import ListChildDragDrop from './ListChildDragDrop';
import { MODE } from '../MyLeads';

const Items = ({
  id,
  title,
  jobLeadsWithStatus,
  handleDeleteLead,
  reloadJobLeads,
  onChangeCheckbox,
  selectedRowKeys,
  isSelectionMode,
  rawFilters,
  companyOverIndex,
  setJobLeads,
  jobLeads,
  setJobLeadsGlobal,
  inColumnLoading,
  mode = MODE.VERTICAL,
}) => {
  const { id: statusId, companies } = jobLeadsWithStatus;

  return (
    <ListChildDragDrop
      mode={mode}
      companyOverIndex={companyOverIndex}
      statusId={statusId}
      companies={companies || []}
      reloadJobLeads={reloadJobLeads}
      handleDeleteLead={handleDeleteLead}
      isSelectionMode={isSelectionMode}
      onChangeCheckbox={onChangeCheckbox}
      selectedRowKeys={selectedRowKeys}
      isDisableDnd={Object.keys(rawFilters || {}).length > 0}
      setJobLeads={setJobLeads}
      jobLeads={jobLeads}
      setJobLeadsGlobal={setJobLeadsGlobal}
      inColumnLoading={inColumnLoading}
    />
  );
};

export default Items;
