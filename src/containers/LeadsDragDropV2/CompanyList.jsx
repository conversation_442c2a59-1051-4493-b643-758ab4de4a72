/* eslint-disable react/no-unknown-property */
/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useRef, useState } from 'react';
import _get from 'lodash/get';
import { Collapse, Spin, Image, Checkbox } from 'antd';
import './style.css';
import zileo<PERSON>ogo from '../../assets/img/welcome/logo.png';
import { getLinkS3 } from '../../services/aws';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import clsx from 'clsx';
import { DragOutlined } from '@ant-design/icons';
import { parsedCompanyId } from '.';
import LeadCard from '../../components/JobsLeads/LeadCard';

import { List } from 'react-virtualized';

const panelStyle = {
  // marginBottom: 8,
  background: 'white',
  borderRadius: 8,
  borderWidth: 1,
  boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  alignItems: 'center',
};

const CompanyList = React.memo(function InnerQuoteList({
  leads,
  logoCompany,
  isSelectionMode,
  onChangeCheckbox,
  selectedRowKeys,
  isDisableDnd,
  handleDeleteLead,
  company_id,
  jl_company_name,
  reloadJobLeads,
  title,
  statusId,
  isOverlay = false,
  companyIdRaw,
  isOver = false,
  setJobLeads,
  jobLeads
}) {
  const [imageUrl, setImageUrl] = useState(null);
  const [key, setKey] = useState([]);
  const jlCompanyNameRef = useRef(null);
  const [companyNameWidth, setCompanyNameWidth] = useState(0);

  useEffect(() => {
    if (jlCompanyNameRef.current) {
      setCompanyNameWidth(jlCompanyNameRef.current.offsetWidth);
    }
  }, [jl_company_name]);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    over,
  } = useSortable({
    id: `${parsedCompanyId(company_id)}`,
    data: {
      type: 'company',
    },
    transition: {
      duration: 150, // milliseconds
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });

  const fetchImageUrl = async () => {
    const logoCompaniesLocalStorage = sessionStorage.getItem('LOGO_COMPANIES');
    const logoCompanies = logoCompaniesLocalStorage
      ? JSON.parse(logoCompaniesLocalStorage)
      : [];

    const existingLogo = logoCompanies.find(
      (item) => item?.companyLogoId === logoCompany
    );

    if (existingLogo) {
      setImageUrl(existingLogo?.data);
    } else {
      try {
        const { data } = await getLinkS3(logoCompany);

        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            { companyLogoId: logoCompany, data },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
        }

        setImageUrl(data);
      } catch (error) {
        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            {
              companyLogoId: logoCompany,
              data:
                document?.location?.origin + '/src/assets/img/welcome/logo.png',
            },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
          setImageUrl(zileoLogo);
        } else {
          setImageUrl(existingLogo);
        }
      }
    }
  };

  useEffect(() => {
    fetchImageUrl();
  }, []);

  const isChecked = () => {
    const comparingLeadIds = leads.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );

    if (leads?.length === comparingLeadIds?.length) {
      return true;
    } else {
      return false;
    }
  };

  const listenerData = key?.length === 0 ? listeners : {};

  const collapseItem = [
    {
      key: company_id,
      label: (
        <div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              marginLeft: '70px',
            }}
          >
            <div
              style={{
                width: companyNameWidth ? `${companyNameWidth}px` : '200px',
                height: '30px',
                marginTop: '10px',
                position: 'absolute',
                zIndex: '1000',
                cursor: 'pointer',
              }}
              onClick={(e) => {
                setKey((prevKey) =>
                  prevKey.includes(company_id)
                    ? prevKey.filter((k) => k !== company_id)
                    : [...prevKey, company_id]
                );
              }}
            ></div>
          </div>
          <div
            {...listenerData}
            className={`font-medium flex items-start flex flex-col ${key?.length === 0 ? 'cursor-grab' : ''}`}
          >
            <div
              className={`relative grid ${isSelectionMode ? `grid-cols-7` : `grid-cols-7`}  gap-2 w-full`}
            >
              {isSelectionMode && !isDragging && (
                <Checkbox
                  className="absolute -left-10 top-0"
                  checked={isChecked()}
                  value={`company_id/${companyIdRaw}`}
                  defaultChecked={true}
                  onClick={(e) => e.stopPropagation()}
                  onChange={onChangeCheckbox}
                />
              )}
              <div className="flex flex-col col-span-2">
                {!isDragging && !isOverlay && !over && (
                  <div>
                    {!imageUrl && <Spin />}
                    {imageUrl && (
                      <Image
                        width={'35px'}
                        src={imageUrl}
                        onError={() => setImageUrl(zileoLogo)}
                      />
                    )}
                  </div>
                )}
                <div className="flex items-center text-sm text-[#565961]">
                  <span>{leads?.length || 0} leads</span>
                </div>
              </div>
              <span
                className="text-[#379df1] line-clamp-1 col-span-4 w-full flex items-center justify-center text-cyan-600"
                title={jl_company_name}
                data-no-dnd="true"
              >
                <span ref={jlCompanyNameRef}>{jl_company_name}</span>
              </span>
            </div>
          </div>
        </div>
      ),
      children: (
        <div
          className={`${leads?.length === 1 ? 'h-[14rem]' : 'h-[20rem]'} flex justify-center`}
        >
          <List
            width={270}
            height={300}
            rowHeight={240}
            rowRenderer={({ index, key, style }) => (
              <LeadCard
                style={style}
                lead={leads[index]}
                reloadJobLeads={reloadJobLeads}
                setJobLeads={setJobLeads}
                handleDeleteLead={handleDeleteLead}
                jobLeads={jobLeads}
                companyLogoUrl={imageUrl}
                isSelectionMode={isSelectionMode}
                onChangeCheckbox={onChangeCheckbox}
                selectedRowKeys={selectedRowKeys}
              />
            )}
            rowCount={leads?.length}
            overscanRowCount={3}
          />
        </div>
        // <SortableContext items={leads} strategy={verticalListSortingStrategy}>
        // <List
        //   height={300}
        //   itemCount={leads.length}
        //   itemSize={() => 254}
        //   itemData={leads}
        //   width={270}
        // >
        //   {({ index, data, style }) => (
        //     <LeadCard
        //       style={style}
        //       lead={data[index]}
        //       reloadJobLeads={reloadJobLeads}
        //       handleDeleteLead={handleDeleteLead}
        //     />
        //   )}
        // </List>
        // </SortableContext>
      ),
      // <InnerList
      //   isDisableDnd={isDisableDnd}
      //   reloadJobLeads={reloadJobLeads}
      //   handleDeleteLead={handleDeleteLead}
      //   quotes={leads || []}
      //   // quotes={quotes}
      //   title={title}
      // />,
      style: panelStyle,
    },
  ];

  return (
    <div
      ref={setNodeRef}
      // {...listeners}
      //   {...attributes}
      style={{
        transition,
        transform: CSS.Translate.toString(transform),
      }}
      className={clsx(
        'bg-white shadow-md rounded-xl w-full border border-transparent hover:border-gray-200 cursor-pointer relative',
        isDragging && 'opacity-50'
      )}
    >
      {isOver && (
        <div className="absolute -top-2 left-0 w-full border-gray-500 border-2"></div>
      )}
      <Collapse
        // collapsible={isSelectionMode ? 'disabled' : 'icon'}
        bordered={false}
        expandIconPosition="end"
        // expandIcon={({ isActive }) =>
        //   !isSelectionMode &&
        //   key?.length === 0 && (
        //     <DragOutlined {...listeners} className="cursor-grab !text-xl" />
        //   )
        // }
        activeKey={key}
        items={collapseItem}
        style={{
          background: 'transparent',
        }}
        onChange={(key) => {
          setKey([...key]);
        }}
      />
    </div>
  );
});

export default CompanyList;
