import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import clsx from 'clsx';
import {
  ArrowDownOutlined,
  BgColorsOutlined,
  CloseOutlined,
  DragOutlined,
  FilterOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  notification,
} from 'antd';
import { fieldItems } from './constant';
import { useState } from 'react';
import { useEffect } from 'react';
import {
  savePotentialLeadValue,
  selectPotentialLeadValue,
} from '../../store/common';
import { useDispatch, useSelector } from 'react-redux';
import { getPotentialLeadPercentValue } from '../../services/jobLeads';
import { useQuery } from '@tanstack/react-query';
import { MODE, TYPE } from '../MyLeads';

const HorizontalContainer = ({
  id,
  children,
  title,
  jobLeadsWithStatus,
  onSubmitBoardFilter,
  rawFilters,
  isSelectionMode,
  selectedRowKeys,
  onChangeCheckbox,
  isOverlay = false,
  mode,
}) => {
  const {
    attributes,
    setNodeRef,
    listeners,
    transform,
    transition,
    isDragging,
    over,
    active,
  } = useSortable({
    id,
    data: {
      type: 'status',
    },
  });

  const isOverContainer =
    over && active.data.current.type !== 'status'
      ? over?.id?.includes(jobLeadsWithStatus?.id)
      : false;

  const dispatch = useDispatch();

  const { data: potentialLeadValue } = useQuery(['POTENTIAL_RETURN_PERCENT'], {
    queryFn: async () => {
      const potentialLeadValue = useSelector(selectPotentialLeadValue);

      if (potentialLeadValue) {
        return potentialLeadValue;
      }

      const { data } = await getPotentialLeadPercentValue();

      dispatch(savePotentialLeadValue(data.result.potentialLeadValue));

      return data.result.potentialLeadValue;
    },
  });

  const [totalInfor, setTotalInfor] = useState({
    leads: 0,
    bonus: 0,
  });

  const [totalFilters, setTotalFilter] = useState(0);
  const [filtersWatch, setFiltersWatch] = useState([]);
  const [openFilterModal, setOpenFilterModal] = useState(false);

  const showFilterModal = () => setOpenFilterModal(true);
  const closeFilterModal = () => setOpenFilterModal(false);

  // filter on board

  const [form] = Form.useForm();
  const filters = Form.useWatch('filters', form);

  const onFinish = async (values) => {
    let payload = {};
    if (values?.filters && values?.filters?.length > 0) {
      for await (const filter of values?.filters) {
        let value = filter?.value;
        if (!value) return;
        payload[filter?.field] = value;
      }
    }
    console.log('onFinish values: ', values);
    onSubmitBoardFilter(jobLeadsWithStatus.id, payload);
    setTotalFilter(Object.keys(payload).length);
    closeFilterModal();
  };

  const handleChangeFieldName = (key) => (value) => {
    // setValue(name, value);
    const newFiltersList = filtersWatch.map((filter) => {
      if (filter?.key === key) {
        return { ...filter, value };
      } else {
        return { ...filter };
      }
    });
    setFiltersWatch(newFiltersList);
  };

  const handleClearFilter = () => {
    form.resetFields();
    setTotalFilter(0);
    setFiltersWatch([]);
    closeFilterModal();
    onSubmitBoardFilter(jobLeadsWithStatus?.id, null);
  };

  const checkValueInput = (index) => (
    <Input
      style={{
        width: 260,
      }}
    />
  );

  const isChecked = () => {
    const jobLeadsIdWithStatusId = jobLeadsWithStatus?.companies.flatMap(
      (company) => company?.leads
    );
    const comparingLeadIds = jobLeadsIdWithStatusId.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );

    if (jobLeadsIdWithStatusId?.length === comparingLeadIds?.length) {
      return true;
    } else {
      return false;
    }
  };

  //   useEffect area

  useEffect(() => {
    if (jobLeadsWithStatus?.companies?.length > 0) {
      const leadsListTemp =
        jobLeadsWithStatus?.companies?.flatMap((company) => company?.leads) ||
        [];
      const leadSalaryValue =
        leadsListTemp.reduce(
          (total, lead) => total + parseFloat(lead?.salary || 0),
          0
        ) || 0;

      const bonusValue = parseFloat(
        leadSalaryValue * (potentialLeadValue / 100)
      ).toFixed(2);

      setTotalInfor({
        leads: leadsListTemp?.length,
        bonus: bonusValue,
      });
    } else {
      setTotalInfor({
        leads: 0,
        bonus: 0,
      });
    }
  }, [JSON.stringify(jobLeadsWithStatus)]);

  useEffect(() => {
    setFiltersWatch(filters);
  }, [filters]);

  useEffect(() => {
    const currFilters = [];
    if (Object.keys(rawFilters || {}).length > 0) {
      for (const [field, value] of Object.entries(rawFilters)) {
        currFilters.push({ field, value });
      }
      form.setFieldValue('filters', [...currFilters]);
    } else {
      form.setFieldValue('filters', []);
    }
  }, [rawFilters, openFilterModal]);

  return (
    <div
      //   {...attributes}
      ref={setNodeRef}
      style={{
        transition,
        transform: CSS.Translate.toString(transform),
        opacity: isDragging ? 0.5 : undefined,
        height: mode === MODE.VERTICAL && '70vh',
      }}
      className={clsx(
        `w-full bg-gray-50 rounded-xl flex flex-col gap-y-4`,
        isDragging && 'opacity-50',
        isOverContainer && '_src_components_Container_Container_module__hover',
        mode === MODE.VERTICAL && isOverlay && 'min-h-[40rem]'
      )}
    >
      <div
        className="min-w-[20.5rem] w-full h-full grid grid-cols-12 items-center gap-3"
        key={id.toString()}
      >
        <div className="w-full h-full col-span-2">
          <h4
            style={{
              background: jobLeadsWithStatus?.color
                ? jobLeadsWithStatus?.color
                : '#f5f5f6',
            }}
            className={clsx(
              'h-full w-full px-2 py-3 font-semibold bg-[#f5f5f6] flex justify-between items-center grid grid-cols-10',
              isOverContainer && 'opacity-60'
            )}
            aria-label={`${title}`}
          >
            <div className="pl-3 col-span-1">
              <div className="flex justify-between items-center relative">
                <div className="flex items-center gap-2 justify-center">
                  <span
                    className="text-lg line-clamp-1 whitespace-nowrap"
                    title={title}
                  >
                    {title}
                  </span>
                  <Button
                    type="text"
                    icon={<FilterOutlined />}
                    onClick={showFilterModal}
                  >
                    {totalFilters > 0 ? `(${totalFilters})` : ''}
                  </Button>
                </div>
                {isSelectionMode &&
                  parseInt(jobLeadsWithStatus?.total_company) > 0 && (
                    <div className="absolute top-0 -left-10">
                      <Checkbox
                        checked={isChecked()}
                        defaultChecked={true}
                        value={`lead_status_id/${jobLeadsWithStatus?.id}`}
                        onChange={onChangeCheckbox}
                      />
                    </div>
                  )}
              </div>
              <div className="flex gap-2 text-[#565961] text-sm font-normal">
                <span className="whitespace-nowrap">
                  {jobLeadsWithStatus?.total_company || 0} leads{' '}
                </span>
                <span>·</span>
                <span
                  className="whitespace-nowrap"
                  title="Potential Lead Value"
                >
                  {totalInfor?.bonus > 0 ? `£ ${totalInfor?.bonus}` : 'N/A'}
                </span>
              </div>
            </div>
            {!isSelectionMode && (
              <div
                {...listeners}
                className="col-span-9 w-full h-full cursor-grab"
              ></div>
            )}
          </h4>
        </div>

        <div className="col-span-10 w-full h-full">{children}</div>
      </div>

      {openFilterModal && (
        <Modal
          width={600}
          title={
            <div className="flex gap-2 items-center">
              <FilterOutlined />
              <span>Filters on</span>
              <div
                style={{
                  background: jobLeadsWithStatus.color
                    ? jobLeadsWithStatus.color
                    : '#f5f5f6',
                }}
                className="p-2 rounded-md"
              >
                {jobLeadsWithStatus?.name}
              </div>
            </div>
          }
          open={openFilterModal}
          footer={null}
          onCancel={closeFilterModal}
        >
          <div className="flex flex-col gap-4">
            <div>
              <div className="border p-5 rounded-lg">
                <div className="text-base font-medium pb-2">
                  Add Filters (condition{' '}
                  <p className="font-bold inline">AND </p>
                  condition)
                </div>
                <Form
                  form={form}
                  name="dynamic_form_nest_item"
                  onFinish={onFinish}
                  autoComplete="off"
                  className="flex flex-col gap-2"
                >
                  <Form.List name="filters">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, ...restField }, index) => (
                          <Space
                            key={key}
                            style={{
                              display: 'flex',
                            }}
                            align="baseline"
                          >
                            <Form.Item
                              {...restField}
                              name={[name, 'field']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Please choose a field name',
                                },
                              ]}
                            >
                              <Select
                                showSearch
                                style={{
                                  width: 220,
                                }}
                                placeholder="Search to Select"
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                  (option?.label ?? '').includes(input)
                                }
                                filterSort={(optionA, optionB) =>
                                  (optionA?.label ?? '')
                                    .toLowerCase()
                                    .localeCompare(
                                      (optionB?.label ?? '').toLowerCase()
                                    )
                                }
                                options={fieldItems}
                                onChange={handleChangeFieldName(key)}
                              />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Missing value',
                                },
                              ]}
                            >
                              {checkValueInput(index)}
                            </Form.Item>
                            <MinusCircleOutlined onClick={() => remove(name)} />
                          </Space>
                        ))}
                        <Form.Item>
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            icon={<PlusOutlined />}
                          >
                            Add a Field to Filter
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>

                  <div className="w-full grid grid-cols-2 gap-2">
                    <Button
                      htmlType="button"
                      // disabled={filtersWatch?.length === 0 || !filtersWatch}
                      onClick={handleClearFilter}
                      icon={<CloseOutlined />}
                      danger
                    >
                      Clear
                    </Button>
                    <Button
                      disabled={filtersWatch?.length === 0}
                      htmlType="submit"
                      icon={<BgColorsOutlined />}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </Form>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default HorizontalContainer;
