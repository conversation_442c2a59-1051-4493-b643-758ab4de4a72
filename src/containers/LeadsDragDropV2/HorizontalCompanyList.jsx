/* eslint-disable react/no-unknown-property */
/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useRef, useState } from 'react';
import _get from 'lodash/get';
import { Collapse, Spin, Image, Checkbox, Tag } from 'antd';
import './style.css';
import zileo<PERSON>ogo from '../../assets/img/welcome/logo.png';
import { getLinkS3 } from '../../services/aws';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import clsx from 'clsx';
import {
  BookOutlined,
  CalendarOutlined,
  ClusterOutlined,
  ContainerOutlined,
  DragOutlined,
  EnvironmentOutlined,
  PoundCircleOutlined,
  RightCircleOutlined,
  RightSquareTwoTone,
  UserOutlined,
} from '@ant-design/icons';
import { parsedCompanyId } from '.';
import LeadCard from '../../components/JobsLeads/LeadCard';

import { List } from 'react-virtualized';
import dayjs from 'dayjs';
import CompanyCompact from './HorizontalView/CompanyCompact';
import { formatNumber } from '../../components/BullHorn/BullHornJobSubmissionForm';
import HorizontalLeadCardV2 from '../../components/JobsLeads/HorizontalLeadCardV2';
import styled from 'styled-components';
import NoteRow from '../../components/JobsLeads/NoteRow';
import { currencyFormatter } from '../../helpers/util';

const StyledCollapse = styled(Collapse)`
  .ant-collapse-content-box {
    padding: 0px 15px !important;
  }
  .ant-collapse-expand-icon {
    padding-inline-start: 0px !important;
  }
`;

const panelStyle = {
  // marginBottom: 8,
  background: 'white',
  borderRadius: 8,
  borderWidth: 1,
  boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  alignItems: 'center',
};

const HorizontalCompanyList = React.memo(function InnerQuoteList({
  leads,
  logoCompany,
  isSelectionMode,
  onChangeCheckbox,
  selectedRowKeys,
  isDisableDnd,
  handleDeleteLead,
  company_id,
  jl_company_name,
  reloadJobLeads,
  title,
  statusId,
  isOverlay = false,
  companyIdRaw,
  isOver = false,

  setJobLeads,
  jobLeads,
}) {
  const [imageUrl, setImageUrl] = useState(null);
  const [key, setKey] = useState([]);
  const jlCompanyNameRef = useRef(null);
  const [companyNameWidth, setCompanyNameWidth] = useState(0);

  const collapseRef = useRef(null); // Create a ref for the Collapse component
  const [collapseWidth, setCollapseWidth] = useState(0); // State to store the width

  const firstLead = leads?.length === 1 ? leads?.[0] : null;

  useEffect(() => {
    if (jlCompanyNameRef.current) {
      setCompanyNameWidth(jlCompanyNameRef.current.offsetWidth);
    }
  }, [jl_company_name]);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    over,
  } = useSortable({
    id: `${parsedCompanyId(company_id)}`,
    data: {
      type: 'company',
    },
    transition: {
      duration: 150, // milliseconds
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });

  const fetchImageUrl = async () => {
    const logoCompaniesLocalStorage = sessionStorage.getItem('LOGO_COMPANIES');
    const logoCompanies = logoCompaniesLocalStorage
      ? JSON.parse(logoCompaniesLocalStorage)
      : [];

    const existingLogo = logoCompanies.find(
      (item) => item?.companyLogoId === logoCompany
    );

    if (existingLogo) {
      setImageUrl(existingLogo?.data);
    } else {
      try {
        const { data } = await getLinkS3(logoCompany);

        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            { companyLogoId: logoCompany, data },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
        }

        setImageUrl(data);
      } catch (error) {
        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            {
              companyLogoId: logoCompany,
              data:
                document?.location?.origin + '/src/assets/img/welcome/logo.png',
            },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
          setImageUrl(zileoLogo);
        } else {
          setImageUrl(existingLogo);
        }
      }
    }
  };

  useEffect(() => {
    fetchImageUrl();
  }, []);

  useEffect(() => {
    if (collapseRef.current) {
      setCollapseWidth(collapseRef.current.getBoundingClientRect().width);
    }
  }, [collapseRef.current]); // Update the width when the ref changes

  const isChecked = () => {
    const comparingLeadIds = leads.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );

    if (leads?.length === comparingLeadIds?.length) {
      return true;
    } else {
      return false;
    }
  };

  const listenerData = key?.length === 0 ? listeners : {};

  const collapseItem = [
    {
      key: company_id,
      label: (
        <div className="grid grid-cols-12 gap-1">
          <div
            {...listenerData}
            className={`font-medium flex items-start flex flex-col col-span-8 ${key?.length === 0 ? 'cursor-grab' : ''}`}
          >
            <div
              className={`relative grid grid-cols-8 gap-2 w-full items-center`}
            >
              <div className="flex items-center justify-center">
                {firstLead ? (
                  <Tag icon={<CalendarOutlined />} color="cyan">
                    {dayjs(firstLead?.date_added).format('DD/MM/YYYY')}
                  </Tag>
                ) : (
                  <Tag icon={<CalendarOutlined />} color="cyan">
                    Grouped
                  </Tag>
                )}
              </div>
              <div className="col-span-3">
                <CompanyCompact
                  value={null}
                  row={{ logoCompany, leads, jl_company_name }}
                  index={null}
                />
              </div>
              <div className="col-span-3">
                {firstLead ? (
                  <div>
                    <div
                      className="font-medium line-clamp-1"
                      title={firstLead?.title}
                    >
                      {firstLead?.title}
                    </div>
                    <div className="flex gap-4 justify-start items-center text-xs opacity-60">
                      <span
                        className="flex items-center gap-1 line-clamp-1"
                        title={firstLead?.employment_type}
                      >
                        <ContainerOutlined />
                        {firstLead?.employment_type}
                      </span>
                      <span
                        className="flex items-center gap-1 line-clamp-1"
                        title={
                          firstLead?.address_city || firstLead?.address_country
                        }
                      >
                        <EnvironmentOutlined />
                        {firstLead?.address_city || firstLead?.address_country}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-start">
                    <Tag icon={<ClusterOutlined />} color="magenta">
                      Grouped
                    </Tag>
                  </div>
                )}
              </div>
              <div className="col-span-1">
                {firstLead ? (
                  <div className="font-medium">{`${currencyFormatter(firstLead?.salary)}`}</div>
                ) : (
                  <div className="flex items-center justify-start">
                    <Tag icon={<PoundCircleOutlined />} color="geekblue">
                      Grouped
                    </Tag>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="col-span-3 flex items-center">
            {firstLead ? (
              <NoteRow lead={firstLead} />
            ) : (
              // <div className="flex items-center justify-start gap-1 font-medium text-sm">
              //   <BookOutlined />{' '}
              //   <span>firstLead?.company_contact_name}</span>
              // </div>
              <div className="flex items-center justify-center w-full">
                <Tag icon={<BookOutlined />} color="purple">
                  Grouped
                </Tag>
              </div>
            )}
          </div>
          <div
            className="col-span-1 relative w-full h-full flex items-center justify-end"
            draggable={false}
            onClick={(e) => e.stopPropagation()}
          >
            {firstLead?.title?.trim() && ( // title of lead
              <div className="flex items-center justify-center">
                <LeadCard
                  setJobLeads={setJobLeads}
                  jobLeads={jobLeads}
                  lead={firstLead}
                  reloadJobLeads={reloadJobLeads}
                  handleDeleteLead={handleDeleteLead}
                  isButtonMode={true}
                  companyLogoUrl={imageUrl}
                />
              </div>
            )}
          </div>
        </div>
      ),
      children: (
        <div className={`w-full flex justify-center`}>
          <List
            width={collapseWidth}
            height={leads?.length > 4 ? 300 : leads?.length * 70}
            rowHeight={70}
            rowRenderer={({ index, key, style }) => (
              <HorizontalLeadCardV2
                style={style}
                lead={leads[index]}
                reloadJobLeads={reloadJobLeads}
                handleDeleteLead={handleDeleteLead}
                companyLogoUrl={imageUrl}
                setJobLeads={setJobLeads}
                jobLeads={jobLeads}
              />
            )}
            rowCount={leads?.length}
            overscanRowCount={3}
          />
        </div>
      ),
      // style: panelStyle,
    },
  ];

  return (
    <div
      ref={setNodeRef}
      // {...listeners}
      //   {...attributes}
      style={{
        transition,
        transform: CSS.Translate.toString(transform),
      }}
      className={clsx(
        'bg-white w-full border border-transparent hover:border-gray-200 cursor-pointer relative hover:shadow hover:shadow-md',
        isDragging && 'opacity-50'
      )}
    >
      {isOver && (
        <div className="absolute -top-2 left-0 w-full border-gray-500 border-2"></div>
      )}
      <StyledCollapse
        ref={collapseRef}
        collapsible={isSelectionMode ? 'disabled' : 'icon'}
        bordered={false}
        expandIconPosition="end"
        expandIcon={({ isActive }) =>
          leads?.length === 1 ? (
            <></>
          ) : (
            <RightSquareTwoTone className="!text-lg" rotate={isActive && 90} />
          )
        }
        activeKey={key}
        items={collapseItem}
        style={{
          background: 'transparent',
        }}
        onChange={(key) => {
          setKey([...key]);
        }}
      />
    </div>
  );
});

export default HorizontalCompanyList;
