import { useMemo, useState } from 'react';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _range from 'lodash/range';

// DnD
import {
  DndContext,
  DragOverlay,
  KeyboardSensor,
  MeasuringStrategy,
  PointerSensor,
  closestCorners,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import Container from './Container';
import Items from './Items';
import { v4 as uuid } from 'uuid';
import {
  useChangeLogOfJobLeadsMutation,
  useChangeStatusByCompanyMutation,
  useLazyGetJobLeadsCompaniesQuery,
  useLazySearchJobLeadsCompaniesQuery,
  useUpdateBulkLeadsMutation,
} from '../../app/myLeads';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { Col } from 'antd';
import { useAuth } from '../../store/auth';
import { useEffect } from 'react';
import { Fragment } from 'react';
import ColumnDragDrop from '../../components/JobsLeads/ColumnDragDrop';
import { arrayUniqueByKey } from '../../utils/common';
import CompanyList from './CompanyList';
import LeadCard from '../../components/JobsLeads/LeadCard';
import { MODE, TYPE } from '../MyLeads';
import { PlusCircleOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import HorizontalContainer from './HorizontalContainer';
import HorizontalCompanyList from './HorizontalCompanyList';
import HorizontalLeadCardV2 from '../../components/JobsLeads/HorizontalLeadCardV2';

export const parsedStatusId = (id) => `status/${id}`;
export const parsedCompanyId = (id) => `company/${id}`;
export const parsedLeadId = (id) => `lead/${id}`;

const LeadsDragDropV2 = ({
  jobLeadsWithStatuses,
  reloadJobLeads,
  searchText,
  searchedLeads,
  setJobLeadsGlobal,
  rawSearchedLeads,
  isSelectionMode,
  onChangeCheckbox,
  setSelectedRowKeys,
  selectedRowKeys,
  rawJobLeadsWithStatuses,
  rawSelectedRowKeys,
  setDisableSearchByCompany,
  leadSheetId,
  handleManageStatusesClick,
  mode,
}) => {
  const { profile } = useAuth();
  const [jobLeads, setJobLeads] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [companyOverIndex, setCompanyOverIndex] = useState(null);
  const [companyOverStatusId, setCompanyOverStatusId] = useState(null);

  const [leadLoading, setLeadLoading] = useState(true);
  const [inColumnLoading, setInColumnLoading] = useState(false);

  const userId = useMemo(() => {
    return _get(profile, 'user.id') || '';
  }, [profile]);

  //   My leads board business logic
  const [changeLogOfJobLeads] = useChangeLogOfJobLeadsMutation();
  const [changeStatusByCompany] = useChangeStatusByCompanyMutation();
  const [updateBulkLeadsMutation] = useUpdateBulkLeadsMutation();

  const [getJobLeadsCompanies] = useLazyGetJobLeadsCompaniesQuery();

  const [searchJobLeadsCompaniesQuery] = useLazySearchJobLeadsCompaniesQuery();

  const getLeads = async ({ statusId, companyId }) => {
    const companyData = await searchJobLeadsCompaniesQuery({
      statusId,
      companyId,
    });

    const convertData = await _get(companyData, 'data.result.items', []).map(
      (lead) => ({
        ...lead,
        id: `${lead.id}/${companyId}/${statusId}`,
        leadRawId: lead?.id,
        leadSheetId,
      })
    );
    return convertData;
  };

  const getCompanies = async (page = 1) => {
    setInColumnLoading(true);
    const getCompaniesPromises = await Promise.all(
      jobLeadsWithStatuses.map(async (jobLead) => {
        return await getJobLeadsCompanies({
          statusId: jobLead.id,
          page: page,
          search: searchText || '',
          limit: 10,
        });
      })
    );

    const jobLeadsWithStatusesTemp = await Promise.all(
      jobLeadsWithStatuses.map(async (jobLead) => {
        const companies = getCompaniesPromises.find(
          (companyObj) => companyObj.originalArgs.statusId === jobLead.id
        );
        let companiesList = _get(companies, 'data.result.items', []);
        if (companiesList.length > 0) {
          const companiesListTemp = await Promise.all(
            companiesList.map(async (company) => {
              // if (company?.company_id === 'bullhorn-client-corporate-23583') {
              //   return {
              //     ...company,
              //     leads: [],
              //     company_id: `${company.company_id}/${jobLead.id}`,
              //     companyIdRaw: company.company_id,
              //   };
              // }

              const leadsTemp = await getLeads({
                statusId: jobLead.id,
                companyId: company.company_id,
              });
              if (leadsTemp.length > 0) {
                return {
                  ...company,
                  leads: leadsTemp,
                  company_id: `${company.company_id}/${jobLead.id}`,
                  companyIdRaw: company.company_id,
                };
              } else {
                return {
                  ...company,
                  leads: [],
                  company_id: `${company.company_id}/${jobLead.id}`,
                  companyIdRaw: company.company_id,
                };
              }
            })
          );
          companiesList = [...companiesListTemp];
        }

        if (companies) {
          return { ...jobLead, companies: companiesList || [] };
        }
        return { ...jobLead, companies: [] };
      })
    );

    setInColumnLoading(false);

    return jobLeadsWithStatusesTemp;
  };

  useEffect(() => {
    if (jobLeadsWithStatuses.length > 0 && searchedLeads?.length === 0) {
      getCompanies().then((res) => {
        setJobLeads(res);
        setJobLeadsGlobal(res);
        setLeadLoading(false);
      });
    } else {
      setLeadLoading(false);
      setJobLeads([]);
      setJobLeadsGlobal([]);
    }
  }, [jobLeadsWithStatuses, searchText]);

  useEffect(() => {
    if (searchedLeads?.length > 0) {
      setJobLeads([...searchedLeads]);
    } else {
      setJobLeads(jobLeadsWithStatuses);
    }
  }, [searchedLeads]);

  const handleDeleteLead = (leadToDelete) => {
    const newJobLeads = jobLeads?.map((status) => {
      const newCompanies = status?.companies
        ?.map((company) => {
          const newLeads = company?.leads?.filter(
            (lead) => lead?.leadRawId !== leadToDelete?.leadRawId
          );
          return { ...company, leads: newLeads };
        })
        .filter((company) => company?.leads?.length > 0);

      return {
        ...status,
        companies: newCompanies,
      };
    });
    setJobLeads([...newJobLeads]);
  };

  // filter board

  const handleFilterLeadsByCompanyName = () => {
    if (searchedLeads?.length > 0) {
      const newJobLeadsWithStatuses = searchedLeads.map((status) => {
        if (status?.companies?.length === 0 || !status?.companies)
          return { ...status };
        const newCompanies = status.companies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
        return { ...status, companies: newCompanies };
      });
      setJobLeads([...newJobLeadsWithStatuses]);
    } else {
      const newJobLeadsWithStatuses = rawJobLeadsWithStatuses.map((status) => {
        if (status?.companies?.length === 0 || !status?.companies)
          return { ...status };
        const newCompanies = status.companies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
        return { ...status, companies: newCompanies };
      });
      setJobLeads([...newJobLeadsWithStatuses]);
    }
    const newSelectedRowKeys = rawSearchedLeads
      .filter(
        (lead) =>
          lead.company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase()) &&
          rawSelectedRowKeys.includes(lead?.id)
      )
      .map((lead) => lead.id);
    setSelectedRowKeys([...newSelectedRowKeys]);
  };

  const onSubmitBoardFilter = (filteringStatusId, query) => {
    // haven't master refine and search by company
    const newJobLeadsWithStatuses = jobLeads.map((status) => {
      if (status.id !== filteringStatusId) return { ...status };

      const leadsData =
        searchedLeads?.length > 0
          ? [...searchedLeads]
          : [...rawJobLeadsWithStatuses];

      const rawCompanies = leadsData.find(
        (status) => status.id === filteringStatusId
      ).companies;

      let newCompanies = rawCompanies
        .map((company) => {
          if (company?.leads?.length === 0 || !company?.leads || !query)
            return { ...company };
          const newLeads = company?.leads?.filter((lead) => {
            let score = 0;
            for (const [key, value] of Object.entries(query)) {
              if (
                lead[key]
                  ?.trim()
                  ?.toLowerCase()
                  ?.includes(value?.trim()?.toLowerCase())
              ) {
                score += 1;
              }
            }
            return score === Object.keys(query).length;
          });

          return { ...company, leads: newLeads };
        })
        .filter((company) => company?.leads?.length > 0);

      if (searchText) {
        newCompanies = newCompanies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
      }

      return { ...status, companies: newCompanies, filters: query };
    });

    setJobLeads([...newJobLeadsWithStatuses]);
  };

  useEffect(() => {
    if (jobLeadsWithStatuses?.length > 0) {
      handleFilterLeadsByCompanyName();
    }
  }, [searchText]);

  useEffect(() => {
    if (jobLeads?.length === 0) return;
    const newSelectedRowKeys = jobLeads
      .flatMap((status) => status?.companies || [])
      .flatMap((company) => company?.leads || [])
      .map((lead) => lead?.id);

    const isDisableSearchByCompany = jobLeads.some(
      (status) => Object.keys(status?.filters || {}).length > 0
    );
    setDisableSearchByCompany(isDisableSearchByCompany);
    setSelectedRowKeys([...newSelectedRowKeys]);
  }, [JSON.stringify(jobLeads)]);

  // Find the value of the items
  const findCompanyByRawId = (id) => {
    const allCompanies = [...jobLeads]?.flatMap((item) => item?.companies);
    const company = allCompanies.find((item) =>
      id.includes(item?.companyIdRaw)
    );

    return company;
  };

  const groupingTheCompanies = (statusLeads) =>
    statusLeads.map((status) => {
      const groupedKey = 'companyIdRaw';
      const uniquedCompanyIds = [
        ...new Set([
          ...status?.companies?.map(
            (company) => company && company[groupedKey]
          ),
        ]),
      ];
      const tempCompanies = [...status?.companies];
      const uniquedCompanies = uniquedCompanyIds
        .map((uniquedCompanyId) => {
          let company = {};
          const foundCompanies = tempCompanies.filter(
            (comp) => comp && comp[groupedKey] === uniquedCompanyId
          );
          if (foundCompanies?.length > 1) {
            company = {
              ...foundCompanies[1],
              leads: foundCompanies[1]?.leads?.concat([
                ...foundCompanies[0]?.leads,
              ]),
            };
          } else if (foundCompanies?.length === 1) {
            company = foundCompanies[0];
          }
          return { ...company };
        })
        .filter((company) => company?.leads?.length > 0);
      // const uniquedCompanies = arrayUniqueByKey(tempCompanies, groupedKey);

      return { ...status, companies: uniquedCompanies };
      // const uniquedCompanies = status?.companies?.map(
      //   (company) => company.company_id
      // );
    });

  function findValueOfItems(id, type) {
    if (type === 'status') {
      return jobLeads.find((item) => parsedStatusId(item.id) === id);
    }
    if (type === 'company') {
      return jobLeads.find((status) =>
        status?.companies?.find(
          (company) => company && parsedCompanyId(company?.company_id) === id
        )
      );
    }

    if (type === 'lead') {
      return jobLeads.find((status) =>
        status?.companies?.find((company) =>
          company?.leads?.find((lead) => lead && parsedLeadId(lead?.id) === id)
        )
      );
    }
  }

  const findItem = (id) => {
    const container = findValueOfItems(id, 'company');
    if (!container) return '';
    const item = container.companies.find(
      (company) => company && parsedCompanyId(company?.company_id) === id
    );
    if (!item) return '';
    return item;
  };

  const findLead = (id) => {
    const container = findValueOfItems(id, 'lead');
    if (!container) return '';
    const allLeads = container.companies.flatMap(
      (company) => company && company?.leads
    );
    const item = allLeads?.find((item) => parsedLeadId(item?.id) === id);
    if (!item) return '';
    return item;
  };

  const findContainerTitle = (id) => {
    const container = findValueOfItems(id, 'status');
    if (!container) return '';
    return container.name;
  };

  const findContainerItems = (id) => {
    const container = findValueOfItems(id, 'status');
    if (!container) return [];
    return container.companies;
  };

  const findContainerByItemId = (id) =>
    jobLeads.filter((status) =>
      status?.companies?.some(
        (company) => parsedCompanyId(company?.company_id) === id
      )
    )[0] || null;

  const handleUpdateCompanyDnd = (payload) => {
    if (!searchedLeads || searchedLeads?.length === 0) {
      changeStatusByCompany(payload);
    } else {
      const leadsIdList = rawSearchedLeads
        .filter((lead) => lead?.company_id === payload.companyId)
        .map((lead) => lead?.id);

      leadsIdList.forEach((leadId) => {
        payload.jobLeadId = leadId;
        changeLogOfJobLeads(payload);
      });
    }
  };

  const handleUpdateLeadDnd = (payload) => changeLogOfJobLeads(payload);

  // DND Handlers
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  function handleDragStart(event) {
    const { active } = event;
    const { id } = active;
    setActiveId(id);
  }

  const handleDragMove = (event) => {
    const { active, over } = event;
    setCompanyOverIndex(null);
    setCompanyOverStatusId(null);
    // Handling status Sorting
    if (
      active.id.toString().includes('status') &&
      over?.id.toString().includes('status') &&
      active &&
      over &&
      active.id !== over.id
    ) {
      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        ({ id }) => parsedStatusId(id) === active.id
      );
      const overContainerIndex = jobLeads.findIndex(
        ({ id }) => parsedStatusId(id) === over.id
      );
      // Swap the active and over container
      let newItems = [...jobLeads];
      newItems = arrayMove(newItems, activeContainerIndex, overContainerIndex);
      setJobLeads(newItems);
    }

    // Handle company Sorting
    if (
      active.id.toString().includes('company') &&
      over?.id.toString().includes('company') &&
      active &&
      over &&
      active.id !== over.id
    ) {
      // Find the active container and over container
      const activeContainer = findValueOfItems(active.id, 'company');
      const overContainer = findValueOfItems(over.id, 'company');

      // If the active or over container is not found, return
      if (!activeContainer || !overContainer) return;

      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        (container) => container.id === activeContainer.id
      );
      const overContainerIndex = jobLeads.findIndex(
        (container) => container.id === overContainer.id
      );

      // Find the index of the active and over item
      const activeitemIndex = activeContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === active.id
      );
      const overitemIndex = overContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === over.id
      );

      // In the same container
      if (activeContainerIndex === overContainerIndex) {
        let newItems = [...jobLeads];
        newItems[activeContainerIndex].companies = arrayMove(
          newItems[activeContainerIndex].companies,
          activeitemIndex,
          overitemIndex
        );
        setJobLeads(newItems);
      } else {
        setCompanyOverIndex(overitemIndex);
        setCompanyOverStatusId(jobLeads[overContainerIndex]?.id);
      }
      // else
      // {
      //   // In different containers
      //   let newItems = [...jobLeads];
      //   const [removeditem] = newItems[activeContainerIndex].companies.splice(
      //     activeitemIndex,
      //     1
      //   );

      //   const newLeads = removeditem?.leads?.map((lead) => ({
      //     ...lead,
      //     id: `${lead.leadRawId}/${removeditem.companyIdRaw}/${overContainer.id}`,
      //   }));
      //   const newRemovedItem = {
      //     ...removeditem,
      //     company_id: `${removeditem.companyIdRaw}/${overContainer.id}`,
      //     leads: newLeads,
      //   };

      //   newItems[overContainerIndex].companies.splice(
      //     overitemIndex,
      //     0,
      //     newRemovedItem
      //   );
      //   setJobLeads(newItems);
      // }
    }
  };

  // This is the function that handles the sorting of the containers and items when the user is done dragging.
  function handleDragEnd(event) {
    const { active, over } = event;

    // Handling status Sorting
    if (
      active.id.toString().includes('status') &&
      over?.id.toString().includes('status') &&
      active &&
      over
    ) {
      const payload = {
        data: jobLeads.map((item, index) => ({
          id: item?.id,
          orderOfDisplay: index,
        })),
      };

      // Update status on db
      updateBulkLeadsMutation({ payload });
    }

    // Handling Company Drop Into a Status
    if (
      active.id.toString().includes('company') &&
      over?.id.toString().includes('status') &&
      active &&
      over &&
      active.id !== over.id
    ) {
      // Find the active and over container
      const activeContainer = findValueOfItems(active.id, 'company');
      const overContainer = findValueOfItems(over.id, 'status');

      // If the active or over container is not found, return
      if (!activeContainer || !overContainer) return;

      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        (container) => container.id === activeContainer.id
      );
      const overContainerIndex = jobLeads.findIndex(
        (container) => container.id === overContainer.id
      );

      // Find the index of the active and over item
      const activeitemIndex = activeContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === active.id
      );

      // Remove the active item from the active container and add it to the over container
      let newItems = [...jobLeads];
      const [removeditem] = newItems[activeContainerIndex].companies.splice(
        activeitemIndex,
        1
      );
      const newLeads =
        removeditem?.leads?.length > 0
          ? removeditem?.leads?.map((lead) => ({
              ...lead,
              id: `${lead.leadRawId}/${removeditem.companyIdRaw}/${overContainer.id}`,
            }))
          : [];
      const newRemovedItem = {
        ...removeditem,
        company_id: `${removeditem.companyIdRaw}/${overContainer.id}`,
        leads: newLeads,
      };
      if (newItems[overContainerIndex]?.companies?.length > 0) {
        newItems[overContainerIndex].companies.push(newRemovedItem);
      } else {
        newItems[overContainerIndex].companies = [newRemovedItem];
      }
      setJobLeads(groupingTheCompanies(newItems));
      const newStatusCompanyIdsOrder = newItems[
        overContainerIndex
      ].companies.map((company) => company?.companyIdRaw);
      // Update on db
      const payload = {
        newStatusId: overContainer.id,
        updatedFor: userId,
        companyId: removeditem.companyIdRaw,
        newStatusCompanyIdsOrder,
      };
      handleUpdateCompanyDnd(payload);
    }

    // Handling company Sorting
    if (
      active.id.toString().includes('company') &&
      over?.id.toString().includes('company') &&
      active &&
      over
      // && active.id !== over.id
    ) {
      // Find the active and over container
      const activeContainer = findValueOfItems(active.id, 'company');
      const overContainer = findValueOfItems(over.id, 'company');

      // If the active or over container is not found, return
      if (!activeContainer || !overContainer) return;
      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        (container) => container.id === activeContainer.id
      );
      const overContainerIndex = jobLeads.findIndex(
        (container) => container.id === overContainer.id
      );
      // Find the index of the active and over item
      const activeitemIndex = activeContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === active.id
      );
      const overitemIndex = overContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === over.id
      );

      // In the same container
      if (activeContainerIndex === overContainerIndex) {
        let newItems = [...jobLeads];
        const droppedCompany =
          newItems[activeContainerIndex].companies[activeitemIndex];

        newItems[activeContainerIndex].companies = arrayMove(
          newItems[activeContainerIndex].companies,
          activeitemIndex,
          overitemIndex
        );
        setJobLeads(newItems);

        // Update on db
        const newStatusCompanyIdsOrder = newItems[
          activeContainerIndex
        ].companies?.map((company) => company?.companyIdRaw);

        const payload = {
          newStatusId: overContainer.id,
          updatedFor: userId,
          companyId: droppedCompany?.companyIdRaw,
          newStatusCompanyIdsOrder,
        };
        handleUpdateCompanyDnd(payload);
      } else {
        // In different containers
        let newItems = [...jobLeads];
        const [removeditem] = newItems[activeContainerIndex].companies.splice(
          activeitemIndex,
          1
        );
        const newLeads = removeditem?.leads?.map((lead) => ({
          ...lead,
          id: `${lead.leadRawId}/${removeditem.companyIdRaw}/${overContainer.id}`,
        }));
        const newRemovedItem = {
          ...removeditem,
          company_id: `${removeditem.companyIdRaw}/${overContainer.id}`,
          leads: newLeads,
        };
        // const isSplice =
        //   overitemIndex < newItems[overContainerIndex].companies?.length - 1;
        // if (isSplice) {
        newItems[overContainerIndex].companies.splice(
          overitemIndex,
          0,
          newRemovedItem
        );
        // } else {
        //   newItems[overContainerIndex].companies.push(newRemovedItem);
        // }
        setJobLeads(groupingTheCompanies(newItems));
        const newStatusCompanyIdsOrder = newItems[
          overContainerIndex
        ]?.companies?.map((company) => company?.companyIdRaw);
        // Update on db
        const payload = {
          newStatusId: overContainer.id,
          updatedFor: userId,
          companyId: removeditem.companyIdRaw,
          newStatusCompanyIdsOrder,
        };
        handleUpdateCompanyDnd(payload);
      }
    }

    // Handling company dropping into Container
    if (
      active.id.toString().includes('company') &&
      over?.id.toString().includes('status') &&
      active &&
      over &&
      active.id !== over.id
    ) {
      // Find the active and over container
      const activeContainer = findValueOfItems(active.id, 'company');
      const overContainer = findValueOfItems(over.id, 'status');

      // If the active or over container is not found, return
      if (!activeContainer || !overContainer) return;
      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        (container) => container.id === activeContainer.id
      );
      const overContainerIndex = jobLeads.findIndex(
        (container) => container.id === overContainer.id
      );
      // Find the index of the active and over item
      const activeitemIndex = activeContainer.companies.findIndex(
        (item) => item && parsedCompanyId(item.company_id) === active.id
      );

      let newItems = [...jobLeads];
      const [removeditem] = newItems[activeContainerIndex].companies.splice(
        activeitemIndex,
        1
      );

      const newLeads = removeditem?.leads?.map((lead) => ({
        ...lead,
        id: `${lead.leadRawId}/${removeditem.companyIdRaw}/${overContainer.id}`,
      }));
      const newRemovedItem = {
        ...removeditem,
        company_id: `${removeditem.companyIdRaw}/${overContainer.id}`,
        leads: newLeads,
      };
      if (newItems[overContainerIndex]?.companies?.length > 0) {
        newItems[overContainerIndex].companies.push(newRemovedItem);
      } else {
        newItems[overContainerIndex].companies = [newRemovedItem];
      }
      setJobLeads(newItems);

      // Update on db
      const newStatusCompanyIdsOrder = newItems[
        overContainerIndex
      ]?.companies?.map((company) => company?.companyIdRaw);

      const payload = {
        newStatusId: overContainer.id,
        updatedFor: userId,
        companyId: removeditem.companyIdRaw,
        newStatusCompanyIdsOrder,
      };

      handleUpdateCompanyDnd(payload);
    }

    // Handling company dropping into container that already has this company
    const activeCompanyId = active.id.toString().includes('company')
      ? active?.id?.split('/')[1]
      : active?.id?.split('/')[2];
    const overCompanyId = over?.id?.split('/')[1];
    if (
      active.id.toString().includes('company') &&
      over?.id.toString().includes('company') &&
      active &&
      over &&
      activeCompanyId === overCompanyId
    ) {
      let newItems = [...jobLeads];
      setJobLeads(groupingTheCompanies(newItems));
    }

    // Handling lead dropping into container
    if (
      active.id.toString().includes('lead') &&
      active &&
      over &&
      active.id !== over.id
    ) {
      const splittedActiveId = active?.id?.toString()?.split('/');
      const splittedOverId = over?.id?.toString()?.split('/');

      const leadId = splittedActiveId[1];
      const companyId = splittedActiveId[2];
      const activeStatusId = splittedActiveId[3];

      const overStatusId = splittedOverId[splittedOverId?.length - 1];

      if (!leadId || !overStatusId || !companyId || !activeStatusId) return;
      // Find the active and over container
      const company = findItem(
        parsedCompanyId(`${companyId}/${activeStatusId}`)
      );
      const activeLead = findLead(active?.id);
      const overContainer = jobLeads.find((item) => item?.id === overStatusId);

      if (!overContainer || !activeLead) return;
      // Find the index of the active and over container
      const activeContainerIndex = jobLeads.findIndex(
        (container) => container?.id === activeStatusId
      );
      const overContainerIndex = jobLeads.findIndex(
        (container) => container?.id === overStatusId
      );

      const activeCompanyIndex = jobLeads[
        activeContainerIndex
      ].companies.findIndex((item) => item && item.companyIdRaw === companyId);

      if (
        activeContainerIndex === -1 ||
        overContainerIndex === -1
        // || activeContainerIndex === overContainerIndex
      ) {
        return;
      }

      // Remove the active item from the active container and add it to the over container
      if (activeContainerIndex === overContainerIndex) return;

      const activeLeadIndex = jobLeads[activeContainerIndex].companies[
        activeCompanyIndex
      ]?.leads?.findIndex((lead) => lead && lead?.id === activeLead?.id);

      const overCompaniesIndex = jobLeads[
        overContainerIndex
      ]?.companies?.findIndex(
        (company) =>
          company && company.company_id === `${companyId}/${overStatusId}`
      );

      let newItems = [...jobLeads];
      const [removeditem] = newItems[activeContainerIndex].companies[
        activeCompanyIndex
      ].leads.splice(activeLeadIndex, 1);

      const newRemovedItem = {
        ...removeditem,
        id: `${removeditem.leadRawId}/${companyId}/${overStatusId}`,
      };

      // In case over container doesn't have this company
      if (overCompaniesIndex < 0) {
        const newCompany = {
          ...company,
          company_id: `${company.companyIdRaw}/${overStatusId}`,
        };
        if (newItems[overContainerIndex]?.companies?.length > 0) {
          newItems[overContainerIndex].companies.push({
            ...newCompany,
            leads: [newRemovedItem],
          });
        } else {
          newItems[overContainerIndex].companies = [
            {
              ...newCompany,
              leads: [newRemovedItem],
            },
          ];
        }
      }
      // In case over container has this company
      else {
        newItems[overContainerIndex].companies[overCompaniesIndex].leads.push({
          ...newRemovedItem,
        });
      }
      // Update on db
      const payload = {
        newStatusId: overContainer.id,
        updatedFor: userId,
        jobLeadId: removeditem.leadRawId,
      };
      handleUpdateLeadDnd(payload);
      setJobLeads(groupingTheCompanies(newItems));
    }

    setActiveId(null);
    setCompanyOverIndex(null);
    setCompanyOverStatusId(null);
  }

  return (
    <div
      className={clsx(
        `leads-drag-drop-container h-full flex`,
        mode === MODE.VERTICAL && 'flex-row gap-7',
        mode === MODE.HORIZONTAL && 'flex-col'
      )}
    >
      {mode === MODE.VERTICAL && (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCorners}
          onDragStart={handleDragStart}
          onDragMove={handleDragMove}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            disabled={isSelectionMode}
            items={jobLeads.map(({ id }) => parsedStatusId(id))}
          >
            {!leadLoading &&
              jobLeads?.length > 0 &&
              _map(jobLeads, (jobLeadsWithStatus, index) => {
                const { id, name, companies } = jobLeadsWithStatus;
                const newId = parsedStatusId(id);
                return (
                  <>
                    <Container
                      mode={mode}
                      id={newId}
                      title={name}
                      key={newId}
                      jobLeadsWithStatus={jobLeadsWithStatus}
                      onSubmitBoardFilter={onSubmitBoardFilter}
                      rawFilters={jobLeadsWithStatus?.filters}
                      isSelectionMode={isSelectionMode}
                      selectedRowKeys={selectedRowKeys}
                      onChangeCheckbox={onChangeCheckbox}
                    >
                      <SortableContext
                        disabled={isSelectionMode}
                        items={companies?.map(
                          (company) =>
                            company && parsedCompanyId(company?.company_id)
                        )}
                      >
                        <SortableContext
                          disabled={isSelectionMode}
                          items={
                            companies &&
                            companies
                              ?.flatMap((company) =>
                                company?.leads?.length > 0
                                  ? [...company?.leads]
                                  : []
                              )
                              .map((lead) => parsedLeadId(lead?.id))
                          }
                        >
                          {/* Temp solution for now */}
                          {/* {type === TYPE.VACANCIES && ( */}
                          <Items
                            companyOverIndex={
                              companyOverStatusId === id
                                ? companyOverIndex
                                : null
                            }
                            // title={company?.jl_company_name}
                            jobLeadsWithStatus={jobLeadsWithStatus}
                            handleDeleteLead={handleDeleteLead}
                            reloadJobLeads={reloadJobLeads}
                            isSelectionMode={isSelectionMode}
                            onChangeCheckbox={onChangeCheckbox}
                            selectedRowKeys={selectedRowKeys}
                            rawFilters={jobLeadsWithStatus?.filters}
                            setJobLeads={setJobLeads}
                            jobLeads={jobLeads}
                            setJobLeadsGlobal={setJobLeadsGlobal}
                            inColumnLoading={inColumnLoading}
                          />
                          {/* )} */}
                        </SortableContext>
                      </SortableContext>
                    </Container>
                  </>
                );
              })}
            {!leadLoading && jobLeads?.length === 0 && (
              <div
                onClick={handleManageStatusesClick}
                className={clsx(
                  'w-full h-11/12 bg-[#F2F9FF] border border-dashed rounded-xl flex gap-y-4 min-h-[10rem] max-w-[25%] border-2 justify-center items-center opacity-50 cursor-pointer hover:opacity-100 pb-2',
                  mode === MODE.VERTICAL && 'h-[70vh] flex-col'
                  // mode === MODE.HORIZONTAL && 'flex-col'
                )}
              >
                <div className="flex justify-center items-center gap-2">
                  <PlusCircleOutlined />
                  <span>Add more</span>
                </div>
              </div>
            )}
          </SortableContext>
          <DragOverlay adjustScale={false}>
            {/* Drag Overlay For item Lead */}
            {activeId && activeId.toString().includes('lead') && (
              <LeadCard lead={findLead(activeId)} />
            )}

            {/* Drag Overlay For item Company */}
            {activeId && activeId.toString().includes('company') && (
              <CompanyList {...findCompanyByRawId(activeId)} isOverlay />
            )}

            {/* Drag Overlay For Status */}
            {activeId && activeId.toString().includes('status') && (
              <Container
                key={activeId}
                jobLeadsWithStatus={findValueOfItems(activeId, 'status')}
                id={activeId}
                title={findContainerTitle(activeId)}
                isOverlay={true}
              >
                <div className="flex flex-col gap-2">
                  {findValueOfItems(activeId, 'status')?.companies?.map(
                    (company) => (
                      <CompanyList {...company} isOverlay />
                    )
                  )}
                </div>
              </Container>
            )}
          </DragOverlay>
        </DndContext>
      )}
      {mode === MODE.HORIZONTAL && !leadLoading && (
        <div className="flex flex-col w-full h-full gap-4">
          <div className="grid bg-white rounded-tl-md rounded-tr-md grid-cols-12">
            <div className="col-span-2 p-4 font-semibold">Stage</div>
            <div className="col-span-10 w-full grid grid-cols-9 flex px-[1.1rem] items-center">
              <div className="col-span-1 font-semibold">Date</div>
              <div className="col-span-2 font-semibold">Company Name</div>
              <div className="col-span-2 font-semibold">Title</div>
              <div className="col-span-1 font-semibold pl-5">Salary</div>
              <div className="col-span-2 font-semibold flex items-center justify-center">Notes</div>
              <div className="col-span-1 font-semibold flex justify-end">
                Actions
              </div>
            </div>
          </div>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCorners}
            onDragStart={handleDragStart}
            onDragMove={handleDragMove}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              disabled={isSelectionMode}
              items={jobLeads.map(({ id }) => parsedStatusId(id))}
            >
              {!leadLoading &&
                jobLeads?.length > 0 &&
                _map(jobLeads, (jobLeadsWithStatus, index) => {
                  const { id, name, companies } = jobLeadsWithStatus;
                  const newId = parsedStatusId(id);
                  return (
                    <>
                      <HorizontalContainer
                        mode={mode}
                        id={newId}
                        title={name}
                        key={newId}
                        jobLeadsWithStatus={jobLeadsWithStatus}
                        onSubmitBoardFilter={onSubmitBoardFilter}
                        rawFilters={jobLeadsWithStatus?.filters}
                        isSelectionMode={isSelectionMode}
                        selectedRowKeys={selectedRowKeys}
                        onChangeCheckbox={onChangeCheckbox}
                      >
                        <SortableContext
                          disabled={isSelectionMode}
                          items={companies?.map(
                            (company) =>
                              company && parsedCompanyId(company?.company_id)
                          )}
                        >
                          <SortableContext
                            disabled={isSelectionMode}
                            items={
                              companies &&
                              companies
                                ?.flatMap((company) =>
                                  company?.leads?.length > 0
                                    ? [...company?.leads]
                                    : []
                                )
                                .map((lead) => parsedLeadId(lead?.id))
                            }
                          >
                            {/* Temp solution for now */}
                            {/* {type === TYPE.VACANCIES && ( */}
                            <Items
                              mode={mode}
                              companyOverIndex={
                                companyOverStatusId === id
                                  ? companyOverIndex
                                  : null
                              }
                              // title={company?.jl_company_name}
                              jobLeadsWithStatus={jobLeadsWithStatus}
                              handleDeleteLead={handleDeleteLead}
                              reloadJobLeads={reloadJobLeads}
                              isSelectionMode={isSelectionMode}
                              onChangeCheckbox={onChangeCheckbox}
                              selectedRowKeys={selectedRowKeys}
                              rawFilters={jobLeadsWithStatus?.filters}
                              setJobLeads={setJobLeads}
                              jobLeads={jobLeads}
                              setJobLeadsGlobal={setJobLeadsGlobal}
                              inColumnLoading={inColumnLoading}
                            />
                            {/* )} */}
                          </SortableContext>
                        </SortableContext>
                      </HorizontalContainer>
                    </>
                  );
                })}
              {!leadLoading && jobLeads?.length === 0 && (
                <div
                  onClick={handleManageStatusesClick}
                  className={clsx(
                    'w-full h-11/12 bg-[#F2F9FF] border border-dashed rounded-xl flex flex-col gap-y-4 min-h-[10rem] max-w-[25%] border-2 justify-center items-center opacity-50 cursor-pointer hover:opacity-100 pb-2',
                    mode === MODE.VERTICAL && 'h-[70vh]'
                  )}
                >
                  <div className="flex justify-center items-center gap-2">
                    <PlusCircleOutlined />
                    <span>Add more</span>
                  </div>
                </div>
              )}
            </SortableContext>
            <DragOverlay adjustScale={false}>
              {/* Drag Overlay For item Lead */}
              {activeId && activeId.toString().includes('lead') && (
                <HorizontalLeadCardV2 lead={findLead(activeId)} />
              )}

              {/* Drag Overlay For item Company */}
              {activeId && activeId.toString().includes('company') && (
                <HorizontalCompanyList
                  {...findCompanyByRawId(activeId)}
                  isOverlay
                />
              )}

              {/* Drag Overlay For Status */}
              {activeId && activeId.toString().includes('status') && (
                <HorizontalContainer
                  key={activeId}
                  jobLeadsWithStatus={findValueOfItems(activeId, 'status')}
                  id={activeId}
                  title={findContainerTitle(activeId)}
                  isOverlay={true}
                >
                  <div className="flex flex-col gap-2">
                    {findValueOfItems(activeId, 'status')?.companies?.map(
                      (company) => (
                        <HorizontalCompanyList {...company} isOverlay />
                      )
                    )}
                  </div>
                </HorizontalContainer>
              )}
            </DragOverlay>
          </DndContext>
        </div>
      )}
      {leadLoading &&
        _map(jobLeadsWithStatuses, () => (
          <div className="w-full">
            {_map(_range(1), (_, idx) => (
              <Fragment key={idx.toString()}>
                <Col span={6}>
                  <LoadingAdvanced isSkeleton className={'w-full'} />
                </Col>
              </Fragment>
            ))}
          </div>
        ))}
    </div>
  );
};

export default LeadsDragDropV2;
