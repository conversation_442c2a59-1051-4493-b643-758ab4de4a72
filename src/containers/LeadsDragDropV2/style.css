.drag-item {
  left: auto !important;
  top: auto !important;
}

.leads-drag-drop-container .ant-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* new UI for view leads */
.custom-new-ui-leads {
  width: 100%;
}
.custom-new-ui-leads .ant-modal .ant-modal-content {
  background: transparent !important;
}

.right-content-leads-custom {
  font-size: medium !important;
}

.right-content-leads-custom input,
.right-content-leads-custom .ant-select-selector,
.right-content-leads-custom .ant-input,
.ant-input-group {
  color: gray;
  font-size: medium;
  border-radius: 1rem !important;
}
/* .ant-picker-input input,
.ant-input-affix-wrapper {
  color: #576f89;
}
.ant-input-affix-wrapper,
.ant-input-number,
.ant-picker,
.ant-select-selector,
.ant-input {
  border-radius: 1rem !important;
} */
.right-content-leads-custom .ant-select-selector {
  height: 2.7rem !important;
}
/* .left-content-lead-custom{
  background-color: #0891b2;
} */
/* .lead-view-container .ant-modal-content{
  background-color: #2B80C9;
} */
/* .lead-view-container .ant-modal-title{
  background-color: #2B80C9;
} */
.comment-container .search-input,
.comment-container .search-input:focus,
.comment-container .search-input:focus-within,
.comment-container .search-input:focus-visible {
  border: none !important;
}
.comment-container .ant-form-item {
  margin-bottom: 0px !important;
}

div[data-rbd-droppable-id='board'] {
  height: 90%;
  /* border-color: #0891b2; */
  /* border-width: 1px; */
  /* padding: 5px; */
  /* border-radius: 0.5rem; */
}

.ant-collapse-expand-icon {
  align-self: center;
}

/* .ant-collapse-header {
  background-color: #eff6ff;
} */

/* .ant-modal:has(.open-lead-detail-mode) {
  margin: 0 !important;
} */

/* .ant-collapse-header:has(.job-pinned) {
  background-color: #3b82f6 !important;
  border-radius: 8px !important;
} */

.customized-spin .ant-spin-dot-item {
  background-color: white !important;
}