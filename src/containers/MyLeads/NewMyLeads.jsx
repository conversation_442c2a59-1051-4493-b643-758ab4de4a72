import {
  HeartOutlined,
  PlusOutlined,
  StarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import './NewMyLeadsStyle.css';
import { useState } from 'react';
import { Button, Input, notification, Tooltip } from 'antd';
import { LEAD_STATUS_TYPE } from '../../services/jobLeadStatuses';
import { createNewLeadSheet } from '../../services/leadSheet';
import CreateNewLeadSheetForm from './CreateNewLeadSheetForm';
import clsx from 'clsx';
import CreateLeadSheetButton from './CreateLeadSheetButton';
import { COMMON_STRINGS } from '../../constants/common.constant';

export const LEAD_STATUS_TYPE_ICON = {
  VACANCY: <HeartOutlined />,
  OPPORTUNITY: <TrophyOutlined />,
  LEAD: <StarOutlined />,
};

const NewMyLeads = ({
  setLeadSheets,
  setType,
  handleManageStatusesClick,
  reloadJobLeads,
  setTypeId
}) => {
  const [typeLeadSheetCreate, setLeadSheetCreate] = useState('');
  const [newLeadStatusName, setNewLeadStatusName] = useState('');
  const [leadStatusLoading, setLeadStatusLoading] = useState(false);
  const [toggleActive, setToggleActive] = useState(false);

  const onNewLeadStatusNameChange = (event) => {
    setNewLeadStatusName(event.target.value);
  };

  const toggle = () => {
    setToggleActive(!toggleActive);
    setLeadSheetCreate('');
  };

  const onChangeLeadSheet = (type) => setLeadSheetCreate(type);

  const addNewLeadStatus = async (e) => {
    if (!newLeadStatusName?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }
    e.preventDefault();
    try {
      const newLeadSheet = {
        name: `${COMMON_STRINGS[typeLeadSheetCreate] ?? typeLeadSheetCreate}: ${newLeadStatusName}`,
        leadStatusType: typeLeadSheetCreate,
      };
      setLeadStatusLoading(true);
      const { data } = await createNewLeadSheet(newLeadSheet);
      setLeadStatusLoading(false);
      if (data?.result) {
        const newItem = { ...data?.result };
        setNewLeadStatusName('');
        setLeadSheets([newItem]);
        setType(newItem.name);
        setTypeId(newItem?.id)
        handleManageStatusesClick();
      }
      notification.success({
        description: 'New Sheet Lead is created!',
      });
    } catch (error) {
      notification.error({
        description: 'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadStatus: ', error);
      setLeadStatusLoading(false);
    }
  };

  return (
    <>
      <div className="Animated-Radial-Menu flex flex-col gap-2">
        <CreateLeadSheetButton
          toggleActive={toggleActive}
          toggle={toggle}
          onChangeLeadSheet={onChangeLeadSheet}
        />
        {typeLeadSheetCreate && (
          <CreateNewLeadSheetForm
            typeLeadSheetCreate={typeLeadSheetCreate}
            onNewLeadStatusNameChange={onNewLeadStatusNameChange}
            leadStatusLoading={leadStatusLoading}
            addNewLeadStatus={addNewLeadStatus}
            newLeadStatusName={newLeadStatusName}
          />
        )}
      </div>
    </>
  );
};

export default NewMyLeads;
