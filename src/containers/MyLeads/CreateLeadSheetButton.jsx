import {
    HeartOutlined,
    PlusOutlined,
    StarOutlined,
    TrophyOutlined,
  } from '@ant-design/icons';
  import './NewMyLeadsStyle.css';
  import {Tooltip } from 'antd';
  import { LEAD_STATUS_TYPE } from '../../services/jobLeadStatuses';
  import clsx from 'clsx';

const CreateLeadSheetButton = ({toggleActive,toggle, onChangeLeadSheet }) =>{
    return <ul className={clsx(
        'menu',
        toggleActive && 'active'
      )}>
        <div className={`toggle`} onClick={toggle}>
          <PlusOutlined />
        </div>
        <li style={{ '--i': '1', '--clr': '#04fc43' }}>
          <Tooltip title="Create new Vacancy Sheet">
            <a onClick={() => onChangeLeadSheet(LEAD_STATUS_TYPE.VACANCY)}>
              <HeartOutlined />
            </a>
          </Tooltip>
        </li>
        <li style={{ '--i': '2', '--clr': '#00b0fe' }}>
          <Tooltip title="Create new Opportunity Sheet">
            <a
              onClick={() => onChangeLeadSheet(LEAD_STATUS_TYPE.OPPORTUNITY)}
            >
              <TrophyOutlined />
            </a>
          </Tooltip>
        </li>
        <li style={{ '--i': '3', '--clr': '#01bdab' }}>
          <Tooltip title="Create new Lead Sheet">
            <a onClick={() => onChangeLeadSheet(LEAD_STATUS_TYPE.LEAD)}>
              <StarOutlined />
            </a>
          </Tooltip>
        </li>
      </ul>
}

export default CreateLeadSheetButton