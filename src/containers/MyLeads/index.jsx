import React, { useEffect, useState } from 'react';

import _map from 'lodash/map';
import _range from 'lodash/range';

import {
  Col,
  Divider,
  Dropdown,
  Input,
  Modal,
  Row,
  Tabs,
  Drawer,
  Button,
  Popconfirm,
  notification,
  Switch,
  Spin,
  Space,
  Tooltip,
  Image,
} from 'antd';
import EditableNumber from '../../components/EditableNumberComponent/EditableNumber';
import LeadStatusCRUD from '../../components/JobsLeads/LeadStatusCRUD';
import LeadsDragDrop from '../../components/JobsLeads/LeadsDragDrop';
import useJobLeads from '../../hooks/useJobLeads';
import { bulkUpdateLeadStatus } from '../../services/jobLeadStatuses';
import { useAuth } from '../../store/auth';

import { useDispatch, useSelector } from 'react-redux';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { saveAllStatusLead, selectAllStatusLead } from '../../store/common';
import Mailbox from '../../components/Mailbox';
import { countLeadMailBox } from '../../services/myLead';
import {
  getUserViewAs,
  getUserViewAsLicenseType,
} from '../../helpers/getUserViewAs';
import {
  ClearOutlined,
  ControlOutlined,
  DeleteOutlined,
  DownOutlined,
  FileSearchOutlined,
  FileTextOutlined,
  GroupOutlined,
  HeartOutlined,
  ImportOutlined,
  InsertRowAboveOutlined,
  InsertRowLeftOutlined,
  MenuFoldOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  StarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import _debounce from 'lodash/debounce';
import MasterRefine from './MasterRefine';
import {
  bulkChangeLeadsStatus,
  bulkDeleteLeads,
} from '../../services/jobLeads';
import LeadsDragDropV2 from '../LeadsDragDropV2';
import NewMyLeads, { LEAD_STATUS_TYPE_ICON } from './NewMyLeads';
import ManageLeadSheets from './ManageLeadSheets';
import {
  createNewLeadSheet,
  deleteLeadSheet,
  editLeadSheet,
  getAllLeadSheets,
  LEAD_SHEET_TYPE,
} from '../../services/leadSheet';
import CreateNewLeadSheetForm from './CreateNewLeadSheetForm';
import { COMMON_STRINGS, licenseType } from '../../constants/common.constant';
import HorizontalView from '../LeadsDragDropV2/HorizontalView';
import clsx from 'clsx';
import ColumnIcon from '../../assets/img/icons/column.png';
import { useNavigate } from 'react-router-dom';

export const TYPE = {
  VACANCIES: 'VACANCIES',
  OPPORTUNITIES: 'OPPORTUNITIES',
  LEADS: 'LEADS',
};

const initialLeadSheet = {
  name: '',
  leadSheetType: LEAD_SHEET_TYPE.VACANCY,
};

export const MODE = {
  VERTICAL: 'VERTICAL',
  HORIZONTAL: 'HORIZONTAL',
};

function MyLeads() {
  const navigate = useNavigate();
  const allStatusLead = useSelector(selectAllStatusLead);
  const { profile } = useAuth();
  const idUserViewAs = getUserViewAs();
  const dispatch = useDispatch();

  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const {
    jobLeads: jobLeadsWithStatuses,
    reloadJobLeads,
    setJobLeads,
    isLoading: isLoadingGetAPI,
  } = useJobLeads();

  // display mode
  const [mode, setMode] = useState(MODE.VERTICAL);

  const [isManageStatusesOpen, setIsManageStatusesOpen] = useState(false);
  const [countLeads, setCountLeads] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [rawSearchText, setRawSearchText] = useState('');
  const [type, setType] = useState('');
  const [typeId, setTypeId] = useState('');
  const handleManageStatusesClick = () => {
    setIsManageStatusesOpen(true);
  };

  // Master refine modal

  const [isMasterRefineOpen, setMasterRefineOpen] = useState(false);
  const [isDisableSearchByCompany, setDisableSearchByCompany] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [searchedLeads, setSearchedLeads] = useState([]);
  const [rawSearchedLeads, setRawSearchedLeads] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [rawSelectedRowKeys, setRawSelectedRowKeys] = useState([]);
  const [rawJobLeadsWithStatuses, setRawJobLeadsWithStatuses] = useState([]);

  const showMasterRefineModal = () => setMasterRefineOpen(true);
  const closeMasterRefineModal = () => setMasterRefineOpen(false);

  const [isSelectionMode, setSelectionMode] = useState(true);

  // selection mode
  // const openSelectionMode = () => setSelectionMode(true);
  // const closeSelectionMode = () => setSelectionMode(false);

  // Lead Sheet
  const [leadSheets, setLeadSheets] = useState([]);
  const [isManageLeadSheetsOpen, setIsManageLeadSheetsOpen] = useState(false);
  const [leadSheetLoading, setLeadSheetLoading] = useState(false);

  const [openLeadSheetForm, setOpenLeadSheetForm] = useState(false);
  const [selectedLeadSheet, setSelectedLeadSheet] = useState({
    ...initialLeadSheet,
  });

  const [leadStatusLoading, setLeadStatusLoading] = useState(false);

  const showLeadSheetForm = () => setOpenLeadSheetForm(true);
  const closeLeadSheetForm = () => setOpenLeadSheetForm(false);

  const [toggleActive, setToggleActive] = useState(false);

  const toggle = () => {
    setToggleActive(!toggleActive);
  };

  const openManageLeadSheet = () => setIsManageLeadSheetsOpen(true);
  const closeManageLeadSheet = () => setIsManageLeadSheetsOpen(false);

  const getAllLeadSheetsFunc = async () => {
    setLeadSheetLoading(true);
    await getAllLeadSheets()
      .then(({ data }) => {
        if (data?.result?.length > 0) {
          setLeadSheets([...data?.result]);
          handleSetType([...data?.result][0]);
        }
      })
      .catch((err) => {
        console.log('Error in getAllLeadSheetsFunc:', err);
      })
      .finally(() => {
        setLeadSheetLoading(false);
      });
  };

  useEffect(() => {
    getAllLeadSheetsFunc();
  }, []);

  const handleCloseModal = () => {
    setIsManageStatusesOpen(false);
    reloadJobLeads(typeId);
  };
  const updateLeadStatus = async (statuses) => {
    await bulkUpdateLeadStatus(statuses);
    handleCloseModal();
  };

  useEffect(() => {
    if (jobLeadsWithStatuses) {
      dispatch(saveAllStatusLead(jobLeadsWithStatuses));
    }
  }, [jobLeadsWithStatuses]);

  useEffect(() => {
    handleEffectData();
  }, []);

  const handleEffectData = async () => {
    const response = await countLeadMailBox(idUserViewAs);
    setCountLeads(response?.data.result);
  };

  const handleUpdateCountLeadAfterChangeStatusToDone = () => {
    setCountLeads(countLeads - 1);
  };

  const debouncedSetSearchText = _debounce((value) => {
    setSearchText(value);
  }, 500);

  const onChangeTab = (key) => {
    console.log(key);
  };

  const onSubmitSearchedLeads = (result, type = 'submit') => {
    setSearchText('');
    setRawSearchText('');
    console.log('result: ', result);
    console.log('allStatusLead: ', allStatusLead);
    setRawSearchedLeads(result);
    const searchedCompanies = [
      ...new Set([...result?.map((item) => item?.company_name)]),
    ];

    const searchedLeadIdList = [...result]?.map((item) => item?.id);
    setSelectedRowKeys([...searchedLeadIdList]);
    setRawSelectedRowKeys([...searchedLeadIdList]);
    const key = 'companyIdRaw';
    const companiesList = [
      ...new Map(
        rawJobLeadsWithStatuses
          .flatMap((status) => status.companies)
          .map((item) => [item[key], item])
      ).values(),
    ];

    const newJobLeadsWithStatuses = rawJobLeadsWithStatuses.map((item) => {
      if (!item?.companies) return { ...item };

      // const newCompaniesList = item?.companies?.filter((company) =>
      //   searchedCompanies.includes(company?.jl_company_name)
      // );

      const newCompaniesListWithSearchedLeads = companiesList
        ?.map((company) => {
          if (!company?.leads) return { ...company };

          const mewLeadsBasedOnCompany = [];

          result.forEach((lead) => {
            if (
              lead?.company_id === company?.companyIdRaw &&
              lead?.lead_status_id === item?.id
            ) {
              mewLeadsBasedOnCompany.push(lead);
            }
          });

          // const mewLeadsBasedOnCompany = company?.leads?.filter((lead) => {
          //   searchedLeadIdListWithStatusId.forEach((leadWithStatus) => {
          //     if (
          //       leadWithStatus?.id === lead?.id &&
          //       leadWithStatus?.statusId === item?.id
          //     ) {
          //       return true;
          //     }
          //     else{
          //       return false;
          //     }
          //   });
          // });

          return { ...company, leads: mewLeadsBasedOnCompany };
        })
        .filter((company) => company?.leads?.length > 0);

      // if (searchText) {
      //   newCompaniesListWithSearchedLeads =
      //     newCompaniesListWithSearchedLeads.filter((company) =>
      //       company?.jl_company_name
      //         ?.trim()
      //         .toLowerCase()
      //         .includes(searchText?.trim().toLowerCase())
      //     );
      // }

      return { ...item, companies: newCompaniesListWithSearchedLeads };
    });

    setSearchedLeads(newJobLeadsWithStatuses);
    console.log('onSubmitSearchedLeads: ', result);
    console.log('newJobLeadsWithStatuses: ', newJobLeadsWithStatuses);
  };

  const clearMasterRefineSearch = () => {
    setSearchText('');
    setRawSearchText('');
    setSearchedLeads([]);
    setRawSearchedLeads([]);
    reloadJobLeads(typeId);
    // setSelectionMode(false);
  };

  const confirmBulkDelete = () =>
    new Promise((resolve, reject) => {
      bulkDeleteLeads({ ids: selectedRowKeys })
        .then((res) => {
          console.log('selectedRowKeys: ', selectedRowKeys);
          const newDataSource = rawSearchedLeads.filter(
            (item) => !selectedRowKeys.includes(item?.id)
          );
          notification.success({
            description: 'Deleted!',
          });
          onSubmitSearchedLeads(newDataSource);
          resolve(res);
        })
        .catch((err) => {
          console.log('err: ', err);
          notification.error({
            description: 'Please try again later!',
          });
          reject(err);
        });
    });

  const actionList = [
    {
      key: 'delete-all',
      label: (
        <div>
          <Popconfirm
            title="Delete Confirmation"
            description="Are you sure to delete all selected leads?"
            onConfirm={confirmBulkDelete}
            okText="Yes"
            cancelText="No"
          >
            <div className="w-full">Bulk Delete</div>
          </Popconfirm>
        </div>
      ),
    },
    {
      key: 'update-status-all',
      label: 'Bulk change status',
      children: allStatusLead?.map((status) => ({
        key: status?.id,
        label: status?.name,
      })),
    },
  ];

  const onBulkActionMenuClick = async (e) => {
    if (e.key === 'delete-all') return;
    const statusId = e.key;
    const payload = {
      ids: selectedRowKeys,
      leadStatusId: statusId,
    };
    setActionLoading(true);
    bulkChangeLeadsStatus(payload)
      .then((res) => {
        const newDataSource = rawSearchedLeads?.map((item) => {
          if (selectedRowKeys.includes(item?.id)) {
            return { ...item, lead_status_id: statusId };
          }
          return { ...item };
        });
        onSubmitSearchedLeads([...newDataSource]);
        notification.success({
          description: 'Updated.',
        });
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          description: 'Please try again later!',
        });
      })
      .finally(() => {
        setActionLoading(false);
      });
  };

  // const onChangeSelectionMode = () =>
  //   isSelectionMode ? closeSelectionMode() : openSelectionMode();

  const onChangeCheckbox = (e) => {
    const checkBoxValue = e.target.value.split('/');
    const field = checkBoxValue[0];
    const id = checkBoxValue[1];
    const checked = e.target.checked;
    console.log('rawSearchedLeads: ', rawSearchedLeads);
    if (checked) {
      const addedLeads = rawSearchedLeads
        .filter((item) => item[field] === id)
        .map((item) => item?.id);
      const newRowSelectedKeys = [...selectedRowKeys, ...addedLeads];
      setSelectedRowKeys([...newRowSelectedKeys]);
    } else {
      const removedLeads = rawSearchedLeads
        .filter((item) => item[field] === id)
        .map((item) => item?.id);

      console.log('removedLeads: ', removedLeads);
      console.log('selectedRowKeys: ', selectedRowKeys);
      const newRowSelectedKeys = selectedRowKeys.filter(
        (leadId) => !removedLeads.includes(leadId)
      );

      setSelectedRowKeys([...newRowSelectedKeys]);
    }
  };

  const handleSetType = (inputType) => {
    const { name, id } = inputType;
    if (!id || id === typeId) return;
    setType(name);
    setTypeId(id);
  };

  const addNewLeadStatus = async () => {
    if (!selectedLeadSheet?.name?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }

    try {
      const newLeadSheet = {
        name: `${COMMON_STRINGS[selectedLeadSheet.leadSheetType] || selectedLeadSheet.leadSheetType}: ${selectedLeadSheet?.name}`,
        leadStatusType: selectedLeadSheet?.leadSheetType,
      };
      setLeadStatusLoading(true);
      const { data } = await createNewLeadSheet(newLeadSheet);
      setLeadStatusLoading(false);
      if (data?.result) {
        const newItem = { ...data?.result };
        setSelectedLeadSheet({ ...initialLeadSheet });
        setLeadSheets([...leadSheets, newItem]);
        setTypeId(newItem?.id);
        handleManageStatusesClick();
        notification.success({
          description: 'Lead Sheet created!',
        });
        closeLeadSheetForm();
      } else {
        notification.error({
          description: data?.message,
        });
      }
    } catch (error) {
      notification.error({
        description:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadStatus: ', error);
      setLeadStatusLoading(false);
    }
  };

  const updateLeadSheet = async () => {
    if (!selectedLeadSheet?.name?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }

    try {
      const updatedLeadSheet = {
        id: selectedLeadSheet?.id,
        name: `${selectedLeadSheet?.leadSheetType}: ${selectedLeadSheet?.name}`,
        leadStatusType: selectedLeadSheet?.leadSheetType,
      };
      setLeadStatusLoading(true);
      const { data } = await editLeadSheet(updatedLeadSheet);
      setLeadStatusLoading(false);
      if (data?.result) {
        const updatedItem = { ...data?.result };
        let currLeadSheets = [...leadSheets];
        const updatedIndex = currLeadSheets.findIndex(
          (leadSheet) => leadSheet?.id === updatedItem?.id
        );
        currLeadSheets[updatedIndex] = { ...updatedItem };
        setLeadSheets([...currLeadSheets]);

        notification.success({
          description: 'Lead Sheet updated!',
        });
        closeLeadSheetForm();
      } else {
        notification.error({
          description: data?.message,
        });
      }
    } catch (error) {
      notification.error({
        description:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadStatus: ', error);
      setLeadStatusLoading(false);
    }
  };

  const handleUpdateLeadStatus = () => {
    console.log('selectedLeadSheet: ', selectedLeadSheet);

    const isNew = !selectedLeadSheet?.id;

    if (isNew) {
      addNewLeadStatus();
    } else {
      updateLeadSheet();
    }
  };

  const handleDeleteLeadSheet = async () => {
    try {
      setLeadSheetLoading(true);
      const res = await deleteLeadSheet(selectedLeadSheet?.id);
      const currLeadSheets = [...leadSheets];
      const newLeadSheets = currLeadSheets.filter(
        (leadSheet) => leadSheet?.id !== selectedLeadSheet?.id
      );
      setLeadSheets([...newLeadSheets]);
      setLeadSheetLoading(false);
      if (newLeadSheets?.length > 0) {
        setType(newLeadSheets[0]?.name);
        setTypeId(newLeadSheets[0]?.id);
      }

      closeLeadSheetForm();
      notification.success({
        description: 'Lead Sheet deleted!',
      });
    } catch (error) {
      setLeadSheetLoading(false);
      notification.error({
        description: 'Something went wrong! Try again later.',
      });
    }
  };

  useEffect(() => {
    if (!typeId) return;
    reloadJobLeads(typeId);
  }, [typeId]);

  const handleImportLeadsClick = ({ key }) => {
    if (key === 'manual-lead') {
      navigate('/my-leads/manual');
    } else if (key === 'import-leads') {
      navigate('/my-leads/import');
    }
  };

  return (
    <div className="relative h-[95%]">
      <Modal
        title={'Manage Lead Statuses'}
        open={isManageStatusesOpen}
        onCancel={handleCloseModal}
        footer={null}
        destroyOnClose={true}
      >
        <LeadStatusCRUD onFinish={handleCloseModal} typeId={typeId} />
      </Modal>

      {/* Lead Sheet Form */}
      <Modal
        // title={'Manage Lead Sheets'}
        open={openLeadSheetForm}
        onCancel={closeLeadSheetForm}
        closable={false}
        footer={
          <div className="flex gap-2 items-center w-full justify-end">
            {selectedLeadSheet?.id && (
              <Button
                onClick={handleDeleteLeadSheet}
                type="dashed"
                danger
                icon={<DeleteOutlined />}
                loading={leadSheetLoading}
              >
                Delete
              </Button>
            )}
            <Button onClick={closeLeadSheetForm}>Close</Button>
          </div>
        }
        destroyOnClose={true}
        width={700}
      >
        <div className="py-2">
          <CreateNewLeadSheetForm
            typeLeadSheetCreate={selectedLeadSheet.leadSheetType}
            onNewLeadStatusNameChange={(event) => {
              setSelectedLeadSheet({
                ...selectedLeadSheet,
                name: event.target.value,
              });
            }}
            leadStatusLoading={leadStatusLoading}
            addNewLeadStatus={handleUpdateLeadStatus}
            newLeadStatusName={selectedLeadSheet.name}
            isEdit={selectedLeadSheet?.id}
          />
        </div>
      </Modal>

      {/* Manage Lead Sheets modal */}

      <Modal
        title={'Manage Lead Sheets'}
        open={isManageLeadSheetsOpen}
        onCancel={closeManageLeadSheet}
        footer={null}
        destroyOnClose={true}
      >
        <ManageLeadSheets />
      </Modal>

      {/* Master Refine modal */}

      {!isLoadingGetAPI && (
        <Drawer
          open={isMasterRefineOpen}
          onClose={closeMasterRefineModal}
          width={'50rem'}
        >
          <Tabs
            onChange={onChangeTab}
            type="card"
            items={[
              {
                label: `Search`,
                key: `search-tab`,
                children: (
                  <MasterRefine
                    onSubmitSearchedLeads={onSubmitSearchedLeads}
                    closeMasterRefineModal={closeMasterRefineModal}
                  />
                ),
              },
            ]}
          />
        </Drawer>
      )}
      {leadSheetLoading && (
        <div className="flex h-full w-full items-center justify-center">
          <Spin size="large" />
        </div>
      )}
      {!leadSheetLoading &&
        (leadSheets?.length > 0 ? (
          <div className="h-full">
            <div className="w-full grid grid-cols-12 gap-2">
              {/* <Button
                disabled={leadSheetLoading}
                onClick={openManageLeadSheet}
                icon={<SettingOutlined />}
              ></Button> */}

              <Dropdown
                placement="bottomLeft"
                arrow
                menu={{
                  items: [
                    {
                      key: 'vacancy',
                      label: <span className="font-medium">Vacancy</span>,
                      icon: <HeartOutlined />,
                      onClick: () => {
                        setSelectedLeadSheet({
                          name: '',
                          leadSheetType: LEAD_SHEET_TYPE.VACANCY,
                        });
                        showLeadSheetForm();
                      },
                    },
                    {
                      key: 'opportunity',
                      label: <span className="font-medium">Opportunity</span>,
                      icon: <TrophyOutlined />,
                      onClick: () => {
                        setSelectedLeadSheet({
                          name: '',
                          leadSheetType: LEAD_SHEET_TYPE.OPPORTUNITY,
                        });

                        showLeadSheetForm();
                      },
                    },
                    {
                      key: 'lead',
                      label: <span className="font-medium">Lead</span>,
                      icon: <StarOutlined />,
                      onClick: () => {
                        setSelectedLeadSheet({
                          name: '',
                          leadSheetType: LEAD_SHEET_TYPE.LEAD,
                        });

                        showLeadSheetForm();
                      },
                    },
                  ],
                }}
              >
                <Button
                  type="primary"
                  className=" !border-[#b2b8be] flex gap-2 items-center"
                  icon={<PlusOutlined />}
                >
                  {COMMON_STRINGS.CREATE}
                </Button>
              </Dropdown>

              <div className="mb-3 pb-2 flex justify-center items-center col-span-11 overflow-x-auto">
                <div className="flex items-center gap-2 max-w-full">
                  {leadSheets?.map((leadSheet) => (
                    <Tooltip title="Double click to edit">
                      <Button
                        className={clsx(
                          'line-clamp-1 min-w-fit',
                          typeId === leadSheet?.id && '!bg-cyan-100'
                        )}
                        type="primary"
                        onClick={() => handleSetType(leadSheet)}
                        onDoubleClick={() => {
                          setSelectedLeadSheet({
                            ...leadSheet,
                            leadSheetType: leadSheet?.leadStatusType,
                            name:
                              leadSheet?.name?.split(':')[1]?.trim() ||
                              leadSheet?.name,
                          });
                          showLeadSheetForm();
                        }}
                        icon={LEAD_STATUS_TYPE_ICON[leadSheet?.leadStatusType]}
                      >
                        {leadSheet?.name}
                      </Button>
                    </Tooltip>
                  ))}
                </div>
              </div>
            </div>
            <Row gutter={[32, 32]} className="h-full overflow-y-auto ">
              <Col span={24}>
                <div className="flex gap-4 items-center py-4">
                  <Input
                    title={
                      isDisableSearchByCompany
                        ? 'Please clear all filter to enable search'
                        : 'Search By Company'
                    }
                    disabled={isDisableSearchByCompany}
                    placeholder="Search By Company"
                    className="max-w-xs"
                    prefix={<SearchOutlined />}
                    // value={searchText}
                    onChange={(e) => {
                      debouncedSetSearchText(e.target.value);
                    }}
                  />
                  <Button
                    type="primary"
                    disabled={isLoadingGetAPI}
                    onClick={showMasterRefineModal}
                    icon={<FileSearchOutlined />}
                  >
                    Master Refine
                  </Button>

                  {searchedLeads.length > 0 && (
                    <div className="flex gap-2 justify-center items-center">
                      <Button
                        title="Clear both master refine and filter"
                        danger
                        type="dashed"
                        disabled={isLoadingGetAPI}
                        onClick={clearMasterRefineSearch}
                        icon={<ClearOutlined />}
                      >
                        Clear all
                      </Button>
                      <Dropdown.Button
                        loading={actionLoading}
                        disabled={isLoadingGetAPI}
                        className="w-auto"
                        menu={{
                          items: actionList,
                          onClick: onBulkActionMenuClick,
                        }}
                      >
                        {`${selectedRowKeys.length} Selected`}
                      </Dropdown.Button>
                      {/* <Switch
                        checkedChildren="Selection"
                        unCheckedChildren="Non-selection"
                        defaultValue={isSelectionMode}
                        onChange={onChangeSelectionMode}
                      /> */}
                    </div>
                  )}
                  <EditableNumber className="ml-auto" />
                  <Divider type="vertical" className="h-4 mx-0" />
                  <Button
                    disabled={isLoadingGetAPI}
                    type="primary"
                    onClick={handleManageStatusesClick}
                    icon={<ControlOutlined />}
                  >
                    Manage Statuses
                  </Button>

                  <Divider type="vertical" className="h-4 mx-0" />
                  <Tooltip title="Click to change view">
                    <Image
                      onClick={() => {
                        const modeTemp =
                          mode === MODE.VERTICAL
                            ? MODE.HORIZONTAL
                            : MODE.VERTICAL;
                        setMode(modeTemp);
                      }}
                      preview={false}
                      width={30}
                      src={ColumnIcon}
                      className={clsx(
                        'cursor-pointer hover:shadow-sm transition-all transform flex items-center justify-center',
                        mode === MODE.HORIZONTAL && 'rotate-90'
                      )}
                    />
                  </Tooltip>

                  <Divider type="vertical" className="h-4 mx-0" />
                  <Dropdown
                    arrow
                    placement="bottomLeft"
                    trigger={['click']}
                    menu={{
                      items: [
                        {
                          key: 'manual-lead',
                          label: (
                            <span className="text-sm font-medium">
                              Manual Lead
                            </span>
                          ),
                          icon: <FileTextOutlined />,
                        },
                        ...(isStandardUser
                          ? []
                          : [
                              {
                                key: 'import-leads',
                                label: (
                                  <span className="text-sm font-medium">
                                    Import Lead
                                  </span>
                                ),
                                icon: <ImportOutlined />,
                              },
                            ]),
                      ],
                      onClick: handleImportLeadsClick,
                    }}
                  >
                    <Tooltip title="Add new lead">
                      <Button type="primary" icon={<PlusCircleOutlined />} />
                    </Tooltip>
                  </Dropdown>
                </div>
                {!isLoadingGetAPI && jobLeadsWithStatuses && (
                  <Col span={24} className="h-full">
                    <LeadsDragDropV2
                      mode={mode}
                      updateLeadStatus={updateLeadStatus}
                      jobLeadsWithStatuses={jobLeadsWithStatuses}
                      reloadJobLeads={() => {
                        console.log('typeId: ', typeId);
                        reloadJobLeads(typeId);
                      }}
                      // handleDeleteLead={handleDeleteLead}
                      setJobLeadsGlobal={setRawJobLeadsWithStatuses}
                      rawJobLeadsWithStatuses={rawJobLeadsWithStatuses}
                      searchText={searchText}
                      searchedLeads={searchedLeads}
                      rawSearchedLeads={rawSearchedLeads}
                      isSelectionMode={
                        isSelectionMode && searchedLeads.length > 0
                      }
                      selectedRowKeys={selectedRowKeys}
                      setSelectedRowKeys={setSelectedRowKeys}
                      onChangeCheckbox={onChangeCheckbox}
                      rawSelectedRowKeys={rawSelectedRowKeys}
                      setDisableSearchByCompany={setDisableSearchByCompany}
                      leadSheetId={typeId}
                      handleManageStatusesClick={handleManageStatusesClick}
                    />
                  </Col>
                )}
              </Col>
              {isLoadingGetAPI && (
                <>
                  {_map(_range(4), (_, idx) => (
                    <React.Fragment key={idx.toString()}>
                      <Col span={6}>
                        <LoadingAdvanced isSkeleton />
                      </Col>
                    </React.Fragment>
                  ))}
                </>
              )}
            </Row>
          </div>
        ) : (
          <NewMyLeads
            setLeadSheets={setLeadSheets}
            setType={setType}
            handleManageStatusesClick={handleManageStatusesClick}
            setTypeId={setTypeId}
          />
        ))}
    </div>
  );
}

export default MyLeads;
