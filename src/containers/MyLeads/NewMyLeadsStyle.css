.Animated-Radial-Menu {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
}
.Animated-Radial-Menu .menu {
  position: relative;
  width: 280px;
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.Animated-Radial-Menu .menu .toggle {
  position: absolute;
  width: 60px;
  height: 60px;
  background: #2f363e;
  border: 2px solid #fff;
  border-radius: 50%;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 100;
  font-size: 2em;
  transition: transform 0.5s;
}
.Animated-Radial-Menu .menu li {
  position: absolute;
  left: 0;
  list-style: none;
  transition: 0.3s;
  transition-delay: calc(0.04s * var(--i));
  transform: rotate(0deg) translateX(110px);
  transform-origin: 140px;
}
.Animated-Radial-Menu .menu li a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  color: var(--clr);
  border: 2px solid var(--clr);
  border-radius: 50%;
  font-size: 1.5em;
  transform: rotate(calc(-45deg * var(--i)));
  transition: 0.3s;
}
.Animated-Radial-Menu .menu li a:hover {
  background: var(--clr);
  color: #333;
  box-shadow: 0 0 10px var(--clr), 0 0 30px var(--clr), 0 0 50px var(--clr);
}
.Animated-Radial-Menu .menu.active .toggle {
  transform: rotate(315deg);
}
.Animated-Radial-Menu .menu.active li {
  transform: rotate(calc(45deg * var(--i))) translateX(0px);
}/*# sourceMappingURL=Animated-Radial-Menu.css.map */


