import {
  Avatar,
  Button,
  Input,
  notification,
  Popconfirm,
  Select,
  Tag,
  Typography,
} from 'antd';
import { LEAD_STATUS_TYPE_ICON } from './NewMyLeads';
import { CloseOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { COMMON_STRINGS } from '../../constants/common.constant';
import { useState } from 'react';
import _ from 'lodash';
import { getColorHexFromName } from '../../function/getRandomColor';
import { stringAvatar } from '../../function/stringAvatar';
import { useSelector } from 'react-redux';
import { selectAllUsers } from '../../store/common';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { SelectContainer } from '../../components/JobDetail';
import { editLeadSheet } from '../../services/leadSheet';

const CreateNewLeadSheetForm = ({
  typeLeadSheetCreate,
  onNewLeadStatusNameChange,
  leadStatusLoading,
  addNewLeadStatus,
  newLeadStatusName,
  isEdit = null,
}) => {
  const leadSheetId = isEdit;
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;
  const userId = userToSet?.user?.id || userToSet?.id;

  const [isChangeOwner, setIsChangeOwner] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newOwner, setNewOwner] = useState(null);

  const showChangeOwner = () => setIsChangeOwner(true);
  const hideChangeOwner = () => setIsChangeOwner(false);

  const allUsers = useSelector(selectAllUsers);

  const currentUser =
    _.find(allUsers, { id: userId })?.user || _.find(allUsers, { id: userId });

  const standardType =
    COMMON_STRINGS[typeLeadSheetCreate] ?? typeLeadSheetCreate;

  const onSubmitChangeOwner = async () => {
    setLoading(true);
    try {
      const { data } = await editLeadSheet({
        name: newLeadStatusName,
        id: leadSheetId,
        userId: newOwner.id,
      });
      window.location.reload();
    } catch (error) {
      setLoading(false);
      notification.error({
        description: 'Failed to change owner',
      });
    }
  };

  return (
    <div className="border rounded-md bg-white p-4 flex flex-col gap-3 min-w-[30rem] shadow-md">
      <div className="flex gap-2 items-center pb-2 border-b">
        <span className="font-medium text-base">
          {isEdit ? 'Update' : 'Create new'}
        </span>
        <Button
          type="primary"
          icon={LEAD_STATUS_TYPE_ICON[typeLeadSheetCreate]}
        >
          {standardType}
        </Button>
      </div>
      <div className="grid grid-cols-10 gap-4 py-2 px-3">
        <Input
          placeholder="Please enter sheet name"
          onChange={onNewLeadStatusNameChange}
          onKeyDown={(e) => e.stopPropagation()}
          className="col-span-7 font-medium"
          addonBefore={`${standardType} :`}
          value={newLeadStatusName}
          maxLength={100}
          showCount
        ></Input>
        <Button
          loading={leadStatusLoading}
          className="col-span-3 font-medium"
          type="default"
          icon={<PlusOutlined />}
          onClick={addNewLeadStatus}
        >
          Save
        </Button>
      </div>
      {isEdit &&
        (!isChangeOwner ? (
          <div className="px-3 flex items-center justify-start gap-2">
            <span className="text-base">
              Owner by <strong>you</strong>
            </span>
            <span
              onClick={showChangeOwner}
              className="text-xs hover:underline font-medium cursor-pointer italic text-cyan-600"
            >
              Change owner
            </span>
          </div>
        ) : (
          <div className="grid grid-cols-10 gap-4 py-2 px-3">
            <SelectContainer className="col-span-7">
              <Select
                defaultValue={
                  _.get(currentUser, 'username') || _.get(currentUser, 'email')
                }
                className="w-full flex items-center"
                showSearch
                options={_.map(allUsers, (option) => ({
                  value: _.get(option, 'username') || _.get(option, 'email'),
                  key: _.get(option, 'id'),
                  id: _.get(option, 'id'),
                  label: (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Avatar
                        style={{
                          backgroundColor: getColorHexFromName(
                            _.get(option, 'username') || _.get(option, 'email')
                          ),
                          verticalAlign: 'middle',
                        }}
                      >
                        {stringAvatar(
                          _.get(option, 'username') || _.get(option, 'email')
                        )}
                      </Avatar>
                      &ensp;
                      <Typography.Text
                        style={{
                          width: '150px',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {_.get(option, 'username') || _.get(option, 'email')}
                      </Typography.Text>
                    </div>
                  ),
                }))}
                placeholder="None"
                onSelect={async (value, option) => {
                  setNewOwner(option);
                }}
              />
            </SelectContainer>
            <Popconfirm
              disabled={!newOwner || userId === newOwner.id}
              title={
                <div>
                  <Tag color="warning">Warning!!!</Tag>
                </div>
              }
              description={
                <div className="flex flex-col">
                  <div>After changing the owner,</div>
                  <div>you will no longer be able to access this sheet.</div>
                </div>
              }
              onConfirm={onSubmitChangeOwner}
              okText="Yes"
              cancelText="Cancel"
            >
              <Button
                disabled={!newOwner || userId === newOwner.id}
                className="col-span-2 font-medium"
                type="primary"
                icon={<SaveOutlined />}
              >
                Save
              </Button>
            </Popconfirm>

            <Button icon={<CloseOutlined />} onClick={hideChangeOwner}></Button>
          </div>
        ))}
    </div>
  );
};
export default CreateNewLeadSheetForm;
