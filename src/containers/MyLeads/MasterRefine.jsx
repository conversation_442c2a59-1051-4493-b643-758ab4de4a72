import React, { useEffect, useState } from 'react';
import {
  Button,
  Switch,
  Form,
  Input,
  Space,
  Select,
  InputNumber,
  DatePicker,
  Modal,
  Table,
  Card,
  Dropdown,
  notification,
} from 'antd';
import {
  MinusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { selectAllStatusLead } from '../../store/common';
import {
  masterRefineLeads,
  bulkDeleteLeads,
  bulkChangeLeadsStatus,
} from '../../services/jobLeads';
import dayjs from 'dayjs';
import _ from 'lodash';

const { RangePicker } = DatePicker;

const MasterRefine = ({ onSubmitSearchedLeads, closeMasterRefineModal }) => {
  const allStatusLead = useSelector(selectAllStatusLead);

  const [form] = Form.useForm();
  const additionalCriteria = Form.useWatch('additionalCriteria', form);
  const [additionalCriteriaWatch, setAdditionalCriteriaWatch] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);

  //   searched leads modal
  const [isSearchedLeadsModalOpen, setSearchedLeadsModalOpen] = useState(false);
  const showSearchedLeadsModal = () => setSearchedLeadsModalOpen(true);
  const closeSearchedLeadsModal = () => {
    setSearchedLeadsModalOpen(false);
    setSelectedRowKeys([]);
  };

  //    table action state

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);

  const onFinish = async (values) => {
    const payload = { search: values?.search?.optional };

    if (values?.search?.required) {
      payload['required'] = values?.search?.required;
    }
    if (values?.search?.excluded) {
      payload['excluded'] = values?.search?.excluded;
    }
    // if (values?.minSalary) {
    //   payload['minSalary'] = values?.minSalary;
    // }
    // if (values?.maxSalary) {
    //   payload['maxSalary'] = values?.maxSalary;
    // }
    console.log('Received values of payload:', values);
    if (values?.additionalCriteria && values?.additionalCriteria?.length > 0) {
      for await (const criteria of values?.additionalCriteria) {
        let value = criteria?.value;
        if (!value) return;
        if (criteria['field'] === 'datePosted') {
          value = dayjs(criteria?.value).format('YYYY-MM-DD');
        }
        if (criteria['field'] === 'datePostedRange') {
          value = value.map((pickedDate) =>
            dayjs(pickedDate).format('YYYY-MM-DD')
          );
        }
        payload[criteria?.operator] = value;
      }
    }
    setLoading(true);
    getMasterRefineLeads(payload)
      .then((searchedLeads) => {
        if (searchedLeads && searchedLeads?.length > 0) {
          // const handledDataSource = searchedLeads.map((item) => ({
          //   ...item,
          //   key: item?.id,
          // }));
          // setDataSource(handledDataSource);
          const handledDataSource = searchedLeads.map((item) => ({
            ...item,
            key: item?.id,
          }));
          onSubmitSearchedLeads(handledDataSource);
          closeMasterRefineModal();
          notification.success({
            description: `There are ${handledDataSource?.length} leads found!`,
          });
          // showSearchedLeadsModal();
        } else {
          notification.warning({
            description: `No lead found!`,
          });
        }
      })
      .catch((err) => {
        console.log('Received values of err:', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleChangeFieldName = (key) => (value) => {
    // setValue(name, value);
    const newAdditionalCriteriaList = additionalCriteriaWatch.map(
      (criteria) => {
        if (criteria?.key === key) {
          return { ...criteria, value };
        } else {
          return { ...criteria };
        }
      }
    );
    setAdditionalCriteriaWatch(newAdditionalCriteriaList);

    console.log('newAdditionalCriteriaList:', newAdditionalCriteriaList);
  };

  const getMasterRefineLeads = async (payload) => {
    const res = await masterRefineLeads(payload);
    const searchedLeads = res?.data?.result?.items || [];
    return searchedLeads;
  };

  useEffect(() => {
    setAdditionalCriteriaWatch(additionalCriteria);
  }, [additionalCriteria]);

  const fieldItems = [
    {
      value: 'salary',
      label: 'Salary',
    },
    // {
    //   value: 'datePosted',
    //   label: 'Date Posted',
    // },
    {
      value: 'datePostedRange',
      label: 'Date Posted',
    },
    // Add new fields as needed
    {
      value: 'source',
      label: 'Source',
    },
    {
      value: 'jobType',
      label: 'Job Type',
    },
  ];

  const numberOperators = [
    {
      value: 'minSalary',
      label: 'Greater Than',
    },
    {
      value: 'maxSalary',
      label: 'Less Than',
    },
    {
      value: 'salary',
      label: 'Equals',
    },
  ];

  const dateOperators = [
    {
      value: 'datePostedTo',
      label: 'Before',
    },
    {
      value: 'datePostedFrom',
      label: 'After',
    },
    {
      value: 'datePosted',
      label: 'On',
    },
  ];

  const dateRangeOperators = [
    {
      value: 'inRange',
      label: 'In Range',
    },
    {
      value: 'outRange',
      label: 'Out Range',
    },
  ];

  const textOperators = [
    {
      value: 'includeAny',
      label: 'Include Any',
    },
    {
      value: 'Exclude',
      label: 'Exclude',
    },
  ];

  const sourceOperators = [
    {
      value: 'source',
      label: 'Equals',
    },
  ];

  const jobTypeOperators = [
    {
      value: 'jobType',
      label: 'Equals',
    },
  ];

  const checkOperators = (index) => {
    if (!additionalCriteriaWatch || additionalCriteriaWatch?.length <= 0)
      return;
    const field = additionalCriteriaWatch?.find(
      (_criteria, criteriaIndex) => criteriaIndex === index
    )?.field;
    if (!field) return;
    switch (field) {
      case 'salary':
        return numberOperators;
      case 'datePosted':
        return dateOperators;
      case 'datePostedRange':
        return dateRangeOperators;
      case 'source':
        return sourceOperators;
      case 'jobType':
        return jobTypeOperators;
      default:
        return textOperators;
    }
  };

  const checkValueInput = (index) => {
    if (!additionalCriteriaWatch || additionalCriteriaWatch?.length <= 0)
      return (
        <Input
          style={{
            width: 230,
          }}
        />
      );
    const field = additionalCriteriaWatch?.find(
      (_criteria, criteriaIndex) => criteriaIndex === index
    )?.field;

    switch (field) {
      case 'salary':
        return (
          <InputNumber
            style={{
              width: 230,
            }}
          />
        );
      case 'datePosted':
        return (
          <DatePicker
            format={'DD-MM-YYYY'}
            style={{
              width: 230,
            }}
          />
        );
      case 'datePostedRange':
        return (
          <RangePicker
            format={'DD-MM-YYYY'}
            style={{
              width: 230,
            }}
          />
        );
      case 'jobType':
        return (
          <Select
            showSearch
            options={[
              { value: 'permanent', label: 'Permanent' },
              { value: 'full time', label: 'Full Time' },
              { value: 'part time', label: 'Part Time' },
              { value: 'contract', label: 'Contract' },
              { value: 'internship', label: 'Internship' },
              { value: 'temporary', label: 'Temporary' },
              { value: 'freelance', label: 'Freelance' },
            ]}
            style={{
              width: 230,
            }}
            placeholder="Select Job Type"
          />
        );
      default:
        return (
          <Input
            style={{
              width: 230,
            }}
          />
        );
    }
  };

  const singleSearch = () => (
    <>
      <Form.Item
        name={['search', 'required']}
        // rules={[
        //   {
        //     required: true,
        //   },
        // ]}
      >
        <Input.TextArea
          // showCount
          maxLength={100}
          placeholder="Fill the optional keywords"
        />
      </Form.Item>
    </>
  );

  const multiSearch = () => (
    <div className="flex flex-col gap-2">
      <Form.Item
        name={['search', 'required']}
        // label="required"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <Input
          addonBefore="Required"
          placeholder="Fill the required keywords"
        />
      </Form.Item>
      <Form.Item
        name={['search', 'optional']}
        // label="optional"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <Input
          addonBefore="Optional"
          placeholder="Fill the optional keywords"
        />
      </Form.Item>
      <Form.Item
        name={['search', 'excluded']}
        // label="excluded"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <Input
          addonBefore="Excluded"
          placeholder="Fill the excluded keywords"
        />
      </Form.Item>
    </div>
  );

  const columns = [
    {
      title: 'Lead Name',
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => a.title.localeCompare(b.title),
      sortDirections: ['descend', 'ascend'],
    },
    {
      title: 'Company',
      dataIndex: 'company_contact_name',
      key: 'company_contact_name',
      sorter: (a, b) =>
        a?.company_contact_name?.localeCompare(b?.company_contact_name),
      sortDirections: ['descend', 'ascend'],
    },
    {
      title: 'Salary',
      dataIndex: 'salary',
      key: 'salary',
      sorter: (a, b) => parseInt(a?.salary) || 0 > parseInt(b?.salary) || 0,
      sortDirections: ['descend', 'ascend'],
    },
    {
      title: 'Employment Type',
      dataIndex: 'employment_type',
      key: 'employment_type',
      sorter: (a, b) => a?.employment_type?.localeCompare(b?.employment_type),
      sortDirections: ['descend', 'ascend'],
    },
    // {
    //   title: 'Employment Type',
    //   dataIndex: 'employment_type',
    //   key: 'employment_type',
    // },
    {
      title: 'Status',
      dataIndex: 'lead_status_id',
      key: 'lead_status_id',
      render: (text, record) => {
        const leadStatusName = allStatusLead?.find(
          (status) => status?.id === record?.lead_status_id
        )?.name;
        return (
          <>
            <span>{leadStatusName || ''}</span>
          </>
        );
      },
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const hasSelected = selectedRowKeys.length > 0;

  const onBulkStatusMenuClick = async (e) => {
    const statusId = e.key;

    const payload = {
      ids: selectedRowKeys,
      leadStatusId: statusId,
    };
    setTableLoading(true);
    bulkChangeLeadsStatus(payload)
      .then((res) => {
        const newDataSource = dataSource?.map((item) => {
          if (selectedRowKeys.includes(item?.key)) {
            return { ...item, lead_status_id: statusId };
          }
          return { ...item };
        });
        setDataSource(newDataSource);
        notification.success({
          description: 'Updated.',
        });
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          description: 'Please try again later!',
        });
      })
      .finally(() => {
        setTableLoading(false);
      });
  };
  const items = allStatusLead?.map((status) => ({
    key: status?.id,
    label: status?.name,
  }));

  const bulkDelete = async () => {
    setTableLoading(true);
    console.log('selectedRowKeys: ', selectedRowKeys);
    bulkDeleteLeads({ ids: selectedRowKeys })
      .then((res) => {
        const newDataSource = dataSource.filter(
          (item) => !selectedRowKeys.includes(item?.key)
        );
        setDataSource(newDataSource);
        notification.success({
          description: 'Deleted!',
        });
        setSelectedRowKeys([]);
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          description: 'Please try again later!',
        });
      })
      .finally(() => {
        setTableLoading(false);
      });
  };

  const bulkUpdateStatus = async () => {
    console.log('selectedRowKeys: ', selectedRowKeys);
  };

  return (
    <>
      <Form
        form={form}
        name="dynamic_form_nest_item"
        onFinish={onFinish}
        autoComplete="off"
        className="flex flex-col gap-2"
      >
        <div className="border p-5 rounded-lg">
          <div className="text-base font-medium pb-2">Keywords:</div>
          {multiSearch()}
        </div>
        <div className="border p-5 rounded-lg">
          <div className="text-base font-medium pb-2">Additional Criteria</div>
          <Form.List name="additionalCriteria">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }, index) => (
                  <Space
                    key={key}
                    style={{
                      display: 'flex',
                    }}
                    align="baseline"
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'field']}
                      rules={[
                        {
                          required: true,
                          message: 'Please choose a field name',
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        style={{
                          width: 220,
                        }}
                        placeholder="Search to Select"
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.label ?? '').includes(input)
                        }
                        filterSort={(optionA, optionB) =>
                          (optionA?.label ?? '')
                            .toLowerCase()
                            .localeCompare((optionB?.label ?? '').toLowerCase())
                        }
                        options={fieldItems}
                        onChange={handleChangeFieldName(key)}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'operator']}
                      rules={[
                        {
                          required: true,
                          message: 'Missing operator',
                        },
                      ]}
                    >
                      <Select
                        style={{
                          width: 220,
                        }}
                        placeholder="Select a operator"
                        optionFilterProp="children"
                        options={checkOperators(index)}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'value']}
                      rules={[
                        {
                          required: true,
                          message: 'Missing value',
                        },
                      ]}
                    >
                      {checkValueInput(index)}
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    Add a Field to Search
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </div>
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SearchOutlined />}
            loading={loading}
          >
            Search
          </Button>
        </Form.Item>
      </Form>
      {isSearchedLeadsModalOpen && (
        <Modal
          width={'80rem'}
          title="Searched Leads"
          open={isSearchedLeadsModalOpen}
          //   onOk={handleOk}
          onCancel={closeSearchedLeadsModal}
          footer={
            <>
              <Button onClick={closeSearchedLeadsModal}>Close</Button>
            </>
          }
        >
          <div className="flex flex-col gap-2 p-3">
            <Card>
              <div className="flex justify-between items-center">
                <div>
                  {hasSelected
                    ? `Selected ${selectedRowKeys.length} leads`
                    : ''}
                </div>
                <div className="flex gap-3">
                  <Button
                    danger
                    onClick={bulkDelete}
                    disabled={!hasSelected || tableLoading}
                  >
                    Bulk Delete
                  </Button>
                  <Dropdown.Button
                    type="primary"
                    disabled={!hasSelected || tableLoading}
                    menu={{
                      items,
                      onClick: onBulkStatusMenuClick,
                    }}
                  >
                    Bulk Update status
                  </Dropdown.Button>
                </div>
              </div>
            </Card>
            <div>
              <Table
                loading={tableLoading}
                rowSelection={rowSelection}
                dataSource={dataSource}
                columns={columns}
              />
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default MasterRefine;
