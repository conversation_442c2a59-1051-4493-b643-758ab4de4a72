import React from 'react';
import { MdSync } from 'react-icons/md';
import Widget from '../../../components/Widget/Widget';
import { useQuery } from '@tanstack/react-query';
import { getLeadCounts } from '../../../services/jobLeads';

const ManualSyncedLeadCount = () => {
  const { data } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT_SYNCED_LEAD'],
    async () => {
      const { data } = await getLeadCounts();
      return data;
    },
    {
      enabled: true,
      initialData: {
        result: {
          manualLeadCount: 0,
          syncedLeadCount: 0,
          emailSentsCount: 0,
          newEmailFoundCount: 0,
          contactAddedCount: 0,
        },
      },
    }
  );

  return (
    <div className="h-full">
      <Widget
        icon={<MdSync className="h-7 w-7" />}
        title={'Synced Leads'}
        subtitle={data?.result.syncedLeadCount}
        extra="shadow-none"
      />
    </div>
  );
};

export default ManualSyncedLeadCount;
