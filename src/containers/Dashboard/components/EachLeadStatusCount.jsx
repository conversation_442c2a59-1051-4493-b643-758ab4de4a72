import React from 'react';
import { useQuery } from '@tanstack/react-query';
import PieChartCard from '../../../components/Widget/PieChartCard';
import { getLeadCountsForEachStatus } from '../../../services/jobLeads';

const EachLeadStatusCount = () => {
  const { data } = useQuery(
    ['EACH_LEAD_STATUS_COUNT'],
    async () => {
      const { data } = await getLeadCountsForEachStatus();
      return data.result;
    },
    {
      enabled: true,
    }
  );

  const leadLabels = data?.map((item) => item.name) || [];
  const leadCount = data?.map((item) => Number(item.count)) || [];

  return (
    <PieChartCard
      title="Lead Status"
      pieLabels={leadLabels}
      pieData={leadCount}
    />
  );
};

export default EachLeadStatusCount;
