import React from 'react';
import { MdMarkEmailRead } from 'react-icons/md';
import Widget from '../../../components/Widget/Widget';
import { useQuery } from '@tanstack/react-query';
import { getLeadCounts } from '../../../services/jobLeads';

const ManualSyncedLeadCount = () => {
  const { data } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT_NEW_EMAIL'],
    async () => {
      const { data } = await getLeadCounts();
      return data;
    },
    {
      enabled: true,
      initialData: {
        result: {
          manualLeadCount: 0,
          syncedLeadCount: 0,
          emailSentsCount: 0,
          newEmailFoundCount: 0,
          contactAddedCount: 0,
        },
      },
    }
  );

  return (
    <div className="h-full">
      <Widget
        icon={<MdMarkEmailRead className="h-7 w-7" />}
        title={'New Emails Found'}
        subtitle={data?.result.newEmailFoundCount}
        extra="shadow-none"
      />
    </div>
  );
};

export default ManualSyncedLeadCount;
