import React from 'react';
import { MdPersonAdd } from 'react-icons/md';
import Widget from '../../../components/Widget/Widget';
import { useQuery } from '@tanstack/react-query';
import { getLeadCounts } from '../../../services/jobLeads';

const ManualSyncedLeadCount = () => {
  const { data } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT_MANUAL_LEAD'],
    async () => {
      const { data } = await getLeadCounts();
      return data;
    },
    {
      enabled: true,
      initialData: {
        result: {
          manualLeadCount: 0,
          syncedLeadCount: 0,
          emailSentsCount: 0,
          newEmailFoundCount: 0,
          contactAddedCount: 0,
        },
      },
    }
  );

  return (
    <div className="h-full">
      <Widget
        icon={<MdPersonAdd className="h-7 w-7" />}
        title={'Manual Leads'}
        subtitle={data?.result.manualLeadCount}
        extra="shadow-none"
      />
    </div>
  );
};

export default ManualSyncedLeadCount;
