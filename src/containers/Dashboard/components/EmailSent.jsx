import React from 'react';
import { MdEmail } from 'react-icons/md';
import Widget from '../../../components/Widget/Widget';
import { useQuery } from '@tanstack/react-query';
import { getLeadCounts } from '../../../services/jobLeads';

const ManualSyncedLeadCount = () => {
  const { data } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT_EMAIL_SENT'],
    async () => {
      const { data } = await getLeadCounts();
      return data;
    },
    {
      enabled: true,
      initialData: {
        result: {
          manualLeadCount: 0,
          syncedLeadCount: 0,
          emailSentsCount: 0,
          newEmailFoundCount: 0,
          contactAddedCount: 0,
        },
      },
    }
  );

  return (
    <div className="h-full">
      <Widget
        icon={<MdEmail className="h-7 w-7" />}
        title={'Emails Sent'}
        subtitle={data?.result.emailSentsCount}
        extra="shadow-none"
      />
    </div>
  );
};

export default ManualSyncedLeadCount;
