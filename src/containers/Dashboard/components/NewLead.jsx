import React from 'react';
import { useQuery } from '@tanstack/react-query';
import Widget from '../../../components/Widget/Widget';
import { MdBarChart } from 'react-icons/md';
import { getLeadCountsForEachStatus } from '../../../services/jobLeads';

const NewLead = () => {
  const { data = [] } = useQuery(
    ['EACH_LEAD_STATUS_COUNT'],
    async () => {
      const { data } = await getLeadCountsForEachStatus();
      return data.result;
    },
    {
      enabled: true,
    }
  );

  const count =
    data?.reduce(
      (min, current) =>
        current.order_of_display < min.order_of_display ? current : min,
      { count: 0 }
    )?.count || 0;

  return (
    <div className="grid bg-white rounded-[20px] shadow-3xl shadow-shadow-500">
      <Widget
        icon={<MdBarChart className="h-7 w-7" />}
        title={'New Lead'}
        subtitle={count}
      />
    </div>
  );
};

export default NewLead;
