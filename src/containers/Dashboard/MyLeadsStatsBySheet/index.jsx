import React, { useState, useEffect } from 'react';
import { Button, Dropdown, Empty, Spin, Typography } from 'antd';
import { Link } from 'react-router-dom';

import { SessionChartWrapper, SessionState } from './style';
import { Cards } from '../../../components/Cards';
import { ChartjsDonutChart } from '../../../components/Charts/DonutChart';
import clsx from 'clsx';

const MyLeadsStatsBySheet = ({ myLeadsCount, loading }) => {
  const [selectedLeadSheet, setSelectedLeadSheet] = useState('');

  useEffect(() => {
    if (myLeadsCount?.length > 0) {
      setSelectedLeadSheet(myLeadsCount?.[0]?.leadsheetname);
    }
  }, [myLeadsCount]);

  useEffect(() => {
    if (loading) {
      setSelectedLeadSheet('');
    }
  }, [loading]);

  return (
    <SessionChartWrapper className="shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      {/* <Cards
        isbutton={
          !loading && (
            <div className="card-nav">
              <ul>
                <Dropdown
                  menu={{
                    items: [
                      ...new Set([
                        ...myLeadsCount?.map((item) => item.leadsheetname),
                      ]),
                    ].map((leadSheetName, index) => ({
                      key: index,
                      onClick: (e) => setSelectedLeadSheet(leadSheetName),
                      label: (
                        <div
                          className={
                            selectedLeadSheet === leadSheetName
                              ? 'active'
                              : 'deactivate'
                          }
                          // onClick={() => setSelectedLeadSheet(leadSheetName)}
                        >
                          <Link
                            // onClick={() => setSelectedLeadSheet(leadSheetName)}
                            to="#"
                          >
                            {leadSheetName}
                          </Link>
                        </div>
                      ),
                    })),
                  }}
                  placement="bottom"
                  arrow={{
                    pointAtCenter: true,
                  }}
                >
                  <Button>
                    {selectedLeadSheet || myLeadsCount?.[0]?.leadsheetname}
                  </Button>
                </Dropdown>
              </ul>
            </div>
          )
        }
        title="My Leads Statics"
        size="large"
      > */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-700">
            My Leads Statics
          </h3>
          {!loading && (
            <div className="card-nav">
              <ul>
                <Dropdown
                  menu={{
                    items: [
                      ...new Set([
                        ...myLeadsCount?.map((item) => item.leadsheetname),
                      ]),
                    ].map((leadSheetName, index) => ({
                      key: index,
                      onClick: (e) => setSelectedLeadSheet(leadSheetName),
                      label: (
                        <div
                          className={
                            selectedLeadSheet === leadSheetName
                              ? 'active'
                              : 'deactivate'
                          }
                          // onClick={() => setSelectedLeadSheet(leadSheetName)}
                        >
                          <Link
                            // onClick={() => setSelectedLeadSheet(leadSheetName)}
                            to="#"
                          >
                            {leadSheetName}
                          </Link>
                        </div>
                      ),
                    })),
                  }}
                  placement="bottom"
                  arrow={{
                    pointAtCenter: true,
                  }}
                >
                  <Button>
                    {selectedLeadSheet || myLeadsCount?.[0]?.leadsheetname}
                  </Button>
                </Dropdown>
              </ul>
            </div>
          )}
        </div>
      </div>
      {loading ? (
        <div className="sd-spin h-[25rem] w-full flex justify-center items-center">
          <Spin />
        </div>
      ) : myLeadsCount
          .filter((lead) => lead?.leadsheetname === selectedLeadSheet)
          .map((item) => item?.count)
          ?.reduce((a, b) => parseInt(a) + parseInt(b), 0) > 0 ? (
        <div className="session-chart-inner">
          <ChartjsDonutChart
            height={120}
            labels={myLeadsCount
              .filter((lead) => lead?.leadsheetname === selectedLeadSheet)
              .map((item) => item.statusname)}
            datasets={[
              {
                data: myLeadsCount
                  .filter((lead) => lead?.leadsheetname === selectedLeadSheet)
                  .map((item) => parseInt(item?.count)),
                backgroundColor: myLeadsCount
                  .filter((lead) => lead?.leadsheetname === selectedLeadSheet)
                  .map((item) => item?.color),
              },
            ]}
          />

          <SessionState className="flex items-center justify-center gap-2">
            {myLeadsCount
              .filter((lead) => lead?.leadsheetname === selectedLeadSheet)
              .map((item) => (
                <div className="session-single">
                  <div className="chart-label font-semibold">
                    <span
                      className={clsx(`label-dot mr-2 bg-gray-600`)}
                      style={{
                        backgroundColor: item.color,
                      }}
                    />
                    {item.statusname}
                  </div>
                  <span>{item.count}</span>
                  <sub>{`${Math.round(
                    (
                      (parseInt(item.count) /
                        parseInt(
                          myLeadsCount
                            .filter(
                              (lead) =>
                                lead?.leadsheetname === selectedLeadSheet
                            )
                            .map((item) => item?.count)
                            ?.reduce((a, b) => parseInt(a) + parseInt(b), 0)
                        )) *
                      100
                    ).toFixed(2)
                  )} %`}</sub>
                </div>
              ))}
          </SessionState>
        </div>
      ) : (
        <Empty
          imageStyle={{ height: '100%' }}
          description={
            <Typography.Text>There are no leads here</Typography.Text>
          }
        ></Empty>
      )}
      {/* </Cards> */}
    </SessionChartWrapper>
  );
};

export default MyLeadsStatsBySheet;
