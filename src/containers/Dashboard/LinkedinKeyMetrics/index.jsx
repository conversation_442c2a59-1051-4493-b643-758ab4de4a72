import { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  LineChartWrapper,
} from '../../../components/Charts/style';
import { Cards } from '../../../components/Cards';
import { Col, Row, Skeleton, Spin } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { ChartjsLineChart } from '../../../components/Charts';
import { customTooltips } from '../../../utils/chart.utils';
import Heading from '../../../components/Heading';
import { useQuery } from '@tanstack/react-query';
import moment from 'moment';
import { v4 } from 'uuid';
import { getLinkedinMetrics } from '../../../services/users';
import { LINKINEDIN_REQUEST_TYPE } from '../../../components/BullHorn/EmailtriggerStep';
import { getMonthsNeeded } from '..';
import dayjs from 'dayjs';
import { ArrowDownRight, ArrowUpRight } from 'lucide-react';

const LinkedinKeyMetrics = ({ dashboardFilter }) => {
  // const [loading, setLoading] = useState(false);
  const [chartLabels, setChartLabels] = useState([]);

  const { data, isFetching: loading } = useQuery(
    ['LINKEDIN_KEY_METRICS'],
    async () => {
      const linkedinSteps = Object.values(LINKINEDIN_REQUEST_TYPE);
      const response = await Promise.all(
        linkedinSteps.map((step) => {
          return getLinkedinMetrics(step, dashboardFilter);
        })
      );

      const monthsNeeded = getMonthsNeeded();

      const connection = response?.[0]?.data?.result || {};
      const message = response?.[1]?.data?.result || {};
      const inmail = response?.[2]?.data?.result || {};
      const normalMessage = response?.[3]?.data?.result || {};
      const reactPost = response?.[4]?.data?.result || {};
      const follow = response?.[5]?.data?.result || {};
      const viewProfile = response?.[6]?.data?.result || {};

      // Connection Calculation
      const monthCountConnectionDataMap = connection?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const connectionDataList = monthsNeeded?.map(
        (month) => monthCountConnectionDataMap[month] || '0'
      );

      const rangeConnectionList = Math.round(
        ((Number(connectionDataList?.[connectionDataList?.length - 1]) -
          Number(connectionDataList?.[connectionDataList?.length - 2])) *
          100) /
          (Number(connectionDataList?.[connectionDataList?.length - 2]) > 0
            ? Number(connectionDataList?.[connectionDataList?.length - 2])
            : 1)
      );
      // Messages Calculation
      const monthCountMessageDataMap = message?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const messageDataList = monthsNeeded?.map(
        (month) => monthCountMessageDataMap[month] || '0'
      );

      const rangeMessageList = Math.round(
        ((Number(messageDataList?.[messageDataList?.length - 1]) -
          Number(messageDataList?.[messageDataList?.length - 2])) *
          100) /
          (Number(messageDataList?.[messageDataList?.length - 2]) > 0
            ? Number(messageDataList?.[messageDataList?.length - 2])
            : 1)
      );

      // InMails Calculation
      const monthCountInMailDataMap = inmail?.monthData.reduce((acc, item) => {
        const monthKey = dayjs(item.month).format('YYYY-MM');
        acc[monthKey] = item.count;
        return acc;
      }, {});

      const inMailDataList = monthsNeeded?.map(
        (month) => monthCountInMailDataMap[month] || '0'
      );

      const rangeInMailList = Math.round(
        ((Number(inMailDataList?.[inMailDataList?.length - 1]) -
          Number(inMailDataList?.[inMailDataList?.length - 2])) *
          100) /
          (Number(inMailDataList?.[inMailDataList?.length - 2]) > 0
            ? Number(inMailDataList?.[inMailDataList?.length - 2])
            : 1)
      );
      // const normalMessageRange = [
      //   normalMessage?.monthData?.[normalMessage?.monthData?.length - 2] || 0,
      //   normalMessage?.monthData?.[normalMessage?.monthData?.length - 1] || 0,
      // ];

      // React/ Like Calculation

      const monthCountReactPostDataMap = reactPost?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const reactPostDataList = monthsNeeded?.map(
        (month) => monthCountReactPostDataMap[month] || '0'
      );

      const rangeReactPostList = Math.round(
        ((Number(reactPostDataList?.[reactPostDataList?.length - 1]) -
          Number(reactPostDataList?.[reactPostDataList?.length - 2])) *
          100) /
          (Number(reactPostDataList?.[reactPostDataList?.length - 2]) > 0
            ? Number(reactPostDataList?.[reactPostDataList?.length - 2])
            : 1)
      );

      // Follow Calculation

      const monthCountFollowDataMap = follow?.monthData.reduce((acc, item) => {
        const monthKey = dayjs(item.month).format('YYYY-MM');
        acc[monthKey] = item.count;
        return acc;
      }, {});

      const followDataList = monthsNeeded?.map(
        (month) => monthCountFollowDataMap[month] || '0'
      );

      const rangeFollowList = Math.round(
        ((Number(followDataList?.[followDataList?.length - 1]) -
          Number(followDataList?.[followDataList?.length - 2])) *
          100) /
          (Number(followDataList?.[followDataList?.length - 2]) > 0
            ? Number(followDataList?.[followDataList?.length - 2])
            : 1)
      );

      // View Profile Calculation

      const monthCountViewProfileDataMap = viewProfile?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const viewProfileDataList = monthsNeeded?.map(
        (month) => monthCountViewProfileDataMap[month] || '0'
      );

      const rangeViewProfileList = Math.round(
        ((Number(viewProfileDataList?.[viewProfileDataList?.length - 1]) -
          Number(viewProfileDataList?.[viewProfileDataList?.length - 2])) *
          100) /
          (Number(viewProfileDataList?.[viewProfileDataList?.length - 2]) > 0
            ? Number(viewProfileDataList?.[viewProfileDataList?.length - 2])
            : 1)
      );

      let initialLLineChartBarDataSet = [
        {
          key: 'connection-requests',
          name: 'Connection Requests',
          total: +connection?.totalCount || 0,
          isIncrease:
            connectionDataList[connectionDataList?.length - 1] -
              connectionDataList[connectionDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeConnectionList || 0)}%`,
          data: connectionDataList || [],
        },
        {
          key: 'inmails',
          name: 'InMails',
          total: +inmail?.totalCount || 0,
          isIncrease:
            inMailDataList[inMailDataList?.length - 1] -
              inMailDataList[inMailDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeInMailList || 0)}%`,
          data: inMailDataList || [],
        },
        {
          key: 'messages',
          name: 'Messages',
          total: +message?.totalCount || 0,
          isIncrease:
            messageDataList[messageDataList?.length - 1] -
              messageDataList[messageDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeMessageList || 0)}%`,
          data: messageDataList || [],
        },
        {
          key: 'like',
          name: 'Like',
          total: +reactPost?.totalCount || 0,
          isIncrease:
            reactPostDataList[reactPostDataList?.length - 1] -
              reactPostDataList[reactPostDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeReactPostList || 0)}%`,
          data: reactPostDataList || [],
        },
        {
          key: 'viewed-profile',
          name: 'Viewed Profile',
          total: +viewProfile?.totalCount || 0,
          isIncrease:
            viewProfileDataList[viewProfileDataList?.length - 1] -
              viewProfileDataList[viewProfileDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeViewProfileList || 0)}%`,
          data: viewProfileDataList || [],
        },
        {
          key: 'follow',
          name: 'Follow',
          total: +follow?.totalCount || 0,
          isIncrease:
            followDataList[followDataList?.length - 1] -
              followDataList[followDataList?.length - 2] >=
            0,
          range: `${Math.abs(rangeFollowList || 0)}%`,
          data: followDataList || [],
        },
      ];

      const previousMonths = [];

      for (let i = 7; i >= 0; i--) {
        let monthName = moment(new Date())
          .subtract(i, 'month')
          .startOf('month')
          .format('MMM');
        previousMonths.push(monthName);
      }
      setChartLabels([...previousMonths]);

      return initialLLineChartBarDataSet;
    },
    {
      enabled: true,
      initialData: [],
    }
  );

  const chartOptions = {
    tooltips: {
      yAlign: 'bottom',
      xAlign: 'center',
      mode: 'nearest',
      position: 'nearest',
      intersect: false,
      enabled: false,
      custom: customTooltips,
      callbacks: {
        labelColor(tooltipItem, chart) {
          return {
            backgroundColor: '#20C997',
          };
        },
      },
    },
    hover: {
      mode: 'nearest',
      intersect: false,
    },
    layout: {
      padding: {
        left: '0',
        right: 8,
        top: 15,
        bottom: -10,
      },
    },
    maintainAspectRatio: true,
    responsive: true,
    legend: {
      display: false,
      labels: {
        display: false,
      },
    },
    elements: {
      line: {
        tension: 0,
      },
    },
    scales: {
      yAxes: [
        {
          stacked: true,
          gridLines: {
            display: false,
            color: '#e5e9f2',
            borderDash: [8, 4],
            zeroLineColor: 'transparent',
            beginAtZero: true,
          },
          ticks: {
            display: false,
          },
        },
      ],
      xAxes: [
        {
          stacked: true,
          gridLines: {
            display: false,
            color: '#e5e9f2',
            borderDash: [8, 4],
            zeroLineColor: 'transparent',
          },
          ticks: {
            display: false,
          },
        },
      ],
    },
  };

  const lineChartPointStyle = {
    borderColor: '#C6D0DC',
    borderWidth: 2,
    fill: false,
    pointRadius: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6],
    pointBackgroundColor: [
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      'transparent',
      '#20C997',
    ],
    pointHoverBackgroundColor: '#20C997',
    pointHoverRadius: 6,
    pointBorderColor: 'transparent',
  };

  return (
    <div className="w-full h-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <div className="p-6">
        <h3 className="text-lg font-semibold text-cyan-600 mb-4 flex items-center">
          LinkedIn Key Metrics
          <div className="ml-2 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        </h3>
        {loading ? (
          <div className="sd-spin flex w-full h-full items-center justify-center">
            <Skeleton active />
          </div>
        ) : (
          <div className="space-y-4">
            {data.map((item, index) => (
              <div
                key={index}
                className="flex justify-between items-center group hover:bg-gray-50/50 rounded-lg p-2 -m-2 transition-all duration-200"
              >
                <span className="text-sm text-gray-700 font-medium">{item.name}</span>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{item.total}</span>
                  <div className="bg-emerald-100 text-emerald-700 rounded-full px-2 py-1 text-xs flex items-center space-x-1">
                    {!item?.isIncrease ? (
                      <ArrowUpRight className="w-3 h-3" />
                    ) : (
                      <ArrowDownRight className="w-3 h-3" />
                    )}

                    <span>{item.range}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
    // <LineChartWrapper>
    //   {data?.length > 0 && (
    //     <Cards
    //       isbutton={<div className="card-nav"></div>}
    //       title="Linkedin Key Metrics"
    //       size="large"
    //     >
    //       {loading ? (
    //         <div className="sd-spin">
    //           <Spin />
    //         </div>
    //       ) : (
    //         <div className="linkedin-chart-wrap">
    //           {data?.map((item, index) => (
    //             <Row className="line-chart-row" key={item.key || v4()}>
    //               <Col xxl={10} xs={24}>
    //                 <div className="growth-upward">
    //                   <p>{item.name}</p>
    //                   <Heading as="h4">
    //                     {item.total}
    //                     <sub>
    //                       {!item?.isIncrease ? (
    //                         <ArrowDownOutlined />
    //                       ) : (
    //                         <ArrowUpOutlined />
    //                       )}
    //                       {item.range}
    //                     </sub>
    //                   </Heading>
    //                 </div>
    //               </Col>
    //               <Col xxl={14} xs={24}>
    //                 <div className="border-linechart">
    //                   <ChartContainer className="parentContainer">
    //                     <ChartjsLineChart
    //                       height={55}
    //                       datasets={[
    //                         {
    //                           data: item.data,
    //                           ...lineChartPointStyle,
    //                         },
    //                       ]}
    //                       options={chartOptions}
    //                       labels={chartLabels}
    //                     />
    //                   </ChartContainer>
    //                 </div>
    //               </Col>
    //             </Row>
    //           ))}

    //           {/* <Row className="line-chart-row">
    //             <Col xxl={10} xs={24}>
    //               <div className="growth-upward">
    //                 <p>Like</p>
    //                 <Heading as="h4">
    //                   {linkdinOverviewState.like.data}
    //                   <sub>
    //                     <ArrowUpOutlined />
    //                     108%
    //                   </sub>
    //                 </Heading>
    //               </div>
    //             </Col>
    //             <Col xxl={14} xs={24}>
    //               <div className="border-linechart">
    //                 <ChartContainer className="parentContainer">
    //                   <ChartjsLineChart
    //                     height={55}
    //                     datasets={[
    //                       {
    //                         data: linkdinOverviewState.like.chartValue,
    //                         ...lineChartPointStyle,
    //                       },
    //                     ]}
    //                     options={chartOptions}
    //                   />
    //                 </ChartContainer>
    //               </div>
    //             </Col>
    //           </Row>
    //           <Row className="line-chart-row">
    //             <Col xxl={10} xs={24}>
    //               <div className="growth-downward">
    //                 <p>Comments</p>
    //                 <Heading as="h4">
    //                   {linkdinOverviewState.comments.data}
    //                   <sub>
    //                     <ArrowDownOutlined />
    //                     30%
    //                   </sub>
    //                 </Heading>
    //               </div>
    //             </Col>
    //             <Col xxl={14} xs={24}>
    //               <div className="border-linechart">
    //                 <ChartContainer className="parentContainer">
    //                   <ChartjsLineChart
    //                     height={55}
    //                     datasets={[
    //                       {
    //                         data: linkdinOverviewState.comments.chartValue,
    //                         ...lineChartPointStyle,
    //                       },
    //                     ]}
    //                     options={chartOptions}
    //                   />
    //                 </ChartContainer>
    //               </div>
    //             </Col>
    //           </Row>
    //           <Row className="line-chart-row">
    //             <Col xxl={10} xs={24}>
    //               <div className="growth-upward">
    //                 <p>New Followers</p>
    //                 <Heading as="h4">
    //                   {linkdinOverviewState.rate.data}
    //                   <sub>
    //                     <ArrowUpOutlined />
    //                     34%
    //                   </sub>
    //                 </Heading>
    //               </div>
    //             </Col>
    //             <Col xxl={14} xs={24}>
    //               <div className="border-linechart">
    //                 <ChartContainer className="parentContainer">
    //                   <ChartjsLineChart
    //                     height={55}
    //                     datasets={[
    //                       {
    //                         data: linkdinOverviewState.rate.chartValue,
    //                         ...lineChartPointStyle,
    //                       },
    //                     ]}
    //                     options={chartOptions}
    //                   />
    //                 </ChartContainer>
    //               </div>
    //             </Col>
    //           </Row>
    //           <Row className="line-chart-row">
    //             <Col xxl={10} xs={24}>
    //               <div className="growth-downward">
    //                 <p>Following</p>
    //                 <Heading as="h4">
    //                   {linkdinOverviewState.followers.data}
    //                   <sub>
    //                     <ArrowDownOutlined />
    //                     27%
    //                   </sub>
    //                 </Heading>
    //               </div>
    //             </Col>
    //             <Col xxl={14} xs={24}>
    //               <div className="border-linechart">
    //                 <ChartContainer className="parentContainer">
    //                   <ChartjsLineChart
    //                     height={55}
    //                     datasets={[
    //                       {
    //                         data: linkdinOverviewState.followers.chartValue,
    //                         ...lineChartPointStyle,
    //                       },
    //                     ]}
    //                     options={chartOptions}
    //                   />
    //                 </ChartContainer>
    //               </div>
    //             </Col>
    //           </Row> */}
    //         </div>
    //       )}
    //     </Cards>
    //   )}
    // </LineChartWrapper>
  );
};

export default LinkedinKeyMetrics;
