import { useEffect, useState } from 'react';
import { useLazySearchJobLeadsCompaniesQuery } from '../../../app/myLeads';
import _get from 'lodash/get';
import { TableWrapper, UserTableStyleWrapper } from '../../CRM/styled';
import { Table, Tag } from 'antd';
import { currencyFormatter } from '../../../helpers/util';
import { JOB_TYPES } from '../../CRM/Leads';
import dayjs from 'dayjs';

const SyncedLeadDetail = () => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchJobLeadsCompaniesQuery] = useLazySearchJobLeadsCompaniesQuery();

  const columns = [
    {
      title: 'Date Added',
      dataIndex: 'date_added',
      key: 'date_added',
      render: (dateAdded, record) => {
        return (
          dateAdded && (
            <div className="font-semibold text-cyan-600 flex w-full items-end justify-end gap-1">
              {dayjs(dateAdded).format('DD/MM/YYYY HH:MM a')}
            </div>
          )
        );
      },
    },
    {
      title: 'Job Title',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => {
        return (
          <div
            className="font-semibold text-cyan-600 line-clamp-1 max-w-xs"
            title={text}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: 'Company',
      dataIndex: 'company_name',
      key: 'company',
      render: (company, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {company || '-'}
          </div>
        );
      },
    },
    {
      title: 'Address',
      dataIndex: 'address_country',
      key: 'address_country',
      render: (address, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {address || '-'}
          </div>
        );
      },
    },
    {
      title: 'Salary/Rate',
      dataIndex: 'salary',
      key: 'salary',
      render: (salary, record) => {
        return (
          <div className="font-semibold text-cyan-600 flex items-center gap-1">
            {currencyFormatter(salary)}
          </div>
        );
      },
    },
    {
      title: 'Job Type',
      dataIndex: 'jobType',
      key: 'jobType',
      render: (jobType, record) => {
        const officeJobType =
          jobType || record?.job_type || record?.employment_type;
        return (
          jobType && (
            <div className="font-semibold text-cyan-600 flex items-center gap-1">
              <Tag color={'cyan'}>{officeJobType}</Tag>
            </div>
          )
        );
      },
    },
  ];

  const handleGetData = async () => {
    setLoading(true);
    try {
      const companyData = await searchJobLeadsCompaniesQuery({
        statusId: '',
        companyId: '',
        limit: 20,
      });
      const convertData = await _get(companyData, 'data.result.items', []);
      console.log('convertData', convertData);
      setDataSource([...convertData]);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('Error fetching data:', error);
    }
  };

  useEffect(() => {
    handleGetData();
  }, []);

  return (
    <UserTableStyleWrapper>
      <div className="contact-table">
        <TableWrapper className="table-responsive text-gray-800">
          <Table
            loading={loading}
            className="customized-style-pagination w-full"
            rowClassName="editable-row"
            dataSource={dataSource}
            columns={columns}
            pagination={false}
            footer={() => {
              return (
                <div
                  onClick={() => window.open('/my-leads', '_blank')}
                  className="text-cyan-600 font-medium cursor-pointer text-center hover:text-cyan-800 hover:underline "
                >
                  View all
                </div>
              );
            }}
          />
        </TableWrapper>
      </div>
    </UserTableStyleWrapper>
  );
};
export default SyncedLeadDetail;
