import { InfoCircleOutlined } from '@ant-design/icons';
import { Cards } from '../../../components/Cards';
import { getTotalHours, numberWithCommas } from '../../../utils/common';
import { Button, Modal, Popconfirm, Spin, Table } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { getSequenceMetrics } from '../../../services/users';
import { useState } from 'react';

const SequenceDeals = (props) => {
  const { dashboardFilter } = props;
  const [isModalVisible, setIsModalVisible] = useState(false); // State for modal visibility

  const showModal = () => {
    setIsModalVisible(true); // Show modal
  };

  const handleCancel = () => {
    setIsModalVisible(false); // Hide modal
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
  ];

  const {
    data: { sequencedAction, timeSaved, openRate, engagedContacts },
    isFetching,
  } = useQuery(
    ['SEQUENCE_METRICS', dashboardFilter],
    async () => {
      const { data } = await getSequenceMetrics(dashboardFilter);
      console.log('SEQUENCE_METRICS: ', data);
      return data?.result?.data;
    },
    {
      enabled: true,
      initialData: {
        sequencedAction: 0,
        timeSaved: 0,
        openRate: 0,
        engagedContacts: 0,
      },
    }
  );

  return (
    <div className="p-6 h-full w-full rounded-lg border bg-card text-card-foreground shadow-lg border border-gray-200/50 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <div>
        <div className="text-4xl font-bold pb-2">
          {isFetching ? (
            <Spin />
          ) : (
            <div className="flex items-center">
              {numberWithCommas(sequencedAction)}
              <div class="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            </div>
          )}
        </div>
        <span className="text-sm font-semibold text-gray-700">
          Sequenced Actions
        </span>
      </div>
      <div className="font-Montserrat py-3">
        <div className="text-4xl font-bold pb-2">
          {isFetching ? <Spin /> : getTotalHours(timeSaved)}
        </div>
        <span className="text-sm font-semibold text-gray-700">
          Time Saved{' '}
          <Popconfirm
            rootClassName="customize-tooltip-widget"
            placement="right"
            title="Time Saved"
            description="Every lead counts as 20 minutes saved"
          >
            <Button type="text" icon={<InfoCircleOutlined />} />
          </Popconfirm>{' '}
        </span>
      </div>
      <div className="font-Montserrat py-3">
        <div
          className="text-4xl font-bold pb-2"
          onClick={showModal}
          style={{ cursor: 'pointer' }}
        >
          {isFetching ? <Spin /> : numberWithCommas(engagedContacts?.length)}
        </div>
        <span className="text-sm font-semibold text-gray-700">
          <span onClick={showModal} style={{ cursor: 'pointer' }}>
            Engaged Contacts
          </span>
          <Popconfirm
            rootClassName="customize-tooltip-widget"
            placement="right"
            title="Engaged Contacts"
            description="Contacts that have opened/clicked an email, responded to a text message, or viewed a page on your site"
          >
            <Button type="text" icon={<InfoCircleOutlined />} />
          </Popconfirm>{' '}
        </span>
      </div>
      {/* <div className="font-Montserrat border-b py-5">
          <div className="text-4xl font-bold pb-2">
            {isFetching? <Spin /> : `${openRate} %`}
          </div>
          <span className="text-sm font-semibold text-gray-700">Open Rate</span>
        </div> */}
      <Modal
        title="List Engaged Contacts"
        visible={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={900}
        className="search-table-new-design-container"
      >
        <Table
          dataSource={engagedContacts}
          columns={columns}
          rowKey="email"
          pagination={false}
          scroll={{
            y: 400,
          }}
        />
      </Modal>
    </div>
  );
};

export default SequenceDeals;
