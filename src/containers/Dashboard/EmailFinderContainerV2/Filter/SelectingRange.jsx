import React, { useRef, useState } from 'react';
import { PlusOutlined, SwapRightOutlined } from '@ant-design/icons';
import { Button, Divider, Input, InputNumber, Select, Space } from 'antd';
import { formatNumber } from '../../../../components/BullHorn/BullHornJobSubmissionForm';
import { tagRender } from '../../../../components/SearchDetailV2/NewSearchFilterComponent';

const SelectingRange = ({ onSelect, defaultItems, ...props }) => {
  const [items, setItems] = useState([...defaultItems]);
  const [min, setMin] = useState('');
  const [max, setMax] = useState('');
  const inputRef = useRef(null);
  //   const onNameChange = (event) => {
  //     setName(event.target.value);
  //   };
  const addItem = (e) => {
    e.preventDefault();
    setItems([...items, [min, max]]);
    setMin('');
    setMax('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };
  return (
    <Select
      optionRender={({ value }) => {
        return (
          value?.length > 0 && (
            <div className="flex items-center gap-2 font-medium">
              <span>{formatNumber(value?.[0])}</span>
              <SwapRightOutlined />
              <span>{formatNumber(value?.[1])}</span>
            </div>
          )
        );
      }}
      className="w-full"
      placeholder="Select..."
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider
            style={{
              margin: '8px 0',
            }}
          />
          <Space
            style={{
              padding: '0 8px 4px',
            }}
          >
            <InputNumber
              placeholder="min"
              ref={inputRef}
              value={min}
              onChange={(value) => setMin(value)}
              onKeyDown={(e) => e.stopPropagation()}
            />
            <InputNumber
              placeholder="max"
              value={max}
              onChange={(value) => setMax(value)}
              onKeyDown={(e) => e.stopPropagation()}
            />
            <Button
              disabled={!min || !max}
              type="text"
              icon={<PlusOutlined />}
              onClick={addItem}
            >
              Add item
            </Button>
          </Space>
        </>
      )}
      {...props}
      // allowClear
      mode="multiple"
      onSelect={onSelect}
      options={items.map((item) => ({
        label: `${item[0]} - ${item[1]}`,
        value: item,
      }))}
    />
  );
};

export default SelectingRange;
