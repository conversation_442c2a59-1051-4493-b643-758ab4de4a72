import {
  AutoComplete,
  Avatar,
  Button,
  Checkbox,
  Collapse,
  Input,
  Modal,
  Popconfirm,
  Segmented,
  Select,
  Slider,
  Space,
  Spin,
} from 'antd';
import { SEARCH } from '..';
import {
  AimOutlined,
  BankOutlined,
  Bar<PERSON><PERSON>Outlined,
  <PERSON>esOutlined,
  EnterOutlined,
  FileSearchOutlined,
  FlagOutlined,
  FundOutlined,
  LaptopOutlined,
  LoadingOutlined,
  MergeCellsOutlined,
  PaperClipOutlined,
  QuestionCircleFilled,
  SlidersOutlined,
  SwapRightOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import CheckboxGroup from '../CheckboxGroup';
import { useEffect, useState } from 'react';
import {
  getKeywordSuggestions,
  getTitleSuggestions,
} from '../../../../services/search';
import { debounce, get } from 'lodash';
import SelectingRange from './SelectingRange';
import {
  departmentList,
  industryList,
} from '../../../../constants/common.constant';
import {
  findFloquerLocations,
  findFloquerSkills,
} from '../../../../services/emailFinder';
import { useQuery } from '@tanstack/react-query';
import {
  employeeFinderSearchTag,
  getListCompany,
  getRevenue,
  handleSearchRawData,
} from '../../../../services/employee';
import {
  getCompanyDetail,
  getListCompanyLinkedin,
  queryParameters,
} from '../../../../services/linkedInFinder';
import DebounceSelect from '../../../../common/DebounceSelect';
import { formatNumber } from '../../../../components/BullHorn/BullHornJobSubmissionForm';
import { tagRender } from '../../../../components/SearchDetailV2/NewSearchFilterComponent';

const Filter = ({
  selectedSearch,
  filter,
  setFilter,
  rawFilter,
  setRawFilter,
  handleSubmitSearch,
  handleSaveSearch,
  loading,
}) => {
  const [companyListPeople, setCompanyListPeople] = useState([]);
  const [companyToSearchListPeople, setCompanyToSearchListPeople] = useState(
    []
  );
  const [companyLoading, setCompanyLoading] = useState(false);
  const [sequenceFilterType, setSequenceFilterType] = useState('+');

  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [revenueLoading, setRevenueLoading] = useState(true); // Revenue and Funding loading
  const [listDataFunding, setListDataFunding] = useState([]);
  const [fundingLoading, setFundingLoading] = useState(true); // Revenue and Funding loading
  const [searchNameToSave, setSearchNameToSave] = useState();
  const [loadingSavedSearch, setLoadingSavedSearch] = useState(false);
  const [openSavedSearch, setOpenSavedSearch] = useState(false);
  const [searchCompanyTimeOut, setSearchCompanyTimeOut] = useState(null);
  const [inputSearchRawValue, setInputSearchRawValue] = useState('');
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [optionRawSearch, setOptionRawSearch] = useState([]);

  useEffect(() => {
    const handler = setTimeout(async () => {
      if (inputSearchRawValue) {
        const payload = {
          keyword: encodeURIComponent(inputSearchRawValue),
        };
        const { data } = await handleSearchRawData(payload);
        setLoadingSearch(true);
        setOptionRawSearch(data?.result?.data);
        setLoadingSearch(false);
      }
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [inputSearchRawValue]);

  const functionSaveSearch = async () => {
    setLoadingSavedSearch(true);
    const data = await handleSaveSearch(searchNameToSave);
    setLoadingSavedSearch(false);
    setOpenSavedSearch(false);
  };

  const handleGetRevenue = async (payload) => {
    try {
      setRevenueLoading(true);
      const { data } = await getRevenue(payload);
      if (data?.faceting?.organization_trading_status_facets?.length > 0) {
        setListDataRevenue(data?.faceting?.organization_trading_status_facets);
      }
      setRevenueLoading(false);
    } catch (error) {
      setRevenueLoading(false);
      setListDataRevenue([]);
      console.log('error: ', error);
    }
  };

  const handleGetFunding = async (payload) => {
    try {
      setFundingLoading(true);
      const { data } = await getRevenue(payload);
      if (data?.faceting?.latest_funding_stage_facets?.length > 0) {
        setListDataFunding(data?.faceting?.latest_funding_stage_facets);
      }
      setFundingLoading(false);
    } catch (error) {
      setFundingLoading(false);
      setListDataFunding([]);
      console.log('error: ', error);
    }
  };

  const getTechnoListOptions = async (keywords) => {
    setCompanyLoading(true);
    try {
      const payload = {
        searchText: keywords,
        type: 'technology',
      };
      const { data } = await employeeFinderSearchTag(payload);
      if (data?.tags?.length > 0) {
        const options = data?.tags.map((item) => ({
          label: item?.cleaned_name,
          value: item?.uid,
        }));
        return options;
      }
      return [];
    } catch (error) {
      setCompanyLoading(false);
      console.log('error: ', error);
    }
  };

  const handleSearchOption = async (keywords) => {
    if (searchCompanyTimeOut) {
      clearTimeout(searchCompanyTimeOut);
    }

    const timeout = setTimeout(() => {
      getCompanyListOptions(keywords);
    }, 1000);

    setSearchCompanyTimeOut(timeout);
  };

  const getCompanyListOptions = async (keywords) => {
    setCompanyLoading(true);
    setCompanyListPeople([]);
    try {
      const payload = {
        type: 'classic',
        category: 'companies',
        keywords,
        limit: 10,
      };
      const { data } = await getListCompanyLinkedin(payload);
      if (data?.result?.data?.items?.length > 0) {
        const companyList = data?.result?.data?.items;
        setCompanyListPeople([...companyList]);
        const newOptionsList = [...companyList, ...companyToSearchListPeople];
        const cleanArr = newOptionsList.filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.id === item.id)
        );
        setCompanyToSearchListPeople(cleanArr);
      } else {
        setCompanyListPeople([]);
      }

      setCompanyLoading(false);
      return data?.result?.data?.items;
    } catch (error) {
      setCompanyLoading(false);
      console.log('error: ', error);
    }
  };

  const querySearchParameters = async (type, keywords = '') => {
    try {
      const { data } = await queryParameters({ type, keywords });
      if (data?.result?.data?.items?.length > 0) {
        const options = data?.result?.data?.items.map((item) => ({
          label: item?.title,
          value: item?.id,
        }));
        return options;
      }
      return [];
    } catch (error) {
      return [];
    }
  };

  useEffect(() => {
    handleGetRevenue({
      openFactorNames: ['organization_trading_status'],
    });
    handleGetFunding({
      openFactorNames: ['organization_latest_funding_stage_cd'],
    });
    getCompanyListOptions('');
  }, []);

  return (
    <div>
      <div className="w-full flex flex-col gap-2 max-h-[70vh] overflow-y-auto">
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['company-name']}
          items={[
            {
              key: 'company-name',
              label: (
                <div className="flex items-center gap-2">
                  <BankOutlined className="text-lg" />
                  <span>Company</span>
                  {filter?.companyName?.length > 0 && (
                    <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                      {filter?.companyName?.length}
                    </span>
                  )}
                </div>
              ),
              children: (
                <div>
                  <Select
                    showSearch
                    mode="multiple"
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-cyan' })
                    }
                    className="w-full"
                    value={filter?.companyIds ? filter?.companyName : []}
                    options={companyListPeople
                      ?.filter((item) => item?.name?.trim())
                      .map((option) => ({
                        value: option?.id,
                        label: option?.name,
                      }))}
                    // disabled={companyLoading}
                    optionRender={(item) => {
                      const itemId = item?.value;
                      const option = companyToSearchListPeople.find(
                        (it) => it?.id === itemId
                      );

                      return (
                        <div className="grid p-2">
                          <div className="flex justify-between">
                            <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                              {option?.name}
                              <br />
                              <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                {option?.industry || '-'}
                              </span>
                            </span>

                            <Avatar
                              className="w-10 h-10"
                              src={option?.logo ? `${option?.logo}` : ''}
                              shape="square"
                            ></Avatar>
                          </div>
                        </div>
                      );
                    }}
                    loading={companyLoading}
                    filterOption={false}
                    placeholder="Enter company name..."
                    suffixIcon={
                      companyLoading ? <LoadingOutlined spin /> : null
                    }
                    onSearch={(e) => handleSearchOption(e)}
                    // onSelect={async (selectedCompanyName) => {
                    //   setFilter({
                    //     ...filter,
                    //     companyName: selectedCompanyName,
                    //     companyId: companyListPeople?.find(
                    //       (item) => item?.name === selectedCompanyName
                    //     )?.id,
                    //   });
                    // }}
                    onChange={(values) => {
                      const items = companyToSearchListPeople.filter(
                        (item) =>
                          values?.includes(item.id) ||
                          values?.includes(item.name)
                      );
                      setFilter({
                        ...filter,
                        companyName: items?.map((item) => item.name),
                        companyIds: items?.map((item) => item.id),
                      });
                    }}
                  />
                  <Checkbox
                    style={{ marginTop: '20px' }}
                    defaultChecked={true}
                    onChange={() => {
                      const currentChecked = filter?.isCurrentCompany
                        ? true
                        : false;
                      setFilter({
                        ...filter,
                        isCurrentCompany: !currentChecked,
                      });
                    }}
                  >
                    Current Company
                  </Checkbox>
                </div>
              ),
            },
          ]}
        />
        {selectedSearch === SEARCH.CONTACT && (
          <Collapse
            expandIconPosition="end"
            defaultActiveKey={['contact-name']}
            items={[
              {
                key: 'contact-name',
                label: (
                  <div className="flex items-center gap-2">
                    <UserOutlined className="text-lg" />
                    <span>Name</span>
                    {filter?.contactName?.trim() && (
                      <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                        1
                      </span>
                    )}
                  </div>
                ),
                children: (
                  <div className="flex items-center gap-2">
                    <Select
                      placeholder="Enter personal name..."
                      optionFilterProp="label"
                      className="w-11/12"
                      // mode="tags"
                      showSearch
                      // fetchOptions={(text) =>
                      //   querySearchParameters('PEOPLE', text)
                      // }
                      options={optionRawSearch?.map((item) => ({
                        value: item?.name,
                        label: item?.name,
                      }))}
                      onSelect={(item) => {
                        setFilter({
                          ...filter,
                          contactName: item,
                        });
                      }}
                      value={filter?.contactName ? [filter?.contactName] : []}
                      onInputKeyDown={(evt) => {
                        setInputSearchRawValue(evt.target.value);
                      }}
                    />
                    <Popconfirm
                      rootClassName="customize-tooltip-widget"
                      placement="right"
                      title="Guide"
                      description={'Hit "Enter" to add the name to the filter'}
                      trigger={'hover'}
                    >
                      <Button
                        type="text"
                        icon={
                          <EnterOutlined className="font-medium text-gray-700" />
                        }
                      />
                    </Popconfirm>
                  </div>
                ),
              },
            ]}
          />
        )}

        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['location']}
          items={[
            {
              key: 'location',
              label: (
                <div className="flex items-center gap-2">
                  <AimOutlined className="text-lg" />
                  <span>Location</span>
                  {filter?.location?.length > 0 && (
                    <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                      {filter?.location?.length}
                    </span>
                  )}
                </div>
              ),
              children: (
                <div>
                  <DebounceSelect
                    value={
                      filter?.locationIds?.length > 0 ? filter?.locationIds : []
                    }
                    labelValue={filter?.location}
                    placeholder="Search location..."
                    optionFilterProp="label"
                    className="w-full"
                    mode="multiple"
                    showSearch
                    fetchOptions={(text) =>
                      querySearchParameters('LOCATION', text)
                    }
                    onChange={(evt) => {
                      const items = evt.map((item) => item?.label);
                      const newListItems = [...new Set([...items])];
                      const itemIds = evt.map((item) => item?.value);
                      setFilter({
                        ...filter,
                        location: newListItems,
                        locationIds: itemIds,
                      });
                    }}
                  />
                  <div className="flex flex-col pt-4">
                    <div className="flex items-center gap-2">
                      <div className="font-medium">Radius (mile)</div>
                      <Popconfirm
                        rootClassName="customize-tooltip-widget"
                        placement="right"
                        title="Radius"
                        description="Optionally set radius (mile) from the city. Note: Leaving at 'Nearby' will disable this feature."
                      >
                        <QuestionCircleFilled />
                      </Popconfirm>{' '}
                    </div>
                    <Slider
                      marks={{
                        0: '0',
                        20: '20',
                        40: '40',
                        80: '80',
                        100: {
                          label: <strong className="text-cyan-600">100</strong>,
                        },
                      }}
                      value={filter?.radius}
                      onChange={(value) =>
                        setFilter({ ...filter, radius: value })
                      }
                      step={10}
                      defaultValue={0}
                    />
                  </div>
                </div>
              ),
            },
          ]}
        />
        {selectedSearch === SEARCH.CONTACT && (
          <Collapse
            expandIconPosition="end"
            defaultActiveKey={['occupation']}
            items={[
              {
                key: 'occupation',
                label: (
                  <div className="flex items-center gap-2">
                    <FlagOutlined className="text-lg" />
                    <span>Occupation</span>
                    {(filter?.jobTitle?.length > 0 ||
                      filter?.skills?.length > 0 ||
                      filter?.department?.length > 0 ||
                      filter?.yearOfExperience?.length > 0) && (
                      <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                        {filter?.jobTitle?.length +
                          filter?.skills?.length +
                          filter?.department?.length +
                          (filter?.yearOfExperience?.length > 0 ? 1 : 0)}
                      </span>
                    )}
                  </div>
                ),
                className: 'customized-body-collapse',
                children: (
                  <div>
                    <Collapse
                      ghost
                      expandIconPosition="end"
                      defaultActiveKey={[
                        'role-and-department',
                        'skills',
                        'yoe',
                      ]}
                      items={[
                        {
                          key: 'role-and-department',
                          label: 'Role & Department',
                          children: (
                            <div className="flex flex-col py-3 gap-4">
                              <div className="px-4 flex flex-col gap-2">
                                <div className="font-medium">Job Title</div>
                                <DebounceSelect
                                  placeholder="Enter job title..."
                                  optionFilterProp="label"
                                  className="w-full"
                                  mode="multiple"
                                  showSearch
                                  fetchOptions={(text) =>
                                    querySearchParameters('JOB_TITLE', text)
                                  }
                                  value={
                                    filter?.jobTitleIds?.length > 0
                                      ? filter?.jobTitleIds
                                      : []
                                  }
                                  labelValue={filter?.jobTitle}
                                  onChange={(evt) => {
                                    console.log('evt: ', evt);
                                    const items = evt.map(
                                      (item) => item?.label
                                    );
                                    setFilter({
                                      ...filter,
                                      jobTitle: items,
                                      jobTitleIds: evt.map(
                                        (item) => item?.value
                                      ),
                                    });
                                  }}
                                />
                              </div>
                              <div className="px-4 flex flex-col gap-2">
                                <div className="font-medium">Department</div>
                                <DebounceSelect
                                  value={
                                    filter?.departmentIds?.length > 0
                                      ? filter?.departmentIds
                                      : []
                                  }
                                  labelValue={filter?.department}
                                  placeholder="Search department..."
                                  optionFilterProp="label"
                                  className="w-full"
                                  mode="multiple"
                                  showSearch
                                  fetchOptions={(text) =>
                                    querySearchParameters('DEPARTMENT', text)
                                  }
                                  onChange={(evt) => {
                                    const items = evt.map(
                                      (item) => item?.label
                                    );
                                    setFilter({
                                      ...filter,
                                      department: items,
                                      departmentIds: evt.map(
                                        (item) => item?.value
                                      ),
                                    });
                                  }}
                                />
                              </div>
                              {/* <div className="px-4 flex flex-col gap-2">
                              <div className="font-medium">
                                Changed Jobs Within
                              </div>
                              <Select
                                showSearch
                                placeholder="Select..."
                                filterOption={(input, option) =>
                                  (option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                                }
                                onSelect={(value) => {
                                  setFilter({
                                    ...filter,
                                    changedJobsWithin: value,
                                  });
                                }}
                                options={[
                                  {
                                    value: '3',
                                    label: 'Last 3 Months',
                                  },
                                  {
                                    value: '6',
                                    label: 'Last 6 Months',
                                  },
                                  {
                                    value: '12',
                                    label: 'Last Year',
                                  },
                                  {
                                    value: '24',
                                    label: 'Last 2 Years',
                                  },
                                  {
                                    value: '36',
                                    label: 'Last 3 Years',
                                  },
                                ]}
                              />
                            </div> */}
                            </div>
                          ),
                        },
                        {
                          key: 'skills',
                          label: 'Skills',
                          children: (
                            <div className="p-4">
                              <DebounceSelect
                                value={
                                  filter?.skillIds?.length > 0
                                    ? filter?.skillIds
                                    : []
                                }
                                labelValue={filter?.skills}
                                placeholder="Search skill..."
                                optionFilterProp="label"
                                className="w-full"
                                mode="multiple"
                                showSearch
                                fetchOptions={(text) =>
                                  querySearchParameters('SKILL', text)
                                }
                                onChange={(evt) => {
                                  const skills = evt.map((item) => item?.label);
                                  const newListSkills = [
                                    ...new Set([...skills]),
                                  ];
                                  const skillIds = evt.map(
                                    (item) => item?.value
                                  );
                                  setFilter({
                                    ...filter,
                                    skills: newListSkills,
                                    skillIds,
                                  });
                                  // setRawFilter({
                                  //   ...rawFilter,
                                  //   skills: newListSkills,
                                  //   skillIds,
                                  // });
                                }}
                              />
                            </div>
                          ),
                        },
                        // {
                        //   key: 'yoe',
                        //   label: 'Years of Experience',
                        //   children: (
                        //     <div className="p-4">
                        //       <SelectingRange
                        //         defaultItems={[
                        //           [0, 3],
                        //           [3, 5],
                        //           [5, 10],
                        //         ]}
                        //         value={filter?.yearOfExperience}
                        //         onSelect={(value) => {
                        //           setFilter({
                        //             ...filter,
                        //             yearOfExperience: value,
                        //           });
                        //         }}
                        //       />
                        //     </div>
                        //   ),
                        // },
                      ]}
                    />
                  </div>
                ),
              },
            ]}
          />
        )}

        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['industry']}
          items={[
            {
              key: 'industry',
              label: (
                <div className="flex items-center gap-2">
                  <SlidersOutlined className="text-lg" />
                  <span>Industry</span>
                  {filter?.industry?.length > 0 && (
                    <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                      {filter?.industry?.length}
                    </span>
                  )}
                </div>
              ),
              children: (
                <div className="flex flex-col gap-2">
                  <DebounceSelect
                    value={
                      filter?.industryIds?.length > 0 ? filter?.industryIds : []
                    }
                    labelValue={filter?.industry}
                    placeholder="Search industry..."
                    optionFilterProp="label"
                    className="w-full"
                    mode="multiple"
                    showSearch
                    fetchOptions={(text) =>
                      querySearchParameters('INDUSTRY', text)
                    }
                    onChange={(evt) => {
                      const items = evt.map((item) => item?.label);
                      const newItems = [...new Set([...items])];
                      setFilter({
                        ...filter,
                        industry: newItems,
                        industryIds: evt.map((item) => item?.value),
                      });
                      // setRawFilter({
                      //   ...rawFilter,
                      //   industry: newItems,
                      //   industryIds: evt.map((item) => item?.value),
                      // });
                    }}
                  />
                </div>
              ),
            },
          ]}
        />

        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['company-size']}
          items={[
            {
              key: 'company-size',
              label: (
                <div className="flex items-center gap-2">
                  <UsergroupAddOutlined className="text-lg" />
                  <span>
                    Company Employees
                    {/* <span className="text-xs font-semibold pl-1">(Size)</span> */}
                  </span>
                  {filter?.companySize?.length > 0 && (
                    <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                      1
                    </span>
                  )}
                </div>
              ),
              children: (
                <div>
                  <Select
                    placeholder="Select company employees..."
                    className="w-full"
                    options={[
                      [1, 10],
                      [11, 50],
                      [51, 200],
                      [201, 500],
                      [501, 1000],
                      [1001, 5000],
                      [5001, 10000],
                      [10001],
                    ].map((item) => ({
                      label: `${item[0]} ${item?.[1] ? `- ${item[1]}` : '+'} employees`,
                      value: item,
                    }))}
                    value={
                      filter?.companySize?.length > 0
                        ? [
                            `${filter?.companySize?.[0]} ${filter?.companySize?.[1] ? `- ${filter?.companySize?.[1]}` : '+'} employees`,
                          ]
                        : []
                    }
                    onSelect={(value) => {
                      setFilter({
                        ...filter,
                        companySize: value,
                      });
                    }}
                  />
                </div>
              ),
            },
          ]}
        />

        {selectedSearch === SEARCH.CONTACT && (
          <Collapse
            expandIconPosition="end"
            defaultActiveKey={['keywords']}
            items={[
              {
                key: 'keywords',
                label: (
                  <div className="flex items-center gap-2">
                    <FileSearchOutlined className="text-lg" />
                    <span>Keywords</span>
                    {filter?.keywords?.trim() && (
                      <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                        1
                      </span>
                    )}
                  </div>
                ),
                children: (
                  <div className="flex items-center gap-2">
                    <Input
                      className="w-11/12"
                      onPressEnter={(evt) =>
                        setFilter({
                          ...filter,
                          keywords: evt.target.value,
                        })
                      }
                      onChange={(evt) =>
                        setRawFilter({
                          ...rawFilter,
                          keywords: evt.target.value,
                        })
                      }
                      placeholder="Enter keywords..."
                    />
                    <Popconfirm
                      rootClassName="customize-tooltip-widget"
                      placement="right"
                      title="Guide"
                      description={
                        'Hit "Enter" to add the keyword to the filter'
                      }
                      trigger={'hover'}
                    >
                      <Button
                        type="text"
                        icon={
                          <EnterOutlined className="font-medium text-gray-700" />
                        }
                      />
                    </Popconfirm>
                  </div>
                ),
              },
            ]}
          />
        )}

        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['sequence']}
          items={[
            {
              key: 'sequence',
              label: (
                <div className="flex items-center gap-2">
                  <BranchesOutlined className="text-lg" />
                  <span>Sequence</span>
                  {filter?.sequence?.trim() && (
                    <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                      1
                    </span>
                  )}
                </div>
              ),
              children: (
                <div className="flex items-center gap-2">
                  <Space.Compact className="w-11/12">
                    <Select
                      value={sequenceFilterType}
                      onSelect={(value) => setSequenceFilterType(value)}
                      options={[
                        {
                          value: '+',
                          label: 'Include',
                        },
                        {
                          value: '-',
                          label: 'Exclude',
                        },
                      ]}
                    />
                    <Input
                      onPressEnter={(evt) =>
                        setFilter({
                          ...filter,
                          sequence: `${sequenceFilterType}${evt.target.value}`,
                        })
                      }
                      placeholder="Enter sequence..."
                    />
                  </Space.Compact>
                  <Popconfirm
                    rootClassName="customize-tooltip-widget"
                    placement="right"
                    title="Guide"
                    description={
                      'Hit "Enter" to add the sequence to the filter'
                    }
                    trigger={'hover'}
                  >
                    <Button
                      type="text"
                      icon={
                        <EnterOutlined className="font-medium text-gray-700" />
                      }
                    />
                  </Popconfirm>
                </div>
              ),
            },
          ]}
        />
        {/* <Collapse
        expandIconPosition="end"
        defaultActiveKey={['website-visitors']}
        items={[
          {
            key: 'website-visitors',
            label: (
              <div className="flex items-center gap-2">
                <PaperClipOutlined className="text-lg" />
                <span>Website Visitors</span>
                {filter?.websiteUrl?.trim() && (
                  <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                    1
                  </span>
                )}
              </div>
            ),
            children: (
              <div className="flex items-center gap-2">
                <Input
                  className="w-11/12"
                  onPressEnter={(evt) =>
                    setFilter({
                      ...filter,
                      websiteUrl: evt.target.value,
                    })
                  }
                  prefix="https://"
                  onChange={(evt) =>
                    setRawFilter({
                      ...rawFilter,
                      websiteUrl: evt.target.value,
                    })
                  }
                  placeholder="Enter website URL..."
                />
                <Popconfirm
                  rootClassName="customize-tooltip-widget"
                  placement="right"
                  title="Guide"
                  description={'Hit "Enter" to add the website to the filter'}
                  trigger={'hover'}
                >
                  <Button
                    type="text"
                    icon={
                      <EnterOutlined className="font-medium text-gray-700" />
                    }
                  />
                </Popconfirm>
              </div>
            ),
          },
        ]}
      /> */}

        {/* <Collapse
        expandIconPosition="end"
        defaultActiveKey={['technologies']}
        items={[
          {
            key: 'technologies',
            label: (
              <div className="flex items-center gap-2">
                <LaptopOutlined className="text-lg" />
                <span>Technologies</span>
                {filter?.technologies?.length > 0 && (
                  <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                    {filter?.technologies?.length}
                  </span>
                )}
              </div>
            ),
            children: (
              <div>
                <DebounceSelect
                  placeholder="Search technology..."
                  optionFilterProp="label"
                  className="w-full"
                  mode="multiple"
                  showSearch
                  fetchOptions={(text) => getTechnoListOptions(text)}
                  value={
                    filter?.technologyIds?.length > 0
                      ? filter?.technologyIds
                      : []
                  }
                  onChange={(evt) => {
                    const items = evt.map((item) => item?.label);
                    const newItems = [...new Set([...items])];
                    setFilter({
                      ...filter,
                      technologies: newItems,
                      technologyIds: evt.map((item) => item?.value),
                    });
                  }}
                />
              </div>
            ),
          },
        ]}
      /> */}

        {/* <Collapse
        expandIconPosition="end"
        defaultActiveKey={['revenue']}
        items={[
          {
            key: 'revenue',
            label: (
              <div className="flex items-center gap-2">
                <BarChartOutlined className="text-lg" />
                <span>
                  Revenue
                </span>
                {filter?.revenue?.length > 0 && (
                  <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                    1
                  </span>
                )}
              </div>
            ),
            children: (
              <div className="w-full flex flex-col gap-2">
                <Segmented
                  className="segmented-filter-container"
                  disabled={revenueLoading}
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    // setValue('revenueStatus', value);
                    setFilter({
                      ...filter,
                      revenueStatus: value,
                    });
                  }}
                  options={[
                    { label: 'Is between', value: 'is_between' },
                    { label: 'Is know', value: 'is_know' },
                    { label: 'Is unknown', value: 'is_un_known' },
                  ]}
                />
                <SelectingRange
                  disabled={revenueLoading}
                  defaultItems={[
                    [100000, 500000],
                    [500000, 1000000],
                    [1000000, 5000000],
                    [5000000, 10000000],
                    [10000000, 500000000],
                  ]}
                  tagRender={(props) =>
                    tagRender({
                      ...props,
                      onClose: () => {
                        setFilter({
                          ...filter,
                          revenue: [],
                        });
                      },
                      tagColor: 'tag-cyan',
                    })
                  }
                  value={
                    filter?.revenue?.length > 0
                      ? [
                          `${formatNumber(filter?.revenue?.[0])} - ${formatNumber(filter?.revenue?.[1])}`,
                        ]
                      : []
                  }
                  onSelect={(value) => {
                    setFilter({
                      ...filter,
                      revenue: value,
                    });
                  }}
                />
                {!revenueLoading && (
                  <Checkbox.Group
                    onChange={(checkedValues) =>
                      setFilter({
                        ...filter,
                        revenueStatusItem: checkedValues,
                      })
                    }
                    className="w-full"
                  >
                    {listDataRevenue?.map((item) => (
                      <>
                        <Checkbox value={item?.value} className="w-full">
                          <div className="flex justify-between items-center justify-center">
                            <div>{item?.display_name}</div>
                            <div>{formatNumber(item?.count)}</div>
                          </div>
                        </Checkbox>
                      </>
                    ))}
                  </Checkbox.Group>
                )}
                {revenueLoading && (
                  <div className="w-full flex items-center justify-center">
                    <Spin />
                  </div>
                )}
              </div>
            ),
          },
        ]}
      /> */}
        {/* <Collapse
        expandIconPosition="end"
        defaultActiveKey={['funding']}
        items={[
          {
            key: 'funding',
            label: (
              <div className="flex items-center gap-2">
                <FundOutlined className="text-lg" />
                <span>
                  Funding
                </span>
                {filter?.funding?.length > 0 && (
                  <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                    1
                  </span>
                )}
              </div>
            ),
            children: (
              <div className="w-full flex flex-col gap-2">
                <Segmented
                  className="segmented-filter-container"
                  disabled={fundingLoading}
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setFilter({
                      ...filter,
                      fundingStatus: value,
                    });
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
                {!fundingLoading && (
                  <Checkbox.Group
                    onChange={(checkedValues) =>
                      setFilter({
                        ...filter,
                        organizationLatestFundingStageCd: checkedValues,
                      })
                    }
                    className="w-full"
                  >
                    {listDataFunding?.map((item) => (
                      <>
                        <Checkbox value={item?.value} className="w-full">
                          <div className="flex justify-between items-center justify-center">
                            <div>{item?.display_name}</div>
                            <div>{formatNumber(item?.count)}</div>
                          </div>
                        </Checkbox>
                      </>
                    ))}
                  </Checkbox.Group>
                )}
                {fundingLoading && (
                  <div className="w-full flex items-center justify-center">
                    <Spin />
                  </div>
                )}
                <SelectingRange
                  disabled={fundingLoading}
                  defaultItems={[
                    [100000, 500000],
                    [500000, 1000000],
                    [1000000, 5000000],
                    [5000000, 10000000],
                    [10000000, 500000000],
                  ]}
                  tagRender={(props) =>
                    tagRender({
                      ...props,
                      onClose: () => {
                        setFilter({
                          ...filter,
                          funding: [],
                        });
                      },
                      tagColor: 'tag-cyan',
                    })
                  }
                  value={
                    filter?.funding?.length > 0
                      ? [
                          `${formatNumber(filter?.funding?.[0])} - ${formatNumber(filter?.funding?.[1])}`,
                        ]
                      : []
                  }
                  onSelect={(value) => {
                    setFilter({
                      ...filter,
                      funding: value,
                    });
                  }}
                />
              </div>
            ),
          },
        ]}
      /> */}

        <Modal
          title="Save your Search"
          open={openSavedSearch}
          footer={false}
          onCancel={() => setOpenSavedSearch(false)}
        >
          <Input
            value={searchNameToSave}
            onChange={(e) => setSearchNameToSave(e.target.value)}
            placeholder="Input your search name ..."
          />
          <Button
            loading={loadingSavedSearch}
            onClick={() => functionSaveSearch(searchNameToSave)}
            style={{ width: '100%', marginTop: '10px' }}
            type="primary"
          >
            Save
          </Button>
        </Modal>
      </div>
      <div className="mt-4">
        <Button
          type="default"
          className="bg-cyan-600 font-semibold text-white"
          style={{
            width: '100%',
            height: '40px',
            padding: '0 20px',
          }}
          onClick={() => {
            handleSubmitSearch();
          }}
          loading={loading}
        >
          Search
        </Button>

        <Button
          type="primary"
          className="font-semibold text-white"
          style={{
            width: '100%',
            height: '40px',
            padding: '0 20px',
            marginTop: '10px',
            // backgroundColor: "#415893",
          }}
          onClick={() => {
            setOpenSavedSearch(true);
          }}
        >
          Save this search
        </Button>
      </div>
    </div>
  );
};
export default Filter;
