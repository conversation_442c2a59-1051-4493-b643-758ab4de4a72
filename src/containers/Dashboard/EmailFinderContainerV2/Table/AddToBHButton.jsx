import { Button, Image, notification, Popover } from 'antd';
import logo from '/logo_bull.webp';
import { useEffect, useState } from 'react';
import { searchBullhorn } from '../../../../services/bullhorn';
import clsx from 'clsx';
import { CheckCircleOutlined } from '@ant-design/icons';

const AddToBHButton = ({ isLoading, handleAddContact, record }) => {
  const [existing, setExisting] = useState(false);
  const [loading, setLoading] = useState(true);

  const checkExisting = async (email) => {
    try {
      setLoading(true);
      const { data } = await searchBullhorn(
        'ClientContact',
        0,
        5,
        '',
        '',
        '',
        email,
        null,
        true
      );

      if (data?.result?.length > 0) {
        setExisting(true);
      }
      setLoading(false);
    } catch (error) {
      console.log('error', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    const email = record?.work_email || '';
    if (!email) {
      setLoading(false);
      return;
    }
    checkExisting(email);
  }, []);

  return (
    <>
      <Popover
        placement="top"
        title={false}
        content={
          <div>{existing ? `Added to <PERSON>horn` : `Add to Bullhorn`}</div>
        }
      >
        <Button
          disabled={isLoading || loading}
          loading={loading}
          onClick={async (e) => {
            e.stopPropagation();
            if (existing) {
              notification.info({
                message: 'Contact already exists in Bullhorn',
                description: 'Please try another contact',
                duration: 2,
              });
              return;
            }
            await handleAddContact(record);
          }}
          style={{ borderRadius: '0', width: '50px' }}
          className={clsx(
            'p-1 flex items-center justify-center',
            existing && 'border-green-600'
          )}
          {...(existing && {
            icon: <CheckCircleOutlined className={'text-green-600'} />,
          })}
        >
          <Image
            className="object-fill"
            preview={false}
            src={logo}
            style={{ width: '20px', height: '20px' }}
          />
        </Button>
      </Popover>
    </>
  );
};

export default AddToBHButton;
