import { Avatar } from 'antd';

const ExperienceInfor = ({ record }) => {
  const { work_experience = [], current_positions = [] } = record;
  const items = work_experience?.length ? work_experience : current_positions;

  return (
    items?.length > 0 && (
      <div className="flex flex-col gap-2">
        {items?.map(
          (item, index) =>
            index < 2 && (
              <div
                className="grid grid-cols-10 gap-2 items-center justify-center text-sm"
                key={index}
              >
                <div className="col-span-2 flex items-center justify-center">
                  <Avatar
                    shape="square"
                    src={item?.logo}
                    size={40}
                    alt={item?.company}
                  />
                </div>
                <div className="col-span-8">
                  <span
                    className="text-cyan-700 font-medium line-clamp-1"
                    title={item?.role || item?.position}
                  >
                    {item?.role || item?.position}
                  </span>
                  {(item?.role || item?.position) && (
                    <div className="items-center flex gap-1">
                      <span>at</span>
                      <span
                        className="font-medium line-clamp-1"
                        title={item?.company}
                      >
                        {item?.company}
                      </span>
                    </div>
                  )}
                  {item?.start?.year && (<div className="font-medium opacity-[0.6] text-xs">{`${item?.start?.month && `${item?.start?.month}/`}${item?.start?.year} - ${item?.end ? `${item?.end?.month && `${item?.end?.month}/`}${item?.end?.year}` : 'Present'}`}</div>)}
                  {typeof item?.start === 'string' && (<div className="font-medium opacity-[0.6] text-xs">{`${item.start} - ${item.end ? `${item.end}` : 'Present'}`}</div>)}
                </div>
              </div>
            )
        )}
      </div>
    )
  );
};

export default ExperienceInfor;
