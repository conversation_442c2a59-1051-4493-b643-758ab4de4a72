import { <PERSON><PERSON>, Card } from 'antd';
import { PieChart, Pie, Sector, Cell, ResponsiveContainer } from 'recharts';
import { ChartjsDonutChart } from '../../../components/Charts/DonutChart';
import { useState } from 'react';
import {
  FacebookOutlined,
  LinkedinOutlined,
  TwitterOutlined,
} from '@ant-design/icons';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  index,
}) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const EmployeesTab = ({ name, employeeSize }) => {
  const [topEmployees, setTopEmployees] = useState([]);

  return (
    <div className="w-full grid grid-cols-2 text-xs gap-5">
      <div>
        <div className="font-medium text-sm pb-2">Departments</div>
        <div>
          {`${name} has ${employeeSize} employees, the majority of which are in product & engineering functions.`}
        </div>
        <div className="mt-2 bg-white p-4 rounded-lg shadow-md">
          <div>All Employees</div>
          <div>
            <ChartjsDonutChart
              labels={[
                'Product & Engineering',
                'Sales',
                'Operations',
                'legal',
                'Finance',
              ]}
              isShowTotal={false}
            />
            {}
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-3">
        <div>{`Top Employees from ${name}`}</div>
        <div className="flex flex-col gap-2 max-h-[23rem] overflow-auto">
          {topEmployees?.length > 0 &&
            topEmployees?.map((item, index) => (
              <div className="flex items-center gap-2 p-2 bg-white rounded-lg shadow-md">
                <Avatar size={60} src={item?.profile_pic} />
                <div className="flex flex-col">
                  <span className="font-semibold text-sm">{name}</span>
                  <span
                    title={item?.current_title}
                    className="font-medium text-gray-900 text-xs line-clamp-1 max-w-[14rem]"
                  >
                    {item?.current_title}
                  </span>
                  <div className="flex items-center gap-2 pt-2">
                    {item?.links?.linkedin && (
                      <a
                        href={item?.links?.linkedin}
                        target="_blank"
                        rel="noreferrer"
                      >
                        <LinkedinOutlined className="text-xl font-medium text-[#0288d1] hover:text-[#0a66c2]" />
                      </a>
                    )}
                    {item?.links?.facebook && (
                      <a
                        href={item?.links?.facebook}
                        target="_blank"
                        rel="noreferrer"
                      >
                        <FacebookOutlined className="text-xl font-medium text-[#3f51b5] hover:text-[#0a66c2]" />
                      </a>
                    )}
                    {item?.links?.twitter && (
                      <a
                        href={item?.links?.twitter}
                        target="_blank"
                        rel="noreferrer"
                      >
                        <TwitterOutlined className="text-xl font-medium text-[#03a9f4] hover:text-[#0a66c2]" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
        </div>
        <a
          href="#"
          className="text-[#08c]"
        >{`Show all employees from ${name}`}</a>
      </div>
    </div>
  );
};

export default EmployeesTab;
