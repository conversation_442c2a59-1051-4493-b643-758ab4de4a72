import { LoadingOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';

const MESSAGES = [
  'Processing request',
  'Finding email',
  'Validating email',
  'Verifying Email address',
];

const MESSAGE_PERCENT = [0, 25, 50, 75];

const UnlockEmailLoading = () => {
  const [counter, setCounter] = useState(0);
  const [message, setMessage] = useState(MESSAGES[0]);

  useEffect(() => {
    //Implementing the setInterval method
    const limit = 100;
    const interval = setInterval(() => {
      if (parseInt(counter) === parseInt(limit - 2)) {
        clearInterval(interval);
        return;
      }
      setCounter(counter + 1);
      getMessage(counter + 1);
    }, 300);

    //Clearing the interval
    return () => clearInterval(interval);
  }, [counter]);

  const getMessage = (percent) => {
    switch (percent) {
      case 0:
        setMessage(MESSAGES[0]);
        break;
      case 25:
        setMessage(MESSAGES[1]);
        break;
      case 50:
        setMessage(MESSAGES[2]);
        break;
      case 75:
        setMessage(MESSAGES[3]);
        break;
    }
  };

  return (
    <div className="p-2 border flex items-center justify-center gap-2 text-xs bg-[#f0f0f2] text-gray-900 opacity-60">
      <LoadingOutlined spin />
      <div className="ml-1 h-[1rem] border border-gray-800 w-[1px]" />
      <div className="min-w-[9rem] flex items-center justify-center">
        {message}
      </div>
      <div className="h-[1rem] border w-[1px] border-gray-800" />
      <span className='min-w-[2rem] flex items-center justify-center'>{counter} %</span>
    </div>
  );
};

export default UnlockEmailLoading;
