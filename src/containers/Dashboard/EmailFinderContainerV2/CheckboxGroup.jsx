import React, { useEffect, useState } from 'react';
import { Checkbox, Collapse, Divider } from 'antd';

const CheckboxGroup = ({ plainOptions, title, onSelectChange, value }) => {
  const [checkedList, setCheckedList] = useState([]);
  const checkAll = plainOptions.length === checkedList.length;
  const indeterminate =
    checkedList.length > 0 &&
    checkedList.length < plainOptions.length;
  const onChange = (list) => {
    setCheckedList(list);
    onSelectChange && onSelectChange({ title, list });
  };
  const onCheckAllChange = (e) => {
    setCheckedList(e.target.checked ? plainOptions : []);
    onSelectChange &&
      onSelectChange({ title, list: e.target.checked ? plainOptions : [] });
  };

  useEffect(() => {
    if (value && value.length > 0) {
      // const checkedListTemp = value?.map((item) => item.list)?.flat();
      // console.log('checkedListTemp', checkedListTemp);
      setCheckedList([...value]);
    } else {
      setCheckedList([]);
    }
    // setCheckedList(value);
  }, [value]);

  return (
    <>
      <Collapse
        ghost
        items={[
          {
            key: 'title',
            label: (
              <Checkbox
                indeterminate={indeterminate}
                onChange={onCheckAllChange}
                checked={checkAll}
              >
                {title}
              </Checkbox>
            ),
            children: (
              <Checkbox.Group
                rootClassName="flex flex-col pl-16 gap-2 pt-1"
                options={plainOptions}
                value={checkedList}
                onChange={onChange}
              />
            ),
          },
        ]}
      />
    </>
  );
};
export default CheckboxGroup;
