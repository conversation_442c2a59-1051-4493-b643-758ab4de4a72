import {
  BankFilled,
  DownOutlined,
  PhoneFilled,
  UpOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useState } from 'react';

const ExpandRowRender = ({
  jobs,
  education,
  skills,
  phones,
  professional_emails,
}) => {
  const [isShowAll, setShowAll] = useState(false);
  return (
    <div className="w-full grid grid-cols-2 gap-4 text-[#34383F]">
      <div className="flex flex-col gap-4 w-full ">
        {jobs?.length > 0 && (
          <div className="font-medium grid grid-cols-7 gap-3 items-start w-full">
            <span className="font-semibold text-cyan-600">Experience</span>
            <div className="flex flex-col gap-1 w-full col-span-6">
              {jobs?.map(
                (job, index) =>
                  ((!isShowAll && index < 3) || isShowAll) && (
                    <span key={index} className=" text-sm">
                      {job}
                    </span>
                  )
              )}
              {jobs?.length > 3 && (
                <div
                  className="w-fit flex items-center gap-1 cursor-pointer text-gray-700 font-semibold hover:text-cyan-600"
                  type="text"
                  onClick={() => setShowAll(!isShowAll)}
                >
                  <span>
                    {!isShowAll ? `Show all (${jobs?.length - 3})` : 'Hide'}
                  </span>
                  {!isShowAll ? <DownOutlined className='font-extrabold' /> : <UpOutlined className='font-extrabold' />}
                </div>
              )}
            </div>
          </div>
        )}
        {education?.length > 0 && (
          <div className="font-medium grid grid-cols-7 gap-3 items-start w-full">
            <span className="font-semibold text-cyan-600">Education</span>
            <div className="flex flex-col gap-1 w-full col-span-6">
              {education?.map((edu, index) => (
                <span key={index} className=" text-sm">
                  {edu}
                </span>
              ))}
            </div>
          </div>
        )}
        {skills?.length > 0 && (
          <div className="font-medium grid grid-cols-7 gap-3 items-start w-full">
            <span className="font-semibold text-cyan-600">Skills:</span>
            <div className="flex items-start gap-1 w-full col-span-6">
              {/* {skills?.map((skill, index) => ( */}
              <span className=" text-sm">{skills.join(', ')}</span>
              {/* ))} */}
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-col gap-3 w-full">
        <div className="font-semibold">Other Contact Options</div>
        {professional_emails?.length - 1 > 0 && (
          <div className="flex flex-col items-start gap-1 justify-center">
            {professional_emails?.map(
              (item, index) =>
                index > 0 && (
                  <div className="flex items-center gap-1">
                    <BankFilled className="font-medium" />
                    <div className="font-medium ">{item}</div>
                  </div>
                )
            )}
          </div>
        )}
        {phones?.length - 1 > 0 && (
          <div className="flex flex-col items-start gap-1 justify-center">
            {phones?.map(
              (item, index) =>
                index > 0 && (
                  <div className="flex items-center gap-1">
                    <PhoneFilled className="font-medium" />
                    <div className="font-medium ">{item?.number}</div>
                  </div>
                )
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpandRowRender;
