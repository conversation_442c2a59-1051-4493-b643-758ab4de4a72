import { Tabs, Tag } from 'antd';
import { useState } from 'react';
import EmployeesTab from './EmployeesTab';

const ExpandCompanyRowRender = ({
  name,
  website_url,
  ticker_symbol,
  ticker_symbol_url,
  revenue,
  employees,
  founded,
  best_address,
  phone,
  fax,
  industry_tags,
  website_rank,
  website_search_keywords,
  genai_company_competitors,
  sic_codes,
  naics_codes,
  techstack,
  calculated_employee_size,
}) => {
  const [loading, setLoading] = useState(false);
  const [employeesData, setEmployeesData] = useState([]);
  return (
    <div className="w-full grid grid-cols-2 gap-3 text-xs">
      <div className="w-full grid grid-cols-10 gap-3 text-xs">
        <div className="flex items-center gap-1 col-span-10 font-semibold text-sm mb-4">
          <Tag color="cyan">{name}</Tag>
          <span>Information</span>
        </div>

        <div className="col-span-2 font-semibold">Website</div>
        <a className="col-span-8 text-[#08c]" href={website_url}>
          {website_url || '-'}
        </a>

        <div className="col-span-2 font-semibold">Ticker</div>
        <a className="col-span-8 text-[#08c]" href={ticker_symbol_url}>
          {ticker_symbol || '-'}
        </a>

        <div className="col-span-2 font-semibold">Revenue</div>
        <span className="col-span-8 text-[#ABABAB]">{revenue || '-'}</span>

        <div className="col-span-2 font-semibold">Employees</div>
        <span className="col-span-8 text-[#ABABAB]">{employees || '-'}</span>

        <div className="col-span-2 font-semibold">Founded</div>
        <span className="col-span-8 text-[#ABABAB]">{founded || '-'}</span>

        <div className="col-span-2 font-semibold">Address</div>
        <a
          className="col-span-8 text-[#08c]"
          href={`https://www.google.com/maps?q=${best_address}`}
        >
          {best_address || '-'}
        </a>

        <div className="col-span-2 font-semibold">Phone</div>
        <a className="col-span-8 text-[#08c]" href={`tel:${phone}`}>
          {phone || '-'}
        </a>

        <div className="col-span-2 font-semibold">Fax</div>
        <span className="col-span-8 text-[#ABABAB]">{fax || '-'}</span>

        <div className="col-span-2 font-semibold">Industry</div>
        <span className="col-span-8 text-[#ABABAB]">
          {industry_tags?.join(', ') || '-'}
        </span>

        <div className="col-span-2 font-semibold">Web Rank</div>
        <span className="col-span-8 text-[#ABABAB]">{website_rank || '-'}</span>

        <div className="col-span-2 font-semibold">Keywords</div>
        <span className="col-span-8 text-[#ABABAB]">
          {website_search_keywords?.join(', ') || '-'}
        </span>

        <div className="col-span-2 font-semibold">Competitors</div>
        <div className="col-span-8 text-[#08c]">
          {genai_company_competitors?.length > 0 &&
            genai_company_competitors?.map((item, index) => (
              <>
                <a
                  className="col-span-8 text-[#08c]"
                  href={item?.info_url}
                  key={item?.unique_company_id}
                >
                  {item?.name || '-'}
                </a>
                {index < genai_company_competitors?.length - 1 && (
                  <span>, </span>
                )}
              </>
            ))}
        </div>

        <div className="col-span-2 font-semibold">SIC</div>
        <span className="col-span-8 text-[#ABABAB]">
          {sic_codes?.join(', ') || '-'}
        </span>

        <div className="col-span-2 font-semibold">NAICS</div>
        <span className="col-span-8 text-[#ABABAB]">
          {naics_codes?.join(', ') || '-'}
        </span>

        <div className="col-span-2 font-semibold">Technologies</div>
        <span className="col-span-8 text-[#ABABAB]">
          {techstack?.join(', ') || '-'}
        </span>
      </div>
      <div className="w-full flex items-start">
        <Tabs
          defaultActiveKey="employees"
          items={[
            {
              label: 'Employees',
              key: 'employees',
              children: (
                <EmployeesTab
                  name={name}
                  employeeSize={calculated_employee_size}
                />
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default ExpandCompanyRowRender;
