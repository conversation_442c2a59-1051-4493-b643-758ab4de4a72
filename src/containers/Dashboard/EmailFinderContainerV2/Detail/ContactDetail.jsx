import {
  BankFilled,
  LeftOutlined,
  MailOutlined,
  PhoneFilled,
} from '@ant-design/icons';
import { Avatar, Button, message } from 'antd';
import WorkExperienceCompact from './WorkExperienceCompact';
import { useEffect, useState } from 'react';
import { handleClayCreateSearchData } from '../../../../services/employee';
import EventSourceRender from '../../../../components/EventSource';
import Loading from '../../../HotList/Loading';
import { getContactDetail } from '../../../../services/linkedInFinder';
import { getClayAccessEmailResults } from '../../../../services/emailFinder';

const ContactDetail = ({
  selectedContact,
  setSelectedContact,
  handleGenderSupportBar,
  setDataSource,
  dataSource,
  savedCompany,
  setSelectedCompany,
  setSavedCompany,
}) => {
  const [extraInformation, setExtraInformation] = useState(null);
  const [viewAllSummary, setViewAllSummary] = useState(false);

  const [contactUuid, setContactUuid] = useState();
  const [enrichData, setEnrichData] = useState();
  const [enrichLoading, setEnrichLoading] = useState(false);

  const handleGetContactData = async () => {
    const accessEmailResults = await getClayAccessEmailResults({
      contactIds: [selectedContact?.id],
    });

    if (accessEmailResults) {
      setSelectedContact({
        ...selectedContact,
        work_email:
          accessEmailResults?.data?.result?.data[0].enrichContact
            ?.valid_work_email ||
          accessEmailResults?.data?.result?.data[0].enrichContact?.email_1,
      });
    }
  };

  const handleCreateData = async () => {
    setEnrichLoading(true);
    const uuidData = selectedContact?.id;
    setContactUuid(uuidData);
    const payload = {
      data: [
        {
          linkedInUrl: selectedContact.linkedin_url,
          recordId: uuidData,
        },
      ],
    };
    toggleLoading();
    await handleClayCreateSearchData(payload);
  };

  useEffect(() => {
    handleGetContactData();
  }, []);

  const handleGetDetail = async (identifier) => {
    try {
      const payload = {
        identifier,
      };
      const { data } = await getContactDetail(payload);
      if (data?.result?.data) {
        setExtraInformation({ ...data?.result?.data });
      } else {
        setExtraInformation(null);
      }
      console.log('data', data);
    } catch (error) {
      setExtraInformation(null);
      console.log('error', error);
    }
  };

  // useEffect(() => {
  //   if (!selectedContact?.work_email) {
  //     handleCreateData();
  //   }
  // }, [selectedContact]);

  useEffect(() => {
    handleGetDetail(selectedContact?.public_identifier);
  }, [selectedContact?.public_identifier]);

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    message.success(`Enriched 1 contact successfully!`);
    if (enrichedDataObject.record_id === contactUuid) {
      setEnrichData(enrichedDataObject);
      const newDataSource = dataSource.map((item) => {
        if (item.id === enrichedDataObject.record_id) {
          return { ...item, work_email: enrichedDataObject?.valid_work_email };
        }
        return item;
      });
      setDataSource(newDataSource);
    }
    setEnrichLoading(false);
    endProcess();
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (type === 'add') {
      el.classList.add('expanded');
      el.classList.add('collapsed');
    } else if (type === 'remove') {
      el.classList.remove('expanded');
      el.classList.remove('collapsed');
    }
  };

  const endProcess = () => {
    toggleLoading('remove');
    setEnrichLoading(false);
  };

  return (
    <div className="px-20">
      <div className="flex justify-between text-2xl font-semibold items-center mb-4">
        <span className="Montserrat">Contact Details</span>
        <div className="flex gap-3">
          <Button
            className="bg-white flex items-center "
            onClick={() => {
              setSelectedContact(null);
              setSelectedCompany(savedCompany);
              setSavedCompany(null);
            }}
            icon={<LeftOutlined />}
          >
            Back
          </Button>
        </div>
      </div>
      <div className="flex justify-center items-center w-full">
        <div className="max-w-[60rem] flex flex-col gap-4 items-center justify-center">
          <div className="w-full bg-white border rounded-md shadow-sm grid grid-cols-2 gap-5 py-4 px-14 items-center justify-center">
            <div className="flex flex-col gap-1 items-start">
              <Avatar
                size={160}
                src={
                  selectedContact?.profile_pic ||
                  selectedContact?.profile_picture_url
                }
              />
              <div className="text-4xl font-bold">{selectedContact?.name}</div>
              <div className="opacity-[0.6]">
                {selectedContact?.current_title}
              </div>
              <div className="opacity-[0.6]">{selectedContact?.location}</div>
              {handleGenderSupportBar(selectedContact, handleCreateData)}
            </div>
            <div className="w-full" title={selectedContact?.summary}>
              {selectedContact?.summary ? (
                <>
                  {selectedContact?.summary?.length <= 1000 &&
                    `${selectedContact?.summary} "`}
                  {selectedContact?.summary?.length > 1000 && (
                    <>
                      {viewAllSummary
                        ? `" ${selectedContact?.summary} "`
                        : `" ${selectedContact?.summary.slice(0, 1000)}...`}
                      <span
                        className="pl-2 text-cyan-700 font-medium cursor-pointer hover:text-cyan-400"
                        onClick={() => setViewAllSummary(!viewAllSummary)}
                      >
                        {!viewAllSummary ? `more` : 'hide'}
                      </span>
                    </>
                  )}
                </>
              ) : (
                <span className="opacity-[0.6] italic">No Summary</span>
              )}
            </div>
          </div>
          {selectedContact?.teaser?.phones?.length > 0 && (
            <div className="w-full bg-white border rounded-md shadow-sm py-4 px-14 grid grid-cols-10 items-start justify-start gap-20">
              <div className="text-xl font-semibold col-span-3">Phones</div>
              <div className="flex flex-col gap-4 w-full col-span-7">
                <div className="flex flex-col items-start gap-1 justify-center">
                  {selectedContact?.teaser?.phones?.map((item, index) => (
                    <div className="flex items-center gap-1  pb-2 border-b w-full font-medium text-cyan-700">
                      <PhoneFilled className="font-medium" />
                      <a className="font-medium " href={`tel:${item?.number}`}>
                        {item?.number}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          <div className="col-span-10" id="expand-container-bulk-enrich">
            <div className="w-full" id="expand-contract-bulk-enrich">
              <div className="w-full justify-center items-center flex gap-4 items-center">
                {enrichLoading && (
                  <EventSourceRender
                    updateRecentlyData={updateRecentlyData}
                    sseName="ENRICH_CONTACT_DATA"
                    sseEndpoint="sse/enrich-data"
                  />
                )}
                <Loading />
                <div className="font-semibold text-cyan-700 italic tracking-wide">
                  Accessing Email...
                </div>
              </div>
            </div>
          </div>
          {(enrichData?.valid_work_email || selectedContact?.work_email) &&
            [enrichData?.valid_work_email ?? selectedContact?.work_email]
              .length > 0 && (
              <div className="w-full bg-white border rounded-md shadow-sm py-4 px-14 grid grid-cols-10 items-start justify-start gap-20">
                <div className="text-xl font-semibold col-span-3">Emails</div>
                <div className="flex flex-col gap-4 w-full col-span-7">
                  <div className="flex flex-col items-start gap-1 justify-center">
                    {[
                      enrichData?.valid_work_email ||
                        selectedContact?.work_email,
                    ]?.map((item, index) => (
                      <div className="flex items-center gap-1 pb-2 border-b w-full font-medium text-cyan-700">
                        <MailOutlined className="font-medium" />
                        <a className="font-medium" href={`mailTo:${item}`}>
                          {item}
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          {selectedContact?.experiences?.length > 0 && (
            <div className="w-full bg-white border rounded-md shadow-sm py-4 px-14 grid grid-cols-10 items-start justify-start gap-20  w-full">
              <div className="text-xl font-semibold col-span-3">Experience</div>
              <div className="flex flex-col gap-4 w-full col-span-7">
                {selectedContact?.experiences?.map((item, index) => (
                  <WorkExperienceCompact item={item} index={index} />
                ))}
              </div>
            </div>
          )}
          {selectedContact?.education?.length > 0 && (
            <div className="w-full bg-white border rounded-md shadow-sm py-4 px-14 grid grid-cols-10 items-start justify-start gap-20">
              <div className="text-xl font-semibold col-span-3">Education</div>
              <div className="flex flex-col gap-4 w-full col-span-7">
                {selectedContact?.education?.map((item, index) => (
                  <div
                    className="flex items-center gap-4 pb-2 border-b w-full"
                    key={index}
                  >
                    <Avatar
                      className="min-w-[5rem]"
                      shape="square"
                      size={50}
                      src={
                        item?.school_details?.logo ||
                        'https://example.com/logo.png'
                      }
                    ></Avatar>
                    <div>
                      <div className="flex flex-col">
                        <span className="text-base font-semibold text-cyan-600">
                          {item?.school}
                        </span>
                        <span className="font-medium opacity-[0.6]">
                          {item?.degree}
                        </span>
                        {item?.start?.year && (
                          <div className="font-medium opacity-[0.6] text-xs">{`${item?.start?.month && `${item?.start?.month}/`}${item?.start?.year} - ${item?.end ? `${item?.end?.month && `${item?.end?.month}/`}${item?.end?.year}` : 'Present'}`}</div>
                        )}
                        {typeof item?.start === 'string' && (
                          <div className="font-medium opacity-[0.6] text-xs">{`${item.start} - ${item.end ? `${item.end}` : 'Present'}`}</div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          {extraInformation?.skills?.length > 0 && (
            <div className="w-full bg-white border rounded-md shadow-sm py-4 px-14 grid grid-cols-10 items-start justify-start gap-20">
              <div className="text-xl font-semibold col-span-3">Skills</div>
              <div className="flex flex-col gap-4 w-full col-span-7">
                {extraInformation?.skills?.map((item, index) => (
                  <div
                    className="flex items-center gap-4 pb-2 border-b w-full font-medium text-cyan-700"
                    key={index}
                  >
                    {item?.name}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactDetail;
