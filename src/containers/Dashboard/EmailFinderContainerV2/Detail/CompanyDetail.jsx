import {
  LeftOutlined,
  LinkedinOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Tabs, Tag } from 'antd';
import ContactDetailCompact from './ContactDetailCompact';
import { useEffect, useState } from 'react';
import {
  getCompanyDetail,
  searchPeople,
} from '../../../../services/linkedInFinder';
import Loading from '../../../HotList/Loading';
import { migrateCompanyDetailData } from '../../../../helpers/util';
const googleKey = import.meta.env.VITE_KEY_GOOGLE;

const CompanyDetail = ({
  selectedCompany,
  setSelectedCompany,
  selectedContact,
  setSelectedContact,
  handleGenderSupportBar,
  setDataSource,
  dataSource,
  setSavedCompany,
  savedCompany,
}) => {
  const [company, setCompany] = useState(null);
  const [employees, setEmployees] = useState([]);
  const [companyLoading, setCompanyLoading] = useState(true);
  const [employeesLoading, setEmployeesLoading] = useState(true);

  const [isShowMore, setShowMore] = useState({
    locations: false,
    industry: false,
  });

  const getCompany = async (companyId) => {
    setCompanyLoading(true);
    try {
      const { data } = await getCompanyDetail(companyId);
      const company = migrateCompanyDetailData(data?.result?.data);
      setCompany(company);
      setCompanyLoading(false);
      console.log('getCompany :', data);
    } catch (error) {
      setCompanyLoading(false);
      console.log('getCompany error:', error);
    }
  };

  const getTopEmployees = async (companyId) => {
    setEmployeesLoading(true);
    try {
      const payload = {
        companyIds: [companyId],
        limit: 20,
      };
      const { data } = await searchPeople(payload);
      console.log('getTopEmployees :', data);
      setEmployees(data?.result?.data?.items || []);
      // const company = migrateCompanyDetailData(data?.result?.data);
      // setCompany(company);
      setEmployeesLoading(false);
      console.log('getCompany :', data);
    } catch (error) {
      setEmployeesLoading(false);
      console.log('getTopEmployees error:', error);
    }
  };

  useEffect(() => {
    getCompany(selectedCompany?.id);
    getTopEmployees(selectedCompany?.id);
  }, [selectedCompany?.id]);

  return (
    <div className="px-20">
      <div className="flex justify-between text-2xl font-semibold items-center mb-4">
        <span className="Montserrat">Company Details</span>
        <div className="flex gap-3">
          <Button
            className="bg-white flex items-center "
            onClick={() => {
              setSelectedCompany(null);
            }}
            icon={<LeftOutlined />}
          >
            Back
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-10 gap-5 flex">
        <div className="flex justify-center items-center w-full col-span-7">
          {!companyLoading && (
            <>
              <div className="max-w-[60rem] flex flex-col gap-5">
                <div className="w-full bg-white border rounded-md shadow-sm gap-10 py-4 px-14 items-center justify-center">
                  <div className="flex flex-col gap-1 items-center">
                    <Avatar
                      shape="square"
                      size={160}
                      src={company?.logo_large}
                    />
                    <div className="text-4xl font-bold">{company?.name}</div>
                    <div className="flex items-center gap-1 opacity-[0.6]">
                      <span>{company?.industry?.[0]}</span>
                      <span>-</span>
                      <span>{company?.locations?.[0]?.city}</span>
                      <span>-</span>
                      <span>{`${company?.employee_count_range?.from} ${company?.employee_count_range?.to ? `- ${company?.employee_count_range?.to}` : ''} employees`}</span>
                    </div>
                  </div>
                </div>
                <div
                  className="w-full bg-white border rounded-md shadow-sm gap-10 items-center justify-center"
                  style={{
                    padding: '10px',
                  }}
                >
                  <Tabs
                    defaultActiveKey="1"
                    items={[
                      {
                        key: '1',
                        label: 'Overview',
                        children: (
                          <div
                            className="w-full mt-5"
                            style={{ width: '49vw' }}
                          >
                            <div className="py-2 font-bold text-xl">
                              Overview
                            </div>
                            {company?.description && (
                              <div
                                style={{ whiteSpace: 'pre-line' }}
                                dangerouslySetInnerHTML={{
                                  __html: company?.description,
                                }}
                                className="opacity-[0.6]"
                              />
                            )}
                            <div className="flex flex-col gap-2 mt-5">
                              <span className="font-semibold">Website</span>
                              <a
                                className="font-medium text-cyan-700"
                                target="_blank"
                                href={company?.website}
                              >
                                {company?.website || '-'}
                              </a>
                              <span className="font-semibold">Industry</span>
                              <span className="opacity-[0.6] flex items-start gap-1 flex-col">
                                {company?.industry?.length > 0
                                  ? company?.industry?.map(
                                      (item, index) =>
                                        (isShowMore?.industry ||
                                          (!isShowMore?.industry &&
                                            index <= 5)) && (
                                          <div className="pb-2 border-b">
                                            {item}
                                          </div>
                                        )
                                    )
                                  : '-'}
                                {company?.industry?.length > 5 && (
                                  <>
                                    {!isShowMore?.industry && (
                                      <span
                                        onClick={() =>
                                          setShowMore({
                                            ...isShowMore,
                                            industry: !isShowMore?.industry,
                                          })
                                        }
                                        className="font-medium text-cyan-700 cursor-pointer hover:text-cyan-400"
                                      >
                                        Show more
                                      </span>
                                    )}
                                    {isShowMore?.industry && (
                                      <span
                                        onClick={() =>
                                          setShowMore({
                                            ...isShowMore,
                                            industry: !isShowMore?.industry,
                                          })
                                        }
                                        className="font-medium text-cyan-700 cursor-pointer hover:text-cyan-400"
                                      >
                                        Show less
                                      </span>
                                    )}
                                  </>
                                )}
                              </span>
                              <span className="font-semibold">
                                Company size
                              </span>
                              <span className="opacity-[0.6]">
                                {company?.employee_count}
                              </span>
                              <span className="font-semibold">
                                Headquarters
                              </span>
                              <span className="opacity-[0.6] flex items-start justify-center gap-1 flex-col">
                                {company?.locations?.length > 0
                                  ? company?.locations?.map(
                                      (item, index) =>
                                        (isShowMore?.locations ||
                                          (!isShowMore?.locations &&
                                            index <= 5)) && (
                                          <div className="flex items-start justify-center gap-1 flex-col pb-2 border-b">
                                            <span className="opacity-100 font-medium">
                                              {item?.description}
                                            </span>
                                            <span>{`${item?.street?.[0] || ''} ${item?.city} ${item?.country}`}</span>
                                          </div>
                                        )
                                    )
                                  : '-'}
                                {company?.locations?.length > 5 && (
                                  <>
                                    {!isShowMore?.locations && (
                                      <span
                                        onClick={() =>
                                          setShowMore({
                                            ...isShowMore,
                                            locations: !isShowMore?.locations,
                                          })
                                        }
                                        className="font-medium text-cyan-700 cursor-pointer hover:text-cyan-400"
                                      >
                                        Show more
                                      </span>
                                    )}
                                    {isShowMore?.locations && (
                                      <span
                                        onClick={() =>
                                          setShowMore({
                                            ...isShowMore,
                                            locations: !isShowMore?.locations,
                                          })
                                        }
                                        className="font-medium text-cyan-700 cursor-pointer hover:text-cyan-400"
                                      >
                                        Show less
                                      </span>
                                    )}
                                  </>
                                )}
                              </span>
                              <span className="font-semibold">Founded</span>
                              <span className="opacity-[0.6]">
                                {company?.foundation_date || '-'}
                              </span>
                            </div>
                          </div>
                        ),
                      },
                      {
                        key: '2',
                        label: 'Employees',
                        children: (
                          <div
                            className="w-full mt-5"
                            style={{ width: '49vw' }}
                          >
                            <div className="py-2 font-bold text-xl">
                              Employees
                            </div>
                            {employees?.map((employee, index) => (
                              <div style={{ marginBottom: '5px' }}>
                                <ContactDetailCompact
                                  employee={employee}
                                  index={index}
                                  setSelectedContact={setSelectedContact}
                                  setSelectedCompany={setSelectedCompany}
                                  setSavedCompany={setSavedCompany}
                                  selectedCompany={selectedCompany}
                                />
                              </div>
                            ))}
                            <div className="w-full flex justify-end items-center gap-3 mt-4">
                              <Button
                                disabled={false}
                                className="bg-white flex items-center justify-center shadow-md"
                                // onClick={() => handleGetPrevPage()}
                                icon={<LeftOutlined />}
                              ></Button>
                              {/* <div>{`${parseInt(companyPagination?.pageSize < companyPagination?.totalCount ? companyPagination?.pageSize : companyPagination?.totalCount)} / ${formatNumber(pagination?.totalCount)}`}</div> */}
                              <Button
                                className="bg-white flex items-center justify-center shadow-md"
                                // onClick={() => handleGetNextPage()}
                                icon={<RightOutlined />}
                              ></Button>
                            </div>
                          </div>
                        ),
                      },
                    ]}
                    // onChange={onChange}
                  />
                </div>
                <div className="w-full bg-white border rounded-md shadow-sm flex flex-col gap-1 py-4 px-14 items-start justify-center">
                  <div className="py-2 font-bold text-xl">Location</div>
                  <Tag color="blue-inverse">Primary</Tag>
                  <div>{`${company?.locations?.[0]?.street?.[0] || ''} ${company?.locations?.[0]?.city} ${company?.locations?.[0]?.country}`}</div>
                  <iframe
                    src={`https://www.google.com/maps/embed/v1/place?key=${googleKey}&q=${`${company?.locations?.[0]?.street?.[0] || ''} ${company?.locations?.[0]?.city} ${company?.locations?.[0]?.country}`}`}
                    height="450"
                    frameborder="0"
                    width={'100%'}
                    style={{ border: 0 }}
                    allowfullscreen=""
                    aria-hidden="false"
                    tabindex="0"
                    className="mt-5"
                  />
                </div>
              </div>
            </>
          )}
          {companyLoading && (
            <div className="w-full flex items-center justify-center h-[20rem]">
              <Loading />
            </div>
          )}
        </div>
        <div className="col-span-3 w-full bg-white border rounded-md shadow-sm gap-10 py-2 px-8 items-center justify-center overflow-y-auto max-h-[90rem]">
          {!employeesLoading &&
            (employees?.length > 0 ? (
              <>
                <div className="flex items-center justify-start gap-2 text-base font-semibold py-3">
                  Top Employees from
                  <Tag color="cyan-inverse" className="text-base">
                    {selectedCompany?.company_name}
                  </Tag>
                </div>
                <div className="p-2 flex flex-col gap-2">
                  {employees?.map((employee, index) => (
                    <ContactDetailCompact employee={employee} index={index} />
                  ))}
                </div>
                <div className="flex w-full justify-end my-2">
                  <a className="flex items-center justify-center text-xs font-medium">
                    <span>{`View all contact from ${selectedCompany?.company_name}`}</span>
                    <RightOutlined />
                  </a>
                </div>
              </>
            ) : (
              <div className="w-full flex items-center justify-center h-[20rem]">
                <span className="text-xl font-semibold italic opacity-60">
                  No employees found
                </span>
              </div>
            ))}
          {employeesLoading && (
            <div className="w-full flex items-center justify-center h-[20rem]">
              <Loading />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CompanyDetail;
