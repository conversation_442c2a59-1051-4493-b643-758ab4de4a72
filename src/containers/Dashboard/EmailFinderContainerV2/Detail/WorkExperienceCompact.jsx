import { Avatar, Button } from 'antd';
import { useEffect, useState } from 'react';
import clsx from 'clsx';

const WorkExperienceCompact = ({ item, index }) => {
  const [seeMore, setSeeMore] = useState(false);

  return (
    <div className="flex items-center gap-4 pb-2 border-b w-full" key={index}>
      {item?.logo ? (
        <Avatar
          className="min-w-[5rem]"
          shape="square"
          size={50}
          src={item?.logo}
        />
      ) : (
        <Avatar className="min-w-[5rem]" shape="square" size={50}>
          {item?.name?.charAt(0)}
        </Avatar>
      )}
      <div className="">
        <div className="flex flex-col">
          <span className="text-base font-semibold text-cyan-600">
            {item?.company}
          </span>
          <span className="font-medium opacity-[0.6]">{item?.industry}</span>
          {item?.start?.year && (<div className="font-medium opacity-[0.6] text-xs">{`${item?.start?.month && `${item?.start?.month}/`}${item?.start?.year} - ${item?.end ? `${item?.end?.month && `${item?.end?.month}/`}${item?.end?.year}` : 'Present'}`}</div>)}
          {typeof item?.start === 'string' && (<div className="font-medium opacity-[0.6] text-xs">{`${item.start} - ${item.end ? `${item.end}` : 'Present'}`}</div>)}
          {item?.description && (
            <div>
              <span
                className={clsx(
                  'text-xs opacity-[0.6]',
                  !seeMore && 'line-clamp-1'
                )}
                title={item?.description}
              >
                {item?.description}
              </span>
              <span
                className="text-xs text-cyan-700 font-medium cursor-pointer hover:text-cyan-400"
                onClick={() => setSeeMore(!seeMore)}
              >
                {seeMore ? 'Hide' : 'See more'}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkExperienceCompact;
