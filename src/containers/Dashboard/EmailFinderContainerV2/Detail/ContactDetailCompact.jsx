import { RightOutlined } from '@ant-design/icons';
import { Avatar, Button, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { getLinkedinProfile } from '../../../../services/task';

const ContactDetailCompact = ({ employee, index, setSelectedContact, setSelectedCompany, selectedCompany, setSavedCompany }) => {
  // const [profilePicture, setProfilePicture] = useState('');

  // const handleLinkedinProfile = async (linkedinUrl) => {
  //   try {
  //     const { data } = await getLinkedinProfile(linkedinUrl);
  //     const profilePictureTemp = data?.result?.profile_picture_url_large;
  //     setProfilePicture(profilePictureTemp);
  //   } catch (error) {
  //     console.log('error: ', error);
  //   }
  // };
  // useEffect(() => {
  //   handleLinkedinProfile(employee?.linkedin_url);
  // }, [employee?.linkedin_url]);

  return (
    <div
      className="grid grid-cols-10 gap-3 items-center gap-2 p-2 bg-white rounded-lg shadow-md"
      key={index || uuid()}
    >
      <Avatar onClick={() => {
        setSelectedCompany(null)
        setSelectedContact(employee)
        setSavedCompany(selectedCompany)
        }} src={employee?.profile_picture_url_large} size={60} className="col-span-2">
        {employee?.name}
      </Avatar>
      <div className="flex flex-col gap-1 col-span-6 w-11/12">
        <div className="font-semibold">{`${employee?.name}`}</div>
        <div className="opacity-[0.6] line-clamp-1" title={employee?.headline}>{employee?.headline}</div>
      </div>
      <Button
        href={employee?.profile_url}
        type="link"
        className="flex items-center justify-center text-xs font-medium"
        target="_blank"
      >
        <span>View more</span>
        <RightOutlined />
      </Button>
    </div>
  );
};

export default ContactDetailCompact;
