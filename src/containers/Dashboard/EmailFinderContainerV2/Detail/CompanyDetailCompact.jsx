import {
  FacebookOutlined,
  LinkedinOutlined,
  <PERSON>Outlined,
  TwitterOutlined,
} from '@ant-design/icons';
import { Avatar, Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { getLinkedinProfile } from '../../../../services/task';
import { getCompanyDetail } from '../../../../services/linkedInFinder';

const CompanyDetailCompact = ({
  companyDetails,
  record,
  setSelectedCompany,
}) => {
  const id = companyDetails?.id || null;
  const name = companyDetails?.name || companyDetails?.company || null;
  const logo_url = record?.logo || companyDetails?.logo_url || null;
  const links = companyDetails?.links || null;

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  return (
    companyDetails && (
      <div key={id} className="flex items-center gap-2">
        <div className="min-w-[5rem]">
          {logo_url ? (
            <Avatar shape="square" size={70} src={logo_url} />
          ) : (
            <Avatar shape="square" size={70}>
              {name?.charAt(0)}
            </Avatar>
          )}
        </div>
        <div className="flex flex-col">
          <span
            onClick={(e) => {
              e.stopPropagation();
              if (record?.item_type === 'apollo') return;
              setSelectedCompany({ id, company_name: name });
              // handleSubmitCompanyDetailSearch()
            }}
            className="font-semibold text-base text-[#415896] hover:underline cursor-pointer"
          >
            {name || record?.current_employer}
          </span>
          <span className="font-medium text-gray-900 text-sm">
            {record?.location}
          </span>
          {links && (
            <div>
              <Row className="flex gap-2">
                <Col>
                  <LinkOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(links?.website);
                    }}
                    className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(links?.linkedin);
                    }}
                    className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <FacebookOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(links?.facebook);
                    }}
                    className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <TwitterOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(links?.twitter);
                    }}
                    className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                  />
                </Col>
              </Row>
            </div>
          )}
        </div>
      </div>
    )
  );
};

export default CompanyDetailCompact;
