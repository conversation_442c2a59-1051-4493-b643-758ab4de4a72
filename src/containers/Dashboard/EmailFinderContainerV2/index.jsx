import {
  AimOutlined,
  BankFilled,
  BankOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BookFilled,
  <PERSON>esOutlined,
  B<PERSON>Outlined,
  CaretDownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  CopyOutlined,
  CrownOutlined,
  DeleteOutlined,
  DownOutlined,
  FacebookOutlined,
  FileSearchOutlined,
  FlagOutlined,
  FundOutlined,
  IdcardFilled,
  LaptopOutlined,
  LeftOutlined,
  LinkedinOutlined,
  LinkOutlined,
  LoadingOutlined,
  MailFilled,
  MailOutlined,
  MenuUnfoldOutlined,
  MoreOutlined,
  PaperClipOutlined,
  PhoneFilled,
  PhoneOutlined,
  QuestionCircleFilled,
  ReadOutlined,
  RightOutlined,
  SearchOutlined,
  SendOutlined,
  SlidersOutlined,
  <PERSON>DashOutlined,
  SyncOutlined,
  ThunderboltFilled,
  <PERSON>boltOutlined,
  TwitterOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  AutoComplete,
  Avatar,
  Button,
  Card,
  Checkbox,
  Col,
  Collapse,
  Divider,
  Drawer,
  Dropdown,
  Form,
  Image,
  Input,
  message,
  Modal,
  notification,
  Pagination,
  Popconfirm,
  Popover,
  Radio,
  Row,
  Segmented,
  Select,
  Slider,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import Search from 'antd/es/input/Search';
import { useEffect, useState } from 'react';
import {
  COMMON_STRINGS,
  EMAIL_NOT_FOUND,
} from '../../../constants/common.constant';
import logo from '/logo_bull.webp';
import zileoLogo from '../../../assets/img/welcome/logo.png';
import {
  getLDetailEmployee,
  getListCompanies,
  handleBulkEnrichContactData,
  handleClayCreateSearchData,
  handleFloqerCreateCompanyDetailSearchData,
  handleFloqerCreateSearchData,
  handleSearchRawData,
} from '../../../services/employee';
import EventSourceRender from '../../../components/EventSource';
import { v4 as uuid } from 'uuid';
import Loading from '../../HotList/Loading';
import ExpandRowRender from './ExpandRowRender';
import BullhornBulkAddContactModal from '../../../components/BullHorn/BullhornBulkAddContactModal';
import { useForm } from 'react-hook-form';
import {
  bulkAddToBullhorn,
  insertBullhorn,
  searchBullhorn,
  searchBullhornData,
  upladteBullhorn,
} from '../../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../../hooks/useInfinitiveScroll';
import BullhornSubmissionContact from '../../../components/BullHorn/BullhornSubmissionContact';
import ModalListUserGroup from '../../Sequence/ModalListUserGroup';
import ExpandCompanyRowRender from './ExpandCompanyRowRender';
import Filter from './Filter';
import ContactDetail from './Detail/ContactDetail';
import CompanyDetail from './Detail/CompanyDetail';
import {
  calculateCurrentPage,
  cleanPayload,
  decodeBase64,
  encodeBase64,
  handleGenerateNotificationBulkAddContact,
  migrateCompanyData,
  migrateContactData,
} from '../../../helpers/util';
import ModalShowListExitSequence from '../../Sequence/ModalShowListExitSequence';
import {
  getCompanyDetail,
  getListCompanyLinkedin,
  searchParameters,
  searchPeople,
} from '../../../services/linkedInFinder';
import CompanyDetailCompact from './Detail/CompanyDetailCompact';
import { formatNumber } from '../../../components/BullHorn/BullHornJobSubmissionForm';
import { isEmpty } from 'lodash';
import {
  createSavedSearch,
  deleteSavedSearch,
  getClayAccessEmailResults,
  getSavedSearch,
  updateSavedSearch,
} from '../../../services/emailFinder';
import BullHornJobSubmissionCompany, {
  HENLEY,
} from '../../../components/BullHorn/BullhornJobSubmissionCompany';
import { useViewAs } from '../../../store/viewAs';
import { useAuth } from '../../../store/auth';
import { TIME_ENRICH_EACH_FIELD } from '../../HotList/HotListTable';
import BulkEnrichData from '../../HotList/BulkEnrich';
import { useParams } from 'react-router-dom';
import ExperienceInfor from './Table/ExperienceInfor';
import AddToBHButton from './Table/AddToBHButton';
import clsx from 'clsx';
import UnlockEmailLoading from './UnlockEmailLoading';
import styled from 'styled-components';

const { confirm } = Modal;

const initialFilter = {
  contactName: '',
  companyName: '',
  companyIds: '',
  location: [],
  locationIds: [],
  radius: 0,
  jobTitle: [],
  jobTitleIds: [],
  department: [],
  departmentIds: [],
  changedJobsWithin: 0, // months
  skills: [],
  skillIds: [],
  yearOfExperience: [],
  // Add more filters
  keywords: '',
  // technologies: [],
  // technologyIds: [],
  revenue: [],
  revenueStatus: 'is_between', // is_between | is_know | is_un_known
  revenueStatusItem: [], // public | private
  funding: [],
  organizationLatestFundingStageCd: [],
  fundingStatus: 'is_between', // is_between | is_know | is_un_known
  sequence: '',
  companySize: [],
  websiteUrl: '',
  industry: [],
  industryIds: [],
  isCurrentCompany: true,
};

export const SEARCH = {
  COMPANY: 'Company',
  CONTACT: 'People',
};

const initialPagination = {
  totalCount: 0,
  pageSize: 10,
  currentPage: 1,
  start: 0,
};

const getEnrichFields = (enrichFields) => {
  switch (enrichFields) {
    case 'both':
      return ['email', 'phone'];
    case 'email':
      return ['email'];
    case 'phone':
      return ['phone'];
    default:
      return ['email', 'phone'];
  }
};

const RestyledRadioGroup = styled(Radio.Group)`
  .ant-radio-button-wrapper-checked {
    background-color: rgb(8 145 178) !important;
    border-color: rgb(8 145 178) !important;
  }
  .ant-radio-button-wrapper {
    font-weight: 500 !important;
  }
  .ant-radio-button-wrapper:hover {
    color: rgb(8 145 178) !important;
  }
`;

const EmailFinderContainerV2 = () => {
  // query string
  // let { tab } = useParams();

  const [cursorToken, setCursorToken] = useState(false);

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  // Contact finder
  const { control, getValues, setValue, watch } = useForm();

  const [selectedContact, setSelectedContact] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);

  const [selectedSearch, setSelectedSearch] = useState(SEARCH.CONTACT);

  const [isAddContactFormBulk, setIsAddContactFormBulk] = useState(false);
  const [isAddContactForm, setIsAddContactForm] = useState(false);
  const [isSubmitForm, setIsSubmitForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [flagDetailContact, setFlagDetailContact] = useState(false);
  const [loadingBulkData, setLoadingBulkData] = useState(false);

  const [handleCloseClient, setHandleCloseClient] = useState(false);
  const [handleCloseContact, setHandleCloseContact] = useState(true);

  const [filter, setFilter] = useState(initialFilter);
  const [rawFilter, setRawFilter] = useState(initialFilter);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [expandList, setExpandList] = useState([]);
  const [pagination, setPagination] = useState(initialPagination);

  const [bulkContacts, setBulkContacts] = useState([]);
  const [openExistingUserGroup, setOpenExistingUserGroup] = useState(false);
  const [currentContact, setCurrentContact] = useState(null);

  const [openExistingSequence, setOpenExistingSequence] = useState(false);

  const [nextPageTokenArr, setNextPageTokenArr] = useState([]);
  const [nextPageToken, setNextPageToken] = useState();

  const [inputSearchRawValue, setInputSearchRawValue] = useState('');
  const [optionRawSearch, setOptionRawSearch] = useState([]);
  const [loadingSearch, setLoadingSearch] = useState(false);

  // Company Search
  const [companyDataSource, setCompanyDataSource] = useState([]);
  const [companyPagination, setCompanyPagination] = useState(initialPagination);
  const [showCompany, setShowCompany] = useState(false);

  const [nextPageTokenCompanyArr, setNextPageTokenCompanyArr] = useState([]);
  const [nextPageTokenCompany, setNextPageTokenCompany] = useState();
  const [enrichLoading, setEnrichLoading] = useState(false);
  const [loadingContact, setLoadingContact] = useState([]);
  const [accessEmailResults, setAccessEmailResults] = useState([]);
  const [listSavedData, setListSavedData] = useState([]);
  const [loadingGetSavedSearchData, setLoadingGetSavedSearchData] = useState(
    []
  );
  const [listSavedDataDefault, setListSavedDataDefault] = useState([]);
  const [loadingSaveAsASequence, setLoadingSaveAsASequence] = useState(false);
  const [openEditSavedSearch, setOpenEditSavedSearch] = useState(false);
  const [searchNameToSave, setSearchNameToSave] = useState();
  const [idSearchNameToSave, setIdSearchNameToSave] = useState();
  const [idSearchNameToDelete, setIdSearchNameToDelete] = useState();
  const [loadingEditSearchName, setLoadingEditSearchName] = useState(false);
  const [savedCompany, setSavedCompany] = useState();
  // Bulk Enrich
  const [openBulkEnrichModal, setOpenBulkEnrichModal] = useState(false);

  const showBulkEnrichModal = () => setOpenBulkEnrichModal(true);
  const closeBulkEnrichModal = () => setOpenBulkEnrichModal(false);
  const [selectedTable, setSelectedTable] = useState();
  const [selectedTableItems, setSelectedTableItems] = useState();
  const [selectedRowItems, setSelectedRowItems] = useState([]);
  const onSelectChangeTable = (e, g) => {
    setSelectedTable({ ...selectedTable, [pagination.currentPage]: e });
    setSelectedTableItems({
      ...selectedTableItems,
      [pagination.currentPage]: g,
    });
  };

  // Bulk Add Contacts to BH/ Contact List
  const [enrichFields, setEnrichFields] = useState('both');

  useEffect(() => {
    if (selectedTable) {
      const result = Object.values(selectedTable).flat();
      const resultItems = Object.values(selectedTableItems).flat();
      setSelectedRowItems(resultItems);
      setSelectedRowKeys(result);
    }
  }, [selectedTable]);

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChangeTable,
  };

  const [openModalAfterBulkAdd, setOpenModalAfterBulkAdd] = useState(false);

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleAddContact = async (dataContact) => {
    try {
      const email = dataContact?.work_email || '';
      if (!email?.trim()) {
        notification.warning({
          message: `This contact does not have email`,
        });
        return;
      }
      setIsLoading(true);
      await handleSetDataContact({
        ...dataContact,
        email,
      });
      setIsLoading(false);
      setIsAddContactForm(true);
    } catch (error) {
      console.log(error);
      notification.error({
        message: `There was an error when adding contact to Bullhorn`,
      });
    }
  };

  const handleGenderSupportBar = (record, handleEnrichRecord = () => {}) => {
    const email = record?.work_email || '';
    const phone = record?.teaser?.phones?.[0]?.number || '';
    const isNotFoundEmail = record?.work_email === EMAIL_NOT_FOUND;
    return (
      <div style={{ display: 'flex' }} className="justify-center w-full">
        <div>
          {!isAddContactFormBulk && (
            <AddToBHButton
              isLoading={isLoading}
              handleAddContact={handleAddContact}
              record={record}
            />
          )}
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                    className="items-center"
                  >
                    <Button
                      disabled={!email || isNotFoundEmail}
                      title={
                        !email || isNotFoundEmail
                          ? 'Email not found'
                          : 'Add to Sequence'
                      }
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                        setCurrentContact(record);
                      }}
                    >
                      Add to sequence
                    </Button>
                    {email && !isNotFoundEmail && (
                      <a
                        href={`mailto:${email}`}
                        className="p-2 border rounded-md"
                      >
                        Send Email
                      </a>
                    )}
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    {!isNotFoundEmail &&
                      (email ? (
                        <div style={{ color: '#1677ff' }}>
                          {email}{' '}
                          <CopyOutlined
                            style={{ marginLeft: '10px' }}
                            onClick={() => {
                              navigator.clipboard.writeText(email),
                                notification.success({
                                  message: 'Copy To Clipboard success',
                                });
                            }}
                          />
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Button
                            type="default"
                            className="bg-cyan-600 font-semibold text-white"
                            onClick={() => handleCreateAccessEmail(record)}
                            loading={loadingContact?.includes(record.id)}
                          >
                            Unlock Email
                          </Button>
                          <Tooltip title="Cost 2 credits for accessing email">
                            <div className="flex items-center gap-1 font-semibold">
                              <Tag color="cyan" className="rounded-full">
                                2
                              </Tag>
                              <span className="text-xs">Credits</span>
                            </div>
                          </Tooltip>
                        </div>
                      ))}
                    {isNotFoundEmail && (
                      <div className="font-medium opacity-60">
                        No email found!
                      </div>
                    )}
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: email ? '70px' : '50px',
              }}
              className={clsx(
                'flex items-center justify-center customized-icon-button',
                email && !isNotFoundEmail && 'border-green-600',
                email && isNotFoundEmail && 'border-yellow-600'
              )}
              {...(email && {
                icon: <CheckCircleOutlined className={'text-green-600'} />,
              })}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="topLeft"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div className="w-full">
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  {phone && (
                    <div
                      style={{
                        marginTop: '5px',
                        fontSize: '15px',
                        fontWeight: '700',
                      }}
                    >
                      {phone}
                    </div>
                  )}
                  {/* {!phone && (
                    <div className="italic opacity-60">
                      Phone number not found
                    </div>
                  )} */}
                  <div style={{ marginTop: '12px' }}>
                    {!phone && (
                      <Tooltip title="Cost to unlock is 2 credits this will find Head office number or Mobile Number">
                        <div className="flex items-center gap-2">
                          <Button
                            type="default"
                            className="bg-cyan-600 font-semibold text-white"
                            // onClick={() => handleCreateAccessEmail(record)}
                            loading={loadingContact?.includes(record.id)}
                          >
                            Unlock Phone Number
                          </Button>
                          <div className="flex items-center gap-1 font-semibold">
                            <Tag color="cyan" className="rounded-full">
                              2
                            </Tag>
                            <span className="text-xs">Credits</span>
                          </div>
                        </div>
                      </Tooltip>
                    )}
                    {phone && (
                      <Button
                        title={'Calling to this contact'}
                        disabled={!phone}
                        type="primary"
                      >
                        <a href={`tel:${phone}`}>Call</a>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record?.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => {
                        const contactData = {
                          name: record?.name,
                          email: record?.work_email || '',
                          phone: record?.teaser?.phones?.[0]?.number ?? null,
                          linkedin_url: record?.links?.linkedin,
                          title: record?.current_title,
                          organization_name:
                            record?.company_details?.name ?? null,
                        };
                        setOpenExistingUserGroup(true);
                        setCurrentContact(contactData);
                        setBulkContacts([]);
                      }}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        {/* <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div> */}
        {/* <div>
          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  label: <div>Edit</div>,
                },
                {
                  key: '2',
                  label: <div>Delete</div>,
                },
              ],
            }}
            placement="top"
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div> */}
      </div>
    );
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (type === 'add' && el) {
      el?.classList?.add('expanded');
      el?.classList?.add('collapsed');
    } else if (type === 'remove' && el) {
      el?.classList?.remove('expanded');
      el?.classList?.remove('collapsed');
    }
  };

  const endProcess = () => {
    toggleLoading('remove');
    setEnrichLoading(false);
  };

  useEffect(() => {
    const handler = setTimeout(async () => {
      if (inputSearchRawValue) {
        const payload = {
          keyword: encodeURIComponent(inputSearchRawValue),
        };
        const { data } = await handleSearchRawData(payload);
        setLoadingSearch(true);
        setOptionRawSearch(data?.result?.data);
        setLoadingSearch(false);
      }
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [inputSearchRawValue]);

  const handleCreateAccessEmail = async (record) => {
    setEnrichLoading(true);
    const uuidData = record?.id;
    const payload = {
      data: [
        {
          linkedInUrl: record.linkedin_url,
          recordId: record?.id,
          companyDomain: record?.current_company_website,
          companyId:
            record?.work_experience?.[0]?.company_id ||
            record?.experiences?.[0]?.company_id,
          companyName:
            record?.work_experience?.[0]?.company ||
            record?.experiences?.[0]?.company,
          fullName: record?.name,
        },
      ],
    };
    // toggleLoading();
    await handleClayCreateSearchData(payload);
    setLoadingContact([...loadingContact, uuidData]);
  };

  const columns = [
    {
      title: 'Full Name',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
      render: (name, record) => {
        const { links, current_title, profile_pic } = record;
        return (
          <div className="flex items-center gap-2">
            <Avatar size={70} src={profile_pic} />
            <div className="flex flex-col">
              <span className="font-semibold text-base">{name}</span>
              <span
                title={current_title}
                className="font-medium text-gray-900 text-sm line-clamp-1 max-w-[14rem]"
              >
                {current_title}
              </span>
              <div className="flex items-center gap-2 pt-2">
                {links?.linkedin && (
                  <a href={links?.linkedin} target="_blank" rel="noreferrer">
                    <LinkedinOutlined className="text-xl font-medium text-[#0288d1] hover:text-[#0a66c2]" />
                  </a>
                )}
                {links?.facebook && (
                  <a href={links?.facebook} target="_blank" rel="noreferrer">
                    <FacebookOutlined className="text-xl font-medium text-[#3f51b5] hover:text-[#0a66c2]" />
                  </a>
                )}
                {links?.twitter && (
                  <a href={links?.twitter} target="_blank" rel="noreferrer">
                    <TwitterOutlined className="text-xl font-medium text-[#03a9f4] hover:text-[#0a66c2]" />
                  </a>
                )}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Company',
      dataIndex: 'company_details',
      key: 'company_details',
      width: '25%',
      render: (companyDetails, record) => (
        <CompanyDetailCompact
          companyDetails={companyDetails || record?.current_positions?.[0]}
          record={record}
          setSelectedCompany={setSelectedCompany}
        />
      ),
    },
    {
      title: 'Information',
      dataIndex: 'teaser',
      key: 'teaser',
      width: '25%',
      render: (teaser, record) => {
        return <ExperienceInfor record={record} />;
      },
    },
    {
      title: <div className="w-full flex justify-center">Action</div>,
      dataIndex: 'id',
      key: 'id',
      width: '25%',
      render: (id, record) => {
        const findRecord = accessEmailResults?.find(
          (it) => it.contactId === record.id
        );
        const emailCheck =
          (findRecord && findRecord?.enrichContact?.valid_work_email) ||
          record?.work_email
            ? findRecord?.enrichContact?.valid_work_email || record?.work_email
            : false;
        return (
          <div className="flex flex-col items-center justify-center gap-1 text-[#177AC6] font-semibold skip-action-column">
            {/* {record?.work_email &&
            record?.work_email !== '<EMAIL>' &&
            emailCheck ? (
              handleGenderSupportBar(record)
            ) : (record?.work_email &&
                record?.work_email === '<EMAIL>') ||
              (findRecord && emailCheck == false) ? (
              <div>
                <div style={{ color: 'red' }}> No email available </div>
              </div>
            ) : (
              <Button
                type="default"
                className="bg-cyan-600 font-semibold text-white"
                onClick={() => handleCreateAccessEmail(record)}
                loading={loadingContact?.includes(record.id)}
              >
                Unlock Email
              </Button>
            )} */}
            {handleGenderSupportBar(record)}
            {/* <div className="flex w-full justify-end">
            {expandList.includes(id) ? 'View less' : 'View more'}
          </div> */}
          </div>
        );
      },
    },
  ];

  const companyColumns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (name, record) => {
        const { links } = record;
        return (
          <CompanyDetailCompact
            companyDetails={{
              id: record?.id,
              name: record?.name,
              links,
              logo_url: record?.logo_url,
            }}
            record={record}
          />
        );
      },
    },
    {
      title: 'Industry',
      dataIndex: 'industry',
      key: 'industry',
      width: '20%',
      render: (industry, record) => {
        return (
          <div className="font-semibold font-lg text-cyan-600">{industry}</div>
        );
      },
    },
    {
      title: 'Address',
      dataIndex: ' best_address',
      key: ' best_address',
      width: '20%',
      render: (bestAddress, record) => {
        return (
          <div
            className="font-medium line-clamp-1"
            title={record?.best_address}
          >
            {record?.best_address}
          </div>
        );
      },
    },
    {
      title: 'Summary',
      dataIndex: 'summary',
      key: 'summary',
      width: '20%',
      render: (summary, record) => (
        <div className="font-medium line-clamp-2" title={summary}>
          {summary}
        </div>
      ),
    },
    {
      title: <div className="w-full flex justify-center">Action</div>,
      dataIndex: 'id',
      key: 'id',
      width: '20%',
      render: (id, record) => (
        <div className="font-medium flex justify-center">
          {enrichLoading && loadingContact?.includes(record?.id) ? (
            <LoadingOutlined spin />
          ) : (
            <>
              <Button
                onClick={(evt) => {
                  evt.stopPropagation();
                  setValue('clientCorporation.name', record?.name);
                  setValue(
                    'clientCorporation.companyAddress',
                    record?.best_address
                  );
                  setShowCompany(true);
                }}
                className="flex items-center gap-2 !rounded-md border border-[#e2642a]"
              >
                <div>Add new</div>
                <Image
                  preview={false}
                  src={logo}
                  style={{ width: '20px', height: '20px' }}
                />
              </Button>
            </>
          )}
        </div>
      ),
    },
  ];

  const handleClearFilter = () => {
    setFilter(initialFilter);
    setRawFilter(initialFilter);
    setDataSource([]);
    setCompanyDataSource([]);
    setPagination(initialPagination);
  };

  const isOpenClearAll = Object.keys(filter).some(
    (key) => filter[key] !== initialFilter[key]
  );

  const handleGetListSavedContacts = async () => {
    setLoadingGetSavedSearchData(true);
    const { data } = await getSavedSearch(
      selectedSearch === SEARCH.CONTACT
        ? 'SAVED_CONTACT_SEARCH'
        : 'SAVED_COMPANY_SEARCH'
    );
    setListSavedData(data?.result?.data);
    setListSavedDataDefault(data?.result?.data);
    setLoadingGetSavedSearchData(false);
  };

  useEffect(() => {
    setListSavedData([]);
    setListSavedDataDefault([]);
    handleGetListSavedContacts();
    setFilter(initialFilter);
  }, [selectedSearch]);

  useEffect(() => {
    return () => {
      setCursorToken('');
    };
  }, []);

  const handleSaveSearch = async (searchName, cursor) => {
    let filterFields = filter;

    const payload = {
      searchName: searchName,
      searchType:
        selectedSearch === SEARCH.CONTACT
          ? 'SAVED_CONTACT_SEARCH'
          : 'SAVED_COMPANY_SEARCH',
      filterFields: filterFields,
    };

    const data = await createSavedSearch(payload);
    notification.success({
      message: 'Create Saved search success !!!',
    });
    handleGetListSavedContacts();
    return data;
  };

  const handleSubmitContactSearch = async (
    cursor = '',
    keyword = '',
    pagination = {
      pageSize: 10,
      currentPage: 1,
    }
  ) => {
    try {
      setLoading(true);
      const payload = {
        // pagination
        limit: 10,
        ...(pagination && {
          pageSize: pagination.pageSize,
          currentPage: pagination.currentPage,
        }),
        // filter factors
        ...((filter?.keywords?.trim() || keyword) && {
          keywords: filter?.keywords || keyword,
        }),
        ...(!cursor && { cursor: cursorToken }),
        ...(filter?.companyIds && {
          companyIds: [...filter?.companyIds].filter((item) => item?.trim()),
          isCurrentCompany: filter?.isCurrentCompany ? true : false,
        }),
        ...(filter?.location?.length > 0 && {
          location: filter?.locationIds,
        }),
        ...(filter?.radius && {
          location_within_area: filter?.radius || 0,
        }),
        ...(filter?.industry?.length > 0 && { industry: filter?.industryIds }),
        ...(filter?.skills?.length > 0 && { skills: filter?.skillIds }),
        ...(filter?.yearOfExperience?.length > 0 && {
          yearExperiences: {
            min: filter?.yearOfExperience[0],
            max: filter?.yearOfExperience[1],
          },
        }),
        ...(filter?.jobTitle?.length > 0 && {
          jobTitleIds: filter?.jobTitleIds,
        }),
        ...(filter?.department?.length > 0 && {
          departmentIds: filter?.departmentIds,
        }),
        ...(filter?.contactName && {
          first_name: filter?.contactName?.split(/\s+/).slice(0, -1).join(' '),
          last_name: filter?.contactName?.split(/\s+/).pop(),
        }),
        // Search on Apollo
        ...(filter?.changedJobsWithin > 0 && {
          changedJobsWithin: filter?.changedJobsWithin,
        }),
        ...(filter?.technologies?.length > 0 && {
          technologyIds: filter?.technologyIds,
        }),
        ...(filter?.websiteUrl?.trim() && { website: filter?.websiteUrl }),
        ...(filter?.sequence?.trim() && { sequence: filter?.sequence }),
        ...(!isEmpty(filter?.companySize) && {
          companySize: {
            min: filter?.companySize?.[0],
            max: filter?.companySize?.[1],
          },
        }),

        ...(filter?.revenue?.length > 0 && {
          revenue: {
            min: filter?.revenue?.[0],
            max: filter?.revenue?.[1],
          },
          revenueStatus: filter?.revenueStatus,
          revenueStatusItem: filter?.revenueStatusItem,
        }),

        ...(filter?.funding?.length > 0 && {
          funding: {
            min: filter?.funding?.[0],
            max: filter?.funding?.[1],
          },
          organizationLatestFundingStageCd:
            filter?.organizationLatestFundingStageCd,
          fundingStatus: filter?.fundingStatus,
        }),
      };
      const { data } = await searchPeople(payload);
      const listIds = await data?.result?.data?.items.map((item) => item.id);
      // setOpenSSE(true);
      const accessEmailResults = await getClayAccessEmailResults({
        contactIds: listIds,
      });
      console.log(
        'accessEmailResults?.data?.result?.data',
        accessEmailResults?.data?.result?.data
      );
      setAccessEmailResults(
        accessEmailResults?.data?.result?.data?.filter(
          (item) =>
            item?.enrichContact?.valid_work_email ||
            item?.enrichContact?.valid_work_email_1 ||
            item?.enrichContact?.email_1
        )
      );
      const mergedData = data?.result?.data?.items.map((item) => {
        const foundContact = accessEmailResults?.data?.result?.data?.find(
          (it) => it.contactId === item.id
        );

        return {
          ...item,
          work_email: foundContact
            ? foundContact?.enrichContact?.valid_work_email ||
              foundContact?.enrichContact?.valid_work_email_1 ||
              foundContact?.enrichContact?.email_1
            : undefined,
        };
      });

      if (data?.result?.data?.items?.length > 0) {
        const newData = migrateContactData(mergedData);
        setDataSource([...newData]);
      } else {
        setDataSource([]);
        setPagination(initialPagination);
      }

      if (data?.result?.data?.cursor && !cursorToken) {
        setCursorToken(data?.result?.data?.cursor);
      }

      if (data?.result?.data?.paging) {
        const totalCount = data?.result?.data?.paging?.total_count;
        setPagination({
          ...pagination,
          totalCount,
        });
      }
      window.scrollTo(0, 0);
      setLoading(false);
    } catch (error) {
      notification.success({
        message: 'Search created failed! Try again later!',
      });
      console.error(error);
      setDataSource([]);
      setLoading(false);
    }
  };

  const handleSubmitCompanySearch = async (
    cursor = null,
    keyword = '',
    pagination = {
      pageSize: 10,
      currentPage: 1,
    }
  ) => {
    setLoading(true);
    try {
      const payload = {
        type: 'classic',
        category: 'companies',
        // pagination
        ...(pagination && {
          pageSize: pagination.pageSize,
          currentPage: pagination.currentPage,
        }),
        // filter factors
        ...((filter?.companyName || keyword) && {
          keywords:
            filter?.companyName[filter?.companyName?.length - 1] || keyword,
        }),
        ...(!isEmpty(filter?.companySize) && {
          companySize: {
            min: filter?.companySize?.[0],
            max: filter?.companySize?.[1],
          },
        }),
        ...(filter?.location?.length > 0 && {
          location: filter?.locationIds,
        }),
        ...(filter?.radius && {
          location_within_area: filter?.radius || 0,
        }),
        ...(filter?.industry?.length > 0 && { industry: filter?.industryIds }),
        limit: 10,
        ...(!cursor && { cursor: cursorToken }),
        // Search on Apollo
        ...(filter?.changedJobsWithin > 0 && {
          changedJobsWithin: filter?.changedJobsWithin,
        }),
        // ...(filter?.technologies?.length > 0 && {
        //   technologyIds: filter?.technologyIds,
        // }),
        ...(filter?.websiteUrl?.trim() && { website: filter?.websiteUrl }),
        ...(filter?.sequence?.trim() && { sequence: filter?.sequence }),
        ...(!isEmpty(filter?.companySize) && {
          companySize: {
            min: filter?.companySize?.[0],
            max: filter?.companySize?.[1],
          },
        }),
        ...(filter?.revenue?.length > 0 && {
          revenue: {
            min: filter?.revenue?.[0],
            max: filter?.revenue?.[1],
          },
          revenueStatus: filter?.revenueStatus,
          revenueStatusItem: filter?.revenueStatusItem,
        }),
        ...(filter?.funding?.length > 0 && {
          funding: {
            min: filter?.funding?.[0],
            max: filter?.funding?.[1],
          },
          organizationLatestFundingStageCd:
            filter?.organizationLatestFundingStageCd,
          fundingStatus: filter?.fundingStatus,
        }),
      };
      const { data } = await getListCompanyLinkedin(payload);
      if (data?.result?.data?.items?.length > 0) {
        const companyList = data?.result?.data?.items;
        const newDataSource = migrateCompanyData(companyList);
        setCompanyDataSource([...newDataSource]);
      } else {
        setCompanyDataSource([]);
        setCompanyPagination(initialPagination);
      }

      if (data?.result?.data?.cursor && !cursorToken) {
        setCursorToken(data?.result?.data?.cursor);
      }

      if (data?.result?.data?.paging) {
        const totalCount = data?.result?.data?.paging?.total_count;
        setCompanyPagination({
          ...pagination,
          totalCount,
        });
      }
      setLoading(false);
    } catch (err) {
      console.error(err);
      setCompanyDataSource([]);
      setCompanyPagination({
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
      });
      setLoading(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const handleSubmitSearch = async (cursor = '', keyword = '') => {
    const TRIGGER_RESET_CURSOR = 'TRIGGER_RESET_CURSOR';
    setCursorToken('');
    return selectedSearch === SEARCH.CONTACT
      ? handleSubmitContactSearch(TRIGGER_RESET_CURSOR, keyword)
      : handleSubmitCompanySearch(TRIGGER_RESET_CURSOR, keyword);
  };

  const checkHenley = userToSet?.organization?.name === HENLEY;

  const handleSubmitAddContact = async () => {
    setIsSubmitForm(true);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      categories,
      company,
    } = getValues()?.clientContact;
    if (!companyId) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      setIsSubmitForm(false);
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Address is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (checkHenley ? skills.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (categories.length === 0) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    }

    // Based on format of BH
    const rawInformation = {
      ...(getValues()?.rawInformation?.[0] || {}),
      organization_name: company,
      first_name: getValues()?.clientContact?.firstName,
      last_name: getValues()?.clientContact?.surename,
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      owner_id: getValues()?.clientContact.consultantId,
    };

    if (
      address?.length > 100 ||
      getValues()?.clientContact?.address2?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      // lastName: getValues()?.clientContact?.lastname,
      lastName: getValues()?.clientContact?.surename,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        address2: getValues()?.clientContact?.address2,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      categories: {
        replaceAll: categories?.map((obj) => obj.value),
      },
      skills: {
        replaceAll: skills?.map((obj) => obj.value),
      },
      customText1: getValues()?.clientContact.linkedProfileUrl,
      rawInformation,
    };

    if (
      getValues()?.clientContact?.address > 100 ||
      getValues()?.clientContact?.address2 > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const newContactPayloadCleaned = cleanPayload(payload);
    let data;
    try {
      data = await insertBullhorn(newContactPayloadCleaned);
    } catch (err) {
      notification.error(err);
      setIsSubmitForm(false);
    }
    // handleContactSearch('', getValues().companyId);
    setIsSubmitForm(false);
    setIsLoading(false);
    setIsAddContactForm(false);
    setCurrentContact(null);
    notification.success({
      message: `Contact has been added successfully!`,
    });
  };

  const handleBulkAddContact = async () => {
    await handleSubmitBulkAddContact();
  };

  const handleSubmitBulkAddContact = async () => {
    setLoadingBulkData(true);
    if (!getValues()?.clientContactBulk?.companyId) {
      notification.error({
        message: 'Company is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.status) {
      notification.error({
        message: 'Status is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.industries) {
      notification.error({
        message: 'Industries is required.',
      });
      return;
    }
    if (checkHenley && !getValues()?.clientContactBulk?.categories) {
      notification.error({
        message: 'Categories is required.',
      });
      return;
    }
    const checkListContact = selectedRowItems.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );
    const { companyId, industries, skills, status, categories } =
      getValues()?.clientContactBulk;

    const additionalField = {
      clientCorporation: {
        id: companyId,
      },
      businessSectors: {
        replaceAll: industries.map((obj) => obj.value),
      },
      skills: {
        replaceAll: skills?.map((obj) => obj.value),
      },
      categories: {
        replaceAll: categories?.map((obj) => obj.value),
      },
    };

    const contactInformation = checkListContact?.map((item) => {
      return {
        linkedInUrl: item?.linkedin_url,
        companyUnipileId: 'random',
        recordId: item?.id,
        firstName: item?.first_name,
        lastName: item?.last_name,
        email: item?.work_email,
        occupation: item?.headline,
        address: {
          address1: item?.location,
        },
        customText1: item?.linkedin_url,
        name: item?.name,
        companyDomain: item?.current_company_website,
        companyId:
          item?.work_experience?.[0]?.company_id ||
          item?.experiences?.[0]?.company_id,
        companyName:
          item?.work_experience?.[0]?.company ||
          item?.experiences?.[0]?.company,
        owner: {
          id: userToSet?.user?.consultantId || userToSet?.consultantId,
        },
        status: status?.label,
      };
    });

    const bulkAddContact = {
      additionalField,
      contactInformation,
      enrichFields: getEnrichFields(enrichFields),
    };

    const data = await bulkAddToBullhorn(bulkAddContact);
    console.log(data);
    // notification.info({
    //   message: `Adding ${selectedRowKeys?.length} contacts to Bullhorn...`,
    //   description: (
    //     <div className="grid grid-cols-10 items-start w-full text-sm">
    //       <span className="font-semibold text-xs col-span-3">Please note:</span>
    //       <span className="col-span-7 flex justify-start">
    //         this may take up to 1 Minute per contact, we are fetching the most
    //         accurate data any contact that is already on Bullhorn will not be
    //         added.
    //       </span>
    //     </div>
    //   ),
    //   // placement: 'top',
    // });

    setOpenModalAfterBulkAdd(true);

    setIsAddContactFormBulk(false);
    setLoadingBulkData(true);
    // const checkListContact = dataSource.filter((item) =>
    //   selectedRowKeys.includes(item?.id)
    // );
    // const dataToInsert = [];
    // const unretrievableContactIds = [];
    // const checkListContactIds = [];

    // handleGenerateNotificationBulkAddContact(
    //   arrContact,
    //   selectedRowKeys,
    //   unretrievableContactIds
    // );
  };

  const handleResetFormAddContact = async () => {
    setValue('clientContact.namePrefixSelect', null);
    setValue('clientContact.namePrefix', null);
    setValue('clientContact.firstName', null);
    setValue('clientContact.middleName', null);
    setValue('clientContact.surename', null);
    setValue('clientContact.consultantId', null);
    setValue('clientContact.consultant', null);
    setValue('clientContact.consultantSelect', null);
    setValue('clientContact.statusSelect', null);
    setValue('clientContact.type', null);
    setValue('clientContact.secondaryOwnerSelect', null);
    setValue('clientContact.companyId', null);
    setValue('clientContact.department', null);
    setValue('clientContact.jobTitle', null);
    setValue('clientContact.workEmail', null);
    setValue('clientContact.personalEmail', null);
    setValue('clientContact.workPhone', null);
    setValue('clientContact.mobilePhone', null);
    setValue('clientContact.otherPhone', null);
    setValue('clientContact.fax', null);
    setValue('clientContact.stateId', null);
    setValue('clientContact.state', null);
    setValue('clientContact.county', null);
    setValue('clientContact.countySelect', null);
    setValue('clientContact.address', null);
    setValue('clientContact.city', null);
    setValue('clientContact.zip', null);
    setValue('clientContact.industries', []);
    setValue('clientContact.generalCommets', null);
    setValue('clientContact.referredById', null);
  };

  const refreshPageState = () => {
    setSelectedRowKeys([]);
    setExpandList([]);
    // setFilter(initialFilter);
    // setRawFilter(initialFilter);
    // setPagination(initialFilter);
    // setCompanyPagination(initialFilter);
  };

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    message.success(`Access 1 contact successfully!`);
    if (loadingContact.includes(enrichedDataObject.record_id)) {
      const updatedLoadingItems = loadingContact.filter(
        (item) => item !== enrichedDataObject.record_id
      );
      setLoadingContact(updatedLoadingItems);
      const newDataSource = dataSource.map((item) => {
        if (item.id === enrichedDataObject.record_id) {
          return {
            ...item,
            work_email:
              enrichedDataObject?.valid_work_email ||
              '<EMAIL>',
          };
        }
        return item;
      });
      setDataSource(newDataSource);
    }
    endProcess();
  };

  const handleBulkEnrich = async () => {
    showBulkEnrichModal();
  };

  const handleBulkAddToSequence = async () => {
    const accessedContacts = dataSource
      .filter((item) => selectedRowKeys.includes(item.id))
      .map((item) => ({
        email: item?.work_email || '',
        name: item?.name,
        firstName: item?.first_name || item?.name,
        phone: item?.teaser?.phones?.[0]?.number ?? null,
        linkedin_url: item?.links?.linkedin,
        title: item?.current_title,
        organization_name: item?.company_details?.name ?? null,
      }));
    const isAllowAdding = accessedContacts?.every((item) => item?.email);

    if (!isAllowAdding) {
      notification.error({
        description:
          'Please access email for all contacts before adding to Sequence',
      });
      return;
    }

    setOpenExistingSequence(true);
  };

  const handleEditSavedSearch = async (e) => {
    const payload = {
      searchName: searchNameToSave,
    };
    setLoadingEditSearchName(true);

    const data = await updateSavedSearch(idSearchNameToSave, payload);
    await handleGetListSavedContacts();
    setLoadingEditSearchName(false);
    setOpenEditSavedSearch(false);
    notification.success({
      message: 'Edit saved search successfully !!!',
    });
  };

  const handleDeleteSavedSearch = async (id) => {
    const data = await deleteSavedSearch(id);
    notification.success({
      message: 'Delete saved search successfully !!!',
    });
    await handleGetListSavedContacts();
  };

  const handleSearchByKeywords = async (record) => {
    if (record?.type === 'USER') {
      setSelectedSearch(SEARCH.CONTACT);
      await setFilter({ ...filter, keywords: record?.name });
      await handleSubmitContactSearch(null, record?.name);
    } else {
      setSelectedSearch(SEARCH.COMPANY);
      await setFilter({ ...filter, keywords: record?.name });
      await handleSubmitCompanySearch(null, record?.name);
    }
  };

  const renderNotification = () => {
    return (
      <>
        <div className="col-span-8 w-full flex flex-col items-center justify-start h-full bg-white p-4 border border-gray-200 rounded-md">
          <div className="flex items-center justify-end w-full ">
            <Button
              type="primary"
              icon={<CloseOutlined />}
              onClick={() => {
                setOpenModalAfterBulkAdd(false);
                setSelectedRowKeys([]);
              }}
            >
              Close
            </Button>
          </div>
          <div class="flex flex-col gap-1 w-full items-center justify-center pb-3 px-36 items-center justify-start h-full">
            <div className="flex items-center gap-2">
              <CheckCircleOutlined className="text-2xl text-cyan-600" />
              <span class="text-lg font-medium flex items-center gap-1">
                You have successfully added{' '}
                <Tag className="text-xl font-medium" color="cyan">
                  {selectedRowKeys?.length}
                </Tag>{' '}
                contacts to Bullhorn
              </span>
            </div>
            <p class="mt-1 opacity-70 text-sm font-medium italic">
              Please note this may take up to one minute to add the contacts to
              Bullhorn however you can close this screen and they will be added
              in the background.
            </p>
          </div>
          <hr class="w-full h-1 bg-cyan-700 border-none" />
          <div className="pt-10 font-semibold">
            Zileo Contact gives you access to 20+ premium data sources. This
            guarantees a maximized finding rate for emails and mobile phone
            numbers.
          </div>
          <div
            class="flex-col gap-2"
            style={{ display: 'flex !important', paddingBottom: '20px' }}
          >
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/RocketReach.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  RocketReach
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/apollo.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  Apollo
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/ContactOut.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  ContactOut
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/Datagma.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  Datagma
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/People-Data-Labs.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  People Data Labs
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/Enrich.so_.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  Enrich.so
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/Prospeo.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  Prospeo
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <img
                  src="https://bettercontact.rocks/wp-content/uploads/2024/09/Hunter.svg"
                  className="brxe-image pricing__discount__logo css-filter size-full"
                  alt="RocketReach Logo"
                  decoding="async"
                  data-type="string"
                  style={{
                    fontSize: '20px',
                    width: '50px',
                    height: '50px',
                    objectFit: 'contain',
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                />
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  RocketReach
                </span>
              </div>
            </div>
            <div
              class="flex items-center gap-2"
              style={{
                float: 'left',
                width: '33%',
                marginTop: '10px',
                justifyContent: 'center',
              }}
            >
              <Checkbox checked={true} />
              <div
                class="brxe-div pricing__discount__logo-box"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '70%',
                }}
              >
                <span
                  style={{
                    marginLeft: '10px',
                    fontWeight: 500,
                  }}
                >
                  And more...
                </span>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  const renderBulkAddContactsToBH = () => {
    return (
      <div className="customize-contact-form col-span-8 w-full bg-white px-4 border border-gray-200 rounded-md">
        <Form layout="vertical" className="w-full justify-start h-full ">
          <div className="w-full text-xl pl-6 pb-3 pt-5 font-semibold flex items-center justify-between gap-2">
            <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
            <Button
              type="primary"
              icon={<CloseOutlined />}
              onClick={() => {
                setIsAddContactFormBulk(false);
                handleResetFormAddContact();
              }}
            >
              Close
            </Button>
          </div>
          <BullhornBulkAddContactModal
            control={control}
            setValue={setValue}
            getValues={getValues}
            dataContacts={selectedRowItems.filter((item) =>
              selectedRowKeys.includes(item.id)
            )}
          />
          <div className="pl-6 left-0 bottom-0 w-full pb-4">
            <div
              className="flex gap-4 mr-8"
              style={{ float: 'right', paddingBottom: '10px' }}
            >
              <Button
                onClick={() => {
                  setIsAddContactFormBulk(false);
                  handleResetFormAddContact();
                }}
                className={`bg-[#BEDAFD33]  `}
              >
                Cancel
              </Button>
              <Button
                htmlType="button"
                onClick={handleBulkAddContact}
                type="primary"
                className={``}
                loading={false}
              >
                Save
              </Button>
            </div>
          </div>
          <div style={{ clear: 'both' }}></div>
        </Form>
      </div>
    );
  };

  return (
    <>
      {loadingContact.length > 0 && (
        <EventSourceRender
          updateRecentlyData={updateRecentlyData}
          sseName="ENRICH_CONTACT_DATA"
          sseEndpoint="sse/enrich-data"
        />
      )}
      {selectedContact && (
        <ContactDetail
          handleGenderSupportBar={handleGenderSupportBar}
          selectedContact={selectedContact}
          setSelectedContact={setSelectedContact}
          setDataSource={setDataSource}
          dataSource={dataSource}
          savedCompany={savedCompany}
          setSelectedCompany={setSelectedCompany}
          setSavedCompany={setSavedCompany}
        />
      )}
      {selectedCompany && (
        <CompanyDetail
          selectedCompany={selectedCompany}
          setSelectedCompany={setSelectedCompany}
          handleGenderSupportBar={handleGenderSupportBar}
          selectedContact={selectedContact}
          setSelectedContact={setSelectedContact}
          setDataSource={setDataSource}
          dataSource={dataSource}
          setSavedCompany={setSavedCompany}
          savedCompany={savedCompany}
        />
      )}
      {!selectedContact && !selectedCompany && (
        <div className="grid grid-cols-10 gap-4 w-full flex items-start contact-finder-v2-root-container">
          <div className="w-full flex items-center justify-center col-span-2">
            <Segmented
              disabled={loading}
              className="customized-segmented-contact-finder-v2"
              options={[SEARCH.CONTACT, SEARCH.COMPANY]}
              onChange={(value) => {
                setSelectedSearch(value);
                refreshPageState();
              }}
              value={selectedSearch}
            />
          </div>
          <div className="w-full h-full flex items-center col-span-7 justify-center">
            <AutoComplete
              placeholder="e.g. LinkedIn URL, Job Title, Industry, Revenue, Number of Employees, Years of Experience, etc."
              className="w-full"
              rootClassName="!rounded-none search-in-contact-finder-v2-container"
              value={inputSearchRawValue}
              suffixIcon={loadingSearch ? <Spin /> : ''}
              disabled={loadingSearch}
              onSelect={(e) => {
                const item = optionRawSearch?.find((item) => item.id === e);
                setInputSearchRawValue(item.name);
                handleSearchByKeywords(item);
              }}
              size="large"
              onSearch={(e) => setInputSearchRawValue(e)}
              options={optionRawSearch.map((item) => ({
                value: item?.id,
                label: (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ fontWeight: '500', fontSize: '15px' }}>
                        {item?.name}
                      </div>
                      <div
                        style={{
                          fontWeight: '300',
                          fontSize: '13px',
                          marginLeft: '10px',
                          width: '600px',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {item?.subTitle}
                      </div>
                    </div>
                    <div>
                      <img
                        style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                        }}
                        src={item?.image}
                      />
                    </div>
                  </div>
                ),
              }))}
            >
              <Input.Search
                size="middle"
                enterButton={
                  <Button
                    type="default"
                    icon={<SearchOutlined className="font-medium text-white" />}
                    onClick={(e) => {
                      if (selectedSearch == SEARCH.CONTACT) {
                        handleSubmitContactSearch(null, inputSearchRawValue);
                      } else {
                        handleSubmitCompanySearch(null, inputSearchRawValue);
                      }
                    }}
                  ></Button>
                }
              />
            </AutoComplete>
          </div>
          {selectedSearch === SEARCH.CONTACT && (
            <div className="w-full flex items-start justify-center col-span-1">
              <Dropdown
                disabled={selectedRowKeys?.length === 0}
                className="animated fadeInDownBig"
                placement="bottom"
                arrow
                menu={{
                  items: [
                    {
                      key: 'bulk-add-to-bullhorn',
                      label: (
                        <a className="Montserrat flex gap-2 items-center py-2">
                          <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
                        </a>
                      ),
                      onClick: () => {
                        confirm({
                          title: 'Need your action to Attention',
                          // icon: <ExclamationCircleFilled />,
                          className: 'min-w-[30rem]',
                          content: (
                            <div className="flex flex-col gap-2 py-3">
                              <div className="text-sm font-medium">
                                Please click below which you would like to
                                unlock:
                              </div>
                              <div className="flex gap-2 w-full justify-center items-center pt-1">
                                <RestyledRadioGroup
                                  block
                                  options={[
                                    {
                                      label: 'Emails',
                                      value: 'email',
                                    },
                                    {
                                      label: 'Phone Number',
                                      value: 'phone',
                                    },
                                    {
                                      label: 'Both',
                                      value: 'both',
                                    },
                                  ]}
                                  defaultValue="both"
                                  optionType="button"
                                  buttonStyle="solid"
                                  onChange={(e) => {
                                    setEnrichFields(e.target.value);
                                  }}
                                />
                              </div>
                            </div>
                          ),
                          onOk: () => {
                            setIsAddContactFormBulk(true);
                          },
                          okText: 'Add',
                          cancelText: 'Close',
                        });
                      },
                    },
                    {
                      key: 'bulk-add-contact-lists',
                      label: (
                        <a className="Montserrat flex gap-2 items-center py-2">
                          <span>{COMMON_STRINGS.BULK_ADD_TO_CONTACT_LIST}</span>
                        </a>
                      ),
                      onClick: async () => {
                        confirm({
                          title: 'Need your action to Attention',
                          // icon: <ExclamationCircleFilled />,
                          className: 'min-w-[30rem]',
                          content: (
                            <div className="flex flex-col gap-2 py-3">
                              <div className="text-sm font-medium">
                                Please click below which you would like to
                                unlock:
                              </div>
                              <div className="flex gap-2 w-full justify-center items-center pt-1">
                                <RestyledRadioGroup
                                  block
                                  options={[
                                    {
                                      label: 'Both',
                                      value: 'both',
                                    },
                                    {
                                      label: 'Email',
                                      value: 'email',
                                    },
                                    {
                                      label: 'Phone',
                                      value: 'phone',
                                    },
                                  ]}
                                  defaultValue="both"
                                  optionType="button"
                                  buttonStyle="solid"
                                  onChange={(e) => {
                                    setEnrichFields(e.target.value);
                                  }}
                                />
                              </div>
                            </div>
                          ),
                          onOk: () => {
                            const accessedContacts = selectedRowItems
                              .filter((item) =>
                                selectedRowKeys.includes(item.id)
                              )
                              .map((item) => ({
                                name: item?.name,
                                email: item?.work_email || '',
                                phone:
                                  item?.teaser?.phones?.[0]?.number ?? null,
                                linkedin_url: item?.links?.linkedin,
                                title: item?.current_title,
                                organization_name:
                                  item?.company_details?.name ?? null,
                              }));
                            setOpenExistingUserGroup(true);
                            setCurrentContact(null);
                            setBulkContacts([...accessedContacts]);
                          },
                          okText: 'Add',
                          cancelText: 'Close',
                        });
                      },
                    },
                    // {
                    //   key: 'bulk-add-to-sequence',
                    //   label: (
                    //     <a className="Montserrat flex gap-2 items-center py-2">
                    //       <span>{COMMON_STRINGS.BULK_ADD_TO_SEQUENCE}</span>
                    //     </a>
                    //   ),
                    //   onClick: handleBulkAddToSequence,
                    // },
                    // {
                    //   key: 'bulk-enrich',
                    //   label: (
                    //     <a className="Montserrat flex gap-2 items-center py-2">
                    //       <span>{COMMON_STRINGS.BULK_ENRICH}</span>
                    //     </a>
                    //   ),
                    //   onClick: handleBulkEnrich,
                    // },
                  ],
                }}
              >
                <Space>
                  <Button
                    disabled={selectedRowKeys?.length === 0 || enrichLoading}
                    type="default"
                    className="!border-[#b2b8be] flex gap-2 items-center bg-cyan-600 font-semibold text-white"
                  >
                    <p className="Montserrat">
                      {`${selectedRowKeys?.length} Selected`}
                    </p>
                    <DownOutlined className="font-semibold" />
                  </Button>
                </Space>
              </Dropdown>
            </div>
          )}
          <div className="col-span-2 flex items-start h-full pr-2">
            <div className="w-full flex flex-col gap-4 contact-finder-v2-filter-container">
              <Collapse
                expandIconPosition="end"
                defaultActiveKey={['keywords']}
                items={[
                  {
                    key: 'keywords',
                    label: (
                      <div className="flex items-center gap-2">
                        <FileSearchOutlined className="text-lg" />
                        <span>Saved Searches</span>(
                        {loadingGetSavedSearchData ? (
                          <Spin size="small"></Spin>
                        ) : (
                          listSavedData.length
                        )}
                        )
                      </div>
                    ),
                    children: (
                      <div>
                        <div className="flex items-center gap-2">
                          <Input
                            placeholder="Type here to search ..."
                            onChange={(e) => {
                              const searchText = e.target.value;
                              const newOptions =
                                listSavedDataDefault?.filter((v) =>
                                  v?.searchName
                                    ?.toString()
                                    .toLocaleLowerCase()
                                    .includes(searchText.toLocaleLowerCase())
                                ) ?? [];
                              setListSavedData(newOptions);
                            }}
                          />
                        </div>
                        <div
                          style={{
                            maxHeight: '200px',
                            overflowY: 'scroll',
                          }}
                        >
                          {listSavedData?.map((item) => (
                            <div
                              style={{
                                cursor: 'pointer',
                                width: '100%',
                                height: '30px',
                                display: 'flex',
                                alignItems: 'center',
                                marginTop: '10px',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <Dropdown
                                  rootClassName="font-Montserrat"
                                  menu={{
                                    items: [
                                      {
                                        label: 'Edit Name',
                                        key: 'edit_name',
                                        icon: <CopyOutlined />,
                                      },
                                      {
                                        label: 'Delete',
                                        key: 'delete',
                                        icon: <DeleteOutlined />,
                                      },
                                    ],
                                    onClick: (e) => {
                                      // e?.stopPropagation();
                                      if (e.key === 'edit_name') {
                                        setSearchNameToSave(item?.searchName);
                                        setOpenEditSavedSearch(true);
                                        setIdSearchNameToSave(item?.id);
                                      }

                                      if (e.key === 'delete') {
                                        setIdSearchNameToDelete(item?.id);
                                        handleDeleteSavedSearch(item?.id);
                                      }
                                    },
                                  }}
                                  disabled={loadingSaveAsASequence}
                                >
                                  <MoreOutlined
                                    style={{
                                      fontSize: '20px',
                                      cursor: 'pointer',
                                    }}
                                  />
                                </Dropdown>
                                <UserOutlined style={{ marginLeft: '10px' }} />
                                <span
                                  onClick={() => {
                                    setFilter({
                                      ...filter,
                                      ...item?.filterFields,
                                    });
                                    console.log({
                                      ...filter,
                                      ...item?.filterFields,
                                    });
                                  }}
                                  style={{ marginLeft: '10px' }}
                                >
                                  {item?.searchName}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ),
                  },
                ]}
              />
              <div className="flex items-center justify-between w-full">
                <span className="text-[#626E83] font-semibold">
                  Search Filters
                </span>
                {isOpenClearAll && (
                  <Button
                    onClick={handleClearFilter}
                    type="link"
                    className="text-[#177AC6] font-semibold"
                  >
                    Clear All
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-1 flex-wrap">
                {selectedSearch === SEARCH.CONTACT && filter?.contactName && (
                  <Tag
                    color="cyan"
                    icon={<UserOutlined />}
                    closable={true}
                    onClose={() => setFilter({ ...filter, contactName: '' })}
                  >
                    {filter?.contactName}
                  </Tag>
                )}

                {filter?.companyName && (
                  <Tag
                    color="cyan"
                    icon={<BankOutlined />}
                    closable={true}
                    onClose={() =>
                      setFilter({ ...filter, companyName: '', companyIds: '' })
                    }
                  >
                    {filter?.companyName?.join(', ')}
                  </Tag>
                )}
                {selectedSearch === SEARCH.CONTACT && filter?.keywords && (
                  <Tag
                    color="magenta"
                    icon={<FileSearchOutlined />}
                    closable={true}
                    onClose={() => setFilter({ ...filter, keywords: '' })}
                  >
                    {filter?.keywords}
                  </Tag>
                )}

                {/* {filter?.technologies?.length > 0 &&
                  filter?.technologies?.map((item) => (
                    <Tag
                      color="red"
                      icon={<LaptopOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.technologies?.indexOf(item);

                        const newTechnologies = [...filter?.technologies];
                        const newTechnologyIds = [...filter?.technologyIds];

                        newTechnologies.splice(index, 1);
                        newTechnologyIds.splice(index, 1);

                        setFilter({
                          ...filter,
                          technologies: newTechnologies,
                          technologyIds: newTechnologyIds,
                        });
                      }}
                    >
                      {item}
                    </Tag>
                  ))} */}

                {filter?.revenue?.length > 0 && (
                  <Tag
                    color="volcano"
                    icon={<BarChartOutlined />}
                    closable={true}
                    onClose={(e) => {
                      e.preventDefault();

                      setFilter({
                        ...filter,
                        revenue: [],
                        revenueStatus: 'is_between',
                        revenueStatusItem: [],
                      });
                    }}
                  >
                    £ {formatNumber(filter?.revenue?.[0])} - £{' '}
                    {formatNumber(filter?.revenue?.[1])}
                  </Tag>
                )}

                {filter?.websiteUrl && (
                  <Tag
                    color="success"
                    icon={<PaperClipOutlined />}
                    closable={true}
                    onClose={() => setFilter({ ...filter, websiteUrl: '' })}
                  >
                    {filter?.websiteUrl}
                  </Tag>
                )}

                {filter?.sequence?.trim() && (
                  <Tag
                    color="processing"
                    icon={<BranchesOutlined />}
                    closable={true}
                    onClose={() => setFilter({ ...filter, sequence: '' })}
                  >
                    {filter?.sequence}
                  </Tag>
                )}

                {filter?.funding?.length > 0 && (
                  <Tag
                    color="orange"
                    icon={<FundOutlined />}
                    closable={true}
                    onClose={() => {
                      setFilter({
                        ...filter,
                        funding: [],
                        organizationLatestFundingStageCd: [],
                        fundingStatus: 'is_between',
                      });
                    }}
                  >
                    £ {formatNumber(filter?.funding?.[0])}- £{' '}
                    {formatNumber(filter?.funding?.[1])}
                  </Tag>
                )}
                {filter?.industry?.length > 0 &&
                  filter?.industry?.map((item) => (
                    <Tag
                      color="warning"
                      icon={<SlidersOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.industry?.indexOf(item);
                        const newIndustries = [...filter?.industry];
                        const newIndustryIds = [...filter?.industryIds];

                        newIndustries.splice(index, 1);
                        newIndustryIds.splice(index, 1);

                        setFilter({
                          ...filter,
                          industry: newIndustries,
                          industryIds: newIndustryIds,
                        });
                      }}
                    >
                      {item}
                    </Tag>
                  ))}
                {filter?.companySize?.length > 0 && (
                  <Tag
                    color="gold"
                    icon={<UsergroupAddOutlined />}
                    closable={true}
                    onClose={() => {
                      setFilter({
                        ...filter,
                        companySize: [],
                      });
                    }}
                  >
                    {filter?.companySize?.[0]} - {filter?.companySize?.[1]}
                  </Tag>
                )}

                {filter?.location?.length > 0 &&
                  filter?.location?.map((item) => (
                    <Tag
                      color="lime"
                      icon={<AimOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.location?.indexOf(item);
                        const newLocations = [...filter?.location];
                        const newLocationIds = [...filter?.locationIds];

                        newLocations.splice(index, 1);
                        newLocationIds.splice(index, 1);
                        setFilter({
                          ...filter,
                          location: newLocations,
                          locationIds: newLocationIds,
                        });
                      }}
                    >
                      {item}
                    </Tag>
                  ))}
                {selectedSearch === SEARCH.CONTACT &&
                  filter?.jobTitle?.length > 0 &&
                  filter?.jobTitle?.map((item) => (
                    <Tag
                      color="purple"
                      icon={<BuildOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.jobTitle?.indexOf(item);
                        const newJobTitles = [...filter?.jobTitle];
                        const newJobTitleIds = [...filter?.jobTitleIds];

                        newJobTitles.splice(index, 1);
                        newJobTitleIds.splice(index, 1);
                        setFilter({
                          ...filter,
                          jobTitle: newJobTitles,
                          jobTitleIds: newJobTitleIds,
                        });
                      }}
                    >
                      {item}
                    </Tag>
                  ))}
                {selectedSearch === SEARCH.CONTACT &&
                  filter?.skills?.length > 0 &&
                  filter?.skills?.map((item) => (
                    <Tag
                      color="geekblue"
                      icon={<ThunderboltOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.skills?.indexOf(item);
                        const newSkills = [...filter?.skills];
                        const newSkillIds = [...filter?.skillIds];

                        newSkills.splice(index, 1);
                        newSkillIds.splice(index, 1);
                        setFilter({
                          ...filter,
                          skills: newSkills,
                          skillIds: newSkillIds,
                        });
                      }}
                    >
                      {item}
                    </Tag>
                  ))}
                {selectedSearch === SEARCH.CONTACT &&
                  filter?.department?.length > 0 &&
                  filter?.department?.map((item) => (
                    <Tag
                      color="geekblue"
                      icon={<ThunderboltOutlined />}
                      closable={true}
                      onClose={(e) => {
                        e.preventDefault();
                        const index = filter?.department?.indexOf(item);
                        const newDepartment = [...filter?.department];
                        const newDepartmentIds = [...filter?.departmentIds];

                        newDepartment.splice(index, 1);
                        newDepartmentIds.splice(index, 1);
                        setFilter({
                          ...filter,
                          department: newDepartment,
                          departmentIds: newDepartmentIds,
                        });
                      }}
                    >
                      {`${item}`}
                    </Tag>
                  ))}
                {selectedSearch === SEARCH.CONTACT &&
                  filter?.yearOfExperience?.length > 0 && (
                    <Tag
                      color="blue"
                      icon={<ReadOutlined />}
                      closable={true}
                      onClose={() => {
                        setFilter({
                          ...filter,
                          yearOfExperience: [],
                        });
                      }}
                    >
                      {filter?.yearOfExperience?.[0]} -{' '}
                      {filter?.yearOfExperience?.[1]}
                    </Tag>
                  )}
              </div>
              <Filter
                selectedSearch={selectedSearch}
                filter={filter}
                setFilter={setFilter}
                rawFilter={rawFilter}
                setRawFilter={setRawFilter}
                handleSubmitSearch={handleSubmitSearch}
                handleSaveSearch={handleSaveSearch}
                loading={loading}
                handleSubmitContactSearch={handleSubmitContactSearch}
              />
            </div>
          </div>
          {openModalAfterBulkAdd && renderNotification()}
          {isAddContactFormBulk && renderBulkAddContactsToBH()}
          {!openModalAfterBulkAdd && !isAddContactFormBulk && (
            <>
              {selectedSearch === SEARCH.CONTACT && (
                <div className="col-span-8 w-full">
                  <div className="text-cyan-700 font-semibold pb-2 border-b-2 border-cyan-700 w-fit">{`Total (${formatNumber(pagination?.totalCount || 0)})`}</div>
                  {loading && (
                    <div className="flex justify-center items-center w-full h-[20rem] gap-3">
                      <Loading />
                      <div className="font-medium text-cyan-700">
                        Finding Contacts...
                      </div>
                    </div>
                  )}
                  {!loading && (
                    <div>
                      <div className="task-table-new-design-container">
                        <Table
                          className="customized-style-pagination w-full"
                          dataSource={dataSource}
                          columns={columns}
                          // loading={loading}
                          rowKey={(record) => record?.id}
                          rowSelection={rowSelection}
                          onRow={(record, rowIndex) => {
                            return {
                              onClick: (event) => {
                                if (selectedRowKeys?.length > 0) return;
                                const isActionColumn = event.target.closest(
                                  '.skip-action-column'
                                );
                                if (!isActionColumn) {
                                  setSelectedContact(record);
                                }
                              },
                              style: { cursor: 'pointer' },
                            };
                          }}
                          pagination={{
                            pageSize: pagination?.pageSize,
                            current: pagination?.currentPage,
                            total: pagination?.totalCount,
                            onChange: (page, pageSize) => {
                              const newPagination = {
                                ...pagination,
                                currentPage: page,
                                pageSize,
                              };
                              handleSubmitContactSearch('', '', newPagination);
                              setPagination({ ...newPagination });
                            },
                            showSizeChanger: false,
                          }}
                          // scroll={{ x: 'max-content' }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
              {selectedSearch === SEARCH.COMPANY && (
                <div className="col-span-8 w-full">
                  <div className="text-cyan-700 font-semibold pb-2 border-b-2 border-cyan-700 w-fit">{`Total (${formatNumber(companyPagination?.totalCount || 0)})`}</div>
                  {loading && (
                    <div className="flex justify-center items-center w-full h-[20rem] gap-3">
                      <Loading />
                      <div className="font-medium text-cyan-700">
                        Finding Companies...
                      </div>
                    </div>
                  )}
                  {!loading && (
                    <div className="task-table-new-design-container">
                      <Table
                        className="customized-style-pagination w-full"
                        dataSource={companyDataSource}
                        columns={companyColumns}
                        // loading={loading}
                        rowKey={(record) => record?.id}
                        rowSelection={rowSelection}
                        onRow={(record, rowIndex) => {
                          return {
                            onClick: () => {
                              setSelectedCompany({
                                id: record?.id,
                                company_name: record?.name,
                              });
                            },
                            style: { cursor: 'pointer' },
                          };
                        }}
                        pagination={{
                          pageSize: companyPagination?.pageSize,
                          current: companyPagination?.currentPage,
                          total: companyPagination?.totalCount,
                          onChange: (page, pageSize) => {
                            const newPagination = {
                              ...companyPagination,
                              currentPage: page,
                              pageSize,
                            };
                            handleSubmitCompanySearch('', '', newPagination);
                            setCompanyPagination({ ...newPagination });
                          },
                          showSizeChanger: false,
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* MODAL ADD CONTACT */}
      {isAddContactForm && (
        <Modal
          className="customize-contact-form"
          width={1000}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          // title="Add Contact Form"
          open={isAddContactForm}
          onCancel={() => {
            setIsAddContactForm(false);
            handleResetFormAddContact();
          }}
          footer={false}
        >
          <Form layout="vertical">
            <div className="text-xl mx-6 pb-3 pt-5 font-semibold text-base border-b-2 border-b-[#17c1e8]">
              <span>Add Contact Form</span>
            </div>
            <BullhornSubmissionContact
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              handleCloseClient={handleCloseClient}
              setHandleCloseClient={setHandleCloseClient}
              handleCloseContact={handleCloseContact}
              setHandleCloseContact={setHandleCloseContact}
              detailDataContact={detailDataContact}
              flagDetailContact={flagDetailContact}
            />
            <div className="pl-6 left-0 bottom-0 w-full pb-4">
              <div className="flex gap-4 mr-8">
                <Button
                  disabled={isSubmitForm}
                  onClick={() => {
                    setIsAddContactForm(false);
                    handleResetFormAddContact();
                  }}
                  className={`bg-[#BEDAFD33] `}
                >
                  Cancel
                </Button>
                <Button
                  disabled={isSubmitForm}
                  htmlType="button"
                  onClick={handleSubmitAddContact}
                  type="primary"
                  className={` `}
                >
                  Save
                </Button>
              </div>
            </div>
          </Form>
        </Modal>
      )}
      {/* Modal bulk add contacts */}
      <ModalListUserGroup
        currentContact={currentContact}
        currentContacts={bulkContacts}
        setOpen={setOpenExistingUserGroup}
        open={openExistingUserGroup}
        enrichFields={getEnrichFields(enrichFields)}
      />
      {/* Modal existing sequence */}
      <ModalShowListExitSequence
        currentContact={{
          email: currentContact?.work_email,
          name: currentContact?.name,
          firstName: currentContact?.first_name || currentContact?.name,
        }}
        setOpen={setOpenExistingSequence}
        open={openExistingSequence}
      />
      {/* Modal bulk enrich data */}
      <Modal
        title="Bulk Enrich contacts data"
        open={openBulkEnrichModal}
        footer={null}
        width={1200}
        closable={false}
        destroyOnClose={true}
      >
        <BulkEnrichData
          selectedContacts={[
            ...selectedRowItems
              .filter((item) => selectedRowKeys.includes(item?.id))
              ?.map((option) => {
                return {
                  ...option,
                  value:
                    (option?.id || option?.work_email) + '_' + option?.name,
                  label: option?.name || option?.work_email,
                  email: option?.work_email,
                  // phone: option?.phone,
                  address: option?.address,
                  status: option?.status,
                  occupation: option?.occupation,
                  name: option?.name,
                  contactId: option?.id,
                  linkedInProfileUrl: option?.links?.linkedin,
                };
              }),
          ]}
          handleSaveBulkEnrichData={(submittedDataSource) => {
            const newDataSource = dataSource.map((item) => {
              const enrichedItem = submittedDataSource.find(
                (enrichedItem) => enrichedItem?.id === item?.id
              );
              if (enrichedItem) {
                return {
                  ...item,
                  ...(enrichedItem?.valid_work_email && {
                    work_email: enrichedItem?.valid_work_email,
                  }),
                };
              }
              return { ...item };
            });
            setDataSource([...newDataSource]);
          }}
          closeBulkEnrichModal={closeBulkEnrichModal}
        />
      </Modal>

      <Modal
        title="Edit your Search name"
        open={openEditSavedSearch}
        footer={false}
        onCancel={() => setOpenEditSavedSearch(false)}
      >
        <Input
          value={searchNameToSave}
          onChange={(e) => setSearchNameToSave(e.target.value)}
          placeholder="Input your search name ..."
        />
        <Button
          loading={loadingEditSearchName}
          onClick={() => handleEditSavedSearch()}
          style={{ width: '100%', marginTop: '10px' }}
          type="primary"
        >
          Save
        </Button>
      </Modal>

      {/* section company add & edit */}
      <Modal
        open={showCompany}
        onCancel={() => {
          setShowCompany(false);
        }}
        width={1000}
        closable={false}
        footer={false}
      >
        <div className="bullhorn-job-submission-form-container bg-cyan-600">
          <Form layout="vertical">
            <Form.Item>
              <Card className=" max-w-full mx-auto bg-[#BEDAFD33] shadow-lg rounded-2xl overflow-hidden hover:shadow-xl">
                <div className="w-full bg-[#BEDAFD] pl-6 py-3 font-semibold text-base">
                  <span>Add New Company</span>
                </div>
                <div className="p-6">
                  <div className="mb-6 border-b-2 border-b-white pb-2">
                    <span className="font-medium text-base text-white ">
                      Add Company
                    </span>
                  </div>
                  <BullHornJobSubmissionCompany
                    isEditCompany={false}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    handleCloseClient={handleCloseClient}
                    setHandleCloseClient={setHandleCloseClient}
                    watch={watch}
                  />
                  <div className="left-0 bottom-0 w-full">
                    <div className="flex gap-4 mr-8">
                      <Button
                        onClick={() => {
                          setShowCompany(false);
                          setValue('companySelect', null);
                          setHandleCloseClient(true);
                          // setIsAddCompany(false);
                        }}
                        className={`bg-[#BEDAFD33] `}
                      >
                        Cancel
                      </Button>
                      <Button
                        htmlType="button"
                        // onClick={handleSubmitCompany}
                        type="primary"
                        className={`bg-white text-cyan-600 `}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default EmailFinderContainerV2;
