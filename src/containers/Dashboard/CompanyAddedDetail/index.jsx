import { useEffect, useState } from 'react';
import { TableWrapper, UserTableStyleWrapper } from '../../CRM/styled';
import { Avatar, Table, Tag, Tooltip } from 'antd';
import { HOT_LIST_TAB_KEY, USER_LIST_TAB_KEY } from '../../Sequence';
import { LinkedinOutlined, UserOutlined } from '@ant-design/icons';
import { searchBullhornData } from '../../../services/bullhorn';
import dayjs from 'dayjs';
import { useQuery } from '@tanstack/react-query';
import { getCompaniesAdded } from '../../../services/users';
import { getLinkS3 } from '../../../services/aws';
import { UserGroupIcon } from '../../../components/Sidebar/consts';
import { STATUS_COLOR } from '../../CompanyManagement';

const CompanyAddedDetail = () => {
  const { data: dataSource = [], isFetching: loading } = useQuery(
    ['COMPANY_LIST_DETAIL'],
    {
      queryFn: async () => {
        const { data } = await getCompaniesAdded();
        console.log('Company List Data:', data);
        const dataAddedFromBH =
          data?.result?.dataAddedFromBH?.map((item) => ({
            id: item?.id,
            name: item?.rawInformation?.name,
            description: '',
            website: item?.rawInformation?.companyURL,
            telephone: item?.rawInformation?.phone,
            address: {
              country: item?.rawInformation?.address?.countryName,
              ...item?.rawInformation?.address,
            },
            status: item?.rawInformation?.status,
            ...item,
          })) || [];
        const dataAddedFromCRM = data?.result?.dataAddedFromCRM || [];

        return [...dataAddedFromBH, ...dataAddedFromCRM];
      },
    }
  );

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (text, record) => {
        return (
          <div className="flex items-center gap-2">
            {record?.avatarUrl && <Avatar size={50} src={record?.avatarUrl} />}
            {!record?.avatarUrl && <Avatar size={50} icon={<UserOutlined />} />}
            <div className="flex flex-col">
              <span
                className="font-semibold text-sm line-clamp-1 text-cyan-600"
                title={text || '-'}
              >
                {text || '-'}
              </span>
              {/* <span className="text-sm italic">{record?.website || '-'}</span> */}
              <a
                className="text-xs line-clamp-1 font-semibold"
                href={record?.website || '#'}
                title={record?.website || '-'}
                target="_blank"
              >
                {record?.website || '-'}
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      render: (address, record) => (
        <span className="text-cyan-600 text-sm font-semibold">
          {address?.address1 || address?.address2 || '-'}{' '}
          {address?.country ? `, ${address?.country}` : ''}
        </span>
      ),
    },
    {
      title: 'Telephone',
      dataIndex: 'telephone',
      key: 'telephone',
      render: (phone, record) => {
        return <div className="font-semibold text-sm ">{phone}</div>;
      },
    },
    {
      title: 'Company Size',
      dataIndex: 'companySize',
      key: 'companySize',
      render: (text, record) => (
        <div className="flex gap-1 items-center text-cyan-600 font-semibold">
          <UserGroupIcon className="w-4 h-4" />
          {` ${text || 'N/A'} `}
          <span className="text-sm">Employees</span>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status, record) => {
        return (
          <div className="w-full flex items-center justify-center">
            <Tag color={'cyan'} className="flex items-center px-5 py-1 text-sm">
              <span className=" text-sm">
                {status?.toLowerCase()?.capitalize() || 'Active'}
              </span>
            </Tag>
          </div>
        );
      },
    },
  ];

  return (
    <UserTableStyleWrapper>
      <div className="contact-table">
        <TableWrapper className="table-responsive text-gray-800">
          <Table
            loading={loading}
            className="customized-style-pagination w-full"
            rowClassName="editable-row"
            dataSource={dataSource}
            columns={columns}
            pagination={false}
            // footer={() => {
            //   return (
            //     <div
            //       onClick={() =>
            //         window.open(`/user-management/company`, '_blank')
            //       }
            //       className="text-cyan-600 font-medium cursor-pointer text-center hover:text-cyan-800 hover:underline "
            //     >
            //       View all
            //     </div>
            //   );
            // }}
          />
        </TableWrapper>
      </div>
    </UserTableStyleWrapper>
  );
};
export default CompanyAddedDetail;
