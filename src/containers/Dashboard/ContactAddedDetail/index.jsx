import { useEffect, useRef, useState } from 'react';
import { TableWrapper, UserTableStyleWrapper } from '../../CRM/styled';
import { Badge, Button, Input, Row, Table, Tag, Tooltip } from 'antd';
import { HOT_LIST_TAB_KEY, USER_LIST_TAB_KEY } from '../../Sequence';
import { LinkedinOutlined, UserOutlined } from '@ant-design/icons';
import { searchBullhornData } from '../../../services/bullhorn';
import dayjs from 'dayjs';
import {
  Briefcase,
  Building2,
  Calendar,
  Download,
  LinkedinIcon,
  Mail,
  MapPin,
  Phone,
  Search,
} from 'lucide-react';
import * as AvatarPrimitive from '@radix-ui/react-avatar';
import { getInitials } from '../../../helpers/util';
import { getLinkedinProfile } from '../../../services/task';

const initialPagination = {
  start: 1,
  limit: 20,
  total: 0,
};

const ContactAddedDetail = ({ total }) => {
  const tableContainerRef = useRef(null);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ ...initialPagination });

  const handleLinkedinProfile = async (linkedinUrl) => {
    try {
      const { data } = await getLinkedinProfile(linkedinUrl);
      return data?.result?.profile_picture_url_large;
    } catch (error) {
      console.log('error: ', error);
      return '';
    }
  };

  const getBHContactOptions = async (newStart, newLimit) => {
    setLoading(true);
    try {
      const { data } = await searchBullhornData('ClientContact')(
        newStart || pagination.start,
        newLimit || pagination.limit
      );

      if (data?.result?.length > 0) {
        const options = data?.result?.map((item) => ({
          ...item,
        }));
        // const optionsWithAvatar = await Promise.all(
        //   options.map(async (contact) => ({
        //     ...contact,
        //     avatar: contact?.linkedinProfileUrl
        //       ? await handleLinkedinProfile(contact?.linkedinProfileUrl)
        //       : '',
        //   }))
        // );
        setDataSource([...dataSource, ...options]);
      } else {
        setDataSource([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    getBHContactOptions();
  }, []);

  const handleScroll = () => {
    const container = tableContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight) {
        const newStart = pagination.start + pagination.limit;
        setPagination({
          ...pagination,
          start: newStart,
        });
        getBHContactOptions(newStart, pagination.limit);
      }
    }
  };

  return (
    <div className="bg-white/95 backdrop-blur-xl shadow-2xl border-l border-gray-200/50 flex flex-col animate-in slide-in-from-right duration-300">
      <div className="flex-shrink-0 px-6 py-4 bg-white/80 border-b border-gray-200/50">
        <div className="grid grid-cols-10 items-center justify-between space-x-4">
          <div className="relative flex-1 col-span-8">
            <Input
              placeholder="Search contacts..."
              className="bg-white/80 border-gray-300/50"
              prefix={<Search className="text-gray-400 w-4 h-4" />}
              // value={searchQuery}
              // onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2 col-span-2">
            <Button
              // variant="outline"
              className="bg-white/80 border-gray-300/50 w-full"
              icon={<Download className="w-4 h-4" />}
            >
              <span className="font-medium">Export</span>
            </Button>
          </div>
        </div>
      </div>
      {/* Contacts List */}
      <div className="h-[79vh] flex flex-col overflow-y-auto">
        <Row
          id="contact-detail-table"
          className="max-h-[79vh] overflow-y-auto"
          ref={tableContainerRef}
          onScroll={handleScroll}
        >
          <div className="flex-1 overflow-auto p-6 space-y-4">
            {dataSource.map((contact, index) => (
              <div
                key={contact.id}
                className="rounded-lg border bg-card text-card-foreground shadow-sm border border-gray-200/50 shadow-sm bg-white/80 backdrop-blur-sm hover:shadow-lg hover:border-gray-300/50 transition-all duration-300 group"
              >
                <div className="p-4">
                  <div className="flex items-start space-x-4">
                    {/* Avatar */}
                    <div className="relative">
                      <AvatarPrimitive.Root className="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full h-12 w-12 border-2 border-white shadow-lg">
                        <AvatarPrimitive.Image
                          className="aspect-square h-full w-full"
                          src={contact.avatar || ''}
                          alt={contact.name}
                        />
                        <AvatarPrimitive.Fallback className="flex h-full w-full items-center justify-center rounded-full bg-muted bg-gradient-to-br from-purple-500 to-cyan-600 text-white font-semibold">
                          {getInitials(contact.name)}
                        </AvatarPrimitive.Fallback>
                      </AvatarPrimitive.Root>
                    </div>

                    {/* Main Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
                              {contact?.name}
                            </h3>
                          </div>

                          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                            <Briefcase className="w-4 h-4" />
                            <span className="font-medium">
                              {contact?.occupation}
                            </span>
                            <span className="text-gray-400">•</span>
                            <Building2 className="w-4 h-4" />
                            <span>{contact?.clientCorporation?.name}</span>
                          </div>

                          {contact.location && (
                            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                              <MapPin className="w-4 h-4" />
                              <span>
                                {contact?.address?.address1}{' '}
                                {contact?.address?.countryName}
                              </span>
                            </div>
                          )}

                          <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                            <div className="flex items-center space-x-1">
                              <Phone className="w-4 h-4" />
                              <span>{contact?.phone}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Mail className="w-4 h-4" />
                              <span className="truncate">{contact?.email}</span>
                            </div>

                            {contact?.linkedinProfileUrl && (
                              <div
                                onClick={() =>
                                  window.open(
                                    contact?.linkedinProfileUrl,
                                    '_blank'
                                  )
                                }
                                className="flex items-center space-x-1 hover:text-cyan-600 cursor-pointer hover:underline"
                              >
                                <LinkedinIcon className="w-4 h-4" />
                                <span className="truncate">
                                  {contact?.linkedinProfileUrl
                                    ?.split('/')
                                    .pop()}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-1 mb-3">
                            {contact?.businessSectors?.data?.length > 0 &&
                              contact?.businessSectors?.data?.map(
                                ({ id, name }) => (
                                  <div
                                    key={id}
                                    variant="outline"
                                    className="px-3 py-1 rounded-2xl border font-semibold text-xs bg-gradient-to-r from-purple-50 to-cyan-50 border-purple-200 text-purple-700"
                                  >
                                    {name}
                                  </div>
                                )
                              )}
                          </div>

                          {/* Timeline */}
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Calendar className="w-3 h-3" />
                              <span>
                                Added{' '}
                                {dayjs(contact?.dateAdded).format(
                                  'HH:MM a DD/MM/YYYY'
                                )}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Row>
        {loading && (
          <div className="flex items-center justify-center p-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
          </div>
        )}
      </div>
      {/* Footer */}
      <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {dataSource.length} of {total} contacts
          </div>
        </div>
      </div>
    </div>
    // <UserTableStyleWrapper>
    //   <div className="contact-table">
    //     <TableWrapper className="table-responsive text-gray-800">
    //       <Table
    //         loading={loading}
    //         className="customized-style-pagination w-full"
    //         rowClassName="editable-row"
    //         dataSource={dataSource}
    //         columns={columns}
    //         pagination={false}
    //         footer={() => {
    //           return (
    //             <div
    //               onClick={() =>
    //                 window.open(
    //                   `/sequence?activeKey=${HOT_LIST_TAB_KEY}`,
    //                   '_blank'
    //                 )
    //               }
    //               className="text-cyan-600 font-medium cursor-pointer text-center hover:text-cyan-800 hover:underline "
    //             >
    //               View all
    //             </div>
    //           );
    //         }}
    //       />
    //     </TableWrapper>
    //   </div>
    // </UserTableStyleWrapper>
  );
};
export default ContactAddedDetail;
