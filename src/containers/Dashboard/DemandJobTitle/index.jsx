import React, { useState, useEffect, useRef } from 'react';
import { Row, Col, Table, Skeleton, Spin } from 'antd';
import { NavLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { DemanJobTitleList } from './styles';
import { Cards } from '../../../components/Cards';
import {
  FileExcelOutlined,
  FileImageOutlined,
  PrinterOutlined,
} from '@ant-design/icons';

// const moreContent = (
//   <>
//     <NavLink to="#">
//       <span>Printer</span>
//     </NavLink>
//     <NavLink to="#">
//       <span>PDF</span>
//     </NavLink>
//     <NavLink to="#">
//       <span>Google Sheets</span>
//     </NavLink>
//     <NavLink to="#">
//       <span>Excel (XLSX)</span>
//     </NavLink>
//     <NavLink to="#">
//       <span>CSV</span>
//     </NavLink>
//   </>
// );

const moreContent = [
  {
    key: 'Printer',
    label: (
      <NavLink to="#">
        <span>Printer</span>
      </NavLink>
    ),
    icon: <PrinterOutlined />,
  },
  {
    key: 'Excel',
    label: (
      <NavLink to="#">
        <span>Excel</span>
      </NavLink>
    ),
    icon: <FileExcelOutlined />,
  },
  {
    key: 'Image',
    label: (
      <NavLink to="#">
        <span>Image</span>
      </NavLink>
    ),
    icon: <FileImageOutlined />,
  },
];

const keywordColumns = [
  {
    title: 'Top Keywords',
    dataIndex: 'keyword',
    key: 'keyword',
  },
  {
    title: 'Appearances',
    dataIndex: 'count',
    key: 'count',
    align: 'center',
  },
];

// const keywordData = [
//   {
//     key: 1,
//     keyword: 'JavaScript',
//     loop: 93947,
//   },
//   {
//     key: 2,
//     keyword: 'JS',
//     loop: 85578,
//   },
//   {
//     key: 3,
//     keyword: 'TS',
//     loop: 10398,
//   },
//   {
//     key: 4,
//     keyword: 'Python',
//     loop: 9391,
//   },
//   {
//     key: 5,
//     keyword: 'React',
//     loop: 7242,
//   },
//   {
//     key: 6,
//     keyword: 'Angular',
//     loop: 974,
//   },
//   {
//     key: 7,
//     keyword: 'Sharepoint',
//     loop: 710,
//   }
// ];

const DemandJobTitle = (props) => {
  const { keywordData, handleGetKeyWords, loadingKeyWord } = props;
  const [page, setPage] = useState(0);
  const renderThumb = ({ style, ...props }) => {
    const thumbStyle = {
      borderRadius: 6,
      backgroundColor: '#F1F2F6',
      height: '220px',
    };
    return <div style={{ ...style, ...thumbStyle }} props={props} />;
  };

  renderThumb.propTypes = {
    style: PropTypes.shape(PropTypes.object).isRequired,
  };

  const tableContainerRef = useRef(null);

  const handleScroll = () => {
    const container = tableContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight) {
        setPage(page + 1);
        handleGetKeyWords(page + 1);
      }
    }
  };

  return (
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/30 to-white hover:shadow-xl transition-all duration-300">
      <div className="p-6 w-full">
        <div className="w-[20rem] flex items-center mb-4">
          <h3 className="text-lg font-semibold text-cyan-600 ">
            In Demand Job Titles{' '}
          </h3>
          {loadingKeyWord && (
            <span className="flex items-center justify-center pl-3">
              <Spin size="small" />
            </span>
          )}
        </div>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm font-medium text-gray-600 border-b pb-2">
            <div>Top Keywords</div>
            <div className="text-right">Appearances</div>
          </div>
          <Row
            id="keywords-table"
            className="keywords-table-container max-h-[26.5rem] overflow-y-auto space-y-3 w-full bg-white"
            ref={tableContainerRef}
            onScroll={handleScroll}
          >
            {keywordData?.map((item, index) => (
              <div
                key={index}
                className="grid grid-cols-6 gap-4 text-sm hover:bg-white/50 rounded-lg p-2 -m-2 transition-all duration-200 w-full cursor-pointer"
              >
                <div className="text-gray-900 col-span-5">{item.keyword}</div>
                <div className="text-right font-semibold text-cyan-600">
                  {item.count}
                </div>
              </div>
            ))}
          </Row>
        </div>
      </div>
    </div>
  );
};

export default DemandJobTitle;
