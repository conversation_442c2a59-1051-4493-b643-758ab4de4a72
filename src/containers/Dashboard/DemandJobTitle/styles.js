import Styled from 'styled-components'

export const DemanJobTitleList = Styled.div`
    max-height: 300px;
    overflow: hidden auto;
    border: 1px solid rgb(241, 242, 246);
    table{
        tr{
            &:first-child{
                td{
                    padding-top: 15px;
                }
            }
            &:last-child{
                td{
                    padding-bottom: 15px;
                }
            }
            th{
                font-size: 13px;
                font-weight: 500;
                color: ${({ theme }) => theme['dark-color']};
                background: ${({ theme }) => theme['bg-color-light']};
                padding: 9px 20px;
                border: 0 none;
            }
            td{
                font-size: 13px;
                border: 0 none;
                padding: 6px 20px;
                color: ${({ theme }) => theme['gray-color']};
            }
        }
    }

`;