import React, { useRef, useState } from 'react';
import {
  Button,
  Dropdown,
  Image,
  Modal,
  Row,
  Space,
  Spin,
  Table,
  Tooltip,
  notification,
} from 'antd';
import { Cards } from '../../../components/Cards';
import {
  CheckCircleOutlined,
  CommentOutlined,
  DownOutlined,
  FolderOpenOutlined,
  <PERSON><PERSON>inOutlined,
  <PERSON>uOutlined,
  RedoOutlined,
  ShrinkOutlined,
} from '@ant-design/icons';
import handleRenderTime from '../../../function/handleRenderTime';
import ClickedIcon from '../../../assets/img/icons/click.png';
import OpenedIcon from '../../../assets/img/icons/emailopen.png';
import RepliedIcon from '../../../assets/img/icons/emailreply.png';
import AllIcon from '../../../assets/img/icons/all.png';
import SendIcon from '../../../assets/img/icons/send.png';
import UnknowIcon from '../../../assets/img/icons/unknown.png';

import DetailEditContactList from '../../Sequence/EditDetailContactList';
import DetailContactListItem from '../../Sequence/DetailContactListItem';
import { getUserDetail } from '../../../services/users';
import { getContactBhDetail } from '../../../services/search';
import { head } from 'lodash';
import { Mail } from 'lucide-react';
const LIVE_FEED_ACTION_TYPE = {
  CLICKED: 'CLICKED',
  REPLIED: 'REPLIED',
  OPENED: 'OPENED',
  LINKEDIN_CONNECTION_REQ: 'LINKEDIN CONNECTION REQ',
  LINKEDIN_MESSAGE: 'LINKEDIN MESSAGE',
};

const LIVE_FEED_ACTION_TYPE_BE = {
  // OPENED: 'opened',
  // LINK_CLICKED: 'link_clicked',
  FAILED: 'failed',
  SKIPPED: 'skipped',
  SENT: 'sent',
  // AUTO_REPLIED: 'auto_replied',
  LINKEDIN_INVITATION_ACCEPTED: 'linkedin_invitation_accepted',
};

const MAPPING_LIVE_TYPE_ACTION = {
  linkedin_invitation_accepted: 'accepted',
  auto_replied: 'replied',
  skipped: 'skipped',
  failed: 'failed',
  link_clicked: 'clicked',
  opened: 'opened',
  sent: 'sent',
  message: 'message',
  replied: 'replied',
};

const ICON_BY_ACTION = {
  link_clicked: (
    <Image src={ClickedIcon} preview={false} height={30} width={30} />
  ),
  auto_replied: (
    <Image src={RepliedIcon} preview={false} height={30} width={30} />
  ),
  opened: <Image src={OpenedIcon} preview={false} height={30} width={30} />,
  linkedin_invitation_accepted: (
    <LinkedinOutlined className="text-[#0288d1] text-3xl" />
  ),
  sent: <Image src={SendIcon} preview={false} height={30} width={30} />,
};

const LIVE_FEED_LABEL = {
  all: 'All',
  link_clicked: 'Clicks',
  auto_replied: 'Replies',
  opened: 'Open',
  linkedin_invitation_accepted: 'Connection Request',
  'LINKEDIN MESSAGE': 'Message',
};

const LIVE_FEED_FILTER = {
  all: 'all',
  link_clicked: 'link_clicked',
  auto_replied: 'auto_replied',
  opened: 'opened',
  linkedin_invitation_accepted: 'linkedin_invitation_accepted',
  'LINKEDIN MESSAGE': 'message',
};

const LiveFeed = ({
  liveFeedData,
  loading,
  refresh,
  setLiveFeedType,
  liveFeedStart,
  setLiveFeedStart,
}) => {
  const [selectedAction, setSelectedAction] = useState('all');
  // Open contact modal
  const [openContactModal, setOpenContactModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const showContactModal = () => setOpenContactModal(true);
  const closeContactModal = () => setOpenContactModal(false);
  const [loadingContact, setLoadingContact] = useState(false);

  const getUserDetailFunc = async (userId) => {
    if (!userId) {
      setOpenContactModal(true);
      return;
    }
    try {
      setLoadingContact(true);
      showContactModal();
      const { data } = await getContactBhDetail(userId);
      setSelectedUser(data?.result?.data);
      setLoadingContact(false);
    } catch (err) {
      // notification.error({ message: err?.response?.data?.message });
      setLoadingContact(false);
    }
  };

  const liveFeedColumns = [
    {
      title: '',
      dataIndex: 'sequence_id',
      key: 'sequence_id',
      render: (id, record) => {
        const {
          contact_name,
          type,
          sequence_name,
          created_at,
          times,
          sequence_id,
          contact_id,
          link_onclick,
          contact_email,
        } = record;
        return (
          <div className="grid grid-cols-10 items-start space-x-3 hover:bg-gray-50/50 rounded-lg p-2 -m-2 transition-all duration-200 text-sm">
            <div className="col-span-1 flex justify-start items-center">
              {/* {ICON_BY_ACTION[type] || (
                <Image
                  src={UnknowIcon}
                  preview={false}
                  height={20}
                  width={20}
                />
              )} */}
              <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                <span className="text-white !text-xs">
                  {/* <Mail size={15} className='font-bold' />{' '} */}
                  <Image
                    className="shadow-md"
                    src={OpenedIcon}
                    preview={false}
                    height={15}
                    width={15}
                  />
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-1 col-span-9">
              <div
                className="gap-1 font-semibold line-clamp-1 text-sm"
                title={sequence_name}
              >
                {type === MAPPING_LIVE_TYPE_ACTION['message'] && (
                  <>
                    <>
                      {' '}
                      Sent message to
                      <a
                        className="text-[#00a5dd]"
                        onClick={() => {
                          getUserDetailFunc(contact_id);
                          if (contact_id) {
                            setSelectedUser({
                              name: contact_name,
                              email: contact_email,
                            });
                          }
                        }}
                      >
                        {contact_name}
                      </a>
                      <span>on</span>
                      <a
                        className="text-[#00a5dd]"
                        href={`/sequence?seqId=${sequence_id}`}
                        target="_blank"
                      >
                        {sequence_name}
                      </a>
                    </>
                  </>
                )}
                {type === 'link_clicked' && (
                  <>
                    <a
                      className="text-[#00a5dd]"
                      onClick={() => {
                        getUserDetailFunc(contact_id);
                        if (contact_id) {
                          setSelectedUser({
                            name: contact_name,
                            email: contact_email,
                          });
                        }
                      }}
                    >
                      {contact_name}
                    </a>
                    <span>{` ${MAPPING_LIVE_TYPE_ACTION[type]}`}</span>
                    <a
                      className="text-[#00a5dd]"
                      href={link_onclick}
                      target="_blank"
                    >
                      Link
                    </a>
                    <span>on</span>
                    <a
                      className="text-[#00a5dd]"
                      href={`/sequence?seqId=${sequence_id}`}
                      target="_blank"
                    >
                      {sequence_name}
                    </a>
                  </>
                )}
                {type !== 'link_clicked' &&
                  type !== MAPPING_LIVE_TYPE_ACTION['message'] && (
                    <>
                      <a
                        className="text-[#00a5dd]"
                        onClick={() => {
                          getUserDetailFunc(contact_id);
                          if (contact_id) {
                            setSelectedUser({
                              name: contact_name,
                              email: contact_email,
                            });
                          }
                        }}
                      >
                        {contact_name}
                      </a>
                      <span>{` ${MAPPING_LIVE_TYPE_ACTION[type]} on `}</span>
                      <a
                        className="text-[#00a5dd]"
                        href={`/sequence?seqId=${sequence_id}`}
                        target="_blank"
                      >
                        {sequence_name}
                      </a>
                    </>
                  )}
              </div>
              <div className="text-xs font-medium flex items-center gap-1 text-gray-700">
                <span>{handleRenderTime(created_at)}</span>
                <div className={`rounded-full w-1 h-1 bg-gray-400`} />
                <span>{`${type === 'link_clicked' ? 1 : times} times in total`}</span>
              </div>
            </div>
          </div>
        );
      },
    },
  ];

  const items = [
    {
      key: 'all',
      label: (
        <div className="w-full flex justify-center font-medium min-w-[4rem]">
          All
        </div>
      ),
      icon: <Image src={AllIcon} preview={false} height={15} width={15} />,
    },
    {
      key: 'link_clicked',
      label: (
        <div className="w-full flex justify-center font-medium">Clicks</div>
      ),
      icon: <Image src={ClickedIcon} preview={false} height={15} width={15} />,
    },
    {
      key: 'opened',
      label: <div className="w-full flex justify-center font-medium">Open</div>,
      icon: <Image src={OpenedIcon} preview={false} height={15} width={15} />,
    },
    {
      key: 'auto_replied',
      label: (
        <div className="w-full flex justify-center font-medium">Replies</div>
      ),
      icon: <Image src={RepliedIcon} preview={false} height={15} width={15} />,
    },
    {
      key: 'linkedin_invitation_accepted',
      label: (
        <div className="w-full flex justify-center font-medium">
          Connection Request
        </div>
      ),
      icon: <LinkedinOutlined className="text-[#0288d1]" />,
    },
    {
      key: LIVE_FEED_ACTION_TYPE.LINKEDIN_MESSAGE,
      label: (
        <div className="w-full flex justify-center font-medium">Message</div>
      ),
      icon: <LinkedinOutlined className="text-[#0288d1]" />,
    },
  ];

  const tableContainerRef = useRef(null);

  const handleGetDetailData = async () => {
    // const { data } = await getDetailContactList(currentDetail?.id);
  };

  const handleScroll = () => {
    const container = tableContainerRef.current;
    if (container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (scrollTop + clientHeight >= scrollHeight) {
        setLiveFeedStart(liveFeedStart + 1);
      }
    }
  };

  return (
    // <Cards
    //   title="Live Feed"
    //   size="large"
    //   isbutton={
    //     <div>
    //       <Dropdown
    //         menu={{
    //           items,
    //           onClick: ({ key }) => {
    //             setSelectedAction(key),
    //               setLiveFeedType(LIVE_FEED_FILTER[key]),
    //               setLiveFeedStart(0);
    //           },
    //         }}
    //       >
    //         <a onClick={(e) => e.preventDefault()}>
    //           <Space>
    //             {LIVE_FEED_LABEL[selectedAction]}
    //             <DownOutlined />
    //           </Space>
    //         </a>
    //       </Dropdown>
    //       <Tooltip title="Refresh data">
    //         <Button
    //           type="text"
    //           icon={
    //             <RedoOutlined
    //               onClick={() => {
    //                 liveFeedStart > 0 ? setLiveFeedStart(0) : refresh();
    //               }}
    //               spin={loading}
    //             />
    //           }
    //           className="ml-2"
    //         ></Button>
    //       </Tooltip>
    //     </div>
    //   }
    // >
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/30 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-cyan-600 flex items-center">
            Live Feed
            <div className="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </h3>
          <div className="flex items-center space-x-2">
            <div className="flex items-center gap-2">
              <Dropdown
                className="border border-gray-300 rounded-lg px-2 py-1 flex items-center bg-white hover:bg-gray-50 transition-colors duration-200"
                menu={{
                  items,
                  onClick: ({ key }) => {
                    setSelectedAction(key),
                      setLiveFeedType(LIVE_FEED_FILTER[key]),
                      setLiveFeedStart(0);
                  },
                }}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    {LIVE_FEED_LABEL[selectedAction]}
                    <DownOutlined />
                  </Space>
                </a>
              </Dropdown>
              <Tooltip title="Refresh data">
                <Button
                  type="text"
                  icon={
                    <RedoOutlined
                      className="text-gray-700 font-medium"
                      spin={loading}
                    />
                  }
                  onClick={() => {
                    liveFeedStart > 0 ? setLiveFeedStart(0) : refresh();
                  }}
                  className="ml-2"
                ></Button>
              </Tooltip>
            </div>
          </div>
        </div>
        <Row
          id="live-feed-table"
          className="live-feed-table-container max-h-[26.5rem] overflow-y-auto"
          ref={tableContainerRef}
          onScroll={handleScroll}
        >
          <Table
            size="small"
            loading={loading}
            className="custom-table"
            columns={liveFeedColumns}
            dataSource={liveFeedData}
            pagination={false}
            showHeader={false}
            scroll={(e) => console.log(e)}
          />
        </Row>
      </div>
      {/* Contact modal */}
      <Modal
        title="Contact Information"
        open={openContactModal}
        onCancel={closeContactModal}
        footer={null}
        width={1200}
      >
        {loadingContact ? (
          <Spin />
        ) : (
          <div style={{ display: 'flex' }}>
            <div
              className="flex justify-center"
              style={{
                width: '300px',
                borderRight: '1px solid #ccc',
                alignItems: 'center',
              }}
            >
              <img
                style={{
                  width: '200px',
                  height: '200px',
                  borderRadius: '8px',
                }}
                src={'./common_user.webp'}
              />
            </div>
            <div
              className="grid grid-cols-2 gap-4"
              style={{ width: '900px', padding: '20px' }}
            >
              {selectedUser?.name && (
                <>
                  <div className="flex">
                    <span className="w-32 font-semibold">Full Name:</span>
                    <span style={{ width: '300px' }}>{selectedUser?.name}</span>
                  </div>
                  <div></div>
                </>
              )}
              {!selectedUser?.name && (
                <>
                  <div className="flex">
                    <span className="w-32 font-semibold">First Name:</span>
                    <span style={{ width: '300px' }}>
                      {selectedUser?.firstName}
                    </span>
                  </div>
                  <div className="flex">
                    <span className="w-32 font-semibold">Last Name:</span>
                    <span style={{ width: '300px' }}>
                      {selectedUser?.lastName}
                    </span>
                  </div>
                </>
              )}
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Email:</span>
                <span style={{ width: '300px' }}>{selectedUser?.email}</span>
              </div>
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Linked Person:</span>
                <span style={{ width: '300px' }}>
                  {selectedUser?.linkedPerson?.firstName
                    ? selectedUser?.linkedPerson?.firstName
                    : '' + ' ' + selectedUser?.linkedPerson?.lastName
                      ? selectedUser?.linkedPerson?.lastName
                      : ''}
                </span>
              </div>
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Address:</span>
                <span style={{ width: '300px' }}>
                  {selectedUser?.address?.address1
                    ? selectedUser?.address?.address1
                    : '' + selectedUser?.address?.state &&
                        ', ' + selectedUser?.address?.state
                      ? selectedUser?.address?.state
                      : selectedUser?.address?.state}
                </span>
              </div>
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Corporation:</span>
                <span style={{ width: '300px' }}>
                  {selectedUser?.clientCorporation?.name}
                </span>
              </div>
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Occupation:</span>
                <span style={{ width: '300px' }}>
                  {selectedUser?.occupation}
                </span>
              </div>
              <div
                className="flex"
                style={{
                  marginTop: '15px',
                }}
              >
                <span className="w-32 font-semibold">Status:</span>
                <span style={{ width: '300px' }}>{selectedUser?.status}</span>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
    // </Cards>
  );
};

export default LiveFeed;
