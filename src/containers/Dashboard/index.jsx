/* eslint-disable react/jsx-key */
/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
/* eslint-disable indent */

import { useQuery } from '@tanstack/react-query';
import {
  Row,
  Col,
  Skeleton,
  Button,
  Dropdown,
  Select,
  notification,
  Avatar,
  Typography,
  Spin,
  Drawer,
} from 'antd';

import { useState, lazy, Suspense, useEffect, useRef } from 'react';
import React from 'react';
import {
  AimOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
  CalendarOutlined,
  CloseOutlined,
  ClusterOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  PlusOutlined,
  PrinterOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  ChartjsBarChartTransparent,
  chartOptions,
} from '../../components/Charts';

import { Cards } from '../../components/Cards';
import Heading from '../../components/Heading';

import { CardBarChart2, Container, EChartCard } from './style';
import { numberWithCommas } from '../../utils/common';
import {
  getActiveSequence,
  getCompaniesAdded,
  getContactAdded,
  getCountForEachLeadStatus,
  getEmailSent,
  getLeadCounts,
  getPotentialLeadReturn,
  getStatsKeyword,
  getStatsNewLead,
} from '../../services/jobLeads';
import moment from 'moment';
import { chartLinearGradient } from '../../utils/chart.utils';
import { Popover } from '../../components/Popup';
import { DateRangePickerOne } from '../../components/DatePicker';
import { countries } from 'country-list-json';
import { getLiveFeed } from '../../services/mailBox';
import dayjs from 'dayjs';
import { COMMON_STRINGS, userRole } from '../../constants/common.constant';
import { getUsers } from '../../services/users';
import { getColorHexFromName } from '../../function/getRandomColor';
import _isArray from 'lodash/isArray';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _compact from 'lodash/compact';
import _get from 'lodash/get';
import { stringAvatar } from '../../function/stringAvatar';
import { toPng } from 'html-to-image';
import jsPDF from 'jspdf';
import clsx from 'clsx';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';
import { permissions } from '../../constants/permissions';
import SyncedLeadDetail from './SyncedLeadDetail';
import ContactAddedDetail from './ContactAddedDetail';
import CompanyAddedDetail from './CompanyAddedDetail';
import {
  Activity,
  ArrowDownRight,
  ArrowUpRight,
  Building2,
  Database,
  File,
  FileText,
  Mail,
  Minus,
  Target,
  UserPlus,
  X,
} from 'lucide-react';
import styled from 'styled-components';

const MinMaxLeadsValue = lazy(() => import('./MinMaxLeadsValue'));
const SequenceDeals = lazy(() => import('./SequenceDeals'));
const MyLeadsStatsBySheet = lazy(() => import('./MyLeadsStatsBySheet'));
const DemandJobTitle = lazy(() => import('./DemandJobTitle'));
const LiveFeed = lazy(() => import('./LiveFeed'));
const LinkedinKeyMetrics = lazy(() => import('./LinkedinKeyMetrics'));

const ReStyledDrawer = styled(Drawer)`
  .ant-drawer-header,
  .ant-drawer-body {
    padding: 0 !important;
  }
`;

const initialFilter = {
  country: null,
  consultant: null,
  fromDate: null,
  toDate: null,
  dateRange: null,
};

const EXPORT_TYPE = {
  PRINT: 'PRINT',
  PDF: 'PDF',
  IMAGE: 'IMAGE',
};

const keyItems = [
  {
    key: 'syncedLeadCount',
    label: 'Synced Leads',
  },
  {
    key: 'emailSentsCount',
    label: 'Emails Sent',
  },
  {
    key: 'contactAddedCount',
    label: 'Contacts Added',
  },
  // {
  //   key: 'munualLeadCount',
  //   label: 'Manual Leads',
  // },
  // {
  //   key: 'newLeadCount',
  //   label: 'New Leads',
  // },
  {
    key: 'activeSequenceCount',
    label: 'Active Sequences',
  },

  {
    type: 'divider',
  },
  {
    key: 'sequence-stats',
    label: 'Sequence Stats',
  },
  {
    key: 'total-leads-value',
    label: 'Total Leads Value',
  },
  {
    type: 'divider',
  },
  {
    key: 'my-leads-statics',
    label: 'My Leads Statics',
  },
  {
    key: 'in-demand-job-titles',
    label: 'In Demand Job Titles',
  },
  {
    key: 'live-feed',
    label: 'Live Feed',
  },
  {
    key: 'linkedin-key-metrics',
    label: 'Linkedin Key Metrics',
  },
];

export const getMonthsNeeded = () => {
  const months = [];
  const now = dayjs();

  for (let i = 7; i >= 0; i--) {
    months.push(now.subtract(i, 'month').format('YYYY-MM'));
  }

  return months;
};

const handleMetricClick = (metricTitle) => {
  console.log(`Clicked on ${metricTitle}`);
};

const renderTrendIndicator = (trend, change) => {
  const changeValue = Number?.parseInt(change?.replace('%', ''));

  return (
    <div className="flex items-center space-x-2">
      <div
        className={`w-12 h-2 rounded-full overflow-hidden ${
          trend === 'up'
            ? 'bg-lime-100'
            : trend === 'down'
              ? 'bg-red-100'
              : 'bg-gray-100'
        }`}
      >
        <div
          className={`h-full rounded-full transition-all duration-1000 ease-out ${
            trend === 'up'
              ? 'bg-gradient-to-r from-lime-400 to-lime-600'
              : trend === 'down'
                ? 'bg-gradient-to-r from-red-400 to-red-600'
                : 'bg-gradient-to-r from-gray-400 to-gray-600'
          }`}
          style={{ width: `${Math.min(Math.abs(changeValue), 100)}%` }}
        />
      </div>
      <div
        className={`text-xs font-medium ${
          trend === 'up'
            ? 'text-lime-600'
            : trend === 'down'
              ? 'text-red-600'
              : 'text-gray-600'
        }`}
      >
        {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'} {change}
      </div>
    </div>
  );
};

const getTrendIcon = (trend) => {
  console.log('trend', trend);
  switch (trend) {
    case 'up':
      return <ArrowUpRight className="w-3 h-3" />;
    case 'down':
      return <ArrowDownRight className="w-3 h-3" />;
    default:
      return <Minus className="w-3 h-3" />;
  }
};

const Dashboard = () => {
  const currentUserRole = getUserViewAsRole();
  const currentChart = useRef(null);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const [dashboardFilter, setDashboardFilter] = useState({ ...initialFilter });
  const countryList = countries.map((country) => ({
    text: country.name,
    value: country.name,
  }));
  const [keywordData, setKeywordData] = useState([]);
  const [loadingKeyWord, setLoadingKeyword] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const [users, setUsers] = useState([]);
  const [liveFeedType, setLiveFeedType] = useState();
  const [liveFeedStart, setLiveFeedStart] = useState(0);
  const [newLiveFeedData, setNewLiveFeedData] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);

  // Synced leads detail drawer
  const [isShowSyncedLeadDetail, setIsShowSyncedLeadDetail] = useState(false);
  const showSyncedLeadDetail = () => setIsShowSyncedLeadDetail(true);
  const hideSyncedLeadDetail = () => setIsShowSyncedLeadDetail(false);

  // Synced leads detail drawer
  const [isShowContactAddedDetail, setIsShowContactAddedDetail] =
    useState(false);
  const showContactAddedDetail = () => setIsShowContactAddedDetail(true);
  const hideContactAddedDetail = () => setIsShowContactAddedDetail(false);

  // Companies added detail drawer
  const [isShowCompanyAddedDetail, setIsShowCompanyAddedDetail] =
    useState(false);
  const showCompanyAddedDetail = () => setIsShowCompanyAddedDetail(true);
  const hideCompanyAddedDetail = () => setIsShowCompanyAddedDetail(false);

  const [chartLabels, setChartLabels] = useState([]);

  const {
    data,
    isFetching,
    refetch: refetchLeadStats,
  } = useQuery(
    ['MANUAL_LEAD_SYNCED_LEAD_COUNT'],
    async () => {
      const { data } = await getLeadCounts(dashboardFilter);
      const [countStatLead, countContacts, countEmailSent, countCompany] =
        await Promise.all([
          handleSequenceActive(dashboardFilter),
          handleGetCountContact(dashboardFilter),
          handleGetEmailSent(dashboardFilter),
          handleGetCountCompany(dashboardFilter),
        ]);

      let listMonthValueOfKeys = [];
      const monthsNeeded = getMonthsNeeded();
      if (data?.result?.monthData?.length > 0) {
        const monthDataMap = data.result.monthData.reduce((acc, item) => {
          const monthKey = item.month.substring(0, 7);
          acc[monthKey] = item;
          return acc;
        }, {});

        listMonthValueOfKeys = Object.keys(data.result.monthData[0])
          .filter((key) => key !== 'month')
          .map((key) => ({
            key,
            [key]: monthsNeeded.map((month) => {
              const dataForMonth = monthDataMap[month] || {};
              return dataForMonth[key] || '0';
            }),
          }))
          .reduce((obj, item) => {
            obj[item.key] = item;
            return obj;
          }, {});
      }
      // Get list dataset
      const syncedLeadCountList =
        listMonthValueOfKeys?.['syncedLeadCount']?.['syncedLeadCount'] || [];
      const emailSentsCountList =
        listMonthValueOfKeys?.['emailSentsCount']?.['emailSentsCount'] || [];
      const newEmailFoundCountList =
        listMonthValueOfKeys?.['newEmailFoundCount']?.['newEmailFoundCount'] ||
        [];
      const manualLeadCountList =
        listMonthValueOfKeys?.['manualLeadCount']?.['manualLeadCount'] || [];

      // Calculate range for each dataset
      // compare first value and second value in list dataset.

      const rangeSyncedLeadCountList = Math.round(
        ((syncedLeadCountList[syncedLeadCountList?.length - 1] -
          syncedLeadCountList[syncedLeadCountList?.length - 2]) *
          100) /
          syncedLeadCountList[syncedLeadCountList?.length - 2] ?? 1
      );
      const rangeEmailSentsCountList = Math.round(
        ((Number(emailSentsCountList[emailSentsCountList?.length - 1]) -
          Number(emailSentsCountList[emailSentsCountList?.length - 2])) *
          100) /
          Number(emailSentsCountList[emailSentsCountList?.length - 2]) ?? 1
      );

      const rangeNewEmailFoundCountList = Math.round(
        ((newEmailFoundCountList[0] - newEmailFoundCountList[1]) * 100) /
          newEmailFoundCountList[1] ?? 1
      );

      const rangeManualLeadCountList = Math.round(
        ((manualLeadCountList[manualLeadCountList?.length - 1] -
          manualLeadCountList[manualLeadCountList?.length - 2]) *
          100) /
          manualLeadCountList[manualLeadCountList?.length - 2] ?? 1
      );

      const monthCountContactsDataMap = countContacts?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const contactDataList = monthsNeeded?.map(
        (month) => monthCountContactsDataMap[month] || '0'
      );

      const rangeContactAddedCountList = Math.round(
        ((Number(contactDataList?.[contactDataList?.length - 1]) -
          Number(contactDataList?.[contactDataList?.length - 2])) *
          100) /
          (Number(contactDataList?.[contactDataList?.length - 2]) > 0
            ? Number(contactDataList?.[contactDataList?.length - 2])
            : 1)
      );
      const monthCountEmailSentDataMap = countEmailSent?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const emailSentDataList = monthsNeeded?.map(
        (month) => monthCountEmailSentDataMap[month] || '0'
      );

      const rangeEmailSentCountList = Math.round(
        ((Number(emailSentDataList?.[emailSentDataList?.length - 1]) -
          Number(emailSentDataList?.[emailSentDataList?.length - 2])) *
          100) /
          (Number(emailSentDataList?.[emailSentDataList?.length - 2]) > 0
            ? Number(emailSentDataList?.[emailSentDataList?.length - 2])
            : 1)
      );
      const monthNewLeadsDataMap = countStatLead?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const newLeadList = monthsNeeded?.map(
        (month) => monthNewLeadsDataMap[month] || '0'
      );

      const rangeNewLeadAdded = Math.round(
        ((Number(newLeadList?.[newLeadList?.length - 1]) -
          Number(newLeadList?.[newLeadList?.length - 2])) *
          100) /
          (Number(newLeadList?.[newLeadList?.length - 2]) > 0
            ? Number(newLeadList?.[newLeadList?.length - 2])
            : 1)
      );

      const monthCountCompanyDataMap = countCompany?.monthData.reduce(
        (acc, item) => {
          const monthKey = dayjs(item.month).format('YYYY-MM');
          acc[monthKey] = item.count;
          return acc;
        },
        {}
      );

      const companyDataList = monthsNeeded?.map(
        (month) => monthCountCompanyDataMap[month] || '0'
      );

      const rangeCompanyAddedCountList = Math.round(
        ((Number(companyDataList?.[companyDataList?.length - 1]) -
          Number(companyDataList?.[companyDataList?.length - 2])) *
          100) /
          (Number(companyDataList?.[companyDataList?.length - 2]) > 0
            ? Number(companyDataList?.[companyDataList?.length - 2])
            : 1)
      );

      let initialChartBarDataSet = [
        {
          key: 'syncedLeadCount',
          icon: Database,
          name: 'Synced Leads',
          total:
            +data?.result?.totalData['syncedLeadCount'] +
            +data?.result?.totalData['manualLeadCount'],
          isIncrease:
            syncedLeadCountList[syncedLeadCountList?.length - 1] +
              manualLeadCountList[manualLeadCountList?.length - 1] -
              (syncedLeadCountList[syncedLeadCountList?.length - 2] +
                manualLeadCountList[manualLeadCountList?.length - 2]) >
            0,
          range: `${Math.abs(rangeSyncedLeadCountList + rangeManualLeadCountList)}%`,
          data: [
            {
              data: [...syncedLeadCountList].map(
                (item, index) =>
                  Number(item) + Number(manualLeadCountList[index])
              ),
              backgroundColor: '#EFEFFE',
              hoverBackgroundColor: '#5F63F2',
              label: 'lead(s)',
              barPercentage: 1,
            },
          ],
          onClick: () => showSyncedLeadDetail(),
          color: '!text-emerald-600',
          bgColor: 'bg-emerald-50',
          iconBgColor: 'bg-emerald-100',
          gradientFrom: 'from-emerald-500',
          gradientTo: 'to-emerald-600',
        },
        {
          key: 'emailSentsCount',
          name: 'Emails Sent',
          icon: Mail,
          total: +countEmailSent?.totalCount,
          isIncrease:
            Number(emailSentDataList?.[emailSentDataList?.length - 1]) -
              Number(emailSentDataList?.[emailSentDataList?.length - 2]) >
            0,
          range: `${Math.abs(rangeEmailSentCountList)}%`,
          data: [
            {
              data: emailSentDataList,
              backgroundColor: '#FFF0F6',
              hoverBackgroundColor: '#FF69A5',
              label: 'email(s)',
              barPercentage: 1,
            },
          ],
          onClick: () => {},
          color: '!text-cyan-600',
          bgColor: 'bg-cyan-50',
          iconBgColor: 'bg-cyan-100',
          gradientFrom: 'from-cyan-500',
          gradientTo: 'to-cyan-600',
        },
        {
          key: 'contactAddedCount',
          name: 'Contacts Added',
          total: +countContacts?.totalCount,
          icon: UserPlus,
          isIncrease:
            Number(contactDataList?.[contactDataList?.length - 1]) -
              Number(contactDataList?.[contactDataList?.length - 2]) >
            0,
          range: `${Math.abs(rangeContactAddedCountList)}%`,
          data: [
            {
              data: contactDataList,
              backgroundColor: '#E9F5FF',
              hoverBackgroundColor: '#2C99FF',
              label: 'contact(s)',
              barPercentage: 1,
            },
          ],
          onClick: () => showContactAddedDetail(),
          color: '!text-purple-600',
          bgColor: 'bg-purple-50',
          iconBgColor: 'bg-purple-100',
          gradientFrom: 'from-purple-500',
          gradientTo: 'to-purple-600',
        },
        // Add new
        {
          key: 'activeSequenceCount',
          name: 'Active Sequences',
          icon: Target,
          total: +countStatLead?.totalCount,
          isIncrease:
            newLeadList[newLeadList?.length - 1] -
              newLeadList?.[newLeadList?.length - 2] >
            0,
          range: `${Math.abs(rangeNewLeadAdded)}%`,
          data: [
            {
              data: manualLeadCountList,
              backgroundColor: '#A53860',
              hoverBackgroundColor: '#670D2F',
              label: 'sequence(s)',
              barPercentage: 1,
            },
          ],
          onClick: () => window.open('/sequence?status=LIVE', '_blank'),
          color: '!text-orange-600',
          bgColor: 'bg-orange-50',
          iconBgColor: 'bg-orange-100',
          gradientFrom: 'from-orange-500',
          gradientTo: 'to-orange-600',
        },
        {
          key: 'companyAddedCount',
          name: 'Companies Added',
          icon: Building2,
          total: +countCompany?.totalCount || 0,
          isIncrease:
            companyDataList[companyDataList?.length - 1] -
              companyDataList[companyDataList?.length - 2] >
            0,
          range: `${Math.abs(rangeCompanyAddedCountList || 0)}%`,
          data: [
            {
              data: companyDataList,
              backgroundColor: '#CEDF9F',
              hoverBackgroundColor: '#A1D6B2',
              label: 'item(s)',
              barPercentage: 1,
            },
          ],
          onClick: () => showCompanyAddedDetail(),
          color: '!text-rose-600',
          bgColor: 'bg-rose-50',
          iconBgColor: 'bg-rose-100',
          gradientFrom: 'from-rose-500',
          gradientTo: 'to-rose-600',
        },
      ];

      const previousMonths = [];

      for (let i = 7; i >= 0; i--) {
        let monthName = moment(new Date())
          .subtract(i, 'month')
          .startOf('month')
          .format('MMM');
        previousMonths.push(monthName);
      }
      setChartLabels([...previousMonths]);

      return initialChartBarDataSet;
    },
    {
      enabled: true,
      initialData: [],
    }
  );

  const handleGetDataUser = async () => {
    const { data } = await getUsers();
    setUsers(data);
  };

  const {
    data: potentialLeadValue,
    isFetching: isFetchingPotentialLeads,
    refetch: refetchPotentialLeadValue,
  } = useQuery(['GET_POTENTIAL_LEAD_RETURN', dashboardFilter], {
    queryFn: async () => {
      const { data } = await getPotentialLeadReturn(dashboardFilter);

      const labels = [];

      for (let i = 1; i <= 12; i += 1) {
        let monthName = moment(new Date())
          .subtract(i, 'month')
          .startOf('month')
          .format('MMM');
        labels.push(monthName);
      }

      const minRangeData = data.result?.monthData
        .map((item) => item.minRange)
        .concat(new Array(12).fill(0).map(Number))
        .slice(0, 12)
        .reverse();
      const maxRangeData = data.result?.monthData
        .map((item) => item.maxRange)
        .concat(new Array(12).fill(0).map(Number))
        .slice(0, 12)
        .reverse();

      const performanceDatasets = [
        {
          data: maxRangeData,
          borderColor: '#5F63F2',
          borderWidth: 4,
          fill: true,
          backgroundColor: () => {
            const performanceEl = document?.getElementById('performance');
            return chartLinearGradient(performanceEl, 300, {
              start: '#5F63F230',
              end: '#ffffff05',
            });
          },
          label: 'Leads Value',
          pointStyle: 'circle',
          pointRadius: '0',
          hoverRadius: '9',
          pointBorderColor: '#fff',
          pointBackgroundColor: '#5F63F2',
          hoverBorderWidth: 5,
          amount: `£${numberWithCommas(data.result.totalData.maxRange)}`,
          amountClass: 'current-amount',
        },
        // {
        //   data: minRangeData,
        //   borderColor: '#C6D0DC',
        //   borderWidth: 2,
        //   fill: false,
        //   backgroundColor: '#00173750',
        //   label: 'Min. Leads Value',
        //   borderDash: [3, 3],
        //   pointRadius: '0',
        //   hoverRadius: '0',
        //   amount: `£${numberWithCommas(data.result.totalData.minRange)}`,
        //   amountClass: 'prev-amount',
        // },
      ];
      return { performanceDatasets, labels: labels.reverse() };
    },
    initialData: { label: [], performanceDatasets: [] },
  });

  const {
    data: liveFeedData,
    isFetching: isFetchingLiveFeedData,
    refetch: refreshLiveFeedData,
  } = useQuery(['GET_LIVE_FEED_DATA'], {
    queryFn: async ({ queryKey }) => {
      const offset = queryKey[1] || liveFeedStart * 20;
      const { data } = await getLiveFeed({
        ...dashboardFilter,
        type: liveFeedType,
        offset,
      });

      if (offset === 0) {
        setNewLiveFeedData(data?.result?.items || []);
      } else {
        setNewLiveFeedData([
          ...newLiveFeedData,
          ...(data?.result?.items || []),
        ]);
      }

      return data?.result?.items || [];
    },
    initialData: [],
    enabled: false,
  });

  const {
    data: myLeadsCount,
    isFetching: isFetchingMyLeadsCount,
    refetch: refetchMyLeadCount,
  } = useQuery(['MY_LEADS_COUNT'], {
    queryFn: async () => {
      const { data } = await getCountForEachLeadStatus(dashboardFilter);
      return data?.result || [];
    },
    initialData: [],
  });

  useEffect(() => {
    refreshLiveFeedData();
  }, [liveFeedStart, dashboardFilter]);

  useEffect(() => {
    if (liveFeedType && liveFeedStart === 0) {
      refreshLiveFeedData();
    }
  }, [liveFeedType]);

  const handleSelectDateRange = (dateRange) => {
    if (dateRange) {
      let [fromDate, toDate] = dateRange
        .split(' - ')
        .map((dateStr) => dayjs(dateStr));
      fromDate = fromDate.startOf('day');
      toDate = toDate.endOf('day');
      fromDate = fromDate.format('YYYY-MM-DD HH:mm:ss');
      toDate = toDate.format('YYYY-MM-DD HH:mm:ss');
      setDashboardFilter({ ...dashboardFilter, fromDate, toDate, dateRange });
      setLiveFeedStart(0);
      setOpenPopup(false);
    } else {
      setDashboardFilter({
        ...dashboardFilter,
        fromDate: null,
        toDate: null,
        dateRange: null,
      });
      setOpenPopup(false);
    }
  };

  const handleGetKeyWords = async (page = 0) => {
    try {
      setLoadingKeyword(true);
      const { data } = await getStatsKeyword(dashboardFilter, page);
      if (page === 0) {
        setKeywordData(data?.result?.items);
      } else {
        setKeywordData([...keywordData, ...data?.result?.items]);
      }
      setLoadingKeyword(false);
    } catch (err) {
      setLoadingKeyword(false);
      notification.error({
        message: 'Error fetching keywords',
      });
    }
  };

  const handleGetStatsLead = async () => {
    try {
      const { data } = await getStatsNewLead(dashboardFilter);
      return data?.result;
    } catch (err) {
      notification.error({
        message: 'Error fetching data',
      });
    }
  };

  const handleGetCountContact = async () => {
    try {
      const { data } = await getContactAdded(dashboardFilter);
      return data?.result;
    } catch (err) {
      notification.error({
        message: 'Error fetching data',
      });
    }
  };

  const handleGetCountCompany = async () => {
    try {
      const { data } = await getCompaniesAdded(dashboardFilter);
      return data?.result;
    } catch (err) {
      notification.error({
        message: 'Error fetching data',
      });
    }
  };

  const handleSequenceActive = async () => {
    try {
      const { data } = await getActiveSequence(dashboardFilter);
      return data?.result;
    } catch (err) {
      notification.error({
        message: 'Error fetching data',
      });
    }
  };

  const handleGetEmailSent = async () => {
    try {
      const { data } = await getEmailSent(dashboardFilter);
      return data?.result;
    } catch (err) {
      notification.error({
        message: 'Error fetching data',
      });
    }
  };

  useEffect(() => {
    handleGetKeyWords();
    // refreshLiveFeedData()
    refetchLeadStats();
    refetchMyLeadCount();
  }, [dashboardFilter]);

  useEffect(() => {
    handleGetDataUser();
  }, []);

  const handleExportChart = async (type = EXPORT_TYPE.IMAGE) => {
    setExportLoading(true);
    const restyledKeyWordChart = document.getElementById('keywords-table');
    const restyledLiveFeedChart = document.getElementById('live-feed-table');
    if (restyledKeyWordChart) {
      restyledKeyWordChart.classList.remove('overflow-y-auto');
      restyledKeyWordChart.classList.remove('max-h-[26.5rem]');
    }
    if (restyledLiveFeedChart) {
      restyledLiveFeedChart.classList.remove('overflow-y-auto');
      restyledLiveFeedChart.classList.remove('max-h-[26.5rem]');
    }
    toPng(currentChart.current, { cacheBust: true })
      .then((dataUrl) => {
        const fileName = `Report_${dayjs().format('DD-MM-YYYYTHH:mm:ss')}`;

        if (restyledKeyWordChart) {
          restyledKeyWordChart.classList.add('overflow-y-auto');
          restyledKeyWordChart.classList.add('max-h-[26.5rem]');
        }
        if (restyledLiveFeedChart) {
          restyledLiveFeedChart.classList.add('overflow-y-auto');
          restyledLiveFeedChart.classList.add('max-h-[26.5rem]');
        }

        if (type === EXPORT_TYPE.PDF || type === EXPORT_TYPE.PRINT) {
          const pdf = new jsPDF();
          const imgProps = pdf.getImageProperties(dataUrl);
          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
          pdf.addImage(dataUrl, 'PNG', 0, 0, pdfWidth, pdfHeight);
          if (type === EXPORT_TYPE.PRINT) {
            // Later
            // window.open(pdf.link, 'PRINT');
          } else {
            pdf.save(`${fileName}.pdf`);
          }

          return;
        }

        const link = document.createElement('a');
        link.download = `${fileName}.png`;
        link.href = dataUrl;
        link.click();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => setExportLoading(false));
  };

  const items = [
    // {
    //   key: 'print',
    //   label: <div className="font-medium">Print</div>,
    //   icon: <PrinterOutlined className="font-semibold !text-sm" />,
    //   onClick: () => {
    //     handleExportChart(EXPORT_TYPE.PRINT);
    //   },
    // },
    {
      key: 'pdf',
      label: <div className="font-medium">PDF</div>,
      icon: <FilePdfOutlined className="font-semibold !text-sm" />,
      onClick: () => handleExportChart(EXPORT_TYPE.PDF),
    },
    {
      key: 'image',
      label: <div className="font-medium">Image</div>,
      icon: <FileImageOutlined className="font-semibold !text-sm" />,
      onClick: () => handleExportChart(EXPORT_TYPE.IMAGE),
    },
  ];

  const onClickView = ({ key }) => {
    if (selectedKeys.includes(key)) {
      const newSelectedKeys = [...selectedKeys].filter((item) => item !== key);
      setSelectedKeys([...newSelectedKeys]);
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
  };

  return (
    <div className="h-screen w-full relative">
      <Container>
        <div className="flex justify-between items-center px-3 pb-4 font-Inter">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2 flex items-center">
              Monitor your working performance here.
              <Activity className="w-5 h-5 ml-2 text-cyan-500" />
            </h2>
            <div className="w-16 h-1 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full"></div>
          </div>
          <div className="flex items-center gap-2">
            {/* <Select
              placeholder={COMMON_STRINGS.SELECT_A_COUNTRY}
              options={countryList}
              showSearch
              suffixIcon={<AimOutlined />}
              value={dashboardFilter.country}
              className="min-w-[5rem] w-fit"
              onSelect={(country) => {
                setDashboardFilter({ ...dashboardFilter, country });
                setLiveFeedStart(0);
              }}
            /> */}
            {/* {dashboardFilter.country && (
              <DeleteOutlined
                onClick={() => {
                  setDashboardFilter({ ...dashboardFilter, country: null });
                  setLiveFeedStart(0);
                }}
              />
            )} */}
            {permissions.viewConsultantReports.includes(currentUserRole) && (
              <Select
                placeholder="Select a consultant"
                options={_map(users, (option) => ({
                  value: _get(option, 'username') || _get(option, 'email'),
                  key: _get(option, 'id'),
                  id: _get(option, 'id'),
                  username: _get(option, 'username'),
                  email: _get(option, 'email'),
                }))}
                value={
                  users?.find((item) => item?.id === dashboardFilter.consultant)
                    ?.username ||
                  users?.find((item) => item?.id === dashboardFilter.consultant)
                    ?.email
                }
                optionRender={(option) => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Avatar
                        style={{
                          backgroundColor: getColorHexFromName(
                            _get(option.data, 'username') ||
                              _get(option.data, 'email')
                          ),
                          verticalAlign: 'middle',
                        }}
                      >
                        {stringAvatar(
                          _get(option.data, 'username') ||
                            _get(option.data, 'email')
                        )}
                      </Avatar>
                      &ensp;
                      <Typography.Text
                        style={{
                          width: '150px',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {_get(option.data, 'username') ||
                          _get(option.data, 'email')}
                      </Typography.Text>
                    </div>
                  );
                }}
                showSearch
                // value={users.find(item => item?.id === dashboardFilter.consultant)?.username}
                suffixIcon={<UserOutlined />}
                className="min-w-[6rem] w-[14rem]"
                onSelect={async (value, option) => {
                  setDashboardFilter({
                    ...dashboardFilter,
                    consultant: option?.id,
                  });
                  setLiveFeedStart(0);
                }}
              />
            )}
            {dashboardFilter.consultant && (
              <DeleteOutlined
                onClick={() => {
                  setDashboardFilter({ ...dashboardFilter, consultant: null });
                  setLiveFeedStart(0);
                }}
              />
            )}
            <Popover
              placement="bottomRight"
              title="Search by Calendar"
              openStatus={openPopup}
              content={
                <DateRangePickerOne
                  handleSelectDateRange={handleSelectDateRange}
                  setOpenPopup={setOpenPopup}
                />
              }
              action="click"
            >
              <Button
                onClick={(e) => {
                  e.stopPropagation(), setOpenPopup(true);
                }}
                // type="primary"
                className="gap-2 py-3 flex items-center bg-[#fff] font-semibold hover:text-[#2684c7]"
              >
                <CalendarOutlined className="font-semibold" />
                <p className="font-Inter">
                  {dashboardFilter?.dateRange || 'Calendar'}
                </p>
              </Button>
            </Popover>
            {dashboardFilter.dateRange && (
              <DeleteOutlined
                onClick={() => {
                  setDashboardFilter({
                    ...dashboardFilter,
                    dateRange: null,
                    fromDate: null,
                    toDate: null,
                  });
                  setLiveFeedStart(0);
                }}
              />
            )}
            {/* <Dropdown
              menu={{
                items,
              }}
              placement="bottomCenter"
              arrow
              trigger={'click'}
              
            >
              <Button
                type="default"
                className="bg-white"
                loading={exportLoading}
                // onClick={handleExportChart}
                icon={<ExportOutlined />}
              >
                Export
              </Button>
            </Dropdown> */}

            <Dropdown.Button
              className="bg-white w-fit !p-0"
              placement="bottomCenter"
              arrow
              trigger={'click'}
              menu={{
                items: keyItems,
                selectable: true,
                selectedKeys,
                onClick: onClickView,
              }}
              icon={
                exportLoading ? (
                  <Spin className="font-medium" />
                ) : (
                  <EyeInvisibleOutlined className="font-medium" />
                )
              }
              disabled={exportLoading}
            >
              <Dropdown
                menu={{
                  items,
                }}
                placement="bottomCenter"
                arrow
                trigger={'click'}
              >
                <div className="flex items-center gap-2 justify-center font-semibold">
                  <ExportOutlined className="" />
                  <div className="font-Inter">Export</div>
                </div>
              </Dropdown>
            </Dropdown.Button>
            <Dropdown
              trigger={'click'}
              menu={{
                items: [
                  {
                    key: 'Search',
                    label: (
                      <a className="font-Inter" href="/search">
                        Search
                      </a>
                    ),
                  },
                  {
                    key: 'Leads',
                    label: (
                      <a className="font-Inter" href="/manual-leads">
                        Leads
                      </a>
                    ),
                  },
                  {
                    key: 'Sequence',
                    label: (
                      <a className="font-Inter" href="/sequence">
                        Sequence
                      </a>
                    ),
                  },
                  {
                    key: 'Contact',
                    label: (
                      <a className="font-Inter" href="/email-finder">
                        Contact
                      </a>
                    ),
                  },
                ],
              }}
              placement="bottom"
              arrow
            >
              <button
                onClick={(e) => e.stopPropagation()}
                type="primary"
                className="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary hover:bg-primary/90 h-9 rounded-md px-3 bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-purple-600 text-white shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                // icon={<File className='text-white font-semibold' />}
              >
                <FileText
                  size={18}
                  className="text-white font-semibold text-sm"
                />
                <p className="font-Inter text-white">Reports</p>
              </button>
            </Dropdown>
          </div>
        </div>
        {isFetching && (
          <div className="flex gap-3 p-4">
            {Array.from({
              length: 5,
            }).map((_, i) => (
              <Skeleton active />
            ))}
          </div>
        )}
        <div ref={currentChart} className="p-4">
          {!isFetching && (
            <div
              className={clsx(
                'grid grid-cols-1 gap-5 lg:grid-cols-2 2xl:grid-cols-5 mb-4'
              )}
            >
              {data?.map(
                (dataSet) =>
                  !selectedKeys.includes(dataSet?.key) && (
                    <div
                      key={dataSet?.key}
                      className={`rounded-lg border text-card-foreground shadow-lg border border-gray-200/50 shadow-lg bg-white/80 backdrop-blur-sm transition-all duration-500 hover:shadow-xl hover:shadow-gray-200/50 hover:-translate-y-1 ${'cursor-pointer hover:border-gray-300/50'} group relative overflow-hidden`}
                      onClick={dataSet?.onClick}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="p-6 relative z-10">
                        <div className="flex items-center justify-between mb-4">
                          <div
                            className={`p-2 rounded-xl ${dataSet.bgColor} group-hover:scale-110 transition-transform duration-300 shadow-sm`}
                          >
                            <dataSet.icon
                              className={`w-5 h-5 ${dataSet.color} group-hover:text-white transition-colors duration-300`}
                            />
                          </div>
                          <div
                            className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 shadow-sm ${
                              dataSet.isIncrease
                                ? 'bg-green-100 text-green-700'
                                : 'bg-red-100 text-red-700'
                            }`}
                          >
                            {getTrendIcon(dataSet.isIncrease ? 'up' : 'down')}
                            <span>{dataSet.range}</span>
                          </div>
                        </div>
                        <div>
                          <div
                            className={clsx(
                              `text-3xl font-bold mb-1 group-hover:scale-105 transition-transform duration-300`,
                              dataSet.color
                            )}
                          >
                            {dataSet.total}
                          </div>
                          <div className="text-sm font-semibold text-gray-700 mb-3">
                            {dataSet.name}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-700">
                            Since last month
                          </div>
                          {renderTrendIndicator(
                            !dataSet.isIncrease ? 'down' : 'up',
                            dataSet.range
                          )}
                        </div>
                      </div>
                    </div>
                    // <Col
                    //   className="w-full transition-transform duration-300 hover:scale-105 cursor-pointer"
                    //   key={dataSet?.key}
                    //   onClick={dataSet?.onClick}
                    // >
                    //   <Cards headless>
                    //     <EChartCard style={{ minHeight: '130px' }}>
                    //       <div className="card-chunk">
                    //         <CardBarChart2>
                    //           <Heading
                    //             as="h1"
                    //             className="font-Inter text-cyan-600"
                    //           >
                    //             {numberWithCommas(dataSet.total ?? 0)}
                    //           </Heading>
                    //           <span
                    //             className="font-semibold line-clamp-1 w-[10rem] !text-cyan-600"
                    //             title={dataSet.name}
                    //           >
                    //             {dataSet.name}
                    //           </span>
                    //           <div>
                    //             <p>
                    //               {dataSet.range !== '0%' &&
                    //                 !dataSet.range.includes('Infinity') && (
                    //                   <span
                    //                     className={`font-Inter ${dataSet.isIncrease ? 'growth-upward' : 'growth-downward'}`}
                    //                   >
                    //                     {dataSet.isIncrease ? (
                    //                       <ArrowUpOutlined />
                    //                     ) : (
                    //                       <ArrowDownOutlined />
                    //                     )}{' '}
                    //                     {dataSet.range === 'NaN%'
                    //                       ? `0%`
                    //                       : dataSet.range}
                    //                   </span>
                    //                 )}
                    //               {dataSet.range == '0%' && (
                    //                 <span
                    //                   className={`font-Inter ${dataSet.isIncrease ? 'growth-upward' : 'growth-downward'}`}
                    //                 >
                    //                   {dataSet.isIncrease ? (
                    //                     <ArrowUpOutlined />
                    //                   ) : (
                    //                     <ArrowDownOutlined />
                    //                   )}{' '}
                    //                   {dataSet.range === 'NaN%'
                    //                     ? `0%`
                    //                     : dataSet.range}
                    //                 </span>
                    //               )}
                    //               {dataSet.range === 'Infinity%' && (
                    //                 <span
                    //                   style={{
                    //                     height: '40px',
                    //                   }}
                    //                 ></span>
                    //               )}
                    //               {/* {dataSet.range.includes('Infinity') && (
                    //           <span
                    //             className={`font-Inter pr-3 !text-yellow-400`}
                    //           >
                    //             Infinity
                    //           </span>
                    //         )} */}
                    //               <span
                    //                 className={`font-Inter`}
                    //                 style={{ width: '100%' }}
                    //               >
                    //                 Since last month
                    //               </span>
                    //             </p>
                    //           </div>
                    //         </CardBarChart2>
                    //       </div>
                    //       {dataSet?.key !== 'activeSequenceCount' && (
                    //         <div className="card-chunk">
                    //           <ChartjsBarChartTransparent
                    //             labels={chartLabels}
                    //             datasets={dataSet.data}
                    //             options={chartOptions}
                    //           />
                    //         </div>
                    //       )}
                    //     </EChartCard>
                    //   </Cards>
                    // </Col>
                  )
              )}
            </div>
          )}
          <Row gutter={25} className="flex h-full">
            {!selectedKeys.includes('sequence-stats') && (
              <Col xxl={8} xs={24} className="flex mb-6">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <SequenceDeals dashboardFilter={dashboardFilter} />
                </Suspense>
              </Col>
            )}

            {!selectedKeys.includes('total-leads-value') && (
              <Col xxl={8} xs={24} className="flex mb-6 w-full">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <MinMaxLeadsValue
                    title="Total Leads Value"
                    preIsLoading={isFetchingPotentialLeads}
                    potentialLeadValue={potentialLeadValue}
                    refetch={refetchPotentialLeadValue}
                  />
                </Suspense>
              </Col>
            )}
            {!selectedKeys.includes('linkedin-key-metrics') && (
              <Col xxl={8} xs={24} className="flex mb-6">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <LinkedinKeyMetrics dashboardFilter={dashboardFilter} />
                </Suspense>
              </Col>
            )}
          </Row>

          <Row gutter={25} className="mb-6">
            {!selectedKeys.includes('my-leads-statics') && (
              <Col xxl={8} xs={24} className="flex">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <MyLeadsStatsBySheet
                    myLeadsCount={myLeadsCount}
                    loading={isFetchingMyLeadsCount}
                  />
                </Suspense>
              </Col>
            )}
            {!selectedKeys.includes('in-demand-job-titles') && (
              <Col xxl={8} xs={24} className="flex">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <DemandJobTitle
                    keywordData={keywordData}
                    handleGetKeyWords={handleGetKeyWords}
                    loadingKeyWord={loadingKeyWord}
                  />
                </Suspense>
              </Col>
            )}
            {!selectedKeys.includes('live-feed') && (
              <Col xxl={8} xs={24} className="flex">
                <Suspense
                  fallback={
                    <Cards headless>
                      <Skeleton active />
                    </Cards>
                  }
                >
                  <LiveFeed
                    liveFeedData={newLiveFeedData}
                    loading={isFetchingLiveFeedData}
                    refresh={refreshLiveFeedData}
                    setLiveFeedType={setLiveFeedType}
                    setLiveFeedStart={setLiveFeedStart}
                    liveFeedStart={liveFeedStart}
                  />
                </Suspense>
              </Col>
            )}
          </Row>
        </div>
      </Container>

      {/* Synced leads detail drawer */}
      <Drawer
        title="Synced leads"
        closable={{ 'aria-label': 'Close Button' }}
        width={window?.innerWidth > 768 ? '60%' : '100%'}
        onClose={hideSyncedLeadDetail}
        open={isShowSyncedLeadDetail}
      >
        <SyncedLeadDetail />
      </Drawer>

      {/* Contacts Added detail drawer */}
      <ReStyledDrawer
        title={
          <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200/50 bg-gradient-to-r from-purple-50 to-cyan-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-cyan-600 rounded-lg shadow-lg">
                  <UserPlus className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Contacts Added
                  </h2>
                  <p className="text-sm text-gray-600">
                    Manage your recent contacts and leads
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div
                  variant="outline"
                  className="px-3 py-1 rounded-2xl border font-semibold text-xs bg-gradient-to-r from-purple-50 to-cyan-50 border-purple-200 text-purple-700"
                >
                  {data?.[2]?.total} contacts
                </div>
                <Button
                  variant="ghost"
                  size="small"
                  onClick={hideContactAddedDetail}
                  className="text-gray-500 hover:text-gray-700 hover:bg-white/80"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        }
        closable={false}
        width={window?.innerWidth > 768 ? '45%' : '100%'}
        // onClose={hideContactAddedDetail}
        open={isShowContactAddedDetail}
        destroyOnClose
      >
        <ContactAddedDetail total={data?.[2]?.total} />
      </ReStyledDrawer>

      {/* Companies Added detail drawer */}
      <Drawer
        title="Companies Added"
        closable={{ 'aria-label': 'Close Button' }}
        width={window?.innerWidth > 768 ? '60%' : '100%'}
        onClose={hideCompanyAddedDetail}
        open={isShowCompanyAddedDetail}
        destroyOnClose
      >
        <CompanyAddedDetail />
      </Drawer>
    </div>
  );
};
export default Dashboard;
