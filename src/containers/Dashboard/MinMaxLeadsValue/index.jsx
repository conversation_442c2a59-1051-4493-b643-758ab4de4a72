import React, { useState, useEffect } from 'react';
import { Skeleton } from 'antd';
import PropTypes from 'prop-types';
import { Cards } from '../../../components/Cards';
import { RevenueWrapper } from './style';
import { ChartjsAreaChart } from '../../../components/Charts';
import { customTooltips } from '../../../utils/chart.utils';
import { nFormatter } from '../../../utils/common';
import EditableNumber from '../../../components/EditableNumberComponent/EditableNumber';
import { useQuery } from '@tanstack/react-query';
import { getPotentialLeadPercentValue } from '../../../services/jobLeads';
import { savePotentialLeadValue } from '../../../store/common';
import { useDispatch } from 'react-redux';
import { currencyFormatter } from '../../../helpers/util';

const TotalRevenue = ({ title, preIsLoading, potentialLeadValue, refetch }) => {
  const { performanceDatasets, labels } = potentialLeadValue;
  const dispatch = useDispatch();

  const { data: currentValue } = useQuery(['POTENTIAL_RETURN_PERCENT'], {
    queryFn: async () => {
      const { data } = await getPotentialLeadPercentValue();

      const { potentialLeadValue } = data?.result;

      return potentialLeadValue;
    },
  });

  return (
    <div className="h-full w-full rounded-lg border bg-card text-card-foreground border border-gray-200/50 shadow-lg bg-gradient-to-br from-cyan-50/50 to-white backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-cyan-600 flex items-center">
            Total Leads Value
            <div className="ml-2 w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
          </h3>
          <EditableNumber headless refetchData={refetch} />
        </div>
        {preIsLoading ? (
          <div className="sd-spin flex w-full h-full items-center justify-center">
            <Skeleton active />
          </div>
        ) : (
          <>
            {console.log(performanceDatasets)}
            <div className="text-3xl font-bold text-gray-900 mb-2">
              {performanceDatasets?.[0]?.amount || 0}
            </div>
            <div className="text-sm text-cyan-600 mb-4">Leads Value</div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  Potential Lead Value Percentage
                </span>
                <span className="text-sm font-semibold">{currentValue}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-cyan-500 to-purple-600 h-3 rounded-full transition-all duration-1000 ease-out"
                  style={{ width: `${currentValue}%` }}
                ></div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
    // <RevenueWrapper>
    //   {/* {performanceState !== null && ( */}
    //   <Cards
    //     isbutton={<EditableNumber headless refetchData={refetch} />}
    //     className="h-full"
    //     // more={!preIsLoading && moreContent}
    //     title={title}
    //     size="large"
    //   >
    //     {preIsLoading ? (
    //       <div className="sd-spin flex w-full h-full items-center justify-center">
    //         <Skeleton active />
    //       </div>
    //     ) : (
    //       <div id="performance-lineChart" className="performance-lineChart">
    //         <ul>
    //           {performanceDatasets &&
    //             performanceDatasets.map((item, key) => {
    //               return (
    //                 <li key={key + 1} className="custom-label">
    //                   <strong className={item.amountClass}>
    //                     {item.amount}
    //                   </strong>
    //                   <div>
    //                     <span
    //                       style={{
    //                         backgroundColor: item.borderColor,
    //                       }}
    //                     />
    //                     {item.label}
    //                   </div>
    //                 </li>
    //               );
    //             })}
    //         </ul>

    //         <ChartjsAreaChart
    //           id="performance"
    //           labels={labels}
    //           datasets={performanceDatasets}
    //           options={{
    //             maintainAspectRatio: true,
    //             elements: {
    //               z: 9999,
    //             },
    //             legend: {
    //               display: false,
    //               position: 'bottom',
    //               align: 'start',
    //               labels: {
    //                 boxWidth: 6,
    //                 display: false,
    //                 usePointStyle: true,
    //               },
    //             },
    //             hover: {
    //               mode: 'index',
    //               intersect: false,
    //             },
    //             tooltips: {
    //               mode: 'label',
    //               intersect: false,
    //               backgroundColor: '#ffffff',
    //               position: 'average',
    //               enabled: false,
    //               custom: customTooltips,
    //               callbacks: {
    //                 title() {
    //                   return `Total Leads Value`;
    //                 },
    //                 label(t, d) {
    //                   const { yLabel, datasetIndex } = t;
    //                   return `<span class="chart-data">${nFormatter(yLabel)}</span> <span class="data-label">${d.datasets[datasetIndex].label}</span>`;
    //                 },
    //               },
    //             },
    //             scales: {
    //               yAxes: [
    //                 {
    //                   gridLines: {
    //                     color: '#e5e9f2',
    //                     borderDash: [3, 3],
    //                     zeroLineColor: '#e5e9f2',
    //                     zeroLineWidth: 1,
    //                     zeroLineBorderDash: [3, 3],
    //                   },
    //                   ticks: {
    //                     // beginAtZero: true,
    //                     // fontSize: 13,
    //                     // fontColor: '#182b49',
    //                     // suggestedMin: 50,
    //                     // suggestedMax: 80,
    //                     // stepSize: 20,
    //                     callback(label) {
    //                       return `${nFormatter(label)}`;
    //                     },
    //                   },
    //                 },
    //               ],
    //               xAxes: [
    //                 {
    //                   gridLines: {
    //                     display: true,
    //                     zeroLineWidth: 2,
    //                     zeroLineColor: 'transparent',
    //                     color: 'transparent',
    //                     z: 1,
    //                     tickMarkLength: 0,
    //                   },
    //                   ticks: {
    //                     padding: 10,
    //                   },
    //                 },
    //               ],
    //             },
    //           }}
    //           height={window.innerWidth <= 575 ? 200 : 82}
    //         />
    //       </div>
    //     )}
    //   </Cards>
    //   {/* )} */}
    // </RevenueWrapper>
  );
};

TotalRevenue.defaultProps = {
  title: 'Total Leads Value',
};

TotalRevenue.propTypes = {
  title: PropTypes.string,
};

export default TotalRevenue;
