import React, { useState, useEffect, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { theme, Spin } from 'antd';
import SearchContext from '../../context';
import JobList from '../../components/JobList';
import { ReactComponent as Filter } from '../../assets/svg/Filter.svg';

function AgencyDetali() {
  const { id } = useParams();
  // useEffect(() => {
  //   if (localStorage.getItem("savedSync") == null)
  //     localStorage.setItem("savedSync", JSON.stringify([]));
  //   setTableData(JSON.parse(localStorage.getItem("savedSync")));
  // }, []);
  const { savedSearchData, setSavedSearchData } = useContext(SearchContext);
  const [isSearchLoading, setSearchLoading] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [viewData, setViewData] = useState([]);

  useEffect(() => {
    if (id) {
      if (localStorage.getItem('savedAgency') == null)
        localStorage.setItem('savedAgency', JSON.stringify([]));
      setViewData(
        JSON.parse(localStorage.getItem('savedAgency')).filter(
          (item) => item.companies == id
        )
      );
    }
  }, [id]);

  React.useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200 ? setShowFilter(false) : setShowFilter(true)
    );
  }, []);

  const { token } = theme.useToken();

  const containerStyle = {
    position: 'relative',
    height: '84vh',
    padding: 20,
    overflow: 'overlay',
    textAlign: 'left',
    background: token.colorFillAlter,
    border: `1px solid ${token.colorBorderSecondary}`,
  };

  if (viewData.length == 0 || isSearchLoading)
    return (
      <div className="text-left mt-20">
        <Spin tip="Loading" size="large">
          <div className="content" />
        </Spin>
      </div>
    );
  return (
    <>
      <div style={containerStyle}>
        {
          <div className="text-right mt-10 xl:mx-[40px] mx-1">
            <div className="flex justify-end items-center">
              <div className="mr-11">{`Total Counts: ${viewData[0].totalCount}`}</div>
              <div
                className="sm:hidden flex align-middle"
                onClick={() => setShowFilter(!showFilter)}
              >
                <Filter className="w-5 text-gray-400 mr-3 pb-2" />
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div
                className={`lg:col-span-1 sm:col-span-1 col-span-4 sm:block  ${
                  showFilter ? 'block h-full w-full' : 'hidden'
                }`}
              >
                {/*<EditFilter*/}
                {/*  id={id}*/}
                {/*  editSearch={viewData}*/}
                {/*  savedSearchData={savedSearchData}*/}
                {/*  setSavedSearchData={setSavedSearchData}*/}
                {/*  setSearchLoading={setSearchLoading}*/}
                {/*/>*/}
              </div>
              <div className="lg:col-span-3 sm:col-span-3 col-span-4">
                <div className="lg:col-span-3">
                  <JobList
                    searchData={viewData[0]}
                    isSearchLoading={isSearchLoading}
                    setSavedSearchData={setSavedSearchData}
                    savedSearchData={savedSearchData}
                    setSearchLoading={setSearchLoading}
                    setViewData={setViewData}
                    method="agency"
                  />
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    </>
  );
}

export default AgencyDetali;
