import React, { useEffect, useMemo, useState } from 'react';

import _get from 'lodash/get';
import _find from 'lodash/find';

import {
  Button,
  Col,
  Input,
  Row,
  notification,
  Dropdown,
  Form,
  Select,
  Flex,
  Switch,
  Progress,
  Modal,
} from 'antd';
import { Controller, useForm } from 'react-hook-form';
import JobList from '../../components/JobListV2/JobList';
import NewSearchFilterComponent from '../../components/SearchDetailV2/NewSearchFilterComponent';
import PinnedJobList from '../../components/SearchDetailV2/PinnedJobList';
import {
  getSearchById,
  getPinnedJobsSearchById,
  getPaginationSearchById,
} from '../../services/search';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { BiFilter } from 'react-icons/bi';
import { getToggleSyncStatus, postToggleSync } from '../../services/searchSync';
import JobCollapseCompany from '../../components/JobListV2/JobCollapseCompany';
import {
  resetAllCompany,
  saveFilterJob,
  saveIsGroupCompany,
  selectIsGroupCompany,
  saveAllCompany,
  setIsSearching,
  selectFilterJob,
  setIsReset,
  saveRecentJobs,
  selectRecentJobs,
  selectIsRefineBySearch,
  saveIsRefineBySearch,
  setIsLoadingPin,
} from '../../store/common';
import { useSelector, useDispatch } from 'react-redux';
import useSearches from '../../hooks/useSearches';
import SearchTableV2 from '../../components/SearchTableV2/SearchTableV2';
import './style.scss';
import {
  useUpdateRefineBySearchStatusMutation,
  useUpdateStatusGroupJobMutation,
} from '../../app/jobSearch';
import LoadingBar from './LoadingBar';
import {
  DotChartOutlined,
  FallOutlined,
  RiseOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { refineOptions } from '../SearchDetailV2';
import { FaRobot, FaSpinner } from 'react-icons/fa';
import AILoading from '../../components/AILoading';

// import PinnedJobListGroupByCompany from '../../components/SearchDetailV2/PinnedJobListGroupByCompany';

export function paramsToObj(params) {
  const paramsObject = {};
  for (const [key, value] of params.entries()) {
    if (paramsObject[key]) {
      if (Array.isArray(paramsObject[key])) {
        paramsObject[key].push(value);
      } else {
        paramsObject[key] = [paramsObject[key], value];
      }
    } else {
      paramsObject[key] = value;
    }
  }
  return paramsObject;
}

export const sorterOptions = {
  desc: 'Newest',
  asc: 'Oldest',
  relevance: 'Relevance',
};

const { Option } = Select;

function SearchDetailV2() {
  const filterJob = useSelector(selectFilterJob);
  const { syncId, searchId } = useParams();
  const locationRouter = useLocation();
  const dispatch = useDispatch();
  const isGroupCompany = useSelector(selectIsGroupCompany);
  const isRefineBySearch = useSelector(selectIsRefineBySearch);
  const [updateStatusGroupJob, { isLoading }] =
    useUpdateStatusGroupJobMutation();
  const [updateRefineBySearch, { isLoading: isRefineBySearchLoading }] =
    useUpdateRefineBySearchStatusMutation();
  const {
    searches,
    fetchSearches,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    pagination: paginationSearch,
    setPagination: setPaginationSearch,
    isGetAll,
  } = useSearches(true);

  const [showFilter, setShowFilter] = useState(true);
  const [pagination, setPagination] = useState({});
  const [pagingKey, setPagingKey] = useState(['']);
  const [isChecked, setIsChecked] = useState(false);
  const [totalJobs, setTotalJobs] = useState(0);
  const [sentJobStatus, setSentJobStatus] = useState();

  let [searchParams, setSearchParams] = useSearchParams();
  const [currentCompany, setCurrentCompany] = useState([]);

  // Initialize the state for the refine by AI blocker
  const [isShowRefineByAIBlocker, setShowRefineByAIBlocker] = useState(false);

  const showRefineByAIBlocker = () => setShowRefineByAIBlocker(true);
  const closeRefineByAIBlocker = () => setShowRefineByAIBlocker(false);

  const searchParamsObj = paramsToObj(searchParams);
  const page = searchParamsObj?.page ?? 1;
  const pageSize = searchParamsObj?.pageSize ?? 10;
  const filter = {
    datePostedText: searchParamsObj?.datePostedText ?? 'Date posted',
    searchText: searchParamsObj?.searchText ?? '',
    jobBoards: searchParamsObj?.jobBoards ?? [],
    keywords: searchParamsObj?.keywords ?? [],
    location: searchParamsObj?.location ?? [],
    maxSalary: searchParamsObj?.maxSalary ?? '',
    minSalary: searchParamsObj?.minSalary ?? '',
    postedStartDate: searchParamsObj?.postedStartDate ?? '',
    postedEndDate: searchParamsObj?.postedEndDate ?? '',
    newJobOnly: searchParamsObj?.newJobOnly ?? false,
    notificationId: searchParamsObj?.notificationId ?? '',
    company: searchParamsObj?.company ?? '',
    previousPage: searchParamsObj?.previousPage ?? 1,
    jobTitles: searchParamsObj?.jobTitles ?? [],
    includeSentJob: searchParamsObj?.includeSentJob ?? '',
    sort: searchParamsObj?.sort ?? 'desc',
    excludeCompanies: searchParamsObj?.excludeCompanies ?? [],
    excludeKeywords: searchParamsObj?.excludeKeywords ?? [],
    excludeTitles: searchParamsObj?.excludeTitles ?? [],
    isSavedExclude: searchParamsObj?.isSavedExclude,
  };

  const sorter = searchParamsObj?.sort || 'desc';
  const [activeGroupByCompany, setActiveGroupByCompany] = useState(false);
  const [groupByCompanyIdJob, setGroupByCompanyIdJob] = useState('');

  const { handleSubmit, control, getValues, setValue, reset, watch } = useForm({
    defaultValues: {
      datePostedText: filter.datePostedText,
      jobBoards: filter.jobBoards,
      keywords: filter.keywords,
      location: filter.location || [],
      minSalary: filter.minSalary,
      maxSalary: filter.maxSalary,
      postedWithin: filter.postedStartDate
        ? [
            dayjs(filter.postedStartDate, 'YYYY-MM-DDTHH:mm:ss'),
            dayjs(filter.postedEndDate, 'YYYY-MM-DDTHH:mm:ss'),
          ]
        : [],
      searchText: filter.searchText,
      company: filter.company,
      jobTitles: filter.jobTitles,
      includeSentJob: filter.includeSentJob,
      sort: filter.sort,
      excludeCompanies: filter.excludeCompanies,
      excludeKeywords: filter.excludeKeywords,
      excludeTitles: filter.excludeTitles,
      isSavedExclude: filter.isSavedExclude,
    },
  });

  const queryClient = useQueryClient();

  const search = useQuery({
    queryKey: ['search-detail', searchId, filter, page, pageSize],
    queryFn: () => {
      // const pageBasedOnMode = isGroupCompany ? 0 : pageSize;
      return getSearchById(searchId, filter, page, pageSize);
    },
    enabled: Boolean(searchId) && !isGroupCompany,
    keepPreviousData: false,
  });

  const pinned = useQuery({
    queryKey: ['pinned', searchId],
    queryFn: () => getPinnedJobsSearchById(searchId, filter?.includeSentJob),
    enabled: Boolean(searchId),
  });

  const searchInformation = useQuery({
    queryKey: ['pagination-search', searchId, filter, page, pageSize],
    queryFn: async () => {
      const { data } = await getPaginationSearchById(
        searchId,
        filter,
        page,
        pageSize
      );
      const paginationTemp = data.result;
      setPagination(paginationTemp);
      setTotalJobs(
        parseInt(paginationTemp?.totalCount || 0) +
          parseInt(pinned?.data?.data?.result?.data?.length || 0)
      );
      return { data };
    },
    enabled: Boolean(searchId),
    keepPreviousData: false,
  });

  const toggleStatus = useQuery({
    queryKey: ['toggle-sync-status', searchId],
    queryFn: () => getToggleSyncStatus(searchId),
  });

  const isToggleActive =
    toggleStatus?.data?.data?.result?.status === 'ON' ? true : false;

  const handleFormSubmit = async (formData = null) => {
    if (!formData) {
      setPagination({});
      setPagingKey(['']);
      dispatch(setIsReset(true));
      setSearchParams();
      return;
    }

    console.log('formData: ', formData);
    const {
      datePostedText,
      jobBoards,
      keywords,
      location,
      maxSalary,
      minSalary,
      postedWithin,
      jobTitles,
      excludeCompanies,
      excludeKeywords,
      excludeTitles,
      includeSentJob,
      isSavedExclude,
    } = formData;

    let postedStartDate =
      postedWithin && postedWithin.length == 2
        ? postedWithin[0].format('YYYY-MM-DDTHH:mm:ss')
        : null;
    let postedEndDate =
      postedWithin && postedWithin.length == 2
        ? postedWithin[1].format('YYYY-MM-DDTHH:mm:ss')
        : null;
    postedEndDate = postedEndDate ? postedEndDate : '';
    postedStartDate = postedStartDate ? postedStartDate : '';

    const queryParams = {
      ...filter,
    };
    if (minSalary || queryParams.minSalary) {
      queryParams.minSalary = minSalary || '';
    }
    if (maxSalary || queryParams.maxSalary) {
      queryParams.maxSalary = maxSalary || '';
    }
    if (jobBoards) {
      queryParams.jobBoards = jobBoards;
    }
    if (location) {
      queryParams.location = location;
    }
    if (keywords) {
      queryParams.keywords = keywords;
    }
    if (datePostedText) {
      queryParams.datePostedText = datePostedText;
    }
    if (postedWithin && postedWithin.length === 2) {
      queryParams.postedStartDate = new Date(postedStartDate).toISOString();
      queryParams.postedEndDate = new Date(postedEndDate).toISOString();
    }
    // if (datePostedText === 'Date posted') {
    //   queryParams.postedEndDate = '';
    //   queryParams.postedStartDate = '';
    // }
    if (jobTitles) {
      queryParams.jobTitles = jobTitles;
    }
    if (sorter) {
      queryParams.sort = sorter;
    }

    // exclude data
    queryParams.excludeCompanies = excludeCompanies || [];
    queryParams.excludeKeywords = excludeKeywords || [];
    queryParams.excludeTitles = excludeTitles || [];

    queryParams.includeSentJob = includeSentJob ?? '';
    queryParams.isSavedExclude = isSavedExclude;
    setSearchParams(queryParams);
    console.log('queryParams: ', queryParams);
    console.log('filter: ', filter);
    if (JSON.stringify(filter) === JSON.stringify(queryParams)) return;

    queryParams.page = 1;
    queryParams.exclusiveStartKey = '';
    setPagination({});
    setPagingKey(['']);
    dispatch(setIsReset(true));
  };

  const handleToggleSync = async (toggle) => {
    try {
      const payload = {
        jobSearchId: searchId,
        toggle,
      };
      await postToggleSync(payload);
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      notification.error({
        message: 'Error',
        description: errorMessage,
      });
    }
  };

  const generateSearchFilter = () => {
    return (
      <div className="rounded-lg">
        <NewSearchFilterComponent
          search={searchInformation}
          reset={reset}
          watch={watch}
          setValue={setValue}
          isEdit={true}
          onFinish={(message = false) => {
            if (message === 'CLEAR') {
              reset();
              handleFormSubmit();
            } else {
              handleSubmit((data) =>
                handleFormSubmit({ ...data, includeSentJob: sentJobStatus })
              )();
            }
          }}
          control={control}
          toggleStatus={isToggleActive}
          onToggleSync={handleToggleSync}
          callbackSearch={() => search.refetch()}
          handleSubmit={handleSubmit}
          sentJobStatus={sentJobStatus}
          setSentJobStatus={setSentJobStatus}
          handleFormSubmit={handleFormSubmit}
          isRefineBySearchLoading={isRefineBySearchLoading}
          isRefineBySearch={isRefineBySearch}
          handleUpdateRefineBySearch={handleUpdateRefineBySearch}
          showRecentJobs={showRecentJobs}
          recentJobs={recentJobs}
        />
      </div>
    );
  };

  const handleShowFilter = () => {
    setShowFilter(!showFilter);
  };

  const genderFastSearch = () => {
    return (
      <div
        style={{ marginTop: '10px' }}
        className="bg-primary p-5 rounded-xl drop-shadow fast-search "
      >
        <Form
          layout="vertical"
          onFinish={() =>
            handleSubmit((data) =>
              handleFormSubmit({ ...data, includeSentJob: sentJobStatus })
            )()
          }
        >
          {/* <Form.Item name="keywords">
            <Controller
              name="jobTitles"
              style={{ borderRadius: '2rem' }}
              control={control}
              render={({ field }) => (
                <Select
                  placeholder="Job Titles"
                  className="custom-filter"
                  mode="tags"
                  onSelect={() => handleSubmit(handleFormSubmit)()}
                  dropdownRender={(menu) => (
                    <div style={{ display: 'none' }}>{menu}</div>
                  )}
                  dropdownStyle={{ display: 'none' }}
                  {...field}
                />
              )}
            />
          </Form.Item>
          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  label: (
                    <div
                      onClick={() => {
                        setSentJobStatus('SENT');
                        setValue('isIncludeSentJob', 'SENT');
                      }}
                    >
                      Job Sent to BullHorn
                    </div>
                  ),
                },
                {
                  key: '2',
                  label: (
                    <div
                      onClick={() => {
                        setSentJobStatus('NOT_SENT');
                        setValue('includeSentJob', 'NOT_SENT');
                      }}
                    >
                      Jobs NOT sent to Bullhorn
                    </div>
                  ),
                },
              ],
            }}
            placement="bottomLeft"
          >
            <Button
              style={{
                marginTop: '-15px',
                marginBottom: '15px',
                width: '100%',
                background: '#fff',
              }}
            >
              {sentJobStatus === 'NOT_SENT'
                ? 'Jobs NOT sent to Bullhorn'
                : sentJobStatus === 'SENT'
                  ? 'Job Sent to BullHorn'
                  : 'Bullhorn Jobs Filter'}
            </Button>
          </Dropdown> */}
          <Form.Item>
            <Button
              type="default"
              htmlType="submit"
              className="bg-white"
              style={{ width: '100%' }}
            >
              Submit
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  const renderUI = isGroupCompany ? (
    <>
      <JobCollapseCompany
        // pinnedData={pinnedCompanyList.length > 0 && pinnedCompanyList}
        isFromSyncSearches={true}
        searchData={searchInformation?.data?.data?.result}
        populateSearchData={() => queryClient.invalidateQueries('search')}
        populatePinnedJobsData={() =>
          queryClient.invalidateQueries('search-detail')
        }
        setCurrentCompany={setCurrentCompany}
        currentCompany={currentCompany}
        newJobOnly={filter?.newJobOnly}
        activeGroupByCompany={activeGroupByCompany}
        setActiveGroupByCompany={setActiveGroupByCompany}
        groupByCompanyIdJob={groupByCompanyIdJob}
        setGroupByCompanyIdJob={setGroupByCompanyIdJob}
        setGroupCompany={(company) => {
          if (company === filter.company) {
            setSearchParams({
              ...filter,
              company: '',
              page: filter.previousPage,
            });
            setActiveGroupByCompany(false);
            setGroupByCompanyIdJob('');
          } else {
            setSearchParams({
              ...filter,
              company: company,
              previousPage: pagination.currentPage,
            });
          }
        }}
        pinnedData={{
          isFromSyncSearches: true,
          searchData: { data: pinned?.data?.data?.result },
          isSearchLoading: search.isFetching || search.isLoading,
          populateSearchData: () => queryClient.invalidateQueries('search'),
          populatePinnedJobsData: () =>
            queryClient.invalidateQueries('search-pinned'),
        }}
      />
    </>
  ) : (
    <>
      <PinnedJobList
        isFromSyncSearches
        searchData={{
          data: pinned?.data?.data?.result,
          loading: pinned.isLoading || pinned.isFetching,
        }}
        isSearchLoading={search.isFetching}
        populateSearchData={() => {
          queryClient.invalidateQueries('pagination-search');
          queryClient.invalidateQueries('search');
          queryClient.invalidateQueries('search-detail');
        }}
        populatePinnedJobsData={() =>
          queryClient.invalidateQueries('search-pinned')
        }
      />
      <JobList
        setTotalJobs={setTotalJobs}
        totalJobs={totalJobs}
        pagination={pagination}
        searchData={search?.data?.data?.result}
        isSearchLoading={search.isFetching || search.isLoading}
        populateSearchData={() => {
          queryClient.invalidateQueries('pagination-search');
          queryClient.invalidateQueries('search');
          queryClient.invalidateQueries('search-detail');
        }}
        populatePinnedJobsData={() =>
          queryClient.invalidateQueries('search-detail')
        }
        pageSize={pageSize}
        newJobOnly={filter?.newJobOnly}
        page={parseInt(pagination?.currentPage || 1)}
        setPage={(page, pageSize) => {
          setPagination({
            ...pagination,
            currentPage: page,
            pageSize: pageSize || 10,
          });
          setSearchParams({ ...filter, page, pageSize: pageSize || 10 });
        }}
        isFromSyncSearches={true}
        pagingKey={pagingKey}
        setGroupCompany={(company) => {
          if (company === filter.company) {
            setSearchParams({
              ...filter,
              company: '',
              page: filter.previousPage,
            });
            setActiveGroupByCompany(false);
            setGroupByCompanyIdJob('');
          } else {
            setSearchParams({
              ...filter,
              company: company,
              previousPage: pagination.currentPage,
            });
          }
        }}
        activeGroupByCompany={activeGroupByCompany}
        setActiveGroupByCompany={setActiveGroupByCompany}
        groupByCompanyIdJob={groupByCompanyIdJob}
        setGroupByCompanyIdJob={setGroupByCompanyIdJob}
      />
    </>
  );

  const handleToggle = () => {
    handleToggleSync(isChecked ? 'OFF' : 'ON');
    setIsChecked(!isChecked);
  };

  const handleUpdateStatusGroupJob = async () => {
    try {
      const convertStatus = isGroupCompany ? 'OFF' : 'ON';

      const payload = {
        searchId: searchId,
        status: convertStatus,
      };

      const response = await updateStatusGroupJob(payload);

      if (_get(response, 'data.success')) {
        await dispatch(
          saveIsGroupCompany(_get(response, 'data.result.isGroupByCompany'))
        );
        if (isGroupCompany) {
          await queryClient.invalidateQueries('search-detail');
        }

        await notification.open({
          message: 'Success!',
          description: 'Toggled "Group Job by Company" status successfully!',
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Toggled "Group Job by Company" status failed!',
        });
      }
    } catch {
      notification.error({
        message: 'Error!',
        description: 'Toggled "Group Job by Company" status failed!',
      });
    }
  };

  const handleUpdateRefineBySearch = async () => {
    try {
      const convertStatus = isRefineBySearch ? 'OFF' : 'ON';

      const payload = {
        searchId: searchId,
        status: convertStatus,
      };
      const searchInfor = searchInformation?.data?.data?.result?.search;
      const latestRefinedJobDate = searchInfor?.latestRefinedJobDate || null;

      const response = await updateRefineBySearch(payload);

      if (_get(response, 'data.success')) {
        const isRefineBySearchRes = _get(
          response,
          'data.result.isRefineBySearch'
        );
        dispatch(saveIsRefineBySearch(isRefineBySearchRes));
        dispatch(setIsLoadingPin(true));

        if (!latestRefinedJobDate && isRefineBySearchRes) {
          showRefineByAIBlocker();
        }

        await queryClient.invalidateQueries('search-detail');
        await queryClient.invalidateQueries('pagination-search');
        await queryClient.invalidateQueries('search');

        if (!latestRefinedJobDate && isRefineBySearchRes) {
          await queryClient.invalidateQueries('search-detail');
          await queryClient.invalidateQueries('pagination-search');
          await queryClient.invalidateQueries('search');
          closeRefineByAIBlocker();
        }

        if (!isRefineBySearchRes) return;

        notification.success({
          message: <div className="!text-white">Success</div>,
          description: (
            <div className="!text-white">
              Ai Charles is refining your search to give you the best results!
            </div>
          ),
          icon: <FaRobot className="font-semibold" />,
          className: 'ai-notification',
          placement: 'top',
          showProgress: true,
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Something went wrong, Please try again later!',
        });
      }
    } catch (error) {
      console.error('Error: ', error);
      notification.error({
        message: 'Error!',
        description: 'Something went wrong, Please try again later!',
      });
    }
  };

  const searchData = useMemo(() => {
    if (_find(searches, (item) => _get(item, 'id') === searchId))
      return [_find(searches, (item) => _get(item, 'id') === searchId)];
    return [];
  }, [searches, isGroupCompany]);

  useEffect(() => {
    dispatch(resetAllCompany());
  }, [locationRouter.pathname]);

  useEffect(() => {
    if (JSON.stringify(filterJob) !== JSON.stringify(filter)) {
      dispatch(saveFilterJob(filter));
    }
  }, [filter]);

  useEffect(() => {
    if (searchInformation?.data?.data?.result?.search?.isGroupByCompany) {
      dispatch(saveIsGroupCompany(true));
    } else {
      dispatch(saveIsGroupCompany(false));
    }
  }, [searchInformation?.data?.data?.result?.search?.isGroupByCompany]);

  useEffect(() => {
    if (searchInformation?.data?.data?.result?.search?.isRefineBySearch) {
      dispatch(saveIsRefineBySearch(true));
    } else {
      dispatch(saveIsRefineBySearch(false));
    }
  }, [searchInformation?.data?.data?.result?.search?.isRefineBySearch]);

  useEffect(() => {
    setIsChecked(isToggleActive);
  }, [isToggleActive]);

  const recentJobs = useSelector(selectRecentJobs);
  const jobFilters = Object.entries({ ...filter })
    .filter(([_, value]) => value || value === 0)
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
  const sseFilters = { ...searchData[0], ...jobFilters };
  delete sseFilters?.sort;
  const sseQueryString = Object.keys(sseFilters)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${sseFilters[key] ? encodeURIComponent(sseFilters[key]) : ''}`
    )
    .join('&');
  const sseUrl = `${import.meta.env.VITE_API_URL}/sse/recent-jobs${sseQueryString ? `?${sseQueryString}` : ''}`;

  useEffect(() => {
    const updateRecentlyJobs = ({ data }) => {
      dispatch(
        saveRecentJobs([
          data,
          ...recentJobs.filter(({ job_id }) => job_id !== data.job_id),
        ])
      );
    };
    const sseJobs = new EventSource(sseUrl);
    sseJobs.onopen = () =>
      console.log('[SSE] Recent Jobs connection is opened');
    sseJobs.onerror = (error) =>
      console.log('[SSE] Recent Jobs connection error occurred: ', error);
    sseJobs.addEventListener('RECENTLY_ADDED_JOB', updateRecentlyJobs);

    // Whenever we're done with the data stream we must close the connection
    return () => {
      sseJobs.removeEventListener('RECENTLY_ADDED_JOB', updateRecentlyJobs);
      sseJobs.close();
    };
  }, []);

  useEffect(() => {
    return () => {
      // Clear recent jobs
      dispatch(saveRecentJobs([]));
    };
  }, []);

  const showRecentJobs = () => {
    setPagination({
      ...pagination,
      currentPage: 1,
      pageSize: pageSize || 10,
    });
    setSearchParams({ ...filter, page: 1, pageSize: pageSize || 10 });
    // Clear recent jobs
    // dispatch(saveRecentJobs([]));
  };

  const handleSorterClick = (e) => {
    if (!e?.key) return;
    console.log('setSortOrder', e);
    dispatch(setIsSearching(true));
    dispatch(setIsReset(true));
    setSearchParams({ ...filter, sort: e?.key });
    queryClient.invalidateQueries('search-detail');
  };

  return (
    <Row gutter={[16, 16]} className="relative">
      <div
        style={{
          position: 'sticky',
          top: 0,
          background: '#fff',
          zIndex: 1000,
          borderRadius: '3px',
          width: '100%',
        }}
      >
        <Row span={24} className="float-right w-[70vw] relative">
          <div className='absolute -left-[21rem] top-0 z-10'>
            {generateSearchFilter()}
          </div>
          <SearchTableV2
            searches={searchData}
            populateSearchData={fetchSearches}
            sortField={sortField}
            setSortField={setSortField}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
            pagination={paginationSearch}
            setPagination={setPaginationSearch}
            isGetAll={isGetAll}
            syncId={syncId}
            totalJobs={totalJobs}
            isSearching={
              search.isFetching ||
              search.isLoading ||
              searchInformation.isFetching
            }
          />
        </Row>
      </div>
      {/* <div
        style={{
          position: 'stciky',
          left: 0,
          top: 0,
          zIndex: 1000,
        }}
      >
        
      </div> */}
      <Col xs={{ span: 24 }} md={{ span: 5 }}>
        <></>
      </Col>
      <Col xs={{ span: 24 }} md={{ span: 19 }}>
        <div
          style={{
            position: 'sticky',
            top: 150,
            background: '#fff',
            zIndex: 1000,
            padding: '10px',
            borderRadius: '3px',
          }}
          // className='border-cyan-600 border'
        >
          <div className="grid grid-cols-2">
            <div className="date-posted-drop-down">
              <div>
                <Dropdown
                  trigger={['click']}
                  menu={{
                    items: [
                      {
                        key: 'last_1_hour',
                        label: 'Last 1 hour',
                      },
                      {
                        key: 'last_one_days',
                        label: 'Last 1 day',
                      },
                      {
                        key: 'last_seven_days',
                        label: 'Last 7 days',
                      },
                      {
                        key: 'last_fourteen_days',
                        label: 'Last 14 days',
                      },
                      {
                        key: 'last_one_month',
                        label: 'Last one month',
                      },
                    ],
                    onClick: ({ key }) => {
                      switch (key) {
                        case 'last_1_hour':
                          setSearchParams({
                            ...filter,
                            postedStartDate: dayjs().subtract(1, 'hour'),
                            postedEndDate: dayjs(),
                            datePostedText: 'Last 1 hour',
                          });
                          setValue('datePostedText', 'Last 1 hour');
                          handleFormSubmit({
                            datePostedText: 'Last 1 hour',
                            postedWithin: [
                              dayjs().subtract(1, 'hour'),
                              dayjs(),
                            ],
                          });
                          break;
                        case 'last_one_days':
                          setSearchParams({
                            ...filter,
                            postedStartDate: dayjs().subtract(1, 'day'),
                            postedEndDate: dayjs(),
                            datePostedText: 'Last 1 day',
                          });
                          setValue('datePostedText', 'Last 1 day');
                          handleFormSubmit({
                            datePostedText: 'Last 1 day',
                            postedWithin: [dayjs().subtract(1, 'day'), dayjs()],
                          });
                          break;
                        case 'last_seven_days':
                          setSearchParams({
                            ...filter,
                            postedStartDate: dayjs().subtract(6, 'day'),
                            postedEndDate: dayjs(),
                            datePostedText: 'Last 7 days',
                          });
                          setValue('datePostedText', 'Last 7 days');
                          handleFormSubmit({
                            datePostedText: 'Last 7 days',
                            postedWithin: [dayjs().subtract(6, 'day'), dayjs()],
                          });
                          break;
                        case 'last_fourteen_days':
                          setSearchParams({
                            ...filter,
                            postedStartDate: dayjs().subtract(13, 'day'),
                            postedEndDate: dayjs(),
                            datePostedText: 'Last 14 days',
                          });
                          setValue('datePostedText', 'Last 14 days');
                          handleFormSubmit({
                            datePostedText: 'Last 14 days',
                            postedWithin: [
                              dayjs().subtract(13, 'day'),
                              dayjs(),
                            ],
                          });
                          break;
                        case 'last_one_month':
                          setSearchParams({
                            ...filter,
                            postedStartDate: dayjs().subtract(1, 'month'),
                            postedEndDate: dayjs(),
                            datePostedText: 'Last one month',
                          });
                          setValue('datePostedText', 'Last one month');
                          handleFormSubmit({
                            datePostedText: 'Last one month',
                            postedWithin: [
                              dayjs().subtract(1, 'month'),
                              dayjs(),
                            ],
                          });
                          break;
                        default:
                          break;
                      }
                    },
                  }}
                >
                  {getValues().datePostedText === 'Date posted' ? (
                    <Button>Date posted</Button>
                  ) : (
                    <Button
                      style={{ backgroundColor: 'black', color: 'white' }}
                    >
                      {getValues().datePostedText}
                    </Button>
                  )}
                </Dropdown>
                {getValues().datePostedText !== 'Date posted' && (
                  <Button
                    className="ml-2"
                    onClick={() => {
                      setSearchParams({
                        ...filter,
                        datePostedText: 'Date posted',
                        postedStartDate: '',
                        postedEndDate: '',
                      });
                      setValue('datePostedText', 'Date posted');
                      setValue('postedWithin', [null, null]);
                      handleFormSubmit({
                        datePostedText: 'Date posted',
                        postedWithin: null,
                      });
                    }}
                  >
                    X
                  </Button>
                )}
              </div>
            </div>
            <Dropdown.Button
              menu={{
                items: [
                  {
                    label: 'Newest',
                    key: 'desc',
                    icon: <RiseOutlined />,
                  },
                  {
                    label: 'Oldest',
                    key: 'asc',
                    icon: <FallOutlined />,
                  },
                  {
                    label: 'Relevance',
                    key: 'desc',
                    icon: <DotChartOutlined />,
                  },
                ],
                onClick: handleSorterClick,
              }}
              onClick={(e) => console.log('e: ', e)}
              arrow
              placement="bottom"
              className="flex justify-end"
            >
              {sorter
                ? sorterOptions[sorter.toLowerCase()]
                : sorterOptions['desc']}
            </Dropdown.Button>
          </div>
        </div>

        <> {renderUI}</>
      </Col>
      {/* Refine by AI Blocker */}
      <Modal
        className="ai-notification"
        title={<></>}
        footer={null}
        closable={false}
        destroyOnClose
        maskClosable={false}
        centered
        open={isShowRefineByAIBlocker}
        onCancel={closeRefineByAIBlocker}
        afterClose={() => {
          console.log('Modal closed');
        }}
      >
        <AILoading
          text={
            <div className="flex items-center gap-2 justify-center">
              <FaRobot className="font-semibold text-white text-3xl" />
              <p className="!text-white">Ai Charles is refining your search.</p>
            </div>
          }
        />
        <div className="text-xs text-white font-medium italic pt-2">
          <span className="text-red-600 font-semibold mr-1">*</span>
          Please wait while Ai Charles refines your search. This may take a few
          minutes.
        </div>
      </Modal>
    </Row>
  );
}

export default SearchDetailV2;
