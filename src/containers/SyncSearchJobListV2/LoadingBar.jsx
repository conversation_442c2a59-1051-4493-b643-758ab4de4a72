import { Modal, Progress } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import zileoIcon from '../../assets/img/zileoIcon.png';

function LoadingBar(props) {
  const { loading } = props;
  const [percent, setPercent] = useState(0);
  const [openProgress, setOpenProgress] = useState(false);

  const startProgress = () => {
    setPercent(1);
    let value = 1;
    const interval = setInterval(() => {
      value += 5;
      setPercent(value);
      if (value >= 95) {
        clearInterval(interval);
      }
    }, 100);
  };

  useEffect(() => {
    if (loading) {
      setOpenProgress(true)
      startProgress();
    } else {
      setPercent(100)
      setTimeout(() => {
        setOpenProgress(false);
      }, 1000);
    }
  }, [loading]);

  if (openProgress) {
    return (
      <div>
        <div className="process-bg"></div>
        <div className="process-modal">
          <div className="process-body-cus">
            {/* <div
            className="process-value-cus"
            style={{ '--target-width': `${percent}%` }}
          >
            {percent} %
          </div> */}
            <div className="container">
              <div className="progress2 progress-moved">
                <div
                  className="progress-bar2"
                  style={{ '--target-width': `${percent}%` }}
                ></div>
              </div>
            </div>
            <div className="step-custom">
              <img src={zileoIcon} alt="" />
            </div>
          </div>
        </div>
      </div>
    );
  }
  return <></>;
}

export default LoadingBar;
