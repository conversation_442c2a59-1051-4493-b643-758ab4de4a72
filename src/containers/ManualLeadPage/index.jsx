import {
  ArrowLeftOutlined,
  FileTextOutlined,
  HeartOutlined,
  LeftOutlined,
  StarOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { Button, Select } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import StandardJobSubmissionModalPage from '../SyncStandardLeadPage/StandardJobSubmissionModalPage';
import BullHornJobSubmissionModalPage from '../../components/BullHorn/BullHornJobSubmissionModalPage';
import {
  getUserViewAsLicense,
  getUserViewAsLicenseType,
} from '../../helpers/getUserViewAs';
import { licenseType } from '../../constants/common.constant';

const RestyledSelect = styled(Select)`
  .ant-select-selection-item {
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
`;

export const TYPE_ICONS = {
  Vacancy: <HeartOutlined />,
  Lead: <TrophyOutlined />,
  Opportunity: <StarOutlined />,
};

const ManualLeadsPage = () => {
  const [selectedType, setSelectedType] = useState('Vacancy');
  const [key, setKey] = useState(1);
  const navigate = useNavigate();

  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const back = () => {
    if (window?.history?.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm');
    }
  };

  const handleChange = (value) => {
    setSelectedType(value);
    if (value === 'Vacancy') {
      setKey(1);
    } else if (value === 'Lead') {
      setKey(2);
    } else if (value === 'Opportunity') {
      setKey(3);
    }
  };

  return isStandardUser ? (
    <div>
      <Button
        onClick={back}
        type="dashed"
        icon={<LeftOutlined />}
        className="font-semibold"
      >
        Back
      </Button>
      <div className="mt-2 rounded-md bg-white flex items-center justify-center min-h-[50vh]">
        <StandardJobSubmissionModalPage
          // actionKey={key} // Lead form
          job={null}
          searchData={[]}
          searchIdProp={''}
          fromManualCreate={true}
        />
      </div>
    </div>
  ) : (
    <div className="w-full flex flex-col">
      <div className="flex items-center justify-between w-full mx-2 px-9 py-3 rounded-md bg-white mb-2">
        <Button onClick={back} icon={<LeftOutlined />} className="bg-white">
          Back
        </Button>
        <RestyledSelect
          suffixIcon={TYPE_ICONS[selectedType]}
          value={selectedType}
          onChange={handleChange}
          options={[
            {
              value: 'Vacancy',
              label: 'Vacancy',
            },
            {
              value: 'Lead',
              label: 'Lead',
            },
            {
              value: 'Opportunity',
              label: 'Opportunity',
            },
          ]}
        />
        <div className="flex flex-col items-center justify-end max-w-xs">
          <div className="text-cyan-600 text-base font-semibold flex items-center gap-1 justify-end w-full p-2 border rounded-md">
            <FileTextOutlined />
            Add Lead Manually
          </div>
          {/* <div className="text-xs opacity-60 flex items-center justify-end w-full">
            Manually add job openings by entering titles, descriptions, and
            requirements to attract candidates.
          </div> */}
        </div>
      </div>
      <div className="mx-2 my-3 rounded-md bg-white shadow-md">
        <BullHornJobSubmissionModalPage
          actionKey={key} // Lead form
          job={null}
          searchData={[]}
          searchIdProp={''}
          setIsModalVisible={(_value) => {
            back();
          }}
          fromManualCreate={true}
        />
      </div>
    </div>
  );
};

export default ManualLeadsPage;
