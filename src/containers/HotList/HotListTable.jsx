import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Badge,
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  message,
  Modal,
  notification,
  Pagination,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import {
  deleteBullhornContact,
  bullhornMassUpdate,
  searchCommonBulhorn,
  upladteBullhorn,
  createNewEmailValid,
  getNewEmailValid,
} from '../../services/bullhorn';
import { useParams } from 'react-router-dom';
import { STATUS_COLOR } from '../SequenceHotListTab/ViewContacts';
import { arrayUniqueByKey, formatPhoneNumber } from '../../utils/common';
import {
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  EnvironmentOutlined,
  ExclamationCircleOutlined,
  LinkedinOutlined,
  MailOutlined,
  MoreOutlined,
  PhoneOutlined,
  SolutionOutlined,
  StopOutlined,
  UserDeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  SisternodeOutlined,
  ThunderboltOutlined,
  AuditOutlined,
  DownOutlined,
  InteractionOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { EditableCell } from '../ContactList/ContactListTable';
import { v4 as uuid } from 'uuid';
import { validListEmail } from '../../services/emailFinder';
import ModalEnrich from './ModalEnrich';
import BulkEnrichData from './BulkEnrich';
import { isEmpty } from 'lodash';
import {
  enrichAllHotList,
  handleBulkEnrichContactData,
  handleGetEnrichData,
} from '../../services/employee';
import EventSourceRender from '../../components/EventSource';
import Loading from './Loading';
import handleRenderTime from '../../function/handleRenderTime';

export const TIME_ENRICH_EACH_FIELD = 35 * 1000; // seconds

export const defaultEnrichFields = [
  'person',
  'linkedin_url',
  'valid_work_email',
  'company',
];

export const ENRICH_DATA_STATUS = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESSFUL: 'SUCCESSFUL',
  ERROR: 'ERROR',
};

export const ENRICH_DATA_LIST_TYPE = {
  HOTLIST: 'HOTLIST',
  CONTACTLIST: 'CONTACTLIST',
};

export const VALIDATE_STATUS_COLOR = {
  Valid: 'success',
  Risky: 'warning',
  Invalid: 'error',
};

export const VALIDATE_STATUS_ICON = {
  Valid: <CheckCircleOutlined />,
  Risky: <ExclamationCircleOutlined />,
  Invalid: <CloseCircleOutlined />,
};

const HotListTable = ({ totalCount }) => {
  let { hotListId } = useParams();

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');
  const [listEmailChecked, setListEmailChecked] = useState([]);

  const [dataListContact, setDataListContact] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingContact, setLoadingContact] = useState(false);
  const [validateEmailLoading, setValidateEmailLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [loadingValidAllContact, setLoadingValidAllContact] = useState(false);
  const [listEmailValid, setListEmailValid] = useState([]);
  const [dataListInValidEmail, setDataListInvalidEmail] = useState([]);
  const [loadingGetInValid, setLoadingInValid] = useState(false);
  const [tab, setTab] = useState('tab_all');
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [continueLoadingInValidEmail, setContinueLoadingInValidEmail] =
    useState(true);
  const [continueLoadingInAllEmail, setContinueLoadingInAllEmail] =
    useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [percent, setPercent] = useState();
  const [erichItemSelected, setErichItemSelected] = useState();
  const [openEnrichModal, setOpenEnrichModal] = useState(false);
  const isEditing = (record) => record?.key === editingKey;

  // Bulk Enrich
  const [openBulkEnrichModal, setOpenBulkEnrichModal] = useState(false);

  const [estimationDuration, setEstimationDuration] = useState('0');
  const [updatingData, setUpdatingData] = useState({});
  const [enrichData, setEnrichData] = useState([]);
  const [enrichingData, setEnrichingData] = useState([]); // contacts list is enriching
  const [enrichLoading, setEnrichLoading] = useState(false);

  const showBulkEnrichModal = () => setOpenBulkEnrichModal(true);
  const closeBulkEnrichModal = () => setOpenBulkEnrichModal(false);

  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const removeItemById = (id) => {
    setDataListInvalidEmail((prevDataList) =>
      prevDataList.filter((item) => item.id !== id)
    );
  };

  const save = async (key) => {
    try {
      setLoadingContact(true);
      const row = await form.validateFields();

      const newData = [...dataListContact];

      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        const newItem = {
          ...item,
          ...row,
        };
        newData.splice(index, 1, newItem);
        if (item?.id) {
          const payload = {
            entityName: 'ClientContact',
            ...newItem,
          };
          delete payload?.key;
          const res = await upladteBullhorn(item?.id, payload);
          setLoadingContact(false);
        } else {
          console.log('Create new item:', item);
        }
        setDataListContact(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setDataListContact(newData);
        setEditingKey('');
        setLoadingContact(false);
      }
      form.resetFields();
    } catch (errInfo) {
      setLoadingContact(false);
      console.log('Validate Failed:', errInfo);
    }
  };

  const handleGetContacts = async () => {
    const fields = [
      'id',
      'name',
      'email',
      'customText1',
      'status',
      'phone',
      'occupation',
      'address',
      'clientCorporation',
    ].join(',');
    let query = `tearsheets.id=${hotListId}`;

    let start = 0;
    let limit = 500;
    let paginationTotal = 500; // Default total for the first call
    const emailList = [];
    const fullDataEmail = [];
    try {
      // First API call
      const { data } = await searchCommonBulhorn(
        'ClientContact',
        query,
        fields,
        start,
        limit
      );

      const fetchedEmails = data?.result?.data?.map((item) => item.email);
      emailList.push(...fetchedEmails);
      fullDataEmail.push(...data?.result?.data);

      paginationTotal = data?.result?.total || data?.result?.count || 0;

      start += limit;

      for (let i = 1; i < Math.ceil(paginationTotal / limit); i++) {
        const { data } = await searchCommonBulhorn(
          'ClientContact',
          query,
          fields,
          start,
          limit
        );

        const fetchedEmails = data?.result?.data?.map((item) => item.email);
        emailList.push(...fetchedEmails);
        fullDataEmail.push(...data?.result?.data);

        start += limit;
      }

      return { emailList, fullDataEmail };
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const splitArray = (array, chunkSize) => {
    const result = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      result.push(array.slice(i, i + chunkSize));
    }
    return result;
  };

  const handleGetAllContact = async () => {
    setLoadingValidAllContact(true);
    try {
      const { emailList } = await handleGetContacts();
      const emailChunks = splitArray(emailList, 7);
      const totalChunks = emailChunks.length;
      let allValidEmails = [];
      setPercent(0);
      for (let i = 0; i < totalChunks; i++) {
        const chunk = emailChunks[i];
        const { data: dataValidEmail } = await validListEmail({
          emails: chunk,
        });

        allValidEmails = [
          ...allValidEmails,
          ...dataValidEmail?.result?.map((item) => ({
            email: item?.email,
            status: item?.result,
          })),
        ];

        const percent = Math.round(((i + 1) / totalChunks) * 100);
        setPercent(percent);
      }

      await handleCreateNewEmailValid(allValidEmails);

      setPercent(null);
      setLoadingValidAllContact(false);
      notification.success({
        message: 'Valid All Email Success',
      });
      handleGetContact();
    } catch (error) {
      setLoadingValidAllContact(false);
      notification.error({
        description: 'error',
      });
    }
  };

  const handleGetContact = async () => {
    setLoading(true);
    const fields = [
      'id',
      'name',
      'email', // Work Email
      'email2', // Personal Email
      'customText1', // linkedinProfileUrl
      'status',
      'phone',
      'occupation',
      'address',
      'clientCorporation',
    ].join(',');
    let query = `tearsheets.id=${hotListId}`;
    if (searchText?.trim()) {
      query += ` AND name LIKE '%${searchText}%'`;
    }
    const start =
      pagination.page === 1 ? 0 : parseInt(pagination.page) - 1 + '0';
    try {
      const { data } = await searchCommonBulhorn(
        'ClientContact',
        query,
        fields,
        start,
        pagination.pageSize
      );
      const dataListContactTemp = data?.result?.data?.map((item) => ({
        ...item,
        key: uuid(),
      }));
      setPagination({
        ...pagination,
        total: data?.result?.total || data?.result?.count || 0,
      });
      setDataListContact(dataListContactTemp);
      const listEmail = await dataListContactTemp?.map((item) => item.email);
      const { data: dataListEmailValid } = await getNewEmailValid({
        emails: listEmail,
      });
      setListEmailChecked(dataListEmailValid?.result);

      setLoading(false);
    } catch (error) {
      setLoading(false);
      // notification.error({
      //   description: error
      // })
    }
  };

  const handleValidEmail = async (email = '') => {
    try {
      setValidateEmailLoading(true);
      const listEmailToSave = !email
        ? dataListContact
            .filter((contact) => selectedRowKeys.includes(contact?.id))
            .map((item) => item?.email)
        : [email];

      const { data } = await validListEmail({
        emails: listEmailToSave,
      });

      setValidateEmailLoading(false);
      setListEmailChecked([...listEmailChecked, ...data?.result]);

      await handleCreateNewEmailValid(
        data?.result?.map((item) => ({
          email: item?.email,
          status: item?.result,
        }))
      );
      notification.success({
        description: `Validated ${selectedRowKeys?.length} emails successful!`,
      });
      setSelectedRowKeys([]);
    } catch (error) {
      setValidateEmailLoading(false);
      notification.success({
        description: `Validated ${selectedRowKeys?.length} emails failed. Try again later!`,
      });
    }
  };

  const handleDeleteContact = async (contactId) => {
    try {
      const res = await deleteBullhornContact({
        id: contactId,
        entity: 'ClientContact',
      });
      // handleGetContact();
      const currContacts = [...dataListContact];
      const newList = currContacts?.filter((item) => item?.id !== contactId);
      setDataListContact([...newList]);
      notification.success({
        description: 'Contact was deleted from the HotList!',
      });
    } catch (error) {
      notification.error({
        description: 'Network error! Try again later.',
      });
    }
  };

  const handleCreateNewEmailValid = async (body) => {
    try {
      const { data } = await createNewEmailValid({ data: body });
    } catch (e) {
      notification.error({
        description: 'Network error! Try again later.',
      });
    }
  };

  const removeContactFromHotList = async (contactId) => {
    try {
      const updatedPayload = {
        ids: [parseInt(hotListId)],
        clientContacts: {
          remove: [contactId],
        },
      };
      const res = await bullhornMassUpdate({
        entityName: 'Tearsheet',
        updatedPayload,
      });
      // handleGetContact();
      const currContacts = [...dataListContact];
      const newList = currContacts?.filter((item) => item?.id !== contactId);
      setDataListContact([...newList]);
      notification.success({
        description: 'Contact was removed from the HotList!',
      });
    } catch (error) {
      notification.error({
        description: 'Network error! Try again later.',
      });
    }
  };

  const renderOption = (option) => {
    const data = option?.data || null;
    if (!data) return <div>{option?.label}</div>;

    return data?.value?.includes('linkedin') ? (
      <div className="flex items-start flex-col">
        <span className="flex items-center gap-1">
          <LinkedinOutlined className="text-[#0288d1]" />
          <span className="italic">{`${option?.label}`}</span>
        </span>
        <span className="text-xs italic text-gray-600">
          {handleRenderTime(data?.createdAt)}
        </span>
      </div>
    ) : (
      <div className="flex items-start flex-col">
        <span>{option?.label}</span>
        <span className="text-xs italic text-gray-600">
          {handleRenderTime(data?.createdAt)}
        </span>
      </div>
    );
  };

  const handleSelectEnrichData = async (newItem) => {
    setLoadingContact(true);
    try {
      console.log('newItem: ', newItem);
      const payload = {
        entityName: 'ClientContact',
        ...newItem,
      };
      delete payload?.key;
      const res = await upladteBullhorn(newItem?.id, payload);
      const newData = [...dataListContact];
      const updatedIndex = newData?.findIndex(
        (item) => item?.id === newItem?.id
      );
      if (updatedIndex > -1) {
        newData[updatedIndex] = { ...newItem };
        setDataListContact([...newData]);
      }
      notification.success({
        description: 'contact updated!',
      });
      setLoadingContact(false);
    } catch (error) {
      console.log('handleSelectEnrichData error: ', error);
      setLoadingContact(false);
    }
  };

  const columnJogLogs = [
    {
      title: 'Linkedin Profile',
      dataIndex: 'customText1',
      key: 'customText1',
      editable: true,
      render: (text, record, index) => {
        // enrich data feature

        const linkedinOptions = arrayUniqueByKey(
          enrichData,
          'linkedin_url'
        )?.filter(
          (enrichItem) =>
            parseInt(enrichItem?.record_id) === record?.id &&
            enrichItem?.linkedin_url &&
            enrichItem?.linkedin_url?.trim() !== record?.customText1?.trim()
        );
        const linkedinOptionsCount = linkedinOptions?.length || 0;

        const splittedLinkedinUrl = text?.split('/');
        const subName =
          splittedLinkedinUrl?.length > 0
            ? splittedLinkedinUrl[splittedLinkedinUrl?.length - 1] ||
              splittedLinkedinUrl[splittedLinkedinUrl?.length - 2]
            : '';
        return (
          <div className="flex flex-col gap-1">
            {
              linkedinOptionsCount > 0 ? (
                <>
                  <Tooltip
                    title={`Having ${linkedinOptionsCount} option(s) after enriching`}
                  >
                    <Badge count={linkedinOptionsCount}>
                      <div className="flex items-center">
                        <LinkedinOutlined className="text-[#0288d1]" />
                        <Select
                          onSelect={(value) =>
                            handleSelectEnrichData({
                              ...record,
                              customText1: value,
                            })
                          }
                          optionRender={renderOption}
                          onClick={(evt) => evt.stopPropagation()}
                          rootClassName="!pl-0"
                          className="min-w-fit"
                          defaultValue={subName?.trim() || 'Missing input'}
                          options={[...linkedinOptions].map((item) => {
                            const splittedLinkedinUrl =
                              item?.linkedin_url?.split('/');
                            const subName =
                              splittedLinkedinUrl?.[
                                splittedLinkedinUrl?.length - 1
                              ] ||
                              splittedLinkedinUrl?.[
                                splittedLinkedinUrl?.length - 2
                              ] ||
                              '';
                            return {
                              ...item,
                              value: item?.linkedin_url,
                              label: subName,
                            };
                          })}
                        />
                      </div>
                    </Badge>
                  </Tooltip>
                </>
              ) : (
                <a
                  className="flex items-center justify-start w-full font-medium text-sm italic text-[#0288d1] gap-1"
                  href={text || '#'}
                  target="_blank"
                >
                  <LinkedinOutlined className="text-[#0288d1]" />
                  <span>{subName || 'Not Linked'}</span>
                </a>
              )
              // (
              //   <a className="flex items-center justify-start w-full font-medium text-sm italic text-[#0288d1] cursor-not-allowed gap-1">
              //     <LinkedinOutlined className="text-[#0288d1]" />
              //     <span>Not linked</span>
              //   </a>
              // )
            }
          </div>
        );
      },
    },
    // {
    //   title: 'Contact Name',
    //   dataIndex: 'name',
    //   key: 'name',
    //   editable: true,
    //   render: (text, record, index) => {
    //     // enrich data feature
    //     const nameOptions = arrayUniqueByKey(enrichData, 'person')?.filter(
    //       (enrichItem) =>
    //         parseInt(enrichItem?.record_id) === record?.id &&
    //         enrichItem?.person &&
    //         enrichItem?.person?.trim() !== record?.name?.trim()
    //     );
    //     const nameOptionsCount = nameOptions?.length || 0;

    //     return (
    //       <div className="flex items-center gap-3">
    //         {/* <Avatar>{Array.from(record?.name ?? 'admin')[0]}</Avatar> */}
    //         <div className="flex flex-col gap-1">
    //           {nameOptionsCount > 0 ? (
    //             <>
    //               <Tooltip
    //                 title={`Having ${nameOptionsCount} option(s) after enriching`}
    //               >
    //                 <Badge count={nameOptionsCount}>
    //                   <Select
    //                     onSelect={(value) =>
    //                       handleSelectEnrichData({
    //                         ...record,
    //                         name: value,
    //                       })
    //                     }
    //                     optionRender={renderOption}
    //                     className="!border-none"
    //                     defaultValue={record?.name?.trim() || 'Missing input'}
    //                     options={[...nameOptions].map((item) => ({
    //                       ...item,
    //                       value: item?.person,
    //                       label: item?.person,
    //                     }))}
    //                   />
    //                 </Badge>
    //               </Tooltip>
    //             </>
    //           ) : (
    //             <div className="font-medium">{record?.name}</div>
    //           )}
    //         </div>
    //       </div>
    //     );
    //   },
    // },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      editable: true,
      width: '5%',
      render: (text, record, index) => {
        // enrich data feature
        const options = arrayUniqueByKey(
          enrichData,
          'valid_work_email'
        )?.filter(
          (enrichItem) =>
            parseInt(enrichItem?.record_id) === record?.id &&
            enrichItem?.valid_work_email &&
            enrichItem?.valid_work_email?.trim() !== text?.trim()
        );
        const optionsCount = options?.length || 0;

        const listEmailCheckedTemp = [...listEmailChecked];
        const itemChecked = listEmailCheckedTemp.find(
          (checkedItem) => checkedItem?.email === text
        );
        const emailValid = listEmailValid.find(
          (checkedItem) => checkedItem?.email === text
        );
        const status =
          record?.validStatus ||
          itemChecked?.result ||
          itemChecked?.status ||
          emailValid?.status ||
          emailValid?.result;
        return (
          <div className="flex items-start gap-1">
            {optionsCount > 0 ? (
              <>
                <Tooltip
                  title={`Having ${optionsCount} option(s) after enriching`}
                >
                  <Badge count={optionsCount}>
                    <Select
                      onSelect={(value) =>
                        handleSelectEnrichData({
                          ...record,
                          email: value,
                        })
                      }
                      optionRender={renderOption}
                      className="!border-none"
                      defaultValue={text?.trim() || 'Missing input'}
                      options={[...options].map((item) => ({
                        ...item,
                        value: item?.valid_work_email,
                        label: item?.valid_work_email,
                      }))}
                    />
                  </Badge>
                </Tooltip>
              </>
            ) : (
              <a
                className="flex items-center gap-1 justify-start w-fit font-medium"
                href={`mailTo:${text}`}
                target="_blank"
              >
                <MailOutlined className="text-[#0288d1]" />
                <span>{text}</span>
              </a>
            )}
            {status ? (
              <div style={{ display: 'flex' }} className="pl-2">
                <Tag
                  icon={VALIDATE_STATUS_ICON[status]}
                  color={VALIDATE_STATUS_COLOR[status]}
                >
                  {status}
                </Tag>
                {!enrichingData?.includes(record?.id?.toString()) && (
                  <Tag
                    onClick={() => {
                      handleBulkEnrich([record]);
                    }}
                    style={{ cursor: 'pointer' }}
                    icon={<SyncOutlined />}
                    color="#55acee"
                  >
                    Enrich
                  </Tag>
                )}
              </div>
            ) : (
              <Button
                icon={<CheckOutlined />}
                size="small"
                disabled={validateEmailLoading}
                onClick={() => handleValidEmail(text)}
              >
                Validate
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: 'Company Name',
      dataIndex: 'clientCorporation',
      key: 'clientCorporation',
      editable: false,
      // width: '20%',
      render: (comapny, record, index) => (
        <div className="flex items-center gap-1 justify-start w-full font-medium">
          <HomeOutlined className="text-[#0288d1]" />
          <span className="line-clamp-1" title={comapny?.name || ''}>
            {comapny?.name || ''}
          </span>
        </div>
      ),
    },
    {
      title: 'Job Title',
      dataIndex: 'occupation',
      key: 'occupation',
      editable: true,
      render: (text, record, index) =>
        text && (
          <div className="flex justify-start w-full">
            <span className="bg-[#d0fdf5] px-4 py-2 font-semibold rounded-lg flex gap-1 items-center">
              <SolutionOutlined />
              <span>{text}</span>
            </span>
          </div>
        ),
    },
    {
      title: 'Phone Number',
      dataIndex: 'phone',
      align: 'center',
      key: 'phone',
      editable: true,
      render: (text, record, index) =>
        text && (
          <a
            className="flex items-center gap-1 justify-center"
            href={`tel:${text}`}
            target="_blank"
          >
            <PhoneOutlined />
            <span>{formatPhoneNumber(text)}</span>
          </a>
        ),
    },
    // {
    //   title: 'Status',
    //   dataIndex: 'status',
    //   key: 'status',
    //   editable: true,
    //   align: 'center',
    //   render: (text, record, index) => (
    //     <span className={`px-2 py-1 ${STATUS_COLOR[text]}`}>{text}</span>
    //   ),
    // },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      render: (text, record) => {
        const editable = isEditing(record);
        return (
          <div className="flex items-center justify-center w-full font-semibold ">
            {editable ? (
              <>
                <Button
                  type="dashed"
                  className="flex gap-1 items-center"
                  onClick={() => save(record?.key)}
                  icon={<CheckOutlined />}
                  loading={loadingContact}
                >
                  Save
                </Button>
                {record?.id && (
                  <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
                    <Button
                      type="text"
                      danger
                      className="flex gap-1 items-center"
                      icon={<StopOutlined />}
                    >
                      <span>Cancel</span>
                    </Button>
                  </Popconfirm>
                )}
              </>
            ) : (
              <>
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'edit',
                        label: (
                          <div className="flex gap-2 items-center">
                            <EditOutlined size={15} />
                            <span>Edit contact</span>
                          </div>
                        ),
                        onClick: () => edit(record),
                      },
                      {
                        key: 'remove-from-hotlist',
                        label: (
                          <Popconfirm
                            title="Remove the contact"
                            description={
                              <div className="flex items-center gap-1 pb-2">
                                <span>Are you sure to remove</span>
                                <span className="px-1 py-1 bg-[#41B3A2] font-medium rounded-md text-white">
                                  {record?.name}
                                </span>
                                <span>from the hotlist ?</span>
                              </div>
                            }
                            onConfirm={() =>
                              removeContactFromHotList(record?.id)
                            }
                            okText="Yes"
                            cancelText="No"
                            arrow={false}
                          >
                            <div className="flex gap-2 items-center">
                              <UserDeleteOutlined size={15} />
                              <span>Remove from HotList</span>
                            </div>
                          </Popconfirm>
                        ),
                      },
                      {
                        key: 'delete',
                        label: (
                          <Popconfirm
                            title="Delete the contact"
                            description={
                              <div className="flex items-center gap-1 pb-2">
                                <span>Are you sure to delete</span>
                                <span className="px-1 py-1 bg-[#41B3A2] font-medium rounded-md text-white">
                                  {record?.name}
                                </span>
                                <span>?</span>
                              </div>
                            }
                            onConfirm={() => handleDeleteContact(record?.id)}
                            // onCancel={cancel}
                            arrow={false}
                            okText="Yes"
                            cancelText="No"
                          >
                            <div className="flex gap-2 items-center">
                              <DeleteOutlined size={15} />
                              <span>Delete contact</span>
                            </div>
                          </Popconfirm>
                        ),
                      },
                    ],
                  }}
                  placement="bottom"
                  arrow
                >
                  <Button
                    type="text"
                    onClick={(e) => e.stopPropagation()}
                    icon={<MoreOutlined />}
                  />
                </Dropdown>
              </>
            )}
          </div>
        );
      },
    },
  ];

  const mergedColumns = columnJogLogs.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  useEffect(() => {
    handleGetContact();
  }, [hotListId, pagination.page, searchText]);

  const handlePageChange = (page) => {
    setPagination({ ...pagination, page });
    setSelectedRowKeys([]);
  };

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleGetInValidEmail = async () => {
    setLoadingInValid(true);
    const { fullDataEmail, emailList } = await handleGetContacts();
    const { data: dataValidEmail } = await getNewEmailValid({
      emails: emailList,
    });

    setListEmailValid([...listEmailValid, ...(dataValidEmail?.result || [])]);
    const result = fullDataEmail.filter((item2) => {
      const matchingItem = dataValidEmail?.result.find(
        (item1) => item1.email === item2.email
      );
      return matchingItem && matchingItem.status === 'Invalid';
    });

    setDataListInvalidEmail(
      result?.map((item) => ({
        ...item,
        validStatus: 'Invalid',
      }))
    );
    setLoadingInValid(false);
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (!el) return;
    if (type === 'add') {
      setEnrichLoading(true);
      el.classList.add('expanded');
      el.classList.add('collapsed');
    } else if (type === 'remove') {
      setEnrichLoading(false);
      el.classList.remove('expanded');
      el.classList.remove('collapsed');
    }
  };

  const endProcess = () => {
    toggleLoading('remove');
  };

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    setUpdatingData({ ...enrichedDataObject });
    message.success(`Enriched 1 contact successfully!`);
  };

  const handleBulkEnrich = async (contacts = []) => {
    message.info('Starting enrich contact(s) data!');
    const selectedContacts =
      contacts?.length === 0
        ? dataListContact.filter((contact) =>
            selectedRowKeys?.includes(contact?.id)
          )
        : contacts;
    if (!selectedContacts || selectedContacts?.length === 0) return;

    const enrichContacts = [...selectedContacts].map((contact) => ({
      ...contact,
      person: contact?.name || '',
      // record_id: contact?.id,
      record_id: contact?.id, // For testing only
      // company: contact?.clientCorporation?.name || '',
      linkedin_url: contact?.customText1 || contact?.linkedInProfileUrl,
      work_email_2: '',
      valid_work_email: contact?.email || contact?.linkedInProfileUrl,
    }));

    toggleLoading();
    const delay = parseInt(
      selectedRowKeys.length *
        defaultEnrichFields.length *
        TIME_ENRICH_EACH_FIELD,
      10
    );

    var timeout = new Date().getTime() + delay;
    setEstimationDuration(timeout);
    const selectedContactsPayload = enrichContacts
      ?.filter(
        (contact) =>
          contacts?.length > 0 || selectedRowKeys.includes(contact?.id)
      )
      .map((contact) => ({
        fullName: contact?.person || '',
        recordId: contact?.record_id,
        companyName: contact?.company || '',
        linkedInUrl: contact?.linkedin_url,
      }));

    const payload = {
      data: selectedContactsPayload,
      listId: hotListId,
      type: 'HOTLIST',
    };
    setEnrichingData([
      ...enrichingData,
      ...selectedContactsPayload?.map((item) => item?.recordId),
    ]);
    await handleBulkEnrichContactData(payload);
  };

  const handleEnrichAll = async () => {
    message.info('Starting enrich contact(s) data!');
    toggleLoading();

    setEnrichingData([...dataListContact?.map((item) => item?.id)]);
    await enrichAllHotList(hotListId);
  };

  const handleBulkAction = async (evt) => {
    const { key } = evt;
    switch (key) {
      case 'validate-emails':
        handleValidEmail();
        break;
      case 'enrich-emails':
        handleBulkEnrich();
        break;
      case 'validate-all-emails':
        handleGetAllContact();
      case 'enrich-all':
        handleEnrichAll();
        break;
    }
  };

  const tabItems = [
    {
      key: 'tab_all',
      label: 'All',
      children: (
        <div>
          <div className="flex w-full justify-between col-span-10">
            <div style={{ display: 'flex' }}>
              {selectedRowKeys?.length > 0 && (
                <div>
                  <Dropdown
                    className="animated fadeInDownBig"
                    placement="bottom"
                    // arrow
                    menu={{
                      items: [
                        {
                          key: 'enrich-emails',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <ThunderboltOutlined />
                              <span>Bulk Enrich</span>
                            </a>
                          ),
                        },
                        {
                          key: 'enrich-all',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <ThunderboltOutlined />
                              <span>Enrich All</span>
                            </a>
                          ),
                        },
                        {
                          key: 'validate-emails',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <AuditOutlined />
                              <span>Validate Emails</span>
                            </a>
                          ),
                        },
                        {
                          key: 'validate-all-emails',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <InteractionOutlined />
                              <span>Validate All Emails</span>
                            </a>
                          ),
                        },
                      ],
                      onClick: handleBulkAction,
                    }}
                  >
                    <Space>
                      <Button
                        type="primary"
                        className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] animated fadeInDownBig"
                        loading={validateEmailLoading || loadingValidAllContact}
                      >
                        <p className="Montserrat">
                          {`${selectedRowKeys?.length} Selected`}
                        </p>
                        <DownOutlined />
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              )}
            </div>
            <Input.Search
              onSearch={(value) => {
                setSearchText(value);
                setPagination({ ...pagination, page: 1 });
              }}
              size="middle"
              className="customize-search-container max-w-sm"
              allowClear
              placeholder="Search contact..."
              enterButton="Search"
              loading={loading}
            />
          </div>
          <div className="col-span-10 search-table-new-design-container">
            {percent && (
              <Progress
                percent={percent}
                status="active"
                strokeColor={{ from: '#108ee9', to: '#87d068' }}
              />
            )}

            <Form form={form} component={false}>
              <Table
                className="contact-table-container"
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                rowClassName="editable-row"
                columns={mergedColumns}
                dataSource={dataListContact || []}
                rowSelection={editingKey ? null : rowSelection}
                rowKey={(record) => record.id}
                locale={{
                  emptyText: (
                    <Empty description="No data found" className="w-full" />
                  ),
                }}
                pagination={false}
                loading={loading || loadingContact}
              />
              <Pagination
                className="mt-3"
                current={pagination.page}
                total={pagination.total}
                showSizeChanger={false}
                onChange={handlePageChange}
              />
            </Form>
          </div>
        </div>
      ),
    },
    {
      key: 'tab_invalid',
      label: 'Invalid',
      children: (
        <div>
          <div className="col-span-10 search-table-new-design-container">
            <Button
              loading={validateEmailLoading}
              disabled={selectedRowKeys?.length === 0}
              onClick={showBulkEnrichModal}
              type="primary"
              style={{ marginBottom: '20px' }}
              className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] animated fadeInDownBig"
            >
              <ThunderboltOutlined />
              <p>Bulk Enrich</p>
            </Button>
            <div className="custom-valid-table">
              <Form form={form} component={false}>
                <Table
                  rowClassName="editable-row"
                  columns={mergedColumns.filter(
                    (item) => item?.dataIndex !== 'actions'
                  )}
                  dataSource={dataListInValidEmail || []}
                  locale={{
                    emptyText: (
                      <Empty description="No data found" className="w-full" />
                    ),
                  }}
                  rowSelection={editingKey ? null : rowSelection}
                  rowKey={(record) => record.id}
                  // pagination={false}
                  loading={loadingGetInValid}
                />
              </Form>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const getEnrichData = async (query) => {
    try {
      const { data } = await handleGetEnrichData(hotListId, query);
      console.log('getEnrichData: ', data);
      return data?.result?.items || [];
    } catch (error) {
      return [];
    }
  };

  const checkEnrichingData = async () => {
    const query = {
      type: 'HOTLIST',
      // status: 'IN_PROGRESS',
    };
    const enrichedDataTemp = await getEnrichData(query);

    if (enrichedDataTemp?.length > 0) {
      const inprogressList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.IN_PROGRESS
      );
      const sucessfullList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.SUCCESSFUL
      );
      const enrichedData = sucessfullList?.map((contact) => ({
        ...contact?.enrichContact,
        createdAt: contact?.createdAt,
      }));

      if (inprogressList?.length > 0) {
        setEnrichingData(
          [...inprogressList]?.map((item) => item?.contactId?.toString())
        );
        toggleLoading();
      }
      setEnrichData([...enrichedData]);
    }
  };

  useEffect(() => {
    console.log(continueLoadingInValidEmail);
    if (tab === 'tab_invalid' && continueLoadingInValidEmail) {
      handleGetInValidEmail();
      setContinueLoadingInValidEmail(false);
    }

    if (tab === 'tab_all' && continueLoadingInAllEmail) {
      setContinueLoadingInAllEmail(false);
      handleGetContact();
    }
  }, [tab]);

  const handleUpdateEnrichData = async () => {
    const newDataSource = [
      ...enrichData,
      { ...updatingData, createdAt: new Date() },
    ];

    setEnrichData([...newDataSource]);
    setUpdatingData({});
    console.log('newDataSource: ', newDataSource);
    const query = {
      type: 'HOTLIST',
      status: ENRICH_DATA_STATUS.IN_PROGRESS,
    };
    const enrichedData = await getEnrichData(query);
    if (enrichedData?.length === 0) {
      endProcess();
      message.success('Enriching process finished!');
    }
  };

  useEffect(() => {
    if (isEmpty(updatingData)) return;
    handleUpdateEnrichData();
  }, [updatingData]);

  useEffect(() => {
    checkEnrichingData();
  }, []);

  return (
    <div className="font-Montserrat bg-white first-letter:p-4 rounded-md shadow-md grid  p-4 custom-tab">
      <Tabs
        tabBarExtraContent={
          <div className="col-span-10" id="expand-container-bulk-enrich">
            <div className="w-full" id="expand-contract-bulk-enrich">
              <div className="w-full justify-center items-center flex gap-4 items-center">
                {enrichLoading && (
                  <EventSourceRender
                    updateRecentlyData={updateRecentlyData}
                    sseName="ENRICH_FLOQER_DATA_LINKEDIN_URL"
                  />
                )}
                <Loading />
                <div className="font-semibold text-cyan-700 italic tracking-wide">
                  Enriching...
                </div>
              </div>
            </div>
          </div>
        }
        defaultActiveKey="1"
        items={tabItems}
        style={{
          width: '500px !important',
        }}
        onChange={(e) => {
          setTab(e);
          setSelectedRowKeys([]);
        }}
      />
      {openEnrichModal && (
        <ModalEnrich
          erichItemSelected={erichItemSelected}
          openEnrichModal={openEnrichModal}
          setOpenEnrichModal={setOpenEnrichModal}
          handleGetInValidEmail={handleGetInValidEmail}
          removeItemById={removeItemById}
          setContinueLoadingInValidEmail={setContinueLoadingInValidEmail}
        />
      )}
      <Modal
        title="Bulk Enrich contacts data"
        open={openBulkEnrichModal}
        footer={null}
        width={1200}
        closable={false}
        destroyOnClose={true}
      >
        <BulkEnrichData
          listEmailChecked={listEmailChecked.concat(listEmailValid)}
          selectedContacts={
            tab === 'tab_all'
              ? dataListContact.filter((contact) =>
                  selectedRowKeys.includes(contact?.id)
                )
              : dataListInValidEmail.filter((contact) =>
                  selectedRowKeys.includes(contact?.id)
                )
          }
          reloadData={
            tab === 'tab_all' ? handleGetContact : handleGetInValidEmail
          }
          closeBulkEnrichModal={closeBulkEnrichModal}
        />
      </Modal>
    </div>
  );
};

export default HotListTable;
