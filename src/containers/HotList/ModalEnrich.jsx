import {
  Alert,
  Button,
  Empty,
  Form,
  Modal,
  notification,
  Select,
  Table,
  Tag,
} from 'antd';
import { getListEmployee } from '../../services/employee';
import { useEffect, useState } from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LinkedinOutlined,
  PhoneOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { formatPhoneNumber } from '../../utils/common';
import { createNewEmailValid, getNewEmailValid, upladteBullhorn } from '../../services/bullhorn';
import { validListEmail } from '../../services/emailFinder';

const STATUS_COLOR = {
  unavailable: 'red',
};

const VALIDATE_STATUS_COLOR = {
  Valid: 'success',
  Risky: 'warning',
  Invalid: 'error',
};

const VALIDATE_STATUS_ICON = {
  Valid: <CheckCircleOutlined />,
  Risky: <ExclamationCircleOutlined />,
  Invalid: <CloseCircleOutlined />,
};

export const enrichFields = [
  { value: 'email', label: 'Email' },
  { value: 'occupation', label: 'Job Title' },
  { value: 'customText1', label: 'LinkedIn Profile Url' },
]

export const defaultEnrichFields = ['email', 'occupation', 'customText1']

const ModalEnrich = ({
  erichItemSelected,
  openEnrichModal,
  setOpenEnrichModal,
  handleGetInValidEmail,
  removeItemById,
  setContinueLoadingInValidEmail
}) => {
  const [form] = Form.useForm();
  const [dataContact, setDataContact] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [fieldSelect, setFieldSelect] = useState(['email', 'occupation', 'customText1']);
  const [loading, setLoading] = useState(false);
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [listEmailValid, setListEmailValid] = useState([]);
  const [showNote, setShowNote] = useState(false);

  const handleSearchData = async () => {
    setLoading(true);
    const bodyToSearch = {
      searchText:
        erichItemSelected.name + ' ' + erichItemSelected.clientCorporation.name,
    };
    const { data } = await getListEmployee(bodyToSearch);
    let dataSet =
      data?.result.contacts?.length > 0
        ? data?.result.contacts
        : data?.result.people || [];

    if (dataSet.length == 0) {
      const bodyToSearchNext = {
        searchText: erichItemSelected.name,
      };
      const { data: dataNext } = await getListEmployee(bodyToSearchNext);
      dataSet =
        dataNext?.result.contacts?.length > 0
          ? dataNext?.result.contacts
          : dataNext?.result.people || [];
      
      if(dataSet.length > 0 ) {
        setShowNote(true);
      }
    }
    setDataContact(dataSet);
    setSelectedRowKeys([dataSet?.[0]?.id])

    const listEmail = dataSet?.map((item) => item.email);

    const { data: dataListEmailValid } = await validListEmail({
      emails: listEmail,
    });
    setListEmailValid(dataListEmailValid?.result);
    setLoading(false);
  };

  useEffect(() => {
    if (erichItemSelected) {
      handleSearchData();
    }
  }, [erichItemSelected]);

  const columnJogLogs = [
    // {
    //   title: 'No',
    //   dataIndex: 'name',
    //   render: (text, record, index) => (
    //     <span>{index * (currentPage + 1) + 1}</span>
    //   ),
    // },
    {
      title: 'Name',
      dataIndex: 'customText1',
      key: 'customText1',
      editable: true,
      render: (text, record, index) => <div>{record?.name}</div>,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      editable: true,
      render: (text, record, index) => {
        const itemChecked = listEmailValid.find(
          (checkedItem) => checkedItem?.email === record?.email
        );
        return (
          <>
            {record?.email}{' '}
            <span>
              <Tag
                icon={VALIDATE_STATUS_ICON[itemChecked?.result]}
                color={VALIDATE_STATUS_COLOR[itemChecked?.result]}
              >
                {itemChecked?.result}
              </Tag>
            </span>
          </>
        );
      },
    },
    {
      title: 'LinkedIn',
      dataIndex: 'occupation',
      key: 'occupation',
      editable: true,
      align: 'center',
      render: (text, record, index) => (
        <a
          className="flex justify-center w-full"
          href={record?.linkedin_url}
          target="_blank"
        >
          <LinkedinOutlined
            style={{ color: '#0288d1', fontSize: '20px', cursor: 'pointer' }}
          />
        </a>
      ),
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      editable: false,
      width: '20%',
      render: (text, record, index) => (
        <div className="flex items-center gap-1 justify-start w-full font-medium">
          {record?.present_raw_address ||
            (record?.city || '') + ' ' + (record?.country || '')}
        </div>
      ),
    },
    {
      title: 'Phone',
      dataIndex: 'phone_numbers',
      align: 'center',
      key: 'phone_numbers',
      editable: true,
      render: (text, record, index) =>
        text && (
          <a
            className="flex items-center gap-1 justify-center"
            href={`tel:${text?.[0]}`}
            target="_blank"
          >
            <PhoneOutlined />
            <span>{text?.[0]?.raw_number}</span>
          </a>
        ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys([newSelectedRowKeys?.[1] || newSelectedRowKeys?.[0]]);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleEnrichData = async () => {
    if (fieldSelect.length == 0) {
      notification.error({
        message: 'Please select Field to Enrich',
      });
      return;
    }
    setLoadingUpdate(true);
    const itemSelect = dataContact?.find((item) => item.id == selectedRowKeys);
    const payload = {
      email: fieldSelect?.includes('email')
        ? itemSelect?.email
        : erichItemSelected?.email,
      entityName: 'ClientContact',
      id: erichItemSelected.id,
      occupation: fieldSelect?.includes('occupation')
        ? itemSelect?.title
        : erichItemSelected?.occupation,
      customText1: fieldSelect?.includes('customText1')
        ? itemSelect?.linkedin_url
        : erichItemSelected?.customText1,
    };
    const res = await upladteBullhorn(erichItemSelected?.id, payload);
    const emailUpdate = fieldSelect?.includes('email')
    ? itemSelect?.email
    : erichItemSelected?.email;
    const payloadUpdateValidEmail = {
      email: emailUpdate,
      status: listEmailValid.find(
        (checkedItem) => checkedItem?.email === emailUpdate
      ).result
    }
    await createNewEmailValid({data: [payloadUpdateValidEmail]});
    setContinueLoadingInValidEmail(true)
    if (res) {
      notification.success({
        message: 'Update data Success',
      });
      setOpenEnrichModal(false);
      if(!listEmailValid.find(
        (checkedItem) => checkedItem?.email === emailUpdate
      ).result === 'Invalid') {
        removeItemById(erichItemSelected.id);
      }
    }
    setLoadingUpdate(false);
  };

  return (
    <div>
      <Modal
        title={
          <div>
            Enrich <span>{erichItemSelected?.name}</span>
          </div>
        }
        open={openEnrichModal}
        onCancel={() => setOpenEnrichModal(false)}
        width={1200}
        footer={
          <div>
            <Button
              type="primary"
              onClick={() => handleEnrichData()}
              loading={loadingUpdate}
              icon={<SyncOutlined />}
            >
              Enrich
            </Button>
            <Button
              onClick={() => setOpenEnrichModal(false)}
              disabled={loadingUpdate}
            >
              Cancel
            </Button>
          </div>
        }
      >
        <div>
          <div>
            <div className="col-span-10 search-table-new-design-container">
              <div style={{ marginTop: '20px', marginBottom: '20px' }}>
                {showNote && (
                  <div
                    style={{
                      marginBottom: '10px',
                      color: 'red',
                      fontSize: '16px',
                    }}
                  >
                    <Alert
                      description={dataContact.length > 0 ? "This contact may change their job. There are some related data to this contact" : "This contact may change their job. We do not find any data related to this"}
                      type="warning"
                    />
                  </div>
                )}
                <div style={{ marginBottom: '10px' }}>
                  Select fields to enrich
                </div>
                <Select
                  mode="multiple"
                  placeholder="Please select"
                  style={{ width: '100%' }}
                  options={enrichFields}
                  defaultValue={defaultEnrichFields}
                  onChange={(e) => setFieldSelect(e)}
                />
              </div>
              <Form form={form} component={false}>
                <Table
                  rowClassName="editable-row"
                  columns={columnJogLogs}
                  dataSource={dataContact || []}
                  locale={{
                    emptyText: (
                      <Empty description="No data found" className="w-full" />
                    ),
                  }}
                  rowSelection={rowSelection}
                  rowKey={(record) => record.id}
                  pagination={false}
                  loading={loading}
                />
              </Form>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ModalEnrich;
