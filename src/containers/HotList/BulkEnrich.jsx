import {
  Badge,
  Button,
  Empty,
  message,
  notification,
  Select,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { VALIDATE_STATUS_COLOR, VALIDATE_STATUS_ICON } from './HotListTable';
import {
  CloseOutlined,
  LinkedinOutlined,
  MailOutlined,
  PhoneOutlined,
  SaveOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import Loading from './Loading';
import dayjs from 'dayjs';
import { handleBulkEnrichContactData } from '../../services/employee';
import { v4 as uuid } from 'uuid';
import clsx from 'clsx';
import { upladteBullhorn } from '../../services/bullhorn';
import { isEmpty } from 'lodash';
import EventSourceRender from '../../components/EventSource';

const TIME_ENRICH_EACH_FIELD = 35 * 1000; // seconds

const defaultEnrichFields = ['person', 'linkedin_url', 'valid_work_email'];

export const enrichFields = [
  { value: 'person', label: 'Name' },
  //   { value: 'company', label: 'Company' },
  { value: 'linkedin_url', label: 'LinkedIn Profile Url' },
  { value: 'valid_work_email', label: 'Email' },
];

const BulkEnrichData = ({
  selectedContacts,
  reloadData,
  closeBulkEnrichModal,
  isHotList = true,
  handleUpdateContact = null,
  listEmailChecked = [],
  handleSaveBulkEnrichData = null,
}) => {
  const [fieldSelect, setFieldSelect] = useState(defaultEnrichFields);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedUpdatedRowKeys, setSelectedUpdatedRowKeys] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [updatingData, setUpdatingData] = useState({});
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [estimationDuration, setEstimationDuration] = useState('0');

  const columnJogLogs = [
    {
      title: 'Name',
      dataIndex: 'person',
      key: 'person',
      editable: true,
      render: (text, record, index) => {
        const oldText = selectedContacts?.find(
          (item) => item?.id === record?.id
        )?.name;

        return (
          text && (
            <Tooltip
              title={
                record?.updatedKey?.includes('person') && oldText
                  ? `Replaced for "${oldText}"`
                  : text
              }
            >
              <div
                className={clsx(
                  record?.updatedKey?.includes('person') &&
                    'text-[#0aba9f] font-semibold'
                )}
              >
                {text}
              </div>
            </Tooltip>
          )
        );
      },
    },
    {
      title: 'Email',
      dataIndex: 'valid_work_email',
      key: 'valid_work_email',
      editable: true,
      render: (text, record, index) => {
        const oldItem = selectedContacts?.find(
          (item) => item?.id === record?.id
        );
        const oldText = oldItem?.email;
        const itemChecked = listEmailChecked.find(
          (checkedItem) => checkedItem?.email === oldItem?.email
        );
        const status =
          (text === oldItem?.email && record?.emailStatus) ||
          itemChecked?.result ||
          itemChecked?.status;
        return (
          text && (
            <Tooltip
              title={
                record?.updatedKey?.includes('valid_work_email') && oldText
                  ? `Replaced for "${oldText}"`
                  : text
              }
            >
              <div className="flex items-center gap-1">
                <a
                  className="flex items-center gap-1 justify-start w-fit font-medium"
                  href={`mailTo:${text}`}
                  target="_blank"
                >
                  <MailOutlined className="text-[#0288d1]" />
                  <span
                    className={clsx(
                      record?.updatedKey?.includes('valid_work_email') &&
                        'text-[#0aba9f] font-semibold'
                    )}
                  >
                    {text}
                  </span>
                </a>
                {status &&
                  !record?.updatedKey?.includes('valid_work_email') && (
                    <Tag
                      icon={VALIDATE_STATUS_ICON[status]}
                      color={VALIDATE_STATUS_COLOR[status]}
                    >
                      {status}
                    </Tag>
                  )}
              </div>
            </Tooltip>
          )
        );
      },
    },
    {
      title: 'LinkedIn',
      dataIndex: 'linkedin_url',
      key: 'linkedin_url',
      editable: true,
      align: 'center',
      render: (text, record, index) => {
        const oldText = selectedContacts?.find(
          (item) => item?.id === record?.id
        )?.customText1;
        return (
          text && (
            <Tooltip
              title={
                record?.updatedKey?.includes('linkedin_url') && oldText
                  ? `Replaced for "${oldText}"`
                  : text
              }
            >
              <a
                className="flex justify-center w-full items-center gap-2"
                href={text}
                target="_blank"
              >
                <LinkedinOutlined
                  style={{
                    color: '#0288d1',
                    fontSize: '20px',
                    cursor: 'pointer',
                  }}
                />
                <p
                  className={clsx(
                    'line-clamp-1',
                    record?.updatedKey?.includes('linkedin_url') &&
                      'text-[#0aba9f] font-semibold'
                  )}
                >
                  {text}
                </p>
              </a>
            </Tooltip>
          )
        );
      },
    },
    // {
    //   title: 'Company',
    //   dataIndex: 'company',
    //   key: 'company',
    //   editable: false,
    //   width: '20%',
    //   render: (text, record, index) =>
    //     text && (
    //       <div
    //         className={clsx(
    //           'flex items-center gap-1 justify-start w-full font-medium',
    //           record?.updatedKey?.includes('valid_work_email') &&
    //             'text-[#0aba9f] font-semibold'
    //         )}
    //       >
    //         {text}
    //       </div>
    //     ),
    // },
    {
      title: 'Phone',
      dataIndex: 'phone',
      align: 'center',
      key: 'phone',
      editable: true,
      render: (text, record, index) =>
        text && (
          <a
            className="flex items-center gap-1 justify-center"
            href={`tel:${text?.[0]}`}
            target="_blank"
          >
            <PhoneOutlined />
            <span
              className={clsx(
                'max-w-xs line-clamp-1',
                record?.updatedKey?.includes('phone') &&
                  'text-[#0aba9f] font-semibold'
              )}
            >
              {text}
            </span>
          </a>
        ),
    },
    // {
    //   title: 'Action',
    //   dataIndex: 'record_id',
    //   align: 'center',
    //   key: 'record_id',

    // },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys([...newSelectedRowKeys]);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (type === 'add') {
      el.classList.add('expanded');
      el.classList.add('collapsed');
    } else if (type === 'remove') {
      el.classList.remove('expanded');
      el.classList.remove('collapsed');
    }
  };

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    let updatedNumber = 0;
    const updatingData = dataSource.find(
      (contact) =>
        contact?.record_id === enrichedDataObject?.record_id &&
        selectedRowKeys?.includes(contact?.id)
    );
    const updatedKey = [];
    const newContact = { ...updatingData };
    for (const [key, value] of Object.entries(enrichedDataObject)) {
      if (
        value &&
        updatingData[key]?.trim() !== value?.toString()?.trim() &&
        fieldSelect.includes(key)
      ) {
        newContact[key] = value;
        updatedKey.push(key);
        updatedNumber += 1;
      }
    }
    setUpdatingData({ ...newContact, updatedKey });
    message.success(`Updated ${updatedNumber} cell(s) successfully!`);
  };

  const endProcess = () => {
    toggleLoading('remove');
    setLoading(false);
  };

  const handleEnrichData = async () => {
    message.info('Enriching process started!');
    setLoading(true);
    toggleLoading();
    const delay = parseInt(
      selectedRowKeys.length * fieldSelect.length * TIME_ENRICH_EACH_FIELD,
      10
    );
    var timeout = new Date().getTime() + delay;
    setEstimationDuration(timeout);
    const selectedContactsPayload = dataSource
      ?.filter((contact) => selectedRowKeys.includes(contact?.id))
      .map((contact) => ({
        fullName: contact?.person || '',
        recordId: contact?.record_id,
        companyName: contact?.company || '',
        linkedInUrl: contact?.linkedin_url,
      }));

    const payload = {
      data: selectedContactsPayload,
    };

    await handleBulkEnrichContactData(payload);
    setTimeout(() => {
      endProcess();
      message.error('Enriching process ended!');
    }, delay);
  };

  const handleCancelEnrichProcess = () => {
    endProcess();
    message.error('Enriching process stopped!');
  };

  const handleParseHotListData = (contact) => ({
    entityName: 'ClientContact',
    id: contact?.id,
    name: contact?.person,
    email: contact?.valid_work_email,
    customText1: contact?.linkedin_url,
  });

  const handleParseContactListData = (rawData) => {
    const newData = { ...rawData };
    newData.name = rawData?.person;
    newData.linkedInProfileUrl = rawData?.linkedin_url;
    newData.email = rawData?.valid_work_email;

    // Remove unuse fields
    delete newData.person;
    delete newData.linkedin_url;
    delete newData.valid_work_email;
    delete newData.work_email_2;
    delete newData.company;
    delete newData.record_id;

    return newData;
  };

  const handleSaveContacts = async () => {
    if (handleSaveBulkEnrichData) {
      handleSaveBulkEnrichData(dataSource);
      closeBulkEnrichModal();
      return;
    }
    setSaveLoading(true);
    if (isHotList) {
      const updateBHContactsPromiseData = dataSource
        .filter((item) => selectedRowKeys.includes(item?.id))
        .map((item) => handleParseHotListData(item));
      await Promise.all(
        updateBHContactsPromiseData.map(
          async (payload) => await upladteBullhorn(payload?.id, payload)
        )
      )
        .then((res) => {
          console.log('upladteBullhorn: ', res);
          reloadData();
          closeBulkEnrichModal();

          notification.success({
            description: 'Contact updated!',
          });
        })
        .catch((err) => {
          console.log('upladteBullhorn err: ', err);
          notification.error({
            description: 'Something went wrong!',
          });
        })
        .finally(() => {
          setSaveLoading(false);
        });
    } else {
      const dataUpdate = dataSource.filter((item) =>
        selectedRowKeys.includes(item?.id)
      );
      try {
        const res = await Promise.all(
          dataUpdate?.map(
            async (item) =>
              await handleUpdateContact(
                handleParseContactListData(item),
                item?.key
              )
          )
        );
        reloadData();
        closeBulkEnrichModal();
        setSaveLoading(false);
      } catch (error) {
        console.log('bulkUpdateContacts: ', error);
        notification.error({
          description: 'Something went wrong!',
        });
        setSaveLoading(false);
      }
    }
  };

  useEffect(() => {
    if (isEmpty(updatingData)) return;

    const newDataSource = [...dataSource];
    const updatingContactIndex = newDataSource.findIndex(
      (contact) => contact?.id === updatingData?.id
    );
    if (updatingContactIndex < 0) return;
    newDataSource[updatingContactIndex] = updatingData;
    setDataSource([...newDataSource]);
    setUpdatingData({});

    const newSelectedUpdatedRowKeys = [
      ...selectedUpdatedRowKeys,
      updatingData?.id,
    ];

    setSelectedUpdatedRowKeys([...newSelectedUpdatedRowKeys]);
    if (newSelectedUpdatedRowKeys?.length === selectedRowKeys?.length) {
      setSelectedUpdatedRowKeys([]);
      endProcess();
      message.error('Enriching process finished!');
      return;
    }
  }, [updatingData]);

  useEffect(() => {
    if (!selectedContacts || selectedContacts?.length === 0) return;
    setDataSource(
      [...selectedContacts].map((contact) => ({
        ...contact,
        person: contact?.name || '',
        record_id: uuid(),
        // record_id: '11111', // For testing only
        // company: contact?.clientCorporation?.name || '',
        linkedin_url: contact?.customText1 || contact?.linkedInProfileUrl,
        work_email_2: '',
        valid_work_email: contact?.email,
      }))
    );
  }, []);

  return (
    <div>
      <div className="col-span-10 search-table-new-design-container">
        <div style={{ marginTop: '20px', marginBottom: '20px' }}>
          <div style={{ marginBottom: '10px' }}>Select fields to enrich</div>
          <Select
            mode="multiple"
            placeholder="Please select"
            style={{ width: '100%' }}
            options={enrichFields}
            value={fieldSelect}
            onChange={(e) => {
              setFieldSelect([...e]);
            }}
          />
        </div>

        <Table
          rowClassName="editable-row"
          columns={columnJogLogs}
          dataSource={dataSource || []}
          locale={{
            emptyText: <Empty description="No data found" className="w-full" />,
          }}
          rowSelection={rowSelection}
          rowKey={(record) => record.id}
          // pagination={false}
          pagination={{
            pageSize: 5,
          }}
          //   loading={loading}
        />
        <div className="col-span-10" id="expand-container-bulk-enrich">
          <div className="w-full" id="expand-contract-bulk-enrich">
            <div className="w-full justify-center items-center flex flex-col">
              {loading && (
                <EventSourceRender
                  updateRecentlyData={updateRecentlyData}
                  sseName="ENRICH_FLOQER_DATA_LINKEDIN_URL"
                />
              )}
              <Loading />
              <div>{`Finishing at ${dayjs(estimationDuration).format('HH:mm:ss')}`}</div>
            </div>
          </div>
        </div>
        <div className="rounded-md border px-4 py-3 mt-3 shadow-sm flex items-center justify-between gap-2">
          <div className="flex items-center gap-3">
            <div className="flex gap-2 items-center">
              <Badge showZero count={selectedRowKeys.length || 0} />
              <p>selected</p>
            </div>

            <Button
              onClick={handleEnrichData}
              disabled={selectedRowKeys.length === 0 || loading}
              icon={<ThunderboltOutlined />}
            >
              Bulk Enrich
            </Button>

            {loading && (
              <Button
                danger
                onClick={handleCancelEnrichProcess}
                icon={<CloseOutlined />}
              >
                Cancel Process
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              disabled={loading}
              icon={<CloseOutlined />}
              onClick={closeBulkEnrichModal}
            >
              Close
            </Button>
            <Button
              type="primary"
              disabled={selectedRowKeys.length === 0 || loading}
              icon={<SaveOutlined />}
              onClick={handleSaveContacts}
              loading={saveLoading}
            >
              {`Save all (${selectedRowKeys.length})`}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkEnrichData;
