import { Button, notification, Upload } from 'antd';
import { FileExcelOutlined, LeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import HotListTable from './HotListTable';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';

const HotList = () => {
  const [hotList, setHotList] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  let { hotListId } = useParams();

  const getHotList = async () => {
    try {
      const { data } = await searchBullhorn(
        'Tearsheet',
        0,
        1,
        '',
        '',
        '',
        '',
        hotListId
      );
      if (data?.result?.length > 0) {
        const selectedHotList = data?.result[0];
        setHotList(selectedHotList);
      } else {
        notification.error({
          description: 'HotList is not existing or deleted by user!',
        });
      }
      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Network error! please try again later!',
      });
      setLoading(false);
    }
  };

  useEffect(() => {
    getHotList();
  }, []);

  return (
    <div className="flex flex-col gap-5 p-4">
      <div>
        <div className="flex justify-between text-2xl font-semibold">
          <div className="flex gap-3 items-center">
            <Button
              className="bg-white flex items-center "
              disabled={loading}
              onClick={() => {
                navigate(-1);
              }}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
            <span className="Montserrat">{hotList?.name || 'Hot List'}</span>
          </div>
        </div>
      </div>
      <HotListTable
      //  isFetching={loading}
      //  dataSource={data}
      //  contactListId={contactListId}
      //  getAllContacts={getAllContacts}
      //  setDataSource={setData}
      //  handleAddNewContact={handleAddNewContact}
      //  contactLoading={contactLoading}
      //  handleUpdateContact={handleUpdateContact}
      //  pagination={pagination}
      //  handlePagination={handlePagination}

      // totalCount={hotList?.clientContacts?.total}
      />
    </div>
  );
};

export default HotList;
