import {
  ArrowLeftOutlined,
  BankOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EnvironmentOutlined,
  ImportOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  PhoneOutlined,
  SyncOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  AutoComplete,
  Button,
  Checkbox,
  Collapse,
  DatePicker,
  Input,
  notification,
  Select,
  Spin,
  Steps,
  Table,
  Tag,
} from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import {
  bulkImportLeads,
  querySearchCommonBulhorn,
  searchBullhornData,
  searchCommonBulhorn,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import _ from 'lodash';
import dayjs from 'dayjs';
import { getAllLeadSheets } from '../../services/leadSheet';
import { LEAD_STATUS_TYPE_ICON } from '../MyLeads/NewMyLeads';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { tagRender } from '../../components/SearchDetailV2/NewSearchFilterComponent';
import { arrayUniqueByKey } from '../../utils/common';
import { checkExistingJobLeadByExternalIds } from '../../services/jobLeads';
import { getUserViewAsLicenseType } from '../../helpers/getUserViewAs';
import { licenseType } from '../../constants/common.constant';

const RestyledCollapse = styled(Collapse)`
  border-radius: 0 !important;
  border-color: #f4f4f4 !important;
  .ant-collapse-content,
  .ant-collapse-item {
    border-color: #f4f4f4 !important;
    border-radius: 0 !important;
  }

  .ant-select-selection-item,
  .ant-picker-input > input {
    color: rgb(8 145 178 / 1);
    font-weight: 500;
  }
`;

const ImportLeadsPage = () => {
  const { setAuth, profile: profileUserAuth } = useAuth();
  const { profileUser, setViewAs } = useViewAs();

  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const userToSet = profileUser || profileUserAuth;
  const userToSetId = userToSet?.user?.id || userToSet?.id;

  const timeoutRef = useRef(null);

  const [currentStep, setCurrentStep] = useState(0);
  const [filters, setFilters] = useState({
    consultant: null,
    company: [],
    date: [],
  });
  const [loadingVacancies, setLoadingVacancies] = useState(false);
  const [loadingLeadSheet, setLoadingLeadSheet] = useState(true);
  const [leadSheets, setLeadSheets] = useState([]);
  const [vacancies, setVacancies] = useState([]);
  const [selectedVacancies, setSelectedVacancies] = useState([]);
  const [selectedLeadSheet, setselectedLeadSheet] = useState([]);
  const [importLoading, setImportLoading] = useState(false);

  const [isHideSyncedLeads, setHideSyncedLeads] = useState(true);
  const toggleHideSyncedLeads = () => setHideSyncedLeads(!isHideSyncedLeads);

  const navigate = useNavigate();

  const back = () => {
    if (window?.history?.length > 1) {
      navigate(-1);
    } else {
      navigate('/crm');
    }
  };

  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    loadMoreOptions: loadConsultant,
    isLoading: isLoadingConsultants,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  const filterVacancies = async (vacancies) => {
    let newVacancies = [...vacancies];

    try {
      const externalIds = newVacancies.map((vacancy) => vacancy?.id);
      const { data } = await checkExistingJobLeadByExternalIds(externalIds);
      const existingIds = data?.result?.existingIds || [];
      if (existingIds?.length > 0) {
        newVacancies = newVacancies.map((vacancy) => ({
          ...vacancy,
          isExisting: existingIds.includes(vacancy?.id?.toString()),
        }));
        return newVacancies;
      }
      console.log('newVacancies in filterVacancies: ', newVacancies);

      return newVacancies;
    } catch (error) {
      console.log('Error in filterVacancies: ', error);
      return newVacancies;
    }
  };

  const handleGetAllVacancies = async () => {
    setLoadingVacancies(true);
    try {
      let query = `isDeleted:0 `;

      if (filters?.date?.length > 0) {
        const startDate = dayjs(filters?.date?.[0], 'DD-MM-YYYY').format(
          'YYYYMMDD000000'
        );
        const endDate = dayjs(filters?.date?.[1], 'DD-MM-YYYY').format(
          'YYYYMMDD235959'
        );
        query += ` AND dateAdded:[${startDate} TO ${endDate}]`;
      }

      if (filters?.consultant?.id) {
        query += ` AND owner.id:(+${filters?.consultant?.id})`;
      }

      if (filters?.company?.length > 0) {
        const companyIds = filters?.company.map((co) => `${co?.id}`).join(' ');
        query += ` AND clientCorporation.id:(${companyIds})`;
      }

      query += ` AND NOT status:Archive`;

      const fields = `id,address,businessSectors,categories,clientContact,clientCorporation,description,employmentType,isOpen,markUpPercentage,owner,payRate,salary,skills,source,startDate,status,title,dateAdded`;

      const { data } = await querySearchCommonBulhorn(
        'JobOrder',
        query,
        fields,
        0,
        500
      );
      const vacanciesTemp = data?.result?.data || [];

      if (vacanciesTemp?.length > 0) {
        const filteredVacancies = await filterVacancies(vacanciesTemp);
        setVacancies([...filteredVacancies]);
      } else {
        setVacancies([]);
      }
      setLoadingVacancies(false);
    } catch (error) {
      console.log('Error in getVacancyOptions: ', error);
      setLoadingVacancies(false);
    }
  };

  const handleGetAllLeadSheet = async () => {
    try {
      const { data } = await getAllLeadSheets();
      if (data?.result?.length > 0) {
        setLeadSheets([...data?.result]);
      }
      setLoadingLeadSheet(false);
    } catch (error) {
      console.log('Error in getLeadSheetOptions: ', error);
      setLoadingLeadSheet(false);
    }
  };

  const getCurrentImportingLeads = () => {
    const leadSheetsList = leadSheets.filter((leadSheet) =>
      selectedLeadSheet.includes(leadSheet?.id)
    );
    const vacanciesList = vacancies.filter((vacancy) =>
      selectedVacancies.includes(vacancy?.id)
    );
    const data = leadSheetsList.flatMap((leadSheet) => {
      const importVacanciesOnSheet = vacanciesList.map((vacancy) => ({
        bullHornJobId: vacancy?.id,
        skills:
          vacancy?.skills?.data?.length > 0
            ? vacancy?.skills?.data?.map((skill) => ({
                label: skill?.name,
                value: skill?.id,
                key: skill?.id,
              }))
            : [],
        source: vacancy?.source,
        employmentType: vacancy?.employmentType,
        state: vacancy?.address?.countryName,
        stateId: vacancy?.address?.countryID,
        stateSelected: vacancy?.address?.countryID,
        clientContact: {
          ...vacancy?.clientContact,
        },
        jobtype: vacancy?.employmentType,
        jobtitle: vacancy?.title,
        description: vacancy?.description,
        status: vacancy?.status,
        leadSheetId: leadSheet?.id,
        consultant: `${vacancy?.owner?.firstName || ''} ${vacancy?.owner?.lastName || ''}`,
        company: vacancy?.clientCorporation?.name,
        contact: `${vacancy?.clientContact?.firstName || ''} ${vacancy?.clientContact?.lastName || ''}`,
        salary: vacancy?.salary,
        payRate: vacancy?.payRate,
        permFee: vacancy?.markUpPercentage,
        businessSectors:
          vacancy?.businessSectors?.data?.length > 0
            ? vacancy?.businessSectors?.data?.map((item) => ({
                label: item?.name,
                value: item?.id,
                key: item?.id,
              }))
            : [],
        categories:
          vacancy?.categories?.data?.length > 0
            ? vacancy?.categories?.data?.map((item) => ({
                label: item?.name,
                value: item?.id,
                key: item?.id,
              }))
            : [],
        address1: vacancy?.address?.address1,
        address2: vacancy?.address?.address2,
        city: vacancy?.address?.city,
        county: vacancy?.address?.state,
        zip: vacancy?.address?.zip,
        statusSelect: vacancy?.status,
        consultantSelect: vacancy?.owner?.id,
        consultantId: vacancy?.owner?.id,
        contactConsultantSelect: vacancy?.owner?.id,
        contactConsultantId: vacancy?.owner?.id,
        contactConsultant: `${vacancy?.owner?.firstName || ''} ${vacancy?.owner?.lastName || ''}`,
        address_line_1: vacancy?.address?.address1,
        address1Select: vacancy?.address?.address1,
        address_line_2: vacancy?.address?.address2,
        address2Select: vacancy?.address?.address2,
        countySelect: vacancy?.address?.city,
        companySelect: vacancy?.clientCorporation?.id,
        companyId: vacancy?.clientCorporation?.id,
        contactId: vacancy?.clientContact?.id,
        contactSelect: vacancy?.clientContact?.id,
        email: vacancy?.clientContact?.email || '',
        companyDetail: {
          name: vacancy?.clientCorporation?.name,
          id: vacancy?.clientCorporation?.id,
        },
        companySequenceContactId: vacancy?.clientCorporation?.id,
        job: null,
        searchId: '',
        sentJobId: false,
        countryAddress: vacancy?.address?.countryName,
        updatedFor: userToSetId,
      }));

      return importVacanciesOnSheet;
    });
    return [...data];
  };

  const handleBulkImportLeads = async () => {
    setImportLoading(true);
    try {
      const data = getCurrentImportingLeads();
      const payload = {
        data,
      };
      const res = await bulkImportLeads(payload);
      setImportLoading(false);
      notification.success({
        description: 'Leads imported successfully!',
      });
      back();
    } catch (error) {
      setImportLoading(false);
      notification.error({
        description: 'Failed to import leads! Try again later!',
      });
      console.log('Error in handleBulkImportLeads: ', error);
    }
  };

  useEffect(() => {
    handleGetAllLeadSheet();
  }, []);

  useEffect(() => {
    if (isStandardUser) {
      navigate('/not-found');
    }
  }, [currentUserLicenseType]);

  const rowLeadSheetSelection = {
    selectedRowKeys: selectedLeadSheet,
    onChange: (selectedRowKeys) => {
      setselectedLeadSheet(selectedRowKeys);
    },
  };

  const rowVacanciesSelection = {
    selectedRowKeys: selectedVacancies,
    onChange: (selectedRowKeys) => {
      setSelectedVacancies(selectedRowKeys);
    },
    getCheckboxProps: (record) => ({
      disabled: record?.isExisting,
    }),
  };

  const filteredVacancies = vacancies.filter(
    (vacancy) => !isHideSyncedLeads || !vacancy?.isExisting
  );

  return (
    <div className="w-full flex flex-col h-[75vh]">
      <div className="flex items-center justify-between w-full px-2 py-3 rounded-md bg-white mb-2">
        <Button onClick={back} icon={<LeftOutlined />} className="bg-white">
          Back
        </Button>
        <div className="flex flex-col items-center justify-end max-w-xs">
          <div className="text-cyan-600 text-base font-semibold flex items-center gap-1 justify-end w-full p-2 border rounded-md">
            <ImportOutlined />
            Import Leads
          </div>
        </div>
      </div>
      <div className="h-full rounded-md bg-white shadow-md grid grid-cols-10">
        <div className="col-span-7 p-4 flex flex-col gap-4">
          <RestyledCollapse
            onChange={(keys) => {
              if (keys?.length > 0) {
                setCurrentStep(0);
              }
            }}
            activeKey={currentStep === 0 ? ['filtering'] : []}
            // expandIcon={({ isActive }) => <></>}
            expandIconPosition="end"
            items={[
              {
                key: 'filtering',
                label: (
                  <div className="w-full flex items-center justify-between">
                    <div className="flex items-center gap-2 font-semibold text-cyan-600">
                      {currentStep > 0 ? (
                        <CheckCircleOutlined className="text-lg font-bold text-green-600" />
                      ) : (
                        <CloseCircleOutlined className="text-lg font-bold text-gray-400" />
                      )}{' '}
                      Filtering
                    </div>
                    {(filters?.consultant ||
                      filters?.company?.length > 0 ||
                      filters?.date?.length > 0) &&
                      currentStep > 0 && (
                        <div className="font-medium text-sm opacity-60 flex items-center gap-2">
                          {filters?.consultant?.name && (
                            <div>{filters?.consultant?.name}</div>
                          )}
                          {filters?.company?.length > 0 && (
                            <div>
                              {filters?.consultant?.name && (
                                <span className="mr-2 text-lg font-semibold">
                                  •
                                </span>
                              )}

                              {filters?.company
                                ?.map((co) => co?.name)
                                .join(', ')}
                            </div>
                          )}
                          {filters?.date?.length > 0 && (
                            <div>
                              {(filters?.consultant?.name ||
                                filters?.company?.length > 0) && (
                                <span className="mr-2 text-lg font-semibold">
                                  •
                                </span>
                              )}
                              {`from ${filters?.date?.[0]} to ${filters?.date?.[1]}`}
                            </div>
                          )}
                        </div>
                      )}
                  </div>
                ),
                children: (
                  <div className="w-full grid grid-cols-3 gap-4">
                    <div className="font-medium">
                      Consultant{' '}
                      <span className="text-xs opacity-60">(Optional)</span>
                    </div>
                    <div className="col-span-2">
                      <Select
                        allowClear
                        placeholder="Search Consultant"
                        // value={filters?.consultant?.name || ''}
                        suffixIcon={
                          isLoadingConsultants ? (
                            <Spin size="small" />
                          ) : (
                            <UserOutlined />
                          )
                        }
                        className="w-full"
                        notFoundContent={
                          isLoadingConsultants ? <Spin size="small" /> : null
                        }
                        options={consultantOptions.map((option) => ({
                          value: option.id,
                          label: option.name,
                        }))}
                        showSearch
                        onPopupScroll={handleConsultantScroll}
                        onSearch={(searchText) => {
                          handleConsultantSearch(searchText);
                        }}
                        filterOption={(input, option) =>
                          option?.label
                            ?.toLowerCase()
                            .indexOf(input?.toLowerCase()) >= 0
                        }
                        onClear={() => {
                          setFilters({
                            ...filters,
                            consultant: null,
                          });
                        }}
                        onSelect={(value) => {
                          const consultant = consultantOptions.find(
                            (co) => co.id == value
                          );
                          console.log('Selected Consultant:', consultant);
                          setFilters({
                            ...filters,
                            consultant,
                          });
                        }}
                      />
                    </div>
                    <div className="flex items-center font-medium gap-1">
                      Company{' '}
                      <span className="text-xs opacity-60">(Optional)</span>
                    </div>
                    <div className="col-span-2 flex items-center custom_input_sequence">
                      <Select
                        allowClear
                        onClear={() => {
                          setFilters({
                            ...filters,
                            company: null,
                          });
                        }}
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-cyan' })
                        }
                        optionRender={(item) => {
                          const option = arrayUniqueByKey(
                            [...companyOptions, ...filters?.company],
                            'id'
                          )?.find((co) => item?.data?.id == co?.id);

                          return (
                            option && (
                              <>
                                <div className="grid">
                                  <div className="flex justify-between">
                                    <span className="text-base font-base text-cyan-600 font-medium">
                                      {option.id} - {option.name}
                                    </span>
                                  </div>
                                  <div className="contact-details">
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                                        <PhoneOutlined className="font-semibold" />{' '}
                                        {option.phone ? option.phone : '-'}
                                      </span>
                                      <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                                        <InfoCircleOutlined className="font-semibold" />{' '}
                                        {option.status ? option.status : '-'}
                                      </span>
                                      <span className="text-gray-500 text-xs min-w-[8rem] font-bold">
                                        <EnvironmentOutlined className="font-bold" />
                                        {option.address &&
                                        option.address.city &&
                                        option.address.state
                                          ? `${option.address.city}, ${option.address.state}`
                                          : option.address &&
                                              option.address.city
                                            ? option.address.city
                                            : option.address &&
                                                option.address.state
                                              ? option.address.state
                                              : '-'}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </>
                            )
                          );
                        }}
                        mode="multiple"
                        suffixIcon={
                          isLoadingCompany ? (
                            <Spin size="small" spinning />
                          ) : (
                            <BankOutlined />
                          )
                        }
                        className="w-full min-h-[50px]"
                        showSearch
                        loading={isLoadingCompany}
                        onPopupScroll={(e) =>
                          handleCompanyScroll(e, 'ClientCorporation')
                        }
                        optionFilterProp="label"
                        options={arrayUniqueByKey(
                          [...companyOptions, ...filters?.company],
                          'id'
                        )?.map((option) => ({
                          id: option.id,
                          value: option.id,
                          label: option.name,
                        }))}
                        onChange={(value, option) => {
                          const selectedCompanies = [
                            ...companyOptions,
                            ...filters?.company,
                          ]?.filter((item) =>
                            option?.map((i) => i?.id).includes(item?.id)
                          );
                          const newSelectedCompanies = arrayUniqueByKey(
                            [...selectedCompanies],
                            'id'
                          );
                          setFilters({
                            ...filters,
                            company: newSelectedCompanies,
                          });
                        }}
                        onSearch={(value) => {
                          if (timeoutRef.current) {
                            clearTimeout(timeoutRef.current);
                          }
                          timeoutRef.current = setTimeout(() => {
                            if (value) companySetStart(0);
                            handleCompanySearch(value);
                          }, 500);
                        }}
                        placeholder="Search Company"
                        notFoundContent={
                          isLoadingCompany && (
                            <div className="w-full flex justify-center py-4">
                              <Spin size="default" />
                            </div>
                          )
                        }
                      />
                    </div>
                    <div className="font-medium">
                      Date Added{' '}
                      <span className="text-xs opacity-60">(Optional)</span>
                    </div>
                    <div className="col-span-2">
                      <DatePicker.RangePicker
                        format={'DD/MM/YYYY'}
                        onChange={(values) => {
                          const dates = values?.map((i) =>
                            dayjs(i).format('DD-MM-YYYY')
                          );
                          setFilters({
                            ...filters,
                            date: dates,
                          });
                        }}
                        className="w-full"
                      />
                    </div>
                    <div className="col-span-3 flex items-center justify-end">
                      <Button
                        type="primary"
                        className="w-fit"
                        onClick={() => {
                          setVacancies([]);
                          handleGetAllVacancies();
                          setSelectedVacancies([]);
                          nextStep();
                        }}
                      >
                        Save & Next
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          />
          <RestyledCollapse
            onChange={(keys) => {
              if (keys?.length > 0) {
                setCurrentStep(1);
              }
            }}
            activeKey={currentStep === 1 ? ['select-vacancies'] : []}
            expandIconPosition="end"
            items={[
              {
                key: 'select-vacancies',
                label: (
                  <div className="w-full flex items-center justify-between">
                    <div className="flex items-center gap-2 font-semibold text-cyan-600">
                      {currentStep > 1 ? (
                        <CheckCircleOutlined className="text-lg font-bold text-green-600" />
                      ) : (
                        <CloseCircleOutlined className="text-lg font-bold text-gray-400" />
                      )}{' '}
                      Select Vacancies
                    </div>
                    {selectedVacancies.length > 0 && currentStep > 0 && (
                      <div className="font-medium text-cyan-600">{`${selectedVacancies.length} selected`}</div>
                    )}
                  </div>
                ),
                children: (
                  <div className="w-full search-table-new-design-container ">
                    <div className="flex items-center justify-end py-2">
                      <Checkbox
                        checked={isHideSyncedLeads}
                        onClick={toggleHideSyncedLeads}
                        title="Hide synced vacancies"
                        className="font-medium"
                      >
                        Hide synced vacancies
                      </Checkbox>
                    </div>
                    <div className="pr-2 max-h-96 overflow-y-auto">
                      <Table
                        loading={loadingVacancies}
                        rowSelection={rowVacanciesSelection}
                        pagination={false}
                        rowKey={(record) => record?.id}
                        columns={[
                          {
                            title: 'ID',
                            dataIndex: 'id',
                            key: 'id',
                          },
                          {
                            title: 'Job Title',
                            dataIndex: 'title',
                            key: 'title',
                            render: (title, record) => (
                              <div className="flex items-center gap-2">
                                <div
                                  className="font-medium text-cyan-600 line-clamp-1 max-w-3/4"
                                  title={title}
                                >
                                  {title}
                                </div>
                                {record?.isExisting && (
                                  <Tag
                                    className="text-xs font-medium"
                                    color="red"
                                  >
                                    Synced
                                  </Tag>
                                )}
                              </div>
                            ),
                          },
                          {
                            title: 'Client',
                            dataIndex: 'clientCorporation',
                            key: 'clientCorporation',
                            render: (clientCorporation, record) => (
                              <div
                                className="font-medium text-cyan-600 line-clamp-1"
                                title={clientCorporation?.name}
                              >
                                {clientCorporation?.name}
                              </div>
                            ),
                          },
                          // {
                          //   title: 'Employment Type',
                          //   dataIndex: 'employmentType',
                          //   key: 'employmentType',
                          // },
                          {
                            title: 'Open/Closed',
                            dataIndex: 'isOpen',
                            key: 'isOpen',
                            render: (isOpen, record) => (
                              <div className="font-medium">
                                {isOpen ? 'Open' : 'Closed'}
                              </div>
                            ),
                          },
                          {
                            title: 'Consultant',
                            dataIndex: 'owner',
                            key: 'owner',
                            render: (owner, record) => (
                              <div
                                className="font-medium text-cyan-600 line-clamp-1"
                                title={`${owner?.firstName || ''} ${owner?.lastName || ''}`}
                              >
                                {owner?.firstName || ''} {owner?.lastName || ''}
                              </div>
                            ),
                          },
                          // {
                          //   title: 'Source',
                          //   dataIndex: 'source',
                          //   key: 'source',
                          // },
                          {
                            title: 'Date Added',
                            dataIndex: 'dateAdded',
                            key: 'dateAdded',
                            render: (dateAdded, record) => (
                              <div className="font-medium">
                                {dayjs(dateAdded).format('DD/MM/YYYY')}
                              </div>
                            ),
                          },
                          {
                            title: 'Status',
                            dataIndex: 'status',
                            key: 'status',
                            render: (status, record) => (
                              <div className="line-clamp-1" title={status}>
                                {status}
                              </div>
                            ),
                          },
                        ]}
                        dataSource={filteredVacancies}
                        onRow={(record, rowIndex) => {
                          return {
                            onClick: (e) => {
                              if (record?.isExisting) return;
                              if (selectedVacancies.includes(record.id)) {
                                setSelectedVacancies(
                                  selectedVacancies.filter(
                                    (id) => id !== record.id
                                  )
                                );
                              } else {
                                setSelectedVacancies([
                                  ...selectedVacancies,
                                  record.id,
                                ]);
                              }
                            },
                            style: { cursor: 'pointer' },
                          };
                        }}
                      />
                    </div>
                    <div className="col-span-3 flex items-center justify-end pt-2">
                      <Button
                        disabled={loadingVacancies}
                        type="primary"
                        className="w-fit"
                        onClick={() => {
                          nextStep();
                        }}
                      >
                        Save & Next
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          />
          <RestyledCollapse
            onChange={(keys) => {
              if (keys?.length > 0) {
                setCurrentStep(2);
              }
            }}
            activeKey={currentStep === 2 ? ['select-lead-sheet'] : []}
            expandIconPosition="end"
            items={[
              {
                key: 'select-lead-sheet',
                label: (
                  <div className="w-full flex items-center justify-between">
                    <div className="flex items-center gap-2 font-semibold text-cyan-600">
                      {currentStep > 2 ? (
                        <CheckCircleOutlined className="text-lg font-bold text-green-600" />
                      ) : (
                        <CloseCircleOutlined className="text-lg font-bold text-gray-400" />
                      )}{' '}
                      Select Lead Sheet
                    </div>
                    {selectedLeadSheet.length > 0 && currentStep > 1 && (
                      <div className="font-medium text-cyan-600">{`${selectedLeadSheet.length} selected`}</div>
                    )}
                  </div>
                ),
                children: (
                  <div className="w-full search-table-new-design-container ">
                    <Table
                      loading={loadingLeadSheet}
                      rowSelection={rowLeadSheetSelection}
                      pagination={false}
                      rowKey={(record) => record?.id}
                      columns={[
                        {
                          title: 'Name',
                          dataIndex: 'name',
                          key: 'name',
                          render: (name, record) => (
                            <div className="font-medium text-cyan-600">
                              {name}
                            </div>
                          ),
                        },
                        {
                          title: 'Lead Status Type',
                          dataIndex: 'leadStatusType',
                          key: 'leadStatusType',
                          render: (leadStatusType, record) => {
                            return (
                              <Tag
                                color="cyan"
                                icon={LEAD_STATUS_TYPE_ICON[leadStatusType]}
                                className="flex items-center"
                              >
                                {leadStatusType}
                              </Tag>
                            );
                          },
                        },
                      ]}
                      dataSource={leadSheets}
                      onRow={(record, rowIndex) => {
                        return {
                          onClick: (e) => {
                            if (selectedLeadSheet.includes(record.id)) {
                              setselectedLeadSheet(
                                selectedLeadSheet.filter(
                                  (id) => id !== record.id
                                )
                              );
                            } else {
                              setselectedLeadSheet([
                                ...selectedLeadSheet,
                                record.id,
                              ]);
                            }
                          },
                          style: { cursor: 'pointer' },
                        };
                      }}
                    />
                    <div className="col-span-3 flex items-center justify-end pt-2">
                      <Button
                        disabled={loadingLeadSheet}
                        type="primary"
                        className="w-fit"
                        onClick={() => {
                          nextStep();
                        }}
                      >
                        Save & Next
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          />
          <RestyledCollapse
            onChange={(keys) => {
              if (keys?.length > 0) {
                setCurrentStep(3);
              }
            }}
            activeKey={currentStep === 3 ? ['submit-n-finish'] : []}
            expandIconPosition="end"
            items={[
              {
                key: 'submit-n-finish',
                label: (
                  <div className="flex items-center gap-2 font-semibold text-cyan-600">
                    {currentStep > 3 ? (
                      <CheckCircleOutlined className="text-lg font-bold text-green-600" />
                    ) : (
                      <CloseCircleOutlined className="text-lg font-bold text-gray-400" />
                    )}{' '}
                    Submit & Finish
                  </div>
                ),
                children: (
                  <div className="flex flex-col items-center justify-center gap-4">
                    <div className="flex items-center gap-1">
                      Importing{' '}
                      <span className="px-2 rounded-md bg-cyan-600 text-white font-semibold">
                        {selectedVacancies.length}
                      </span>{' '}
                      {selectedVacancies.length > 1 ? `vacancies` : 'vacancy'}{' '}
                      to{' '}
                      <span className="px-2 rounded-md bg-cyan-600 text-white font-semibold">
                        {selectedLeadSheet.length}
                      </span>{' '}
                      {selectedLeadSheet.length > 1
                        ? `lead sheets`
                        : 'lead sheet'}
                    </div>
                    <Button
                      disabled={
                        !(
                          selectedLeadSheet?.length > 0 &&
                          selectedVacancies.length > 0
                        )
                      }
                      loading={importLoading}
                      type="primary"
                      className="w-fit"
                      onClick={handleBulkImportLeads}
                      icon={<SyncOutlined />}
                    >
                      Confirm & Import
                    </Button>
                  </div>
                ),
              },
            ]}
          />
        </div>
        <div className="col-span-3 border-l px-6 md:px-16 2xl:px-16 py-3 h-full flex items-center justify-center">
          <Steps
            rootClassName="font-medium"
            direction="vertical"
            current={currentStep}
            onChange={(step) => setCurrentStep(step)}
            items={[
              {
                title: 'Filtering',
                description:
                  'Narrow down options by applying relevant filters to find.',
              },
              {
                title: 'Select Vacancies',
                description:
                  'Choose the vacancies that match your requirements from the filtered list.',
              },
              {
                title: 'Select Lead Sheet',
                description:
                  'Pick the lead sheet to organize and track selected vacancies effectively.',
              },
              {
                title: 'Submit & Finish',
                description:
                  'Review your selection and complete the import process.',
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default ImportLeadsPage;
