import React, { useState } from 'react';
import { Button, Dropdown } from 'antd';
import {
  StarOutlined,
  HeartOutlined,
  TrophyOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import JobSubmissionModal from '../../components/BullHorn/BullHornJobSubmissionModal';
import BullHornJobSubmissionModalPage from '../../components/BullHorn/BullHornJobSubmissionModalPage';
import { getUserViewAsLicenseType } from '../../helpers/getUserViewAs';
import { licenseType } from '../../constants/common.constant';
import StandardJobSubmissionModalPage from '../SyncStandardLeadPage/StandardJobSubmissionModalPage';
function ManualLeads() {
  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const [isModalOpen, setModalOpen] = useState(false);
  const [key, setKey] = useState('1');
  const handleSorterClick = (e) => {
    const value = e.currentTarget.getAttribute('value');
    // console.log(value);
    if (!value) return;
    setModalOpen(true);
    if (value === '1') {
      setKey(1);
    } else if (value === '2') {
      setKey(2);
    } else if (value === '3') {
      setKey(3);
    }
  };
  return isStandardUser ? (
    <div>
      <div className="mt-2 rounded-md bg-white flex items-center justify-center min-h-[50vh]">
        <StandardJobSubmissionModalPage
          // actionKey={key} // Lead form
          job={null}
          searchData={[]}
          searchIdProp={''}
          fromManualCreate={true}
        />
      </div>
    </div>
  ) : (
    <div>
      {!isModalOpen && (
        <div className="flex justify-center items-center py-40">
          <Button
            className="text-black rounded-md ms-2 px-3 py-1 bg-white flex items-center"
            icon={<HeartOutlined />}
            value="1"
            key="1"
            onClick={handleSorterClick}
          >
            <span className="text-sm font-medium">Vacancy</span>
          </Button>
          <Button
            className="text-black rounded-md ms-2 px-3 py-1 bg-white flex items-center"
            icon={<TrophyOutlined />}
            value="2"
            key="2"
            onClick={handleSorterClick}
          >
            <span className="text-sm font-medium">Lead</span>
          </Button>
          <Button
            className="text-black rounded-md ms-2 px-3 py-1 bg-white flex items-center"
            icon={<StarOutlined />}
            value="3"
            key="3"
            onClick={handleSorterClick}
          >
            <span className="text-sm font-medium">Opportunity</span>
          </Button>
        </div>
      )}

      {isModalOpen && (
        <div>
          <div className="w-full flex justify-start">
            <Button
              className="flex items-center bg-white"
              onClick={() => {
                setModalOpen(false);
              }}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
          </div>
          <div className="mt-2 rounded-md bg-white flex items-center justify-center min-h-[50vh]">
            <BullHornJobSubmissionModalPage
              actionKey={key} // Lead form
              job={null}
              searchData={[]}
              searchIdProp={''}
              setIsModalVisible={(value) => {
                setModalOpen(false);
              }}
              fromManualCreate={true}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default ManualLeads;
