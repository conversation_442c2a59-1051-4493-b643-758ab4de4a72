import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { theme, Spin } from 'antd';
import JobList from '../../components/JobList';
import EditFilter from '../../components/EditFilter';
import { ReactComponent as Filter } from '../../assets/svg/Filter.svg';

function SyncDetail() {
  const [savedSearchData, setSavedSearchData] = useState([]);
  const { id } = useParams();
  const [isSearchLoading, setSearchLoading] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [viewData, setViewData] = useState([]);

  useEffect(() => {
    if (localStorage.getItem('savedSync') == null)
      localStorage.setItem('savedSync', JSON.stringify([]));
    setSavedSearchData(JSON.parse(localStorage.getItem('savedSync')));
  }, []);

  useEffect(() => {
    if (id) {
      if (localStorage.getItem('savedSync') == null)
        localStorage.setItem('savedSync', JSON.stringify([]));
      setViewData(
        JSON.parse(localStorage.getItem('savedSync')).filter(
          (item) => item.id == id
        )
      );
    }
  }, [id]);

  useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200 ? setShowFilter(false) : setShowFilter(true)
    );
  }, []);

  const { token } = theme.useToken();

  const containerStyle = {
    position: 'relative',
    height: '84vh',
    padding: 20,
    overflow: 'overlay',
    textAlign: 'left',
    background: token.colorFillAlter,
    border: `1px solid ${token.colorBorderSecondary}`,
  };

  if (isSearchLoading || viewData.length == 0)
    return (
      <div className="text-left mt-40">
        <Spin tip="Loading" size="large">
          <div className="content" />
        </Spin>
      </div>
    );
  return (
    <>
      <div style={containerStyle}>
        {
          <div className="text-right mt-10 xl:mx-[40px] mx-1">
            <div className="flex justify-end items-center">
              <div className="mr-11">{`Total Counts: ${viewData[0].totalCount}`}</div>
              <div
                className="sm:hidden flex align-middle"
                onClick={() => setShowFilter(!showFilter)}
              >
                <Filter className="w-5 text-gray-400 mr-3 pb-2" />
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div
                className={`lg:col-span-1 sm:col-span-1 col-span-4 sm:block  ${
                  showFilter ? 'block h-full w-full' : 'hidden'
                }`}
              >
                <EditFilter
                  id={id}
                  editSearch={viewData}
                  savedSearchData={savedSearchData}
                  setSavedSearchData={setSavedSearchData}
                />
              </div>
              <div className="lg:col-span-3 sm:col-span-3 col-span-4">
                <div className="lg:col-span-3">
                  <JobList
                    searchData={viewData[0]}
                    isSearchLoading={isSearchLoading}
                    setSavedSearchData={setSavedSearchData}
                    savedSearchData={savedSearchData}
                    setSearchLoading={setSearchLoading}
                    method="sync"
                    setViewData={setViewData}
                  />
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    </>
  );
}

export default SyncDetail;
