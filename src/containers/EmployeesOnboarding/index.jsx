import {
  CloseCircleOutlined,
  CloseOutlined,
  NodeExpandOutlined,
  RightOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useState } from 'react';
import EmployeesOnboardingTable from './EmployeesOnboardingTable';

const stepConstant = {
  BH_CONFIGURE: 1,
  EMPLOYEES_ONBOARDING: 2,
};

const EmployeesOnboarding = ({
  licenseType,
  paymentPackage,
  skipStep,
  handleSubmit,
  isNotAllowConfigure,
  onboardingEmployees,
  employeeCap,
}) => {
  const [step, setStep] = useState(stepConstant.EMPLOYEES_ONBOARDING);

  const onSubmitEmployeesOnboarding = async (payload) => {
    handleSubmit(payload);
  };

  return (
    <div className="w-full flex flex-col gap-5 justify-between min-h-[35rem]">
      <div className="w-full flex items-center justify-center h-full">
        {isNotAllowConfigure && (
          <div className="w-full flex flex-col items-center justify-center gap-4">
            <div className="text-sm font-medium italic text-red-400 flex items-center justify-center gap-1 h-full">
              <CloseCircleOutlined className="text-red-400" />
              <span>
                Please configure your BH account to proceed with the onboarding.
              </span>
            </div>
            <Button
              type="primary"
              onClick={() => {
                handleSubmit({ dataSource: [] });
              }}
              icon={<NodeExpandOutlined />}
            >
              I will do it later
            </Button>
          </div>
        )}
        {step === stepConstant.EMPLOYEES_ONBOARDING && !isNotAllowConfigure && (
          <EmployeesOnboardingTable
            paymentPackage={paymentPackage}
            skipStep={skipStep}
            handleSubmit={onSubmitEmployeesOnboarding}
            onboardingEmployees={onboardingEmployees}
            employeeCap={employeeCap}
          />
        )}
      </div>
    </div>
  );
};

export default EmployeesOnboarding;
