import {
  Button,
  Col,
  Dropdown,
  Form,
  Input,
  InputNumber,
  notification,
  Popconfirm,
  Row,
  Select,
  Space,
  Table,
  Tooltip,
} from 'antd';
import {
  AuditOutlined,
  CalendarOutlined,
  CheckOutlined,
  CiCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  GoldOutlined,
  HourglassOutlined,
  NodeExpandOutlined,
  PlusOutlined,
  SaveOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import {
  CardToolbox,
  ContactPageheaderStyle,
  TableWrapper,
  UserTableStyleWrapper,
} from '../CRM/styled';
import { userRole } from '../../constants/common.constant';
import { totalCredits } from '../CompanyOnboarding/LicensePackageSelection';
import {
  generateRandomPassword,
  generateRandomUsername,
} from '../../helpers/util';
import clsx from 'clsx';
import styled from 'styled-components';
import { perPaymentOptions } from '../../components/Stripe';

const RestyledSelect = styled(Select)`
  .ant-select-selection-item {
    font-weight: 500;
  }
`;

const getElementByField = (field, value, onChange) => {
  const roleOptions = Object.entries(userRole).map(([_key, value]) => ({
    label: value?.toLowerCase()?.capitalize()?.replace('_', ' '),
    value,
  }));

  switch (field) {
    case 'role':
      return <RestyledSelect options={roleOptions} />;
    case 'password':
      return <Input.Password />;
    case 'credits':
      return <InputNumber min={0} />;
    case 'email':
      return <Input onChange={onChange} />;
    default:
      return <Input />;
  }
};

const EmployeesOnboardingTable = ({
  paymentPackage,
  companyCredits = null,
  handleSubmit = null,
  onboardingEmployees,
  employeeCap,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [actionLoading, setActionLoading] = useState(false);

  const [totalOriginalCredits, setTotalOriginalCredits] = useState(0);
  const [isEditCreditsCap, setEditCreditsCap] = useState(false);

  const [payPer, setPayPer] = useState(perPaymentOptions.monthly);

  const toggleEditCreditsCap = () => setEditCreditsCap(!isEditCreditsCap);

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');

  const isEditing = (record) => record?.key === editingKey;

  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };

  const EditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const onChange = (e) => {
      if (dataIndex === 'email') {
        const newEmail = e.target.value;
        form.setFieldsValue({
          ...record,
          email: newEmail,
          userName: newEmail,
        });
      }
    };

    const inputNode = getElementByField(
      dataIndex,
      record?.[dataIndex],
      onChange
    );
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={dataIndex}
            style={{
              margin: 0,
            }}
            rules={
              dataIndex === 'credits'
                ? []
                : [
                    {
                      required: true,
                      message:
                        dataIndex === 'email'
                          ? `${title} is invalid`
                          : `${title} is required`,
                      pattern:
                        dataIndex === 'email' ? /\S+@\S+\.\S+/ : undefined,
                    },
                  ]
            }
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const cancel = () => {
    handleDelete([editingKey]);
    form.resetFields();
    setEditingKey('');
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      setActionLoading(true);
      const newData = [...dataSource];

      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        let newItem = {
          ...item,
          ...row,
        };

        newData.splice(index, 1, newItem);

        setDataSource(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setDataSource(newData);
        setEditingKey('');
      }

      setActionLoading(false);
      form.resetFields();
      notification.success({
        message: 'Employee submitted successfully!',
      });
    } catch (errInfo) {
      setActionLoading(false);
      console.log('Validate Failed:', errInfo);
      const message =
        errInfo?.response?.data?.message ||
        'Please make sure all fields are filled correctly!';
      notification.error({
        message,
      });
    }
  };

  const cancelAdd = () => {
    const newDataSource = [...dataSource];
    newDataSource.shift();
    form.resetFields();
    setDataSource([...newDataSource]);
    setEditingKey('');
  };

  const handleAdd = () => {
    const id = uuid();
    const newData = {
      id,
      key: id,
      email: '<EMAIL>',
      role: userRole.BASIC_USER,
      userName: generateRandomUsername(),
      password: generateRandomPassword(),
      credits: 0,
      createdAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      updatedAt: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
    };
    setDataSource([newData, ...dataSource]);
    edit(newData);
  };

  const handleDelete = async (ids) => {
    try {
      const newDataSource = dataSource.filter(
        (item) => !ids?.includes(item?.id)
      );
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        message: 'Something went wrong!',
      });
    }
  };

  const columns = [
    {
      title: 'Invitation Email',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      editable: true,
      render: (email) => {
        return <div>{email}</div>;
      },
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      editable: true,
      width: '15%',
      render: (role, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1 text-cyan-600">
            {role?.toLowerCase()?.capitalize()?.replace('_', ' ') || '-'}
          </div>
        );
      },
    },
    {
      title: 'Username',
      dataIndex: 'userName',
      key: 'userName',
      width: '20%',
      editable: true,
      render: (userName, record) => {
        return (
          <div className="line-clamp-1" title={userName}>
            {userName}
          </div>
        );
      },
    },
    {
      title: 'Password',
      dataIndex: 'password',
      key: 'password',
      width: '25%',
      editable: true,
      render: (password, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {password || '-'}
          </div>
        );
      },
    },
    {
      title: 'Credits',
      dataIndex: 'credits',
      key: 'credits',
      // editable: true,
      width: '5%',
      render: (credits, record) => {
        return (
          <div className="font-semibold w-full flex justify-start items-center gap-1">
            {credits || '0'}
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: '5%',
      align: 'center',
      render: (_action, record) => {
        const editable = isEditing(record);
        return (
          <div className="flex items-center justify-center w-full font-semibold ">
            {editable ? (
              <div className="flex items-center gap-2">
                <Button
                  loading={actionLoading}
                  type="dashed"
                  // className="flex gap-1 items-center"
                  onClick={() => save(record?.key)}
                  icon={<CheckOutlined />}
                ></Button>
                {/* <Popconfirm title="Sure to cancel?" onConfirm={cancelAdd}>
                  <Button
                    type="text"
                    danger
                    // className="flex gap-1 items-center"
                    icon={<StopOutlined />}
                  ></Button>
                </Popconfirm> */}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  type="primary"
                  disabled={editingKey}
                  onClick={() => edit(record)}
                  icon={<EditOutlined />}
                />
                {/* <Popconfirm
                  title={<div>Confirmation</div>}
                  description={
                    <div>
                      Are you sure to delete <strong>{record?.name}</strong>{' '}
                      Employee?
                    </div>
                  }
                  onCancel={cancel}
                  onConfirm={() => handleDelete([record?.id])}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    danger
                    disabled={editingKey}
                    icon={<DeleteOutlined />}
                  />
                </Popconfirm> */}
              </div>
            )}
          </div>
        );
      },
    },
  ];

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const onSelectChange = (newSelectedRowKeys) => {
    const isExceedingCap = newSelectedRowKeys.length > employeeCap;
    if (isExceedingCap) {
      notification.error({
        message: `You can only select up to ${employeeCap} employees.`,
      });
      return;
    }
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const costCredits =
    dataSource?.reduce((acc, item) => {
      return acc + item?.credits;
    }, 0) || 0;

  const onFinish = async () => {
    // if (costCredits > totalOriginalCredits) {
    //   notification.error({
    //     message: 'You have exceeded the available credits!',
    //   });
    //   return;
    // }
    if (editingKey) {
      notification.error({
        message: 'Please save or cancel the current edit before submitting!',
      });
      return;
    }
    if (!dataSource?.length) {
      notification.error({
        message: 'Please add at least one employee!',
      });
      return;
    }
    const selectedEmployees = dataSource?.filter((item) =>
      selectedRowKeys?.includes(item?.id)
    );
    handleSubmit({
      dataSource: selectedEmployees,
      totalOriginalCredits,
      payPer,
    });
  };

  const handleEqualize = () => {
    const totalAvailableCredits = totalOriginalCredits;
    const creditsPerEmployee = Math.floor(
      totalAvailableCredits / dataSource?.length
    );
    const newDataSource = dataSource?.map((item) => ({
      ...item,
      credits: creditsPerEmployee,
    }));
    setDataSource([...newDataSource]);
    notification.success({
      message: 'Credits equalized successfully!',
    });
  };

  useEffect(() => {
    if (onboardingEmployees?.length > 0) {
      let selectedKeys = [];
      const newDataSource = onboardingEmployees?.map((item) => ({
        ...item,
        key: item?.id,
      }));
      selectedKeys = newDataSource?.map((item) => item?.id);

      const extraEmployees = +employeeCap - newDataSource?.length;
      if (extraEmployees < 0) {
        selectedKeys = selectedKeys.slice(0, +employeeCap);
      }

      setSelectedRowKeys([...selectedKeys]);
      setDataSource([...newDataSource]);
    }
  }, [onboardingEmployees]);

  useEffect(() => {
    const newCapCredits = companyCredits || totalCredits[paymentPackage];
    setTotalOriginalCredits(newCapCredits);
  }, [paymentPackage, companyCredits]);

  return (
    <div className="flex flex-col gap-5 w-full">
      <CardToolbox>
        <ContactPageheaderStyle>
          <div className="w-full h-full flex justify-between items-center">
            <div className="flex items-center gap-4 justify-center">
              {/* <Button
                disabled={editingKey}
                onClick={handleAdd}
                type="primary"
                icon={<PlusOutlined />}
              >
                Add Employee
              </Button> */}
              <div>
                {selectedRowKeys?.length > 0 && (
                  <div>
                    {/* <Dropdown
                      className="animated fadeInDownBig"
                      placement="bottom"
                      arrow
                      menu={{
                        items: [
                          {
                            key: 'delete-selected',
                            label: (
                              <a
                                className="flex gap-2 items-center"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleDelete(selectedRowKeys);
                                  setSelectedRowKeys([]);
                                }}
                              >
                                <span>Delete</span>
                              </a>
                            ),
                            icon: <DeleteOutlined />,
                          },
                        ],
                      }}
                    >
                      <Space> */}
                        <Button
                          type="primary"
                          className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                        >
                          <p className="Montserrat">
                            {`${selectedRowKeys?.length} Selected`}
                          </p>
                          {/* <DownOutlined /> */}
                        </Button>
                      {/* </Space>
                    </Dropdown> */}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="font-medium text-gray-900 text-sm">
                Payment per:
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  className={`shadow-md border relative flex gap-2 h-auto items-center justify-center ${payPer === perPaymentOptions.monthly ? 'border-cyan-700 bg-cyan-100' : ''}`}
                  onClick={() => {
                    setPayPer(perPaymentOptions.monthly);
                  }}
                >
                  <HourglassOutlined className=" text-cyan-600" />
                  <span className="font-semibold">Monthly</span>
                </Button>
                <Button
                  className={`shadow-md border relative flex gap-2 h-auto items-center justify-center ${payPer === perPaymentOptions.annually ? 'border-cyan-700 bg-cyan-100' : ''}`}
                  onClick={() => {
                    setPayPer(perPaymentOptions.annually);
                  }}
                >
                  <CalendarOutlined className=" text-cyan-600" />
                  <span className="font-semibold">Yearly</span>
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* <Button
                disabled={editingKey || dataSource?.length === 0}
                onClick={handleEqualize}
                type="primary"
                icon={<CiCircleOutlined />}
              >
                Equalize
              </Button> */}

              <div className="text-sm flex items-center gap-2">
                Available Credits:{' '}
                {!isEditCreditsCap && (
                  <span
                    className={clsx(
                      'font-semibold flex items-center gap-1 text-cyan-600'
                    )}
                  >
                    <CiCircleOutlined />
                    {`${costCredits}`}
                  </span>
                )}
                {isEditCreditsCap && (
                  <InputNumber
                    value={totalOriginalCredits}
                    onChange={(value) => setTotalOriginalCredits(value)}
                  />
                )}
                {/* <Tooltip title="Edit credits capacity">
                  <Button
                    onClick={toggleEditCreditsCap}
                    type="text"
                    icon={
                      isEditCreditsCap ? <SaveOutlined /> : <EditOutlined />
                    }
                  />
                </Tooltip> */}
              </div>
            </div>
          </div>
        </ContactPageheaderStyle>
      </CardToolbox>
      <Row gutter={15}>
        <Col xs={24}>
          <UserTableStyleWrapper>
            <div className="contact-table max-h-[30rem] overflow-y-auto">
              <TableWrapper className="table-responsive text-gray-800">
                <Form form={form} component={false}>
                  <Table
                    className="customized-style-pagination w-full"
                    components={{
                      body: {
                        cell: EditableCell,
                      },
                    }}
                    rowClassName="editable-row"
                    dataSource={dataSource}
                    columns={mergedColumns}
                    rowSelection={editingKey ? null : rowSelection}
                    rowKey={(record) => record.id}
                    pagination={false}
                  />
                </Form>
              </TableWrapper>
            </div>
          </UserTableStyleWrapper>
        </Col>
      </Row>
      {handleSubmit && (
        <div className="w-full flex justify-between">
          <Button
            type="dashed"
            onClick={() => {
              handleSubmit({ dataSource: [] });
            }}
            icon={<NodeExpandOutlined />}
          >
            Do it later
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={onFinish}>
            Save & Next
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmployeesOnboardingTable;
