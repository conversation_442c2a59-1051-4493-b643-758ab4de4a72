import React from 'react';
import zileo<PERSON>ogo from '../../assets/img/welcome/logo.png'; // Adjust the path as necessary
import { Button, Image } from 'antd';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined } from '@ant-design/icons';

const NotFoundPage = () => {
  const navigate = useNavigate();

  const handleReturnHome = () => {
    navigate('/');
  };

  return (
    <div className="flex flex-col justify-center items-center h-screen bg-[#f0f0f0] gap-2 ">
      <Image width={500} preview={false} src={zileoLogo} alt="Zileo Logo" />
      <h1 className="text-4xl mb-1 font-bold">Not Found</h1>
      <div className="text-base font-medium opacity-70">
        Whoops, We can't seem to find the resource you're looking for.
      </div>
      <Button icon={<HomeOutlined />} onClick={handleReturnHome} type="primary">
        Return to Home
      </Button>
    </div>
  );
};

export default NotFoundPage;
