import React, { useState, useEffect } from 'react';
import Table from '../../components/AgencyTable';

function Sync() {
  const [agencyData, setAgencyData] = useState([]);
  useEffect(() => {
    if (localStorage.getItem('savedAgency') == null)
      localStorage.setItem('savedAgency', JSON.stringify([]));
    setAgencyData(JSON.parse(localStorage.getItem('savedAgency')));
  }, []);
  return (
    <div className="overflow-hidden">
      <Table agencyData={agencyData} setAgencyData={setAgencyData} />
    </div>
  );
}

export default Sync;
