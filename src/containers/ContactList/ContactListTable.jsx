import {
  CheckOutlined,
  DeleteOutlined,
  DeleteRowOutlined,
  DownOutlined,
  EditOutlined,
  LeftSquareOutlined,
  LinkedinOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
  StopOutlined,
  UserOutlined,
  HomeOutlined,
  MoreOutlined,
  SolutionOutlined,
  AuditOutlined,
  SyncOutlined,
  <PERSON>boltOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  notification,
  Popconfirm,
  Table,
  Form,
  Input,
  Typography,
  InputNumber,
  Pagination,
  Dropdown,
  Space,
  Select,
  Modal,
  Tag,
  Tabs,
  message,
  Tooltip,
  Badge,
  Switch,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { arrayUniqueByKey, formatPhoneNumber } from '../../utils/common';
import {
  bulkDeleteContactsInList,
  deleteBulkList,
  deleteContactFromList,
  getDetailContactList,
  toggleUpdateSubscribed,
} from '../../services/contactList';
import { v4 as uuid } from 'uuid';
import _, { isEmpty } from 'lodash';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { useForm } from 'react-hook-form';
import {
  defaultEnrichFields,
  ENRICH_DATA_LIST_TYPE,
  ENRICH_DATA_STATUS,
  TIME_ENRICH_EACH_FIELD,
  VALIDATE_STATUS_COLOR,
  VALIDATE_STATUS_ICON,
} from '../HotList/HotListTable';
import BulkEnrichData from '../HotList/BulkEnrich';
import EventSourceRender from '../../components/EventSource';
import Loading from '../HotList/Loading';
import {
  enrichAllContactList,
  handleBulkEnrichContactData,
  handleGetEnrichData,
} from '../../services/employee';
import handleRenderTime from '../../function/handleRenderTime';
import Search from 'antd/es/input/Search';

export const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const inputNode =
    dataIndex === 'status' ? (
      <Select
        className="min-w-[40px]"
        options={[
          {
            value: 'Live Lead',
            label: 'Live Lead',
          },
          {
            value: 'Prospect',
            label: 'Prospect',
          },
          {
            value: 'Active',
            label: 'Active',
          },
          {
            value: 'Passive',
            label: 'Passive',
          },
          {
            value: 'DNC',
            label: 'DNC',
          },
          {
            value: 'Archive',
            label: 'Archive',
          },
          {
            value: 'Private',
            label: 'Private',
          },
        ]}
      />
    ) : (
      <Input />
    );
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{
            margin: 0,
          }}
          rules={[
            {
              required:
                title === 'Contact Name' || title === 'Email' ? true : false,
              message: `Please Input ${title}!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const ContactListTable = ({
  isFetching,
  dataSource,
  contactListId,
  getAllContacts,
  setDataSource,
  handleAddNewContact,
  handleUpdateContact,
  contactLoading,
  pagination,
  handlePagination,
  fetchImageUrl,
  handleValidateEmails,
  listEmailChecked,
  setLoading,
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [dataContact, setDataContact] = useState([]);
  const { handleSubmit, control, getValues, setValue, watch } = useForm();
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);

  // Bulk Enrich
  const [openBulkEnrichModal, setOpenBulkEnrichModal] = useState(false);

  const [createWithSubscribed, setCreateWithSubscribed] = useState(false);

  const [estimationDuration, setEstimationDuration] = useState('0');
  const [updatingData, setUpdatingData] = useState({});
  const [enrichData, setEnrichData] = useState([]);
  const [enrichingData, setEnrichingData] = useState([]); // contacts list is enriching
  const [enrichLoading, setEnrichLoading] = useState(false);
  const [loadingId, setLoadingId] = useState();

  const showBulkEnrichModal = () => setOpenBulkEnrichModal(true);
  const closeBulkEnrichModal = () => setOpenBulkEnrichModal(false);

  const [tab, setTab] = useState('tab_all');
  const [dataListInValidEmail, setDataListInvalidEmail] = useState([]);
  const [loadingGetInValid, setLoadingInValid] = useState(false);

  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');

  const handleUpdateContactToggle = async (email, unsubscribed, id) => {
    try {
      setLoadingId(id);
      if (id) {
        const { data } = await toggleUpdateSubscribed({
          email: email,
          unsubscribed: unsubscribed,
        });
        const updatedArr = dataSource?.map((item) =>
          item.id === id ? { ...item, isUnsubscribeEmail: unsubscribed } : item
        );
        setDataSource(updatedArr);
      } else {
        setCreateWithSubscribed(unsubscribed);
      }
      setLoadingId();
    } catch (e) {
      setLoadingId();
      console.error('Error update contact', e);
    }
  };

  const handleGetListData = async () => {
    try {
      setDeleteLoading(true);
      const { data } = await getDetailContactList(
        contactListId,
        1,
        '',
        selectedRowKeys
      );
      const dataContactSelect = data?.result?.items?.map((item) => ({
        value: item?.id,
        label: item?.email,
        email: item?.email,
        name: item?.name,
        linkedinProfileUrl: item?.linkedInProfileUrl,
        id: item?.id,
      }));
      setDataContact(dataContactSelect);
      setValue('participants.sendTo', dataContactSelect);
      setValue('participants.sendFromContact', dataContactSelect);
      setValue(
        'sendMail.listEmail',
        data?.result?.items?.map((item) => item?.email)
      );
      setValue(
        'sendMail.listEmailSend',
        data?.result?.items?.map((item) => item?.email)
      );
      setValue('sendMail.mailStepParentMailTo', dataContactSelect);
      setValue('sendMail.listEmailSendChoose', dataContactSelect);
      setValue('sendMail.fromContactListTab', dataContactSelect);
      setOpenSendEmail(true);
      setDeleteLoading(false);
    } catch (error) {
      setDeleteLoading(false);
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const isEditing = (record) => record?.key === editingKey;

  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.key);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...dataSource];

      const index = newData.findIndex((item) => key === item.key);

      if (index > -1) {
        const item = newData[index];
        let newItem = {
          ...item,
          ...row,
          unsubscribed: createWithSubscribed,
        };
        if (item?.id) {
          const res = await handleUpdateContact(newItem, key);
          newItem = {
            ...newItem,
            linkedinAvtUrl: await fetchImageUrl(res?.linkedInProfileImageUrl),
            linkedInProfileUrl: res?.linkedInProfileUrl,
            linkedInFirstName: res?.linkedInFirstName,
            linkedInLastName: res?.linkedInLastName,
          };
        } else {
          const res = await handleAddNewContact(newItem);
        }
        newData.splice(index, 1, newItem);
        setDataSource(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setDataSource(newData);
        setEditingKey('');
      }
      setCreateWithSubscribed(false);
      form.resetFields();
    } catch (errInfo) {
      setCreateWithSubscribed(false);
    }
  };

  const handleDeleteContact = async (contactId) => {
    try {
      const { data } = await deleteContactFromList(contactListId, contactId);
      notification.success({ message: 'Delete Contact Success' });
      const newListInvalidEmail = [...dataListInValidEmail];
      setDataListInvalidEmail(
        newListInvalidEmail?.filter((item) => item?.id !== contactId)
      );
      getAllContacts();
    } catch (errors) {
      notification.error({ message: 'Something went wrong' });
    }
  };

  const renderOption = (option) => {
    const data = option?.data || null;
    if (!data) return <div>{option?.label}</div>;

    return (
      data?.value?.trim() &&
      (data?.value?.includes('linkedin') ? (
        <div className="flex items-start flex-col">
          <span className="flex items-center gap-1">
            <LinkedinOutlined className="text-[#0288d1]" />
            <span className="italic">{`${option?.label}`}</span>
          </span>
          <span className="text-xs italic text-gray-600">
            {handleRenderTime(data?.createdAt)}
          </span>
        </div>
      ) : (
        <div className="flex items-start flex-col">
          <span>{option?.label}</span>
          <span className="text-xs italic text-gray-600">
            {handleRenderTime(data?.createdAt)}
          </span>
        </div>
      ))
    );
  };

  const handleSelectEnrichData = async (newItem) => {
    setLoading(true);
    try {
      const payload = {
        ...newItem,
      };
      delete payload?.key;
      const res = await handleUpdateContact(newItem, newItem?.key);
      const newData = [...dataSource];
      const updatedIndex = newData?.findIndex(
        (item) => item?.id === newItem?.id
      );
      if (updatedIndex > -1) {
        newData[updatedIndex] = {
          ...newItem,
          linkedinAvtUrl: await fetchImageUrl(res?.linkedInProfileImageUrl),
          linkedInProfileUrl: res?.linkedInProfileUrl,
          linkedInFirstName: res?.linkedInFirstName,
          linkedInLastName: res?.linkedInLastName,
        };
        setDataSource([...newData]);
      }
      notification.success({
        description: 'contact updated!',
      });
      setLoading(false);
    } catch (error) {
      console.log('handleSelectEnrichData error: ', error);
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Linkedin Profile',
      dataIndex: 'linkedInProfileUrl',
      key: 'linkedInProfileUrl',
      editable: true,
      fixed: 'left',
      width: '250px',
      render: (text, record) => {
        // enrich data feature

        const linkedinOptions = arrayUniqueByKey(
          enrichData,
          'linkedin_url'
        )?.filter(
          (enrichItem) =>
            enrichItem?.record_id === record?.id &&
            enrichItem?.linkedin_url &&
            enrichItem?.linkedin_url?.trim() !==
              record?.linkedInProfileUrl?.trim()
        );
        const linkedinOptionsCount = linkedinOptions?.length || 0;

        const splittedLinkedinUrl = text?.split('/');
        const subName =
          splittedLinkedinUrl?.[splittedLinkedinUrl?.length - 1] ||
          splittedLinkedinUrl?.[splittedLinkedinUrl?.length - 2];
        return (
          <div className="flex items-center gap-2">
            {/* {record?.linkedinAvtUrl && (
              <Avatar size={50} src={record?.linkedinAvtUrl} />
            )}
            {!record?.linkedinAvtUrl && (
              <Avatar size={50} icon={<UserOutlined />} />
            )} */}
            <div className="flex flex-col">
              {/* <span className="text-base font-semibold">
                {record?.linkedInFirstName || '-'}
              </span> */}
              {linkedinOptionsCount > 0 ? (
                <>
                  <Tooltip
                    title={`Having ${linkedinOptionsCount} option(s) after enriching`}
                  >
                    <Badge count={linkedinOptionsCount}>
                      <div className="flex items-center">
                        <LinkedinOutlined className="text-[#0288d1]" />
                        <Select
                          placeholder="Missing input"
                          onSelect={(value) =>
                            handleSelectEnrichData({
                              ...record,
                              linkedInProfileUrl: value,
                            })
                          }
                          optionRender={renderOption}
                          onClick={(evt) => evt.stopPropagation()}
                          rootClassName="!pl-0"
                          className="min-w-fit"
                          popupClassName="min-w-30rem"
                          defaultValue={subName?.trim() || 'Missing input'}
                          options={[...linkedinOptions].map((item) => {
                            const splittedLinkedinUrl =
                              item?.linkedin_url?.split('/');
                            const subName =
                              splittedLinkedinUrl?.[
                                splittedLinkedinUrl?.length - 1
                              ] ||
                              splittedLinkedinUrl?.[
                                splittedLinkedinUrl?.length - 2
                              ] ||
                              '';
                            return {
                              ...item,
                              value: item?.linkedin_url,
                              label: subName,
                            };
                          })}
                        />
                      </div>
                    </Badge>
                  </Tooltip>
                </>
              ) : (
                <a
                  className="flex items-center gap-1 justify-start w-full font-medium text-sm italic text-[#0288d1]"
                  href={
                    record?.linkedInProfileUrl?.startsWith('http')
                      ? record?.linkedInProfileUrl
                      : `https://${record?.linkedInProfileUrl}`
                  }
                  target="_blank"
                >
                  <LinkedinOutlined className="text-[#0288d1]" />
                  <span
                    className="line-clamp-1 max-w-[10rem]"
                    title={record?.linkedInProfileUrl}
                  >
                    {subName}
                  </span>
                </a>
              )}
            </div>
          </div>
        );
      },
    },
    // {
    //   title: 'Contact Name',
    //   dataIndex: 'name',
    //   key: 'name',
    //   align: 'center',
    //   editable: true,
    //   width: '300px',
    //   render: (text, record) => {
    //     // enrich data feature

    //     const nameOptions = arrayUniqueByKey(enrichData, 'person')?.filter(
    //       (enrichItem) =>
    //         enrichItem?.record_id === record?.id &&
    //         enrichItem?.person?.trim() &&
    //         enrichItem?.person?.trim() !== record?.name?.trim()
    //     );
    //     const nameOptionsCount = nameOptions?.length || 0;
    //     return (
    //       // text &&
    //       nameOptionsCount > 0 ? (
    //         <>
    //           <Tooltip
    //             title={`Having ${nameOptionsCount} option(s) after enriching`}
    //           >
    //             <Badge count={nameOptionsCount}>
    //               <Select
    //                 placeholder="Missing input"
    //                 onSelect={(value) =>
    //                   handleSelectEnrichData({
    //                     ...record,
    //                     name: value,
    //                   })
    //                 }
    //                 optionRender={renderOption}
    //                 className="!border-none"
    //                 defaultValue={record?.name?.trim() || 'Missing input'}
    //                 options={[...nameOptions].map((item) => ({
    //                   ...item,
    //                   value: item?.person,
    //                   label: item?.person,
    //                 }))}
    //               />
    //             </Badge>
    //           </Tooltip>
    //         </>
    //       ) : (
    //         <div className="font-medium">{record?.name}</div>
    //       )
    //     );
    //   },
    // },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      editable: true,
      width: '5%',
      // defaultSortOrder: 'descend',
      // sorter: (a, b) => a?.email?.localeCompare(b?.email),
      render: (text, record) => {
        // enrich data feature
        const options = arrayUniqueByKey(
          enrichData,
          'valid_work_email'
        )?.filter(
          (enrichItem) =>
            enrichItem?.record_id === record?.id &&
            enrichItem?.valid_work_email?.trim() &&
            enrichItem?.valid_work_email?.trim() !== text?.trim()
        );
        const optionsCount = options?.length || 0;

        const listEmailCheckedTemp = [...listEmailChecked];
        const itemChecked = listEmailCheckedTemp.find(
          (checkedItem) => checkedItem?.email === text
        );
        const status =
          record?.validStatus || (itemChecked && itemChecked?.result);
        return (
          // text &&
          <div className="flex items-start gap-2 w-full justify-start">
            {optionsCount > 0 ? (
              <>
                <Tooltip
                  title={`Having ${optionsCount} option(s) after enriching`}
                >
                  <Badge count={optionsCount}>
                    <Select
                      placeholder="Missing input"
                      onSelect={(value) =>
                        handleSelectEnrichData({
                          ...record,
                          email: value,
                        })
                      }
                      optionRender={renderOption}
                      className="!border-none"
                      defaultValue={text?.trim() || 'Missing input'}
                      options={[...options].map((item) => ({
                        ...item,
                        value: item?.valid_work_email,
                        label: item?.valid_work_email,
                      }))}
                    />
                  </Badge>
                </Tooltip>
              </>
            ) : (
              <a
                className="flex items-center gap-1 justify-start w-fit font-medium"
                href={`mailTo:${text}`}
                target="_blank"
              >
                <MailOutlined className="text-[#0288d1]" />
                <span>{text}</span>
              </a>
            )}
            {record?.validStatus || itemChecked ? (
              <div className="flex items-center gap-1">
                <Tag
                  icon={VALIDATE_STATUS_ICON[status]}
                  color={VALIDATE_STATUS_COLOR[status]}
                >
                  {status}
                </Tag>
                <Tag
                  onClick={() => {
                    handleBulkEnrich([record]);
                  }}
                  style={{ cursor: 'pointer' }}
                  icon={<SyncOutlined />}
                  color="#55acee"
                >
                  Enrich
                </Tag>
              </div>
            ) : (
              <Button
                icon={<CheckOutlined />}
                size="small"
                onClick={() => handleValidateEmails([record])}
              >
                Validate
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: 'Company Name',
      dataIndex: 'companyName',
      key: 'companyName',
      editable: true,
      // align: 'center',
      width: '300px',
      // defaultSortOrder: 'descend',
      // sorter: (a, b) => a?.companyName?.localeCompare(b?.companyName),
      render: (text, record) => {
        return (
          text && (
            <div className="flex items-center gap-1 justify-start w-full font-medium">
              <HomeOutlined className="text-[#0288d1]" />
              <span className="line-clamp-1" title={text}>
                {text}
              </span>
            </div>
          )
        );
      },
    },
    {
      title: 'Job Title',
      dataIndex: 'contactTitle',
      key: 'contactTitle',
      editable: true,
      // align: 'center',
      width: '300px',

      // defaultSortOrder: 'descend',
      // sorter: (a, b) => a?.email?.localeCompare(b?.email),
      render: (text, record) => {
        return (
          text && (
            <div className="flex justify-start w-full">
              <span className="bg-[#d0fdf5] px-4 py-2 font-semibold rounded-lg flex gap-1 items-center">
                <SolutionOutlined />
                <span>{text}</span>
              </span>
            </div>
          )
        );
      },
    },
    {
      title: 'Phone Number',
      dataIndex: 'phone',
      key: 'phone',
      editable: true,
      align: 'center',
      width: '200px',

      render: (text, record) => {
        return (
          text && (
            <a
              className="flex items-center gap-1 justify-center"
              href={`tel:${text}`}
              target="_blank"
            >
              <PhoneOutlined />
              <span>{formatPhoneNumber(text)}</span>
            </a>
          )
        );
      },
    },
    // {
    //   title: 'Subscribed',
    //   dataIndex: 'subscribed',
    //   key: 'subscribed',
    //   editable: false,
    //   align: 'center',
    //   width: '200px',
    //   render: (text, record) => {
    //     const editable = isEditing(record);
    //     return (
    //       record && (
    //         <div>
    //           <Switch
    //             loading={loadingId && loadingId === record?.id}
    //             onChange={(e) => {
    //               handleUpdateContactToggle(record?.email, !e, record?.id);
    //             }}
    //             checked={editable ? !createWithSubscribed : (!record?.isUnsubscribeEmail ? true : false)}
    //           />
    //         </div>
    //       )
    //     );
    //   },
    // },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      width: '100px',
      render: (text, record) => {
        const editable = isEditing(record);
        return (
          <div className="flex items-center justify-center w-full font-semibold ">
            {editable ? (
              <>
                <Button
                  type="dashed"
                  className="flex gap-1 items-center"
                  onClick={() => save(record?.key)}
                  icon={<CheckOutlined />}
                  loading={contactLoading}
                >
                  Save
                </Button>
                {/* {record?.id && ( */}
                <Popconfirm
                  title="Sure to cancel?"
                  onConfirm={record?.id ? cancel : cancelAdd}
                >
                  <Button
                    type="text"
                    danger
                    className="flex gap-1 items-center"
                    icon={<StopOutlined />}
                  >
                    <span>Cancel</span>
                  </Button>
                </Popconfirm>
                {/* )} */}
              </>
            ) : (
              <>
                <Dropdown
                  disabled={editingKey}
                  menu={{
                    items: [
                      tab !== 'tab_invalid' && {
                        key: 'edit',
                        label: (
                          <div className="flex gap-2 items-center">
                            <EditOutlined size={15} />
                            <span>Edit</span>
                          </div>
                        ),
                        onClick: () => edit(record),
                      },
                      {
                        key: 'delete',
                        label: (
                          <Popconfirm
                            title="Delete the contact"
                            description="Are you sure to delete this contact?"
                            onConfirm={() => handleDeleteContact(record?.id)}
                            // onCancel={cancel}
                            okText="Yes"
                            cancelText="No"
                          >
                            <div className="flex gap-2 items-center">
                              <DeleteOutlined size={15} />
                              <span>Delete</span>
                            </div>
                          </Popconfirm>
                        ),
                      },
                    ],
                  }}
                  placement="bottom"
                  arrow
                >
                  <Button
                    type="text"
                    onClick={(e) => e.stopPropagation()}
                    icon={<MoreOutlined />}
                  />
                </Dropdown>
              </>
            )}
          </div>
        );
      },
    },
  ];

  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleAdd = () => {
    const newData = {
      key: uuid(),
      name: '',
      firstName: '',
      lastName: '',
      linkedInProfileUrl: '',
      email: '',
      phone: '',
      contactApolloId: null,
      contactTitle: '',
    };
    setDataSource([newData, ...dataSource]);
    setEditingKey(newData?.key);
  };

  const cancelAdd = () => {
    const newDataSource = [...dataSource];
    newDataSource.shift();
    setDataSource([...newDataSource]);
    setEditingKey('');
  };

  useEffect(() => {
    getAllContacts(searchText);
  }, [searchText]);

  const debouncedSearchText = _.debounce(
    async (e) => setSearchText(e?.target?.value),
    500
  );

  const validateEmails = async () => {
    const listEmail = dataSource?.filter((item) =>
      selectedRowKeys?.includes(item?.id)
    );
    handleValidateEmails(listEmail);
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (!el) return;
    if (type === 'add') {
      setEnrichLoading(true);
      el.classList.add('expanded');
      el.classList.add('collapsed');
    } else if (type === 'remove') {
      setEnrichLoading(false);
      el.classList.remove('expanded');
      el.classList.remove('collapsed');
    }
  };

  const endProcess = () => {
    toggleLoading('remove');
  };

  const handleBulkEnrich = async (contacts = []) => {
    message.info('Starting enrich contact(s) data!');
    const selectedContacts =
      contacts?.length === 0
        ? dataSource.filter((contact) => selectedRowKeys?.includes(contact?.id))
        : contacts;
    if (!selectedContacts || selectedContacts?.length === 0) return;

    const enrichContacts = [...selectedContacts].map((contact) => ({
      ...contact,
      person: contact?.name || '',
      // record_id: contact?.id,
      record_id: contact?.id, // For testing only
      // company: contact?.clientCorporation?.name || '',
      linkedin_url: contact?.linkedInProfileUrl,
      work_email_2: '',
      valid_work_email: contact?.email,
    }));

    toggleLoading();
    const delay = parseInt(
      selectedRowKeys.length *
        defaultEnrichFields.length *
        TIME_ENRICH_EACH_FIELD,
      10
    );

    var timeout = new Date().getTime() + delay;
    setEstimationDuration(timeout);
    const selectedContactsPayload = enrichContacts
      ?.filter(
        (contact) =>
          contacts?.length > 0 || selectedRowKeys.includes(contact?.id)
      )
      .map((contact) => ({
        fullName: contact?.person || '',
        recordId: contact?.record_id,
        companyName: contact?.company || '',
        linkedInUrl: contact?.linkedin_url,
      }));

    const payload = {
      data: selectedContactsPayload,
      listId: contactListId,
      type: ENRICH_DATA_LIST_TYPE.CONTACTLIST,
    };
    setEnrichingData([
      ...enrichingData,
      ...selectedContactsPayload?.map((item) => item?.recordId),
    ]);
    await handleBulkEnrichContactData(payload);
  };

  const handleEnrichAll = async () => {
    message.info('Starting enrich contact(s) data!');
    toggleLoading();

    setEnrichingData([...dataSource?.map((item) => item?.id)]);
    await enrichAllContactList(contactListId);
  };

  const handleBulkAction = async (key) => {
    if (key?.key === 'delete-contacts') {
      setDeleteLoading(true);
      await bulkDeleteContactsInList({ ids: selectedRowKeys })
        .then(() => {
          notification.success({
            message: 'Contact Lists deleted!',
          });
          setSelectedRowKeys([]);
          getAllContacts();
        })
        .catch((ex) => {
          notification.error({
            message: 'Something went wrong',
          });
        })
        .finally(() => setDeleteLoading(false));
    }

    if (key?.key === 'add-to-sequence') {
      await handleGetListData();
    }

    if (key?.key === 'validate-emails') {
      await validateEmails();
    }

    if (key?.key === 'enrich-emails') {
      handleBulkEnrich();
    }

    if (key?.key === 'enrich-all') {
      handleEnrichAll();
    }
  };

  const handleGetInValidEmail = async () => {
    setLoadingInValid(true);
    try {
      const rawData = await getAllContacts('', 9999);
      const checkedEmails = await handleValidateEmails(rawData, true);
      const invalidEmails = checkedEmails
        ?.filter((item) => item?.result === 'Invalid')
        ?.map((item) => item?.email);

      if (!invalidEmails || invalidEmails?.length === 0) {
        setLoadingInValid(false);
        return;
      }

      setDataListInvalidEmail(
        rawData?.filter((item) => invalidEmails?.includes(item?.email))
      );
      setLoadingInValid(false);
    } catch (error) {
      setLoadingInValid(false);
      console.log('handleGetInValidEmail: ', error);
      notification.error({
        description: 'Something went wrong!',
      });
    }
  };

  useEffect(() => {
    if (tab === 'tab_invalid') {
      handleGetInValidEmail();
    }
  }, [tab]);

  const tabItems = [
    {
      key: 'tab_all',
      label: 'All',
      children: (
        <>
          <div className="col-span-10 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                type="primary"
                className="flex items-center "
                disabled={isFetching || editingKey}
                onClick={handleAdd}
              >
                <PlusOutlined /> <span>Add New Contact</span>
              </Button>
              {/* {editingKey && (
                <Button
                  type="primary"
                  className="flex items-center "
                  onClick={cancelAdd}
                >
                  <CloseOutlined /> <span>Cancel New Contact</span>
                </Button>
              )} */}
              {selectedRowKeys?.length > 0 && (
                <div>
                  <Dropdown
                    className="animated fadeInDownBig"
                    placement="bottom"
                    arrow
                    menu={{
                      items: [
                        {
                          key: 'enrich-emails',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <ThunderboltOutlined />
                              <span>Bulk Enrich</span>
                            </a>
                          ),
                        },
                        {
                          key: 'enrich-all',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <ThunderboltOutlined />
                              <span>Enrich All</span>
                            </a>
                          ),
                        },
                        {
                          key: 'delete-contacts',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <DeleteOutlined />
                              <span>Bulk Delete</span>
                            </a>
                          ),
                        },
                        {
                          key: 'add-to-sequence',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <PlusOutlined />
                              <span>Add to Sequence</span>
                            </a>
                          ),
                        },
                        {
                          key: 'validate-emails',
                          label: (
                            <a className="Montserrat flex gap-2 items-center py-2">
                              <AuditOutlined />
                              <span>Validate Emails</span>
                            </a>
                          ),
                        },
                      ],
                      onClick: handleBulkAction,
                    }}
                  >
                    <Space>
                      <Button
                        type="primary"
                        className=" !border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                        loading={deleteLoading}
                      >
                        <p className="Montserrat">
                          {`${selectedRowKeys?.length} Selected`}
                        </p>
                        <DownOutlined />
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              )}
            </div>

            <Search
              size="middle"
              className="customize-search-container max-w-sm"
              allowClear
              placeholder="Search contact..."
              enterButton="Search"
              onChange={debouncedSearchText}
            />
          </div>
          <div className="col-span-10 search-table-new-design-container">
            <Form form={form} component={false}>
              <Table
                className="contact-table-container col-span-10"
                // style={{
                //   width: '84vw',
                // }}
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                rowClassName="editable-row"
                dataSource={dataSource}
                // scroll={{
                //   x: 300,
                // }}
                // bordered
                columns={mergedColumns}
                loading={isFetching}
                rowSelection={editingKey ? null : rowSelection}
                rowKey={(record) => record.id}
                pagination={false}
              />
              <Pagination
                className="mt-3"
                // defaultCurrent={pagination.page}
                current={pagination.page}
                total={pagination.total}
                showSizeChanger={false}
                onChange={(page, pageSize) => {
                  handlePagination(page, pageSize);
                  setSelectedRowKeys([]);
                }}
              />
            </Form>
          </div>
        </>
      ),
    },
    {
      key: 'tab_invalid',
      label: 'Invalid',
      children: (
        <div>
          <div className="col-span-10 search-table-new-design-container">
            <div className="custom-valid-table">
              <Table
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                rowClassName="editable-row"
                dataSource={dataListInValidEmail}
                // bordered
                columns={mergedColumns.filter(
                  (item) => item?.dataIndex !== 'actions'
                )}
                loading={loadingGetInValid}
                rowSelection={editingKey ? null : rowSelection}
                rowKey={(record) => record.id}
                // pagination={false}
              />
            </div>
          </div>
        </div>
      ),
    },
  ];

  const getEnrichData = async (query) => {
    try {
      const { data } = await handleGetEnrichData(contactListId, query);
      return data?.result?.items || [];
    } catch (error) {
      return [];
    }
  };

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    setUpdatingData({ ...enrichedDataObject });
    message.success(`Enriched 1 contact successfully!`);
  };

  const handleUpdateEnrichData = async () => {
    const newDataSource = [
      ...enrichData,
      { ...updatingData, createdAt: new Date() },
    ];

    setEnrichData([...newDataSource]);
    setUpdatingData({});
    const query = {
      type: ENRICH_DATA_LIST_TYPE.CONTACTLIST,
      status: ENRICH_DATA_STATUS.IN_PROGRESS,
    };
    const enrichedData = await getEnrichData(query);
    if (enrichedData?.length === 0) {
      endProcess();
      message.success('Enriching process finished!');
    }
  };

  const checkEnrichingData = async () => {
    const query = {
      type: ENRICH_DATA_LIST_TYPE.CONTACTLIST,
    };
    const enrichedDataTemp = await getEnrichData(query);

    if (enrichedDataTemp?.length > 0) {
      const inprogressList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.IN_PROGRESS
      );
      const sucessfullList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.SUCCESSFUL
      );
      const enrichedData = sucessfullList?.map((contact) => ({
        ...contact?.enrichContact,
        createdAt: contact?.createdAt,
      }));

      if (inprogressList?.length > 0) {
        setEnrichingData(
          [...inprogressList]?.map((item) => item?.contactId?.toString())
        );
        toggleLoading();
      }
      setEnrichData([...enrichedData]);
    }
  };

  useEffect(() => {
    if (isEmpty(updatingData)) return;
    handleUpdateEnrichData();
  }, [updatingData]);

  useEffect(() => {
    checkEnrichingData();
  }, []);

  return (
    <div className="font-Montserrat bg-white first-letter:p-4 rounded-md shadow-md grid  p-4 custom-tab">
      <Tabs
        tabBarExtraContent={
          <div className="col-span-10" id="expand-container-bulk-enrich">
            <div className="w-full" id="expand-contract-bulk-enrich">
              <div className="w-full justify-center items-center flex gap-4 items-center">
                {enrichLoading && (
                  <EventSourceRender
                    updateRecentlyData={updateRecentlyData}
                    sseName="ENRICH_FLOQER_DATA_LINKEDIN_URL"
                  />
                )}
                <Loading />
                <div className="font-semibold text-cyan-700 italic tracking-wide">
                  Enriching...
                </div>
              </div>
            </div>
          </div>
        }
        destroyInactiveTabPane={true}
        defaultActiveKey="tab_all"
        activeKey={tab}
        items={tabItems}
        style={{
          width: '90% !important',
        }}
        onChange={(e) => setTab(e)}
      />

      <Modal
        width={600}
        style={{ overflowY: 'auto', top: 50 }}
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>Create a new Sequence</div>
          </div>
        }
        open={openModalSendEmail}
        onCancel={() => {
          setOpenSendEmail(false);
        }}
        footer={false}
      >
        <div className="">
          <div className="sequence-detail create-sequence-from-vacancy-container">
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                openModalSendEmail={openModalSendEmail}
                setOpenSendEmail={setOpenSendEmail}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                fromSequenseEmail={true}
                newUpdatedSequence={true}
                listAddContactSelected={dataContact}
                fromCreateByVacancy={true}
                notLoadingData={true}
              />
            </Form>
          </div>
        </div>
      </Modal>

      {/* Enrich data */}
      {/* <Modal
        title="Bulk Enrich contacts data"
        open={openBulkEnrichModal}
        footer={null}
        width={1200}
        closable={false}
        destroyOnClose={true}
      >
        <BulkEnrichData
          listEmailChecked={listEmailChecked}
          selectedContacts={
            tab === 'tab_all'
              ? dataSource.filter((contact) =>
                  selectedRowKeys.includes(contact?.id)
                )
              : dataListInValidEmail.filter((contact) =>
                  selectedRowKeys.includes(contact?.id)
                )
          }
          reloadData={
            tab === 'tab_all' ? getAllContacts : handleGetInValidEmail
          }
          closeBulkEnrichModal={closeBulkEnrichModal}
          isHotList={false}
          handleUpdateContact={handleUpdateContact}
        />
      </Modal> */}
    </div>
  );
};

export default ContactListTable;
