import { Button, notification, Upload } from 'antd';
import ContactListTable from './ContactListTable';
import {
  FileExcelOutlined,
  LeftOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import {
  addUserToList,
  getDetailContactList,
  getExampleFile,
  insertContactToList,
  updateContactFromList,
} from '../../services/contactList';
import { getLinkS3 } from '../../services/aws';
import { v4 as uuid } from 'uuid';
import { validListEmail } from '../../services/emailFinder';
import {
  createNewEmailValid,
  getEmailStatus,
  getNewEmailValid,
} from '../../services/bullhorn';

const ContactList = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [contactLoading, setContactLoading] = useState(false);
  const [data, setData] = useState([]);

  const [loadingDownload, setLoadingDownload] = useState(false);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [errorData, setErrorData] = useState([]);
  const [dataFileInsert, setDataFileInsert] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
    total: 0,
  });

  let { contactListId } = useParams();

  const fetchImageUrl = async (imageId) => {
    try {
      const { data } = await getLinkS3(imageId);
      return data;
    } catch (error) {
      return '';
    }
  };

  const getAllContacts = async (searchText = '', limit = 10) => {
    if (limit <= 100) {
      setLoading(true);
    }
    try {
      const { data } = await getDetailContactList(
        contactListId,
        pagination.page,
        searchText,
        [],
        limit
      );

      const rawData = [...data?.result?.items];
      if (limit > 100) {
        return rawData;
      }
      const dataWithAvt = await Promise.allSettled(
        rawData.map(async (item) => {
          if (!item?.linkedInProfileImageUrl) return { ...item, key: uuid() };
          const linkedinAvtUrl = await fetchImageUrl(
            item?.linkedInProfileImageUrl
          );
          return { ...item, linkedinAvtUrl, key: uuid() };
        })
      );

      const listEmail = await rawData?.map((item) => item.email);
      const { data: dataListEmailValid } = await getNewEmailValid({
        emails: listEmail,
      });

      if (dataListEmailValid?.result?.length > 0) {
        setListEmailChecked([
          ...listEmailChecked,
          ...dataListEmailValid?.result?.map((item) => ({
            ...item,
            result: item?.status,
          })),
        ]);
      }
      setData([...dataWithAvt.map((item) => item?.value)]);
      setPagination({
        ...pagination,
        total: data?.result?.count,
      });
      setLoading(false);
    } catch (error) {
      notification.error({
        description: 'Contact List not existing or deleted by someone!',
      });
      console.error('error: ', error);
      setLoading(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file) => {
      return false;
    },
    onChange({ file }) {
      if (file?.status === 'removed') {
        setFileList([]);
        return;
      }
      if (fileList?.uid === file.uid) {
        setFileList([]);
      } else {
        setFileList([file]);
      }
    },
  };

  const handleSave = async () => {
    setErrorData([]);
    let formData = new FormData();
    formData.append('id', contactListId);
    if (fileList?.length > 0) {
      formData.append('csvFile', fileList[0]);
    }

    try {
      setLoading(true);
      const { data } = await insertContactToList(formData);
      if (data) {
        setDataFileInsert(data?.result?.fullData ?? []);
        if (data?.result?.errors?.length > 0) {
          notification.warning({
            message: 'Error on Upload CSV',
          });
          setErrorData(data?.result?.errors);
          setLoading(false);
          // return;
        }

        if (data?.result?.data?.length > 0) {
          setFileList([]);
          notification.success({
            message: `${data?.result?.data?.length} / ${data?.result?.fullData?.length} are added`,
          });
        }
      }
      getAllContacts();
      setLoading(false);
    } catch (e) {
      console.log(e);
      notification.error({
        message: e?.response?.data?.message,
      });
      setLoading(false);
    }
  };

  const handleAddNewContact = async (contactData) => {
    setContactLoading(true);
    try {
      const payload = {
        ...contactData,
        listIds: [contactListId],
        linkedinUrl: contactData?.linkedInProfileUrl,
      };

      const { data } = await addUserToList(payload);

      if (data) {
        notification.success({
          message: 'Create new contact success',
        });
      }
      setContactLoading(false);
      getAllContacts();
    } catch (error) {
      notification.error({
        message: 'Something went wrong',
      });
      setContactLoading(false);
    }
  };

  const handleUpdateContact = async (contactData, key) => {
    setContactLoading(true);
    const payload = {
      ...contactData,
      linkedinUrl: contactData?.linkedInProfileUrl,
    };
    try {
      const { data } = await updateContactFromList(
        contactListId,
        contactData?.id,
        payload
      );
      notification.success({
        message: `Update Contact ${contactData?.name} success`,
      });
      setContactLoading(false);
      return data?.result;
    } catch (errors) {
      notification.error({
        message: `Update Contact ${contactData?.name} failed`,
      });
      setContactLoading(false);
    }
  };

  const handlePagination = (page) => setPagination({ ...pagination, page });

  const handleDownloadExampleFile = async () => {
    setLoadingDownload(true);
    const response = await getExampleFile();
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'example.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    setLoadingDownload(false);
  };

  const handleCreateNewEmailValid = async (body) => {
    try {
      const { data } = await createNewEmailValid({ data: body });
    } catch (e) {
      console.log('handleCreateNewEmailValid err: ', e);
      notification.error({
        description: 'Network error! Try again later.',
      });
    }
  };

  const handleValidateEmails = async (selectedContacts, isReturn = false) => {
    if (!isReturn) {
      setLoading(true);
    }
    try {
      const listValidatedEmails = [...listEmailChecked].map(
        (item) => item?.email
      );
      const emails = !isReturn
        ? selectedContacts
            ?.map((item) => item?.email || '')
            ?.filter((email) => email && !listValidatedEmails.includes(email))
        : selectedContacts?.map((item) => item?.email || '');

      if (emails?.length === 0 && !isReturn) {
        notification.info({
          description: 'All selected emails were checked!',
        });
        setLoading(false);
        return;
      }

      const data = await getEmailStatus(emails);
      const listEmailCheckedTemp = [...data?.flat(Infinity)];
      setListEmailChecked([...listEmailChecked, ...listEmailCheckedTemp]);
      if (isReturn) {
        return listEmailCheckedTemp;
      }

      await handleCreateNewEmailValid(
        listEmailCheckedTemp?.map((item) => ({
          email: item?.email,
          status: item?.result,
        }))
      );
      notification.success({
        description: `Validated ${emails?.length} emails successful!`,
      });
      setLoading(false);
    } catch (error) {
      console.log('handleValidateEmails error: ', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getAllContacts();
  }, [pagination.page]);

  return (
    <div className="flex flex-col gap-5 p-4">
      <div>
        <div className="flex justify-between text-2xl font-semibold">
          <div className="flex gap-3 items-center">
            <Button
              className="bg-white flex items-center "
              disabled={loading}
              onClick={() => {
                navigate(-1);
              }}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
            <span className="Montserrat">Contact List</span>
          </div>
          <div className="flex gap-3 items-center">
            <Button
              className="text-xs italic hover:!text-blue-300"
              size="small"
              loading={loadingDownload}
              type="text"
              onClick={handleDownloadExampleFile}
            >
              Example csv file
            </Button>
            <div>
              <Upload
                className="flex gap-3"
                {...uploadProps}
                fileList={fileList}
              >
                <Button
                  type="primary"
                  className="flex items-center "
                  icon={<FileExcelOutlined />}
                >
                  Insert By CSV
                </Button>
              </Upload>
            </div>
            {fileList.length > 0 && (
              <div style={{ marginLeft: '10px' }}>
                <Button
                  loading={loading}
                  onClick={() => handleSave()}
                  type="dashed"
                >
                  Save
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      <ContactListTable
        isFetching={loading}
        dataSource={data}
        contactListId={contactListId}
        getAllContacts={getAllContacts}
        setDataSource={setData}
        handleAddNewContact={handleAddNewContact}
        contactLoading={contactLoading}
        setLoading={setLoading}
        handleUpdateContact={handleUpdateContact}
        pagination={pagination}
        handlePagination={handlePagination}
        fetchImageUrl={fetchImageUrl}
        handleValidateEmails={handleValidateEmails}
        listEmailChecked={listEmailChecked}
      />
    </div>
  );
};

export default ContactList;
