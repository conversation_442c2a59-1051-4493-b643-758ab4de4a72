/* eslint-disable no-unused-vars */
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Select, message } from 'antd';
import { addJob } from '../../services/jobs';
import {
  keywords,
  industryData,
  statusData,
  leadTypeData,
  vacancyData,
} from './const';
let index = 0;

export default function AddLead() {
  const navigate = useNavigate();
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState(false);
  const [customKeywords, setCustomKeywords] = useState([]);
  const [leadData, setLeadData] = useState({
    job_title: '',
    company: '',
    skill: '',
    vacancy: '',
    lead_type: '',
    status: '',
    lead_source: '',
    description: '',
    industries: '',
  });
  const [selectedItems, setSelectedItems] = useState([]);
  const [items, setItems] = useState(keywords);
  const [name, setName] = useState('');
  const inputRef = useRef(null);
  const onNameChange = (event) => {
    setName(event.target.value);
  };

  const addItem = (e) => {
    e.preventDefault();
    setItems([...items, name || `New item ${index++}`]);
    setName('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const onInputChange = (event) => {
    const { name, value } = event.target;
    setLeadData({ ...leadData, [name]: value });
  };

  useEffect(() => {
    setCustomKeywords(JSON.parse(localStorage.getItem('keywords')));
  }, []);

  const onIndusASelChange = (value) => {
    setLeadData({ ...leadData, industries: value });
  };

  const onLeadTypeASelChange = (value) => {
    setLeadData({ ...leadData, lead_type: value });
  };

  const onStatusASelChange = (value) => {
    setLeadData({ ...leadData, status: value });
  };

  const onVacancyASelChange = (value) => {
    setLeadData({ ...leadData, vacancy: value });
  };

  const onDesTextChange = (value) => {
    setLeadData({ ...leadData, description: value });
  };

  const onFinish = async () => {
    setLoading(true);
    await addJob({ leadData })
      .then((res) => {
        messageApi.open({
          type: 'success',
          content: 'Added Successfully',
        });
      })
      .catch((err) =>
        messageApi.open({
          type: 'error',
          content: err,
        })
      );
    setLoading(false);
    setLeadData({
      job_title: '',
      company: '',
      skill: '',
      vacancy: '',
      lead_type: '',
      status: '',
      lead_source: '',
      description: '',
      industries: '',
    });
    navigate('/my-leads');
  };

  const onKeywordsChange = (value) => {
    if (
      value.length != 0 &&
      customKeywords.filter(
        (item) => item.value === value[value.length - 1].toLowerCase()
      ).length == 0
    ) {
      setCustomKeywords([
        ...customKeywords,
        {
          value: value[value.length - 1].toLowerCase(),
          label: value[value.length - 1],
        },
      ]);
      localStorage.setItem(
        'keywords',
        JSON.stringify([
          ...customKeywords,
          {
            value: value[value.length - 1].toLowerCase(),
            label: value[value.length - 1],
          },
        ])
      );
      setLeadData({ ...leadData, skill: value });
    } else {
      setLeadData({ ...leadData, skill: value });
    }
  };

  return (
    <div
      className={
        'w-[80%] mx-auto p-10 flex flex-col rounded-[20px] bg-white bg-clip-border shadow-3xl shadow-shadow-500 dark:!bg-navy-800 dark:text-white dark:shadow-none'
      }
    >
      <div className="space-y-12">
        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label
                htmlFor="job_title"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Job Title
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="job_title"
                  id="job_title"
                  value={leadData.job_title}
                  onChange={onInputChange}
                  autoComplete="given-name"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="company"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Company
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="company"
                  id="company"
                  value={leadData.company}
                  onChange={onInputChange}
                  autoComplete="family-name"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Skill
              </label>
              <div className="mt-2">
                <Select
                  mode="tags"
                  style={{
                    width: '100%',
                  }}
                  placeholder="Tags Mode"
                  onChange={onKeywordsChange}
                  options={customKeywords}
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Indutry
              </label>
              <div className="mt-2">
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="Select a Industry"
                  optionFilterProp="children"
                  onChange={onIndusASelChange}
                  // onSearch={onSearch}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={industryData}
                />
              </div>
            </div>
            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Vacancy
              </label>
              <div className="mt-2">
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="Select a Vacancy"
                  optionFilterProp="children"
                  onChange={onVacancyASelChange}
                  // onSearch={onSearch}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={vacancyData}
                />
              </div>
            </div>
            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Lead Type
              </label>
              <div className="mt-2">
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="Select a Lead Type"
                  optionFilterProp="children"
                  onChange={onLeadTypeASelChange}
                  // onSearch={onSearch}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={leadTypeData}
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Status
              </label>
              <div className="mt-2">
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="Select a Status"
                  optionFilterProp="children"
                  onChange={onStatusASelChange}
                  // onSearch={onSearch}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={statusData}
                />
              </div>
            </div>

            <div className="col-span-full">
              <label
                htmlFor="lead_source"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Lead Source
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="lead_source"
                  id="lead_source"
                  value={leadData.lead_source}
                  onChange={onInputChange}
                  autoComplete="leadSource"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="col-span-full">
              <label
                htmlFor="description"
                className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
              >
                Description
              </label>
              <div className="mt-2">
                <textarea
                  id="description"
                  name="description"
                  rows={3}
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  defaultValue={''}
                  onChange={(e) => onDesTextChange(e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900 dark:text-white"
        >
          Cancel
        </button>
        <button
          onClick={onFinish}
          className="rounded-md bg-[#e30075] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#e30075] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-[#e30075]"
        >
          Sumit
        </button>
      </div>
    </div>
  );
}
