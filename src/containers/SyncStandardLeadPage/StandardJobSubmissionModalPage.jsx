import { useForm } from 'react-hook-form';
import JobDetail from '../../components/JobDetail';
import {
  Button,
  Form,
  message,
  notification,
  Progress,
  Steps,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { SwapRightOutlined } from '@ant-design/icons';
import { isGrantedSequence } from '../../helpers/getUserViewAs';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import LeadForm from '../CRM/Leads/LeadForm';
import { MODE_CONSTANTS } from '../../constants/common.constant';
import StandardBullhornSendEmailModal from './StandardEmailTriggerStep';
import { sendJobToBullhorn } from '../../services/bullhorn';
import { updateJobLogs } from '../../services/jobs';

const StandardJobSubmissionModalPage = ({ job, jobSkills }) => {
  const navigate = useNavigate();
  const { searchId, jobId } = useParams();

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const [numberStep, setNumberStep] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [isOpenSendMailStep, setOpenSendMailStep] = useState(false);
  const [emailSeqId, setEmailSeqId] = useState('');
  const [disableActionSequence, setDisableActionSequence] = useState(true);
  const [notConfirmStep, setConfirmStep] = useState(false);
  const [notDiscard, setNotDiscard] = useState(true);
  const [listEmailSend, setListEmailSend] = useState([]);
  const [pinnedSequence, setPinnedSequence] = useState(true);
  const [loadingSimilar, setLoadingSimilar] = useState(false);
  const [listContactSelected, setListContactSelected] = useState([]);
  const [sendingStatus, setSendingStatus] = useState(false);

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    formState,
    getValues,
  } = useForm({
    defaultValues: { ...job },
  });

  const handleEditLogs = async () => {
    const payload = {
      type: 'SENT_TO_BH_VACANCY_SUBMISSION_SUCCESSFULLY',
      jobId: job?.job_id,
    };
    await updateJobLogs(payload);
  };
  const back = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      const url =
        searchId !== 'non-search' ? `/syncs/true/search/${searchId}` : `/syncs`;
      navigate(url);
    }
  };

  const onSubmit = async (payload) => {
    // Send data to server API using axios or your preferred method
    try {
      setSendingStatus(true);
      const emailPayload = {
        emailSeqId,
        jobBoardId: job?.job_id,
        recipients: [],
        isNotSendSequence: getValues('sequenceStatus') ?? false,
        primaryMail: null,
        externalJobId: null,
      };

      let tempData = {
        ...payload,
        description: payload?.description
          ? payload?.description?.replace(/\n/g, '<br />')
          : payload?.description,
        cleanDescription: payload?.cleanDescription,
        permFee: 0,
        salary: `${payload?.salary ? payload.salary : 0}`,
        payRate: `${payload?.rate ? payload.rate : 0}`,
        job,
        searchId,
        sentJobId: null,
        source: 'Zileo',
        countryAddress: payload?.state,
        skills: payload?.skills?.filter((item) => item?.trim()),
      };
      console.log('tempData: ', tempData);
      const { data } = await sendJobToBullhorn(tempData);

      if (data?.success) {
        // setOnclose(true);
        setCurrentStep(0);
        setSendingStatus(false);
        // setHaveSendJob(true);
        // setIsModalVisible(false);
        reset();
        // handleResetForm();
        message.info('Job sent to Bullhorn Successfully');
        handleEditLogs();
        back();
      }
    } catch (err) {
      setCurrentStep(0);
      setSendingStatus(false);
      message.error(err?.response?.data?.message ?? 'Something went wrong');
    }
  };

  const handleOk = () => {
    handleSubmit(onSubmit)();
  };

  const nextStep = () => {
    if (getValues('jobtitle')?.length > 100) {
      notification.error({
        message: 'Job Title is too long',
        description: 'The job title cannot be longer than 100 characters.',
      });
      return;
    }
    if (getValues('address1')?.length > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'Address cannot be longer than 100 characters.',
      });
      return;
    }
    setDisableActionSequence(false);
    setCurrentStep(currentStep + 1);
  };
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const vacancyForm = (
    <div className="w-full">
      {/* <div className="bullhorn-job-submission-form-container p-5"> */}
      <LeadForm
        lead={job}
        back={() => {}}
        mode={MODE_CONSTANTS.SYNC}
        formState={getValues()}
        setFormState={setValue}
      />
      {/* </div> */}
    </div>
  );

  const sendMailForm = (
    <>
      <div
        id="job-detail-container"
        className="h-full overflow-scroll pt-3 px-3"
      >
        <JobDetail
          job={
            job || {
              jobtitle: getValues('jobtitle'),
              description: getValues('description'),
              company: getValues('company'),
              source: getValues('source'),
              salary: getValues('salary'),
              joblocationcity: getValues('joblocationcity'),
              jobtype: getValues('jobtype'),
            }
          }
        />
      </div>
    </>
  );

  const confirmationStep = () => {
    const isValidatedSyncForm = formState.isValid;

    const completePercentage = isValidatedSyncForm ? 100 : 66;

    return (
      <div className="flex justify-center w-full px-3">
        <div className="p-4 rounded-md shadow-md border w-full flex flex-col gap-3">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div className="font-semibold text-xl">Summary</div>
            </div>
          </div>
          <div>
            <Progress percent={completePercentage} showInfo={false} />
          </div>
          <div>
            <Steps
              direction="vertical"
              current={2}
              items={[
                {
                  title: <div className="font-medium">Job Review</div>,
                  description: (
                    <div>
                      {isValidatedSyncForm ? 'Completed!' : 'Missing Fields'}
                    </div>
                  ),
                  status: isValidatedSyncForm ? 'finish' : 'error',
                },
                {
                  title: (
                    <div className="font-medium flex items-center gap-1">
                      Sequence <span className="font-semibold">(optional)</span>
                    </div>
                  ),
                  description: (
                    <div>
                      {!emailSeqId?.trim()
                        ? 'No sequence created!'
                        : 'Your sequence has been created!'}
                    </div>
                  ),
                  status: !emailSeqId?.trim() ? 'error' : 'finish',
                },
                {
                  title: 'Confirmation',
                  description: (
                    <div className="flex items-center gap-2 font-medium opacity-80">
                      <div>
                        If you checked all the information and it is correct,
                        please
                      </div>
                      <Tooltip
                        title={
                          isValidatedSyncForm
                            ? 'Syncing job'
                            : 'Please check required fields again.'
                        }
                      >
                        <Button
                          disabled={!isValidatedSyncForm}
                          htmlType="submit"
                          loading={sendingStatus}
                          onClick={handleOk}
                          type="primary"
                        >
                          Save
                          <SwapRightOutlined />
                        </Button>
                      </Tooltip>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </div>
      </div>
    );
  };

  const steps = [
    {
      key: 'update-job-detail',
      title: <span className="font-semibold">Job Review</span>,
      content: vacancyForm,
      description: 'Update job detail.',
    },
    {
      key: 'send-mail',
      title: (
        <span className="font-semibold">
          <Tooltip
            placement="topLeft"
            title={
              disableActionSequence
                ? 'Need to review Job first then click Next'
                : ''
            }
          >
            Sequence <span className="text-sm italic">(optional)</span>
          </Tooltip>
        </span>
      ),
      content: sendMailForm,
      description: (
        <Tooltip
          placement="topLeft"
          title={
            disableActionSequence
              ? 'Need to review Job first then click Next'
              : ''
          }
        >
          Set up a sequence automation
        </Tooltip>
      ),
      disabled: disableActionSequence,
    },
    {
      key: 'finish',
      title: <span className="font-semibold">Confirmation</span>,
      content: <>{confirmationStep()}</>,
      description: 'Confirm your actions.',
      disabled: getValues('actionSaveEmail') ? false : true,
    },
  ];

  const onChangeSteps = (step) => {
    if (step === 2 && !getValues('actionSaveEmail')) {
      notification.error({
        message: (
          <em>
            You haven't set up the <strong>Sequence</strong> yet, so please do
            set it up and remember to <strong>save</strong> them for the next
            step. Otherwise, press <strong>do not sequence</strong>
          </em>
        ),
      });
      return;
    }
    if (getValues()?.jobtitle?.length > 100) {
      notification.error({
        message: 'Job Title is too long',
        description: 'The job title cannot be longer than 100 characters.',
      });
      return;
    }
    if (getValues()?.address1?.length > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }
    const SEND_MAIL_STEP = 1;
    const CONFIRM_STEP = 2;
    // if (step === SEND_MAIL_STEP) {
    //   console.log("showCompany: ", showCompany)
    //   if (!showCompany) {
    //     message.warning('Please complete to update job first!');
    //     return;
    //   }
    // }
    setCurrentStep(step);
    // console.log('isOpenSendMailStep: ', isOpenSendMailStep);
    // if (step === SEND_MAIL_STEP && !isOpenSendMailStep) {
    //   getDataEmail();
    // }
  };

  const isAllowToSequence = userToSet && isGrantedSequence();

  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  useEffect(() => {
    if (jobSkills?.length > 0) {
      setValue('jobskills', jobSkills);
    }
  }, [jobSkills]);

  return (
    <div className="sequence-background-container w-full h-full p-4">
      <div className="grid grid-cols-7 gap-4">
        <div className={currentStep === 1 ? `col-span-5` : 'col-span-7'}>
          <Steps
            type="navigation"
            current={currentStep}
            items={steps}
            onChange={onChangeSteps}
          />
          <div className="mt-3">{steps[currentStep].content}</div>
          <div className="flex justify-start gap-2 mt-5">
            <div>
              {currentStep === 1 && (
                <Button type="primary" onClick={nextStep}>
                  Do not sequence
                </Button>
              )}

              {currentStep > 0 && (
                <Button
                  style={{
                    margin: '0 8px',
                  }}
                  onClick={prevStep}
                >
                  Previous
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button
                  disabled={
                    currentStep != 1
                      ? false
                      : getValues('actionSaveEmail')
                        ? false
                        : true
                  }
                  type="primary"
                  onClick={nextStep}
                >
                  Next
                </Button>
              )}
            </div>
          </div>
          {!getValues('actionSaveEmail') && currentStep == 1 && (
            <div style={{ marginLeft: '20px', marginTop: '10px' }}>
              <em>
                You haven't set up the <strong>Sequence</strong> yet, so please
                do set it up and remember to <strong>save</strong> them for the
                next step. Otherwise, press <strong>do not sequence</strong>
              </em>
            </div>
          )}
        </div>

        {currentStep === 1 && (
          <div className="col-span-2">
            {!isAllowToSequence ? (
              <>
                <div
                  style={{
                    textAlign: 'center',
                    marginTop: '20%',
                  }}
                >
                  <div>
                    <div>
                      To use the sequence please visit your profile and link to
                      sequence
                    </div>
                    <div>
                      <Link
                        style={{
                          color: 'blue',
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          fontSize: '14px',
                          marginTop: '10px',
                        }}
                        to={`/settings?sequence=true`}
                      >
                        Go to Sequence Settings
                      </Link>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <StandardBullhornSendEmailModal
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  setConfirmStep={setConfirmStep}
                  setNotDiscard={setNotDiscard}
                  notDiscard={notDiscard}
                  job={{ ...job, state: getValues('state') }}
                  listEmailSend={listEmailSend}
                  setNumberStep={setNumberStep}
                  numberStep={numberStep}
                  inputNumberStep={inputNumberStep}
                  setInputNumberStep={setInputNumberStep}
                  setEmailConfigData={setEmailConfigData}
                  emailConfigData={emailConfigData}
                  onHandleSeqId={handleEmailSeqId}
                  emailSeqId={emailSeqId}
                  newUpdatedSequence={true}
                  setListContactSelected={setListContactSelected}
                  actionKey={3}
                  fromSync={true}
                  isStandardSync={true}
                />
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
export default StandardJobSubmissionModalPage;
