import { LeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Loading from '../HotList/Loading';
import { getJobById } from '../../services/jobs';
import { cleanJobType, stripJobDescription } from '../../utils/common';
import { generateJobSkills } from '../../services/search';
import { getJobFromLocalStorage } from '../SyncVacancyPage';
import { TYPE_ICONS } from '../ManualLeadPage';
import clsx from 'clsx';
import StandardJobSubmissionModalPage from './StandardJobSubmissionModalPage';

export const SyncStandardHeader = () => {
  const { searchId, jobId } = useParams();

  const navigate = useNavigate();
  const back = () => {
    if (window?.history?.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };
  const pathName = window?.location?.pathname?.split('/')?.[1];

  return (
    <div className="relative flex items-center justify-center w-full p-3 rounded-md bg-white mb-2">
      <Button
        icon={<LeftOutlined />}
        className="flex items-center absolute left-5 bg-white"
        onClick={back}
      >
        Back
      </Button>
      <div className="flex items-center justify-center gap-5">
        <Link
          to={`/sync-standard-lead/${searchId}/${jobId}`}
          className={clsx(
            'flex items-center gap-2 px-4 py-2 rounded-md border font-semibold  border-cyan-600 ',
            pathName === 'sync-standard-lead'
              ? 'bg-cyan-600 text-white'
              : 'bg-white text-cyan-600'
          )}
        >
          {TYPE_ICONS.Lead}
          <span>Lead</span>
        </Link>
      </div>
    </div>
  );
};

const SyncStandardLeadPage = () => {
  const navigate = useNavigate();
  const { searchId, jobId } = useParams();

  const [job, setJob] = useState(null);
  const [jobSkills, setJobSkills] = useState([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const getJob = async () => {
    try {
      setLoading(true);
      let jobData = null;
      const jobFromLocalStorage = getJobFromLocalStorage();
      const rawJobId =
        jobFromLocalStorage?.job_id ||
        jobFromLocalStorage?.jobBoardId ||
        jobFromLocalStorage?.id;
      if (rawJobId === jobId) {
        jobData = jobFromLocalStorage;
      } else {
        const encodedJobId = encodeURIComponent(jobId);
        const { data } = await getJobById(encodedJobId);
        jobData = data?.result;
      }

      if (jobData) {
        const jobToSync = {
          ...jobData,
          description: stripJobDescription(jobData?.description),
          source: 'Zileo',
          jobtype: cleanJobType(jobData?.jobtype),
          jobTitle: jobData?.jobtitle,
        };

        setLoading(false);
        setJob({ ...jobToSync });

        if (!jobToSync?.skills || jobToSync?.skills?.length === 0) {
          const { data } = await generateJobSkills(jobToSync?.job_id);
          setJobSkills(data?.result || []);
        }
      } else {
        setLoading(false);
        setErrorMessage('Job was deleted or synced by another user');
      }
    } catch (error) {
      setLoading(false);
      setErrorMessage('Job was deleted or synced by another user');
      console.error(error);
    }
  };

  useEffect(() => {
    getJob();
  }, [jobId]);

  return (
    <div className="rounded-md p-4">
      <SyncStandardHeader />
      <div className="flex items-center justify-center mt-2 min-h-[50vh] bg-white p-3 rounded-md">
        {loading && <Loading />}
        {errorMessage && <p>{errorMessage}</p>}
        {!loading && job && (
          <StandardJobSubmissionModalPage job={job} jobSkills={jobSkills} />
        )}
      </div>
    </div>
  );
};

export default SyncStandardLeadPage;
