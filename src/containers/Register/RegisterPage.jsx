import React from 'react';
import { Col, message, Row } from 'antd';
import RegistrationForm from '../../components/Register/RegistrationForm';
import useRegistration from '../../hooks/useRegisterForm';
import { signUp } from '../../services/auth';
import { useNavigate } from 'react-router-dom';
import { WelcomeBanner } from '../../components/WelcomeBanner/WelcomeBanner';

const RegisterPage = () => {
  const { handleRegistration } = useRegistration(signUp);
  const navigate = useNavigate();
  const onSubmit = async (formData) => {
    try {
      const data = await handleRegistration(formData);
      message.success('Registration success');
      localStorage.setItem('user', JSON.stringify(data));
      localStorage.setItem('role', 'admin');
      navigate('/dashboard');
    } catch (error) {
      message.error(error?.response?.data?.message);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={24} md={12}>
          <WelcomeBanner />
        </Col>
        <Col xs={24} sm={24} md={12}>
          <RegistrationForm onSubmit={onSubmit} />
        </Col>
      </Row>
    </div>
  );
};

export default RegisterPage;
