import React, { useCallback, useEffect, useState } from 'react';
import { TbApi, TbFileInvoice } from 'react-icons/tb';
import { CgProfile } from 'react-icons/cg';
import BullHorn from './BullHorn';
import Profile from './Profile';
import {
  getUserViewAs,
  getUserViewAsBullhorn,
  getUserViewAsRole,
} from '../../helpers/getUserViewAs';
import NoBullhorn from '../NoBullhorn';
import MailBox from './MailBox';
import { Mail, Sequence } from '../../components/Sidebar/consts';
import SequencePage from './Sequence';
import { AiOutlineSchedule } from 'react-icons/ai';
import SchedulingEditor from './SchedulingEditor';
import {
  ApartmentOutlined,
  BankFilled,
  CreditCardOutlined,
  LinkedinOutlined,
} from '@ant-design/icons';
import LinkedIn from './LinkedIn';
import CRMSettingPage from '../CRM/Setting';
import { SALES, SUPER_ADMIN, userRole } from '../../constants/common.constant';
import CreditManagementAdmin from '../page/CreditManagementAdmin';
import CreditManagementCompany from '../page/CreditManagementCompany';
import Account from './Account';
import CRMSettings from './CRMSettings';
import { User, Users } from 'lucide-react';
import UserManagement from '../UserManagement';
import CompanyUsersManagement from '../UserManagement/CompanyUsersManagement';
import SuperAdminUsersManagement from '../UserManagement/SuperAdminUsersManagement';

const TAB_SETTING = {
  BULLHORN: 1,
  PROFILE: 2,
  MAILBOX: 3,
  SEQUENCE: 4,
  // SCHEDULER: 5,
  LINKEDIN: 6,
  CRM: 7,
  CREDITS: 8,
  ACCOUNTS: 9,
  USER_MANAGEMENT: 10,
};

const rawSettingOptions = [
  {
    key: TAB_SETTING.USER_MANAGEMENT,
    label: 'Users',
    icon: <Users className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.BULLHORN,
    label: 'Bullhorn',
    icon: <TbApi className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.PROFILE,
    label: 'Profile',
    icon: <CgProfile className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.MAILBOX,
    label: 'Mailbox',
    icon: <Mail className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.SEQUENCE,
    label: 'Sequence',
    icon: <Sequence className="h-6 w-6" />,
  },
  // {
  //   key: TAB_SETTING.SCHEDULER,
  //   label: 'Scheduler',
  //   icon: <AiOutlineSchedule className="h-6 w-6" />,
  // },
  {
    key: TAB_SETTING.LINKEDIN,
    label: 'LinkedIn',
    icon: <LinkedinOutlined className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.CRM,
    label: 'CRM',
    icon: <ApartmentOutlined className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.CREDITS,
    label: 'Credits',
    icon: <CreditCardOutlined className="h-6 w-6" />,
  },
  {
    key: TAB_SETTING.ACCOUNTS,
    label: 'Accounts',
    icon: <TbFileInvoice className="h-5 w-5" />,
  },
];

const Settings = () => {
  const isBasicUserRole = getUserViewAsRole() === userRole.BASIC_USER;
  const isSuperAdmin =
    getUserViewAsRole() === SALES || getUserViewAsRole() === SUPER_ADMIN;
  const isAdmin = getUserViewAsRole() === userRole.ADMIN;

  const [activeTab, setActiveTab] = useState(isBasicUserRole ? TAB_SETTING.PROFILE : TAB_SETTING.USER_MANAGEMENT);
  const queryParams = new URLSearchParams(location.search);

  const mailbox = queryParams.get('mailbox');
  // const scheduler = queryParams.get('scheduler');
  const sequence = queryParams.get('sequence');
  const showTab = queryParams.get('tab');
  const crm = queryParams.get('crm');

  const isEmptyBullhornKeyByRole =
    !getUserViewAsBullhorn() && getUserViewAsRole() === userRole.BASIC_USER;

  useEffect(() => {
    if (mailbox) {
      setActiveTab(TAB_SETTING.MAILBOX);
    }
  }, [mailbox]);

  // useEffect(() => {
  //   if (scheduler) {
  //     setActiveTab(TAB_SETTING.SCHEDULER);
  //   }
  // }, [scheduler]);

  useEffect(() => {
    if (sequence) {
      setActiveTab(TAB_SETTING.SEQUENCE);
    }
  }, [sequence]);

  useEffect(() => {
    if (showTab && TAB_SETTING[showTab.toUpperCase()]) {
      setActiveTab(TAB_SETTING[showTab.toUpperCase()]);
    }
  }, [showTab]);

  const componentByTabActive = useCallback(() => {
    switch (activeTab) {
      case TAB_SETTING.BULLHORN:
        return <BullHorn />;
      case TAB_SETTING.PROFILE:
        return <Profile />;
      case TAB_SETTING.MAILBOX:
        return <MailBox />;
      case TAB_SETTING.SEQUENCE:
        return <SequencePage />;
      // case TAB_SETTING.SCHEDULER:
      //   return <SchedulingEditor />;
      case TAB_SETTING.LINKEDIN:
        return <LinkedIn userId={getUserViewAs()} />;
      case TAB_SETTING.CRM:
        return <CRMSettings />;
      case TAB_SETTING.CREDITS:
        return (
          <div className="p-2">
            {isSuperAdmin && <CreditManagementAdmin />}
            {isAdmin && <CreditManagementCompany />}
          </div>
        );
      case TAB_SETTING.ACCOUNTS:
        return <Account />;
      case TAB_SETTING.USER_MANAGEMENT:
        return <div className='p-4 bg-gray-50'>
          {isSuperAdmin && <UserManagement />}
          {/* {isSuperAdmin && <SuperAdminUsersManagement />} */}
          {isAdmin && <CompanyUsersManagement />}
        </div>;
      default:
        return <div className="p-2">Select a tab to view settings</div>;
    }
  }, [activeTab]);

  const settingOptions = rawSettingOptions.filter(
    (menu) =>
      menu.key !== TAB_SETTING.BULLHORN ||
      (menu.key === TAB_SETTING.BULLHORN && !isBasicUserRole) ||
      (menu.key === TAB_SETTING.ACCOUNTS && !isBasicUserRole) ||
      (menu.key === TAB_SETTING.USER_MANAGEMENT && !isBasicUserRole)
  );

  return (
    <div className="flex bg-white shadow-md rounded-sm rounded-tl-md rounded-bl-md overflow-hidden">
      <ul className="border-r-2 border-blue-100 w-44">
        {settingOptions.map((menu) => {
          const isActive = activeTab === menu.key;
          const isAllowed =
            menu.key !== TAB_SETTING.CREDITS || isAdmin || isSuperAdmin;

          return (
            isAllowed && (
              <li
                key={menu.key}
                className={`flex items-center gap-4 p-2 px-4 cursor-pointer ${
                  isActive ? 'bg-blue-100' : ''
                }`}
                onClick={() => setActiveTab(menu.key)}
              >
                <i
                  className={`${isActive ? 'text-brand-500' : 'text-gray-600'}`}
                >
                  {menu.icon}
                </i>
                <p
                  className={`leading-1 text-sm font-medium ${
                    isActive ? 'text-navy-700' : 'text-gray-600'
                  }`}
                >
                  {menu.label}
                </p>
              </li>
            )
          );
        })}
      </ul>
      <div className="w-full flex-1 min-h-[500px]">
        {componentByTabActive()}
      </div>
    </div>
  );
};

export default Settings;
