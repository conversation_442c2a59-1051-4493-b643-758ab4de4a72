import React, { useEffect, useState } from 'react';
import { SaveOutlined } from '@ant-design/icons';
import { Button, message, notification } from 'antd';
import { deleteMailBoxId, getListMailBox } from '../../services/mailBox';
import { useNavigate } from 'react-router-dom';
import SignaturesCreateForm from '../../components/BullHorn/SignaturesCreateForm';
import { getUserById } from '../../services/auth';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { getUserViewAs } from '../../helpers/getUserViewAs';

const MailBox = ({ userIdProp = '' }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [isUnlink, setIsUnlink] = useState(null);
  const [authenticationPopup, setAuthenticationPopup] = useState(null);
  const navigate = useNavigate();
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const userToSet = profileUser || profileUserAuth;

  const [isModalOpenSignatures, setIsModalOpenSignatures] = useState(false);

  const apiUrl = import.meta.env.VITE_API_URL;

  const handleGetDataEmail = async () => {
    if (!(userToSet?.user?.id || userToSet?.id)) return;
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await getUserById(
        userIdProp || userToSet?.user?.id || userToSet?.id
      );
      if (data?.grantMailboxId) {
        messageApi.destroy();
        setIsUnlink('UNLINK');
      } else {
        messageApi.destroy();
        setIsUnlink('LINK');
      }
    } catch (e) {
      if (e.response.data.message === 'Not have permission') {
        setIsUnlink('LINK');
      }
      messageApi.destroy();
    }
  };

  useEffect(() => {
    if (!(userToSet?.user?.id || userToSet?.id)) return;
    handleGetDataEmail();
  }, [userToSet]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (!authenticationPopup) {
        timer && clearInterval(timer);
        return;
      }
      if (authenticationPopup.closed) {
        setAuthenticationPopup(null);
        timer && clearInterval(timer);
        handleGetDataEmail();
        // notification.success({
        //   description: 'Mailbox linked.',
        // });
      }
    }, 1000);
  }, [authenticationPopup]);

  const handleDeleteLink = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const { data } = await deleteMailBoxId(
        'grantMailboxId',
        userIdProp || getUserViewAs() || ''
      );
      handleGetDataEmail();
      if (data) {
        messageApi.destroy();
      }
    } catch (e) {
      messageApi.destroy();
    }
  };

  return (
    <div className="p-4 w-full">
      {contextHolder}
      {isUnlink == 'UNLINK' && (
        <div>
          <div>Click here to Unlink Mailbox</div>
          <div style={{ marginTop: '20px' }}>
            <Button onClick={() => handleDeleteLink()} type="primary">
              Unlink Mailbox
            </Button>
          </div>
        </div>
      )}
      {isUnlink === 'LINK' && (
        <div>
          <div>Click here to Link to Mailbox</div>
          <div style={{ marginTop: '20px' }}>
            {/* <a
              href="https://api.eu.nylas.com/v3/connect/auth?client_id=5c4ca094-2de1-4eef-a2e0-5df604aec277&redirect_uri=https%3A%2F%2Fdev.zileo.io%2Fmailbox-callback&access_type=online&response_type=code&state=2886fa5a-4499-4df0-ada3-1e24c22ece1e"
              target="_blank"
              rel="noreferrer"
              
            >
              Link Mailbox
            </a> */}
            <Button
              onClick={() => {
                const popup = window.open(
                  `${apiUrl}/emails/auth-mailbox/user/${userIdProp || getUserViewAs()}`,
                  'authenticationPopup',
                  'width=500,height=500'
                );
                setAuthenticationPopup(popup);
              }}
              type="primary"
            >
              Link Mailbox
            </Button>
          </div>
        </div>
      )}
      <SignaturesCreateForm
        setIsModalOpen={setIsModalOpenSignatures}
        isModalOpen={isModalOpenSignatures}
      />
    </div>
  );
};

export default MailBox;
