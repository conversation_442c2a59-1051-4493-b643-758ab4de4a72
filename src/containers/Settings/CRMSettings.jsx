import { useNavigate } from 'react-router-dom';
import { CRMSettingRoutes } from '../CRM/Setting';
import ContainerCard from '../../components/CRM/ContainerCard';
import { X, Zap } from 'lucide-react';
import { useState } from 'react';
import { Button } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import CRMTagsPage from '../CRM/Tags';
import CRMIndustriesPage from '../CRM/Industries';
import CRMSkillsPage from '../CRM/Skills';

const CRMSettings = () => {
  const [selectedTab, setSelectedTab] = useState(null);

  const routingRenderer = {
    [CRMSettingRoutes.tags.title]: <CRMTagsPage isInSettings />,
    [CRMSettingRoutes.industries.title]: <CRMIndustriesPage isInSettings />,
    [CRMSettingRoutes.skills.title]: <CRMSkillsPage isInSettings />,
  };

  return selectedTab ? (
    <div className="w-full h-full p-4 ">
      <div className="w-full flex justify-between items-center pb-3 border-b">
        <div className="flex items-center space-x-2">
          <div className="relative group">
            <div className="w-7 h-7 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-cyan-500/25 transition-all duration-300">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </div>
          <div className="!text-2xl font-bold gradient-text">ILEO</div>
          <div className="text-2xl font-bold text-gray-700">{selectedTab} Settings</div>
        </div>
        <Button
          onClick={() => setSelectedTab(null)}
          icon={<X />}
          type="text"
          className="bg-white"
        ></Button>
      </div>
      <div className="pt-4">
        {routingRenderer[selectedTab] || <div>Select a tab to view</div>}
      </div>
    </div>
  ) : (
    <div className="w-full h-full rounded-md shadow-md rounded-md p-4 flex flex-col gap-16 items-center justify-center relative">
      <div className="text-4xl font-bold flex items-center justify-center gap-2">
        <div className="flex items-center space-x-2">
          <div className="relative group">
            <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-cyan-500/25 transition-all duration-300">
              <Zap className="w-10 h-10 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
          </div>
          <div className="!text-4xl font-bold gradient-text">ILEO</div>
        </div>
        CRM Settings
      </div>
      <div className="flex items-center justify-center gap-10">
        {Object.entries(CRMSettingRoutes).map(([key, value]) => (
          <ContainerCard
            icon={value.icon}
            title={value.title}
            description={value.description}
            // url={value.path}
            key={key}
            onClick={(tabName) => {
              console.log(`Navigating to ${tabName}`);
              setSelectedTab(tabName);
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default CRMSettings;
