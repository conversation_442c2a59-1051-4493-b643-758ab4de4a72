import { Edit, Plus, Trash2 } from 'lucide-react';
import {
  <PERSON>ge,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  getCardBrandIcon,
  getStatusBadge,
  Separator,
} from './index';
import { Button } from 'antd';
import { v4 } from 'uuid';

const BillingPage = ({ data, companyInfor }) => {
  return (
    <>
      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Payment Methods</CardTitle>
            {/* <Button
              size="sm"
              className="bg-cyan-500 hover:bg-cyan-600 flex items-center text-white font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Card
            </Button> */}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data?.paymentMethods?.map((method) => (
              <div
                key={v4()}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">
                    {getCardBrandIcon(method.brand)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">
                        {method.brand.toUpperCase()} •••• {method.last4}
                      </span>
                      {method.isDefault && (
                        <Badge variant="outline" className="text-xs">
                          Default
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">
                      Expires {method.expiryMonth}/{method.expiryYear}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {/* <Button
                    className="flex items-center"
                    variant="ghost"
                    size="sm"
                  >
                    <Edit className="h-4 w-4" />
                  </Button> */}
                  {/* <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700 flex items-center"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button> */}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Billing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Company Name
                </label>
                <p className="text-sm text-gray-900 mt-1">
                  {companyInfor?.name}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">
                  VAT Number
                </label>
                <p className="text-sm text-gray-900 mt-1">N/a</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">
                Billing Address
              </label>
              <p className="text-sm text-gray-900 mt-1">
                N/a
              </p>
            </div>
            {/* <Button className="flex items-center" variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Update Billing Info
            </Button> */}
          </div>
        </CardContent>
      </Card>
    </>
  );
};
export default BillingPage;
