import { Download, Receipt } from 'lucide-react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  getCardBrandIcon,
  getStatusBadge,
  Separator,
} from './index';
import { Button } from 'antd';

const invoices = [
  {
    id: 'inv_001',
    date: '2024-06-15',
    amount: '£49.99',
    status: 'paid',
    description: 'Professional Plan - Monthly',
    downloadUrl: '#',
  },
];

const InvoicesPage = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Invoice History</CardTitle>
        <CardDescription>
          Download your past invoices and receipts
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {invoices.map((invoice) => (
            <div
              key={invoice.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <Receipt className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="font-medium">{invoice.description}</p>
                  <p className="text-sm text-gray-600">{invoice.date}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className="font-medium">{invoice.amount}</span>
                {getStatusBadge(invoice.status)}
                <Button
                  className="flex items-center gap-1"
                  variant="ghost"
                  size="sm"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
export default InvoicesPage;
