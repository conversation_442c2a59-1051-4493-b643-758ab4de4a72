import { Calendar, CheckCircle, Crown, Edit } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  getStatusBadge,
  Separator,
} from './index';
import { Button } from 'antd';
import dayjs from 'dayjs';
import { currencyFormatter } from '../../../helpers/util';

const SubscriptionPage = ({ data }) => {
  return (
    <>
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-cyan-500" />
              <CardTitle>Current Plan</CardTitle>
            </div>
            {getStatusBadge(data?.status)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{data?.planName}</h3>
                <p className="text-gray-700 flex items-center gap-1 font-medium">
                  {currencyFormatter(data?.currentBillingCycleCost, 'USD')}
                  <span>/</span>
                  {data?.renewInterval}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  {data?.licenseCount} licenses
                </p>
              </div>
              <Button
                className="flex items-center"
                variant="outline"
                size="sm"
                onClick={() => {
                  window.location.href =
                    'mailto:<EMAIL>?subject=Change%20Plan%20Request&body=Hello%20Zileo%20Team,%0D%0A%0D%0AI%20would%20like%20to%20change%20my%20subscription%20plan.%20Please%20contact%20me%20with%20the%20next%20steps.%0D%0A%0D%0AThank%20you.';
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Change Plan
              </Button>
            </div>
            <Separator />
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>
                Next billing date:{' '}
                {dayjs(data?.nextBillingDate).format('HH:mm a, DD-MM-YYYY')}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Plan Features */}
      <Card>
        <CardHeader>
          <CardTitle>Plan Features</CardTitle>
          <CardDescription>
            What's included in your Professional plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Unlimited job searches</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Advanced filtering and analytics</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Email sequences and automation</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Priority customer support</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">API access</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default SubscriptionPage;
