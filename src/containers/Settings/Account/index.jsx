import { <PERSON>ton, Select, Spin } from 'antd';
import {
  Download,
  Calendar,
  CheckCircle,
  Mail,
  Crown,
  Receipt,
  Plus,
  Edit,
  Trash2,
  ArrowRightFromLineIcon,
  ArrowRight,
  ArrowLeft,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import SubscriptionPage from './Subscription';
import BillingPage from './Billing';
import InvoicesPage from './Invoices';
import {
  getUserViewAsOrganizationId,
  getUserViewAsRole,
} from '../../../helpers/getUserViewAs';
import { SALES, SUPER_ADMIN } from '../../../constants/common.constant';
import { useQuery } from '@tanstack/react-query';
import {
  getSubscriptionsDashboard,
  getUserOrganizations,
} from '../../../services/users';
import dayjs from 'dayjs';
import { currencyFormatter } from '../../../helpers/util';

// Dummy <PERSON>ge, Card, CardHeader, CardTitle, CardContent, CardDescription, Separator components for JSX
export const Badge = ({ children, className = '', variant = '' }) => (
  <span
    className={`inline-block px-2 py-1 rounded text-xs font-semibold border ${className}`}
  >
    {children}
  </span>
);
export const Card = ({ children }) => (
  <div className="bg-white rounded-lg shadow p-6">{children}</div>
);
export const CardHeader = ({ children }) => (
  <div className="mb-4">{children}</div>
);
export const CardTitle = ({ children, className = '' }) => (
  <h2 className={`text-lg font-semibold ${className}`}>{children}</h2>
);
export const CardContent = ({ children }) => <div>{children}</div>;
export const CardDescription = ({ children }) => (
  <p className="text-gray-500 text-sm mt-1">{children}</p>
);
export const Separator = () => <hr className="my-4 border-gray-200" />;

export const getCardBrandIcon = (brand) => {
  switch (brand.toLowerCase()) {
    case 'visa':
      return '💳';
    case 'mastercard':
      return '💳';
    case 'amex':
      return '💳';
    default:
      return '💳';
  }
};

export const getStatusBadge = (status) => {
  switch (status) {
    case 'active':
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          Active
        </Badge>
      );
    case 'paid':
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          Paid
        </Badge>
      );
    case 'pending':
      return (
        <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
          Pending
        </Badge>
      );
    case 'failed':
      return (
        <Badge className="bg-red-100 text-red-800 border-red-200">Failed</Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};
export default function AccountsPage() {
  const [activeTab, setActiveTab] = useState('subscription');
  const [selectingCompany, setSelectingCompany] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);

  const [accountData, setAccountData] = useState(null);
  const [loading, setLoading] = useState(true);

  const currentUserRole = getUserViewAsRole();
  const userOrgId = getUserViewAsOrganizationId();
  const isSuperAdmin =
    currentUserRole === SUPER_ADMIN || currentUserRole === SALES; // SUPER ADMIN and SALES are roles that can view all accounts

  const { data: listOrganization = [], isFetching } = useQuery(
    ['LIST_ORGANIZATION'],
    {
      queryFn: async () => {
        if (currentUserRole === SUPER_ADMIN) {
          const { data } = await getUserOrganizations();
          return data.result;
        }
        return [];
      },
    }
  );

  const getSubscriptionData = async () => {
    try {
      setLoading(true);
      const { data } = await getSubscriptionsDashboard(selectedCompany?.id);
      if (data?.result) {
        setAccountData(data?.result);
      }
      setLoading(false);
      console.log('Subscription Data:', data);
    } catch (error) {
      setLoading(false);
      console.log('Subscription error:', error);
    }
  };

  const listOriginConfig = listOrganization.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });

  const userOrg = listOrganization.find((org) => org.id === userOrgId);

  const accountPageRenderer = {
    subscription: <SubscriptionPage data={accountData?.subscription} />,
    billing: (
      <BillingPage
        data={accountData?.billing}
        companyInfor={accountData?.organization}
      />
    ),
    invoices: <InvoicesPage data={accountData} />,
  };

  useEffect(() => {
    getSubscriptionData();
  }, [selectedCompany?.id]);

  return (
    <div className="bg-gray-50 h-full">
      <>
        {((selectedCompany && isSuperAdmin) || !isSuperAdmin) && (
          <div className="p-8">
            {/* Header */}
            <div className="mb-8">
              {isSuperAdmin && (
                <div className="flex items-center gap-2 mb-4">
                  <Button
                    size="small"
                    className="bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium"
                    // type="primary"
                    onClick={() => setSelectedCompany(null)}
                    disabled={!selectingCompany}
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-700 font-medium">
                    Viewing as: {selectedCompany.name}
                  </span>
                  <Crown className="h-5 w-5 text-yellow-500" />
                </div>
              )}
              <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                Account Settings
              </h1>
              <p className="text-gray-600">
                Manage your subscription, billing, and account preferences
              </p>
            </div>
            {!loading && (
              <>
                {/* Navigation Tabs */}
                <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg w-fit">
                  <button
                    onClick={() => setActiveTab('subscription')}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'subscription'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Subscription
                  </button>
                  <button
                    onClick={() => setActiveTab('billing')}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'billing'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Billing
                  </button>
                  <button
                    onClick={() => setActiveTab('invoices')}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === 'invoices'
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    Invoices
                  </button>
                </div>

                {/* Content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Content */}
                  <div className="lg:col-span-2 space-y-6">
                    {accountPageRenderer[activeTab] || <div>Select a tab</div>}
                  </div>

                  {/* Sidebar */}
                  <div className="space-y-6">
                    {/* Quick Stats */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">
                          Account Overview
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Plan</span>
                            <span className="font-medium">
                              {accountData?.subscription?.planName || 'N/A'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Status
                            </span>
                            {getStatusBadge(accountData?.subscription?.status)}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Next billing
                            </span>
                            <span className="font-medium">
                              {dayjs(
                                accountData?.subscription?.nextBillingDate
                              ).format('HH:mm a, DD-MM-YYYY')}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Amount
                            </span>
                            <span className="font-medium">
                              {currencyFormatter(
                                accountData?.subscription
                                  ?.currentBillingCycleCost,
                                'USD'
                              )}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Licenses
                            </span>
                            <span className="font-medium">
                              {accountData?.subscription?.licenseCount}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Support */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Need Help?</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <Button
                            variant="outline"
                            className="w-full justify-start flex items-center justify-center"
                            onClick={() => {
                              window.location.href =
                                'mailto:<EMAIL>?subject=Support%20Request&body=Hello%20Zileo%20Team,%0D%0A%0D%0AThank%20you.';
                            }}
                          >
                            <Mail className="h-4 w-4 mr-2" />
                            Contact Support
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </>
            )}
            {loading && (
              <div className="flex items-center justify-center h-64 gap-1">
                <span className="text-gray-500">Loading account data...</span>
                <Spin size="small" />
              </div>
            )}
          </div>
        )}
        {!selectedCompany && isSuperAdmin && (
          <div className="p-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-4">
              Select a Company
            </h1>
            <p className="text-gray-600 mb-6">
              Please select a company to manage its account settings.
            </p>
            {/* Placeholder for company selection logic */}
            <div className="flex items-center gap-3">
              <Select
                loading={isFetching}
                className="flex items-center max-w-96 w-full h-[2.45rem]"
                value={selectingCompany?.id || null}
                placeholder={'Select a company'}
                options={listOriginConfig}
                onSelect={(value) => {
                  const selectedCompanyTemp = listOrganization.find(
                    (co) => co.id === value
                  );
                  setSelectingCompany(selectedCompanyTemp);
                }}
              />
              <Button
                className="bg-cyan-500 hover:bg-cyan-600 flex items-center gap-2 text-white font-medium"
                // type="primary"
                onClick={() => {
                  setSelectedCompany(selectingCompany)
                  setActiveTab('subscription');
                }}
                disabled={!selectingCompany}
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </>
    </div>
  );
}
