import { SaveOutlined } from '@ant-design/icons';
import { Button, Form, Input, notification } from 'antd';
import _get from 'lodash/get';
import React, { useEffect, useState } from 'react';
import {
  getUserViewAsBullhorn,
  getUserViewAsRole,
  getUserViewAs,
  isViewAs,
  getUserViewAsLicense,
} from '../../helpers/getUserViewAs';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import {
  getUserBullhornConfig,
  getUserDetail,
  updateUserBullhornConfig,
} from '../../services/users';
import { licenseType, userRole } from '../../constants/common.constant';

const BullHorn = () => {
  const { setViewAs } = useViewAs();
  const { setAuth } = useAuth();
  const currentUserPlan = getUserViewAsLicense();
  const isStandardUser = currentUserPlan === licenseType.STANDARD;
  const [loading, setLoading] = useState(false);

  const isUserRole = getUserViewAsRole() === userRole.BASIC_USER;

  const [form] = Form.useForm();

  const handleGetDataConfig = async () => {
    setLoading(true);
    const { data } = await getUserBullhornConfig();
    form.setFieldValue(
      'username',
      data?.result?.user?.organization?.bhUsername
    );
    form.setFieldValue(
      'clientId',
      data?.result?.user?.organization?.bhClientId
    );
    form.setFieldValue(
      'clientSecret',
      data?.result?.user?.organization?.bhClientSecret
    );
    form.setFieldValue(
      'password',
      data?.result?.user?.organization?.bhPassword
    );
    if (data?.result?.user?.organizationId) {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGetDataConfig();
  }, []);

  const onFinish = async (values) => {
    try {
      const response = await updateUserBullhornConfig({
        clientId: values.clientId,
        clientSecret: values.clientSecret,
        username: values.username,
        password: values.password,
      });

      if (_get(response, 'data.success')) {
        notification.open({
          message: 'Success!',
          description: 'Changed Bullhorn key successfully!',
        });
        await handleGetDataConfig();
      } else {
        notification.error({
          message: 'Error!',
          description: 'Changed Bullhorn key failed!',
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error!',
        description: 'Changed Bullhorn key failed!',
      });
    }
  };

  return (
    <div className="p-4 w-full">
      <div className="flex justify-between items-center">
        <h4 className="font-bold">Bullhorn settings</h4>
      </div>
      <div className="my-4">
        <Form
          form={form}
          name="basic"
          labelCol={{
            span: 4,
          }}
          wrapperCol={{
            span: 20,
          }}
          style={{
            maxWidth: 700,
          }}
          layout="horizontal"
          labelAlign="left"
          autoComplete="off"
          onFinish={onFinish}
        >
          <Form.Item
            label="Client Id"
            name="clientId"
            rules={[
              {
                required: true,
                message: 'Please input your Client Id',
              },
            ]}
          >
            <Input
              autoComplete={false}
              disabled={isUserRole || loading || isStandardUser}
            />
          </Form.Item>
          <Form.Item
            label="Username"
            name="username"
            rules={[
              {
                required: true,
                message: 'Please input your Username',
              },
            ]}
          >
            <Input
              autoComplete={false}
              disabled={isUserRole || loading || isStandardUser}
            />
          </Form.Item>
          <Form.Item
            label="Password"
            name="password"
            rules={[
              {
                required: true,
                message: 'Please input your Password',
              },
            ]}
          >
            <Input.Password
              autoComplete={false}
              disabled={isUserRole || loading || isStandardUser}
            />
          </Form.Item>
          <Form.Item
            label="Client Secret"
            name="clientSecret"
            rules={[
              {
                required: true,
                message: 'Please input your Client Secret',
              },
            ]}
          >
            <Input.Password
              autoComplete={false}
              disabled={isUserRole || loading || isStandardUser}
            />
          </Form.Item>
        </Form>
      </div>
      <div className="flex justify-between items-center">
        <Button
          disabled={isUserRole || isStandardUser}
          loading={loading}
          type="primary"
          onClick={() => form.submit()}
          icon={<SaveOutlined />}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default BullHorn;
