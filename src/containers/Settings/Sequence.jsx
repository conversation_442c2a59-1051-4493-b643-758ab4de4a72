import React, { useEffect, useState } from 'react';
import { Button, message, notification } from 'antd';
import { deleteMailBoxId } from '../../services/mailBox';
import { useAuth } from '../../store/auth';
import { getUserById } from '../../services/auth';
import { useViewAs } from '../../store/viewAs';
import { getUserViewAs, isViewAs } from '../../helpers/getUserViewAs';
import { unlinkGrantUnipile } from '../../services/users';
import SequenceSettings from '../Sequence/SequenceSettings';

const Sequence = ({ userIdProp = '' }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [authenticationPopup, setAuthenticationPopup] = useState(null);
  const [isUnlink, setIsUnlink] = useState(null);
  const { profileUser, setViewAs } = useViewAs();
  const { profile: profileUserAuth, setAuth } = useAuth();
  const userToSet = profileUser || profileUserAuth;

  const apiUrl = import.meta.env.VITE_API_URL;

  const handleGetDataEmail = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });

    try {
      console.log('userToSet: ', userToSet);
      const userId = userIdProp || userToSet?.id || userToSet?.user?.id;
      const { data } = await getUserById(userId);

      if (data?.grantUnipileId) {
        messageApi.destroy();
        setIsUnlink('UNLINK');
      } else {
        messageApi.destroy();
        setIsUnlink('LINK');
      }

      if (userId === profileUserAuth?.user?.id || userId === profileUser?.id) {
        isViewAs()
          ? setViewAs({ profileUser: data })
          : setAuth({ profile: { user: data } });
      }
    } catch (error) {
      messageApi.destroy();
    }
  };

  useEffect(() => {
    if (!userToSet) return;
    handleGetDataEmail();
  }, [userIdProp]);

  const handleUnlinkGrantUnipile = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    try {
      const userId = userIdProp || userToSet?.id || userToSet?.user?.id;
      const { data } = await unlinkGrantUnipile(userId);
      if (data) {
        messageApi.destroy();
        handleGetDataEmail();
      }
    } catch (e) {
      notification.error({
        description: '',
      });
      messageApi.destroy();
    }
  };

  useEffect(() => {
    const timer = setInterval(() => {
      if (!authenticationPopup) {
        timer && clearInterval(timer);
        return;
      }
      if (authenticationPopup.closed) {
        setAuthenticationPopup(null);
        timer && clearInterval(timer);
        handleGetDataEmail();
      }
    }, 1000);
  }, [authenticationPopup]);

  return (
    <div className="p-4 w-full">
      {contextHolder}
      {isUnlink == 'UNLINK' && (
        <div>
          <div>Click here to unlink Sequence</div>
          <div style={{ marginTop: '20px' }}>
            <Button onClick={() => handleUnlinkGrantUnipile()} type="primary">
              Unlink Sequence
            </Button>
          </div>
        </div>
      )}
      {isUnlink === 'LINK' && (
        <div>
          <div>Click here to link Sequence</div>
          <div style={{ marginTop: '20px' }}>
            <Button
              onClick={() => {
                const popup = window.open(
                  `${apiUrl}/users/unipile/${userIdProp || getUserViewAs()}/link`,
                  'authenticationPopup',
                  'width=500,height=500'
                );
                setAuthenticationPopup(popup);
              }}
              type="primary"
            >
              Link Sequence
            </Button>
          </div>
        </div>
      )}
      <div className='w-full border-b py-2' />
      <div className="pt-3 border-t ">
        <SequenceSettings />
      </div>
    </div>
  );
};

export default Sequence;
