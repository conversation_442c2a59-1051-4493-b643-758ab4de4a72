import React, { useEffect } from 'react';
import { NylasSchedulerEditor } from '@nylas/react';
import {
  NYLAS_CLIENT_ID,
  NYLAS_API_VERSION,
  NYLAS_API_URL,
  getNylasSchedulingConfigs,
  getCalendars,
  createNewEvent,
  getDetailNylasSchedulingConfig,
  deleteNylasSchedulingConfig,
  updateNylasSchedulingConfig,
} from '../../services/nylas';
import { getUserViewAs } from '../../helpers/getUserViewAs';

import zileoLogo from '../../assets/img/welcome/logo.png';

import { NylasIdentityRequestWrapper } from '@nylas/react';
import { Button, Image, message } from 'antd';
import { useState } from 'react';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { getUserById } from '../../services/auth';
import { LinkOutlined } from '@ant-design/icons';

const nylasExecuters = (payload = null, configurationId = '') => {
  return {
    GET: !configurationId
      ? getNylasSchedulingConfigs().then((res) => {
          return res?.data?.result;
        })
      : getDetailNylasSchedulingConfig(configurationId).then((res) => {
          return res?.data?.result;
        }),
    POST: createNewEvent(payload).then((res) => {
      return res?.data?.result;
    }),
  };
};

const SchedulingEditor = (props) => {
  const apiUrl = import.meta.env.VITE_API_URL;
  const { redirectUri } = props;

  const [messageApi, contextHolder] = message.useMessage();
  const [authenticationPopup, setAuthenticationPopup] = useState(null);
  const [isAuth, setAuth] = useState(false);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const userToSet = profileUser || profileUserAuth;

  const hideNylaslogoutBtn = () => {
    if (!document) return;
    const style = document.createElement('style');
    const host = document?.getElementsByClassName(
      'scheduling-editor-container'
    );
    style.innerHTML = `.scheduler-editor-close {  display: none !important;}`;
    if (host[0]) {
      host[0]?.shadowRoot?.appendChild(style);
    }
  };

  const identity = {
    clientId: NYLAS_CLIENT_ID, // Replace with your Nylas client ID
    redirectUri: redirectUri
      ? redirectUri
      : `${window.location.origin}/settings`,
    domain: `${NYLAS_API_URL}${NYLAS_API_VERSION}`, // or 'https://api.eu.nylas.com/v3' for EU data center
    hosted: true,
    accessType: 'offline',
    multiAccount: false,
    isMultiAccount: () => true,
    getProfile: () => ({}),
    fetch: async (endpoint, payload) => {
      const splittedEndpoint = endpoint.split('/');
      const configurationId = splittedEndpoint[4];
      const body = payload.body;
      const getExecuter = payload.method;

      // get calendars: grants/me/calendars --
      if (endpoint === 'grants/me/calendars') {
        return await getCalendars().then((res) => {
          console.log('calendars: ', res?.data);
          return res?.data?.result;
        });
      }

      if (getExecuter === 'DELETE') {
        if (!configurationId) return;
        return await deleteNylasSchedulingConfig(configurationId).then(
          (res) => {
            return res?.data?.result;
          }
        );
      }

      if (getExecuter === 'PUT') {
        if (!body || !configurationId) return;
        return await updateNylasSchedulingConfig(body, configurationId)
          .then((res) => {
            console.log('updateNylasSchedulingConfig: ', res?.data);
            return res?.data?.result;
          })
          .catch((err) => {
            console.log('getNylasSchedulingConfigs err: ', err);
            // return err;
          });
      }

      const execute = nylasExecuters(body, configurationId)[getExecuter];
      // get all: grants/me/scheduling/configurations --
      // payload.method === 'GET'

      // create new: grants/me/scheduling/configurations
      // payload.method === 'POST'
      // data === payload.body

      // update: grants/me/scheduling/configurations/2ded393e-cb45-4f60-87cf-00ee4d6bbc09
      // payload.method === 'PUT'
      // data === payload.body

      // update: grants/me/scheduling/configurations/2ded393e-cb45-4f60-87cf-00ee4d6bbc09 --
      // payload.method === 'DELETE'
      // data === payload.body === undefined

      return await execute;
    },
    getCalendar: async (e) => console.log('getCalendar: ', e),
  };
  const nylasApiRequest = new NylasIdentityRequestWrapper(identity); // Create a new nylasApiRequest instance

  const handleGetDataEmail = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });

    try {
      const { data } = await getUserById(userToSet?.id || userToSet?.user?.id);
      if (data?.grantId || data?.grantUnipileId) {
        messageApi.destroy();
        setAuth(true);
      } else {
        messageApi.destroy();
        setAuth(false);
      }
    } catch (error) {
      setAuth(false);
      messageApi.destroy();
    }
  };

  useEffect(() => {
    if (!userToSet) return;
    handleGetDataEmail();
  }, []);

  useEffect(() => {
    hideNylaslogoutBtn();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      if (!authenticationPopup) {
        timer && clearInterval(timer);
        return;
      }
      if (authenticationPopup.closed) {
        setAuthenticationPopup(null);
        timer && clearInterval(timer);
        handleGetDataEmail();
      }
    }, 1000);
  }, [authenticationPopup]);

  return (
    <>
      {contextHolder}
      {isAuth ? (
        <NylasSchedulerEditor
          className="scheduling-editor-container"
          schedulerPreviewLink={`${window.location.origin}/schedule?config_id={config.id}`}
          nylasApiRequest={nylasApiRequest}
          defaultSchedulerConfigState={{
            selectedConfiguration: {
              requires_session_auth: false, // Set to 'false' to create a public configuration
            },
          }}
        />
      ) : (
        <div className="w-full flex-col gap-3 flex h-full justify-center items-center">
          <Image height={200} preview={false} src={zileoLogo} />
          <div className="font-medium text-[#8b8a82] italic text-lg">
            Sorry! To use this feature, you have to link your mail account
            first.
          </div>
          <div>
            <Button
              onClick={() => {
                const popup = window.open(
                  `${apiUrl}/emails/auth?id=${getUserViewAs()}`,
                  'authenticationPopup',
                  'width=500,height=500'
                );
                setAuthenticationPopup(popup);
              }}
              type="primary"
              icon={<LinkOutlined />}
            >
              Link to your Mail Account.
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default SchedulingEditor;
