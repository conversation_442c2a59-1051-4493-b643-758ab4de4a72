import {
  CheckOutlined,
  CompassOutlined,
  ContactsOutlined,
  DeleteOutlined,
  EditOutlined,
  FileDoneOutlined,
  FundViewOutlined,
  KeyOutlined,
  LeftOutlined,
  LinkedinFilled,
  LinkedinOutlined,
  MailOutlined,
  RightOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Form,
  Image,
  Input,
  Modal,
  Select,
  Statistic,
  Steps,
  message,
  notification,
} from 'antd';
import _get from 'lodash/get';
import React, { useEffect, useState } from 'react';
import {
  getUserViewAs,
  getUserViewAsRole,
  isViewAs,
} from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import { useForm, Controller } from 'react-hook-form';
import {
  getUnipileProfile,
  linkedInConnections,
  reConnectedAccount,
  resendUnipileCheckpoint,
  resolveUnipileCheckpoint,
  retrieveAccount,
  unLinkedInConnections,
  updateUniplileAccountInApp,
} from '../../services/users';
import { getDataUniplieAccount, getUserById } from '../../services/auth';
import ArkoseCaptchaIntegration from './ArkoseCaptchaIntegration';
import { useViewAs } from '../../store/viewAs';
import dayjs from 'dayjs';
import CopyToClipboard from 'react-copy-to-clipboard';
import clsx from 'clsx';

import rocketImage from '../../assets/img/pricing-plan-enterprise.png';
import { ISO3166A2, userRole } from '../../constants/common.constant';
import { useCookies } from 'react-cookie';

const COLOR_STATUS = {
  OK: 'rgb(68, 183, 0)',
  CREDENTIALS: '#c0201e',
  CONNECTING: '#0891b2',
};

const TEXT_STATUS = {
  OK: 'Connected',
  CREDENTIALS: 'Disconnected',
  CONNECTING: 'Connecting',
};

const BADGE_STATUS = {
  OK: 'success',
  CREDENTIALS: 'error',
  CONNECTING: 'processing',
};

export const LINKEDIN_HELPER_COOKIES_NAMES = [
  'zl_atoken',
  'zl_ptoken',
  'zl_pidentifier',
  'zl_latest_connect',
];

const LinkedIn = ({ userIdProp }) => {
  const [cookies, setCookie] = useCookies(LINKEDIN_HELPER_COOKIES_NAMES);

  const { control, handleSubmit, setValue, getValues, reset } = useForm();
  const { setAuth, profile } = useAuth();
  const { profileUser, setViewAs } = useViewAs();

  const userToSet = profileUser || profile;
  const userId = userIdProp || userToSet?.user?.id || userToSet?.id;
  const [showData, setShowData] = useState(false);

  const { Countdown } = Statistic;

  const [loading, setLoading] = useState(false);
  const [openDeleteLinked, setOpenDeleteLinked] = useState(false);
  const [openEditLinked, setOpenEditLinked] = useState(false);
  const [viewScreen, setViewScreen] = useState(false);
  const [handleCheckPoint, setHandleCheckPoint] = useState(false);
  const [handleCheckCaptcha, setHandleCheckCaptcha] = useState(false);
  const [handleCheck2FAApp, setHandleCheck2FAApp] = useState(false);
  const [handleCheckSMS, setHandleCheckSMS] = useState(false);
  const [handleCheckInApp, setHandleCheckInApp] = useState(false);
  const [startCheckAccount, setStartCheckAccount] = useState(false);
  const [startInsertAccount, setStartInsertAccount] = useState(false);
  const [linkedInAccountInfo, setLinkedAccountInfo] = useState();
  const [accountId, setAccountId] = useState();
  const [accountStatus, setAccountStatus] = useState();
  const [loadingAccount, setLoadingAccount] = useState(false);
  const [loadingResend, setLoadingResend] = useState(false);
  const [loadedNumber, setLoadedNumber] = useState(0);
  const [loadingReconnect, setLoadingReconnect] = useState(false);
  const [captchaData, setCaptchaData] = useState({
    publicKey: null,
    data: null,
  });
  const [otpCode, setOtpCode] = useState();

  const [messageApi, contextHolder] = message.useMessage();

  const isUserRole = getUserViewAsRole() === userRole.BASIC_USER;

  const [form] = Form.useForm();

  // Linkedin Steps
  const [current, setCurrent] = useState(0);
  const next = () => {
    setCurrent(current + 1);
  };
  const prev = () => {
    setCurrent(current - 1);
  };

  // guide modal
  const [openGuideModal, setOpenGuideModal] = useState(false);

  const showGuide = () => setOpenGuideModal(true);
  const closeGuide = () => setOpenGuideModal(false);

  useEffect(() => {
    handleGetDataEmail();
    return () => reset();
  }, []);

  const handleGetDataEmail = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading ...',
      duration: 0,
    });
    if (cookies['zl_ptoken']) {
      setValue('li_a', cookies['zl_ptoken']);
    }
    if (cookies['zl_atoken']) {
      setValue('li_at', cookies['zl_atoken']);
    }
    try {
      setViewScreen(false);
      const { data } = await getDataUniplieAccount(userId);
      if (data.result.data.sources[0]?.status === 'OK') {
        setAccountStatus('OK');
        setLinkedAccountInfo(data.result.data);
        messageApi.destroy();
        setShowData(true);
        setViewScreen(true);
        if (isViewAs()) {
          setViewAs({
            profileUser: {
              ...profileUser,
              unipileAccountId: data?.result?.data?.id,
              unipileAccountStatus: 'CONNECTED',
              linkedinAvatarUrl:
                data?.result?.data?.dataLinkedInProfile?.profile_picture_url,
              linkedInCountryCode: data?.result?.data?.linkedInCountryCode,
              linkedinPublicIdentifier:
                data?.result?.data?.dataLinkedInProfile?.public_identifier,
            },
          });
        } else {
          setAuth({
            profile: {
              ...profile,
              user: {
                ...profile?.user,
                unipileAccountId: data?.result?.data?.id,
                unipileAccountStatus: 'CONNECTED',
                linkedinAvatarUrl:
                  data?.result?.data?.dataLinkedInProfile?.profile_picture_url,
                linkedInCountryCode: data?.result?.data?.linkedInCountryCode,
                linkedinPublicIdentifier:
                  data?.result?.data?.dataLinkedInProfile?.public_identifier,
              },
            },
          });
        }
      } else if (data.result.data.sources[0]?.status === 'CREDENTIALS') {
        setLinkedAccountInfo(data.result.data);
        setAccountStatus('CREDENTIALS');
        setShowData(true);
        setViewScreen(true);
        await actionUpdateStateLinkedIn();
      } else if (data.result.data.sources[0]?.status === 'CONNECTING') {
        setLinkedAccountInfo(data.result.data);
        setAccountStatus('CONNECTING');
        setShowData(true);
        setViewScreen(true);
        await actionUpdateStateLinkedIn();
      } else {
        setShowData(false);
        setViewScreen(true);
        await actionUpdateStateLinkedIn();
      }
    } catch (e) {
      // if (e?.response?.data?.message === 'Not have permission') {
      //   setShowData(false);
      // }
      setShowData(false);
      setViewScreen(true);
      if (isViewAs()) {
        setViewAs({
          profileUser: {
            ...profileUser,
            unipileAccountId: null,
            unipileAccountStatus: 'DISCONNECTED',
            linkedinPublicIdentifier: "",
          },
        });
      } else {
        setAuth({
          profile: {
            ...profile,
            user: {
              ...profile?.user,
              unipileAccountId: null,
              unipileAccountStatus: 'DISCONNECTED',
              linkedinPublicIdentifier: "",
            },
          },
        });
      }
      messageApi.destroy();
    }
  };

  const actionUpdateStateLinkedIn = async () => {
    messageApi.destroy();

    if (isViewAs()) {
      setViewAs({
        profileUser: {
          ...profileUser,
          unipileAccountId: null,
          unipileAccountStatus: 'DISCONNECTED',
          linkedinPublicIdentifier: "",
        },
      });
    } else {
      setAuth({
        profile: {
          ...profile,
          user: {
            ...profile?.user,
            unipileAccountId: null,
            unipileAccountStatus: 'DISCONNECTED',
            linkedinPublicIdentifier: "",
          },
        },
      });
    }
  };

  const handleCancelOtp = async () => {
    setHandleCheckPoint(false);
    await handleGetDataEmail();
    setOpenEditLinked(false);
    setHandleCheck2FAApp(false);
    setHandleCheckSMS(false);
  };

  const handleCancelCaptcha = async () => {
    setHandleCheckPoint(false);
    setHandleCheckCaptcha(false);
    await handleGetDataEmail();
    setOpenEditLinked(false);
    setCaptchaData({
      publicKey: null,
      data: null,
    });
  };

  const handleAccessWithOtp = async () => {
    try {
      if (!otpCode || otpCode.trim() === '') {
        notification.error({
          message: 'Error!',
          description: 'Please input your code',
        });
        return;
      }
      const payload = {
        provider: 'LINKEDIN',
        account_id: accountId,
        code: otpCode,
      };
      setLoadingAccount(true);
      const { data } = await resolveUnipileCheckpoint(payload, userId);
      setLoadingAccount(false);
      if (data) {
        notification.success({
          message: 'Success',
          description: 'Access LinkedIn successfully!',
        });
        setHandleCheckPoint(false);
        await handleGetDataEmail();
        setOpenEditLinked(false);
        setHandleCheck2FAApp(false);
        setHandleCheckSMS(false);
      }
    } catch (e) {
      notification.error({
        message: 'Error!',
        description: 'Failed to access LinkedIn!',
      });
      setLoadingAccount(false);
    }
  };

  const handleLoaded = () => {
    console.log('Captcha loaded');
  };

  useEffect(() => {
    const callApi = async () => {
      if (!startCheckAccount || !accountId) return;

      try {
        const data = await retrieveAccount(accountId);
        if (data) {
          notification.success({
            message: 'Success',
            description: 'Access LinkedIn successfully!',
          });
          await actionInsertAccount();
          setOpenEditLinked(false);
          setHandleCheckInApp(false);
          setHandleCheckPoint(false);
          setStartInsertAccount(true);
          await handleGetDataEmail();
        } else {
          setTimeout(callApi, 2000);
        }
      } catch (error) {
        console.error('API call failed:', error);
        setTimeout(callApi, 2000);
      }
    };

    if (startCheckAccount) {
      callApi();
    }

    return () => setStartCheckAccount(false);
  }, [startCheckAccount]);

  const actionInsertAccount = async () => {
    try {
      const payload = {
        account_id: accountId,
      };

      const { data } = await updateUniplileAccountInApp(payload, userId);
      if (data) {
        notification.success({
          message: 'Success',
          description: 'Access LinkedIn successfully!',
        });
      }
    } catch (e) {
      notification.error({
        message: 'Error!',
        description: 'Failed to access LinkedIn!',
      });
    }
  };

  //  useEffect(() => {
  //   if(startCheckAccount) {
  //     actionInsertAccount();
  //     setStartInsertAccount(false)
  //   }
  //  }, [startInsertAccount])

  const handleSuccess = async (token) => {
    try {
      const payload = {
        provider: 'LINKEDIN',
        account_id: accountId,
        code: token,
      };
      setLoadingAccount(true);
      const { data } = await resolveUnipileCheckpoint(payload, userId);
      setLoadingAccount(false);
      if (data?.result?.data?.checkpoint?.type == 'CAPTCHA') {
        setCaptchaData({
          data: data?.result?.data?.checkpoint?.data,
          publicKey: data?.result?.data?.checkpoint?.public_key,
        });
        setHandleCheckCaptcha(true);
        setLoading(false);
        setOpenEditLinked(false);
        setLoadedNumber(loadedNumber + 1);
      } else if (
        data?.result?.data?.checkpoint?.type === '2FA' &&
        data?.result?.data?.checkpoint?.source === 'APP'
      ) {
        setHandleCheckCaptcha(false);
        setHandleCheck2FAApp(true);
        setAccountId(data?.result?.data?.account_id);
        notification.warning({
          message: 'Warning!',
          description: 'Please check your 2FA and enter the code',
        });
        setLoading(false);
        setOpenEditLinked(false);
        return;
      } else if (data?.result?.data?.checkpoint?.type === 'IN_APP_VALIDATION') {
        setHandleCheckCaptcha(false);
        setAccountId(data?.result?.data?.account_id);
        setStartCheckAccount(true);
        setHandleCheckInApp(true);
        setLoading(false);
        setOpenEditLinked(false);
        return;
      } else if (
        data?.result?.data?.checkpoint?.type === '2FA' &&
        data?.result?.data?.checkpoint?.source === 'SMS'
      ) {
        setHandleCheckCaptcha(false);
        setHandleCheckSMS(true);
        setAccountId(data?.result?.data?.account_id);
        setLoading(false);
        setOpenEditLinked(false);
        return;
      } else {
        notification.success({
          message: 'Success',
          description: 'Access LinkedIn with Captcha successfully!',
        });
        setHandleCheckCaptcha(false);
        setHandleCheckPoint(false);
        await handleGetDataEmail();
        setOpenEditLinked(false);
      }
    } catch (e) {
      notification.error({
        message: 'Error!',
        description: 'Failed to access LinkedIn with Captcha!',
      });
      setHandleCheckPoint(false);
      await handleGetDataEmail();
      setOpenEditLinked(false);
      setLoadingAccount(false);
      setHandleCheckSMS(false);
    }
  };

  const handleError = () => {
    console.log('Captcha error');
  };

  const handleResendOtp = async () => {
    try {
      const payload = {
        provider: 'LINKEDIN',
        account_id: accountId,
      };
      setLoadingResend(true);
      const { data } = await resendUnipileCheckpoint(payload, userId);
      setLoadingResend(false);
      if (data) {
        notification.success({
          message: 'Success',
          description: 'Please check your email, we have sent you the OTP code',
        });
      }
    } catch (e) {
      notification.error({
        message: 'Error!',
        description: 'Failed to sent OTP',
      });
      setLoadingResend(false);
    }
  };

  const handleDeleteLink = async () => {
    try {
      setLoading(true);
      const { data } = await unLinkedInConnections(userId);

      if (data) {
        notification.success({
          message: 'Success!',
          description: 'UnLinkedIn Connection successfully!',
        });
        setLoading(false);
        await handleGetDataEmail();
      } else {
        notification.error({
          message: 'Error!',
          description: 'UnLinkedIn Connection failed!',
        });
        setLoading(false);
      }
      setOpenDeleteLinked(false);
    } catch (error) {
      notification.error({
        message: 'Error!',
        description: 'UnLinkedIn Connection key failed!',
      });
      setOpenDeleteLinked(false);
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    try {
      if (
        !getValues('country') &&
        (!getValues('username') || !getValues('password')) &&
        !getValues('li_at')
      ) {
        notification.warning({
          description: 'Credentials & Region is required!',
        });
        return;
      }

      setLoading(true);

      let response;
      const payload = {
        userAgent: navigator.userAgent,
        country: getValues('country'),
      };

      if (getValues('username') && getValues('password')) {
        payload.username = getValues('username');
        payload.password = getValues('password');
      }

      if (getValues('li_at')) {
        payload.accessToken = getValues('li_at');
      }
      if (getValues('li_a')) {
        payload.premiumToken = getValues('li_a');
      }

      if (!loadingReconnect) {
        response = await linkedInConnections(
          {
            ...payload,
          },
          userId
        );
      } else {
        delete payload.userAgent;
        response = await reConnectedAccount(
          {
            ...payload,
          },
          userId
        );
      }
      if (_get(response, 'data.success')) {
        if (response?.data?.result?.data?.checkpoint?.type === 'OTP') {
          setHandleCheckPoint(true);
          setAccountId(response?.data?.result?.data?.account_id);
          notification.warning({
            message: 'Warning!',
            description:
              'Please check your email, we have sent you the OTP code',
          });
          setLoading(false);
          setOpenEditLinked(false);
          return;
        } else if (
          response?.data?.result?.data?.checkpoint?.type === 'CAPTCHA'
        ) {
          setAccountId(response?.data?.result?.data?.account_id);
          setCaptchaData({
            data: response.data?.result?.data?.checkpoint?.data,
            publicKey: response.data?.result?.data?.checkpoint?.public_key,
          });
          setHandleCheckCaptcha(true);
          setLoading(false);
          setOpenEditLinked(false);
          return;
        } else if (
          response?.data?.result?.data?.checkpoint?.type === '2FA' &&
          response?.data?.result?.data?.checkpoint?.source === 'APP'
        ) {
          setHandleCheck2FAApp(true);
          setAccountId(response?.data?.result?.data?.account_id);
          notification.warning({
            message: 'Warning!',
            description: 'Please check your 2FA and enter the code',
          });
          setLoading(false);
          setOpenEditLinked(false);
          return;
        } else if (
          response?.data?.result?.data?.checkpoint?.type === 'IN_APP_VALIDATION'
        ) {
          //IN_APP_VALIDATION
          setAccountId(response?.data?.result?.data?.account_id);
          setStartCheckAccount(true);
          setHandleCheckInApp(true);
          setLoading(false);
          setOpenEditLinked(false);
          return;
        } else if (
          response?.data?.result?.data?.checkpoint?.type === '2FA' &&
          response?.data?.result?.data?.checkpoint?.source === 'SMS'
        ) {
          setHandleCheckSMS(true);
          setAccountId(response?.data?.result?.data?.account_id);
          setLoading(false);
          setOpenEditLinked(false);
          return;
        }
        notification.open({
          message: 'Success!',
          description: 'LinkedIn Connection successfully!',
        });
        setLoading(false);
        await handleGetDataEmail();
        setOpenEditLinked(false);
      } else {
        notification.error({
          message: 'Error!',
          description: 'LinkedIn Connection failed!',
        });
        setLoading(false);
      }
      setLoadingReconnect(false);
      setCookie('zl_latest_connect', new Date().toISOString());
    } catch (error) {
      console.log('errr: ', error);
      notification.error({
        message: 'Error!',
        description: 'LinkedIn Connection key failed!',
      });
      setLoading(false);
    }
  };

  const EditForm = () => {
    return (
      <div>
        <div className="my-4">
          <Form
            form={form}
            name="basic"
            labelCol={{
              span: 4,
            }}
            wrapperCol={{
              span: 20,
            }}
            style={{
              maxWidth: 700,
            }}
            layout="horizontal"
            labelAlign="left"
            autoComplete="off"
            onFinish={onFinish}
          >
            <Form.Item
              label="Username"
              name="username"
              rules={[
                {
                  required: true,
                  message: 'Please input your Username',
                },
              ]}
            >
              <Input autoComplete={false} disabled={isUserRole || loading} />
            </Form.Item>

            <Form.Item
              label="Password"
              name="password"
              rules={[
                {
                  required: true,
                  message: 'Please input your Password',
                },
              ]}
            >
              <Input.Password
                autoComplete={false}
                disabled={isUserRole || loading}
              />
            </Form.Item>
          </Form>
        </div>
        <div className="flex justify-between items-center">
          <Button
            type="primary"
            onClick={() => form.submit()}
            icon={<SaveOutlined />}
            loading={loading}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  const signInToLinkedinItems = [
    {
      key: 'country-selection',
      title: 'Region',
      // icon: <ContactsOutlined />,
      // subTitle: 'Login by ID & Password'
    },
    {
      key: 'credentials',
      title: 'Credentials',
      // icon: <ContactsOutlined />,
      // subTitle: 'Login by ID & Password'
    },
    {
      key: 'cookies',
      title: (
        <div>
          <span>Cookies</span>
          <span className="text-xs font-medium ml-2">(Optional)</span>
        </div>
      ),
      // icon: <FundViewOutlined />,
      // subTitle: 'Login by Cookies'
    },
    {
      key: 'confirm',
      title: 'Confirm',
      // icon: <FileDoneOutlined />,
      // subTitle: 'Review your information'
    },
  ];

  const countryListOptions = ISO3166A2.map((item) => ({
    label: item.name,
    value: item.code,
  }));

  const steps = [
    {
      title: 'Region',
      content: (
        <>
          <Form.Item
            label={<span className="font-semibold">Your Country</span>}
            name="country"
            rules={[
              {
                required: true,
                message: 'Please input your Country',
              },
            ]}
          >
            <Controller
              control={control}
              name="country"
              render={({ field }) => (
                <Select
                  defaultValue={
                    userToSet?.linkedInCountryCode ||
                    userToSet?.user?.linkedInCountryCode
                  }
                  allowClear
                  showSearch
                  optionRender={(option) => {
                    const { key, label } = option;
                    const countryCode = key?.toLowerCase();
                    return (
                      <div className="flex items-center gap-2">
                        <span class={`fi fi-${countryCode} fis`}></span>
                        <div>{label}</div>
                      </div>
                    );
                  }}
                  optionFilterProp="label"
                  options={countryListOptions}
                  {...field}
                  placeholder="Enter your Region name."
                  suffixIcon={
                    getValues('country') ? (
                      <span
                        class={`fi fi-${getValues('country')?.toLowerCase()} fis`}
                      ></span>
                    ) : (
                      <CompassOutlined />
                    )
                  }
                  disabled={isUserRole || loading}
                />
              )}
            ></Controller>
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Credentials',
      content: (
        <>
          <Form.Item
            label={<span className="font-semibold">Email</span>}
            name="username"
            rules={[
              {
                required: true,
                message: 'Please input your Email',
              },
            ]}
          >
            <Controller
              control={control}
              name="username"
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter your Linkedin Email"
                  addonBefore={<MailOutlined />}
                  disabled={isUserRole || loading}
                />
              )}
            ></Controller>
          </Form.Item>

          <Form.Item
            label={<span className="font-semibold">Password</span>}
            name="password"
            rules={[
              {
                required: true,
                message: 'Please input your Password',
              },
            ]}
          >
            <Controller
              control={control}
              name="password"
              render={({ field }) => (
                <Input.Password
                  {...field}
                  placeholder="Enter your Linkedin Password"
                  addonBefore={<KeyOutlined />}
                  disabled={isUserRole || loading}
                />
              )}
            ></Controller>
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Cookies',
      content: (
        <>
          <div>
            <span>Copy your LinkedIn cookies.</span>
            <span
              onClick={showGuide}
              className="underline cursor-pointer ml-1 font-medium text-cyan-800 hover:text-cyan-600 transition-all delay-100"
            >
              How to find them?
            </span>
          </div>
          <div>
            Your cookies need to be collected in the same browser as this page.
          </div>
          <Form.Item
            // label="Email"
            name="li_at" // zl_atoken
          >
            <Controller
              control={control}
              name="li_at"
              render={({ field }) => (
                <Input.Password
                  {...field}
                  disabled={isUserRole || loading}
                  placeholder="Enter your li_at value"
                />
              )}
            ></Controller>
          </Form.Item>
          <div>
            If your account has Recruiter or Sales Navigator subscription, copy
            the li_a too.
          </div>
          <Form.Item
            // label="Password"
            name="li_a"
          >
            <Controller
              control={control}
              name="li_a" //zl_ptoken
              render={({ field }) => (
                <Input.Password
                  {...field}
                  disabled={isUserRole || loading}
                  placeholder="Enter your li_a value (optional)"
                />
              )}
            ></Controller>
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Review',
      content: (
        <div className="flex flex-col gap-3 items-center justify-center">
          <Image preview={false} src={rocketImage} height={'10rem'} />
          <div className="text-lg font-semibold">You're almost done!</div>
        </div>
      ),
    },
  ];

  const SignInToLinkedin = () => {
    return (
      <div className="h-full p-3 border rounded-bl-md rounded-br-md">
        <Steps
          type="navigation"
          current={current}
          items={signInToLinkedinItems}
        />
        <div className="my-4 ">
          <Form
            form={form}
            name="basic"
            layout="vertical"
            labelAlign="left"
            onFinish={onFinish}
            className={clsx(
              'flex flex-col p-4 border rounded-md linkedin-setting-form min-h-[15rem] justify-center shadow-md',
              current === 0 && 'gap-6',
              current === 1 && 'gap-2'
            )}
          >
            {steps[current].content}
          </Form>
        </div>
        <div className="flex gap-2 justify-center">
          {current > 0 && current < 3 && (
            <Button
              icon={<LeftOutlined />}
              onClick={() => prev()}
              className="bg-white font-semibold py-2 px-10 h-10"
            >
              Back
            </Button>
          )}

          {current < steps.length - 1 && (
            <Button
              // type="primary"
              onClick={() => next()}
              loading={loading}
              className="font-semibold bg-gray-800 text-white py-2 px-10 h-10 flex items-center gap-1"
            >
              <span>Next</span>
              <RightOutlined />
            </Button>
          )}
          {current === steps.length - 1 && (
            <div className="flex gap-2 items-center place-items-center">
              <Button
                icon={<LeftOutlined />}
                onClick={() => prev()}
                className="bg-white font-semibold py-2 px-10 h-10"
              >
                Back
              </Button>
              <span className="text-[#6e6f72] font-semibold">OR</span>
              <Button
                // type="primary"
                onClick={() => form.submit()}
                icon={<CheckOutlined />}
                loading={loading}
                className="font-semibold bg-gray-800 text-white py-2 px-10 h-10"
              >
                Proceed
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  const onChangeCountDown = async (val) => {
    if (val < 0) {
      setStartCheckAccount(false);
      notification.error({
        message: 'Time Out!',
        description: 'Please Link again',
      });
      setHandleCheckInApp(false);
      setHandleCheckPoint(false);
      await handleGetDataEmail();
    }
  };

  const handleLoginAgain = () => {
    setShowData(false);
    setViewScreen(true);
    setLoadingReconnect(true);
  };

  return (
    <div className="p-4 w-full h-full">
      {contextHolder}
      {viewScreen &&
        !(
          handleCheckPoint ||
          handleCheckCaptcha ||
          handleCheck2FAApp ||
          handleCheckSMS ||
          handleCheckInApp
        ) && (
          <div className="h-full w-full">
            {!showData && (
              <div className="h-full w-full flex justify-center items-center">
                <div className="h-full w-2/3 ">
                  <div className="h-full">
                    <div className="flex items-center gap-2 py-3 px-2 border-l border-r border-t rounded-tl-md rounded-tr-md">
                      <LinkedinOutlined className="text-[#0288d1] hover:text-[#0a66c2] text-lg" />
                      <h4 className="font-semibold">Sign in to LinkedIn</h4>
                    </div>
                    <SignInToLinkedin />
                  </div>
                </div>
              </div>
            )}

            {showData && (
              <>
                <div className="flex justify-between items-center">
                  <h4 className="font-bold">LinkedIn Setting</h4>
                </div>
                <div
                  style={{
                    marginTop: '10px',
                    width: '100%',
                    border: '1px solid #ccc',
                    padding: '10px',
                    borderRadius: '3px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div>
                        {userToSet?.user?.linkedinAvatarUrl ? (
                          <img
                            style={{ width: '50px', height: '50px' }}
                            src={userToSet?.user?.linkedinAvatarUrl}
                          ></img>
                        ) : (
                          <LinkedinFilled
                            color={'#0891b2'}
                            style={{
                              color: '#0891b2',
                              fontSize: '30px',
                            }}
                          />
                        )}
                      </div>
                      <div
                        style={{
                          marginLeft: '10px',
                        }}
                      >
                        <div
                          style={{ fontSize: '14px', fontWeight: '600' }}
                          className="flex items-center gap-2"
                        >
                          <span>{linkedInAccountInfo?.name ?? 'LinkedIn'}</span>

                          {(userToSet?.linkedInCountryCode ||
                            userToSet?.user?.linkedInCountryCode) && (
                            <span
                              class={`fi fi-${userToSet?.linkedInCountryCode?.toLowerCase() || userToSet?.user?.linkedInCountryCode?.toLowerCase()} fis`}
                            ></span>
                          )}
                        </div>
                        <div
                          style={{
                            color: COLOR_STATUS[accountStatus],
                            fontSize: '14px',
                          }}
                        >
                          <>
                            <Badge
                              status={BADGE_STATUS[accountStatus]}
                              size="default"
                            />{' '}
                            <span
                              style={{ marginLeft: '0px', fontSize: '13px' }}
                            >
                              {TEXT_STATUS[accountStatus]}
                            </span>
                          </>
                        </div>
                      </div>
                    </div>
                    <div>
                      <CopyToClipboard
                        text={linkedInAccountInfo?.id}
                        onCopy={() => {
                          notification.success({
                            message: 'Success Copy Account Id',
                          });
                        }}
                      >
                        <div
                          style={{
                            cursor: 'pointer',
                            color: 'rgb(109 106 106)',
                          }}
                        >
                          {linkedInAccountInfo?.id}
                        </div>
                      </CopyToClipboard>
                    </div>
                    <div>
                      <div
                        style={{
                          color: COLOR_STATUS[accountStatus],
                          fontSize: '14px',
                          fontWeight: 500,
                        }}
                      >
                        Connected on{' '}
                        {dayjs(linkedInAccountInfo?.created_at).format(
                          'YYYY-MM-DD'
                        )}
                      </div>
                      <div
                        style={{
                          marginTop: '10px',
                          float: 'right',
                          display: 'flex',
                        }}
                      >
                        {accountStatus == 'CREDENTIALS' && (
                          <div>
                            <Button
                              style={{
                                border: '1px solid #c0201e',
                                marginRight: '20px',
                                color: '#c0201e',
                              }}
                              onClick={() => handleLoginAgain()}
                            >
                              Login Again
                            </Button>
                          </div>
                        )}
                        <EditOutlined
                          style={{
                            fontSize: '20px',
                            cursor: 'pointer',
                          }}
                          onClick={() => setOpenEditLinked(!openEditLinked)}
                        />
                        <DeleteOutlined
                          style={{
                            fontSize: '20px',
                            marginLeft: '20px',
                            cursor: 'pointer',
                          }}
                          onClick={() => setOpenDeleteLinked(true)}
                        />
                      </div>
                    </div>
                  </div>
                  {openEditLinked && (
                    <div style={{ marginTop: '20px' }}>
                      <SignInToLinkedin />
                    </div>
                  )}
                </div>
              </>
            )}
            <Modal
              centered
              width={1000}
              bodyStyle={{
                overflowY: 'auto',
                maxHeight: 'calc(100vh - 200px)',
              }}
              title="UnLink LinkedIn Connection"
              open={openDeleteLinked}
              okText={'Confirm'}
              okButtonProps={{
                loading: loading,
              }}
              onOk={handleDeleteLink}
              onCancel={() => setOpenDeleteLinked(false)}
            >
              Are you sure to UnLink LinkedIn
            </Modal>
          </div>
        )}

      {(handleCheckPoint ||
        handleCheckCaptcha ||
        handleCheck2FAApp ||
        handleCheckSMS ||
        handleCheckInApp) && (
        <>
          <div className="flex justify-between items-center">
            <h4 className="font-bold">LinkedIn Setting</h4>
          </div>
          <div
            style={{
              paddingBottom: '20px',
            }}
          >
            <div
              style={{
                width: '444px',
                margin: '0 auto',
                borderColor: 'rgb(228, 228, 231)',
                borderRadius: '6px',
                borderWidth: '1px',
                borderStyle: 'solid',
                boxShadow: 'rgba(0, 0, 0, 0.05) 0px 1px 2px',
              }}
            >
              <div
                style={{
                  padding: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: '600',
                  fontSize: '13px',
                }}
              >
                <LinkedinFilled
                  color={'#0891b2'}
                  style={{
                    color: '#0891b2',
                    fontSize: '30px',
                  }}
                />
                <span style={{ marginLeft: '10px' }}>Sign in to LinkedIn</span>
              </div>
              <hr
                style={{
                  borderWidth: '0px 0px thin',
                  borderStyle: 'solid',
                  borderColor: 'rgb(228, 228, 231)',
                }}
              />
              {handleCheckCaptcha && (
                <div>
                  <div
                    style={{
                      fontWeight: '500',
                      fontSize: '18px',
                      padding: '24px',
                      textAlign: 'center',
                    }}
                  >
                    Please solve the captcha to continue
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <ArkoseCaptchaIntegration
                      publicKey={captchaData.publicKey}
                      data={captchaData.data}
                      onLoaded={handleLoaded}
                      onSuccess={handleSuccess}
                      onError={handleError}
                      loaded={loadedNumber}
                    />
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      marginTop: '24px',
                      paddingBottom: '50px',
                    }}
                  >
                    <Button
                      style={{ width: '47%' }}
                      onClick={handleCancelCaptcha}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {handleCheckPoint && (
                <div style={{ padding: '24px' }}>
                  <div
                    style={{
                      fontSize: '24px',
                      fontWeight: '600',
                      marginBottom: '8px',
                    }}
                  >
                    Enter your OTP code
                  </div>
                  <div
                    style={{
                      marginTop: '24px',
                      fontSize: '18px',
                      fontWeight: '500',
                    }}
                  >
                    Check your email
                  </div>
                  <div style={{ marginTop: '40px' }}>
                    <Input
                      placeholder="Input Code Here"
                      onChange={(e) => setOtpCode(e.target.value)}
                    />
                  </div>
                  <div
                    style={{
                      marginTop: '24px',
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Button
                      style={{ width: '47%' }}
                      onClick={handleCancelOtp}
                      disabled={loadingAccount}
                    >
                      Cancel
                    </Button>
                    <Button
                      style={{
                        width: '47%',
                        background: '#000',
                        color: '#fff',
                      }}
                      onClick={handleAccessWithOtp}
                      loading={loadingAccount}
                    >
                      Submit
                    </Button>
                  </div>
                  <div
                    style={{
                      textAlign: 'center',
                      marginTop: '24px',
                      fontWeight: '500',
                      cursor: loadingResend ? 'no-drop' : 'pointer',
                      textDecoration: 'underline',
                      paddingBottom: '100px',
                      color: loadingResend ? '#ccc' : '#000',
                    }}
                    onClick={() => {
                      !loadingResend && handleResendOtp();
                    }}
                  >
                    Resend OTP code
                  </div>
                </div>
              )}

              {handleCheck2FAApp && (
                <>
                  <div style={{ padding: '24px' }}>
                    <div
                      style={{
                        fontSize: '24px',
                        fontWeight: '600',
                        marginBottom: '8px',
                      }}
                    >
                      Enter your 2FA code
                    </div>
                    <div style={{ marginTop: '40px' }}>
                      <Input
                        placeholder="Input Code Here"
                        onChange={(e) => setOtpCode(e.target.value)}
                      />
                    </div>
                    <div
                      style={{
                        marginTop: '24px',
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Button
                        style={{ width: '47%' }}
                        onClick={handleCancelOtp}
                        disabled={loadingAccount}
                      >
                        Cancel
                      </Button>
                      <Button
                        style={{
                          width: '47%',
                          background: '#000',
                          color: '#fff',
                        }}
                        onClick={handleAccessWithOtp}
                        loading={loadingAccount}
                      >
                        Submit
                      </Button>
                    </div>
                  </div>
                </>
              )}

              {handleCheckSMS && (
                <>
                  <div style={{ padding: '24px' }}>
                    <div
                      style={{
                        fontSize: '24px',
                        fontWeight: '600',
                        marginBottom: '8px',
                      }}
                    >
                      Enter your SMS code
                    </div>
                    <div style={{ marginTop: '40px' }}>
                      <Input
                        placeholder="Input Code Here"
                        onChange={(e) => setOtpCode(e.target.value)}
                      />
                    </div>
                    <div
                      style={{
                        marginTop: '24px',
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Button
                        style={{ width: '47%' }}
                        onClick={handleCancelOtp}
                        disabled={loadingAccount}
                      >
                        Cancel
                      </Button>
                      <Button
                        style={{
                          width: '47%',
                          background: '#000',
                          color: '#fff',
                        }}
                        onClick={handleAccessWithOtp}
                        loading={loadingAccount}
                      >
                        Submit
                      </Button>
                    </div>
                  </div>
                </>
              )}

              {handleCheckInApp && (
                <div
                  style={{
                    padding: '24px',
                  }}
                >
                  <Countdown
                    title="Please check your Device In"
                    value={Date.now() + 60 * 1000}
                    onChange={onChangeCountDown}
                  />
                  <div
                    style={{
                      marginTop: '22px',
                    }}
                  >
                    Please check your device, If within this time, you do not
                    authenticate, we will return you to the dashboard screen
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Guide modal */}
      <Modal
        title="How to find my cookies ?"
        open={openGuideModal}
        onCancel={closeGuide}
        footer={<Button onClick={closeGuide}>Close</Button>}
      >
        <div>
          <p className="font-semibold mb-2">
            Follow the steps to find your linkedin cookies (not available on
            mobile)
          </p>
          <p className="">
            1. Open linkedin in a new tab (or click here:{' '}
            <a
              className="text-cyan-500 underline"
              href="https://www.linkedin.com"
              target="_blank"
            >
              linkedin
            </a>
            ).
          </p>
          <p>2. Log in to your account.</p>
          <p>
            3. Open your browser's developer console (F12 for Chrome and
            Firefox, option + command + I for Safari) then go to the
            "application" or "storage" tab.
          </p>
          <p>
            4. Open the cookies folder and click on the one called
            "https://www.linkedin.com".
          </p>
          <p>
            5. Copy the values for "li_at" into the field below, then click on
            the connect button
          </p>
        </div>
      </Modal>
    </div>
  );
};

export default LinkedIn;
