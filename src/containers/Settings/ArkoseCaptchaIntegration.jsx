import React, { useEffect, useRef } from 'react';

const ArkoseCaptchaIntegration = ({ publicKey, data, onLoaded, onSuccess, onError, loaded }) => {
  const captchaContainerId = 'captcha-frame';
  const setupEnforcementScriptRef = useRef(null);
  const apiScriptRef = useRef(null);

  useEffect(() => {
    const handleEvent = (e) => {
      if (typeof e.data === 'string') {
        const eventData = JSON.parse(e.data);
        if (eventData && typeof eventData === 'object' && 'eventId' in eventData) {
          switch (eventData.eventId) {
            case 'challenge-loaded':
              onLoaded?.();
              break;
            case 'challenge-complete':
              onSuccess?.(eventData.payload.sessionToken);
              break;
            case 'challenge-error':
            case 'challenge-failed':
              onError?.();
              break;
            default:
              break;
          }
        }
      }
    };

    window.addEventListener('message', handleEvent);

    const makeEnforcementScript = ({ data, publicKey, startTime, targetElementId }) => {
      return `
        function setupEnforcement(e) {
          const endTime = Date.now();

          e.setConfig({
            selector: "#${targetElementId}",
            styleTheme: undefined,
            language: "en",
            data: { blob: "${data}" },
            mode: "inline",
            noSuppress: undefined,
            apiLoadTime: { start: ${startTime}, end: endTime, diff: endTime - ${startTime} },
            onCompleted: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-complete", publicKey: "${publicKey}", payload: { sessionToken: e.token } }),
                "*"
              );
            },
            onReady: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-loaded", publicKey: "${publicKey}", payload: { sessionToken: e.token } }),
                "*"
              );
            },
            onSuppress: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-suppressed", publicKey: "${publicKey}", payload: { sessionToken: e.token } }),
                "*"
              );
            },
            onShown: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-shown", publicKey: "${publicKey}", payload: { sessionToken: e.token } }),
                "*"
              );
            },
            onError: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-error", publicKey: "${publicKey}", payload: { error: e.error } }),
                "*"
              );
            },
            onWarning: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-warning", publicKey: "${publicKey}", payload: { warning: e.warning } }),
                "*"
              );
            },
            onFailed: function (e) {
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-failed", publicKey: "${publicKey}", payload: { sessionToken: e.token } }),
                "*"
              );
            },
            onResize: function (e) {
              var n = e && e.height ? e.height : 450,
                a = e && e.width ? e.width : 400;
              try {
                "string" == typeof n && ((n = n.replace("px", "")), (n = parseInt(n, 10)), isNaN(n) && (n = 450)),
                  "string" == typeof a && ((a = a.replace("px", "")), (a = parseInt(a, 10)), isNaN(a) && (a = 400));
              } catch (e) {
                (n = 450), (a = 400);
              }
              parent.postMessage(
                JSON.stringify({ eventId: "challenge-iframeSize", publicKey: "${publicKey}", payload: { frameHeight: n, frameWidth: a } }),
                "*"
              );
            },
          });
        }`;
    };

    // Inject Arkose Labs enforcement script
    const setupEnforcementScript = document.createElement('script');
    setupEnforcementScript.textContent = makeEnforcementScript({
      targetElementId: captchaContainerId,
      publicKey,
      data,
      startTime: Date.now(),
    });
    document.head.appendChild(setupEnforcementScript);
    setupEnforcementScriptRef.current = setupEnforcementScript;

    // Load Arkose Labs game script
    const apiScript = document.createElement('script');
    apiScript.src = `//client-api.arkoselabs.com/v2/${publicKey}/api.js`;
    apiScript.dataset.callback = 'setupEnforcement';
    apiScript.defer = true;
    apiScript.async = true;
    document.head.appendChild(apiScript);
    apiScriptRef.current = apiScript;

    return () => {
      window.removeEventListener('message', handleEvent);
      setupEnforcementScriptRef.current?.remove();
      apiScriptRef.current?.remove();
    };
  }, [publicKey, data, loaded, onError]);

  return <div id={captchaContainerId}></div>;
};

export default ArkoseCaptchaIntegration;