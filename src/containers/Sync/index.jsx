import React, { useState, useEffect } from 'react';
import Table from '../../components/SyncTable';

function Sync() {
  const [tableData, setTableData] = useState([]);
  useEffect(() => {
    if (localStorage.getItem('savedSync') == null)
      localStorage.setItem('savedSync', JSON.stringify([]));
    setTableData(JSON.parse(localStorage.getItem('savedSync')));
  }, []);
  return (
    <div className="overflow-hidden">
      <Table jobData={tableData} setTableData={setTableData} />
    </div>
  );
}

export default Sync;
