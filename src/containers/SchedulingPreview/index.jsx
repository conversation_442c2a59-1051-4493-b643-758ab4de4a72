import React, { useEffect, useState } from 'react';
import { NylasScheduling } from '@nylas/react';

import './scheduling.css';
import Footer from '../../components/Footer';
import { Button, Image, Spin } from 'antd';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { NYLAS_API_URL, NYLAS_API_VERSION, getNylasSchedulingConfig } from '../../services/nylas';

const SchedulingPreview = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const configId = urlParams.get('config_id') || '';

  const [loading, setLoading] = useState(true);
  const [isExistingScheduler, setIsExistingScheduler] = useState(false);

  const hideNylaslogo = () => {
    if (!document) return;
    const style = document.createElement('style');
    const host = document?.getElementsByClassName('scheduling-container');
    style.innerHTML = `.footer {  display: none !important;}`;
    if (host[0]) {
      host[0]?.shadowRoot?.appendChild(style);
      console.log('host: ', host[0].shadowRoot);
    }
  };

  const getNylasSchedulingConfigFunc = async () => {
    if (!configId) {
      setLoading(false);
      return;
    }
    await getNylasSchedulingConfig(configId)
      .then((res) => {
        if (res) {
          setIsExistingScheduler(true);
        }
      })
      .catch((err) => {})
      .finally(() => {
        setLoading(false);
        hideNylaslogo();
      });
  };

  useEffect(() => {
    getNylasSchedulingConfigFunc();
  }, []);

  return (
    <div className="w-full flex flex-col items-center justify-center p-10 gap-5 center h-screen">
      {loading && <Spin />}
      {!loading &&
        (isExistingScheduler ? (
          <>
            <div className="flex justify-between items-center w-1/2">
              <Image width={'10rem'} preview={false} src={zileoLogo} />
              <div className="text-2xl font-medium">Let's book a meeting</div>
            </div>
            <NylasScheduling
              className="scheduling-container"
              configurationId={configId}
              schedulerApiUrl={`${NYLAS_API_URL}${NYLAS_API_VERSION}`}
            />
            <Footer />
          </>
        ) : (
          <div className="flex flex-col gap-3 items-center">
            <Image width={'30rem'} preview={false} src={zileoLogo} />
            <div className="text-6xl font-semibold">ERROR</div>
            <div className="mb-10">
              The scheduler might have cancelled or deleted by event creator!
            </div>
            <Button onClick={() => (window.location.href = '/')}>
              Return home page
            </Button>
          </div>
        ))}
    </div>
  );
};

export default SchedulingPreview;
