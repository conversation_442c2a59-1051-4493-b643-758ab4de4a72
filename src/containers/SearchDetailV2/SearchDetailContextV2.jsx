/* eslint-disable react/prop-types */
import React, { createContext, useContext } from 'react';

const SearchDetailsContext = createContext();

export const useSearchDetailsContext = () => {
  return useContext(SearchDetailsContext);
};

export function SearchDetailsProvider({ children, searchDetails }) {
  return (
    <SearchDetailsContext.Provider value={searchDetails}>
      {children}
    </SearchDetailsContext.Provider>
  );
}
