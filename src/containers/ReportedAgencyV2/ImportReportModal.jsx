import { InboxOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Modal, Table, notification } from 'antd';
import Dragger from 'antd/es/upload/Dragger';
import _ from 'lodash';
import { useState } from 'react';
import * as XLSX from 'xlsx';
import { bulkCreateReportedAgency } from '../../services/reportedAgencies';
import { useMemo } from 'react';

const removeEmptyValues = (object) =>
  Object.fromEntries(Object.entries(object).filter(([_, value]) => value));

const ImportReportModal = ({ populateReportedAgencies, closeImportModal }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  //   preview features
  const [openPreview, setOpenPreview] = useState(false);

  const showPreview = () => setOpenPreview(true);
  const closePreview = () => setOpenPreview(false);

  const onSaveAll = async () => {
    const payload = {
      agencies: [...data],
    };
    setLoading(true);
    await bulkCreateReportedAgency(payload)
      .then((res) => {
        populateReportedAgencies();
        closeImportModal();
        notification.success({
          description: 'Import success!',
        });
        console.log('res: ', res);
      })
      .catch((err) => {
        notification.error({
          description: 'Failed. Try again later!',
        });
        console.log('err: ', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleFileUpload = (file) => {
    // const file = e.target.files[0];
    const reader = new FileReader();
    console.log('file: ', file);
    reader.onload = (event) => {
      const workbook = XLSX.read(event.target.result, { type: 'binary' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const sheetData = XLSX.utils.sheet_to_json(sheet);
      const filteredData = sheetData
        .map((item) =>
          removeEmptyValues({
            companyName: item?.companyName || '',
            aliasCompanyNames: item?.aliasCompanyNames
              ? item?.aliasCompanyNames?.includes(',')
                ? [item?.aliasCompanyNames.split(',')]
                : [item?.aliasCompanyNames]
              : '',
              country: item?.country || ''
          })
        )
        .filter((item) => !_.isEmpty(item));
        
      setData(filteredData);
    };

    reader.readAsBinaryString(file);
  };

  const dragProps = useMemo(() => ({
    name: 'file',
    multiple: false,
    action: '',
    accept: '.xlsx, .csv',
    maxCount: 1,
    // action: '/api/upload',
    onChange(info) {
      const { status } = info?.file;

      if (status === 'removed') {
        setFileList([]);
        return;
      }

      const isExcel =
        info?.file.type === 'text/csv' ||
        info?.file.type === 'text/slsx' ||
        info?.file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        info.file.type === 'application/vnd.ms-excel';

      if (!isExcel) {
        notification.error({
          description: 'You can only upload .xlsx or csv',
        });
        return;
      }

      // console.log("file.size: ",  file.size)
      const isLt5M = info?.file.size < 5242880;
      if (!isLt5M) {
        notification.error({
          description: 'Please upload image less than 5mb',
        });
        return;
      }

      setFileList([info?.file]);
      if (status !== 'uploading') {
        // const isLt1M = info.file.size < 5242880;
        // if (!isLt1M) {
        //   notification.error({
        //     description: 'Please upload image less than 5mb',
        //   });
        //   return;
        // }
        handleFileUpload(info.file);
      }
      //   if (status === 'done') {
      //     closeImportModal();
      //     populateReportedAgencies();

      //   } else if (status === 'error') {
      //     notification.error({
      //       description: `${info.file.name} file upload failed.`,
      //     });
      //   }
    },
    showUploadList: {
      showRemoveIcon: true,
      removeIcon: <DeleteOutlined onClick={() => setData([])} />,
    },
  }));

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: 'Alias Company Names',
      dataIndex: 'aliasCompanyNames',
      key: 'aliasCompanyNames',
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
    },
  ];

  return (
    <div>
      {openPreview && (
        <Modal
          width={1000}
          title="Preview data"
          open={openPreview}
          onCancel={closePreview}
          footer={null}
        >
          <Table className="pt-4" dataSource={data} columns={columns} />
        </Modal>
      )}
      <div className="flex w-full justify-end">
        <Button
          type="link"
          disabled={data?.length === 0 || !data || loading}
          onClick={showPreview}
          icon={<EyeOutlined />}
          title="Preview imported data."
        >
          Preview
        </Button>
      </div>
      <div className="py-4 import-reported-agencies-container">
        <Dragger fileList={fileList} {...dragProps} beforeUpload={() => false}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            Click or drag file to this area to upload
          </p>
          <p className="ant-upload-hint">
            Support for a single upload. Only supporting XLSX and CSV format.
          </p>
        </Dragger>
      </div>

      <div className="flex gap-1 justify-end">
        <Button onClick={closeImportModal}>Close</Button>
        <Button
          loading={loading}
          onClick={onSaveAll}
          disabled={data?.length === 0 || !data}
          type="primary"
        >
          Save all
        </Button>
      </div>
    </div>
  );
};

export default ImportReportModal;
