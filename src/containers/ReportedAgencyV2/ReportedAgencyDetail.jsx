import { useNavigate, useParams } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import JobList from '../../components/JobListV2/JobList';
import { getJobsByAgencyId } from '../../services/reportedAgencies';
import { Button, Image, Spin } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import notFound from '../../assets/svg/404.svg';

const useReportedAgencyDetail = () => {
  let { id: reportedAgencyID } = useParams();
  const [jobsOfAgency, setJobsOfAgency] = useState({ result: { data: [] } });
  const [isLoading, setLoading] = useState(true);
  const [page, setPage] = useState(1);

  const populateJobsOfAgency = async (page) => {
    setLoading(true);
    try {
      const { data } = await getJobsByAgencyId(reportedAgencyID, page);
      setJobsOfAgency(data);
      setLoading(false);
    } catch (error) {
      console.log('jobsOfAgency error', error);
      setLoading(false);
    }
  };
  useEffect(() => {
    if (reportedAgencyID) {
      populateJobsOfAgency(page || 1);
    }
  }, [reportedAgencyID, page]);

  return { jobsOfAgency, isLoading, populateJobsOfAgency, setPage, page };
};

const ReportedAgencyDetail = () => {
  const navigate = useNavigate();
  const { jobsOfAgency, isLoading, populateJobsOfAgency, setPage, page } =
    useReportedAgencyDetail();

  return (
    <>
      {isLoading && (
        <div className="w-full h-full flex justify-center items-center">
          <Spin size="large" />
        </div>
      )}
      {!isLoading &&
        (jobsOfAgency?.result?.data?.length > 0 ? (
          <>
            <div className="flex items-center mb-4 gap-8">
              <Button
                className="bg-white flex items-center "
                disabled={isLoading}
                onClick={() => {
                  navigate(-1);
                }}
                icon={<LeftOutlined />}
              >
                Back
              </Button>
              <div className="text-lg font-medium">
                {jobsOfAgency?.result?.data?.[0]?.company}
              </div>
            </div>
            <JobList
              searchData={jobsOfAgency.result}
              isSearchLoading={isLoading}
              populateSearchData={populateJobsOfAgency}
              isFromReportedAgency={true}
              isFromSyncSearches={true}
              setPage={setPage}
              page={page}
            />
          </>
        ) : (
          <div className="w-full  h-full flex flex-col justify-center items-center gap-3">
            <Image src={notFound} preview={false} />
            <div className="text-center text-4xl font-semibold">404</div>
            <p>There are no jobs from this agency!</p>
            <Button
              icon={<LeftOutlined />}
              className="bg-white"
              onClick={() => navigate(-1)}
            >
              Back to Home
            </Button>
          </div>
        ))}
    </>
  );
};

export default ReportedAgencyDetail;
