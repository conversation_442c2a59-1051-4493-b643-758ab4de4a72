import {
  CheckOutlined,
  ClearOutlined,
  DeleteOutlined,
  SaveOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Input,
  Pagination,
  Select,
  Spin,
  Table,
  notification,
  Popconfirm,
} from 'antd';
import * as React from 'react';
import { useState } from 'react';
import {
  createReportedAgencyAlias,
  getFuzzySearch,
  getReportedAgencies,
  bulkUpdateReportedAgencyAlias,
  deleteReportedAgency,
} from '../../services/reportedAgencies';
import handleRenderTime from '../../function/handleRenderTime';

const ReportedAgenciesBulkUpdate = ({ populateReportedAgencies, total }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 100,
  });
  const [loading, setLoading] = useState(false);
  const [fuzzySearchLoading, setFuzzySearchLoading] = useState(true);
  const [rawDataSource, setRawDataSource] = useState([]);

  const handleUpdateReport = async (updatingReports) => {
    try {
      const data = updatingReports.map((item) => ({
        aliasCompanyNames: item?.similarNames,
        id: item?.id,
      }));
      await bulkUpdateReportedAgencyAlias(data);
      populateReportedAgencies();
      if (updatingReports.length === 1) {
        const findIndex = dataSource.findIndex(
          (item) => item?.id === updatingReports[0]?.id
        );
        const newDataSource = [...dataSource];
        newDataSource[findIndex] = {
          ...updatingReports[0],
          updatedAt: new Date(),
        };
        setDataSource([...newDataSource]);
      } else {
        const others =
          dataSource.filter((item) => !selectedRowKeys.includes(item?.id)) ||
          [];

        const updatedData = updatingReports.map((item) =>
          selectedRowKeys.includes(item?.id)
            ? { ...item, updatedAt: new Date() }
            : { ...item }
        );

        setDataSource([...updatedData, ...others]);
      }

      notification.success({
        message: 'Successfully',
        description: `Updated successfully`,
      });
    } catch (err) {
      notification.error({
        message: 'Error',
        description: 'Some thing went wrong',
      });
    }

    // try {
    //   await createReportedAgencyAlias({
    //     companyName: companyName.trim(),
    //     aliasCompanyNames: fuzzySearch,
    //   });
    //   populateReportedAgencies();
    //   notification.success({
    //     message: 'Successfully',
    //     description: `Add Reported Agency for ${companyName} successfully`,
    //   });
    // } catch (err) {
    //   notification.error({
    //     message: 'Error',
    //     description: 'Some thing went wrong',
    //   });
    // }
  };

  const promiseGetSimilarNames = async (agencies) => {
    setFuzzySearchLoading(true);
    Promise.allSettled(
      agencies.map((agency) => getSimilarName(agency?.companyName))
    ).then((list) => {
      const newDataSource = agencies.map((item, index) => ({
        ...item,
        similarNames: list[index]?.value || [],
      }));
      console.log('newDataSource: ', newDataSource);
      setDataSource([...newDataSource]);
      setRawDataSource([...newDataSource]);

      setFuzzySearchLoading(false);
    });
  };

  const getSimilarName = async (companyName) => {
    const { data } = await getFuzzySearch(companyName);
    if (data) {
      return data?.result;
    } else {
      return [];
    }
  };

  const handlePagination = (page) => {
    setPagination({ ...pagination, page: page });
    getReportedAgenciesData({ ...pagination, page: page });
  };

  const getReportedAgenciesData = async (pagination) => {
    const reg = new RegExp('^[0-9]+$');
    // if (!reg.test(recordNumber) || recordNumber === 0) {
    //   notification.warning({
    //     description: 'Please input correct number of records!',
    //   });
    //   return;
    // }
    setLoading(true);
    getReportedAgencies(pagination.page, pagination.pageSize)
      .then((res) => {
        const agencies = res.data?.result?.data;
        if (agencies) {
          const dataSourceTemp = agencies.map((agency, index) => ({
            ...agency,
            index: index + 1,
          }));

          const selectedRowKeysTemp = agencies.map((agency) => agency?.id);
          setSelectedRowKeys(selectedRowKeysTemp);
          setDataSource([...dataSourceTemp]);
          promiseGetSimilarNames(dataSourceTemp);
        } else {
          notification.warning({
            description: 'Can not found any agencies!',
          });
        }
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          description: 'Network error. Try again later!',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const hasSelected = selectedRowKeys.length > 0;

  const handleSaveAll = async () => {
    // setSavingProgress(0);
    const savingList = dataSource.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );
    setLoading(true);
    await handleUpdateReport(savingList).then((res) => {
      //   setSavingProgress(100);
      //   notification.success({
      //     description: 'Saved.',
      //   });
      setLoading(false);
    });
  };

  const handleUnselectNonSimilarNames = () => {
    const nonSimilarNamesIdList = dataSource
      .filter(
        (item) =>
          selectedRowKeys.includes(item?.id) && item?.similarNames?.length === 0
      )
      .map((item) => item?.id);
    if (nonSimilarNamesIdList?.length === 0) {
      notification.success({
        description: 'Congratulation! There are no items to unselect',
      });
      return;
    }
    const newSelectedRowKeys = selectedRowKeys.filter(
      (id) => !nonSimilarNamesIdList.includes(id)
    );
    setSelectedRowKeys(newSelectedRowKeys);
    notification.success({
      description: `Congratulation! There are ${nonSimilarNamesIdList?.length} unselected items!`,
    });
  };

  const handleDeleteReport = async (reportId) => {
    await deleteReportedAgency(reportId)
      .then((res) => {
        populateReportedAgencies();
        getReportedAgenciesData(pagination)
        return notification.success({
          description: 'Deleted successfull!',
        });
      })
      .catch((error) => {
        return notification.error({
          description: 'Deleted failed!',
        });
      });
  };

  const columns = [
    {
      title: 'No',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: 'Company',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 500,
    },
    {
      title: 'New Found Similar Names',
      dataIndex: 'similarNames',
      width: 700,
      key: 'similarNames',
      render: (similarNames, record) => {
        const handleChange = (value) => {
          const changedItemIndex = dataSource.findIndex(
            (item) => item?.id === record?.id
          );
          let newDataSource = [...dataSource];
          newDataSource[changedItemIndex] = {
            ...newDataSource[changedItemIndex],
            similarNames: [...value],
          };
          setDataSource(newDataSource);
        };
        const selectedRecord = rawDataSource.find(
          (item) => item?.id === record?.id
        );
        let options = [];
        if (selectedRecord) {
          options =
            selectedRecord?.similarNames &&
            selectedRecord?.similarNames?.length > 0
              ? selectedRecord?.similarNames.map((item) => ({
                  value: item,
                  lable: item,
                }))
              : [];
        }

        return (
          <>
            {fuzzySearchLoading && (
              <div className="flex w-full justify-center">
                <Spin />
              </div>
            )}
            {!fuzzySearchLoading &&
              similarNames &&
              similarNames?.length > 0 && (
                <Select
                  mode="tags"
                  style={{
                    width: '100%',
                  }}
                  value={similarNames}
                  onChange={handleChange}
                  options={options}
                />
              )}
            {!fuzzySearchLoading &&
              similarNames &&
              similarNames?.length === 0 && (
                // <div className="flex w-full justify-center">
                //   Can not found similar names
                // </div>
                <Select
                  mode="tags"
                  style={{
                    width: '100%',
                  }}
                  value={similarNames}
                  onChange={handleChange}
                  options={options}
                />
              )}
          </>
        );
      },
    },
    {
      title: 'Alias',
      dataIndex: 'aliasCompanyNames',
      key: 'aliasCompanyNames',
      width: 500,
      render: (aliasCompanyNames) =>
        aliasCompanyNames && aliasCompanyNames.length > 0
          ? aliasCompanyNames.join(', ')
          : '-',
    },
    {
      title: 'Last Update',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt) =>
        updatedAt ? `${handleRenderTime(updatedAt)}` : '-',
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (text, record) => {
        return (
          <div className="flex">
            <Button
              disabled={
                record?.similarNames?.length === 0 ||
                !record?.similarNames ||
                loading
              }
              type="link"
              icon={<SaveOutlined />}
              onClick={async (e) => {
                console.log('record: ', record);
                setLoading(true);
                await handleUpdateReport([record]);
                setLoading(false);
              }}
              title="Update this report."
            >
              {/* Save */}
            </Button>
            <Popconfirm
              title={`Delete ${record?.companyName}`}
              description={`Are you sure to delete ${record?.companyName}?`}
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() =>
                new Promise((resolve) =>
                  resolve(handleDeleteReport(record?.id))
                )
              }
            >
              <Button
                title="Delete this report."
                disabled={loading}
                type="link"
                icon={<DeleteOutlined className="text-red-500" />}
              >
                {/* Delete */}
              </Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  return (
    <div className="py-4">
      <div className="flex justify-between">
        <Button
          loading={loading}
          onClick={() => {
            console.log('pagination: ', pagination);
            getReportedAgenciesData(pagination);
          }}
          icon={<CheckOutlined />}
        >
          Check all
        </Button>

        <div className="flex gap-2">
          <Button
            onClick={handleSaveAll}
            disabled={loading || !hasSelected}
            icon={<SaveOutlined />}
          >
            {`Save all ${hasSelected ? `- ${selectedRowKeys.length} ${selectedRowKeys.length === 1 ? 'Company' : 'Companies'}` : ''} `}
          </Button>
          <Button
            onClick={handleUnselectNonSimilarNames}
            disabled={loading || !hasSelected}
            icon={<ClearOutlined />}
            title="Unselect all report(s) that not have similar name(s)."
          ></Button>
        </div>
      </div>
      <div>
        {dataSource.length > 0 && (
          <>
            <Table
              rowSelection={rowSelection}
              dataSource={dataSource}
              columns={columns}
              rowKey={(record) => record.id}
              className="custom-table"
              style={{ marginTop: '20px' }}
              rowClassName="custom-row"
              pagination={false}
              loading={loading}
            />
            <Pagination
              className="mt-3"
              defaultCurrent={pagination.page}
              total={total}
              showSizeChanger={false}
              onChange={handlePagination}
              pageSize={pagination.pageSize}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default ReportedAgenciesBulkUpdate;
