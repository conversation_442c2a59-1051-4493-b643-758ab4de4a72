import { Button, DatePicker, Form, Select, notification } from 'antd';
import * as React from 'react';
import { exportReportedAgencies } from '../../services/reportedAgencies';
import { useState } from 'react';
import moment from 'moment';
import dayjs from 'dayjs';

const ExportedReportModal = ({ closeExportedModal }) => {
  const [loading, setLoading] = useState(false);
  const [rangeDate, setRangeDate] = useState({
    fromDate: '',
    toDate: '',
  });

  const onFinish = async (values) => {
    setLoading(true);
    console.log('Success:', values);
    const payload = {
      ...values,
      fromDate: values?.fromDate ? dayjs(values?.fromDate).format('YYYY-MM-DD') : "",
      toDate: values?.toDate ? dayjs(values?.toDate).format('YYYY-MM-DD'): "",
    };
    await exportReportedAgencies(payload)
      .then((res) => {
        if (!res?.data) {
          notification.warning({
            description: 'There are no report found!',
          });
          return;
        }
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `agencies.csv`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        closeExportedModal();
        notification.success({
          description: 'Retrieve report success!',
        });
        console.log('res: ', res);
      })
      .catch((err) => {
        notification.error({
          description: 'Failed. Try again later!',
        });
        console.log('err: ', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo);
  };

  const agencyTypeOptions = [
    {
      value: 'GPT',
      label: 'GPT',
    },
    {
      value: 'USER',
      label: 'USER',
    },
  ];
  const delimiterOptions = [
    {
      value: 'COMMA',
      label: 'Comma',
    },
    {
      value: 'SEMICOLON',
      label: 'Semicolon',
    },
  ];

  return (
    <Form
      name="basic"
      labelCol={{
        span: 6,
      }}
      wrapperCol={{
        span: 16,
      }}
      style={{
        maxWidth: 600,
        marginTop: '1.5rem',
      }}
      initialValues={{
        agencyType: 'GPT',
        delimiter: 'COMMA',
      }}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
    >
      <Form.Item
        label="Agency Type"
        name="agencyType"
        // rules={[
        //   {
        //     required: true,
        //     message: 'Please select Agency Type!',
        //   },
        // ]}
      >
        <Select options={agencyTypeOptions} />
      </Form.Item>

      <Form.Item
        label="Delimiter"
        name="delimiter"
        // rules={[
        //   {
        //     required: true,
        //     message: 'Please select Delimiter!',
        //   },
        // ]}
      >
        <Select options={delimiterOptions} />
      </Form.Item>

      <Form.Item
        label="From"
        name="fromDate"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <DatePicker value={rangeDate.fromDate} onChange={(value)=> console.log("eheh:", value)} format={'DD-MM-YYYY'} />
      </Form.Item>

      <Form.Item
        label="To"
        name="toDate"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <DatePicker value={rangeDate.toDate} onChange={(value)=> setRangeDate({...rangeDate, toDate: value})} format={'DD-MM-YYYY'} />
      </Form.Item>

      <Form.Item
        wrapperCol={{
          offset: 12,
          span: 16,
        }}
        //   className='flex gap-2 items-center flex-row'
      >
        <Button onClick={closeExportedModal} htmlType="button">
          Close
        </Button>
        <Button
          loading={loading}
          className="mx-4"
          type="primary"
          htmlType="submit"
        >
          Export Data
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ExportedReportModal;
