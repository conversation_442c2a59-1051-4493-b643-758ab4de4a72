import React, { useState } from 'react';
import {
  Table,
  Button,
  Spin,
  Modal,
  Pagination,
  Input,
  Flex,
  Form,
  Select,
  Upload,
  notification,
  Tag,
  Dropdown,
  Tooltip,
} from 'antd';
import {
  DeleteOutlined,
  ExportOutlined,
  InboxOutlined,
  NotificationOutlined,
  OrderedListOutlined,
  ImportOutlined,
  CloseOutlined,
  CheckOutlined,
  HourglassOutlined,
  CrownOutlined,
} from '@ant-design/icons';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import useReportedAgencies from '../../hooks/useReportedAgencies';
import useReportedAgencyModal from '../../hooks/useReportedAgencyModal';
import { countries } from 'country-list-json';
import dayjs from 'dayjs';
import _ from 'lodash';
import ReportedAgenciesBulkUpdate from './ReportedAgenciesBulkUpdate';
import ExportedReportModal from './ExportedReportModal';
import ImportReportModal from './ImportReportModal';
import './style.scss';
import { updateReportStatus } from '../../services/reportedAgencies';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { SUPER_ADMIN } from '../../constants/common.constant';

const REPORT_STATUS_TEXT = {
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  ALL: 'All Status',
};

const REPORT_STATUS = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
};

const REPORT_STATUS_COLOR = {
  PENDING: 'warning',
  APPROVED: 'success',
  REJECTED: 'error',
  ALL: 'default',
};

const REPORT_STATUS_ICON = {
  PENDING: <HourglassOutlined className="text-base font-medium" />,
  APPROVED: <CheckOutlined className="text-base font-medium" />,
  REJECTED: <CloseOutlined className="text-base font-medium" />,
  ALL: <CrownOutlined className="text-base font-medium" />,
};

const ReportedAgenciesTable = () => {
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;
  const userRole = userToSet?.role || userToSet?.user?.role;
  console.log('userRole', userRole?.keyCode);

  const countriesSelectItems = countries?.map((country) => ({
    label: country.name,
    value: country.name,
  }));

  const {
    isLoading,
    reportedAgencies,
    setReportedAgencies,
    handleDelete,
    handleCancelDelete,
    recordTable,
    setRecordTable,
    deleteModalOpen,
    setDeleteModalOpen,
    confirmLoadingDelete,
    pageSize,
    populateReportedAgencies,
    setSearchName,
    searchName,
    setPage,
    status,
    setStatus,
  } = useReportedAgencies();

  const {
    open,
    companyName,
    listAgencyName,
    isLoadingNewListAgency,
    showModal,
    handleOk,
    handleCancel,
    handleGetNewFuzzySearch,
    setCompanyName,
  } = useReportedAgencyModal();

  // Bulk update modal
  const [openBulkUpdateModal, setOpenBulkUpdateModal] = useState(false);
  const showBulkUpdateModal = () => setOpenBulkUpdateModal(true);
  const closeBulkUpdateModal = () => setOpenBulkUpdateModal(false);

  // export reported agencies by GPT/ user modal
  const [openExportedModal, setOpenExportedModal] = useState(false);
  const showExportedModal = () => setOpenExportedModal(true);
  const closeExportedModal = () => setOpenExportedModal(false);

  // import reported agencies modal
  const [openImportModal, setOpenImportModal] = useState(false);
  const showImportModal = () => setOpenImportModal(true);
  const closeImportModal = () => setOpenImportModal(false);

  const [actionLoading, setActionLoading] = useState(false);

  const handleUpdateStatus = async (id, status) => {
    setActionLoading(true);
    try {
      const { data } = await updateReportStatus(id, status);
      if (data?.success) {
        const newReportedAgencies = [...reportedAgencies?.result?.data];
        const updatedIndex = newReportedAgencies.findIndex(
          (item) => item?.id === id
        );
        if (updatedIndex < 0) return;
        newReportedAgencies[updatedIndex].status = status;
        setReportedAgencies({
          ...reportedAgencies,
          result: {
            ...reportedAgencies.result,
            data: newReportedAgencies,
          },
        });
        notification.success({
          description: `Reported agency has been ${status.toLowerCase()}`,
        });
      }
      setActionLoading(false);
    } catch (error) {
      console.log('error', error);
      setActionLoading(false);
    }
  };

  const itemStatus = [
    {
      label: (
        <div
          className="flex items-center justify-center custom-tag"
          onClick={() => setStatus('ALL')}
        >
          <Tag
            icon={REPORT_STATUS_ICON['ALL']}
            color={REPORT_STATUS_COLOR['ALL']}
          >
            {REPORT_STATUS_TEXT['ALL']}
          </Tag>
        </div>
      ),
      key: 'ALL',
    },
    {
      label: (
        <div
          className="flex items-center justify-center custom-tag"
          onClick={() => setStatus('PENDING')}
        >
          <Tag
            icon={REPORT_STATUS_ICON['PENDING']}
            color={REPORT_STATUS_COLOR['PENDING']}
          >
            {REPORT_STATUS_TEXT['PENDING']}
          </Tag>
        </div>
      ),
      key: 'PENDING',
    },
    {
      label: (
        <div
          className="flex items-center justify-center custom-tag"
          onClick={() => setStatus('APPROVED')}
        >
          <Tag
            icon={REPORT_STATUS_ICON['APPROVED']}
            color={REPORT_STATUS_COLOR['APPROVED']}
          >
            {REPORT_STATUS_TEXT['APPROVED']}
          </Tag>
        </div>
      ),
      key: 'APPROVED',
    },
    {
      label: (
        <div
          className="flex items-center justify-center custom-tag"
          onClick={() => setStatus('REJECTED')}
        >
          <Tag
            icon={REPORT_STATUS_ICON['REJECTED']}
            color={REPORT_STATUS_COLOR['REJECTED']}
          >
            {REPORT_STATUS_TEXT['REJECTED']}
          </Tag>
        </div>
      ),
      key: 'REJECTED',
    },
  ];

  const { control, getValues, reset } = useForm({
    defaultValues: {
      fuzzySearch: [],
      country: 'United Kingdom',
    },
  });

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: 'Alias Company Name',
      dataIndex: 'aliasCompanyNames',
      key: 'aliasCompanyNames',
      // width: 700,
      render: (aliasCompanyNames) =>
        aliasCompanyNames &&
        aliasCompanyNames.length > 0 &&
        aliasCompanyNames?.[0]
          ? aliasCompanyNames.join(', ')
          : '-',
    },
    {
      title: 'Update At',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (updatedAt) =>
        updatedAt ? `${dayjs(updatedAt).format('DD/MM/YYYY hh:mm:ss')}` : '-',
    },
    {
      title: 'Updated By',
      dataIndex: 'userEmail',
      key: 'userEmail',
      align: 'center',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status) => {
        return (
          <div className="company-status-container flex items-center justify-center">
            <Tag
              icon={REPORT_STATUS_ICON[status]}
              color={REPORT_STATUS_COLOR[status]}
            >
              {REPORT_STATUS_TEXT[status]}
            </Tag>
          </div>
        );
      },
    },
  ];
  const navigate = useNavigate();
  const rowLink = (record) => ({
    onClick: () => {
      // Redirect to the specific route based on the record's Id
      navigate(`/reported_agencies/${record.id}`);
    },
  });

  const handlePagination = (page, pageSize) => {
    populateReportedAgencies(page, pageSize);
  };

  const handleSearchChange = _.debounce((searchText) => {
    setCompanyName(searchText);
    handleGetNewFuzzySearch(searchText);
  }, 1000);
  return (
    <>
      <Modal
        centered
        width={1000}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Delete report agency"
        open={deleteModalOpen}
        okType={'danger'}
        okText={isLoading ? <Spin /> : 'Confirm'}
        okButtonProps={{
          loading: confirmLoadingDelete,
        }}
        onOk={handleDelete}
        onCancel={handleCancelDelete}
      >
        Are you sure to delete {recordTable.company_name}?
      </Modal>

      {/* Add new report as an Agency modal */}
      {open && (
        <Modal
          title="Add Reported Agency"
          open={open}
          onCancel={handleCancel}
          okText="Add New Report"
          footer={
            <>
              <Button
                disabled={!companyName}
                onClick={async () => {
                  await handleOk(
                    getValues().fuzzySearch,
                    getValues()?.country || 'United Kingdom'
                  );
                  populateReportedAgencies();
                }}
              >
                Submit
              </Button>
            </>
          }
        >
          <div className="mt-6">
            <Form.Item
              className="m-0"
              label="Agency Name"
              name="agencyName"
              // required={true}
            >
              <Input
                required={true}
                placeholder="Fill the Agency Name"
                onChange={(ev) => {
                  const agencyName = ev.target.value;
                  if (agencyName) handleSearchChange(agencyName);
                }}
              />
            </Form.Item>
            <Form.Item className="my-2" label="Country Name" name="country">
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    defaultValue={{
                      label: 'United Kingdom',
                      value: 'United Kingdom',
                    }}
                    showSearch
                    placeholder="Select the country"
                    options={countriesSelectItems}
                  />
                )}
              ></Controller>
            </Form.Item>
            {isLoadingNewListAgency ? (
              <Spin
                style={{ position: 'inherit' }}
                className="m-auto py-6 mb-6"
                tip={`We are looking for the similar agencies that might be the same as ${companyName}`}
              >
                <div className="content" />
              </Spin>
            ) : (
              <>
                <p className="mb-2 mt-4">{`We have found some similar agencies that might be the same as the Agency you mentioned. Would you like to report these as a agency?`}</p>
                <Form.Item label="" name="fuzzySearch">
                  <Controller
                    name="fuzzySearch"
                    control={control}
                    render={({ field }) => (
                      <Select
                        loading={isLoadingNewListAgency}
                        // labelInValue
                        mode="multiple"
                        // onSearch={(searchText) => {
                        //   if (searchText) handleSearchChange(searchText);
                        // }}
                        notFoundContent={
                          isLoadingNewListAgency ? <Spin size="small" /> : ''
                        }
                        {...field}
                        options={Array.from(
                          new Set(listAgencyName.map((item) => item.trim()))
                        )
                          .filter((item) => item !== '')
                          .map((option) => ({
                            value: option.trim(),
                            label: option.trim(),
                          }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            ?.toLowerCase()
                            ?.indexOf(inputValue?.toLowerCase()) !== -1
                        }
                      />
                    )}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </Modal>
      )}

      {/* Bullk report modal */}
      {openBulkUpdateModal && (
        <Modal
          width={1000}
          title="Bulk Reported Agency"
          open={openBulkUpdateModal}
          onCancel={closeBulkUpdateModal}
          footer={
            <>
              <Button onClick={closeBulkUpdateModal}>Close</Button>
            </>
          }
        >
          <ReportedAgenciesBulkUpdate
            populateReportedAgencies={populateReportedAgencies}
            total={reportedAgencies?.result?.totalCount}
          />
        </Modal>
      )}

      {/* Export reported agencies by GPT/ User modal */}
      {openExportedModal && (
        <Modal
          width={500}
          title="Export Reported Agencies"
          open={openExportedModal}
          onCancel={closeExportedModal}
          footer={null}
        >
          <ExportedReportModal closeExportedModal={closeExportedModal} />
        </Modal>
      )}

      {/* Export reported agencies by GPT/ User modal */}
      {openImportModal && (
        <Modal
          width={500}
          title="Import Reported Agencies"
          open={openImportModal}
          onCancel={closeImportModal}
          footer={null}
        >
          <ImportReportModal
            closeImportModal={closeImportModal}
            populateReportedAgencies={populateReportedAgencies}
          />
        </Modal>
      )}

      <div className="grid grid-cols-10 gap-x-2 items-center">
        <Input.Search
          className="rounded-search col-span-5"
          placeholder="Search"
          allowClear
          enterButton="Search"
          defaultValue={searchName}
          size="middle"
          onSearch={(value) => {
            setPage(1);
            setSearchName(value);
          }}
        />
        <Dropdown
          menu={{
            items: itemStatus,
            onClick: (option) => {
              setStatus(option.key);
            },
          }}
          trigger={['click']}
          arrow
          placement="bottomCenter"
        >
          <div className="flex items-center justify-center custom-tag cursor-pointer">
            <Tag
              icon={REPORT_STATUS_ICON[status]}
              color={REPORT_STATUS_COLOR[status]}
            >
              {REPORT_STATUS_TEXT[status]}
            </Tag>
          </div>
        </Dropdown>

        <Button
          className="col-span-1 col-end-none h-full"
          icon={<ImportOutlined />}
          onClick={() => {
            showImportModal();
          }}
        >
          Import
        </Button>
        <Button
          className="col-span-1 col-end-none h-full"
          icon={<ExportOutlined />}
          onClick={() => {
            showExportedModal();
          }}
        >
          Export
        </Button>
        <Button
          className="col-span-1 col-end-none h-full"
          icon={<OrderedListOutlined />}
          onClick={() => {
            showBulkUpdateModal();
          }}
        >
          Bulk report
        </Button>
        <Button
          danger
          className="col-span-1 col-end-none h-full"
          icon={<NotificationOutlined className="text-red-400" />}
          onClick={() => {
            reset();
            showModal();
          }}
        >
          Add New Report
        </Button>
      </div>
      <div className="search-table-new-design-container">
        <Table
          dataSource={reportedAgencies.result.data}
          columns={[
            ...columns,
            ...(userRole?.keyCode === SUPER_ADMIN
              ? [
                  {
                    title: 'Action',
                    dataIndex: 'id',
                    key: 'id',
                    align: 'center',
                    render: (id, record) => {
                      return (
                        <div className="flex items-center justify-center gap-2">
                          {/* {record?.status === REPORT_STATUS.PENDING && ( */}
                          <>
                            <Tooltip title="Approve this report">
                              <Button
                                disabled={actionLoading}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleUpdateStatus(
                                    id,
                                    REPORT_STATUS.APPROVED
                                  );
                                }}
                                icon={
                                  <CheckOutlined className="text-green-600 font-medium" />
                                }
                              ></Button>
                            </Tooltip>
                            <Tooltip title="Reject this report">
                              <Button
                                disabled={actionLoading}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleUpdateStatus(
                                    id,
                                    REPORT_STATUS.REJECTED
                                  );
                                }}
                                icon={
                                  <CloseOutlined className="text-red-600 font-medium" />
                                }
                              ></Button>
                            </Tooltip>
                          </>
                          {/* )}*/}
                          <Button
                            danger
                            type="link"
                            icon={<DeleteOutlined />}
                            onClick={async (e) => {
                              e.stopPropagation();
                              setRecordTable(record);
                              setDeleteModalOpen(true);
                            }}
                          />
                        </div>
                      );
                    },
                  },
                ]
              : []),
          ]}
          rowKey={(record) => record.id}
          className="custom-table"
          style={{ marginTop: '20px' }}
          onRow={rowLink}
          rowClassName="custom-row"
          pagination={false}
          loading={isLoading}
        />
        <Flex justify="center">
          <Pagination
            className="mt-3"
            defaultCurrent={reportedAgencies?.result?.currentPage}
            defaultPageSize={pageSize}
            total={reportedAgencies?.result?.totalCount}
            showSizeChanger
            onChange={handlePagination}
          />
        </Flex>
      </div>
    </>
  );
};

export default ReportedAgenciesTable;
