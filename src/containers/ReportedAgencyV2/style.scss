.import-reported-agencies-container {
  .ant-upload.ant-upload-drag {
    background-color: #fff;
    min-height: 280px;
    display: flex;
    align-items: center;
    border-color: #c6d0dc;
    border-radius: 10px;
    .ant-upload-drag-icon {
      svg,
      i {
        color: #adb4d2;
      }
    }
  }
  .sDash_import-inner {
    .ant-upload-text {
      font-size: 20px;
      font-weight: 500;
    }
    .ant-upload-hint {
      margin-left: 4px;
      span {
        color: black;
      }
    }
    .ant-upload-list {
      .ant-upload-list-item {
        background-color: #fff;
        border-color: black;
      }
    }
  }
  // .ant-upload-wrapper
  //   .ant-upload-list
  //   .ant-upload-list-item-error
  //   .ant-upload-icon
  //   .anticon,
  // .ant-upload-list-item-name {
  //   color: #c6d0dc !important;
  // }

  // .ant-tooltip-arrow, .ant-tooltip-arrow{
  //   display: none !important;
  // }
}
