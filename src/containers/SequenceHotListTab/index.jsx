import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  Avatar,
  Button,
  Dropdown,
  Image,
  Space,
  Spin,
  Table,
  Tooltip,
} from 'antd';
import {
  EllipsisOutlined,
  EyeOutlined,
  GroupOutlined,
  SearchOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import _ from 'lodash';
import moment from 'moment';

import ViewListContacts from './ViewContacts';

import { searchBullhornData } from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitePagination';
import Search from 'antd/es/input/Search';
import zileoLogo from '../../assets/img/welcome/logo.png';
import bullhorLogo from '/logo_bull.webp';
import handleRenderTime from '../../function/handleRenderTime';
import { COMMON_STRINGS } from '../../constants/common.constant';
import { useNavigate } from 'react-router-dom';

export const columns = [
  {
    title: COMMON_STRINGS.NAME,
    key: 'name',
    dataIndex: 'name',
    // align: 'center',
    width: '30%',
    render: (text, record) => (
      <div className="flex gap-4">
        <Image className="!w-12" src={bullhorLogo} />
        <div className="w-full">
          <div
            className="text-lg font-semibold line-clamp-1"
            title={record?.name}
          >
            {record?.name}
          </div>
          <div>
            <span
              className="text-xs font-medium text-[#b6b2b2] line-clamp-2"
              title={record?.description}
            >
              {record?.description || 'There are no description.'}
            </span>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: 'Count',
    key: 'clientContacts',
    dataIndex: 'clientContacts',
    align: 'center',
    render: (clientContacts, record) => {
      return (
        <Tooltip title={`${clientContacts?.total} contact(s)`}>
          <div className="w-full flex items-center justify-center">
            <span className="h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700">
              {clientContacts?.total || 0}
            </span>
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: 'Added At',
    key: 'dateAdded',
    dataIndex: 'dateAdded',
    align: 'center',
    render: (text) => {
      return (
        <Tooltip title={`${handleRenderTime(text)}`}>
          <div className="w-full flex items-center justify-center font-medium">
            {handleRenderTime(text)}
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: 'Contacts',
    key: 'clientContacts',
    dataIndex: 'clientContacts',
    align: 'center',
    render: (contacts, item) => {
      return (
        <div>
          {item?.clientContacts?.total > 0 ? (
            <Avatar.Group
              max={{
                count: 5,
                style: {
                  color: '#f56a00',
                  backgroundColor: '#fde3cf',
                },
              }}
            >
              {item?.clientContacts?.data?.slice(0, 4)?.map((user, index) => (
                <Tooltip title={`${user?.firstName}`} placement="top">
                  <Avatar
                  // icon={<UserOutlined />}
                  >
                    {Array.from(user?.firstName || 'a')[0]}
                  </Avatar>
                </Tooltip>
              ))}
              <Avatar className="bg-cyan-600">
                <span className="font-semibold">{`+${parseInt(item?.clientContacts?.total) - 5}`}</span>
              </Avatar>
            </Avatar.Group>
          ) : (
            <div className="py-1 italic text-[#9299B8]">
              There are no user found on this list
            </div>
          )}
        </div>
      );
    },
  },
];

const SequenceHotListTab = () => {
  const navigate = useNavigate();
  // useState are
  const [hotListData, setHotListData] = useState([]);
  const [rawHotListData, setRawHotListData] = useState([]);
  const [loadingHotList, setLoadingHotList] = useState(true);

  const handleHotListSearch = async () => {
    try {
      const { data } = await searchBullhornData('Tearsheet')(0, 1000);
      setHotListData(false);
      if (data?.result?.length > 0) {
        const dataTemp = [...data?.result];
        setHotListData([...dataTemp]);
        setRawHotListData([...dataTemp]);
        console.log('res: ', res);
      }
    } catch (error) {
      setLoadingHotList(false);
    }
  };

  useEffect(() => {
    handleHotListSearch('');
  }, []);

  const columns = [
    {
      title: COMMON_STRINGS.NAME,
      key: 'name',
      dataIndex: 'name',
      // align: 'center',
      width: '30%',
      render: (text, record) => (
        <div className="flex gap-4">
          <Image className="!w-12" src={bullhorLogo} />
          <div className="w-full">
            <div className="text-lg font-semibold">{record?.name}</div>
            <div>
              <span
                className="text-xs font-medium text-[#b6b2b2] line-clamp-2"
                title={record?.description}
              >
                {record?.description || 'There are no description.'}
              </span>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Count',
      key: 'clientContacts',
      dataIndex: 'clientContacts',
      align: 'center',
      render: (clientContacts, record) => {
        return (
          <Tooltip title={`${clientContacts?.total} contact(s)`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-xs h-[40px] w-[40px] flex rounded-full font-semibold justify-center items-center border-2 border-cyan-500 text-cyan-700 p-2">
                {clientContacts?.total || 0}
              </span>
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Added At',
      key: 'dateAdded',
      dataIndex: 'dateAdded',
      align: 'center',
      render: (text) => {
        return (
          <Tooltip title={`${handleRenderTime(text)}`}>
            <div className="w-full flex items-center justify-center font-medium">
              {handleRenderTime(text)}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Contacts',
      key: 'clientContacts',
      dataIndex: 'clientContacts',
      align: 'center',
      render: (contacts, item) => {
        return (
          <div>
            {item?.clientContacts?.total > 0 ? (
              item?.clientContacts?.total > 5 ? (
                <Avatar.Group
                  max={{
                    count: 5,
                    style: {
                      color: '#f56a00',
                      backgroundColor: '#fde3cf',
                    },
                  }}
                >
                  {item?.clientContacts?.data
                    ?.slice(0, 4)
                    ?.map((user, index) => (
                      <Tooltip title={`${user?.firstName}`} placement="top">
                        <Avatar
                        // icon={<UserOutlined />}
                        >
                          {Array.from(user?.firstName || 'a')[0]}
                        </Avatar>
                      </Tooltip>
                    ))}
                  <Avatar className="bg-cyan-600">
                    <span className="font-semibold">{`+${parseInt(item?.clientContacts?.total) - 5}`}</span>
                  </Avatar>
                </Avatar.Group>
              ) : (
                <div>
                  {item?.clientContacts?.data?.map((user, index) => (
                    <Tooltip title={`${user?.firstName}`} placement="top">
                      <Avatar
                      // icon={<UserOutlined />}
                      >
                        {Array.from(user?.firstName || 'a')[0]}
                      </Avatar>
                    </Tooltip>
                  ))}
                </div>
              )
            ) : (
              <div className="py-1 italic text-[#9299B8]">
                There are no user found on this list
              </div>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <>
        <div className="flex justify-end w-full">
          <div className="flex items-center gap-3">
            <Search
              allowClear
              className="customize-search-container"
              placeholder="Search..."
              loading={loadingHotList}
              onSearch={(text) => {
                const newHotListData = rawHotListData?.filter(
                  (item) =>
                    item?.name
                      ?.trim()
                      .toLowerCase()
                      ?.includes(text.trim().toLowerCase()) ||
                    item?.description
                      ?.trim()
                      .toLowerCase()
                      ?.includes(text.trim().toLowerCase())
                );
                setHotListData([...newHotListData]);
              }}
              enterButton={<SearchOutlined />}
              size="middle"
            />
          </div>
        </div>

        <div className="search-table-new-design-container mt-5">
          <Table
            rowKey={(record) => record?.id}
            loading={loadingHotList}
            dataSource={hotListData}
            columns={columns}
            className="custom-table"
            onRow={(record, rowIndex) => {
              return {
                onClick: (e) => {
                  e.stopPropagation();
                  navigate(`/hot_list/${record.id}`);
                },
                style: { cursor: 'pointer' },
              };
            }}
          />
        </div>
      </>
    </div>
  );
};

export default SequenceHotListTab;
