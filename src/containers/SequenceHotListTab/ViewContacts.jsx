import { useEffect, useState } from 'react';
import { Avatar, Empty, Modal, Pagination, Table } from 'antd';
import { searchBullhorn } from '../../services/bullhorn';

export const STATUS_COLOR = {
  'Live Lead': 'border-[#9ce9cf] bg-[#e6fff8] text-[#00996d]',
  Prospect: 'border-[#e6e9ed] bg-[#eff1f3] text-[#674677]',
  Active: 'border-[#001F3F] bg-[#7CF5FF] text-[#3A6D8C]',
  Passive: 'border-[#87A2FF] bg-[#C4D7FF] text-[#87A2FF]',
  DNC: 'border-[#D91656] bg-[#EE66A6] text-[#D91656]',
  Archive: 'border-[#624E88] bg-[#8967B3] text-[#624E88]',
  Private: 'border-[#227B94] bg-[#78B7D0] text-[#227B94]',
};

const ViewContacts = ({ handleClose, hotList }) => {
  const [dataListContact, setDataListContact] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(false);

  console.log(hotList);

  const handleGetContact = async () => {
    setLoading(true)
    const { data } = await searchBullhorn(
      'ClientContact',
      currentPage,
      10,
      '',
      '',
      '',
      '',
      hotList?.id
    );
    setDataListContact(data.result);
    setLoading(false)
  };

  const columnJogLogs = [
    {
      title: 'No'.toUpperCase(),
      dataIndex: 'name',
      render: (text, record, index) => (
        <span>{index * (currentPage + 1) + 1}</span>
      ),
    },
    {
      title: 'Name'.toUpperCase(),
      dataIndex: 'name',
      key: 'name',
      render: (text, record, index) => (
        <div className="flex items-center gap-3">
          <Avatar>{Array.from(text ?? "admin")[0]}</Avatar>
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-[#7b838b] text-xs italic">
              {record?.occupation}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'email'.toUpperCase(),
      dataIndex: 'email',
      key: 'email',
      render: (text, record, index) => (
        <a href={`mailto:${record?.email}`}>{record?.email}</a>
      ),
    },
    // {
    //   title: 'fax'.toUpperCase(),
    //   dataIndex: 'fax',
    //   key: 'fax',
    //   render: (text, record, index) => <span>{record?.fax}</span>,
    // },
    {
      title: 'Address'.toUpperCase(),
      dataIndex: 'address',
      key: 'address',
      render: (text, record, index) => (
        <span>{record?.address?.address1 || ''}</span>
      ),
    },
    {
      title: 'phone'.toUpperCase(),
      dataIndex: 'phone',
      key: 'phone',
      render: (text, record, index) => (
        <a href={`tel:${record?.phone}`}>{record?.phone}</a>
      ),
    },
    {
      title: 'status'.toUpperCase(),
      dataIndex: 'status',
      key: 'status',
      render: (text, record, index) => (
        <span className={`px-2 py-1 ${STATUS_COLOR[record?.status]}`}>
          {record?.status}
        </span>
      ),
    },
  ];

  console.log(hotList);

  useEffect(() => {
    handleGetContact();
  }, [hotList, currentPage]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <Modal
      width={1500}
      // style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
      className='top-8'
      title={
        ''
        // <div className='border-b pb-5 pt-2'>
        //   <div>View Contacts</div>
        // </div>
      }
      open={true}
      onCancel={handleClose}
      footer={false}
    >
      <div className="search-table-new-design-container">
        <Table
          columns={columnJogLogs}
          dataSource={dataListContact || []}
          locale={{
            emptyText: <Empty description="No logs found" className="w-full" />,
          }}
          pagination={false}
          loading={loading}
        />
        <Pagination
          className="mt-3"
          // defaultCurrent={listEmployeePagination.page}
          total={hotList?.clientContacts.total}
          showSizeChanger={false}
          onChange={handlePageChange}
        />
      </div>
    </Modal>
  );
};

export default ViewContacts;
