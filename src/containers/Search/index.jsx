/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useContext } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  theme,
  Select,
  Spin,
  DatePicker,
  message,
  Checkbox,
} from 'antd';
import { v4 as uuid } from 'uuid';
import { useNavigate } from 'react-router-dom';
import { getJobs, startScraper } from '../../services/jobs';
import Table from '../../components/SearchTable';
import SearchContext from '../../context';
import { keywordsData, citySelData } from './const';
import './style.css';

function Search() {
  const navigate = useNavigate();
  const context = useContext(SearchContext);
  const [messageApi, contextHolder] = message.useMessage();
  const { savedSearchData, setSavedSearchData } = context;
  const [isSearchLoading, setSearchLoading] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [viewData, setViewData] = useState([]);
  const [customKeywords, setCustomKeywords] = useState([]);
  const { RangePicker } = DatePicker;
  const [searchData, setSearchData] = useState({
    id: '',
    keywords: [],
    distance: 0,
    companies: '',
    agencies: [],
    jobType: [],
    maxSalary: '',
    minSalary: '',
    country: '',
    city: '',
    posted: ['', ''],
    pageSize: 10,
    page: 1,
    data: [],
    jobTitle: '',
    totalCount: 0,
    status: false,
    jobboards: [
      'technojobs',
      'monster',
      'cv-library',
      'reed',
      'indeed',
      'linkedin',
      'totaljobs',
    ],
  });

  useEffect(() => {
    if (localStorage.getItem('savedSearch') == null)
      localStorage.setItem('savedSearch', JSON.stringify([]));
    setSavedSearchData(JSON.parse(localStorage.getItem('savedSearch')));
    if (localStorage.getItem('keywords') == null)
      localStorage.setItem('keywords', JSON.stringify(keywordsData));
    setCustomKeywords(JSON.parse(localStorage.getItem('keywords')));
  }, []);

  useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200 ? setShowFilter(false) : setShowFilter(true)
    );
  }, []);

  const handleOk = () => {
    setOpen(false);
    setSearchLoading(true);
    const { keywords, city, jobTitle } = searchData;
    startScraper({
      status: 'on',
      keywords: keywords.join(','),
      location: city,
      jobTitleQuery: jobTitle,
    })
      .then((res) => {
        setTimeout(() => {
          getJobs({ searchData }).then((data) => {
            let searchId = uuid();
            setSearchLoading(false);
            const jobData = data.data.jobs;
            setSearchData({
              ...searchData,
              data: jobData,
              totalCount: data.data.totalCount,
              id: searchId,
              status: true,
            });

            setViewData([
              {
                ...searchData,
                data: jobData,
                totalCount: data.data.totalCount,
                id: searchId,
                status: true,
              },
            ]);
            setSavedSearchData([
              ...savedSearchData,
              {
                ...searchData,
                data: jobData,
                totalCount: data.data.totalCount,
                id: searchId,
                status: true,
              },
            ]);
            localStorage.setItem(
              'savedSearch',
              JSON.stringify([
                ...savedSearchData,
                {
                  ...searchData,
                  data: jobData,
                  totalCount: data.data.totalCount,
                  id: searchId,
                  status: true,
                },
              ])
            );
            navigate(`/search/${searchId}`);
          });
        }, 10000); // Delay of 1 second (10000 milliseconds)
      })
      .catch((err) => {});
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setSearchData({ ...searchData, [name]: value });
  };

  const { token } = theme.useToken();
  const [open, setOpen] = useState(false);
  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const containerStyle = {
    position: 'relative',
    height: '84vh',
    padding: 20,
    overflow: 'overlay',
    background: token.colorFillAlter,
    border: `1px solid ${token.colorBorderSecondary}`,
  };

  const onDateChange = (date, dateString) => {
    setSearchData({ ...searchData, posted: dateString });
  };

  const onCityChange = (value) => {
    setSearchData({ ...searchData, city: value });
  };

  if (isSearchLoading)
    return (
      <div className="text-left mt-[250px]">
        <Spin
          tip="Please wait... it may take several seconds to get new job data..."
          size="large"
        >
          <div className="content" />
        </Spin>
      </div>
    );

  const onKeywordsChange = (value) => {
    if (
      value.length != 0 &&
      customKeywords.filter(
        (item) => item.value === value[value.length - 1].toLowerCase()
      ).length == 0
    ) {
      setCustomKeywords([
        ...customKeywords,
        {
          value: value[value.length - 1].toLowerCase(),
          label: value[value.length - 1],
        },
      ]);
      localStorage.setItem(
        'keywords',
        JSON.stringify([
          ...customKeywords,
          {
            value: value[value.length - 1].toLowerCase(),
            label: value[value.length - 1],
          },
        ])
      );
      setSearchData({
        ...searchData,
        keywords: value.map((item) => item.toLowerCase()),
      });
    } else {
      setSearchData({
        ...searchData,
        keywords: value.map((item) => item.toLowerCase()),
      });
    }
  };

  const onJobBoardChange = (e) => {
    if (
      searchData.jobboards.filter((item) => item == e.target.value).length != 0
    )
      setSearchData({
        ...searchData,
        jobboards: searchData.jobboards.filter(
          (item) => item != e.target.value
        ),
      });
    else
      setSearchData({
        ...searchData,
        jobboards: [...searchData.jobboards, e.target.value],
      });
  };

  return (
    <>
      <div className="flex justify-between mt-10 mx-5 mb-2">
        {contextHolder}
        {open ? (
          <Tooltip title="Close" className="flex align-middle">
            <Button
              className="bg-[#0080FE] text-white font-[PoppinsSemiBold]"
              type="primary"
              onClick={onClose}
            >
              x Close
            </Button>
          </Tooltip>
        ) : (
          <Button
            className="bg-[#0080FE] text-white font-[PoppinsSemiBold]"
            type="primary"
            onClick={showDrawer}
          >
            + New Search
          </Button>
        )}
      </div>
      <div style={containerStyle}>
        <div className=" bg-gray-800">
          <Drawer
            title="Job Search Filter"
            placement="top"
            closable={false}
            onClose={onClose}
            open={open}
            getContainer={false}
            height={550}
            zIndex={10}
            mask={false}
          >
            <div className="grid grid-cols-2 gap-8">
              <div className="sm:col-span-1 col-span-2 mt-3">
                <div className="pt-8">
                  <label
                    htmlFor="jobTitle"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Search Name
                  </label>
                  <div className="mt-2">
                    <input
                      placeholder="Type Search Name"
                      type="text"
                      name="jobTitle"
                      id="jobTitle"
                      onChange={handleInputChange}
                      autoComplete="given-name"
                      className="block w-full rounded-md border-0 px-2 py-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </div>
                </div>
                <div className="pt-8">
                  <label
                    htmlFor="jobTitle"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Keyword
                  </label>
                  <div className="mt-2">
                    <Select
                      mode="tags"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Select your keyword"
                      onChange={onKeywordsChange}
                      options={customKeywords}
                    />
                  </div>
                </div>
                <div className="pt-8 w-full">
                  <label
                    htmlFor="city"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Location (Leave blank to search the whole of UK)
                  </label>
                  <div className="mt-2">
                    <Select
                      mode="tags"
                      style={{
                        width: '100%',
                      }}
                      placeholder="Select City"
                      onChange={onCityChange}
                      options={citySelData}
                    />
                  </div>
                </div>
              </div>
              <div className="sm:col-span-1 col-span-2">
                <div className="pt-[40px] w-full">
                  <label
                    htmlFor="jobTitle"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Posted Within (Leave blank to search the whole of UK)
                  </label>
                  <div className="mt-3 w-full">
                    <RangePicker
                      className="w-full"
                      picker="month"
                      onChange={onDateChange}
                      placement="topRight"
                    />
                  </div>
                </div>

                <div className="pt-[30px]">
                  <label
                    htmlFor="salary"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Salary (Leave blank to search the whole of UK)
                  </label>
                  <div className="mt-3 flex justify-between">
                    <input
                      placeholder="Type Min Salary"
                      type="text"
                      name="minSalary"
                      id="minsalary"
                      onChange={handleInputChange}
                      autoComplete="given-name"
                      className="mr-2 block w-full rounded-md border-0 px-2 py-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                    <input
                      placeholder="Type Max Salary"
                      type="text"
                      name="maxSalary"
                      id="maxsalary"
                      onChange={handleInputChange}
                      autoComplete="given-name"
                      className="ml-2 block w-full rounded-md border-0 px-2 py-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </div>
                </div>

                <div className="pt-2 col-span-2">
                  <label
                    htmlFor="jobTitle"
                    className="block text-sm font-medium leading-6 text-gray-90"
                  >
                    Job Boards
                  </label>
                  <div className="">
                    <div className="mt-[18px] grid sm:grid-cols-4 grid-cols-3 justify-around">
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="technojobs"
                            value="technojobs"
                            checked={searchData.jobboards.includes(
                              'technojobs'
                            )}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="recuritment"
                            className="font-medium text-gray-90"
                          >
                            TechnoJobs
                          </label>
                        </div>
                      </div>{' '}
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="monster"
                            value="monster"
                            checked={searchData.jobboards.includes('monster')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="directemployer"
                            className="font-medium text-gray-90"
                          >
                            Monster
                          </label>
                        </div>
                      </div>{' '}
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="cv-library"
                            value="cv-library"
                            checked={searchData.jobboards.includes(
                              'cv-library'
                            )}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            CV Library
                          </label>
                        </div>
                      </div>{' '}
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="reed"
                            value="reed"
                            checked={searchData.jobboards.includes('reed')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            Reed
                          </label>
                        </div>
                      </div>{' '}
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="indeed"
                            value="indeed"
                            checked={searchData.jobboards.includes('indeed')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            Indeed
                          </label>
                        </div>
                      </div>
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="linkedin"
                            value="linkedin"
                            checked={searchData.jobboards.includes('linkedin')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            Linkedin
                          </label>
                        </div>
                      </div>
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="totaljobs"
                            value="totaljobs"
                            checked={searchData.jobboards.includes('totaljobs')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            Totaljobs
                          </label>
                        </div>
                      </div>
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="jobsite"
                            value="jobsite"
                            checked={searchData.jobboards.includes('jobsite')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            JobSite
                          </label>
                        </div>
                      </div>
                      <div className="col-span-1 flex gap-x-1">
                        <div className="flex items-center">
                          <Checkbox
                            name="seek"
                            value="seek"
                            checked={searchData.jobboards.includes('seek')}
                            onChange={onJobBoardChange}
                          ></Checkbox>
                        </div>
                        <div className="text-sm leading-6">
                          <label
                            htmlFor="graduate"
                            className="font-medium text-gray-90"
                          >
                            Seek
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-[10px] flex justify-between col-span-full">
                <button
                  onClick={handleOk}
                  className="rounded-md h-[35px] bg-[#0080FE] w-full text-lg font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Save
                </button>
              </div>
            </div>
          </Drawer>
        </div>
        <div className="overflow-hidden">
          <Table
            jobData={savedSearchData}
            setSavedSearchData={setSavedSearchData}
          />
        </div>
      </div>
    </>
  );
}

export default Search;
