import cityData from './gb.json';

export const postedWithin = [
  {
    value: '1 day',
    label: '1 Day',
  },
  {
    value: '3 days',
    label: '3 Days',
  },
  {
    value: '1 week',
    label: '1 Week',
  },
  {
    value: '1 month',
    label: '1 Month',
  },
  {
    value: '2 months',
    label: '2 Months',
  },
  {
    value: '4 months',
    label: '4 Months',
  },
  {
    value: '6 months',
    label: '6 Months',
  },
];

export const keywordsData = [
  {
    value: 'html',
    label: 'HTML',
  },
  {
    value: 'css',
    label: 'CSS',
  },
  {
    value: 'javascript',
    label: 'JavaScript',
  },
  {
    value: 'typpescript',
    label: 'TypeScript',
  },
  {
    value: 'react',
    label: 'React',
  },
  {
    value: 'vue',
    label: 'Vue',
  },
  {
    value: 'shop assistant',
    label: 'Shop Assistant',
  },
];

export const citySelData = cityData.map((item) => ({
  value: item.city,
  label: item.city,
}));
