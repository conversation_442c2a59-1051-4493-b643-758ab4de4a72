import JobList from '../../components/JobListV2/JobList';
import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getJobSame } from '../../services/jobs';
import { Input, Spin } from 'antd';
import LoadingBar from '../SyncSearchJobListV2/LoadingBar';

async function getAll(page, search) {
  const [searchData] = await Promise.all([getJobSame(10, page, search)]);
  return {
    searchData: searchData.data.result,
  };
}

function JobSame() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const queryClient = useQueryClient();

  const { data, isFetching } = useQuery({
    queryKey: ['get-job-same', page, search],
    queryFn: () => getAll(page, search),
    enabled: true,
  });

  return (
    <div>
      <div className="grid grid-cols-8 gap-x-2 items-center">
        <Input.Search
          className="rounded-search col-span-7 md:col-span-8"
          placeholder="Find jobs"
          allowClear
          enterButton="Search"
          defaultValue={search}
          size="middle"
          onSearch={(value) => {
            setPage(1);
            setSearch(value);
          }}
        />
      </div>

      {/* <div className="mt-5 space-y-7">
          <LoadingBar loading={isFetching || isLoading}/>
        </div> */}
      {isFetching && (
        <div className="flex w-full justify-center items-center h-96">
          <Spin size='large' />
        </div>
      )}

      {!isFetching && (
        <JobList
          searchData={data?.searchData}
          isSearchLoading={isFetching}
          populateSearchData={() =>
            queryClient.invalidateQueries('get-job-same')
          }
          populatePinnedJobsData={() =>
            queryClient.invalidateQueries('get-job-same')
          }
          isFromJobSame={true}
          pagination={data?.searchData}
          page={parseInt(data?.searchData?.currentPage || 1)}
          setPage={(page) => {
            setPage(page);
          }}
          pageSize={parseInt(data?.searchData?.pageSize) || 10}
        />
      )}
    </div>
  );
}

export default JobSame;
