import React from 'react';
import { Button, Input, Popconfirm, Tabs, Modal, notification } from 'antd';
import './style.css';
import UserProfilePage from './UserProfilePage';
import {
  AuditOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  FormOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useState } from 'react';
import { getUserDetail, updateUserPassword } from '../../services/users';
import useEditUser from '../../hooks/useEditUser';
import MailBox from '../Settings/MailBox';
import Sequence from '../Settings/Sequence';
import { useViewAs } from '../../store/viewAs';
import LinkedIn from '../Settings/LinkedIn';
const { Password } = Input;

function EditUserManagement() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { setViewAs } = useViewAs();

  const [password, setPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [opendPasswordEditModal, setOpendPasswordEditModal] = useState(false);

  const queryParams = new URLSearchParams(location.search);
  const extractedCompanyId = queryParams.get('companyId');

  const showPasswordEditModal = () => {
    setPassword('');
    setOpendPasswordEditModal(true);
  };
  const closePasswordEditModal = () => setOpendPasswordEditModal(false);

  const handleUpdatePassword = async () => {
    if (!id) return;
    setPasswordLoading(true);

    await updateUserPassword(id, password)
      .then((res) => {
        notification.success({
          description: 'Password changed!',
        });
        closePasswordEditModal();
      })
      .catch((err) => {
        notification.error({
          description: 'Password update failed!',
        });
      })
      .finally(() => {
        setPasswordLoading(false);
      });

    console.log('new password: ', password);
  };

  const items = [
    {
      label: 'Profile',
      key: 'profile',
      children: <UserProfilePage />,
    },
    {
      label: 'Mail Box',
      key: 'mailbox',
      children: (
        <div className="bg-white flex justify-center">
          <MailBox userIdProp={id} />
        </div>
      ),
    },
    {
      label: 'Sequence',
      key: 'sequence',
      children: (
        <div className="bg-white flex justify-center">
          <Sequence userIdProp={id} />
        </div>
      ),
    },
    // {
    //   label: 'Scheduler',
    //   key: 'scheduler',
    //   children: (
    //     <div className="bg-white flex justify-center py-4">
    //       {/* <SchedulingEditor /> */}
    //       <Button
    //       type='primary'
    //         className="Montserrat"
    //         onClick={async () => {
    //           setPasswordLoading(true);
    //           const { data } = await getUserDetail(id);
    //           setViewAs({ profileUser: data });
    //           setPasswordLoading(false);
    //           navigate('/settings?scheduler=1');
    //           window.location.reload(); // reload page to get new instance data
    //         }}
    //       >
    //         {`View as this user to see Scheduler Editor.`}
    //       </Button>
    //     </div>
    //   ),
    // },
    {
      label: 'LinkedIn',
      key: 'linkedIn',
      children: (
        <div className="bg-white flex justify-center py-4">
          <LinkedIn userIdProp={id} />
        </div>
      ),
    },
  ];

  return (
    <div>
      <div>
        <div className="flex justify-between text-2xl font-semibold">
          <span className="Montserrat">User Details</span>
          <div className="flex gap-3">
            <Button
              className="Montserrat  flex items-center "
              type="primary"
              onClick={showPasswordEditModal}
            >
              <AuditOutlined /> <span>Update Password</span>
            </Button>
            <Button
              className="bg-white  flex items-center "
              onClick={() => {
                if (extractedCompanyId) {
                  navigate(`/user-management/company/${extractedCompanyId}`);
                } else {
                  navigate(-1);
                }
              }}
              icon={<LeftOutlined />}
            >
              Back
            </Button>
          </div>
        </div>
      </div>
      <Tabs
        // onChange={onChange}
        className="customized-tabs"
        type="card"
        items={items}
      />

      {/* Password modal */}
      {opendPasswordEditModal && (
        <Modal
          title={
            <div className="flex items-center gap-1">
              <span>Update New Password</span>
              <Popconfirm
                rootClassName="hide-popconfirm-buttons"
                placement="top"
                title={''}
                description={
                  'Password should be between 8-12 letters and should include 1 number and 1 special character'
                }
                // okText="Yes"
                // cancelText="No"
              >
                <Button type="text" icon={<InfoCircleOutlined />} />
              </Popconfirm>
            </div>
          }
          open={opendPasswordEditModal}
          // onOk={closePasswordEditModal}
          onCancel={closePasswordEditModal}
          footer={
            <div className="flex gap-1 w-full justify-end">
              <Button
                loading={passwordLoading}
                className="Montserrat  flex items-center "
                type="primary"
                onClick={handleUpdatePassword}
              >
                <FormOutlined /> <span>Save</span>
              </Button>
              <Button
                disabled={passwordLoading}
                className="Montserrat  flex items-center "
                onClick={closePasswordEditModal}
              >
                <CloseOutlined /> <span>Cancel</span>
              </Button>
            </div>
          }
        >
          <div className="py-2">
            <Password
              className="py-2"
              placeholder="Input new password"
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
              value={password}
              onChange={(ev) => setPassword(ev.target.value)}
            />
          </div>
        </Modal>
      )}
    </div>
  );
}

export default EditUserManagement;
