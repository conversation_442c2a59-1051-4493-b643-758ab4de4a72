import React, { useEffect } from 'react';
import { Controller } from 'react-hook-form';
import {
  Input,
  Checkbox,
  Select,
  Button,
  Form,
  Table,
  Row,
  Col,
  AutoComplete,
  Image,
  Skeleton,
  Modal,
  Popconfirm,
  notification,
  Tabs,
  Upload,
  message,
  Avatar,
  Radio,
} from 'antd';

import {
  AuditOutlined,
  CloseOutlined,
  CrownOutlined,
  FireOutlined,
  FormOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  LockOutlined,
  MailOutlined,
  QuestionOutlined,
  SyncOutlined,
  UploadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import useEditUser from '../../hooks/useEditUser';
import { useNavigate } from 'react-router-dom';
import { validationDisableRoleCode } from '../../helpers/validationDisableRoleCode';
import { useQuery } from '@tanstack/react-query';
import {
  getUserOrganizations,
  updateUserPassword,
  uploadImage,
} from '../../services/users';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { useOnboarding } from '../../store/onboarding';
import { useState } from 'react';
import { baseURL } from '../../utils/axios';
import { getLinkS3 } from '../../services/aws';
import { imageFallbackBase64 } from './constant';

import zileoLogo from '../../assets/img/welcome/logo.png';
import userImage from '../../assets/img/avatars/user.png';
import { licenseType, SUPER_ADMIN } from '../../constants/common.constant';
import styled from 'styled-components';
import { getTimezoneList } from '../../services/nylas';
import { getLinkedinProfile } from '../../services/task';
import e from 'cors';

const LENGTH_ALL_PERMISSIONS = 18;
const ReStyledAutoComplete = styled(AutoComplete)`
  .ant-select-selector {
    width: 100%;
    border-radius: 0.375rem !important;
    padding: 0.15rem 0.75rem !important;
    font-size: 0.875rem;
    color: #1f2937;
  }
`;

const ReStyledSelect = styled(Select)`
  .ant-select-selector {
    width: 100%;
    border-radius: 0.375rem !important;
    padding: 0.15rem 0.75rem !important;
    font-size: 0.875rem;
    color: #1f2937;
  }
`;

const UserProfilePage = ({ userId = '' , setDisableStep, isOnboarding = false}) => {
  const [permissions, setPermissions] = useState([]);
  const [isReadOnly, setReadOnly] = useState(true);
  const [syncLoading, setSyncLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageId, setImageId] = useState('');
  const [tzOptions, setTZOptions] = useState([]);

  // Edit profile
  const [password, setPassword] = useState()

  //
  const {
    user,
    setUser,
    handleSubmit,
    onSubmit,
    control,
    roles,
    loading,
    setFlagSave,
    flagSave,
    setValue,
    getValues,
  } = useEditUser();

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const { setPersonalInfo } = useOnboarding();

  const userToSet = (user?.id ? user : null) || profileUser || profileUserAuth;

  const { data: listOrganization = [] } = useQuery(['LIST_ORGANIZATION'], {
    queryFn: async () => {
      if (
        userToSet?.user?.role?.keyCode === SUPER_ADMIN ||
        userToSet?.role?.keyCode === SUPER_ADMIN
      ) {
        const { data } = await getUserOrganizations();
        return data.result;
      }
      return [];
    },
  });

  const getTimezonesList = async () => {
    try {
      const { data } = await getTimezoneList();

      if (data?.result?.length > 0) {
        const tzList = data?.result?.map((item) => ({
          label: item.text,
          value: item.timezone,
          order: item.order,
        }));

        setTZOptions([...tzList]);
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const handleSyncDataFromLinkedin = async (linkedinUrl) => {
    setSyncLoading(true);
    try {
      const { data } = await getLinkedinProfile(linkedinUrl);
      setSyncLoading(false);
      if (data?.result) {
        // setValue('fullName', data?.result?.full_name);
        // setValue('jobTitle', data?.result?.headline);
        // setValue('organization', data?.result?.company_name);
        // setValue('organizationId', data?.result?.company_id);
        // setValue('avatarId', data?.result?.profile_picture_url_large);
        // setImageUrl(data?.result?.profile_picture_url_large);
        // setImageId(data?.result?.profile_picture_url_large);
        message.success('Sync data successfully!');
      } else {
        message.success('Nothing found.');
      }
      console.log('data: ', data);
    } catch (error) {
      setSyncLoading(false);
      message.error(
        'Sync data failed! Please check your LinkedIn URL or try again later.'
      );
      console.log('error: ', error);
    }
  };

  const listOriginConfig = listOrganization.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });

  const actionList = [
    { label: 'Select All', value: 'ALL' },
    { label: 'Allow read', value: 'READ' },
    { label: 'Allow write', value: 'WRITE' },
  ];

  useEffect(() => {
    const curPermissions = userToSet?.userPermissions?.flatMap((per) => {
      const perList = [];
      if (per?.allowRead) {
        perList.push(`${per?.permission?.keyCode}_READ`);
      }
      if (per?.allowWrite) {
        perList.push(`${per?.permission?.keyCode}_WRITE`);
      }
      return [...perList];
    });

    setPermissions(curPermissions);
  }, [userToSet?.userPermissions, isReadOnly]);

  const consistenceRoleName = (roleName) => {
    switch (roleName) {
      case 'Report Agency':
        return 'Report Agencies';

      case 'Job Search':
        return 'Search';

      case 'Job Lead':
        return 'My Leads';

      case 'Job Sync':
        return 'Syncs';

      case 'Email Finder':
        return 'Contact Finder';

      default:
        return roleName;
    }
  };

  const props = {
    name: 'file',
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        let formData = new FormData();
        formData.append('avatar', file);
        const res = await uploadImage(formData);
        setImageId(res?.data?.result?.data?.imgId);
        setImageUrl(res?.data?.result?.data?.url);
        onSuccess('ok');
      } catch (error) {
        console.log('error upload image: ', error);
        onError(error);
      }
    },
  };

  const fetchImageUrl = async (avatarId) => {
    try {
      const { data } = await getLinkS3(avatarId);
      setImageUrl(data);
    } catch (error) {
      // setImageUrl(userImage);
    }
  };

  useEffect(() => {
    if (!user?.avatarId) return;
    fetchImageUrl(user?.avatarId);
  }, [user?.avatarId, isReadOnly]);

  useEffect(() => {
    getTimezonesList();
  }, []);

  const handleChangePassword = async () => {
    if (!password) {
      notification.error({
        message: "Please enter a password"
      });
      return;
    }

    // For onboarding flow
    if (isOnboarding) {
      setPersonalInfo({
        password
      });

      // Enable the Continue button
      setDisableStep?.(null);

      notification.success({
        message: "Password saved successfully"
      });
    } else {
      try {
        const {data} = await updateUserPassword(userId, password);
        if (data) {
          notification.success({
            message: "Update password success!!!"
          });
        }
      } catch (error) {
        notification.error({
          message: "Failed to update password",
          description: error?.response?.data?.message || "Something went wrong"
        });
      }
    }
  }

  return (
    <div className="flex flex-col gap-5">
      <div className="font-Montserrat bg-white col-span-1 p-4">
        {loading && <Skeleton active />}
        {!loading && (
          <Form
            layout="vertical"
            className="bg-white w-full rounded-md p-6 md:p-10 flex flex-col md:flex-row gap-8 md:gap-12"
          >
            <div className="flex flex-col items-center md:items-start md:w-1/3 space-y-6">
              <div className="relative w-full flex justify-center items-center">
                <div className="flex flex-col items-center">
                  <Image
                    preview={!!imageUrl}
                    width={140}
                    className="rounded-md"
                    src={imageUrl || userImage}
                    onError={() => userImage}
                  />
                  <Upload
                    {...props}
                    rootClassName="customized-upload-component width-inherit"
                  >
                    <div className="w-full flex items-center justify-center">
                      <Button
                        type="primary"
                        className="mt-3"
                        icon={<UploadOutlined />}
                      >
                        <span className="text-xs">Upload</span>
                      </Button>
                    </div>
                  </Upload>
                </div>
              </div>

              <div className="w-full space-y-4">
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="fullName"
                  >
                    Full Name
                  </label>
                  <Controller
                    render={({ field }) => (
                      <Input
                        id="fullName"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-600"
                        {...field}
                        required
                      />
                    )}
                    name="fullName"
                    control={control}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="jobTitle"
                  >
                    Job Title
                  </label>
                  <Controller
                    render={({ field }) => (
                      <Input
                        id="jobTitle"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-600"
                        {...field}
                        required
                        placeholder='e.g. "Sales Manager"'
                      />
                    )}
                    name="jobTitle"
                    control={control}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="licenseType"
                  >
                    License Type
                  </label>
                  <div
                    className="text-cyan-600 font-semibold text-base w-full border border-cyan-600 rounded px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-cyan-600"
                    id="licenseType"
                  >
                    {user?.licenseType || licenseType.CONNECTED}
                  </div>
                </div>

                <div className="w-full grid grid-cols-2 gap-4 items-center">
                  <div
                    className="text-sm text-gray-600 font-semibold mb-1 "
                    for="disableAccount"
                  >
                    Disable Account
                  </div>
                  <Controller
                    render={({ field }) => (
                      <Radio.Group
                        block
                        options={[
                          {
                            label: 'Yes',
                            value: true,
                            style: {
                              width: '50%',
                              fontWeight: '600',
                            },
                          },
                          {
                            label: 'No',
                            value: false,
                            style: { width: '50%', fontWeight: '600' },
                          },
                        ]}
                        defaultValue={false}
                        {...field}
                        optionType="button"
                        buttonStyle="solid"
                        className="w-full"
                      />
                    )}
                    name="disableAccount"
                    control={control}
                  />
                </div>
              </div>
            </div>
            <div className="md:w-full flex flex-col text-gray-700 text-sm">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="linkedinUrl"
                  >
                    LinkedIn
                  </label>
                  <Controller
                    render={({ field }) => (
                      <Input
                        id="linkedinUrl"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-600"
                        {...field}
                        required
                        placeholder='e.g. "https://www.linkedin.com/in/username"'
                      />
                    )}
                    name="linkedinUrl"
                    control={control}
                  />
                  <Button
                    icon={<SyncOutlined />}
                    loading={syncLoading}
                    onClick={() =>
                      handleSyncDataFromLinkedin(getValues('linkedinUrl'))
                    }
                    disabled={getValues('linkedinUrl')?.length === 0}
                    className="text-sm text-cyan-600 font-semibold mt-1 hover:underline"
                    type="text"
                  >
                    SYNC DATA
                  </Button>
                </div>

                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="password"
                  >
                    Password
                    {userId && <span className="text-red-600">*</span>}
                  </label>
                  <Input
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 bg-white cursor-default"
                    id="password"
                    disabled={isOnboarding ? false : true}
                    type="password"
                    value={isOnboarding ? password : "**********"}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <Button
                    icon={<AuditOutlined />}
                    className="text-sm text-cyan-600 font-semibold mt-1 hover:underline"
                    type="text"
                    onClick={handleChangePassword}
                  >
                    {isOnboarding ? "SAVE PASSWORD" : "CHANGE PASSWORD"}
                  </Button>
                </div>

                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="email"
                  >
                    Email
                  </label>
                  <Controller
                    render={({ field }) => (
                      <Input
                        id="email"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-600"
                        {...field}
                        required
                      />
                    )}
                    name="email"
                    control={control}
                  />
                </div>

                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="username"
                  >
                    Username
                  </label>
                  <Controller
                    render={({ field }) => (
                      <Input
                        id="username"
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-1 focus:ring-cyan-600"
                        {...field}
                        required
                      />
                    )}
                    name="username"
                    control={control}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="timezone"
                  >
                    Time Zone
                  </label>
                  <Controller
                    render={({ field }) => (
                      <ReStyledSelect
                        optionFilterProp="label"
                        className="w-full"
                        showSearch
                        {...field}
                        placeholder="Select Timezone"
                        options={tzOptions.sort((a, b) =>
                          a.order < b.order ? -1 : 1
                        )}
                      />
                    )}
                    name="timezone"
                    control={control}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="role"
                  >
                    Role
                  </label>
                  <Controller
                    render={({ field }) => (
                      <ReStyledSelect
                        id="role"
                        className="w-full"
                        onSelect={() => setFlagSave(false)}
                        disabled={validationDisableRoleCode({
                          user,
                          loading,
                        })}
                        {...field}
                        style={{ width: '100%' }}
                        options={roles?.data || []}
                      />
                    )}
                    name="role"
                    control={control}
                  />
                </div>
                <div>
                  <label
                    className="block text-sm text-gray-600 font-semibold mb-1"
                    for="organization"
                  >
                    Organization
                  </label>
                  <Controller
                    render={({ field }) => (
                      <ReStyledAutoComplete
                        id="organization"
                        rootClassName="w-full"
                        {...field}
                        options={listOriginConfig}
                        onSearch={(searchText) => {
                          setValue('organization', searchText);
                          setValue('organizationId', null);
                        }}
                        filterOption={(input, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(input.toLowerCase()) >= 0
                        }
                        onSelect={(value) => {
                          const consultantName = listOriginConfig.find(
                            (co) => co.value === value
                          )?.label;
                          setValue('organization', consultantName);
                          setValue('organizationId', value);
                        }}
                      ></ReStyledAutoComplete>
                    )}
                    name="organization"
                    control={control}
                  />
                </div>
              </div>
            </div>
          </Form>
        )}

        {loading && (
          <div className=" mt-6 grid grid-rows-2 w-full">
            {Array.from({ length: 2 }).map((i) => (
              <Skeleton active />
            ))}
          </div>
        )}

        {!userId && (
          <div className="w-full flex justify-end">
            <div className="flex gap-2">
              <Button
                loading={loading}
                className="Montserrat  flex items-center "
                type="primary"
                // disabled={flagSave ? flagSave : loading}
                onClick={() => {
                  // setUser({...user, avatarId: imageId})
                  setValue('avatarId', imageId);
                  const permissionList = user?.userPermissions;
                  const newPermisionList = permissionList.map((perItem) => {
                    const { keyCode } = perItem.permission;
                    let newItem = {
                      ...perItem,
                      allowRead: false,
                      allowWrite: false,
                    };
                    if (permissions.includes(`${keyCode}_READ`)) {
                      newItem = { ...newItem, allowRead: true };
                    }
                    if (permissions.includes(`${keyCode}_WRITE`)) {
                      newItem = { ...newItem, allowWrite: true };
                    }

                    return { ...newItem };
                  });
                  onSubmit(newPermisionList);
                  setReadOnly(true);
                }}
              >
                <FormOutlined /> <span>Save</span>
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserProfilePage;
