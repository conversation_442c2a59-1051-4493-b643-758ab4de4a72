import React from 'react';
import { useForm } from 'react-hook-form';
import EmailFinderComponent from '../../components/EmailFinder/EmailFinderComponent';
import { searchEmail } from '../../services/emailFinder';
import { Modal } from 'antd';
import SpecificEmailFinderForm from '../../components/EmailFinder/SpecificEmailFinderForm';

const EmailFinderContainer = () => {
  const { handleSubmit, control } = useForm();
  const [searchResults, setSearchResults] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState(null);
  const fetchEmails = async (payload) => {
    setIsLoading(true);
    try {
      const response = await searchEmail(payload);
      setSearchResults(response.data);
    } catch (error) {
      return error;
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data) => {
    await fetchEmails(data);
  };

  return (
    <div className='email-finder-container'>
      <EmailFinderComponent
        onSubmit={handleSubmit(onSubmit)}
        isLoading={isLoading}
        searchResults={searchResults}
        control={control}
        setSelectedRow={setSelectedRow}
      />
      <Modal
        open={selectedRow != null}
        centered
        okButtonProps={{ hidden: true }}
        onCancel={() => {
          setSelectedRow(null);
        }}
      >
        <SpecificEmailFinderForm selectedRow={selectedRow} />
      </Modal>
    </div>
  );
};

export default EmailFinderContainer;
