import { LeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Loading from '../HotList/Loading';
import { getJobById } from '../../services/jobs';
import { cleanJobType, stripJobDescription } from '../../utils/common';
import { generateJobSkills } from '../../services/search';
import BullHornJobSubmissionModalPage from '../../components/BullHorn/BullHornJobSubmissionModalPage';
import { getJobFromLocalStorage, SyncHeader } from '../SyncVacancyPage';

const SyncOpportunityPage = () => {
  const navigate = useNavigate();
  const { searchId, jobId } = useParams();

  const [job, setJob] = useState(null);
  const [jobSkills, setJobSkills] = useState([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const getJob = async () => {
    try {
      setLoading(true);
      let jobData = null;
      const jobFromLocalStorage = getJobFromLocalStorage();
      const rawJobId =
        jobFromLocalStorage?.job_id ||
        jobFromLocalStorage?.jobBoardId ||
        jobFromLocalStorage?.id;
      if (rawJobId === jobId) {
        jobData = jobFromLocalStorage;
      } else {
        const encodedJobId = encodeURIComponent(jobId);
        const { data } = await getJobById(encodedJobId);
        jobData = data?.result;
      }

      if (jobData) {
        const jobToSync = {
          ...jobData,
          description: stripJobDescription(jobData?.description),
          source: 'Zileo',
          jobtype: cleanJobType(jobData?.jobtype),
        };

        setLoading(false);
        setJob({ ...jobToSync });

        if (!jobToSync?.skills || jobToSync?.skills?.length === 0) {
          const { data } = await generateJobSkills(jobToSync?.job_id);
          setJobSkills(data?.result || []);
        }
      } else {
        setLoading(false);
        setErrorMessage('Job was deleted or synced by another user');
      }
    } catch (error) {
      setLoading(false);
      setErrorMessage('Job was deleted or synced by another user');
      console.error(error);
    }
  };

  useEffect(() => {
    getJob();
  }, [jobId]);

  return (
    <div className="rounded-md p-4">
      <SyncHeader />
      <div className="flex items-center justify-center mt-2 min-h-[50vh] bg-white p-3 rounded-md">
        {loading && <Loading />}
        {errorMessage && <p>{errorMessage}</p>}
        {!loading && job && (
          <BullHornJobSubmissionModalPage
            jobSkills={jobSkills}
            actionKey={3} // Opportunity form
            job={job}
            handleDeleteData={() => {}}
            searchData={[]}
            searchIdProp={searchId === 'non-search' ? '' : searchId}
            setIsModalVisible={(value) => {
              navigate(-1);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default SyncOpportunityPage;
