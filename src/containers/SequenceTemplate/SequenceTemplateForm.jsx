import * as React from 'react';
import { useState, useEffect } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { initialSequenceTemplate } from './constant';
import PreviewTemplate from './PreviewTemplate';
import { BranchesOutlined } from '@ant-design/icons';

const SequenceTemplateForm = ({
  sequenceTemplate,
  handleOk,
  handleCancel,
  open,
}) => {
  // useState area
  const [form] = Form.useForm();
  const [sequenceTemplateEdit, setSequenseTemplateEdit] = useState(null);
  const [openContent, setOpenContent] = useState(false);

  const showContent = () => setOpenContent(true);
  const closeContent = () => setOpenContent(false);
  // useEffect area

  const handleSubmitTemplate = () => {
    handleOk(sequenceTemplateEdit);
  };

  const updateSequenceTemplateEdit = () => {
    // if (sequenceTemplate?.id) return sequenceTemplate;

    return sequenceTemplate;
  };

  const handleSubmitTemplateContent = (content) => {
    setSequenseTemplateEdit({ ...sequenceTemplateEdit, content });
  };

  useEffect(() => {
    const updatedSequenceTemplate = updateSequenceTemplateEdit();
    setSequenseTemplateEdit(updatedSequenceTemplate);
  }, [sequenceTemplate]);

  return (
    <Modal
      title={
        <div className="w-full px-3">
          <div className="font-Montserrat px-2 border-b pb-2 w-full">
            {sequenceTemplateEdit?.id ? 'Edit Template' : 'Add new Template'}
          </div>
        </div>
      }
      open={open}
      // onOk={handleSubmitTemplate}
      //   confirmLoading={confirmLoading}
      onCancel={handleCancel}
      footer={
        <div className="w-full flex justify-end gap-2">
          <Button
            // type="dashed"
            className="!border-[#b2b8be] flex gap-2 items-center text-cyan-600 font-medium"
            onClick={handleCancel}
          >
            <div className="font-Montserrat">Close</div>
          </Button>

          <Button
            type="primary"
            className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-medium"
            onClick={handleSubmitTemplate}
          >
            <div className="font-Montserrat">Save</div>
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        className="mt-3 py-4 px-3 border rounded-md"
      >
        <Form.Item
          label={<div className="font-Montserrat">Template Name</div>}
          required
          tooltip="This is a required field"
        >
          <Input
            value={sequenceTemplateEdit?.name}
            onChange={(ev) =>
              setSequenseTemplateEdit({
                ...sequenceTemplateEdit,
                name: ev.target.value,
              })
            }
            maxLength={255}
            placeholder="Enter Template Name"
          />
        </Form.Item>
        <Form.Item
          required
          label={<div className="font-Montserrat">Content</div>}
          tooltip="Accessing to sequence to update steps."
        >
          <Button
            type="primary"
            className="!border-[#b2b8be] flex gap-2 items-center text-[#fff] font-medium"
            onClick={showContent}
          >
            <BranchesOutlined />
            <div className="font-Montserrat">Click here to access Sequence</div>
          </Button>
        </Form.Item>
      </Form>
      <PreviewTemplate
        handleClose={closeContent}
        openModalSendEmail={openContent}
        template={sequenceTemplate}
        isPreview={false}
        handleSubmitTemplateContent={handleSubmitTemplateContent}
      />
    </Modal>
  );
};

export default SequenceTemplateForm;
