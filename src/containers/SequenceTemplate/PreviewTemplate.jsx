import { useEffect, useState } from 'react';
import { Modal, Form, Button, Image, Input, notification } from 'antd';
import { useForm } from 'react-hook-form';
import BullhornSendEmail from '../../components/BullHorn/BullhornSendEmailModal';
import { v4 as uuid } from 'uuid';
// import selectStepIllustration from '../../assets/img/select-step.png';
import { ArrowRightOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import {
  SEQUENCE_GUIDE,
  StyledSteps,
} from '../Sequence/ModalCreateFromCandidate';

const defaultTemplateContent =
  '{"mailDefine":{"mailStep":{},"sendMail":{"content":"","from":"","mailStepParent":0,"mailStepParentContent":"","subject":""}}}';

export const PREVIEW_MODE = {
  VIEW: 'VIEW',
  EDIT: 'EDIT',
  DUPLICATE: 'DUPLICATE',
};

const PreviewTemplate = ({
  openModalSendEmail,
  handleClose,
  template,
  handleOk,
  setSelectedSequenceTemplate,
  previewMode,
  isDuplicateTemplate,
  handleReset,
  isAllowEdit = true,
}) => {
  // useState area
  const [editableTemplateContent, setEditableTemplateContent] = useState(null);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  // const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sendEmailStatus, setSendEmailStatus] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState([]);
  const [previewEmail, setPreviewEmail] = useState(null);

  const { handleSubmit, control, getValues, setValue, watch } = useForm();

  const onSubmit = (data) => {
    console.log('data: ', data);
    // if (!isAllowEdit) {
    //   notification.warning({
    //     description: 'You are not allowed to edit this template',
    //   });
    //   return;
    // }
    const newTemplate = {
      mailDefine: { ...data?.mailDefine },
      triggerItem: {
        rawSequence: [...data?.triggerItem?.mails],
      },
    };

    const templateContent =
      JSON.stringify(newTemplate) || defaultTemplateContent;
    handleOk({ ...template, content: templateContent });
    handleClose();
  };

  const parseData = async () => {
    // setLoading(true);
    const parsedContent = JSON.parse(
      template?.content || defaultTemplateContent
    );

    if (parsedContent?.triggerItem) {
      const stepsTemp = parsedContent?.triggerItem?.rawSequence || [];

      const inputNumberStepTemp = stepsTemp
        ?.map((step) => [
          step?.delays && [
            ...step?.delays?.flatMap((delayItem) => ({
              ...delayItem,
              unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
              key: uuid(),
              // type: ADD_STEP_TYPE.ADD_WAIT
            })),
          ],
          {
            ...step,
            id: uuid(),
            subject: step?.subject,
            content: step?.content,
            type: step?.type || '',
            key: uuid(),
            rawKey: step?.key,
            rawId: step?.id,
          },
        ])
        .flat(Infinity)
        .filter((item) => item?.key);

      const updatedThreadingInputNumberStep = inputNumberStepTemp?.map(
        (item) => {
          // const contentWithSig =
          //   item?.type === ADD_STEP_TYPE.SEND_MAIL
          //     ? replaceSignaturePlaceholder(item?.content, dataSignature)
          //     : item?.content;
          if (!item?.threadId) return { ...item };

          const mainEmail = inputNumberStepTemp.find(
            (main) =>
              main?.rawId === item?.threadId || main?.rawKey === item?.threadId
          );
          const newThreadingId = mainEmail?.id;
          return {
            ...item,
            threadId: newThreadingId,
          };
        }
      );

      const newTriggerItem = {
        rawSequence: [...updatedThreadingInputNumberStep],
      };
      setValue('triggerItem', newTriggerItem);
    }
  };

  const onUpdateTemplate = (newInputNumberStep) => {
    const newTemplate = {
      mailDefine: {},
      triggerItem: {
        rawSequence: [...newInputNumberStep],
      },
    };
    const templateContent =
      JSON.stringify(newTemplate) || defaultTemplateContent;
    setSelectedSequenceTemplate({ ...template, content: templateContent });
  };

  useEffect(() => {
    parseData();
  }, [template]);

  // cons
  return (
    <Modal
      width={1400}
      style={{ top: 10 }}
      title={
        <div className="text-xl font-medium pt-2 flex items-center justify-between px-4">
          <span className="flex items-center gap-2">
            <span>Name:</span>
            {previewMode === PREVIEW_MODE.VIEW && (
              <span className="p-2 text-white bg-gradient-to-r from-cyan-500 to-blue-500 font-semibold rounded-md">
                {template?.name}
              </span>
            )}
            {(previewMode === PREVIEW_MODE.EDIT ||
              previewMode === PREVIEW_MODE.DUPLICATE) && (
              <Input
                className="p-2 font-semibold min-w-[20rem]"
                value={template?.name}
                onChange={(ev) =>
                  setSelectedSequenceTemplate({
                    ...template,
                    name: ev.target.value,
                  })
                }
              />
            )}
          </span>
        </div>
      }
      closable={false}
      open={openModalSendEmail}
      onCancel={handleClose}
      footer={
        <div>
          <Button onClick={handleClose}>Close</Button>
        </div>
      }
      className="sequence-template-container min-h-[48rem] preview-template"
    >
      <div className="grid grid-cols-10 pt-5 min-h-[48rem] overflow-hidden relative p-5 mt-4 ">
        <div
          className={clsx(
            'col-span-6 flex flex-col items-center text-base font-medium justify-center gap-5 p-5  border rounded-md',
            previewEmail && 'bg-white'
          )}
        >
          {!previewEmail && (
            <>
              <div className="max-w-md flex-wrap opacity-60">
                <StyledSteps
                  progressDot
                  current={SEQUENCE_GUIDE?.length}
                  direction="vertical"
                  items={SEQUENCE_GUIDE.map((item) => ({
                    title: item,
                  }))}
                  className="!text-xs"
                />
              </div>
            </>
          )}
          {previewEmail && (
            <div className="flex flex-col gap-5 min-w-[40rem]">
              <div className="font-semibold text-xl">
                {previewEmail?.subject}
              </div>
              <div
                className="p-4 border rounded-md max-h-[40rem] overflow-y-auto"
                dangerouslySetInnerHTML={{
                  __html: previewEmail?.content,
                }}
              />
            </div>
          )}
        </div>
        <div className="col-span-4">
          {/* <Form layout="vertical" className="z-0"> */}
          {!loading && (
            <BullhornSendEmail
              templateContent={editableTemplateContent}
              preview={true}
              previewMode={previewMode}
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              sendToEmail={getValues()?.email}
              mailTitle={getValues()?.jobtitle}
              listAddContactSelected={listAddContactSelected}
              setNumberStep={setNumberStep}
              numberStep={numberStep}
              inputNumberStep={inputNumberStep}
              setInputNumberStep={setInputNumberStep}
              setEmailConfigData={setEmailConfigData}
              emailConfigData={emailConfigData}
              fromSequenseEmail={true}
              onSubmitTemplate={onSubmit}
              fromCreateTemplate={true}
              setPreviewEmail={setPreviewEmail}
              isDuplicateTemplate={isDuplicateTemplate}
              handleReset={handleReset}
              handleClose={handleClose}
              templateName={template?.name}
            />
          )}
          {/* </Form> */}
        </div>
      </div>
    </Modal>
  );
};

export default PreviewTemplate;
