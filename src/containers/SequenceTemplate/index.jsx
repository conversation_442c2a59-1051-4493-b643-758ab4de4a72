import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  notification,
  Button,
  Table,
  Space,
  Spin,
  Popconfirm,
  Input,
  Dropdown,
  Pagination,
  Avatar,
  Tag,
  Segmented,
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  DownOutlined,
  PlusOutlined,
  CopyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import _ from 'lodash';
import moment from 'moment';
import SequenceTemplateForm from './SequenceTemplateForm';
import {
  createSequenceTemplate,
  getSequenceTemplates,
  updateSingleSequenceTemplate,
  deleteSingleSequenceTemplate,
  getSingleSequenceTemplate,
} from '../../services/sequenceTemplate';
import { initialSequenceTemplate } from './constant';
import PreviewTemplate, { PREVIEW_MODE } from './PreviewTemplate';

import { useAuth } from '../../store/auth';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import { getLinkS3 } from '../../services/aws';
import handleRenderTime from '../../function/handleRenderTime';
import { useViewAs } from '../../store/viewAs';

export const QUERY_TEMPLATE_TYPES = {
  MY_TEMPLATE: 'MY_TEMPLATE',
  COMPANY_TEMPLATE: 'COMPANY_TEMPLATE',
};

const SequenceTemplate = () => {
  // useState are
  const [searchText, setSearchText] = useState('');
  const [type, setType] = useState(QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE);

  const [open, setOpen] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [selectedSequenceTemplate, setSelectedSequenceTemplate] =
    useState(null);

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;
  const userToSetId = userToSet?.user?.id || userToSet?.id;

  const [loading, setLoading] = useState(true);
  const [templateDetailLoading, setTemplateDetailLoading] = useState(false);
  const [data, setData] = useState([]);
  const [rawData, setRawData] = useState([]);
  const [page, setPage] = useState(1);
  const [countPage, setCountPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [previewMode, setPreviewMode] = useState(PREVIEW_MODE.EDIT);
  const [isDuplicateTemplate, setIsDuplicateTemplate] = useState(false);

  const showModal = () => setOpen(true);
  const showPreview = () => setOpenPreview(true);
  const closePreview = () => setOpenPreview(false);

  const createNew = (template) => {
    createSequenceTemplate(template)
      .then((res) => {
        if (res?.data?.result) {
          const newDataTemp = [res.data.result].concat([...data]);
          setData(newDataTemp);
          setRawData(newDataTemp);
          notification.success({
            message: 'Sucess',
            description: 'The template is added!',
          });

          setOpen(false);
        }
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        getData();
      });
  };
  const handlePagination = (page) => {
    setPage(page);
    getData(searchText, page, limit);
  };
  const handleSearch = (search) => {
    getData(search, 1, limit);
  };
  const handleReset = () => {
    setOpen(false);
    getData();
  };

  const handleEdit = (template) => {
    const removeUnuseFields = { ...template };
    delete removeUnuseFields?.createdFullName;
    delete removeUnuseFields?.modifiedFullName;
    delete removeUnuseFields?.createdAvatarId;
    delete removeUnuseFields?.createdAvatarUrl;
    delete removeUnuseFields?.modifiedAvatarId;
    delete removeUnuseFields?.modifiedAvatarUrl;

    updateSingleSequenceTemplate(removeUnuseFields)
      .then((res) => {
        if (res?.data?.result) {
          const newTemplate = { ...res.data.result };
          const newDataTemp = [...data].map((template) =>
            template.id === newTemplate.id
              ? { ...template, ...newTemplate }
              : template
          );
          setData(newDataTemp);
          setRawData(newDataTemp);
          notification.success({
            message: 'Sucess',
            description: 'The template is updated!',
          });
        }

        setOpen(false);
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        getData();
      });
  };

  const handleOk = (template) => {
    if (!template) return;
    setLoading(true);
    const contentItem = JSON.parse(
      template?.content || '{"mailDefine":{},"triggerItem":{}}'
    );

    if (_.isEmpty(template?.name)) {
      notification.warning({
        message: 'Warning',
        description: 'Template Name can not empty!',
      });
      setLoading(false);
      return;
    }

    if (_.isEmpty(contentItem) || _.isEmpty(contentItem?.triggerItem)) {
      notification.warning({
        message: 'Warning',
        description: 'Please fill Sequence Template content!',
      });
      setLoading(false);
      return;
    }

    if (template?.id) {
      const edittingTemplate = {
        ...template,
        modifiedBy: userToSet?.user?.id || userToSet?.id,
      };
      handleEdit(edittingTemplate);
    } else {
      const creatingTemplate = {
        ...template,
        organizationId:
          userToSet?.user?.organizationId || userToSet?.organization?.id,
        modifiedBy: userToSet?.user?.id || userToSet?.id,
        createdBy: userToSet?.user?.id || userToSet?.id,
      };
      createNew(creatingTemplate);
    }
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const handleAddNewTemplate = () => {
    setPreviewMode(PREVIEW_MODE.EDIT);
    setSelectedSequenceTemplate(initialSequenceTemplate);
    showPreview();
  };

  const onEditTemplate = async (template) => {
    setTemplateDetailLoading(true);
    const templateData = await getSequenceTemplateContent(template?.id);
    if (!templateData) {
      notification.error({
        description: 'Template not existing or deleted by user!',
      });
      return;
    }
    setSelectedSequenceTemplate({ ...templateData });
    showModal();
    setTemplateDetailLoading(false);
  };

  const onDeleteTemplate = (template) => {
    setLoading(true);
    deleteSingleSequenceTemplate(template)
      .then((res) => {
        const dataTemp = [...data];
        const deletedIndex = dataTemp.findIndex(
          (item) => item.id === template.id
        );
        dataTemp.splice(deletedIndex, 1);
        setData(dataTemp);
        setRawData(dataTemp);
        notification.success({
          message: 'Sucess',
          description: 'The template is deleted!',
        });
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const getData = async (_search, page, _limit, submitType) => {
    setLoading(true);
    const viewAsParams = {
      viewAs: getUserViewAs(),
      page: page || 1,
      limit: 10,
      search: searchText || '',
      type: submitType || type,
    };
    getSequenceTemplates(viewAsParams)
      .then(async (res) => {
        if (res?.data?.result) {
          setCountPage(res?.data?.result?.count);
          const dataTemp = await Promise.all(
            [...res?.data?.result?.items].map(async (template) => {
              let createdAvatarUrl = '';
              let modifiedAvatarUrl = '';
              if (template?.createdAvatarId) {
                getLinkS3(template?.createdAvatarId)
                  .then(({ data: createdAvatarUrlTemp }) => {
                    createdAvatarUrl = createdAvatarUrlTemp;
                    const newData = [...dataTemp];
                    const index = newData.findIndex(
                      (item) => item.id === template.id
                    );
                    newData[index].createdAvatarUrl = createdAvatarUrl;
                    setData(newData);
                    setRawData(newData);
                  })
                  .catch((err) => {
                    console.log('Error getting created avatar URL:', err);
                  });
              }
              if (template?.modifiedAvatarId) {
                getLinkS3(template?.modifiedAvatarId)
                  .then(({ data: modifiedAvatarUrlTemp }) => {
                    modifiedAvatarUrl = modifiedAvatarUrlTemp;
                    const newData = [...dataTemp];
                    const index = newData.findIndex(
                      (item) => item.id === template.id
                    );
                    newData[index].modifiedAvatarUrl = modifiedAvatarUrl;
                    setData(newData);
                    setRawData(newData);
                  })
                  .catch((err) => {
                    console.log('Error getting modified avatar URL:', err);
                  });
              }

              return {
                ...template,
                createdAvatarUrl,
                modifiedAvatarUrl,
              };
            })
          );
          setData([...dataTemp]);
          setRawData([...dataTemp]);
        }
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // const handleSearch = _.debounce(() => {
  //   const searchData = rawData.filter(
  //     (item) =>
  //       item.name.toLowerCase().includes(searchText.toLowerCase().trim()) ||
  //       item.content.toLowerCase().includes(searchText.toLowerCase().trim())
  //   );
  //   setData(searchData);
  // }, 1000);

  const onPreview = async (template) => {
    if (!template?.id) {
      return;
    }
    setTemplateDetailLoading(true);
    const templateData = await getSequenceTemplateContent(template?.id);
    if (!templateData) {
      notification.error({
        description: 'Template not existing or deleted by user!',
      });
      return;
    }
    setSelectedSequenceTemplate({ ...templateData });
    showPreview();
    setTemplateDetailLoading(false);
  };

  const getSequenceTemplateContent = async (selectedTemplateId) => {
    const templateData = await getSingleSequenceTemplate(selectedTemplateId);

    return templateData?.data?.result || null;
  };

  useEffect(() => {
    getData();
  }, []);

  const columns = [
    {
      title: 'Template Name',
      dataIndex: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      sortDirections: ['descend', 'ascend'],
      render: (text, record) => {
        return (
          <div className="flex items-center gap-2">
            <div className="font-semibold">{text}</div>
          </div>
        );
      },
    },
    {
      title: 'Created By',
      dataIndex: 'createdFullName',
      render: (text, record) => (
        <div className="font-Montserrat flex items-center gap-2 border border-[#ecf3fb] text-[#194783] rounded-xl px-4 py-1 font-semibold w-fit">
          {record?.createdAvatarUrl ? (
            <Avatar src={record?.createdAvatarUrl} />
          ) : (
            <Avatar>{Array.from(text ?? 'admin')[0]}</Avatar>
          )}
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      align: 'center',
      sorter: (a, b) => new Date(b?.createdAt) - new Date(a?.createdAt),
      sortDirections: ['descend', 'ascend'],
      render: (createdAt) => <span>{handleRenderTime(createdAt)}</span>,
    },
    {
      align: 'center',
      title: 'Open %',
      dataIndex: 'totalOpened',
      sorter: (a, b) => a.totalOpened.localeCompare(b.totalOpened),
      sortDirections: ['descend', 'ascend'],
      render: (text, record) => {
        const { totalOpened, totalSent } = record;
        const percentage = totalSent
          ? Number((totalOpened / totalSent) * 100)
          : 0;
        return (
          <div className="flex flex-col gap-2">
            <div>{`${isNaN(percentage) ? 0 : percentage.toFixed(0)}%`}</div>
          </div>
        );
      },
    },
    {
      title: 'Response %',
      align: 'center',
      dataIndex: 'totalReply',
      sorter: (a, b) => a.totalReply.localeCompare(b.totalReply),
      sortDirections: ['descend', 'ascend'],
      render: (text, record) => {
        const { totalReply, totalSent } = record;
        const percentage = totalSent
          ? Number((totalReply / totalSent) * 100)
          : 0;
        return (
          <div className="flex flex-col gap-2">
            <div>{`${isNaN(percentage) ? 0 : percentage.toFixed(0)}%`}</div>
          </div>
        );
      },
    },
    {
      title: 'Last Modified By',
      align: 'center',
      dataIndex: 'modifiedFullName',
      render: (text, record) => (
        <div className="flex items-center justify-center w-full">
          <div className='flex items-center justify-center gap-2 border border-[#ecf3fb] text-[#194783] rounded-xl px-4 py-1 font-semibold w-fit'>
            {record?.modifiedAvatarUrl ? (
              <Avatar src={record?.modifiedAvatarUrl} />
            ) : (
              <Avatar>{Array.from(text ?? 'admin')[0]}</Avatar>
            )}
            <span>{text} {record?.createdBy === userToSetId && <span className='text-xs opacity-60'>(You)</span>}</span>
          </div>
        </div>
      ),
    },
    {
      title: '',
      align: 'center',
      dataIndex: 'action',
      render: (_, record) => {
        const actionItems = [
          record?.createdBy &&
            userToSetId === record?.createdBy && {
              key: 'edit-template',
              label: (
                <div
                  title="Edit Template"
                  onClick={(e) => {
                    e.stopPropagation();
                    setPreviewMode(PREVIEW_MODE.EDIT);
                    setIsDuplicateTemplate(false);
                    onPreview(record);
                  }}
                  className="flex justify-left items-left gap-2 font-Montserrat"
                >
                  <EditOutlined />
                  <span>Edit Template</span>
                </div>
              ),
            },
          {
            key: 'duplicate-template',
            label: (
              <div
                title="Duplicate Template"
                onClick={(e) => {
                  e.stopPropagation();
                  setPreviewMode(PREVIEW_MODE.EDIT);
                  setIsDuplicateTemplate(true);
                  onPreview(record);
                }}
                className="flex justify-left items-left gap-2 font-Montserrat"
              >
                <CopyOutlined />
                <span>Duplicate Template</span>
              </div>
            ),
          },
          {
            key: 'delete-template',
            label: (
              <Popconfirm
                title="Confirmation"
                description={`Do you want to delete ${record?.name}`}
                onConfirm={(e) => {
                  e.stopPropagation();
                  onDeleteTemplate(record);
                }}
                okText="Delete"
                cancelText="Cancel"
              >
                <div
                  title="Delete Template"
                  className="flex justify-left items-left gap-2 font-Montserrat"
                >
                  <DeleteOutlined /> <span>Delete Template</span>
                </div>
              </Popconfirm>
            ),
          },
        ];
        return (
          <div
            className="flex w-full justify-center"
            onClick={(e) => e.stopPropagation()}
          >
            <Dropdown
              menu={{
                items: actionItems,
              }}
              placement="bottom"
              arrow
              // trigger={'click'}
            >
              <Button
                onClick={(e) => e.stopPropagation()}
                className="!border-[#b2b8be] flex gap-2 items-center text-[#b2b8be]"
              >
                <p className="Montserrat">Select Action</p>
                <DownOutlined />
              </Button>
            </Dropdown>
          </div>
        );
      },
    },
  ];

  const filteredColumns = columns.filter(
    (column) =>
      (type === QUERY_TEMPLATE_TYPES.MY_TEMPLATE &&
        column.dataIndex !== 'createdFullName') ||
      type === QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE
  );

  return (
    <div>
      <div className="flex mb-5 justify-between">
        <div className="w-1/3">
          <Input
            disabled={loading}
            rootClassName="customize-input-container"
            placeholder="Search..."
            value={searchText}
            onChange={(text) => setSearchText(text.target.value)}
            onKeyPress={(event) => {
              if (event.key === 'Enter') {
                setPage(1);
                handleSearch(searchText, 1, limit);
              }
            }}
            prefix={<SearchOutlined />}
          />
        </div>

        <div className="flex items-center gap-2">
          <Segmented
            disabled={loading}
            className="customized-segmented-sequence-template"
            value={type}
            onChange={(value) => {
              setType(value);
              setPage(1);
              getData(searchText, 1, limit, value);
            }}
            options={[
              {
                label: 'My Templates',
                value: QUERY_TEMPLATE_TYPES.MY_TEMPLATE,
              },
              {
                label: 'Company Templates',
                value: QUERY_TEMPLATE_TYPES.COMPANY_TEMPLATE,
              },
            ]}
          />
          <Button
            disabled={loading}
            type="primary"
            className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
            onClick={handleAddNewTemplate}
          >
            <p className="Montserrat">Add new template</p>
            <PlusOutlined />
          </Button>
        </div>
      </div>
      <div className="search-table-new-design-container">
        <Table
          className="custom-table"
          columns={filteredColumns}
          dataSource={data}
          rowKey={(record) => record.id}
          pagination={false}
          loading={templateDetailLoading || loading}
          onRow={(record, rowIndex) => {
            return {
              onClick: () => {
                setPreviewMode(PREVIEW_MODE.EDIT);
                onPreview(record);
              },
              style: { cursor: 'pointer' },
            };
          }}
        />
      </div>
      <div style={{ marginTop: '30px' }}>
        <Pagination
          disabled={loading}
          current={page}
          defaultPageSize={10}
          total={countPage}
          showSizeChanger={false}
          onChange={handlePagination}
        />
      </div>
      {/* Modal area */}

      {openPreview && (
        <PreviewTemplate
          handleClose={closePreview}
          openModalSendEmail={openPreview}
          template={selectedSequenceTemplate}
          setSelectedSequenceTemplate={setSelectedSequenceTemplate}
          // Edit mode only
          isDuplicateTemplate={isDuplicateTemplate}
          handleOk={handleOk}
          previewMode={previewMode}
          setPreviewMode={setPreviewMode}
          getData={getData}
          handleReset={handleReset}
          // Allow edit
          isAllowEdit={
            selectedSequenceTemplate?.createdBy &&
            userToSetId === selectedSequenceTemplate?.createdBy
          }
        />
      )}
    </div>
  );
};

export default SequenceTemplate;
