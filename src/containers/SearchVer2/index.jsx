import React from 'react';
import './style.css';
import SearchTableV2 from '../../components/SearchTableV2/SearchTableV2';
import useSearches from '../../hooks/useSearches';
import SearchActionsV2 from '../../components/SearchActionsV2/SearchFormV2';
import { useState } from 'react';

const SearchV2 = () => {
  const {
    // searches,
    data,
    fetchSearches,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    pagination,
    setPagination,
    setSearchText,
    loading: loadingSearch,
  } = useSearches();

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const handleSearch = async (searchText) => {
    setSearchText(searchText);
    await fetchSearches();
  };

  return (
    <div className="mt-2 mx-5 mb-2">
      <SearchActionsV2
        setSelectedRowKeys={setSelectedRowKeys}
        selectedRowKeys={selectedRowKeys}
        searches={data}
        handleSearch={handleSearch}
        loadingSearch={loadingSearch}
        populateSearchData={fetchSearches}
      />
      <SearchTableV2
        isSearching={loadingSearch}
        setSelectedRowKeys={setSelectedRowKeys}
        selectedRowKeys={selectedRowKeys}
        searches={data}
        populateSearchData={fetchSearches}
        sortField={sortField}
        setSortField={setSortField}
        sortOrder={sortOrder}
        setSortOrder={setSortOrder}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
};

export default SearchV2;
