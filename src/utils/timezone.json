[{"utc": "UTC-12:00", "name": "(UTC-12:00) International Date Line West", "timezone": "Dateline Standard Time", "order": 1}, {"utc": "UTC-11:00", "name": "(UTC-11:00) Coordinated Universal Time-11", "timezone": "UTC-11", "order": 2}, {"utc": "UTC-10:00", "name": "(UTC-10:00) Aleutian Islands", "timezone": "Aleutian Standard Time", "order": 3}, {"utc": "UTC-10:00", "name": "(UTC-10:00) Hawaii", "timezone": "Hawaiian Standard Time", "order": 4}, {"utc": "UTC-09:30", "name": "(UTC-09:30) Marquesas Islands", "timezone": "Marquesas Standard Time", "order": 5}, {"utc": "UTC-09:00", "name": "(UTC-09:00) Alaska", "timezone": "Alaskan Standard Time", "order": 6}, {"utc": "UTC-09:00", "name": "(UTC-09:00) Coordinated Universal Time-09", "timezone": "UTC-09", "order": 7}, {"utc": "UTC-08:00", "name": "(UTC-08:00) Baja California", "timezone": "Pacific Standard Time (Mexico)", "order": 8}, {"utc": "UTC-08:00", "name": "(UTC-08:00) Coordinated Universal Time-08", "timezone": "UTC-08", "order": 9}, {"utc": "UTC-08:00) Pacific Time (US & Canada", "name": "(UTC-08:00) Pacific Time (US & Canada)", "timezone": "Pacific Standard Time", "order": 10}, {"utc": "UTC-07:00", "name": "(UTC-07:00) Arizona", "timezone": "US Mountain Standard Time", "order": 11}, {"utc": "UTC-07:00", "name": "(UTC-07:00) Chihuahua, La Paz, Mazatlan", "timezone": "Mountain Standard Time (Mexico)", "order": 12}, {"utc": "UTC-07:00) Mountain Time (US & Canada", "name": "(UTC-07:00) Mountain Time (US & Canada)", "timezone": "Mountain Standard Time", "order": 13}, {"utc": "UTC-06:00", "name": "(UTC-06:00) Central America", "timezone": "Central America Standard Time", "order": 14}, {"utc": "UTC-06:00) Central Time (US & Canada", "name": "(UTC-06:00) Central Time (US & Canada)", "timezone": "Central Standard Time", "order": 15}, {"utc": "UTC-06:00", "name": "(UTC-06:00) Easter Island", "timezone": "Easter Island Standard Time", "order": 16}, {"utc": "UTC-06:00", "name": "(UTC-06:00) Guadalajara, Mexico City, Monterrey", "timezone": "Central Standard Time (Mexico)", "order": 17}, {"utc": "UTC-06:00", "name": "(UTC-06:00) Saskatchewan", "timezone": "Canada Central Standard Time", "order": 18}, {"utc": "UTC-05:00", "name": "(UTC-05:00) Bogota, Lima, Quito, Rio Branco", "timezone": "SA Pacific Standard Time", "order": 19}, {"utc": "UTC-05:00", "name": "(UTC-05:00) Chetumal", "timezone": "Eastern Standard Time (Mexico)", "order": 20}, {"utc": "UTC-05:00 Eastern Time (US & Canada", "name": "(UTC-05:00) Eastern Time (US & Canada)", "timezone": "Eastern Standard Time", "order": 21}, {"utc": "UTC-05:00", "name": "(UTC-05:00) Haiti", "timezone": "Haiti Standard Time", "order": 22}, {"utc": "UTC-05:00", "name": "(UTC-05:00) Havana", "timezone": "Cuba Standard Time", "order": 23}, {"utc": "UTC-05:00) Indiana (East", "name": "(UTC-05:00) Indiana (East)", "timezone": "US Eastern Standard Time", "order": 24}, {"utc": "UTC-05:00", "name": "(UTC-05:00) Turks and Caicos", "timezone": "Turks And Caicos Standard Time", "order": 25}, {"utc": "UTC-04:00", "name": "(UTC-04:00) Asuncion", "timezone": "Paraguay Standard Time", "order": 26}, {"utc": "UTC-04:00) Atlantic Time (Canada", "name": "(UTC-04:00) Atlantic Time (Canada)", "timezone": "Atlantic Standard Time", "order": 27}, {"utc": "UTC-04:00", "name": "(UTC-04:00) Caracas", "timezone": "Venezuela Standard Time", "order": 28}, {"utc": "UTC-04:00", "name": "(UTC-04:00) Cuiaba", "timezone": "Central Brazilian Standard Time", "order": 29}, {"utc": "UTC-04:00", "name": "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan", "timezone": "SA Western Standard Time", "order": 30}, {"utc": "UTC-04:00", "name": "(UTC-04:00) Santiago", "timezone": "Pacific SA Standard Time", "order": 31}, {"utc": "UTC-03:30", "name": "(UTC-03:30) Newfoundland", "timezone": "Newfoundland Standard Time", "order": 32}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Araguaina", "timezone": "Tocantins Standard Time", "order": 33}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Brasilia", "timezone": "E. South America Standard Time", "order": 34}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Cayenne, Fortaleza", "timezone": "SA Eastern Standard Time", "order": 35}, {"utc": "UTC-03:00", "name": "(UTC-03:00) City of Buenos Aires", "timezone": "Argentina Standard Time", "order": 36}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Greenland", "timezone": "Greenland Standard Time", "order": 37}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Montevideo", "timezone": "Montevideo Standard Time", "order": 38}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Punta Arenas", "timezone": "Magallanes Standard Time", "order": 39}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Saint Pierre and Miquelon", "timezone": "Saint Pierre Standard Time", "order": 40}, {"utc": "UTC-03:00", "name": "(UTC-03:00) Salvador", "timezone": "Bahia Standard Time", "order": 41}, {"utc": "UTC-02:00", "name": "(UTC-02:00) Coordinated Universal Time-02", "timezone": "UTC-02", "order": 42}, {"utc": "UTC-01:00", "name": "(UTC-01:00) Azores", "timezone": "Azores Standard Time", "order": 43}, {"utc": "UTC-01:00", "name": "(UTC-01:00) Cabo Verde Is.", "timezone": "Cape Verde Standard Time", "order": 44}, {"utc": "UTC", "name": "(UTC) Coordinated Universal Time", "timezone": "UTC", "order": 45}, {"utc": "UTC+00:00", "name": "(UTC+00:00) Dublin, Edinburgh, Lisbon, London", "timezone": "GMT Standard Time", "order": 46}, {"utc": "UTC+00:00", "name": "(UTC+00:00) Monrovia, Reykjavik", "timezone": "Greenwich Standard Time", "order": 47}, {"utc": "UTC+00:00", "name": "(UTC+00:00) Sao Tome", "timezone": "Sao Tome Standard Time", "order": 48}, {"utc": "UTC+01:00", "name": "(UTC+01:00) Casablanca", "timezone": "Morocco Standard Time", "order": 49}, {"utc": "UTC+01:00", "name": "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna", "timezone": "W. Europe Standard Time", "order": 50}, {"utc": "UTC+01:00", "name": "(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague", "timezone": "Central Europe Standard Time", "order": 51}, {"utc": "UTC+01:00", "name": "(UTC+01:00) Brussels, Copenhagen, Madrid, Paris", "timezone": "Romance Standard Time", "order": 52}, {"utc": "UTC+01:00", "name": "(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb", "timezone": "Central European Standard Time", "order": 53}, {"utc": "UTC+01:00", "name": "(UTC+01:00) West Central Africa", "timezone": "W. Central Africa Standard Time", "order": 54}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Amman", "timezone": "Jordan Standard Time", "order": 55}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Athens, Bucharest", "timezone": "GTB Standard Time", "order": 56}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Beirut", "timezone": "Middle East Standard Time", "order": 57}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Cairo", "timezone": "Egypt Standard Time", "order": 58}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Chisinau", "timezone": "E. Europe Standard Time", "order": 59}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Damascus", "timezone": "Syria Standard Time", "order": 60}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Gaza, Hebron", "timezone": "West Bank Standard Time", "order": 61}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Harare, Pretoria", "timezone": "South Africa Standard Time", "order": 62}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius", "timezone": "FLE Standard Time", "order": 63}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Jerusalem", "timezone": "Israel Standard Time", "order": 64}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Kaliningrad", "timezone": "Kaliningrad Standard Time", "order": 65}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Khartoum", "timezone": "Sudan Standard Time", "order": 66}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Tripoli", "timezone": "Libya Standard Time", "order": 67}, {"utc": "UTC+02:00", "name": "(UTC+02:00) Windhoek", "timezone": "Namibia Standard Time", "order": 68}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Baghdad", "timezone": "Arabic Standard Time", "order": 69}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Istanbul", "timezone": "Turkey Standard Time", "order": 70}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Kuwait, Riyadh", "timezone": "Arab Standard Time", "order": 71}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Minsk", "timezone": "Belarus Standard Time", "order": 72}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Moscow, St. Petersburg", "timezone": "Russian Standard Time", "order": 73}, {"utc": "UTC+03:00", "name": "(UTC+03:00) Nairobi", "timezone": "E. Africa Standard Time", "order": 74}, {"utc": "UTC+03:30", "name": "(UTC+03:30) Tehran", "timezone": "Iran Standard Time", "order": 75}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Abu Dhabi, Muscat", "timezone": "Arabian Standard Time", "order": 76}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Astrakhan, Ulyanovsk", "timezone": "Astrakhan Standard Time", "order": 77}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Baku", "timezone": "Azerbaijan Standard Time", "order": 78}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Izhevsk, Samara", "timezone": "Russia Time Zone 3", "order": 79}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Port Louis", "timezone": "Mauritius Standard Time", "order": 80}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Saratov", "timezone": "Saratov Standard Time", "order": 81}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Tbilisi", "timezone": "Georgian Standard Time", "order": 82}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Volgograd", "timezone": "Volgograd Standard Time", "order": 83}, {"utc": "UTC+04:00", "name": "(UTC+04:00) Yerevan", "timezone": "Caucasus Standard Time", "order": 84}, {"utc": "UTC+04:30", "name": "(UTC+04:30) Kabul", "timezone": "Afghanistan Standard Time", "order": 85}, {"utc": "UTC+05:00", "name": "(UTC+05:00) Ashgabat, Tashkent", "timezone": "West Asia Standard Time", "order": 86}, {"utc": "UTC+05:00", "name": "(UTC+05:00) Ekaterinburg", "timezone": "Ekaterinburg Standard Time", "order": 87}, {"utc": "UTC+05:00", "name": "(UTC+05:00) Islamabad, Karachi", "timezone": "Pakistan Standard Time", "order": 88}, {"utc": "UTC+05:00", "name": "(UTC+05:00) Qyzylorda", "timezone": "Qyzylorda Standard Time", "order": 89}, {"utc": "UTC+05:30", "name": "(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi", "timezone": "India Standard Time", "order": 90}, {"utc": "UTC+05:30", "name": "(UTC+05:30) Sri Jayawardenepura", "timezone": "Sri Lanka Standard Time", "order": 91}, {"utc": "UTC+05:45", "name": "(UTC+05:45) Kathmandu", "timezone": "Nepal Standard Time", "order": 92}, {"utc": "UTC+06:00", "name": "(UTC+06:00) Astana", "timezone": "Central Asia Standard Time", "order": 93}, {"utc": "UTC+06:00", "name": "(UTC+06:00) Dhaka", "timezone": "Bangladesh Standard Time", "order": 94}, {"utc": "UTC+06:00", "name": "(UTC+06:00) Omsk", "timezone": "Omsk Standard Time", "order": 95}, {"utc": "UTC+06:30) Yangon (Rangoon", "name": "(UTC+06:30) Yangon (Rangoon)", "timezone": "Myanmar Standard Time", "order": 96}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Bangkok, Hanoi, Jakarta", "timezone": "SE Asia Standard Time", "order": 97}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Barnaul, Gorno-Altaysk", "timezone": "Altai Standard Time", "order": 98}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Hovd", "timezone": "W. <PERSON> Standard Time", "order": 99}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Krasnoyarsk", "timezone": "North Asia Standard Time", "order": 100}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Novosibirsk", "timezone": "N. Central Asia Standard Time", "order": 101}, {"utc": "UTC+07:00", "name": "(UTC+07:00) Tomsk", "timezone": "Tomsk Standard Time", "order": 102}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi", "timezone": "China Standard Time", "order": 103}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Irkutsk", "timezone": "North Asia East Standard Time", "order": 104}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Kuala Lumpur, Singapore", "timezone": "Singapore Standard Time", "order": 105}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Perth", "timezone": "W. <PERSON> Standard Time", "order": 106}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Taipei", "timezone": "Taipei Standard Time", "order": 107}, {"utc": "UTC+08:00", "name": "(UTC+08:00) Ulaanbaatar", "timezone": "Ulaanbaatar Standard Time", "order": 108}, {"utc": "UTC+08:45", "name": "(UTC+08:45) Eucla", "timezone": "Aus Central W. Standard Time", "order": 109}, {"utc": "UTC+09:00", "name": "(UTC+09:00) <PERSON><PERSON>", "timezone": "Transbaikal Standard Time", "order": 110}, {"utc": "UTC+09:00", "name": "(UTC+09:00) Osaka, Sapporo, Tokyo", "timezone": "Tokyo Standard Time", "order": 111}, {"utc": "UTC+09:00", "name": "(UTC+09:00) Pyongyang", "timezone": "North Korea Standard Time", "order": 112}, {"utc": "UTC+09:00", "name": "(UTC+09:00) Seoul", "timezone": "Korea Standard Time", "order": 113}, {"utc": "UTC+09:00", "name": "(UTC+09:00) Yakutsk", "timezone": "Yakutsk Standard Time", "order": 114}, {"utc": "UTC+09:30", "name": "(UTC+09:30) Adelaide", "timezone": "Cen. Australia Standard Time", "order": 115}, {"utc": "UTC+09:30", "name": "(UTC+09:30) Darwin", "timezone": "AUS Central Standard Time", "order": 116}, {"utc": "UTC+10:00", "name": "(UTC+10:00) Brisbane", "timezone": "E. Australia Standard Time", "order": 117}, {"utc": "UTC+10:00", "name": "(UTC+10:00) Canberra, Melbourne, Sydney", "timezone": "AUS Eastern Standard Time", "order": 118}, {"utc": "UTC+10:00", "name": "(UTC+10:00) Guam, Port Moresby", "timezone": "West Pacific Standard Time", "order": 119}, {"utc": "UTC+10:00", "name": "(UTC+10:00) Hobart", "timezone": "Tasmania Standard Time", "order": 120}, {"utc": "UTC+10:00", "name": "(UTC+10:00) Vladivostok", "timezone": "Vladivostok Standard Time", "order": 121}, {"utc": "UTC+10:30", "name": "(UTC+10:30) Lord Howe Island", "timezone": "<PERSON> Standard Time", "order": 122}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Bougainville Island", "timezone": "Bougainville Standard Time", "order": 123}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Chokurdakh", "timezone": "Russia Time Zone 10", "order": 124}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Magadan", "timezone": "Magadan Standard Time", "order": 125}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Norfolk Island", "timezone": "Norfolk Standard Time", "order": 126}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Sakhalin", "timezone": "Sakhalin Standard Time", "order": 127}, {"utc": "UTC+11:00", "name": "(UTC+11:00) Solomon Is., New Caledonia", "timezone": "Central Pacific Standard Time", "order": 128}, {"utc": "UTC+12:00", "name": "(UTC+12:00) Anadyr, Petropavlovsk-Kamchatsky", "timezone": "Russia Time Zone 11", "order": 129}, {"utc": "UTC+12:00", "name": "(UTC+12:00) Auckland, Wellington", "timezone": "New Zealand Standard Time", "order": 130}, {"utc": "UTC+12:00", "name": "(UTC+12:00) Coordinated Universal Time+12", "timezone": "UTC+12", "order": 131}, {"utc": "UTC+12:00", "name": "(UTC+12:00) Fiji", "timezone": "Fiji Standard Time", "order": 132}, {"utc": "UTC+12:45", "name": "(UTC+12:45) Chatham Islands", "timezone": "Chatham Islands Standard Time", "order": 133}, {"utc": "UTC+13:00", "name": "(UTC+13:00) Coordinated Universal Time+13", "timezone": "UTC+13", "order": 134}, {"utc": "UTC+13:00", "name": "(UTC+13:00) <PERSON><PERSON><PERSON><PERSON><PERSON>a", "timezone": "Tonga Standard Time", "order": 135}, {"utc": "UTC+13:00", "name": "(UTC+13:00) Samoa", "timezone": "Samoa Standard Time", "order": 136}, {"utc": "UTC+14:00", "name": "(UTC+14:00) Kiritimati Island", "timezone": "Line Islands Standard Time", "order": 137}]