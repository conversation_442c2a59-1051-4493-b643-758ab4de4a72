export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const transFormDateFormat = (isoString) => {
  const date = new Date(isoString);
  const m = date.getMonth();
  const d = date.getDate();
  const y = date.getFullYear();
  const mlong = months[m];
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  return  hours + ':' + minutes + ':' + seconds + ' ' + mlong + ' ' + d + ', ' + y;
};
