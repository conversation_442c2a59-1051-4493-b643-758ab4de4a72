/* eslint-disable no-case-declarations */
import axios, { HttpStatusCode } from 'axios';
import { notification } from 'antd';
import { useAuth } from '../store/auth';
import { logout } from '../services/auth';
import { useQueryClient } from '@tanstack/react-query';
import { getUserViewAs } from '../helpers/getUserViewAs';

let unauthorizedNotified = false;

// ----------------------------------------------------------------------
// @ts-ignore
export const baseURL = import.meta.env.VITE_API_URL; //  backend Url
// const baseURL = import.meta.env.VITE_API_URL;
const axiosInstance = axios.create({
  baseURL,
  withCredentials: true,
  headers: {
    'Access-Control-Allow-Origin': baseURL,
    'view-as-user-id': getUserViewAs(),
  },
});

// axiosInstance.defaults.withCredentials = true;
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const statusCode = error?.response?.status;
    switch (statusCode) {
      case HttpStatusCode.Forbidden:
        // notification.error({
        //   message: 'Forbidden',
        //   description: 'You do not have permission to access this resource.',
        // });
        Promise.resolve();
        break;
      case HttpStatusCode.Unauthorized:
        const originalRequest = error.config;
        if (
          !originalRequest._retry &&
          originalRequest.url !== '/auth/refresh-token'
        ) {
          originalRequest._retry = true;
          try {
            await axiosInstance.post('/auth/refresh-token');
            return axiosInstance(originalRequest);
          } catch (error) {
            if (!unauthorizedNotified) {
              notification.error({
                message: 'Unauthorized',
                description: 'You need to login again',
              });
              unauthorizedNotified = true;
            }
          }
        } else {
          await logout();
          const clearAuth = useAuth.setState;
          clearAuth({ isAuthenticated: false, profile: null });
          const queryClient = useQueryClient.queryClient;
          queryClient.setQueryData(['CURRENT_USER'], null);
        }
        Promise.resolve();
        break;
      default:
        break;
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
