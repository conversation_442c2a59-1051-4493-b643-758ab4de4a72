import moment from 'moment';
import {
  licenseType,
  userRole,
  SUPER_ADMIN,
  FEATURES,
  SALES,
} from '../constants/common.constant';

Object.defineProperty(String.prototype, 'capitalize', {
  value: function () {
    return this.charAt(0).toUpperCase() + this.slice(1);
  },
  enumerable: false,
});

const numberWithCommas = (x) => {
  return x ? x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : x;
};

const nFormatter = (num, digits = 1) => {
  const lookup = [
    { value: 1, symbol: '' },
    { value: 1e3, symbol: 'k' },
    { value: 1e6, symbol: 'M' },
    { value: 1e9, symbol: 'G' },
    { value: 1e12, symbol: 'T' },
    { value: 1e15, symbol: 'P' },
    { value: 1e18, symbol: 'E' },
  ];
  const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
  const item = lookup.findLast((item) => num >= item.value);
  return item
    ? (num / item.value).toFixed(digits).replace(regexp, '').concat(item.symbol)
    : '0';
};

const getTotalHours = (totalMins) => {
  const TOTAL_MIN_A_HOUR = 60;
  const hour = Math.floor(totalMins / TOTAL_MIN_A_HOUR);
  const min = totalMins % TOTAL_MIN_A_HOUR;

  return `${numberWithCommas(hour)}h ${min}m`;
};

function numberFormat(number) {
  return number
    .split('')
    .reverse()
    .join('')
    .replace(/(\d{1,2})(\d{1,3})?(\d{1,3})?(\d{1,3})?/, '$1.$2,$3,$4')
    .split('')
    .reverse()
    .join('')
    .replace(/^,+/, '');
}

function moneyParse(money) {
  return money.replace(/\D/g, '');
}

const isValidJSON = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

const ADMIN_MAILS = `<EMAIL>`; // if want to add more just only add ,<mail address>

const removeWhiteSpace = (text) => (text ? text : text.replace(/ /g, ''));

const cleanText = (text) =>
  text ? removeWhiteSpace(text).toLowerCase().trim() : '';

const arrayUniqueByKey = (array, key) => [
  ...new Map(array.map((item) => [item?.[key], item])).values(),
];

const decodeHtmlEntities = (html = '') => {
  const textArea = document.createElement('textarea');
  textArea.innerHTML = html;

  return textArea.value;
};

const stripHTMLTags = (str) => str?.replace(/<[^>]*>/g, '');

const stripJobDescription = (description) =>
  stripHTMLTags(decodeHtmlEntities(description?.replaceAll('><', '>\n<')));
const removeEmptyNodes = (htmlStr) => {
  let tmp = document.createElement('div');
  tmp.innerHTML = htmlStr;
  for (let node of tmp.childNodes) {
    if (
      (!decodeHtmlEntities(node.textContent) &&
        !decodeHtmlEntities(tmp.innerText)) ||
      node.textContent.includes('&nbsp;')
    ) {
      node.remove();
    }
  }
  return tmp.innerHTML;
};

const compactWhitespace = (str) => (str ? str?.replace(/\s{2,}/g, ' ') : str);

const getDisabledHours = (offset = '+00:00') => {
  var hours = [];
  for (var i = 0; i < moment().utcOffset(offset).hour(); i++) {
    hours.push(i);
  }
  return hours;
};
const getDisabledMinutes = (selectedHour, offset = '+00:00') => {
  var minutes = [];
  if (selectedHour === moment().hour()) {
    for (var i = 0; i < moment().utcOffset(offset).minute(); i++) {
      minutes.push(i);
    }
  }
  return minutes;
};

const getOffSet = (utcString = '+00:00') =>
  utcString?.replace('UTC', '')?.replace(':', '');

const replaceSignaturePlaceholder = (content, dataSignature) => {
  let contentWithSig = content;

  const temp = document.createElement('div');
  temp.innerHTML = contentWithSig;
  // New signature node

  const oldSigNodes = temp.getElementsByClassName('signature-container');
  if (oldSigNodes.length > 0) {
    for (let i = 0; i < oldSigNodes.length; i++) {
      oldSigNodes[i].outerHTML = dataSignature;
    }

    contentWithSig = temp.innerHTML;
  }
  return contentWithSig;
};

function formatPhoneNumber(phoneNumberString) {
  var cleaned = ('' + phoneNumberString).replace(/\D/g, '');
  var match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    var intlCode = match[1] ? '+1 ' : '';
    return [intlCode, '(', match[2], ') ', match[3], '-', match[4]].join('');
  }
  return phoneNumberString;
}

const getLength = (arr) => (arr?.length > 0 ? arr?.length : null);

const getTotalAddContacts = (object) => {
  if (!object) return 0;
  let total = 0;
  for (const [key, value] of Object.entries(object)) {
    total += value?.length || 0;
  }
  return total;
};

const parseBHQuery = (ids = []) => {
  if (ids?.length === 0 || !ids) return ids;
  const query = ids?.map((id) => `id=${id}`).join(' OR ');
  return query;
};

async function PromisePool(handler, data, concurency) {
  const iterator = data.entries();
  const workers = new Array(concurency).fill(iterator).map(async (iterator) => {
    for (const [index, item] of iterator) {
      await handler(item, index);
    }
  });
  await Promise.all(workers);
}

const removeTrackingParams = (url) => {
  if (!url) return url;

  const urlObj = new URL(url);
  if (urlObj.hostname.includes('linkedin.com')) {
    urlObj.searchParams.delete('trackingId');
    urlObj.searchParams.delete('refId');
    urlObj.searchParams.delete('position');
    urlObj.searchParams.delete('pageNum');
  }

  return urlObj.toString();
};

const capFirst = (str) => {
  try {
    return str?.[0].toUpperCase() + str.slice(1);
  } catch (error) {
    return str;
  }
};

const getContentTypeLinkedinMessage = (type) =>
  type === 'img'
    ? { 'Content-Type': 'image/jpeg' }
    : {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="downloaded_file.pdf"',
      };

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

const base64ToMulterFile = (base64, filename, mimetype) => {
  // Split Base64 string to get the actual data
  const base64Data = base64.split(';base64,').pop();

  // Decode Base64 to a binary buffer
  const buffer = Buffer.from(base64Data, 'base64');

  // Create a file-like object
  return {
    fieldname: 'files', // Name of the field in the form data
    originalname: filename,
    encoding: '7bit', // Encoding type
    mimetype: mimetype, // MIME type
    buffer, // Binary buffer data
    size: buffer.length, // File size in bytes
  };
};

const cleanJobType = (rawJobType) => {
  if (!rawJobType) {
    return rawJobType;
  }
  const cleannedJobType = rawJobType
    ?.toLowerCase()
    ?.replace(/[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, ' ')
    ?.trim();

  const splitedJobType = cleannedJobType.split(' ');
  return splitedJobType.map((word) => word?.capitalize()).join(' ');
};

const mainProtectedRoutes = [
  {
    path: '/dashboard',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.DASHBOARD,
  },
  // Not using Staff Performance anymore
  // {
  //   path: '/staff-performance',
  //   roles: [userRole.MANAGEMENT, userRole.ADMIN, SUPER_ADMIN, SALES],
  //   permission: FEATURES.STAFF_PERFORMANCE,
  // },
  {
    path: '/user-management/company',
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.USER_MANAGEMENT,
  },
  {
    path: '/user-management',
    roles: [userRole.ADMIN, SUPER_ADMIN, SALES],
    permission: FEATURES.USER_MANAGEMENT,
  },
  {
    path: '/syncs',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SYNC,
  },
  // Connected only
  {
    path: '/sync-vacancy',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    license: [licenseType.CONNECTED],
    permission: FEATURES.SYNC,
  },
  {
    path: '/sync-lead',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    license: [licenseType.CONNECTED],
    permission: FEATURES.SYNC,
  },
  {
    path: '/sync-opportunity',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    license: [licenseType.CONNECTED],
    permission: FEATURES.SYNC,
  },

  // Standard only
  {
    path: '/sync-standard-lead',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    license: [licenseType.STANDARD],
    permission: FEATURES.SYNC,
  },

  {
    path: '/email-verification',
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.EMAIL_VERIFICATION,
  },
  {
    path: '/email-finder',
    roles: [
      SUPER_ADMIN,
      SALES,
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
    ],
    permission: FEATURES.EMAIL_FINDER,
  },
  {
    path: '/email-finder-beta',
    roles: [
      SUPER_ADMIN,
      SALES,
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
    ],
    permission: FEATURES.EMAIL_FINDER,
  },
  {
    path: '/reported_agencies',
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.REPORTED_AGENCIES,
  },
  {
    path: '/search',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SEARCH,
  },
  {
    path: '/duplicate-jobs',
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.DUPLICATED_JOBS,
  },
  {
    path: '/task',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.TASK,
  },
  {
    path: '/settings',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SETTINGS,
  },
  {
    path: '/my-leads',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MY_LEADS,
  },
  {
    path: '/sequence',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SEQUENCE,
  },
  {
    path: '/mail-box',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MAILBOX,
  },
  {
    path: '/manual-leads',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MANUAL_LEADS,
  },
  {
    path: '/crm/setting',
    roles: [userRole.ADMIN, SUPER_ADMIN],
    permission: FEATURES.SETTING_ADMIN,
  },
  {
    path: '/crm',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.CRM,
  },
  {
    path: '/company/onboarding',
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.COMPANY_ONBOARDING,
  },
  {
    path: '/credit-management',
    roles: [
      userRole.ADMIN,
      SUPER_ADMIN,
    ],
    permission: FEATURES.CREDIT_MANAGEMENT,
  },
];

export {
  numberWithCommas,
  nFormatter,
  getTotalHours,
  moneyParse,
  numberFormat,
  isValidJSON,
  ADMIN_MAILS,
  cleanText,
  removeWhiteSpace,
  arrayUniqueByKey,
  decodeHtmlEntities,
  stripHTMLTags,
  stripJobDescription,
  removeEmptyNodes,
  compactWhitespace,
  getDisabledHours,
  getDisabledMinutes,
  getOffSet,
  replaceSignaturePlaceholder,
  formatPhoneNumber,
  getLength,
  getTotalAddContacts,
  parseBHQuery,
  PromisePool,
  removeTrackingParams,
  capFirst,
  getContentTypeLinkedinMessage,
  getBase64,
  base64ToMulterFile,
  cleanJobType,
  mainProtectedRoutes,
};
