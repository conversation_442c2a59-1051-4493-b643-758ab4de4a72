import React, { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import Routing from './routing';
import { useAuth } from './store/auth';
import SplashScreen from './components/SplashScreen';
import { useViewAs } from './store/viewAs';
import { ConfigProvider } from 'antd';
import './AntdOverride.scss';
import "/node_modules/flag-icons/css/flag-icons.min.css";

import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';

function App() {
  const [loading, setLoading] = useState(true);
  const queryClient = useQueryClient();
  const { clearViewAs } = useViewAs();
  const { clearAuth } = useAuth();

  dayjs.extend(customParseFormat);
  dayjs.extend(advancedFormat);
  dayjs.extend(weekday);
  dayjs.extend(localeData);
  dayjs.extend(weekOfYear);
  dayjs.extend(weekYear);

  const onLogout = async () => {
    clearViewAs();
    clearAuth();
    queryClient.setQueryData(['CURRENT_USER'], null);
  };

  useEffect(() => {
    const handleLoad = () => {
      setLoading(false);
    };
    let timeoutId;

    const resetTimeout = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        onLogout();
        window.location.href = '/login';
      }, +import.meta.env.VITE_TIME_OUT * 60 * 1000);
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }
  
    window.addEventListener('mousemove', resetTimeout);
    window.addEventListener('keydown', resetTimeout);
  
    resetTimeout();
  
    return () => {
      console.log("Cleanup on unmount");
      clearTimeout(timeoutId);
      window.removeEventListener('mousemove', resetTimeout);
      window.removeEventListener('keydown', resetTimeout);
      window.removeEventListener('load', handleLoad);
    };
  }, []);


  if (loading) return <SplashScreen />;

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#0891b2',
          colorError: '#C0201E',
        },
        components: {
          Table: {
            headerBg: '#0891b2',
            headerColor: '#ffffff',
            headerSortActiveBg: '#0891b2',
            headerSortHoverBg: '#0891b2',
            headerSplitColor: '#0891b2',
            borderColor: '#E5E5E5',
          },
          Layout: {
            siderBg: '#ffffff',
          },
        },
      }}
    >
      <div className="App">
        <Routing />
      </div>
    </ConfigProvider>
  );
}

export default App;
