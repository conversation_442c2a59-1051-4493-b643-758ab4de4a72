import { getUserViewAs } from '../../helpers/getUserViewAs';
import { restAPI } from '../core';

export const api = restAPI.injectEndpoints({
  endpoints: (build) => ({
    getLogOfJobLeads: build.query({
      query: ({ statusId, page, limit }) =>
        `/job-lead/leads/${statusId}/view-as/${getUserViewAs()}?&page=${page}&limit=${limit}`,
    }),
    getJobLeadsCompanies: build.query({
      query: ({ statusId, page, limit, search }) =>
        `/job-lead/leads/${statusId}/view-as/${getUserViewAs()}/companies?search=${search}&page=${page}&limit=${limit}`,
    }),
    searchJobLeadsCompanies: build.query({
      query: ({ companyId, statusId, limit = 1000 }) => {
        const queryText = `start=0&count=${limit}${companyId ? `&companyIds[]=${companyId}` : ''}${statusId ? `&statusId=${statusId}` : ''}`;
        return `/job-lead/search-vacancies/view-as/${getUserViewAs()}?${queryText}`;
      },

      // `/job-lead/leads/${statusId}/view-as/${getUserViewAs()}/company/${companyId}`,
    }),
    changeLogOfJobLeads: build.mutation({
      query: ({ jobLeadId, newStatusId, updatedFor, isUpdateMailBox }) => ({
        url: '/job-lead/change-status',
        method: 'PUT',
        body: {
          jobLeadId: jobLeadId,
          newStatusId: newStatusId,
          updatedFor: updatedFor,
          isUpdateMailBox: isUpdateMailBox,
        },
      }),
    }),
    changeStatusByCompany: build.mutation({
      query: ({
        companyId,
        newStatusId,
        updatedFor,
        newStatusCompanyIdsOrder,
      }) => ({
        url: '/job-lead/change-status-by-company',
        method: 'PUT',
        body: {
          companyId: companyId,
          newStatusId: newStatusId,
          updatedFor: updatedFor,
          newStatusCompanyIdsOrder,
        },
      }),
    }),
    updateLeadStatus: build.mutation({
      query: ({ jobLeadId, assigneeId, creatorId, updatedBy }) => ({
        url: `/job-lead/${jobLeadId}/update-lead`,
        method: 'PATCH',
        body: {
          assigneeId: assigneeId,
          creatorId: creatorId,
          updatedBy: updatedBy,
        },
      }),
    }),
    updateBulkLeads: build.mutation({
      query: ({ payload }) => ({
        url: `/lead-statuses/bulk-update`,
        method: 'POST',
        body: {
          ...payload,
          updatedBy: getUserViewAs(),
        },
      }),
    }),
    getJobLeadDetail: build.query({
      query: ({ id }) => `/job-lead/${id}`,
    }),
  }),
});

export const {
  useLazyGetLogOfJobLeadsQuery,
  useChangeLogOfJobLeadsMutation,
  useChangeStatusByCompanyMutation,
  useUpdateLeadStatusMutation,
  useLazyGetJobLeadDetailQuery,
  useLazyGetJobLeadsCompaniesQuery,
  useLazySearchJobLeadsCompaniesQuery,
  useUpdateBulkLeadsMutation,
} = api;
