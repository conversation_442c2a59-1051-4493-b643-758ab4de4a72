import { restAPI } from '../core';

export const api = restAPI.injectEndpoints({
  endpoints: (build) => ({
    getConfigForm: build.query({
      query: ({ userId, name }) =>
        `/user-settings/my-config-by-name/view-as/${userId}?name=${name}`,
    }),
    changeConfigForm: build.mutation({
      query: ({ userId, payload }) => ({
        url: `/user-settings/update-bh-vacancy-config/view-as/${userId}`,
        method: 'PUT',
        body: {
          data: payload,
        },
      }),
    }),
  }),
});

export const { useLazyGetConfigFormQuery, useChangeConfigFormMutation } = api;
