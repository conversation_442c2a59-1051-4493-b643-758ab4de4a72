import { restAPI } from '../core';

export const api = restAPI.injectEndpoints({
  endpoints: (build) => ({
    getCompany: build.query({
      query: ({ searchId, userId, jsonToSearch, queryString }) =>
        `/job-search/${searchId}/company/view-as/${userId}/open-search?getAll=true&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`,
    }),
    getJobByCompany: build.query({
      query: ({ searchId, userId, company, jsonToSearch, queryString }) => {
        const encodedCompany = encodeURIComponent(company);
        return `/job-search/${searchId}/view-as/${userId}/open-search?companies=${encodedCompany}&${new URLSearchParams(jsonToSearch).toString()}&${queryString}`;
      },
    }),
    bulkGetJobByCompanies: build.query({
      query: ({ searchId, userId, jsonToSearch, queryString }) => {
        return `/job-search/${searchId}/view-as/${userId}/open-search?${new URLSearchParams(jsonToSearch).toString()}&${queryString}`;
      },
    }),
    updateStatusGroupJob: build.mutation({
      query: ({ searchId, status }) => ({
        url: `job-search/${searchId}/update-status-group-job-by-company`,
        method: 'PATCH',
        body: {
          status: status,
        },
      }),
    }),
    updateRefineBySearchStatus: build.mutation({
      query: ({ searchId, status }) => ({
        url: `job-search/${searchId}/update-refine-by-search-status`,
        method: 'PATCH',
        body: {
          status: status,
        },
      }),
    }),
  }),
});

export const {
  useLazyGetCompanyQuery,
  useLazyGetJobByCompanyQuery,
  useUpdateStatusGroupJobMutation,
  useLazyBulkGetJobByCompaniesQuery,
  useUpdateRefineBySearchStatusMutation
} = api;
