import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

import _get from 'lodash/get';

import axiosInstance from '../../utils/axios';
import { notification } from 'antd';
import { getUserViewAs } from '../../helpers/getUserViewAs';

const extendedFetchBaseQuery =
  (baseQueryFn) => async (args, api, extraOptions) => {
    try {
      const result = await baseQueryFn(args, api, extraOptions);

      const statusCode = _get(result, 'error.status');
      const messageCode = _get(result, 'error.data.message');

      if (statusCode === 401 && messageCode === 'Unauthorized') {
        try {
          // Gọi API để làm mới token
          await axiosInstance.post('/auth/refresh-token');

          // Sau khi làm mới token, gọi lại truy vấn ban đầu
          const refreshedResult = await baseQueryFn(args, api, extraOptions);

          // Tr<PERSON> về kết quả sau khi làm mới token
          return refreshedResult;
        } catch (error) {
          notification.error({
            message: 'Unauthorized',
            description: 'You need to login again',
          });
        }
      }

      return result;
    } catch (error) {
      notification.error({
        message: 'Unauthorized',
        description: 'You need to login again',
      });

      throw error; // Ném lại lỗi để cho phép React Query xử lý lỗi
    }
  };

export const restAPI = createApi({
  baseQuery: extendedFetchBaseQuery(
    fetchBaseQuery({
      baseUrl: import.meta.env.VITE_API_URL,
      credentials: 'include',
      prepareHeaders: async (headers) => {
        // Có thể thêm logic để thêm headers mới sau khi làm mới token nếu cần
        headers.set('view-as-user-id', getUserViewAs());
        return headers;
      },
    })
  ),
  endpoints: () => ({}),
});
