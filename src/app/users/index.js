import { restAPI } from '../core';
import { getUserViewAs } from '../../helpers/getUserViewAs';

export const api = restAPI.injectEndpoints({
  endpoints: (build) => ({
    getAllUsers: build.query({
      query: ({ userId }) => `/users/view-as/${userId}`,
    }),
    updateBullhornConfig: build.mutation({
      query: ({ clientId, clientSecret, username, password }) => ({
        url: `/users/${getUserViewAs()}/update-bullhorn-config`,
        method: 'PATCH',
        body: {
          clientId,
          clientSecret,
          username,
          password
        },
      }),
    }),
  }),
});

export const { useLazyGetAllUsersQuery, useUpdateBullhornConfigMutation } = api;
