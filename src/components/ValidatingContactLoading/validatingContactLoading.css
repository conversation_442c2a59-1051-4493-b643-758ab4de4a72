.loader {
  width: 12em;
  height: 12em;
}

.loader path {
  stroke: #000;
  stroke-width: 0.6px;
}

.spin {
  animation:
    dashArray 4s ease-in-out infinite,
    dashOffset 4s linear infinite;
}

@keyframes dashArray {
  0% {
    stroke-dasharray: 0 1 359 0;
  }

  50% {
    stroke-dasharray: 0 359 1 0;
  }

  100% {
    stroke-dasharray: 359 1 0 0;
  }
}

@keyframes dashOffset {
  0% {
    stroke-dashoffset: 365;
  }

  100% {
    stroke-dashoffset: 5;
  }
}

.dots-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.dot {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  border-radius: 10px;
  @apply bg-cyan-700;
}
.spin .dot {
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:last-child {
  margin-right: 0;
}

.dot:nth-child(1) {
  animation-delay: -0.3s;
}

.dot:nth-child(2) {
  animation-delay: -0.1s;
}

.dot:nth-child(3) {
  animation-delay: 0.1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    /* background-color: #b3d4fc; */
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
    @apply bg-cyan-600;
  }

  50% {
    transform: scale(1.2);
    /* background-color: #6793fb; */
    box-shadow: 0 0 0 10px rgba(178, 212, 252, 0);
    @apply bg-cyan-700;
  }

  100% {
    transform: scale(0.8);
    /* background-color: #b3d4fc; */
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
    @apply bg-cyan-800;
  }
}
