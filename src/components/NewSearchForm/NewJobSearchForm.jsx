/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React from 'react';
import { Controller } from 'react-hook-form';
import {
  DatePicker,
  Select,
  Input,
  AutoComplete,
  Button,
  Form,
  Row,
  Col,
} from 'antd';

const { Option } = Select;
const { RangePicker } = DatePicker;

const NewJobSearchForm = (props) => {
  const { control, handleSubmit, reset, onSubmit } = props;
  return (
    <Form layout="vertical" onFinish={handleSubmit(onSubmit)}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Job title name"
            name="jobTitle"
            rules={[{ required: true, message: 'Job title is required' }]}
          >
            <Controller
              name="jobTitle"
              render={({ field }) => <Input {...field} />}
              control={control}
            />
          </Form.Item>

          <Form.Item label="Keyword" name="keywords">
            <Controller
              control={control}
              name="keywords"
              render={({ field }) => (
                <Select mode="tags" style={{ width: '100%' }} {...field} />
              )}
            />
          </Form.Item>

          <Form.Item label="Location" name="location">
            <AutoComplete>
              <Input.Search placeholder="Search locations" />
            </AutoComplete>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Posted Within" name="dateRange">
            <Controller
              control={control}
              name="dateRange"
              render={({ field }) => (
                <RangePicker {...field} style={{ width: '100%' }} />
              )}
            />
          </Form.Item>

          <Form.Item label="Salary" style={{ marginBottom: 0 }}>
            <Form.Item
              style={{ display: 'inline-block', width: 'calc(50% - 8px)' }}
              name="min_salary"
            >
              <Controller
                name="min_salary"
                render={({ field }) => (
                  <Input {...field} placeholder="Min Salary" type="number" />
                )}
                control={control}
              />
            </Form.Item>
            <span
              style={{
                display: 'inline-block',
                width: '16px',
                textAlign: 'center',
              }}
            >
              -
            </span>
            <Form.Item
              style={{ display: 'inline-block', width: 'calc(50% - 8px)' }}
              name="max_salary"
            >
              <Controller
                name="max_salary"
                render={({ field }) => (
                  <Input {...field} placeholder="Max Salary" type="number" />
                )}
                control={control}
              />
            </Form.Item>
          </Form.Item>

          <Form.Item label="Job Boards" name="jobBoards">
            <Controller
              control={control}
              name="jobBoards"
              render={({ field }) => (
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  {...field}
                  options={[
                    'technojobs',
                    'monster',
                    'cv-library',
                    'reed',
                    'indeed',
                    'linkedin',
                    'totaljobs',
                    'jobsite',
                    'seek',
                  ]}
                />
              )}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item>
        <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
};

export default NewJobSearchForm;
