import React from 'react';
import PropTypes from 'prop-types';
import './style.css';
import { Link } from 'react-router-dom';
import { Content, PopoverStyle, Title } from './style';
import { CheckOutlined } from '@ant-design/icons';

const Popover = (props) => {
  const {
    content,
    placement,
    title,
    action,
    children,
    openStatus,
    setOpenStatus = (value) => {},
  } = props;
  const content1 = <Content>{content}</Content>;
  return (
    <PopoverStyle
      placement={placement}
      title={title && <Title>{title}</Title>}
      content={content1}
      trigger={action}
      open={openStatus}
      onOpenChange={(value) => {
        setOpenStatus(value);
      }}
    >
      {children}
    </PopoverStyle>
  );
};

const content = (
  <>
    <Link to="#">
      <CheckOutlined />
      <span>Btn Dropdown one</span>
    </Link>
    <Link to="#">
      <CheckOutlined />
      <span>Btn Dropdown two</span>
    </Link>
    <Link to="#">
      <CheckOutlined />
      <span>Btn Dropdown three</span>
    </Link>
  </>
);

Popover.defaultProps = {
  action: 'hover',
  placement: 'bottomCenter',
  content,
};

Popover.propTypes = {
  placement: PropTypes.string,
  title: PropTypes.string,
  action: PropTypes.string,
  content: PropTypes.node,
  children: PropTypes.node.isRequired,
};

export { Popover };
