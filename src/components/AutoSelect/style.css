/* * {
  box-sizing: border-box;
}

*/
.autocomplete-wrapper {
  width: 70%;
  position: relative;
  min-width: 350px;
}

.autocomplete-wrapper > div {
  width: 100%;
}

.autocomplete-wrapper input {
  border: 1px solid #cecece;
  padding: 5px 15px;
  font-family: 'PoppinsSemiBold';
  font-size: 14px;
  width: 100%;
  border-radius: 5px;
}

.autocomplete-wrapper input:focus {
  border-color: black;
  box-shadow: none;
  outline: none;
  border: 3px solid black;
}

.autocomplete-wrapper .dropdown {
  width: 100%;
  padding: 0;
  text-align: left;
  max-height: 280px;
  overflow: hidden;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #0f67ff;
  border-top: none;
}

.autocomplete-wrapper .item {
  display: block;
  cursor: pointer;
  font-size: 14px;
  padding: 15px;
  font-family: 'PoppinsRegular';
}

.autocomplete-wrapper .item.selected-item {
  background-color: #0069ff;
  color: #fafbfc;
}

.autocomplete-wrapper .item:hover {
  background-color: #0069ff;
  color: #fafbfc;
}
