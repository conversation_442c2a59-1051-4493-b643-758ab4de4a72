import React from 'react';
import { MoviesData, renderMovieTitle } from './movies-data';
import Autocomplete from 'react-autocomplete';
import { SearchOutlined } from '@ant-design/icons';
import './style.css';

const AutoSelect = () => {
  //   state = { val: "" };

  return (
    <div className="autocomplete-wrapper flex">
      <Autocomplete
        className="relative"
        // value={this.state.val}
        items={MoviesData()}
        getItemValue={(item) => item.title}
        shouldItemRender={renderMovieTitle}
        renderMenu={(item) => <div className="dropdown">{item}</div>}
        renderItem={(item, isHighlighted) => (
          <div className={`item ${isHighlighted ? 'selected-item' : ''}`}>
            {item.title}
          </div>
        )}
        onChange={(event, val) => this.setState({ val })}
        onSelect={(val) => this.setState({ val })}
      />
      <SearchOutlined className="absolute top-3 right-2" />
    </div>
  );
};

export default AutoSelect;
