/* eslint-disable react/prop-types */
import React from 'react';
import { Tag } from 'antd';
import { TweenOneGroup } from 'rc-tween-one';

const App = ({ tags, setTags, setViewData }) => {
  const showModal = (jobTitle) => {
    setViewData(tags.filter((tag) => tag.jobTitle === jobTitle.jobTitle));
  };

  const handleClose = (removedTag) => {
    const newTags = tags.filter((tag) => tag !== removedTag);
    localStorage.setItem('savedSearch', JSON.stringify(newTags));
    setTags(newTags);
  };

  const forMap = (tag) => {
    const tagElem = (
      <Tag
        className="text-base font-[PoppinsRegular] xl:font-[PoppinsMedium] cursor-pointer m-[2px]"
        closable
        color="#108ee9"
        onClick={() => showModal(tag)}
        onClose={(e) => {
          e.preventDefault();
          handleClose(tag);
        }}
      >
        {tag.jobTitle}
      </Tag>
    );
    return (
      <span
        key={tag.jobTitle}
        style={{
          display: 'inline-block',
        }}
      >
        {tagElem}
      </span>
    );
  };
  const tagChild = tags.map(forMap);
  return (
    <div
      style={{
        marginBottom: 16,
      }}
    >
      <TweenOneGroup
        enter={{
          scale: 0.8,
          opacity: 0,
          type: 'from',
          duration: 100,
        }}
        onEnd={(e) => {
          if (e.type === 'appear' || e.type === 'enter') {
            e.target.style = 'display: inline-block';
          }
        }}
        leave={{
          opacity: 0,
          width: 0,
          scale: 0,
          duration: 200,
        }}
        appear={false}
      >
        {tagChild}
      </TweenOneGroup>
    </div>
  );
};
export default App;
