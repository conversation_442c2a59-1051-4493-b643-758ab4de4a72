import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { IoDocuments } from 'react-icons/io5';
import Widget from '../Widget/Widget';
import { getPotentialLeadReturn } from '../../services/jobLeads';

const PotentialLeadReturn = () => {
  const {
    data: { minRange, maxRange },
  } = useQuery(['GET_POTENTIAL_LEAD_RETURN'], {
    queryFn: async () => {
      const { data } = await getPotentialLeadReturn();
      return data.result;
    },
    initialData: { minRange: 0, maxRange: 0 },
  });
  return (
    <div className="grid bg-white rounded-[20px] shadow-3xl shadow-shadow-500 h-min">
      <Widget
        icon={<IoDocuments className="w-6 h-min" />}
        title={'My Leads Potential Value'}
        subtitle={`£${minRange} - £${maxRange}`}
      />
    </div>
  );
};

export default PotentialLeadReturn;
