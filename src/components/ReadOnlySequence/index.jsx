import { CaretRightFilled } from '@ant-design/icons';
import { Collapse, Tag, Tooltip } from 'antd';
import { v4 as uuid } from 'uuid';
import {
  ADD_STEP_TYPE,
  ADD_STEP_TYPE_NAME,
} from '../BullHorn/EmailtriggerStep';
import dayjs from 'dayjs';
import Loading from '../../containers/HotList/Loading';
import { useEffect, useState } from 'react';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import {
  getContactLists,
  getHotLists,
  getShortLists,
} from '../../containers/Sequence/SequenceDetail';
import { getListEmailFromSequence } from '../../services/search';
import clsx from 'clsx';

const STEPS_COLORS = {
  ADD_WAIT: 'bg-[#CA7842]',
  TASK: 'bg-[#5A827E]',
  EMAIL: 'bg-[#3D90D7]',
  NOTE: 'bg-[#547792]',
  DUPLICATE_MAIL: 'bg-[#3D90D7]',
  LINKEDIN_CONNECTION_REQUEST: 'bg-[#3D90D7]',
};

const STEPS_COLORS_HEX = {
  ADD_WAIT: '#CA7842',
  TASK: '#5A827E',
  EMAIL: '#3D90D7',
  NOTE: '#547792',
  DUPLICATE_MAIL: '#3D90D7',
  LINKEDIN_CONNECTION_REQUEST: '#3D90D7',
};
const ReadOnlySequence = ({
  //   sequenceLoading = false,
  //   inputNumberStep = [],
  //   participants = [],
  lead = null,
  sequenceId = null,
}) => {
  const [sequenceLoading, setSequenceLoading] = useState(false);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [participants, setParticipants] = useState([]);

  const getSequenceDataByLead = async () => {
    try {
      const fromSequenseEmail = lead?.job_board_id ? false : true;
      const jobLeadExternalId = lead?.job_lead_external_id || '';
      const externalId = jobLeadExternalId
        ? jobLeadExternalId?.split('-')[1]
        : '';
      const id =
        lead?.job_board_id ||
        externalId ||
        lead?.job_id ||
        lead?.leadRawId ||
        lead?.id;
      setSequenceLoading(true);
      const { data } = await getEmailConfigInJobBoard(id, fromSequenseEmail);
      if (data) {
        const newValueArr = data?.result?.mails?.map((item) => {
          return { ...item };
        });

        if (newValueArr[0]?.sequence?.id) {
          const stepsTemp = data?.result?.mails;
          // Mapping participants in Linkedin Step

          const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
            return [
              ...step?.delays.map((delayItem) => ({
                ...delayItem,
                unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
                subject: 'Delay',
                type: ADD_STEP_TYPE.ADD_WAIT,
                key: uuid(),
              })),
              { ...step, key: step?.id || uuid() },
            ];
          });
          setInputNumberStep([...inputNumberStepTemp]);
        } else {
          setInputNumberStep([]);
        }

        const recipients =
          data?.result?.mails?.[0]?.sequence?.participants || null;
        if (recipients) {
          const {
            contactListIds,
            hotListIds,
            recipients: sendToList,
            shortListIds,
          } = recipients;
          const list = await Promise.all([
            await getContactLists(contactListIds),
            await getHotLists(hotListIds),
            await getShortLists(shortListIds),
          ]);
          const contactListTemp = list[0] || [];
          const hotListtemp = list[1] || [];
          const shortListTemp = list[2] || [];
          const participantsTemp = [
            ...contactListTemp,
            ...hotListtemp,
            ...shortListTemp,
            ...sendToList,
          ];
          setParticipants(participantsTemp);
        }
        setSequenceLoading(false);
      }
    } catch (error) {
      console.log('error: ', error);
      setSequenceLoading(false);
    }
  };

  const getSequenceDataById = async () => {
    if (!sequenceId) {
      setSequenceLoading(false);
      return;
    }
    setSequenceLoading(true);
    try {
      const { data } = await getListEmailFromSequence(sequenceId);

      const stepsTemp = data?.result?.mails;
      const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
        return [
          ...step?.delays.map((delayItem) => ({
            ...delayItem,
            unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
            subject: 'Delay',
            type: ADD_STEP_TYPE.ADD_WAIT,
            key: uuid(),
          })),
          { ...step, key: step?.id || uuid() },
        ];
      });
      setInputNumberStep([...inputNumberStepTemp]);
      const participantsTemp =
        data?.result?.sequence?.participants ||
        data?.result?.recipients?.[0] ||
        null;

      if (participantsTemp) {
        const {
          contactListIds,
          hotlistIds,
          recipients: sendToList,
          shortListIds = [],
        } = participantsTemp;

        const list = await Promise.all([
          await getContactLists(contactListIds),
          await getHotLists(hotlistIds),
          await getShortLists(shortListIds),
        ]);

        setParticipants([
          ...(list[1] || []),
          ...(list[1] || []),
          ...(list[2] || []),
          ...(sendToList || []),
        ]);
      }
      setSequenceLoading(false);
    } catch (error) {
      setSequenceLoading(false);
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    if (lead) {
      getSequenceDataByLead();
    }
    if (sequenceId) {
      getSequenceDataById();
    }
  }, [lead, sequenceId]);
  return (
    <div className="flex flex-col gap-5 pr-2 items-center">
      {!sequenceLoading && inputNumberStep?.length > 0 && (
        <div className="flex flex-wrap items-center justify-center gap-1 text-[#5e768d] font-medium py-4 px-2 border rounded-md w-[24rem] min-h-[5rem] max-h-[10rem] overflow-y-auto">
          <span>Participants: </span>
          <Tag color="cyan">{participants?.[0]?.name}</Tag>
          {participants?.slice(1, participants?.length - 1)?.length > 0 && (
            <div className="flex gap-1 items-center">
              <span>and</span>
              <Tooltip
                title={`${participants
                  ?.slice(1, participants?.length)
                  ?.map((participant) => participant?.name)
                  .join(', ')}`}
              >
                <span className="text-[#08979c] font-medium cursor-pointer">
                  Others
                </span>
              </Tooltip>
            </div>
          )}
        </div>
      )}
      <div
        className={clsx(
          'flex flex-col gap-5 overflow-y-auto pr-2',
          !sequenceId && 'max-h-[30rem]'
        )}
      >
        {!sequenceLoading &&
          inputNumberStep?.map((step) => (
            <Collapse
              key={uuid()}
              className={clsx('w-[24rem]', STEPS_COLORS[step?.type])}
              //   onChange={onChange}
              expandIconPosition={'end'}
              expandIcon={({ isActive }) => (
                <CaretRightFilled
                  className="!text-white"
                  rotate={isActive ? 90 : 0}
                />
              )}
              items={[
                {
                  key: uuid(),
                  label: (
                    <div className=" w-full grid grid-cols-3 items-center gap-2 justify-between">
                      <span className="line-clamp-1 text-white font-medium">
                        {step?.name}
                      </span>
                      <div className="flex items-center justify-center">
                        <Tag
                          color={STEPS_COLORS_HEX[step?.type]}
                          className="w-fit"
                        >
                          <span className="text-xs font-semibold">
                            {ADD_STEP_TYPE_NAME[step?.type] || step?.type}
                          </span>
                        </Tag>
                      </div>
                      {step?.executedAt && (
                        <div className="flex justify-end items-center text-xs text-[#5e768d] w-full gap-1">
                          <Tooltip
                            title={`Executed at ${dayjs(step?.executedAt).format('DD/MM/YYYY, hh:mm a')}`}
                          >
                            <Tag
                              color="success"
                              className="font-semibold w-fit text-xs"
                            >
                              Executed
                            </Tag>
                          </Tooltip>
                        </div>
                      )}
                      {/* {!step?.executedAt && (
                        <div className="flex justify-end items-end text-xs flex-col text-[#5e768d] w-full">
                          <Tag className="font-semibold w-fit">Not sent</Tag>
                        </div>
                      )} */}
                    </div>
                  ),
                  children: (
                    <div>
                      {step?.recipients?.length > 0 && (
                        <div className="flex flex-wrap items-center justify-center gap-1 text-[#5e768d] font-medium py-4 px-2 border rounded-md w-[22rem] mb-2 max-h-[5rem] overflow-y-hidden">
                          <span>Participants: </span>
                          <Tag color="cyan">{step?.recipients?.[0]?.name}</Tag>
                          {step?.recipients?.slice(
                            1,
                            step?.recipients?.length - 1
                          )?.length > 0 && (
                            <div className="flex gap-1 items-center">
                              <span>and</span>
                              <Tooltip
                                title={`${step?.recipients
                                  ?.slice(1, step?.recipients?.length)
                                  ?.map((participant) => participant?.name)
                                  .join(', ')}`}
                              >
                                <span className="text-[#08979c] font-medium cursor-pointer">
                                  Others
                                </span>
                              </Tooltip>
                            </div>
                          )}
                        </div>
                      )}
                      <div className="flex justify-between pb-3 border-b border-[#5e768d]">
                        {step?.type === ADD_STEP_TYPE.SEND_MAIL ? (
                          <div className="flex items-center gap-2 text-[#5e768d] font-medium">
                            <span>Subject: </span>
                            <span
                              title={step?.subject}
                              className="line-clamp-1"
                            >
                              {step?.subject}
                            </span>
                          </div>
                        ) : (
                          <Tag color="cyan">{step?.subject}</Tag>
                        )}
                        {step?.type === ADD_STEP_TYPE.ADD_WAIT && (
                          <div className="flex items-center gap-2">
                            <span className="text-[#5e768d] font-semibold">
                              {step?.delay}
                            </span>
                            <span className="text-[#5e768d] font-medium">
                              {step?.unit}
                            </span>
                          </div>
                        )}
                      </div>
                      {step?.type ===
                        ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
                        JSON.parse(step?.content)?.[0]?.message?.trim() && (
                          <p
                            className="max-w-[24rem] p-3 mt-2 border rounded-md"
                            dangerouslySetInnerHTML={{
                              __html: JSON.parse(step?.content)?.[0]?.message,
                            }}
                          ></p>
                        )}
                      {step?.type !==
                        ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
                        step?.content?.trim() && (
                          <p
                            className="max-w-[24rem] p-3 mt-2 border rounded-md"
                            dangerouslySetInnerHTML={{
                              __html: step?.content,
                            }}
                          ></p>
                        )}
                    </div>
                  ),
                },
              ]}
            />
          ))}
      </div>
      {!sequenceLoading && inputNumberStep?.length === 0 && (
        <div className="text-gray-500 font-medium">No Sequence found!</div>
      )}
      {sequenceLoading && (
        <div className="flex justify-center items-center gap-2 text-sm font-medium opacity-60 italic">
          <Loading />
          Loading...
        </div>
      )}
    </div>
  );
};

export default ReadOnlySequence;
