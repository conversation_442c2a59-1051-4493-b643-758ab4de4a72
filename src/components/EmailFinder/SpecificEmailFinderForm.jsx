/* eslint-disable react/jsx-key */
/* eslint-disable react/prop-types */
import { Button, Form, Input } from 'antd';
import { Controller, useForm } from 'react-hook-form';
import React, { useEffect, useState } from 'react';
import { searchEmail } from '../../services/emailFinder';

const SpecificEmailFinderForm = ({ selectedRow }) => {
  const { handleSubmit, control, reset } = useForm();
  const [result, setResult] = useState(null);
  useEffect(() => {
    reset(selectedRow);
  }, [selectedRow]);

  const onSubmit = async (data) => {
    const response = await searchEmail(data);
    setResult(response.data);
  };
  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div style={{ marginBottom: '16px' }}>
          <Form.Item
            label="Company Name"
            name="companyName"
            rules={[{ required: true }]}
          >
            <Controller
              render={({ field }) => <Input {...field} />}
              name="companyName"
              control={control}
            />
          </Form.Item>
        </div>
        <div style={{ marginBottom: '16px' }}>
          <Form.Item
            label="Company Linkedin URL"
            name="companyLinkedinURL"
            rules={[{ required: true }]}
          >
            <Controller
              render={({ field }) => <Input {...field} />}
              name="companyLinkedinURL"
              control={control}
            />
          </Form.Item>
        </div>
        <div style={{ marginBottom: '16px' }}>
          <Form.Item label="Full Name" name="name" rules={[{ required: true }]}>
            <Controller
              render={({ field }) => <Input {...field} />}
              name="fullName"
              control={control}
            />
          </Form.Item>
        </div>
        <Button type="primary" htmlType="submit">
          Search
        </Button>
      </form>
      {result && (
        <>
          <div>
            <b>Found Email:</b> {result.email || ''}
          </div>
          <div>
            <b>Most probable emails:</b>
            {(result.mostProbableEmail || []).map((e) => (
              <div>{e}</div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default SpecificEmailFinderForm;
