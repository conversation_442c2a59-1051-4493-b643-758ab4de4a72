import React, { memo } from 'react';
import SearchTableV2 from '../SearchTableV2/SearchTableV2';
import useSyncSearches from '../../hooks/useSyncSearches';
import Search from 'antd/es/input/Search';

const SyncListV2 = () => {
  const {
    // searches,
    fetchSearches,
    sortField,
    setSortField,
    sortOrder,
    setSortOrder,
    pagination,
    setPagination,
    data,
    setSearchText,
    loading: loadingSearch,
  } = useSyncSearches();

  const handleSearch = async (searchText) => {
    setSearchText(searchText);
    await fetchSearches();
  };

  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '20px',
        }}
      >
        <div></div>
        <div
          style={{
            width: '300px',
          }}
        >
          <Search
            className="customize-search-container"
            allowClear
            placeholder="Search anything..."
            enterButton="Search"
            size="middle"
            loading={loadingSearch}
            onSearch={handleSearch}
          />
        </div>
      </div>
      <SearchTableV2
        isSearching={loadingSearch}
        syncId={true}
        searches={data}
        populateSearchData={fetchSearches}
        sortField={sortField}
        setSortField={setSortField}
        sortOrder={sortOrder}
        setSortOrder={setSortOrder}
        pagination={pagination}
        setPagination={setPagination}
      />
    </>
  );
};

export default memo(SyncListV2);
