/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React from 'react';
import { Button, Table } from 'antd';
import { BiChevronRight } from 'react-icons/bi';
import { useNavigate } from 'react-router-dom';

const StaffPerformanceTable = ({ staffs, syncId = null }) => {
  const navigate = useNavigate();
  const columns = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Action',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      render: (text, record) => (
        <div className="flex justify-end">
          <Button
            // TODO
            // Redirect to dashboard of each staff
            onClick={() => {
              navigate(`/staff-performance/${record.id}`);
            }}
            className="flex items-center"
            type="primary"
          >
            View Performance
            <BiChevronRight className="text-lg" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Table dataSource={staffs} columns={columns} rowClassName="custom-row" />
  );
};

export default StaffPerformanceTable;
