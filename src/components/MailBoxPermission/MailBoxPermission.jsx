import React from 'react';
import axios from 'axios';
import { useEffect, useState } from 'react';

import './style.css';
import { Link, useSearchParams } from 'react-router-dom';
import success from '../../assets/img/success.png';
import fail from '../../assets/svg/fail-19.svg';
import SplashScreen from '../SplashScreen/index';
import { getUserViewAs } from '../../helpers/getUserViewAs';

function MailBoxPermission() {
  const [searchParams] = useSearchParams();
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const validate = async () => {
      try {
        setLoading(true);
        const code = searchParams.get('code');
        const state = searchParams.get('state');

        if (!code || !state) {
          setError(true);
          setLoading(false);
          return;
        }
        const baseURL = import.meta.env.VITE_API_URL;
        const axiosInstance = axios.create({
          headers: {
            'Access-Control-Allow-Origin': baseURL,
            'view-as-user-id': getUserViewAs(),
          },
        });

        await axiosInstance.get(`${baseURL}/emails/oauth/exchange-mailbox`, {
          params: { code, state },
        });
      } catch (error) {
        setError(true);
        setLoading(false);
      }
      setLoading(false);
    };

    validate();
  }, []);

  if (loading) return <SplashScreen />;

  return (
    <div className="mail-permission">
      <div className="mail-permission-header">
        {!error ? (
          <>
            <img src={success} className="mail-permission-logo" alt="logo" />
            <b>YOU ARE DONE</b>
            <p>Now you can use Zileo mailbox</p>
            {window.opener ? (
              <Link
                onClick={() => window.close()}
                to="#"
                style={{ color: '#0891b2' }}
              >
                {' '}
                Close this window
              </Link>
            ) : (
              <Link to="/" style={{ color: '#0891b2' }}>
                {' '}
                Go to Zileo
              </Link>
            )}
          </>
        ) : (
          <>
            <img src={fail} className="mail-permission-logo" alt="logo" />
            <b>OOPS!</b>
            <p>Some thing went wrong!</p>
            {window.opener ? (
              <Link
                onClick={() => window.close()}
                to="#"
                style={{ color: '#0891b2' }}
              >
                {' '}
                Close this window
              </Link>
            ) : (
              <Link to="/" style={{ color: '#0891b2' }}>
                {' '}
                Go to Zileo
              </Link>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default MailBoxPermission;
