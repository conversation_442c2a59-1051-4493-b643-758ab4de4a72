import React, { useEffect, useState } from 'react';
import { <PERSON>lap<PERSON>, Col, But<PERSON>, notification } from 'antd';
import JobCollapseList from './JobCollapseJob';
import PropTypes from 'prop-types';
import { DeleteOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import { deleteJobsSearchByCompanyName } from '../../services/search';
import { useParams } from 'react-router-dom';

const CollapseComponent = ({
  item,
  defaultSize,
  idx,
  setCheckData,
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  newJobOnly,
  activeGroupByCompany,
  setActiveGroupByCompany,
  groupByCompanyIdJob,
  setGroupByCompanyIdJob,
  setGroupCompany,
  isFromSyncSearches,
  isCollapseAll = false,
  handleDeleteCompany,
  handleDeleteDataByCompanyNames,
  bulkJobsByCompaniesData,
  isFetchingBulkJobs,
}) => {
  let { searchId } = useParams();
  const [activeKey, setActiveKey] = useState();
  const [listJobCp, getJobCp] = useState();

  useEffect(() => {
    if (isCollapseAll) {
      setActiveKey([]);
    }
  }, [isCollapseAll]);

  // const handleDeleteJobInCompany = () => {
  //   handleDeleteCompany(item)
  // }

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    setIsLoading(true);
    try {
      const { data } = await deleteJobsSearchByCompanyName(searchId, item);
      setIsLoading(false);
      if (data) {
        notification.success({
          message: `Delete Jobs From ${item} Successfully`,
        });
        const deletedCompanyEl = document.getElementById(item);
        if (!deletedCompanyEl) {
          handleDeleteCompany(item);
        } else {
          deletedCompanyEl.classList.add('!hidden');
        }
        // handleDeleteCompany(item)
      }
    } catch (e) {
      setIsLoading(false);
      notification.error({ message: `Delete Jobs From ${item} Failed` });
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <React.Fragment key={idx.toString()}>
      <Col span={24} id={item}>
        <Collapse
          activeKey={activeKey || defaultSize}
          onChange={(keys) => setActiveKey(keys)}
          items={[
            {
              key: idx,
              label: (
                <>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div>{item}</div>
                    <div>
                      <Button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleOk();
                        }}
                        loading={isLoading}
                      >
                        <DeleteOutlined />
                      </Button>
                    </div>
                  </div>
                </>
              ),
              children: (
                <JobCollapseList
                  isFetchingBulkJobs={isFetchingBulkJobs}
                  bulkJobsByCompaniesData={bulkJobsByCompaniesData}
                  company={item}
                  setCheckData={setCheckData}
                  searchData={searchData}
                  populateSearchData={populateSearchData}
                  populatePinnedJobsData={populatePinnedJobsData}
                  newJobOnly={newJobOnly}
                  activeGroupByCompany={activeGroupByCompany}
                  setActiveGroupByCompany={setActiveGroupByCompany}
                  groupByCompanyIdJob={groupByCompanyIdJob}
                  setGroupByCompanyIdJob={setGroupByCompanyIdJob}
                  setGroupCompany={setGroupCompany}
                  isFromSyncSearches={isFromSyncSearches}
                  getJobCp={getJobCp}
                  handleDeleteCompany={handleDeleteCompany}
                  handleDeleteDataByCompanyNames={
                    handleDeleteDataByCompanyNames
                  }
                />
              ),
            },
          ]}
        />
      </Col>
      {/* <Modal title="Basic Modal" open={isModalOpen} onOk={handleOk} onCancel={handleCancel} footer={false}>
        <p>
          Please make sure you want to delete jobs belonging to this company
        </p>
        <div style={{marginTop: "30px", display: "flex", justifyContent: "space-between"}}>
          <div></div>
          <div>
            <Button type="default" onClick={handleCancel}>Cancel</Button>
            <Button style={{marginLeft: "20px"}} loading={isLoading} disabled={isLoading} onClick={handleOk} type="primary">Confirm</Button>
          </div>
        </div>
      </Modal> */}
    </React.Fragment>
  );
};

CollapseComponent.propTypes = {
  item: PropTypes.any,
  defaultSize: PropTypes.any,
  idx: PropTypes.any,
  setCheckData: PropTypes.any,
  searchData: PropTypes.any,
  populateSearchData: PropTypes.any,
  populatePinnedJobsData: PropTypes.any,
  newJobOnly: PropTypes.any,
  activeGroupByCompany: PropTypes.any,
  setActiveGroupByCompany: PropTypes.any,
  groupByCompanyIdJob: PropTypes.any,
  setGroupByCompanyIdJob: PropTypes.any,
  setGroupCompany: PropTypes.any,
  isFromSyncSearches: PropTypes.any,
  isCollapseAll: PropTypes.any,
};

export default CollapseComponent;
