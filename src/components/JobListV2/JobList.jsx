import React, { useEffect, useState, useLayoutEffect } from 'react';
import PropTypes from 'prop-types';
import { Pagination, Row, Button, Modal } from 'antd';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _slice from 'lodash/slice';
import _orderBy from 'lodash/orderBy';

import JobCardComponent from './JobCardComponent';
import BullHornJobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import ModalWarningSequence from './ModalWarningSequence';
import { generateJobSkills } from '../../services/search';
import { stripJobDescription } from '../../utils/common';

function JobList({
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  isFromSyncSearches = false,
  isFromReportedAgency = false,
  page,
  setPage,
  newJobOnly,
  pagination,
  isExclusiveStartKey,
  handleNextPage,
  pagingKey,
  activePage,
  setActivePage,
  isFromJobSame,
  activeGroupByCompany,
  setActiveGroupByCompany,
  groupByCompanyIdJob,
  setGroupByCompanyIdJob,
  setGroupCompany,
  pageSize = 10,
  setTotalJobs = null,
  totalJobs = -1,
}) {
  const [isModalOpen, setModalOpen] = useState(false);
  const [jobToSync, setJobToSync] = useState(null);
  const [deleteJobId, setDeletedId] = useState(false);
  const [dataSearch, setDataSearch] = useState(null);
  const [openWarningModal, setOpenWarningModal] = useState(false);
  const { setAuth, profile: profileUserAuth } = useAuth();
  const { profileUser, setViewAs } = useViewAs();
  const [actionKey, setActionKey] = useState(null);

  const userToSet = profileUser || profileUserAuth;

  const handleDeleteData = (id) => {
    const index = dataSearch?.data?.findIndex((obj) => obj.job_id === id);
    if (index !== -1) {
      const newData = { ...dataSearch, data: [...dataSearch?.data] };
      newData.data.splice(index, 1);
      if (totalJobs > 0 && setTotalJobs) {
        setTotalJobs(totalJobs - 1);
      }
      setDeletedId(true);
      setDataSearch(newData);
    }
  };

  // const scrollX = window.scrollX;
  // const scrollY = window.scrollY;

  // useLayoutEffect(() => {
  //   window.scrollTo(0, 0);
  // });

  const handleDeleteDataByCompanyNames = (names) => {
    if (!Array.isArray(names)) return;
    const newData = {
      ...dataSearch,
      data: dataSearch?.data.filter((obj) => !names.includes(obj.company)),
    };
    const deleted = newData.length !== dataSearch.length;
    if (deleted) {
      setDeletedId(true);
      setDataSearch({ ...dataSearch, data: newData });
      // if (newData.length === 0) {
      //   handleDeleteCompany(company);
      // }
    }
  };

  useEffect(() => {
    if (deleteJobId) {
      setDeletedId(false);
    }
  }, [deleteJobId]);

  useEffect(() => {
    if (!searchData) return;
    setDataSearch({ ...searchData, data: [...searchData.data] });
  }, [searchData]);

  if (_get(dataSearch, 'data') && _size(_get(dataSearch, 'data')) > 0)
    return (
      <>
        <main className="font-spartan relative text-left">
          {isModalOpen && (
            <BullHornJobSubmissionModal
              job={jobToSync}
              isModalVisible={isModalOpen}
              setIsModalVisible={setModalOpen}
              handleDeleteData={handleDeleteData}
              searchData={searchData}
              actionKey={actionKey}
            />
          )}
          <section className="bg-lightCyan h-full pt-5">
            {_map(
              _slice(
                _orderBy(_get(dataSearch, 'data'), ['posted'], ['desc']),
                0,
                +pageSize
              ),
              (job, idx) => (
                <Row key={idx.toString()}>
                  <JobCardComponent
                    setGroupCompany={setGroupCompany}
                    activeGroupByCompany={activeGroupByCompany}
                    setActiveGroupByCompany={setActiveGroupByCompany}
                    groupByCompanyIdJob={groupByCompanyIdJob}
                    setGroupByCompanyIdJob={setGroupByCompanyIdJob}
                    userTrackings={dataSearch?.search?.userTrackings?.[0]}
                    newJobOnly={newJobOnly}
                    isFromSync={isFromSyncSearches}
                    isFromReportedAgency={isFromReportedAgency}
                    isFromJobSame={isFromJobSame}
                    job={job}
                    handleDeleteData={handleDeleteData}
                    populateSearchData={populateSearchData}
                    populatePinnedJobsData={populatePinnedJobsData}
                    onClickSync={async (jobToSyncRaw, key) => {
                      const jobToSync = {
                        ...jobToSyncRaw,
                        description: stripJobDescription(
                          jobToSyncRaw?.description
                        ),
                      };
                      setActionKey(key);
                      if (
                        !jobToSync?.skills ||
                        jobToSync?.skills?.length === 0
                      ) {
                        setModalOpen(true);
                        setJobToSync(jobToSync);
                        const { data } = await generateJobSkills(
                          jobToSync?.job_id
                        );
                        const jobToSyncWithSkills = {
                          ...jobToSync,
                          skills: data?.result || [],
                        };
                        setJobToSync(jobToSyncWithSkills);
                      } else {
                        setJobToSync(jobToSync);
                        setModalOpen(true);
                      }
                    }}
                    keywords={dataSearch?.search?.keywords}
                    dataSearch={dataSearch}
                    handleDeleteDataByCompanyName={
                      handleDeleteDataByCompanyNames
                    }
                    handleDeleteDataByCompanyNames={
                      handleDeleteDataByCompanyNames
                    }
                  />
                </Row>
              )
            )}
          </section>
          {/* <ModalWarningSequence setOpenWarningModal={setOpenWarningModal} openWarningModal={openWarningModal}/> */}
          <div className="sm:ms-14 dark:text-white ms-0">
            {isExclusiveStartKey ? (
              <div className="flex gap-2">
                {pagingKey.length !== 1 && (
                  <Button
                    onClick={() => {
                      window.scrollTo(0, 0);
                      let previousPageKey = '';
                      pagingKey.forEach((lastKey, j) => {
                        if (activePage === lastKey) {
                          previousPageKey = pagingKey[j - 1];
                        }
                      });
                      handleNextPage(previousPageKey);
                      setActivePage(previousPageKey);
                    }}
                  >
                    previous
                  </Button>
                )}
                {pagingKey.map((lastKey, i) => (
                  <React.Fragment key={i.toString()}>
                    <Button
                      className={`${pagingKey.length === 1 ? 'border-blue-500' : ''} ${pagingKey.length !== 1 && activePage === lastKey ? 'border-blue-500' : ''}`}
                      onClick={() => {
                        window.scrollTo(0, 0);
                        handleNextPage(lastKey);
                        setActivePage(lastKey);
                      }}
                    >
                      {i + 1}
                    </Button>
                  </React.Fragment>
                ))}
                <Button
                  onClick={() => {
                    window.scrollTo(0, 0);
                    handleNextPage(dataSearch.lastEvaluatedKey);
                    setActivePage(dataSearch.lastEvaluatedKey);
                  }}
                >
                  next
                </Button>
              </div>
            ) : (
              <Pagination
                className="customized-style-pagination"
                showSizeChanger={!!pagination}
                current={page}
                onChange={(page, pageSize) => {
                  window.scrollTo(0, 0);
                  setPage(page, pageSize);
                }}
                onShowSizeChange={(page, pageSize) => {
                  setPage(page, pageSize);
                }}
                total={
                  pagination?.totalCount
                    ? pagination?.totalCount
                    : dataSearch?.totalCount
                }
                pageSize={
                  pagination?.pageSize
                    ? pagination?.pageSize
                    : Number(dataSearch?.pageSize)
                }
                showTotal={false}
                showTitle={false}
              />
            )}
          </div>
        </main>
      </>
    );

  return (
    <div className="text-center mt-20">
      <div className="sm:ms-14 dark:text-white ms-0">
        {isExclusiveStartKey ? (
          <div className="flex gap-2">
            {pagingKey.length !== 1 && (
              <Button
                onClick={() => {
                  window.scrollTo(0, 0);
                  let previousPageKey = '';
                  pagingKey.forEach((lastKey, j) => {
                    if (activePage === lastKey) {
                      previousPageKey = pagingKey[j - 1];
                    }
                  });
                  handleNextPage(previousPageKey);
                  setActivePage(previousPageKey);
                }}
              >
                previous
              </Button>
            )}
            {pagingKey.map((lastKey, i) => (
              <React.Fragment key={i.toString()}>
                <Button
                  className={`${pagingKey.length === 1 ? 'border-blue-500' : ''} ${pagingKey.length !== 1 && activePage === lastKey ? 'border-blue-500' : ''}`}
                  onClick={() => {
                    window.scrollTo(0, 0);
                    handleNextPage(lastKey);
                    setActivePage(lastKey);
                  }}
                >
                  {i + 1}
                </Button>
              </React.Fragment>
            ))}
            <Button
              onClick={() => {
                window.scrollTo(0, 0);
                handleNextPage(dataSearch.lastEvaluatedKey);
                setActivePage(dataSearch.lastEvaluatedKey);
              }}
            >
              next
            </Button>
          </div>
        ) : (
          <Pagination
            className="customized-style-pagination"
            current={page}
            onChange={(page, pageSize) => {
              window.scrollTo(0, 0);
              setPage(page, pageSize);
            }}
            onShowSizeChange={(page, pageSize) => {
              setPage(page, pageSize);
            }}
            total={
              pagination?.totalCount
                ? pagination?.totalCount
                : dataSearch?.totalCount
            }
            pageSize={
              pagination?.pageSize
                ? pagination?.pageSize
                : Number(dataSearch?.pageSize)
            }
            showTotal={false}
            showTitle={false}
          />
        )}
      </div>
    </div>
  );
}

JobList.propTypes = {
  searchId: PropTypes.any,
  pageSize: PropTypes.any,
  filter: PropTypes.any,
  searchData: PropTypes.any,
  populateSearchData: PropTypes.any,
  populatePinnedJobsData: PropTypes.any,
  isFromSyncSearches: PropTypes.any,
  isFromReportedAgency: PropTypes.any,
  page: PropTypes.any,
  setPage: PropTypes.any,
  newJobOnly: PropTypes.any,
  pagination: PropTypes.any,
  isExclusiveStartKey: PropTypes.any,
  handleNextPage: PropTypes.any,
  pagingKey: PropTypes.any,
  activePage: PropTypes.any,
  setActivePage: PropTypes.any,
  isFromJobSame: PropTypes.any,
  activeGroupByCompany: PropTypes.any,
  setActiveGroupByCompany: PropTypes.any,
  groupByCompanyIdJob: PropTypes.any,
  setGroupByCompanyIdJob: PropTypes.any,
  setGroupCompany: PropTypes.any,
};

export default JobList;
