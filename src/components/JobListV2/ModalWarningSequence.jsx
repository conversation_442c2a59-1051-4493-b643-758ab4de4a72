import { Modal } from "antd";

function ModalWarningSequence({
    openWarningModal,
    setOpenWarningModal
}) {
    return(
        <Modal
            title="Warning"
            open={openWarningModal}
            onCancel={() => {
              setOpenWarningModal(false);
            }}
            footer={false}
          >
           <p>Please link Sequence at Settings Tab</p>
           <div>
            <p style={{
              color: "blue",
              cursor: "pointer",
              textDecoration: "underline",
              fontSize: "14px",
              marginTop: "10px"
            }}
            onClick={(e) => {
              window.location.href = `${window.location.origin}/settings?sequence=true`;
            }}
            >Go to Sequence Settings</p>
           </div>
          </Modal>
    )
}

export default ModalWarningSequence;