import React, { useState, useEffect, useMemo } from 'react';

import _isArray from 'lodash/isArray';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _compact from 'lodash/compact';
import _get from 'lodash/get';

import PropTypes from 'prop-types';
import {
  Modal,
  Button,
  Row,
  Col,
  Drawer,
  Tag,
  Form,
  Select,
  Spin,
  Image,
  Table,
  Empty,
  Flex,
  Divider,
  AutoComplete,
  Avatar,
  Typography,
  notification,
  Dropdown,
  message,
} from 'antd';
import {
  FileTextOutlined,
  FieldTimeOutlined,
  NotificationOutlined,
  PushpinOutlined,
  BankFilled,
  EnvironmentOutlined,
  PoundOutlined,
  StarOutlined,
  WalletOutlined,
  SettingOutlined,
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  HistoryOutlined,
  SendOutlined,
  MenuOutlined,
  HeartOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import useReportedAgencyModal from '../../hooks/useReportedAgencyModal';
import JobDetail from '../JobDetail';
import {
  pinJobToSearchId,
  unpinJobToSearchId,
  deleteJobsV2ById,
} from '../../services/search';
import { useNavigate, useParams } from 'react-router-dom';
import { Controller, useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import zileoIcon from '../../assets/img/zileoIcon.png';
import { getLinkS3 } from '../../services/aws';
import { getJobLogs, updateJobLogs } from '../../services/jobs';
import handleRenderTime from '../../function/handleRenderTime';
import useResponsive from '../../hooks/useResponsive';
import {
  getColorHexFromName,
  getRandomColor,
} from '../../function/getRandomColor';
import { decodeHtmlEntities, removeTrackingParams } from '../../utils/common';

// Icon source
import companyIcon from '../../assets/img/icons/company-icon.png';
import jobTypeIcon from '../../assets/img/icons/jobtype-icon.png';
import locationIcon from '../../assets/img/icons/location-icon.png';
import salaryIcon from '../../assets/img/icons/salary-icon.png';
import sourceIcon from '../../assets/img/icons/source-icon.png';
import {
  selectAllUsers,
  selectIsGroupCompany,
  setIsLoadingPin,
} from '../../store/common';
import { useSelector } from 'react-redux';
import { stringAvatar } from '../../function/stringAvatar';
import { createSentJob } from '../../services/jobLeads';
import { useDispatch } from 'react-redux';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { licenseType } from '../../constants/common.constant';
import { getUserViewAs, getUserViewAsLicenseType } from '../../helpers/getUserViewAs';

function JobCardComponent({
  isPin,
  job,
  populatePinnedJobsData,
  isFromReportedAgency = false,
  isFromJobSame = false,
  isFromSync = false,
  onClickSync = null,
  populateSearchData,
  newJobOnly,
  userTrackings,
  keywords,
  handleDeleteData,
  dataSearch,
  handleDeleteDataByCompanyNames,
  // companyLogoUrl = '',
}) {
  const navigate = useNavigate();
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const currentUserId = getUserViewAs();
  const userToSet = profileUser || profileUserAuth;
  const isGroupCompany = useSelector(selectIsGroupCompany);

    const currentUserLicenseType = getUserViewAsLicenseType();
    const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const {
    open,
    confirmLoading,
    companyName,
    listAgencyName,
    isLoadingListAgency,
    isLoadingNewListAgency,
    showModal,
    handleOk,
    handleCancel,
    handleGetFuzzySearch,
    handleGetNewFuzzySearch,
  } = useReportedAgencyModal();
  const { isDesktop } = useResponsive();
  let { searchId } = useParams();
  const [jobDetailOpen, setJobDetailOpen] = useState(false);
  const [showJobLogs, setShowJobLogs] = useState(false);
  const [showSendJobForm, setSendJobForm] = useState(false);
  const [dataJobLogs, setDataJobLogs] = useState([]);
  const [imageUrl, setImageUrl] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [userSentJob, setUserSendJob] = useState(null);
  const [loadingSentJob, setLoadingSentJob] = useState(false);
  const [loadingPinJob, setLoadingPinJob] = useState(false);
  const [loadingReportAgency, setLoadingReportAgency] = useState(false);
  // const [localIsPinned, setLocalIsPinned] = useState(job?.isPinned || false);
  const dispatch = useDispatch();
  const handleClickPin = async (e) => {
    e.stopPropagation();

    setLoadingPinJob(true);
    dispatch(setIsLoadingPin(true));

    try {
      // Determine if we're pinning or unpinning
      const isPinning = job.isPinned;

      // Make the API call
      if (!isPinning) {
        await pinJobToSearchId(searchId, job.job_id);
        isGroupCompany && handleDeleteData(job.job_id, true);
      } else {
        await unpinJobToSearchId(searchId, job.job_id);
      }

      // Reload data in the background
      await Promise.all([populatePinnedJobsData(), populateSearchData()]).catch(
        (error) => {
          console.error('Error reloading data after pin/unpin:', error);
        }
      );
    } catch (error) {
      console.error('Error pinning/unpinning job:', error);
      // message.error('Failed to ' + (localIsPinned ? 'unpin' : 'pin') + ' job');
    } finally {
      // Always reset loading state
      dispatch(setIsLoadingPin(false));
      setLoadingPinJob(false);
    }
  };

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [confirmLoadingDelete, setConfirmLoadingDelete] = useState(false);
  const handleDelete = async () => {
    try {
      setConfirmLoadingDelete(true);
      if (searchId) {
        await deleteJobsV2ById(searchId, job.job_id);
      }
      // await populateSearchData();

      if (isFromSync) {
        handleDeleteData(job.job_id);
      }
      message.success(
        `Deleted ${job?.jobtitle || job?.title} Job successfully`
      );
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
    } catch (error) {
      message.error(`Deleted ${job?.jobtitle || job?.title} Job failed!`);
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
      setJobDetailOpen(false);
    }
  };
  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
  };

  const { control, getValues } = useForm({
    defaultValues: {
      fuzzySearch: [],
    },
  });

  let delayTimer;
  const handleSearchChange = (searchText) => {
    clearTimeout(delayTimer);
    delayTimer = setTimeout(() => {
      handleGetNewFuzzySearch(searchText);
    }, 2000);
  };

  const handleImageError = () => {
    setImageError(true);
  };
  const handleSyncClick = () => {
    handleEditLogs();
    const jobId = encodeURIComponent(job?.job_id);

    // Save job in Local Storage
    const jsonJob = JSON.stringify(job);
    // const compressedJob = LZString.compressToEncodedURIComponent(jsonJob);
    localStorage.setItem('job', jsonJob);

    if (isStandardUser) {
      console.log('Standard User Sync');
      navigate(`/sync-standard-lead/${searchId}/${jobId}`);
    } else {
      navigate(`/sync-vacancy/${searchId}/${jobId}`);
    }
  };
  const handleShowLog = async () => {
    const { data } = await getJobLogs(job.job_id);
    setDataJobLogs(data.result.data);
    setShowJobLogs(true);
  };

  const columnJogLogs = [
    {
      title: 'No',
      dataIndex: 'index',
      render: (text, record, index) => <span>{index + 1}</span>,
    },
    {
      title: 'Date',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (text) => (
        <Tag
          color={text === 'EDITING_BH_VACANCY_SUBMISSION' ? 'yellow' : 'blue'}
        >
          {text === 'EDITING_BH_VACANCY_SUBMISSION' ? 'Editing' : 'Sent'}
        </Tag>
      ),
    },
    {
      title: 'Sender',
      dataIndex: 'senderEmail',
      key: 'senderEmail',
    },
  ];

  const handleEditLogs = async () => {
    const payload = {
      type: 'EDITING_BH_VACANCY_SUBMISSION',
      jobId: job.job_id,
    };
    await updateJobLogs(payload);
  };

  const fetchImageUrl = async () => {
    const logoCompaniesLocalStorage = sessionStorage.getItem('LOGO_COMPANIES');
    const logoCompanies = logoCompaniesLocalStorage
      ? JSON.parse(logoCompaniesLocalStorage)
      : [];

    const existingLogo = logoCompanies.find(
      (item) => item?.companyLogoId === job?.logoCompany
    );

    if (existingLogo) {
      setImageUrl(existingLogo?.data);
    } else {
      try {
        const { data } = await getLinkS3(job?.logoCompany);

        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === job?.logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            { companyLogoId: job?.logoCompany, data },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
        }

        setImageUrl(data);
      } catch (error) {
        const existingLogo = logoCompanies.find(
          (item) => item?.companyLogoId === job?.logoCompany
        );
        if (!existingLogo) {
          const newLogoCompanies = [
            ...logoCompanies,
            {
              companyLogoId: job?.logoCompany,
              data: '../../assets/img/zileoIcon.png',
            },
          ];

          const stringifiedLogoCompanies = JSON.stringify(newLogoCompanies);
          sessionStorage.setItem('LOGO_COMPANIES', stringifiedLogoCompanies);
        }
        setImageError(true);
      }
    }
  };

  const handleLoadingData = async () => {
    await fetchImageUrl();
  };

  useEffect(() => {
    handleLoadingData();
  }, [job]);

  // Keep localIsPinned in sync with job.isPinned when it changes from props
  // useEffect(() => {
  //   setLocalIsPinned(job?.isPinned || false);
  // }, [job?.isPinned]);

  const highlightedKeyWords = useMemo(() => {
    const jobDescription = (job?.description || '').toLowerCase();

    const newHighlightedKeywords = (keywords || '')
      .toLowerCase()
      .split(',')
      .filter((item) => jobDescription.includes(item));

    return _compact(newHighlightedKeywords) || [];
  }, [job]);

  if (!job) return null;

  const allUsers = useSelector(selectAllUsers);

  const handleSendJobToLead = async () => {
    if (!userSentJob) {
      notification.error({
        message: 'Please select a user',
      });
      return;
    }
    setLoadingSentJob(true);
    try {
      const { data } = await createSentJob({
        jobBoardId: job?.job_id,
        sentToUser: [userSentJob],
        senderId: userToSet?.user?.id || userToSet?.id,
      });
      if (data) {
        notification.success({
          message: 'Sent job successfully',
        });
        setSendJobForm(false);
        setLoadingSentJob(false);
      }
    } catch (err) {
      notification.error({
        message: err?.response?.data?.message,
      });
      setLoadingSentJob(false);
    }
  };

  return (
    <>
      {jobDetailOpen && (
        <Drawer
          title="Job Details"
          onClose={() => {
            setJobDetailOpen(false);
          }}
          open={jobDetailOpen}
          width={'100vw'}
          placement={'right'}
        >
          <JobDetail
            job={job}
            keywords={keywords}
            isFromSync={isFromSync}
            handleEditLogs={handleEditLogs}
            onClickSync={onClickSync}
            closeDrawer={() => setJobDetailOpen(false)}
            setDeleteModalOpen={setDeleteModalOpen}
            handleDelete={handleDelete}
            loading={confirmLoadingDelete}
          />
        </Drawer>
      )}
      <Modal
        centered
        width={1000}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Delete job"
        open={deleteModalOpen}
        okText={'Confirm'}
        okButtonProps={{
          loading: confirmLoadingDelete,
        }}
        onOk={handleDelete}
        onCancel={handleCancelDelete}
      >
        Are you sure to delete job {job.jobtitle}?
      </Modal>
      <Modal
        centered
        width={1000}
        bodyStyle={{
          overflowY: 'auto',
          maxHeight: 'calc(100vh - 200px)',
        }}
        title="Job Logs"
        open={showJobLogs}
        onOk={() => {
          setShowJobLogs(false);
        }}
        onCancel={() => {
          setShowJobLogs(false);
        }}
      >
        <Table
          columns={columnJogLogs}
          dataSource={dataJobLogs || []}
          locale={{
            emptyText: <Empty description="No logs found" className="w-full" />,
          }}
          pagination={false}
        />
      </Modal>
      <div className="w-full mb-5">
        <div
          className="w-full bg-white items-center dark:text-white dark:bg-gray-800 rounded-lg border border-primary"
          style={isPin ? { backgroundColor: '#eff6ff' } : {}}
        >
          <Row style={{ width: '100%', borderRadius: '0.5rem' }}>
            <Col className="w-full">
              <div className="w-full rounded-lg">
                <Flex gap={16} className="p-4">
                  {imageError ? (
                    <Image
                      className="rounded-md"
                      src={zileoIcon}
                      alt="Zileo Icon"
                      width="40px"
                      height="40px"
                    />
                  ) : (
                    <Image
                      className="rounded-md"
                      src={imageUrl || zileoIcon}
                      alt={'Company Logo'}
                      width="40px"
                      height="40px"
                      onError={handleImageError}
                    />
                  )}
                  <div style={{ width: '40%' }}>
                    <h4 className="font-bold text-lg">
                      {job?.jobtitle}{' '}
                      {/* <Button
                        onClick={() => {
                          window.open(job?.link, '_blank');
                        }}
                        style={{ marginLeft: '10px' }}
                        // type={'primary'}>
                        Go to Job
                      </Button> */}
                    </h4>
                    <Flex style={{ flexWrap: 'wrap', marginTop: '5px' }}>
                      <div>
                        <FieldTimeOutlined className="mr-2" />
                        <span>{handleRenderTime(job.posted)}</span>
                      </div>
                      <Flex align="center">
                        {newJobOnly && newJobOnly !== 'false' ? (
                          <>
                            <Divider type="vertical" />
                            <Tag color="processing">
                              New Job From Last Visited:{' '}
                              {dayjs(userTrackings?.lastVisitedAt).format(
                                'DD/MM/YYYY HH:mm'
                              )}
                            </Tag>
                          </>
                        ) : (
                          ''
                        )}

                        {job.sendToBullHornByUserIds && (
                          <>
                            <Divider type="vertical" />
                            <Tag color="blue">Job sent to Bullhorn</Tag>
                          </>
                        )}

                        {_size(highlightedKeyWords) > 0 && (
                          <Flex align="center">
                            <Divider type="vertical" />
                            {_map(highlightedKeyWords, (item, idx) => (
                              <Tag
                                color="green"
                                key={idx.toString()}
                                className="font-semibold"
                              >
                                {item?.toUpperCase()}
                              </Tag>
                            ))}
                          </Flex>
                        )}

                        {/* {isFromJobSame ? (
                      ''
                    ) : (
                      <Button
                        className={`text-black ${activeGroupByCompany
                            ? 'bg-blue-400 text-white'
                            : 'bg-white'
                          } rounded-md ms-2 px-3 py-1  flex items-center`}
                        onClick={async () => {
                          setActiveGroupByCompany(true);
                          setGroupByCompanyIdJob(job.job_id);
                          setGroupCompany(job.company);
                        }}
                      >
                        <EyeOutlined
                          className={`${activeGroupByCompany
                              ? 'text-white'
                              : 'text-blue-400'
                            }`}
                        />
                        Group By Company
                      </Button>
                    )} */}
                      </Flex>
                    </Flex>
                  </div>
                  <div
                    className="flex justify-end gap-2 items-center"
                    style={{ width: '55%' }}
                  >
                    <div>
                      <Button
                        type={'primary'}
                        onClick={() => setSendJobForm(true)}
                        icon={<SendOutlined />}
                      >
                        Send Job
                      </Button>
                    </div>
                    <div>
                      {_isArray(job?.jobLogs) &&
                      job?.jobLogs &&
                      job?.jobLogs?.length > 0 ? (
                        <div
                          style={{
                            marginRight: 0,
                            paddingRight: 0,
                            width: '22%',
                            float: 'right',
                          }}
                        >
                          {job?.jobLogs?.map((item) => (
                            <Button style={{ float: 'right' }}>
                              <HistoryOutlined className={`text-blue-400`} />
                              {item}
                            </Button>
                          ))}
                          <div style={{ clear: 'both' }}></div>
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                </Flex>
                <hr />
                <div
                  className="p-5"
                  style={{
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setJobDetailOpen(true);
                  }}
                >
                  <div className="grid grid-cols-8 gap-x-4">
                    <div className="col-span-8 sm:col-span-4">
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <Image
                            className="mr-3 text-blue-400"
                            src={companyIcon}
                            alt="Company Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-2 text-graySecondary">
                            Company
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6">
                          {job.company}
                        </span>
                      </div>
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          {/* <FileTextOutlined className="pr-3 text-blue-400" /> */}
                          <Image
                            className="mr-3 text-blue-400"
                            src={jobTypeIcon}
                            alt="Job Type Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-2 text-graySecondary">
                            Job Type
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6">
                          {job.jobtype || '-'}
                        </span>
                      </div>
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          {/* <EnvironmentOutlined className="pr-3 text-blue-400" /> */}
                          <Image
                            className="mr-3 text-blue-400"
                            src={locationIcon}
                            alt="Location Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-2 text-graySecondary">
                            Location
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6">
                          {job?.joblocationcity}
                        </span>
                      </div>
                    </div>
                    <div className="col-span-8 sm:col-span-4">
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          {/* <PoundOutlined className="pr-3 text-blue-400" /> */}
                          <Image
                            className="mr-3 text-blue-400"
                            src={salaryIcon}
                            alt="Salary Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-2 text-graySecondary">
                            Salary
                          </span>
                        </div>
                        {/* <span className="pr-3 font-medium col-span-6">
                          {`${_get(job, 'min_salary') || '--'} - ${_get(job, 'max_salary') || '--'}`}
                        </span> */}
                        <span className="pl font-medium col-span-6">
                          {`${_get(job, 'salary') || '--'}`}
                        </span>
                      </div>
                      {job?.link && (
                        <div className="items-center py-1 grid grid-cols-10">
                          <div className="col-span-3 flex items-center">
                            {/* <SettingOutlined className="pr-3 text-blue-400" /> */}
                            <Image
                              className="mr-3 text-blue-400"
                              src={sourceIcon}
                              alt="Source Icon"
                              width="16px"
                              height="16px"
                              preview={false}
                            />
                            <span className="pl-2 text-graySecondary">
                              Source
                            </span>
                          </div>

                          <Button
                            className="col-span-6 w-fit"
                            onClick={() => {
                              window.open(
                                removeTrackingParams(job?.link),
                                '_blank'
                              );
                            }}
                            type={'primary'}
                          >
                            Click to View Job
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="pr-3 text-graySecondary">Description</p>
                    <p className="font-medium line-clamp-3">
                      {decodeHtmlEntities(
                        job.description?.split(/<[^>]*>/)?.join('')
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          <Flex justify="space-between" className="pb-4 px-4">
            {/* <Button
              type="primary"
              className="text-black rounded-md px-3 py-1 bg-white flex items-center"
              onClick={() => {
                setJobDetailOpen(true);
              }}
            >
              <EyeOutlined />
              View
            </Button> */}
            {!isFromReportedAgency && !isFromJobSame && (
              <div>
                <Button
                  className="text-black rounded-md ms-2 px-3 py-1 bg-white flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    showModal(job.company);
                    handleGetFuzzySearch(job.company);
                  }}
                >
                  <NotificationOutlined className="text-blue-400" />
                  Report as an Agency
                </Button>
              </div>
            )}
            {isFromJobSame ? (
              ''
            ) : (
              <div className="basis-full lg:basis-auto flex justify-end">
                {isFromSync && (
                  <div>
                    {/* Click on sync button */}
                    <Button
                      className="text-black rounded-md bg-white flex items-center justify-center"
                      style={{ backgroundColor: 'green', color: '#fff' }}
                      onClick={handleSyncClick}
                    >
                      <CheckOutlined style={{ color: '#fff' }} />
                    </Button>
                  </div>
                )}
                {isFromSync && (
                  <div>
                    <Button
                      loading={confirmLoadingDelete}
                      className="text-black rounded-md ms-2 px-3 py-1 bg-white flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete();
                      }}
                      style={{
                        backgroundColor: 'red',
                        color: '#fff',
                      }}
                    >
                      <CloseOutlined
                        style={{ color: '#fff' }}
                        className="text-blue-400"
                      />
                    </Button>
                  </div>
                )}
                <div>
                  <Button
                    className={`text-${
                      job.isPinned ? 'white' : 'black'
                    } inline-block rounded-md ms-2 px-3 py-1 bg-${
                      job.isPinned ? 'blue-500' : 'white'
                    } flex items-center`}
                    onClick={handleClickPin}
                    loading={loadingPinJob}
                  >
                    <PushpinOutlined
                      className={`text-${job.isPinned ? 'white' : 'blue'}-400`}
                    />
                    {job.isPinned ? 'Unpin' : 'Pin'}
                  </Button>
                </div>
              </div>
            )}
          </Flex>
        </div>
      </div>
      <Modal
        title="Send Job"
        footer={false}
        open={showSendJobForm}
        onOk={() => {}}
        onCancel={() => setSendJobForm(false)}
      >
        <div>
          <Select
            showSearch
            placeholder="Select a user"
            optionFilterProp="label"
            options={_map(allUsers, (option) => ({
              value: _get(option, 'id'),
              label: _get(option, 'fullName') || _get(option, 'email'),
              id: _get(option, 'id'),
            }))}
            optionRender={({ value }) => {
              const selectedUser = allUsers.find((user) => user.id === value);
              const userDisplayName =
                _get(selectedUser, 'fullName') ||
                _get(selectedUser, 'username') ||
                _get(selectedUser, 'email');

              return (
                <div className="flex items-center gap-2">
                  <Avatar
                    style={{
                      backgroundColor: getColorHexFromName(userDisplayName),
                      verticalAlign: 'middle',
                    }}
                    src={selectedUser?.avatarUrl || <UserOutlined />}
                  >
                    {stringAvatar(userDisplayName)}
                  </Avatar>
                  <div className="flex flex-col">
                    <Typography.Text
                      style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                      className="font-semibold  text-gray-900"
                    >
                      {userDisplayName}
                    </Typography.Text>
                    <span className="opacity-70 text-xs">
                      {_get(selectedUser, 'email')}
                    </span>
                  </div>
                </div>
              );
            }}
            className="w-full"
            onSelect={async (value, option) => {
              setUserSendJob(option.id);
            }}
          />
        </div>
        <div style={{ marginTop: '20px' }}>
          <Button
            loading={loadingSentJob}
            onClick={() => handleSendJobToLead()}
            style={{ width: '100%' }}
            type={'primary'}
          >
            Send Lead
          </Button>
        </div>
      </Modal>
      <Modal
        title="Add Reported Agency"
        open={open}
        onOk={async () => {
          setLoadingReportAgency(true);
          await handleOk(getValues().fuzzySearch, dataSearch?.search?.country);
          setLoadingReportAgency(false);
          // populateSearchData();
          const listCompanyNames = getValues().fuzzySearch?.map(
            (item) => item?.value
          );
          listCompanyNames.push(companyName.trim());
          handleDeleteDataByCompanyNames(listCompanyNames);
        }}
        confirmLoading={confirmLoading | loadingReportAgency}
        onCancel={handleCancel}
      >
        <Form.Item className="m-0" label="Agency Name" name="agencyName">
          <p className="m-0">{companyName}</p>
        </Form.Item>
        {isLoadingListAgency ? (
          <Spin
            style={{ position: 'inherit' }}
            className="m-auto py-6 mb-6"
            tip={`We are looking for the similar agencies that might be the same as ${companyName}`}
          >
            <div className="content" />
          </Spin>
        ) : (
          // listAgencyName.length <= 0 ? "" :
          <>
            <p className="mb-2 mt-4">{`We have found some similar agencies that might be the same as ${companyName}. Would you like to report these as a agency?`}</p>
            <Form.Item label="" name="fuzzySearch">
              <Controller
                name="fuzzySearch"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    mode="multiple"
                    onSearch={(searchText) => {
                      if (searchText !== '') handleSearchChange(searchText);
                    }}
                    notFoundContent={
                      isLoadingNewListAgency ? <Spin size="small" /> : ''
                    }
                    {...field}
                    options={Array.from(
                      new Set(listAgencyName.map((item) => item.trim()))
                    )
                      .filter((item) => item !== '')
                      .map((option) => ({
                        value: option.trim(),
                        label: option.trim(),
                      }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        ?.toLowerCase()
                        ?.indexOf(inputValue?.toLowerCase()) !== -1
                    }
                  />
                )}
              />
            </Form.Item>
          </>
        )}
      </Modal>
    </>
  );
}

JobCardComponent.propTypes = {
  isPin: PropTypes.bool,
  job: PropTypes.any,
  populatePinnedJobsData: PropTypes.any,
  isFromReportedAgency: PropTypes.any,
  isFromJobSame: PropTypes.any,
  isFromSync: PropTypes.any,
  onClickSync: PropTypes.any,
  populateSearchData: PropTypes.any,
  newJobOnly: PropTypes.any,
  userTrackings: PropTypes.any,
  activeGroupByCompany: PropTypes.any,
  setActiveGroupByCompany: PropTypes.any,
  setGroupByCompanyIdJob: PropTypes.any,
  setGroupCompany: PropTypes.any,
  keywords: PropTypes.any,
};

export default JobCardComponent;
