import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { ExpandAltOutlined } from '@ant-design/icons';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _size from 'lodash/size';

import JobCardComponent from './JobCardComponent';
import BullHornJobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../store/auth';
import { useLazyGetJobByCompanyQuery } from '../../app/jobSearch';
import { useViewAs } from '../../store/viewAs';
import { Button, Col, Row } from 'antd';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { useSelector } from 'react-redux';
import { selectFilterJob } from '../../store/common';
import ModalWarningSequence from './ModalWarningSequence';
import { getLinkS3 } from '../../services/aws';
import zileoIcon from '../../assets/img/zileoIcon.png';
import { generateJobSkills } from '../../services/search';
import { stripJobDescription } from '../../utils/common';

const FIRST_INDEX = 0;

const JobCollapseJob = ({
  company,
  setCheckData,
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  newJobOnly,
  activeGroupByCompany,
  setActiveGroupByCompany,
  groupByCompanyIdJob,
  setGroupByCompanyIdJob,
  setGroupCompany,
  isFromSyncSearches = false,
  getJobCp,
  handleDeleteCompany,
  handleDeleteDataByCompanyNames,
  bulkJobsByCompaniesData,
  isFetchingBulkJobs,
}) => {
  const { searchId } = useParams();
  const { profile } = useAuth();
  const { profileUser } = useViewAs();
  const filterJob = useSelector(selectFilterJob);

  const [getJobByCompany, { data: jobByCompanyData, isFetching }] =
    useLazyGetJobByCompanyQuery();

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobToSync, setJobToSync] = useState(null);
  const [showAll, setShowAll] = useState(false);
  const [deleteJobId, setDeletedId] = useState(false);
  const [isShowSeeMore, setShowSeeMore] = useState(false);
  const [isChecked, setChecked] = useState(false);

  // const [imageUrl, setImageUrl] = useState(null);

  const { setAuth, profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const userId = useMemo(() => {
    return _get(profileUser, 'id') || _get(profile, 'user.id') || '';
  }, [profile]);

  const [dataSearch, setDataSearch] = useState();
  const [dataSearchFirst, setDataSearchFirst] = useState();

  const renderData = useMemo(() => {
    const jobsByCompany = bulkJobsByCompaniesData?.filter(
      (item) => item?.company === company
    );
    return [...jobsByCompany];
  }, [bulkJobsByCompaniesData]);

  const firstRenderData = useMemo(() => {
    const jobsByCompany = bulkJobsByCompaniesData?.filter(
      (item) => item?.company === company
    );
    const data =
      jobsByCompany?.length > 0 ? [...jobsByCompany?.slice(0, 1)] : [];
    return data;
  }, [bulkJobsByCompaniesData]);

  const handleEffectData = async () => {
    if (renderData) {
      setDataSearch(renderData);
    }

    if (firstRenderData) {
      setDataSearchFirst(firstRenderData);
    }

    if (isFetchingBulkJobs) return;

    if (renderData.length >= 2) {
      setShowSeeMore(true);
      setChecked(true);
    } else if (renderData.length === 1) {
      if (searchId && userId && company && !isChecked) {
        const jsonToSearch = Object.assign({}, filterJob);
        delete jsonToSearch.jobTitles;
        delete jsonToSearch.previousPage;
        delete jsonToSearch.company;

        let queryString = 'jobTitles=';
        if (filterJob?.jobTitles || filterJob?.jobTitles?.length > 0) {
          if (Array.isArray(filterJob?.jobTitles)) {
            queryString =
              'jobTitles=' + filterJob?.jobTitles?.join('&jobTitles=');
          } else {
            queryString = `jobTitles=${filterJob?.jobTitles}`;
          }
        }
        try {
          const res = await getJobByCompany({
            searchId,
            userId,
            company,
            jsonToSearch: { ...jsonToSearch, limit: 2 },
            queryString: queryString,
          });

          if (res?.data?.result?.data?.length >= 2) {
            setShowSeeMore(true);
          }
        } catch (error) {
          console.error(error);
        }
        setChecked(true);
      }
    }
  };

  useEffect(() => {
    handleEffectData();
  }, [renderData, firstRenderData]);

  const handleDeleteData = (id) => {
    const index = dataSearch?.findIndex((obj) => obj.job_id === id);
    if (index !== -1) {
      const newData = dataSearch.filter((_, i) => i !== index);
      setDeletedId(true);
      setDataSearch(newData);
      if (newData?.length == 0) {
        handleDeleteCompany(company);
      }
      if (index === FIRST_INDEX) {
        handleDeleteFirstData(id);
      }
    }
  };

  const handleDeleteFirstData = (id) => {
    const index = dataSearchFirst?.findIndex((obj) => obj.job_id === id);
    const index2 = dataSearch?.findIndex((obj) => obj.job_id === id);
    if (index !== -1) {
      const newData = dataSearchFirst.filter((_, i) => i !== index);
      setDeletedId(true);
      setDataSearchFirst(newData);
    }
    if (index2 !== -1) {
      const newData = dataSearch.filter((_, i) => i !== index2);
      setDeletedId(true);
      setDataSearch(newData);
    }

    // In case use last job in company => delete company
    if (index === 0 && dataSearch?.length === 1) {
      handleDeleteCompany(company);
    }
  };

  useEffect(() => {
    if (deleteJobId) {
      setDeletedId(false);
    }
  }, [deleteJobId]);

  // useEffect(() => {
  //   if (searchId && userId && company) {
  //     const jsonToSearch = Object.assign({}, filterJob);
  //     delete jsonToSearch.jobTitles;
  //     delete jsonToSearch.previousPage;
  //     delete jsonToSearch.company;

  //     let queryString = 'jobTitles=';
  //     if (filterJob?.jobTitles || filterJob?.jobTitles?.length > 0) {
  //       if (Array.isArray(filterJob?.jobTitles)) {
  //         queryString =
  //           'jobTitles=' + filterJob?.jobTitles?.join('&jobTitles=');
  //       } else {
  //         queryString = `jobTitles=${filterJob?.jobTitles}`;
  //       }
  //     }
  //     try {
  //       getJobByCompany({
  //         searchId,
  //         userId,
  //         company,
  //         jsonToSearch: { ...jsonToSearch, limit: 2 },
  //         queryString: queryString,
  //       })
  //         .unwrap()
  //         .then((data) => {
  //           if (
  //             _get(data, 'result.data') &&
  //             _size(_get(data, 'result.data')) < 1
  //           ) {
  //             console.log('empty company: ', [...data, company]);
  //             // const emptyCompanies = [...data, company];
  //             // console.log("company: ", emptyCompanyEl)
  //             // emptyCompanies.forEach((companyName) => {
  //             //   if (!document) return;
  //             //   const emptyCompanyEl = document.getElementById(companyName);
  //             //   console.log('company: ', emptyCompanyEl);
  //             //   emptyCompanyEl.classList.add('!hidden');
  //             // });
  //             setCheckData((data) => [...data, company]);
  //           }
  //         })
  //         .catch(() => {
  //           setCheckData((data) => [...data, company]);
  //         });
  //     } catch {
  //       setCheckData((data) => [...data, company]);
  //     }
  //   }
  // }, [searchId, userId, company]);

  const handleRenderUI = () => {
    if (_size(showAll) >= _size(dataSearch) || _size(dataSearchFirst) === 0)
      return (
        <Col span={24}>
          {_map(dataSearch, (item, idx) => (
            <React.Fragment key={idx.toString()}>
              <JobCardComponent
                // companyLogoUrl={imageUrl}
                setGroupCompany={setGroupCompany}
                activeGroupByCompany={activeGroupByCompany}
                setActiveGroupByCompany={setActiveGroupByCompany}
                groupByCompanyIdJob={groupByCompanyIdJob}
                setGroupByCompanyIdJob={setGroupByCompanyIdJob}
                userTrackings={searchData?.search?.userTrackings?.[0]}
                newJobOnly={newJobOnly}
                job={item}
                isFromSync={isFromSyncSearches}
                populateSearchData={populateSearchData}
                populatePinnedJobsData={populatePinnedJobsData}
                onClickSync={async (jobToSyncRaw) => {
                  const jobToSync = {
                    ...jobToSyncRaw,
                    description: stripJobDescription(jobToSyncRaw?.description),
                  };
                  if (!jobToSync?.skills || jobToSync?.skills?.length === 0) {
                    setJobToSync(jobToSync);
                    setModalOpen(true);
                    const { data } = await generateJobSkills(jobToSync?.job_id);
                    const jobToSyncWithSkills = {
                      ...jobToSync,
                      skills: data?.result || [],
                    };
                    setJobToSync(jobToSyncWithSkills);
                  } else {
                    setJobToSync(jobToSync);
                    setModalOpen(true);
                  }
                }}
                keywords={searchData?.search?.keywords}
                handleDeleteData={handleDeleteData}
                dataSearch={searchData?.search}
                handleDeleteDataByCompanyNames={handleDeleteDataByCompanyNames}
              />
            </React.Fragment>
          ))}
        </Col>
      );

    return (
      <>
        <Col span={24}>
          {_map(dataSearchFirst, (item, idx) => (
            <React.Fragment key={idx.toString()}>
              <JobCardComponent
                // companyLogoUrl={imageUrl}
                setGroupCompany={setGroupCompany}
                activeGroupByCompany={activeGroupByCompany}
                setActiveGroupByCompany={setActiveGroupByCompany}
                groupByCompanyIdJob={groupByCompanyIdJob}
                setGroupByCompanyIdJob={setGroupByCompanyIdJob}
                userTrackings={searchData?.search?.userTrackings?.[0]}
                newJobOnly={newJobOnly}
                job={item}
                isFromSync={isFromSyncSearches}
                populateSearchData={populateSearchData}
                populatePinnedJobsData={populatePinnedJobsData}
                onClickSync={async (jobToSyncRaw) => {
                  const jobToSync = {
                    ...jobToSyncRaw,
                    description: stripJobDescription(jobToSyncRaw?.description),
                  };
                  if (!jobToSync?.skills || jobToSync?.skills?.length === 0) {
                    setJobToSync(jobToSync);
                    setModalOpen(true);
                    const { data } = await generateJobSkills(jobToSync?.job_id);
                    const jobToSyncWithSkills = {
                      ...jobToSync,
                      skills: data?.result || [],
                    };
                    setJobToSync(jobToSyncWithSkills);
                  } else {
                    setJobToSync(jobToSync);
                    setModalOpen(true);
                  }
                }}
                keywords={searchData?.search?.keywords}
                handleDeleteData={handleDeleteFirstData}
                handleDeleteDataByCompanyNames={handleDeleteDataByCompanyNames}
                dataSearch={searchData?.search}
              />
            </React.Fragment>
          ))}
        </Col>

        {(_size(dataSearch) > 1 || isShowSeeMore) && (
          <Col
            span={24}
            style={{
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              loading={isFetching || isFetchingBulkJobs}
              type="primary"
              className="text-black rounded-md px-3 py-1 bg-white flex items-center"
              onClick={async () => {
                // setShowAll([...renderData])
                const jsonToSearch = Object.assign({}, filterJob);
                delete jsonToSearch.jobTitles;
                delete jsonToSearch.previousPage;
                delete jsonToSearch.company;

                let queryString = 'jobTitles=';
                if (filterJob?.jobTitles || filterJob?.jobTitles?.length > 0) {
                  if (Array.isArray(filterJob?.jobTitles)) {
                    queryString =
                      'jobTitles=' + filterJob?.jobTitles?.join('&jobTitles=');
                  } else {
                    queryString = `jobTitles=${filterJob?.jobTitles}`;
                  }
                }

                await getJobByCompany({
                  searchId,
                  userId,
                  company,
                  jsonToSearch: { ...jsonToSearch, limit: 70 },
                  queryString,
                }).then((res) => {
                  setDataSearch([...res?.data?.result?.data]);
                  setShowAll([...res?.data?.result?.data]);
                });
              }}
            >
              <ExpandAltOutlined />
              See more
            </Button>
          </Col>
        )}
      </>
    );
  };

  return (
    <>
      {isModalOpen && (
        <BullHornJobSubmissionModal
          job={jobToSync}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
          handleDeleteData={handleDeleteData}
          searchData={searchData}
        />
      )}

      <Row>
        {isFetchingBulkJobs && (
          <Col span={24}>
            {isFetchingBulkJobs && <LoadingAdvanced isSkeleton />}
          </Col>
        )}
      </Row>

      {!isFetchingBulkJobs && handleRenderUI()}

      {/* <ModalWarningSequence setOpenWarningModal={setOpenWarningModal} openWarningModal={openWarningModal}/> */}
    </>
  );
};

JobCollapseJob.propTypes = {
  company: PropTypes.string,
  setCheckData: PropTypes.func,
  searchData: PropTypes.any,
  populateSearchData: PropTypes.any,
  populatePinnedJobsData: PropTypes.any,
  isFromSyncSearches: PropTypes.any,
  isFromReportedAgency: PropTypes.any,
  newJobOnly: PropTypes.any,
  activeGroupByCompany: PropTypes.any,
  setActiveGroupByCompany: PropTypes.any,
  groupByCompanyIdJob: PropTypes.any,
  setGroupByCompanyIdJob: PropTypes.any,
  setGroupCompany: PropTypes.any,
  getJobCp: PropTypes.any,
  handleDeleteCompany: PropTypes.func,
};

export default JobCollapseJob;
