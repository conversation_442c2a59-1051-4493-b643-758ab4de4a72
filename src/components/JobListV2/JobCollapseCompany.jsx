import React, { useEffect, useMemo, useRef, useState } from 'react';
import PropTypes from 'prop-types';

import _get from 'lodash/get';
import _map from 'lodash/map';
import _size from 'lodash/size';
import _range from 'lodash/range';
import _ from 'lodash';

import { useParams, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../store/auth';
import {
  useLazyBulkGetJobByCompaniesQuery,
  useLazyGetCompanyQuery,
} from '../../app/jobSearch';
import { Button, Col, Pagination, Row } from 'antd';
import { useViewAs } from '../../store/viewAs';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import {
  loadMoreCompany,
  resetAllCompany,
  saveAllCompany,
  selectAllCompany,
  selectDisplayedItemsCount,
  selectFilterJob,
  selectIsResetStatus,
  selectIsSearchingStatus,
  setIsReset,
  setIsSearching,
} from '../../store/common';
import CollapseComponent from './CollapseComponent';
import PinnedJobListGroupByCompany from '../SearchDetailV2/PinnedJobListGroupByCompany';
import { paramsToObj } from '../../containers/SyncSearchJobListV2';
import { getPaginationGroupByCompanies } from '../../services/search';
import { useQuery } from '@tanstack/react-query';
import LoadingBar from '../../containers/SyncSearchJobListV2/LoadingBar';

const JobCollapseCompany = ({
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  newJobOnly,
  activeGroupByCompany,
  setActiveGroupByCompany,
  groupByCompanyIdJob,
  setGroupByCompanyIdJob,
  setGroupCompany,
  isFromSyncSearches = false,
  setCurrentCompany,
  currentCompany,
  pinnedData = null,
}) => {
  const [
    bulkGetJobByCompanies,
    { data: bulkJobsByCompaniesData, isFetching: isFetchingBulkJobs },
  ] = useLazyBulkGetJobByCompaniesQuery();
  const dispatch = useDispatch();
  const allCompany = useSelector(selectAllCompany);
  const resetStatus = useSelector(selectIsResetStatus);
  const searchStatus = useSelector(selectIsSearchingStatus);
  const displayedItemsCount = useSelector(selectDisplayedItemsCount);
  const filterJob = useSelector(selectFilterJob);
  const [cachedCompanyPagination, setCachedCompanyPagination] = useState({});
  const renderDataRef = useRef();
  const hasRenderDataChanged = useRef(false);

  const { searchId } = useParams();
  const { profile } = useAuth();
  const { profileUser } = useViewAs();

  const [checkData, setCheckData] = useState([]);
  const [jobsByCompanies, setJobsByCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapseAll, setIsCollapseAll] = useState(false);
  const [pagination, setPagination] = useState({
    totalCount: 0,
    pageSize: 10,
    currentPage: 1,
  });
  let [searchParams, setSearchParams] = useSearchParams();

  const searchParamsObj = paramsToObj(searchParams);
  const page = searchParamsObj?.page ?? 1;
  const pageSize = searchParamsObj?.pageSize ?? 10;
  const filter = {
    datePostedText: searchParamsObj?.datePostedText ?? 'Date posted',
    searchText: searchParamsObj?.searchText ?? '',
    jobBoards: searchParamsObj?.jobBoards ?? [],
    keywords: searchParamsObj?.keywords ?? [],
    location: searchParamsObj?.location ?? [],
    maxSalary: searchParamsObj?.maxSalary ?? '',
    minSalary: searchParamsObj?.minSalary ?? '',
    postedStartDate: searchParamsObj?.postedStartDate ?? '',
    postedEndDate: searchParamsObj?.postedEndDate ?? '',
    newJobOnly: searchParamsObj?.newJobOnly ?? false,
    notificationId: searchParamsObj?.notificationId ?? '',
    company: searchParamsObj?.company ?? '',
    previousPage: searchParamsObj?.previousPage ?? 1,
    jobTitles: searchParamsObj?.jobTitles ?? [],
    includeSentJob: searchParamsObj?.includeSentJob ?? '',
    sort: searchParamsObj?.sort ?? 'desc',
  };

  const getPaginationCompaniesSearch = async () => {
    const { data } = await getPaginationGroupByCompanies(
      searchId,
      filter,
      page,
      pageSize
    );
    const paginationTemp = data.result;
    setPagination({ ...pagination, ...paginationTemp });
  };

  useEffect(() => {
    getPaginationCompaniesSearch();
  }, []);

  const [
    getCompany,
    { data: companyData, isFetching, isLoading: isCompanyLoading },
  ] = useLazyGetCompanyQuery();

  // useEffect(() => {
  //   if (!searchStatus) {
  //     if (currentCompany.length === 0) {
  //       // const newCompany = currentCompany.concat(resetStatus ? [] : allCompany);useEffect 1
  //       const newCompany = [...allCompany];
  //       // resetStatus && dispatch(setIsReset(false))
  //       setCurrentCompany(newCompany);
  //     } else {
  //       const filteredCompany = allCompany.filter(
  //         (company) =>
  //           !currentCompany.some(
  //             (c) => c.toLowerCase() === company.toLowerCase()
  //           )
  //       );
  //       const newCompany = [...filteredCompany];
  //       setCurrentCompany(resetStatus ? [] : newCompany);
  //       resetStatus && dispatch(setIsReset(false));
  //       setIsCollapseAll(false);
  //     }
  //   }
  // }, [allCompany]);

  const getJobsByCompaniesData = async (payload, count = 1, comapnies) => {
    payload.jsonToSearch = {
      ...payload.jsonToSearch,
      page: count,
    };
    const res = await bulkGetJobByCompanies({ ...payload });

    if (res.data?.result?.data?.length > 0) {
      const jobsTemp = [...res.data?.result?.data];
      const companiesTemp = [...comapnies];
      const newCompanies = companiesTemp.filter((companyName) => {
        let jobAmount = 0;
        jobsTemp.forEach((job) => {
          if (encodeURIComponent(job?.company) === companyName) {
            jobAmount++;
          }
        });

        return !(jobAmount >= 1);
      });

      payload.jsonToSearch = {
        ...payload.jsonToSearch,
      };
      if (newCompanies?.length > 0) {
        const newCompaniesQueryString = newCompanies?.map((name) =>
          encodeURIComponent(name)
        );
        payload.queryString =
          payload.queryString +
          '&companies=' +
          newCompaniesQueryString.join('&companies=');
        getJobsByCompaniesData(payload, count + 1, newCompaniesQueryString);
      }
    }
    return res.data?.result?.data;
  };

  const getData = async () => {
    if (searchId && userId && currentCompany?.length > 0) {
      const jsonToSearch = Object.assign({}, filterJob);
      delete jsonToSearch.jobTitles;
      delete jsonToSearch.previousPage;
      delete jsonToSearch.company;

      let queryString = 'jobTitles=';
      if (filterJob?.jobTitles || filterJob?.jobTitles?.length > 0) {
        if (Array.isArray(filterJob?.jobTitles)) {
          queryString =
            'jobTitles=' + filterJob?.jobTitles?.join('&jobTitles=');
        } else {
          queryString = `jobTitles=${filterJob?.jobTitles}`;
        }
      }
      try {
        const companies = currentCompany?.map((name) =>
          encodeURIComponent(name)
        );
        if (companies?.length > 0) {
          queryString =
            queryString + '&companies=' + companies.join('&companies=');
        }
        const data = await getJobsByCompaniesData(
          {
            searchId,
            userId,
            jsonToSearch: {
              ...jsonToSearch,
              limit: 20,
            },
            queryString,
          },
          1,
          companies
        );

        setJobsByCompanies([...jobsByCompanies, ...data]);
      } catch (error) {
        console.error(error);
      }
    }
  };

  useEffect(() => {
    setJobsByCompanies([]);
    getData();
  }, [currentCompany]);

  const userId = useMemo(() => {
    return _get(profileUser, 'id') || _get(profile, 'user.id') || '';
  }, [profile]);

  // const renderData = useMemo(() => {
  //   return allCompany;
  // }, [allCompany, displayedItemsCount, checkData]);

  useEffect(() => {
    if (searchStatus) {
      setCurrentCompany([]);
      dispatch(resetAllCompany());
      dispatch(setIsSearching(false));
    }
  }, [searchStatus]);

  const handleLoadData = async () => {
    setIsLoading(true);
    const newFilter = {
      ...filterJob,
      limit: 10,
      page: displayedItemsCount / 10,
    };
    const jsonToSearch = Object.assign({}, newFilter);
    delete jsonToSearch.jobTitles;
    delete jsonToSearch.previousPage;

    let queryString = 'jobTitles=';
    if (newFilter?.jobTitles || newFilter?.jobTitles?.length > 0) {
      if (Array.isArray(newFilter?.jobTitles)) {
        queryString = 'jobTitles=' + newFilter?.jobTitles?.join('&jobTitles=');
      } else {
        queryString = `jobTitles=${newFilter?.jobTitles}`;
      }
    }

    await getCompany({
      searchId,
      userId,
      jsonToSearch,
      queryString,
    });
    setIsLoading(false);
  };

  useEffect(() => {
    if (!searchStatus) {
      if (searchId && userId) {
        handleLoadData();
      } else if (searchId && userId) {
        getCompany({
          searchId,
          userId,
        });
      }
    }
  }, [searchId, userId, displayedItemsCount]);

  useEffect(() => {
    if (
      _.has(filterJob, 'newJobOnly') &&
      _.has(filterJob, 'notificationId') &&
      JSON.stringify(filterJob) !== JSON.stringify(filter)
    ) {
      handleLoadData();
    }
  }, [filterJob]);

  useEffect(() => {
    if (companyData) {
      // setCachedCompanyPagination
      console.log('companyData: ', _get(companyData, 'result.data'));
      console.log('pagination: ', pagination);

      let companyList = [..._get(companyData, 'result.data')];

      const field = `page${pagination?.currentPage}${pagination?.pageSize}`;
      // Check unique company
      for (const [key, valueRaw] of Object.entries(cachedCompanyPagination)) {
        const value = valueRaw.map((name) => name.toLowerCase());

        if (value?.length > 0) {
          const duplicatedCompanies = companyList
            ?.filter((name) => value?.includes(name.toLowerCase()))
            .map((n) => n.toLowerCase());
          if (
            duplicatedCompanies?.length > 0 &&
            !cachedCompanyPagination[field]
          ) {
            companyList = companyList?.filter(
              (name) => !duplicatedCompanies?.includes(name.toLowerCase())
            );
          } else if (cachedCompanyPagination[field]?.length > 0) {
            companyList = cachedCompanyPagination[field];
          }
        }
      }

      if (!cachedCompanyPagination[field]) {
        const currCachedCompanyPagination = { ...cachedCompanyPagination };
        currCachedCompanyPagination[field] = [...companyList];
        setCachedCompanyPagination({ ...currCachedCompanyPagination });
      }
      setCurrentCompany([...companyList]);
      // dispatch(saveAllCompany([...companyList]));
    }
  }, [companyData]);

  // useEffect(() => {

  // }, [pagination.currentPage, pagination.pageSize]);

  // useEffect(() => {
  //   // Lưu giá trị renderData vào biến ref
  //   renderDataRef.current = renderData;
  //   // Đánh dấu là renderData đã thay đổi
  //   hasRenderDataChanged.current = true;
  //   // console.log("renderData: ", renderData)
  // }, [renderData]);

  // useEffect(() => {
  //   if (hasRenderDataChanged.current) {
  //     if (renderData && _size(renderData) < 10) {
  //       dispatch(loadMoreCompany());
  //     }

  //     hasRenderDataChanged.current = false;
  //   }
  // }, []);

  useEffect(() => {
    // console.log('checkData: ', checkData)
    if (checkData?.length > 0) {
      const newCurrentCompany = currentCompany.filter(
        (comp) => !checkData.includes(comp)
      );
      setCurrentCompany([...newCurrentCompany]);
    }
  }, [checkData]);

  const handleDeleteCompany = (item /* companyName */) => {
    const newListCp = currentCompany.filter((arr) => !arr.includes(item));
    setCurrentCompany(newListCp);
  };

  const handleDeleteDataByCompanyNames = (names) => {
    if (!Array.isArray(names)) return;
    const newData = currentCompany.filter(
      (company) => !names.includes(company)
    );
    setCurrentCompany(newData);
  };

  useEffect(() => {
    if (searchStatus && companyData) {
      dispatch(resetAllCompany());
      setCurrentCompany(companyData?.result?.data ?? []);
      dispatch(setIsSearching(false));
      // dispatch(setIsSearching(false));
    }
  }, [companyData, searchStatus]);

  const onPageChange = (currentPage, pageSize) => {
    setCurrentCompany([]);
    window.scrollTo(0, 0);
    setPagination({ ...pagination, pageSize, currentPage });
    // const queryString = `limit=${pageSize}&page=${currentPage}`;

    const newFilter = {
      ...filterJob,
      limit: pageSize,
      page: currentPage,
    };
    const jsonToSearch = Object.assign({}, newFilter);
    delete jsonToSearch.jobTitles;
    delete jsonToSearch.previousPage;

    let queryString = 'jobTitles=';
    if (newFilter?.jobTitles || newFilter?.jobTitles?.length > 0) {
      if (Array.isArray(newFilter?.jobTitles)) {
        queryString = 'jobTitles=' + newFilter?.jobTitles?.join('&jobTitles=');
      } else {
        queryString = `jobTitles=${newFilter?.jobTitles}`;
      }
    }

    getCompany({
      searchId,
      userId,
      jsonToSearch,
      queryString,
    });
  };

  return (
    <>
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col
          span={24}
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <Button
            onClick={() => {
              setIsCollapseAll((state) => !state);
            }}
          >
            Collapse all
          </Button>
        </Col>

        <PinnedJobListGroupByCompany
          isFromSyncSearches={pinnedData?.isFromSyncSearches}
          searchData={pinnedData?.searchData}
          isSearchLoading={pinnedData?.isSearchLoading || pinnedData?.isLoading}
          populateSearchData={pinnedData?.populateSearchData}
          populatePinnedJobsData={pinnedData?.populatePinnedJobsData}
        />

        {/* {isFromSyncSearches ? (
          <Col span={24}>
            {isFetching && (
              <>
                {_map(_range(10), (_, idx) => (
                  <React.Fragment key={idx.toString()}>
                    <LoadingAdvanced isSkeleton />
                  </React.Fragment>
                ))}
              </>
            )}
          </Col>
        ) : (
          <Col span={24}>
            <LoadingBar loading={isFetching} />
          </Col>
        )} */}
        {isFromSyncSearches && isFetching && (
          <Col span={24}>
            {isFetching && (
              <>
                {_map(_range(10), (_, idx) => (
                  <React.Fragment key={idx.toString()}>
                    <LoadingAdvanced isSkeleton />
                  </React.Fragment>
                ))}
              </>
            )}
          </Col>
        )}
        {!isFromSyncSearches && isFetching && currentCompany?.length <= 0 && (
          <Col span={24}>{/* <LoadingBar loading={isFetching} /> */}</Col>
        )}
        {!isFetching &&
          _map(currentCompany, (item, idx) => (
            <CollapseComponent
              bulkJobsByCompaniesData={jobsByCompanies || []}
              isFetchingBulkJobs={isFetchingBulkJobs}
              item={item}
              defaultSize={_range(_size(currentCompany))}
              idx={idx}
              setCheckData={setCheckData}
              searchData={searchData}
              populateSearchData={populateSearchData}
              populatePinnedJobsData={populatePinnedJobsData}
              newJobOnly={newJobOnly}
              activeGroupByCompany={activeGroupByCompany}
              setActiveGroupByCompany={setActiveGroupByCompany}
              groupByCompanyIdJob={groupByCompanyIdJob}
              setGroupByCompanyIdJob={setGroupByCompanyIdJob}
              setGroupCompany={setGroupCompany}
              isFromSyncSearches={isFromSyncSearches}
              isCollapseAll={isCollapseAll}
              handleDeleteCompany={handleDeleteCompany}
              handleDeleteDataByCompanyNames={handleDeleteDataByCompanyNames}
            />
          ))}

        {/* {_size(currentCompany) > 0 && (
          <Col
            span={24}
            style={{
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              onClick={() => {
                // setIsLoading(true);
                dispatch(loadMoreCompany());
              }}
              loading={isLoading}
              disabled={isLoading}
            >
              Load more
            </Button>
          </Col>
        )} */}

        <Pagination
          disabled={isFetching}
          current={pagination.currentPage}
          onChange={onPageChange}
          total={pagination.totalCount}
          pageSize={Number(pagination.pageSize)}
          pageSizeOptions={[10]}
        />
      </Row>
    </>
  );
};

JobCollapseCompany.propTypes = {
  searchData: PropTypes.any,
  populateSearchData: PropTypes.any,
  populatePinnedJobsData: PropTypes.any,
  isFromSyncSearches: PropTypes.any,
  isFromReportedAgency: PropTypes.any,
  newJobOnly: PropTypes.any,
  activeGroupByCompany: PropTypes.any,
  setActiveGroupByCompany: PropTypes.any,
  groupByCompanyIdJob: PropTypes.any,
  setGroupByCompanyIdJob: PropTypes.any,
  setGroupCompany: PropTypes.any,
};

export default JobCollapseCompany;
