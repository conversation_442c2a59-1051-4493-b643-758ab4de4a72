import React, { useEffect, useState } from 'react';
import { Button, message, InputNumber, Space } from 'antd';
import { EditOutlined, CheckOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  getPotentialLeadPercentValue,
  updatePotentialLeadPercentValue,
} from '../../services/jobLeads';
import PropTypes from 'prop-types';
import { savePotentialLeadValue } from '../../store/common';
import { useDispatch } from 'react-redux';

const EditableNumber = ({
  className,
  refetchData = null,
  headless = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const dispatch = useDispatch();

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const { data: currentValue, refetch } = useQuery(
    ['POTENTIAL_RETURN_PERCENT'],
    {
      queryFn: async () => {
        const { data } = await getPotentialLeadPercentValue();

        const { potentialLeadValue } = data?.result;

        dispatch(savePotentialLeadValue(potentialLeadValue));

        return potentialLeadValue;
      },
    }
  );

  const [formValue, setFormValue] = useState(currentValue);

  useEffect(() => {
    setFormValue(currentValue);
  }, [currentValue]);

  const saveMutation = useMutation(async (value) => {
    const { data } = await updatePotentialLeadPercentValue(value);
    return data;
  });

  const handleSaveClick = async () => {
    // Call your API using React Query mutation
    try {
      await saveMutation.mutateAsync(formValue);
      refetch();
      refetchData && refetchData();
      setIsEditing(false);
      message.success('Data saved successfully');
    } catch (error) {
      message.error('Error saving data');
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {!headless && <p>Potential Lead Value Percentage</p>}
      <Space.Compact>
        <InputNumber
          min={0}
          max={100}
          value={formValue}
          onChange={(val) => {
            setFormValue(val);
          }}
          formatter={(value) => `${value}%`}
          parser={(value) => value.replace('%', '')}
          disabled={!isEditing}
        />
        {isEditing ? (
          <Button
            loading={saveMutation.isLoading}
            shape="default"
            icon={<CheckOutlined />}
            onClick={handleSaveClick}
          />
        ) : (
          <Button
            loading={saveMutation.isLoading}
            shape="default"
            icon={<EditOutlined />}
            onClick={handleEditClick}
          />
        )}
      </Space.Compact>
    </div>
  );
};

EditableNumber.propTypes = {
  className: PropTypes.string,
};

export default EditableNumber;
