/* eslint-disable react/prop-types */
import {
  AutoComplete,
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Spin,
  notification,
} from 'antd';
import { Controller } from 'react-hook-form';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { getKeywordJobSearch } from '../../services/getJson';
import { getListCities } from '../../services/geoname';
import { useQuery } from '@tanstack/react-query';
import { jobBoards } from '../../variables/selectOption';
import dayjs from 'dayjs';
import {
  getKeywordSuggestions,
  getSearches,
  getTitleSuggestions,
} from '../../services/search';
import { getCounties, getCountries } from '../../services/bullhorn';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { numberWithCommas } from '../../utils/common';
import { tagRender } from './NewSearchFormComponent';
import DebounceSelect from '../../common/DebounceSelect';
import { Plus, X } from 'lucide-react';

const { RangePicker } = DatePicker;

const NewCloneFormComponent = ({
  onFinish,
  control,
  isLoadingSyncDrawer,
  cloneValue,
  setCloneValue,
  cloneDrawerVisible,
  setValue,
  getValues,
  handleSubmit,
  openDrawler,
  fromCloneTab,
  watch,
}) => {
  const [tempCountries, setTempCountries] = useState([]);
  const [tempCities, setTempCities] = useState([]);
  const [listCities, setListCities] = useState([]);
  const [loadingCity, setLoadingCity] = useState(false);
  const [openClone, setOpenClone] = useState(false);
  const [searches, setSearch] = useState();
  const [pauseSearch, setPauseSearch] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [countryState, setCountryState] = useState();
  const [cloneChoose, setCloneChoose] = useState();

  // keywords and job titles adding text
  const [keywordsOptions, setKeyWordsOptions] = useState([]);
  const [jobTitleOtions, setJobTitleOtions] = useState([]);

  const [keyword, setKeyword] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [showJobTitleSuggestions, setShowJobTitleSuggestions] = useState(false);
  const [showKeywordSuggestions, setShowKeywordSuggestions] = useState(false);
  const [jobTitleLoading, setJobTitleLoading] = useState(false);
  const [keywordLoading, setKeywordLoading] = useState(false);

  const keywordSuggestionsRef = useRef(null);
  const jobTitleSuggestionsRef = useRef(null);
  const debounceRef = useRef();

  const [countyLoading, setCountyLoading] = useState(true);
  const handleGetData = async () => {
    const { data } = await getSearches(
      'active_subprocess_id',
      'ASC',
      currentPage,
      10
    );

    setSearch(data?.result?.data);
  };

  useEffect(() => {
    if (cloneValue) {
      setValue('searchIds', {
        label: cloneValue.searchName,
        value: cloneValue.searchName,
        key: cloneValue.id,
      });
    }
  }, [cloneValue]);

  useEffect(() => {
    if (searches && cloneValue) {
      onFinishCloneForm({
        searchIds: {
          label: cloneValue?.searchName,
          value: cloneValue?.searchName,
          key: cloneValue?.id,
        },
      });
    }
  }, [searches, openDrawler]);

  useEffect(() => {
    handleGetData();
  }, []);

  const handleGetNextData = async () => {
    if (!pauseSearch) {
      const { data } = await getSearches(
        'active_subprocess_id',
        'ASC',
        currentPage + 1,
        10
      );
      setCurrentPage(currentPage + 1);

      const newData = [...searches];
      newData.push(data?.result?.data);
      setSearch(newData.flat());

      if (data?.result?.data.length == 0) {
        setPauseSearch(true);
      }
    }
  };

  const onFinishCloneForm = (data) => {
    if (data?.searchIds?.key) {
      const dataSearchItem = searches?.find(
        (obj) => obj?.id === data?.searchIds?.key
      );
      if (!dataSearchItem) return;
      setCloneChoose(dataSearchItem);
      setOpenClone(true);
      setValue('id', dataSearchItem?.id);
      setValue('searchName', dataSearchItem?.searchName);
      setValue(
        'keywords',
        dataSearchItem?.keywords?.split(',').map((item) => {
          return { label: item, value: item };
        })
      );
      setValue(
        'jobTitles',
        dataSearchItem?.jobTitles?.map((item) => {
          return { label: item, value: item };
        })
      );
      setValue('country', dataSearchItem?.country);
      setCountryState(dataSearchItem?.country);
      setValue(
        'adminLevelOneArea',
        dataSearchItem?.adminLevelOneArea
          ? dataSearchItem?.adminLevelOneArea?.includes(',')
            ? dataSearchItem?.adminLevelOneArea?.split(',')
            : [...dataSearchItem?.adminLevelOneArea]
          : []
      );
      setValue(
        'jobBoards',
        dataSearchItem?.jobBoards ? dataSearchItem?.jobBoards?.split(',') : []
      );
      if (dataSearchItem?.postedStartDate && dataSearchItem?.postedEndDate)
        setValue('postedWithin', [
          dayjs(dataSearchItem?.postedStartDate),
          dayjs(dataSearchItem?.postedEndDate),
        ]);
      else setValue('postedWithin', null);
      setValue('minSalary', dataSearchItem?.minSalary || '');
      setValue('maxSalary', dataSearchItem?.maxSalary || '');
      setValue('city', dataSearchItem?.city || '');
    }
  };

  const fetchSuggestionList = async (text, type = 'keywords') => {
    try {
      let data;
      if (type === 'title') {
        setJobTitleLoading(true);
        const { data: suggestionData } = await getTitleSuggestions(text);
        data = suggestionData;
        setJobTitleOtions(
          suggestionData?.result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          })) || []
        );

        setJobTitleLoading(false);
        setShowJobTitleSuggestions(suggestionData?.result?.length > 0);
      } else {
        setKeywordLoading(true);
        const { data: suggestionData } = await getKeywordSuggestions(text);
        data = suggestionData;
        setKeyWordsOptions(
          suggestionData?.result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          })) || []
        );
        setKeywordLoading(false);
        setShowKeywordSuggestions(suggestionData?.result?.length > 0);
      }
      const { result } = data;
      return result?.length > 0
        ? result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          }))
        : [];
    } catch (error) {
      setJobTitleLoading(false);
      setKeywordLoading(false);
      console.log('Fetching suggestions failed: ', error);
      return [];
    }
  };

  const handleGetListCitiesFromBH = async (code) => {
    try {
      setTempCountries([]);
      const { data } = await getCountries(code);
      setTempCountries(data.result);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const { data: ukCounties = [] } = useQuery(
    ['GET_COUNTIES'],
    async () => {
      try {
        const { data } = await getCountries();
        setTempCountries(data.result);
        return data.result;
      } catch (err) {
        notification.error({ message: err?.response?.data?.message });
      }
    },
    { enabled: true }
  );

  // useEffect(() => {
  //   if (getValues('country') || countryState) {
  //     handleGetListCitiesStateFromBH('', getValues('country') || countryState);
  //   }
  // }, [getValues('country'), countryState]);

  const handleEffectData = async () => {
    setCountyLoading(true);
    if (cloneValue?.country || cloneChoose?.country) {
      try {
        const { data } = await getCountries(
          cloneValue?.country || cloneChoose?.country
        );
        if (data?.result?.length > 0) {
          // setValue('country', data?.result[0]?.label)
          await handleGetListCitiesStateFromBH('', data?.result[0]?.value);
        }
      } catch (error) {
        setCountyLoading(false);
      }
    } else {
      return;
    }
  };

  useEffect(() => {
    handleEffectData();
  }, [cloneValue?.country, cloneChoose]);

  const handleGetListCities = async (code) => {
    setListCities([]);
    const data = await getListCities(code);
    setListCities(data?.geonames);
    setValue('adminLevelOneArea', '');
  };

  // Handle outside click to close suggestions and country dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      // Close keyword suggestions
      if (
        keywordSuggestionsRef.current &&
        !keywordSuggestionsRef.current.contains(event.target)
      ) {
        setShowKeywordSuggestions(false);
      }

      // Close job title suggestions
      if (
        jobTitleSuggestionsRef?.current &&
        !jobTitleSuggestionsRef?.current?.contains(event?.target)
      ) {
        setShowJobTitleSuggestions(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // const handleInputCity = async (inputValue, countryId) => {
  //   await handleGetListCitiesStateFromBH(inputValue, countryId);
  // }; -- not use

  const debouncedFetchSuggestionList = useCallback(
    (text, type = 'keywords') => {
      if (debounceRef.current) clearTimeout(debounceRef.current);
      debounceRef.current = setTimeout(() => {
        fetchSuggestionList(text, type);
      }, 400); // 400ms debounce
    },
    []
  );

  const onSubmitJobTitle = (newKeyword) => {
    if (!newKeyword?.trim()) {
      notification.warning({
        description: 'Please enter a job title!',
      });
      return;
    }
    const currentKeywords = [...(getValues('jobTitles') || [])];
    const isExisting = currentKeywords?.some(
      (currentKeyword) => currentKeyword?.value === newKeyword
    );
    if (isExisting) {
      notification.warning({
        description: `${newKeyword} is existing!`,
      });
      return;
    }
    const addedKeywords = [
      ...currentKeywords,
      { value: newKeyword, label: newKeyword },
    ];
    setValue('jobTitles', [...addedKeywords]);
    const newJobTitleOptions = jobTitleOtions.filter(
      (option) => option.value !== newKeyword
    );
    setJobTitleOtions(newJobTitleOptions);

    if (jobTitle?.toLowerCase()?.trim() === newKeyword?.toLowerCase()?.trim()) {
      setJobTitle('');
      setShowJobTitleSuggestions(false);
      setJobTitleOtions([]); // Clear suggestions after adding
    }
  };

  const onSubmitKeyword = (newKeyword) => {
    if (!newKeyword?.trim()) {
      notification.warning({
        description: 'Please enter a keyword!',
      });
      return;
    }
    const currentKeywords = [...(getValues('keywords') || [])];
    const isExisting = currentKeywords?.some(
      (currentKeyword) => currentKeyword?.value === newKeyword
    );
    if (isExisting) {
      notification.warning({
        description: `${newKeyword} is existing!`,
      });
      return;
    }
    const addedKeywords = [
      ...currentKeywords,
      { value: newKeyword, label: newKeyword },
    ];
    setValue('keywords', [...addedKeywords]);

    const newKeywordsOptions = keywordsOptions.filter(
      (option) => option.value !== newKeyword
    );
    setKeyWordsOptions(newKeywordsOptions);

    if (keyword?.toLowerCase()?.trim() === newKeyword?.toLowerCase()?.trim()) {
      setKeyword('');
      setShowKeywordSuggestions(false);
      setKeyWordsOptions([]); // Clear suggestions after adding
    }
  };

  const handleGetListCitiesStateFromBH = async (filter = '', countryId) => {
    if (!countryId) return;
    try {
      setCountyLoading(true);
      setListCities([]);
      const { data } = await getCounties({ filter, countryId });
      setListCities(data?.result);
      setCountyLoading(false);
    } catch (err) {
      setCountyLoading(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  if (cloneValue) {
    if (!searches) {
      return (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Spin></Spin>
        </div>
      );
    }
  }
  return (
    <>
      <div className="relative h-full">
        {!openClone && (
          <Form
            layout="vertical"
            onFinish={handleSubmit(onFinishCloneForm)}
            // className="grid grid-cols-10 gap-3 items-center"
            className="p-4 rounded-md w-full h-full"
          >
            <Form.Item
              label={
                <span className="font-medium">Choose Search to Clone</span>
              }
              name="searchIds"
              className="!mb-3"
            >
              <Controller
                name="searchIds"
                control={control}
                render={({ field }) => (
                  <Select
                    placeholder="Select a search to clone"
                    labelInValue
                    {...field}
                    options={searches?.map((val) => ({
                      label: val?.searchName,
                      value: val?.searchName,
                      key: val?.id,
                    }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                    onPopupScroll={(e) => {
                      if (
                        e.target.scrollTop ===
                        e.target.scrollHeight - e.target.clientHeight
                      ) {
                        handleGetNextData();
                      }
                    }}
                  />
                )}
              />
            </Form.Item>
            <Form.Item className="!mb-0">
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                {/* <Button
                disabled={isLoadingSyncDrawer}
                onClick={(e) => {
                  e.preventDefault();
                  setOpenClone(false);
                  setValue('id', null);
                  setValue('searchName', null);
                  setValue('keywords', null);
                  setValue('country', null);
                  setValue('adminLevelOneArea', null);
                  setValue('jobBoards', null);
                  setValue('postedWithin', null);
                  setValue('minSalary', null);
                  setValue('maxSalary', null);
                  setValue('city', null);
                  setCountryState(null);
                }}
                className={`w-full ${isLoadingSyncDrawer ? 'bg-gray-500' : 'bg-blue-500'}`}
              >
                <span
                  className={isLoadingSyncDrawer ? 'text-black' : 'text-white'}
                >
                  X
                </span>
              </Button> */}

                <Button
                  type="primary"
                  disabled={isLoadingSyncDrawer}
                  htmlType="submit"
                  className={`w-full ${isLoadingSyncDrawer ? 'bg-gray-500' : 'bg-blue-500'}`}
                >
                  <span
                  // className={
                  //   isLoadingSyncDrawer ? 'text-black' : 'text-white'
                  // }
                  >
                    <span>Process to clone</span> <ArrowRightOutlined />
                  </span>
                </Button>
              </div>
            </Form.Item>
          </Form>
        )}

        {openClone && (
          <div className="h-full">
            {countyLoading ? (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                className="mt-10"
              >
                <Spin />
              </div>
            ) : (
              <Form
                layout="vertical"
                className="h-full"
                onFinish={onFinish}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission on Enter key
                  }
                }}
              >
                <div className="h-full flex flex-col justify-between">
                  <div className="search-form-container p-5">
                    <Form.Item
                      label={
                        <p>
                          Search Name <span className="text-red-600">*</span>
                        </p>
                      }
                      name="searchName"
                    >
                      <Controller
                        name="searchName"
                        control={control}
                        render={({ field }) => (
                          <Input {...field} className="search-input" required />
                        )}
                      />
                    </Form.Item>
                    <Form.Item label="Salary" name="salary">
                      <Row gutter={8}>
                        <Col span={12}>
                          <Controller
                            name="minSalary"
                            control={control}
                            render={({ field }) => (
                              <InputNumber
                                {...field}
                                prefix={
                                  <div className="text-gray-500 font-medium">
                                    £
                                  </div>
                                }
                                className="search-input w-full"
                                placeholder="Min Salary"
                                formatter={numberWithCommas}
                              />
                            )}
                          />
                        </Col>
                        <Col span={12}>
                          <Controller
                            name="maxSalary"
                            control={control}
                            render={({ field }) => (
                              <InputNumber
                                {...field}
                                prefix={
                                  <div className="text-gray-500 font-medium">
                                    £
                                  </div>
                                }
                                className="search-input w-full"
                                placeholder="Max Salary"
                                formatter={numberWithCommas}
                              />
                            )}
                          />
                        </Col>
                      </Row>
                    </Form.Item>
                    <Form.Item label="Job Titles" name="jobTitles">
                      <div className="relative">
                        <Input.Search
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          value={jobTitle}
                          onChange={(e) => {
                            const text = e.target.value;
                            setJobTitle(text);
                            debouncedFetchSuggestionList(text, 'title');
                          }}
                          // onSearch={(text) =>
                          //   debouncedFetchSuggestionList(text, 'title')
                          // }
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault(); // Prevent form submission on Enter key
                              onSubmitJobTitle(jobTitle);
                            }
                          }}
                          className="search-input restyle-search-input"
                          placeholder="Add job titles..."
                          enterButton={
                            jobTitleLoading ? (
                              <Spin
                                size="small"
                                className="h-4 w-4 text-cyan-500"
                              />
                            ) : (
                              <Plus
                                onClick={(e) => {
                                  e.preventDefault();
                                  onSubmitJobTitle(jobTitle);
                                }}
                                className="h-4 w-4 text-cyan-500 hover:text-cyan-600 font-semibold"
                              />
                            )
                          }
                        />
                        {/* Job Title Suggestions Dropdown */}
                        {showJobTitleSuggestions && (
                          <div
                            ref={jobTitleSuggestionsRef}
                            className="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-48 overflow-y-auto"
                          >
                            <div className="p-2 border-b border-gray-100 bg-gray-50">
                              <p className="text-xs text-gray-500 font-medium">
                                Related job titles - click to add:
                              </p>
                            </div>
                            <div className="p-2">
                              <div className="flex flex-wrap gap-1.5">
                                {jobTitleOtions.map((suggestion, index) => (
                                  <button
                                    key={index}
                                    type="button"
                                    onClick={() =>
                                      onSubmitJobTitle(suggestion?.value)
                                    }
                                    className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                                  >
                                    {suggestion?.label}
                                    <Plus className="h-3 w-3 ml-1" />
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Selected Job Title */}
                        {watch('jobTitles')?.length > 0 && (
                          <div className="mt-2 w-full max-h-48 overflow-y-auto">
                            <div className="flex flex-wrap gap-1.5">
                              {watch('jobTitles')?.map((jobTitle) => (
                                <button
                                  key={jobTitle?.value}
                                  type="button"
                                  onClick={() => {
                                    const currentJobTitles = [
                                      ...(getValues('jobTitles') || []),
                                    ];
                                    const updatedJobTitles =
                                      currentJobTitles.filter(
                                        (item) => item.value !== jobTitle.value
                                      );
                                    setValue('jobTitles', updatedJobTitles);
                                  }}
                                  className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                                >
                                  {jobTitle?.value}
                                  <X className="h-3 w-3 ml-1" />
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </Form.Item>
                    <Form.Item label="Keywords" name="keywords">
                      <div className="relative">
                        <Input.Search
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          value={keyword}
                          onChange={(e) => {
                            const text = e.target.value;
                            setKeyword(text);
                            debouncedFetchSuggestionList(text, 'keywords');
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault(); // Prevent form submission on Enter key
                              onSubmitKeyword(keyword);
                            }
                          }}
                          className="search-input restyle-search-input"
                          placeholder="Add keywords..."
                          enterButton={
                            keywordLoading ? (
                              <Spin
                                size="small"
                                className="h-4 w-4 text-cyan-500"
                              />
                            ) : (
                              <Plus
                                onClick={(e) => {
                                  e.preventDefault();
                                  onSubmitKeyword(keyword);
                                }}
                                className="h-4 w-4 text-cyan-500 hover:text-cyan-600 font-semibold"
                              />
                            )
                          }
                        />
                        {/* Job Title Suggestions Dropdown */}
                        {showKeywordSuggestions && (
                          <div
                            ref={keywordSuggestionsRef}
                            className="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-48 overflow-y-auto"
                          >
                            <div className="p-2 border-b border-gray-100 bg-gray-50">
                              <p className="text-xs text-gray-500 font-medium">
                                Related keywords - click to add:
                              </p>
                            </div>
                            <div className="p-2">
                              <div className="flex flex-wrap gap-1.5">
                                {keywordsOptions.map((keyword, index) => (
                                  <button
                                    key={index}
                                    type="button"
                                    onClick={() =>
                                      onSubmitKeyword(keyword?.value)
                                    }
                                    className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                                  >
                                    {keyword?.label}
                                    <Plus className="h-3 w-3 ml-1" />
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                        {/* Selected keywords */}
                        {watch('keywords')?.length > 0 && (
                          <div className="mt-2 w-full max-h-48 overflow-y-auto">
                            <div className="flex flex-wrap gap-1.5">
                              {watch('keywords')?.map((keyword) => (
                                <button
                                  key={keyword?.value}
                                  type="button"
                                  onClick={() => {
                                    const currentJobTitles = [
                                      ...(getValues('keywords') || []),
                                    ];
                                    const updatedJobTitles =
                                      currentJobTitles.filter(
                                        (item) => item.value !== keyword.value
                                      );
                                    setValue('keywords', updatedJobTitles);
                                  }}
                                  className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                                >
                                  {keyword?.value}
                                  <X className="h-3 w-3 ml-1" />
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </Form.Item>
                    <div className="grid grid-cols-2 gap-3">
                      <Form.Item label="Countries" name="country">
                        <Controller
                          name="country"
                          control={control}
                          render={({ field }) => (
                            <Select
                              {...field}
                              showSearch
                              optionFilterProp="label"
                              className="search-input"
                              options={tempCountries.map((ao) => ({
                                label: ao.label,
                                value: ao.label,
                                code: ao.value,
                              }))}
                              // onSearch={(e) => handleGetListCitiesFromBH(e)}
                              placeholder="country"
                              onSelect={async (selected, record) => {
                                await handleGetListCitiesStateFromBH(
                                  '',
                                  record.code
                                );
                                setValue('city', '');
                                setValue('adminLevelOneArea', []);
                                setValue('country', selected);
                                setValue('countryCode', record.code);
                              }}
                            ></Select>
                          )}
                        />
                      </Form.Item>
                      <Form.Item label="State/ County" name="adminLevelOneArea">
                        <Controller
                          name="adminLevelOneArea"
                          control={control}
                          render={({ field }) => (
                            <Select
                              placeholder="eg., London"
                              tagRender={(props) =>
                                tagRender({ ...props, tagColor: 'tag-cyan' })
                              }
                              {...field}
                              mode="multiple"
                              className="search-input"
                              options={listCities
                                .filter(
                                  (item, index, self) =>
                                    self.findIndex(
                                      (t) => t.value === item.value
                                    ) === index
                                )
                                .map((ao) => ({
                                  label: ao.label,
                                  value: ao.value,
                                  adminName1: ao,
                                }))}
                              loading={countyLoading}
                              disabled={countyLoading}
                            ></Select>
                          )}
                        />
                      </Form.Item>
                    </div>
                    <Form.Item label="Posted within" name="postedWithin">
                      <Controller
                        name="postedWithin"
                        control={control}
                        render={({ field }) => <RangePicker {...field} />}
                      />
                    </Form.Item>
                  </div>
                  <Form.Item>
                    <div className="p-4 border-t border-gray-200 bg-gray-50 h-full">
                      <Button
                        disabled={isLoadingSyncDrawer}
                        htmlType="submit"
                        className="w-full bg-cyan-500 hover:bg-cyan-600 text-white h-10 font-medium"
                      >
                        <span>Add new Search</span>
                      </Button>
                    </div>
                  </Form.Item>
                </div>
              </Form>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default NewCloneFormComponent;
