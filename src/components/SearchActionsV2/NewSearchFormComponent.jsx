/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import {
  AutoComplete,
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
  DatePicker,
  Modal,
  Spin,
  Dropdown,
  Tag,
  notification,
  InputNumber,
  Tooltip,
  Popconfirm,
  Space,
} from 'antd';
import { Controller } from 'react-hook-form';
import { jobBoards } from '../../variables/selectOption';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getCounties, getCountries } from '../../services/bullhorn';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import { useQuery } from '@tanstack/react-query';
import {
  CloseCircleOutlined,
  CloseOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { numberWithCommas } from '../../utils/common';
import DebounceSelect from '../../common/DebounceSelect';
import {
  getKeywordSuggestions,
  getTitleSuggestions,
} from '../../services/search';
import { Plus, X } from 'lucide-react';
import { get } from 'lodash';

const { RangePicker } = DatePicker;

const technicalSkills = [
  'JavaScript',
  'React',
  'Node.js',
  'Python',
  'Java',
  'HTML',
  'CSS',
  'SQL',
  'Angular',
  'Vue.js',
  'C#',
  'Ruby',
  'PHP',
  'Swift',
  'Flutter',
  'Django',
  'Spring Boot',
  'Express.js',
  'MongoDB',
  'MySQL',
  'PostgreSQL',
  'AWS',
  'Azure',
  'Git',
  'Docker',
  'Kubernetes',
  'Machine Learning',
  'Artificial Intelligence',
  'Blockchain',
  'DevOps',
  'CI/CD',
  'RESTful API',
  'GraphQL',
  'Redux',
  'Webpack',
  'TypeScript',
  'TensorFlow',
  'PyTorch',
  'Kotlin',
  'Rust',
  'Go',
];

export const tagRender = (
  props,
  isSuggestionMode = false,
  handleAddItem = null
) => {
  const { label, onClose, tagColor } = props;

  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);

  const fetchSuggestions = async () => {
    try {
      let data;

      setLoading(true);

      // tag-blue is for title
      if (tagColor === 'tag-blue') {
        const { data: keywordData } = await getTitleSuggestions(label);
        data = keywordData;
      } else {
        const { data: titleData } = await getKeywordSuggestions(label);
        data = titleData;
      }
      const { result } = data;
      const handledResult = result?.map((keyword) => ({
        label: keyword?.displayName,
        value: keyword?.displayName,
      }));
      handledResult.shift();
      setSuggestions([...handledResult]);
      setLoading(false);
    } catch (error) {
      console.log('Fetching suggestions failed: ', error);
      setLoading(false);
    }
  };
  const onPreventMouseDown = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  // useEffect(() => {
  //   return () => setSuggestions([]);
  // });

  const renderTag = (tagLabel) => (
    <div
      onMouseDown={onPreventMouseDown}
      onClick={isSuggestionMode && fetchSuggestions}
      className={`flex justify-center items-center gap-2 tag font-Montserrat ${tagColor || 'tag-normal'} ${isSuggestionMode && 'cursor-pointer hover:border-[#3a3a3a]'}`}
    >
      <span className="mr-1">{tagLabel}</span>
      <Tooltip title="Remove">
        <CloseOutlined
          onClick={onClose}
          onMouseDown={onPreventMouseDown}
          className="text-[10px] hover:cursor-pointer"
        />
      </Tooltip>
    </div>
  );

  const renderSuggestion = (suggestion) => {
    const { label: suggestionLabel } = suggestion;
    return (
      <Tooltip title="Add">
        <div
          onMouseDown={onPreventMouseDown}
          onClick={() => handleAddItem(suggestion)}
          className={`flex justify-center items-center gap-2 tag font-Montserrat tag-normal ${isSuggestionMode && 'cursor-pointer hover:border-[#3a3a3a]'}`}
        >
          <span className="mr-1">{suggestionLabel}</span>

          <PlusOutlined
            // onClick={() => console.log(`tag ${tagLabel} added.`)}
            onMouseDown={onPreventMouseDown}
            className="text-[10px] hover:cursor-pointer"
          />
        </div>
      </Tooltip>
    );
  };

  return (
    (label && isSuggestionMode && (
      <Popconfirm
        rootClassName="customize-tooltip-suggestion"
        description={
          <div className="min-w-[5rem] max-w-[44rem] flex gap-2 flex-wrap">
            {loading && (
              <div className="w-full flex justify-center py-3">
                <Spin />
              </div>
            )}
            {!loading &&
              suggestions.length > 0 &&
              suggestions?.map((suggestion, index) => (
                <div className="flex gap-2 items-center">
                  <span>{renderSuggestion(suggestion)}</span>
                  {index + 1 !== suggestions?.length && (
                    <span className="text-xs">OR</span>
                  )}
                </div>
              ))}
            {!loading && suggestions.length === 0 && (
              <div>
                <span>There are no suggestion for </span>
                <span className="font-semibold text-red-500">{label}</span>
              </div>
            )}
          </div>
        }
        onConfirm={null}
        onCancel={null}
        placement="bottom"
      >
        <div
          onMouseDown={onPreventMouseDown}
          onClick={isSuggestionMode && fetchSuggestions}
          className={`flex justify-center items-center gap-2 tag font-Montserrat ${tagColor || 'tag-normal'} ${isSuggestionMode && 'cursor-pointer hover:border-[#3a3a3a]'}`}
        >
          <span className="mr-1">{label}</span>
          <Tooltip title="Remove">
            <CloseOutlined
              onClick={onClose}
              onMouseDown={onPreventMouseDown}
              className="text-[10px] hover:cursor-pointer"
            />
          </Tooltip>
        </div>
      </Popconfirm>
    )) ||
    (label && renderTag(label))
  );
};

const NewSearchFormComponent = ({
  isEdit = false,
  control,
  onFinish,
  isLoadingSearchDrawer,
  setValue,
  getValues,
  searchDrawerVisible,
  watch,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [countyLoading, setCountyLoading] = useState(true);
  const [startLoading, setStartLoading] = useState(true);
  const [dayLoad, setDayLoad] = useState(
    getValues('datePostedText') || 'Date posted'
  );

  // keywords and job titles adding text
  const [keywordsOptions, setKeyWordsOptions] = useState([]);
  const [jobTitleOtions, setJobTitleOtions] = useState([]);

  const [keyword, setKeyword] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [showJobTitleSuggestions, setShowJobTitleSuggestions] = useState(false);
  const [showKeywordSuggestions, setShowKeywordSuggestions] = useState(false);
  const [jobTitleLoading, setJobTitleLoading] = useState(false);
  const [keywordLoading, setKeywordLoading] = useState(false);

  const keywordSuggestionsRef = useRef(null);
  const jobTitleSuggestionsRef = useRef(null);
  const debounceRef = useRef();

  const handleOk = async () => {
    setConfirmLoading(true);
    setValue('location', getValues().city);
    setConfirmLoading(false);
    setModalOpen(false);
  };
  const handleCancel = () => {
    setValue('location', '');
    setModalOpen(false);
  };

  const [tempUkCounties, setTempUkCounties] = useState([]);
  const { data: ukCounties = [] } = useQuery(
    ['GET_COUNTIES'],
    async () => {
      try {
        const { data } = await getCountries();
        setTempUkCounties(data.result);
        return data.result;
      } catch (e) {
        notification.error({ message: err?.response?.data?.message });
      }
    },
    { enabled: true }
  );

  const [tempCountries, setTempCountries] = useState([]);

  const [tempCities, setTempCities] = useState([]);
  const [listCities, setListCities] = useState([]);
  const [loadingCity, setLoadingCity] = useState(false);

  const handleGetListCitiesFromBH = async (code) => {
    setCountyLoading(true);
    try {
      setTempUkCounties([]);
      const { data } = await getCountries(code);
      setTempUkCounties(data.result);
      setCountyLoading(false);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
      setCountyLoading(false);
    }
  };

  const handleGetListCitiesStateFromBH = async (filter = '', countryId) => {
    if (!countryId) return;
    try {
      setCountyLoading(true);
      setListCities([]);
      const { data } = await getCounties({ filter, countryId });
      setStartLoading(false);
      setListCities(data?.result);
      setCountyLoading(false);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
      setCountyLoading(false);
    }
  };

  const fetchSuggestionList = async (text, type = 'keywords') => {
    try {
      let data;
      if (type === 'title') {
        setJobTitleLoading(true);
        const { data: suggestionData } = await getTitleSuggestions(text);
        data = suggestionData;
        setJobTitleOtions(
          suggestionData?.result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          })) || []
        );

        setJobTitleLoading(false);

        setShowJobTitleSuggestions(suggestionData?.result?.length > 0);
      } else {
        setKeywordLoading(true);
        const { data: suggestionData } = await getKeywordSuggestions(text);
        data = suggestionData;
        setKeyWordsOptions(
          suggestionData?.result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          })) || []
        );
        setKeywordLoading(false);
        setShowKeywordSuggestions(suggestionData?.result?.length > 0);
      }
      const { result } = data;
      return result?.length > 0
        ? result?.map((keyword) => ({
            label: keyword?.displayName,
            value: keyword?.displayName,
          }))
        : [];
    } catch (error) {
      setJobTitleLoading(false);
      setKeywordLoading(false);
      console.log('Fetching suggestions failed: ', error);
      return [];
    }
  };

  const handleEffectData = async () => {
    if (!getValues('country')) {
      setValue('country', 'United Kingdom');
      await handleGetListCitiesStateFromBH('', 2359);
    } else {
      const { data } = await getCountries(getValues('country'));
      if (data?.result?.length > 0) {
        // setValue('country', data?.result[0]?.label)
        await handleGetListCitiesStateFromBH('', data?.result[0]?.value);
      }
    }
  };

  useEffect(() => {
    handleEffectData();
  }, []);

  useEffect(() => {
    if (getValues('adminLevelOneArea')) {
      if (getValues('adminLevelOneArea')?.length > 0) {
        setValue('adminLevelOneArea', getValues('adminLevelOneArea'));
      } else {
        setValue(
          'adminLevelOneArea',
          getValues('adminLevelOneArea')
            ? getValues('adminLevelOneArea')?.split(',')
            : []
        );
      }
    }
  }, [searchDrawerVisible]);

  // Handle outside click to close suggestions and country dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      // Close keyword suggestions
      if (
        keywordSuggestionsRef.current &&
        !keywordSuggestionsRef.current.contains(event.target)
      ) {
        setShowKeywordSuggestions(false);
      }

      // Close job title suggestions
      if (
        jobTitleSuggestionsRef?.current &&
        !jobTitleSuggestionsRef?.current?.contains(event?.target)
      ) {
        setShowJobTitleSuggestions(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const debouncedFetchSuggestionList = useCallback(
    (text, type = 'keywords') => {
      if (debounceRef.current) clearTimeout(debounceRef.current);
      debounceRef.current = setTimeout(() => {
        fetchSuggestionList(text, type);
      }, 400); // 400ms debounce
    },
    []
  );

  const onSubmitJobTitle = (newKeyword) => {
    if (!newKeyword?.trim()) {
      notification.warning({
        description: 'Please enter a job title!',
      });
      return;
    }
    const currentKeywords = [...(getValues('jobTitles') || [])];
    const isExisting = currentKeywords?.some(
      (currentKeyword) => currentKeyword?.value === newKeyword
    );
    if (isExisting) {
      notification.warning({
        description: `${newKeyword} is existing!`,
      });
      return;
    }
    const addedKeywords = [
      ...currentKeywords,
      { value: newKeyword, label: newKeyword },
    ];
    setValue('jobTitles', [...addedKeywords]);
    const newJobTitleOptions = jobTitleOtions.filter(
      (option) => option.value !== newKeyword
    );
    setJobTitleOtions(newJobTitleOptions);

    if (jobTitle?.toLowerCase()?.trim() === newKeyword?.toLowerCase()?.trim()) {
      setJobTitle('');
      setShowJobTitleSuggestions(false);
      setJobTitleOtions([]); // Clear suggestions after adding
    }
  };

  const onSubmitKeyword = (newKeyword) => {
    if (!newKeyword?.trim()) {
      notification.warning({
        description: 'Please enter a keyword!',
      });
      return;
    }
    const currentKeywords = [...(getValues('keywords') || [])];
    const isExisting = currentKeywords?.some(
      (currentKeyword) => currentKeyword?.value === newKeyword
    );
    if (isExisting) {
      notification.warning({
        description: `${newKeyword} is existing!`,
      });
      return;
    }
    const addedKeywords = [
      ...currentKeywords,
      { value: newKeyword, label: newKeyword },
    ];
    setValue('keywords', [...addedKeywords]);

    const newKeywordsOptions = keywordsOptions.filter(
      (option) => option.value !== newKeyword
    );
    setKeyWordsOptions(newKeywordsOptions);

    if (keyword?.toLowerCase()?.trim() === newKeyword?.toLowerCase()?.trim()) {
      setKeyword('');
      setShowKeywordSuggestions(false);
      setKeyWordsOptions([]); // Clear suggestions after adding
    }
  };

  return (
    <>
      <Form
        layout="vertical"
        className="h-full"
        onFinish={onFinish}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            e.preventDefault(); // Prevent form submission on Enter key
          }
        }}
      >
        {startLoading ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Spin />
          </div>
        ) : (
          <div className="h-full flex flex-col justify-between">
            <div className="search-form-container p-5">
              <Form.Item
                label={
                  <p className="font-medium text-gray-800">
                    Search Name <span className="text-red-600">*</span>
                  </p>
                }
                name="searchName"
              >
                <Controller
                  name="searchName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      placeholder="e.g., Senior React Developers London"
                      {...field}
                      className="search-input"
                      required
                    />
                  )}
                />
              </Form.Item>
              <Form.Item label="Salary" name="salary">
                <Row gutter={8}>
                  <Col span={12}>
                    <Controller
                      name="minSalary"
                      control={control}
                      render={({ field }) => (
                        <InputNumber
                          {...field}
                          prefix={
                            <div className="text-gray-500 font-medium">£</div>
                          }
                          className="search-input w-full"
                          placeholder="Min Salary"
                          formatter={numberWithCommas}
                        />
                      )}
                    />
                  </Col>
                  <Col span={12}>
                    <Controller
                      name="maxSalary"
                      control={control}
                      render={({ field }) => (
                        <InputNumber
                          {...field}
                          prefix={
                            <div className="text-gray-500 font-medium">£</div>
                          }
                          className="search-input w-full"
                          placeholder="Max Salary"
                          formatter={numberWithCommas}
                        />
                      )}
                    />
                  </Col>
                </Row>
              </Form.Item>
              <Form.Item label="Job Titles" name="jobTitles">
                <div className="relative">
                  <Input.Search
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    value={jobTitle}
                    onChange={(e) => {
                      const text = e.target.value;
                      setJobTitle(text);
                      debouncedFetchSuggestionList(text, 'title');
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent form submission on Enter key
                        onSubmitJobTitle(jobTitle);
                      }
                    }}
                    className="search-input restyle-search-input"
                    placeholder="Add job titles..."
                    enterButton={
                      jobTitleLoading ? (
                        <Spin size="small" className="h-4 w-4 text-cyan-500" />
                      ) : (
                        <Plus
                          onClick={(e) => {
                            e.preventDefault();
                            onSubmitJobTitle(jobTitle);
                          }}
                          className="h-4 w-4 text-cyan-500 hover:text-cyan-600 font-semibold"
                        />
                      )
                    }
                  />
                  {/* Job Title Suggestions Dropdown */}
                  {showJobTitleSuggestions && (
                    <div
                      ref={jobTitleSuggestionsRef}
                      className="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-48 overflow-y-auto"
                    >
                      <div className="p-2 border-b border-gray-100 bg-gray-50">
                        <p className="text-xs text-gray-500 font-medium">
                          Related job titles - click to add:
                        </p>
                      </div>
                      <div className="p-2">
                        <div className="flex flex-wrap gap-1.5">
                          {jobTitleOtions.map((suggestion, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() =>
                                onSubmitJobTitle(suggestion?.value)
                              }
                              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                            >
                              {suggestion?.label}
                              <Plus className="h-3 w-3 ml-1" />
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Selected Job Title*/}
                  {watch('jobTitles')?.length > 0 && (
                    <div className="mt-2 w-full max-h-48 overflow-y-auto">
                      <div className="flex flex-wrap gap-1.5">
                        {watch('jobTitles')?.map((jobTitle) => (
                          <button
                            key={jobTitle?.value}
                            type="button"
                            onClick={() => {
                              const currentJobTitles = [
                                ...(getValues('jobTitles') || []),
                              ];
                              const updatedJobTitles = currentJobTitles.filter(
                                (item) => item.value !== jobTitle.value
                              );
                              setValue('jobTitles', updatedJobTitles);
                            }}
                            className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                          >
                            {jobTitle?.value}
                            <X className="h-3 w-3 ml-1" />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Form.Item>
              <Form.Item label="Keywords" name="keywords">
                <div className="relative">
                  <Input.Search
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    value={keyword}
                    onChange={(e) => {
                      const text = e.target.value;
                      setKeyword(text);
                      debouncedFetchSuggestionList(text, 'keywords');
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent form submission on Enter key
                        onSubmitKeyword(keyword);
                      }
                    }}
                    className="search-input restyle-search-input"
                    placeholder="Add keywords..."
                    enterButton={
                      keywordLoading ? (
                        <Spin size="small" className="h-4 w-4 text-cyan-500" />
                      ) : (
                        <Plus
                          onClick={(e) => {
                            e.preventDefault();
                            onSubmitKeyword(keyword);
                          }}
                          className="h-4 w-4 text-cyan-500 hover:text-cyan-600 font-semibold"
                        />
                      )
                    }
                  />
                  {/* Job Title Suggestions Dropdown */}
                  {showKeywordSuggestions && (
                    <div
                      ref={keywordSuggestionsRef}
                      className="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-48 overflow-y-auto"
                    >
                      <div className="p-2 border-b border-gray-100 bg-gray-50">
                        <p className="text-xs text-gray-500 font-medium">
                          Related keywords - click to add:
                        </p>
                      </div>
                      <div className="p-2">
                        <div className="flex flex-wrap gap-1.5">
                          {keywordsOptions.map((keyword, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() => onSubmitKeyword(keyword?.value)}
                              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                            >
                              {keyword?.label}
                              <Plus className="h-3 w-3 ml-1" />
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Selected keywords */}
                  {watch('keywords')?.length > 0 && (
                    <div className="mt-2 w-full max-h-48 overflow-y-auto">
                      <div className="flex flex-wrap gap-1.5">
                        {watch('keywords')?.map((keyword) => (
                          <button
                            key={keyword?.value}
                            type="button"
                            onClick={() => {
                              const currentJobTitles = [
                                ...(getValues('keywords') || []),
                              ];
                              const updatedJobTitles = currentJobTitles.filter(
                                (item) => item.value !== keyword.value
                              );
                              setValue('keywords', updatedJobTitles);
                            }}
                            className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-cyan-50 text-cyan-700 hover:bg-cyan-100 border border-cyan-200 transition-colors cursor-pointer"
                          >
                            {keyword?.value}
                            <X className="h-3 w-3 ml-1" />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Form.Item>
              <div className="grid grid-cols-2 gap-3">
                <Form.Item
                  label={
                    <p>
                      Countries <span className="text-red-600">*</span>
                    </p>
                  }
                  name="country"
                >
                  <Controller
                    name="country"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        showSearch
                        optionFilterProp="label"
                        required
                        className="search-input"
                        options={tempUkCounties.map((ao) => ({
                          label: ao.label,
                          value: ao.label,
                          code: ao.value,
                        }))}
                        // onSearch={(e) => handleGetListCitiesFromBH(e)}
                        defaultValue={'United Kingdom'}
                        placeholder="Type to search country"
                        onSelect={async (selected, record) => {
                          await handleGetListCitiesStateFromBH('', record.code);
                          setValue('city', '');
                          setValue('adminLevelOneArea', []);
                          setValue('country', selected);
                          setValue('countryCode', record.code);
                        }}
                      ></Select>
                    )}
                  />
                </Form.Item>
                {/* <Form.Item label="State/ County" name="adminLevelOneArea">
             <Controller
               name="adminLevelOneArea"
               control={control}
               render={({ field }) => (
                 <Select
                   {...field}
                   className="search-input"
                   mode="tags"
                   options={listCities
                     .filter(
                       (item, index, self) =>
                         self.findIndex((t) => t.value === item.value) ===
                         index
                     )
                     .map((ao) => ({
                       label: ao.label,
                       value: ao.value,
                       adminName1: ao,
                     }))}
                   onSearch={(e) =>
                     handleInputCity(e, getValues('countryCode') ?? 2359)
                   }
                   placeholder="State/County"
                   onSelect={async (selected, record) => {
                     setValue('city', selected);
                     setValue('adminLevelOneArea', selected);
                  }}
                 >
                   <Input addonAfter={loadingCity && <Spin />} />
                 </Select>
               )}
             />
           </Form.Item> */}
                <Form.Item label="State/ County" name="adminLevelOneArea">
                  <Controller
                    name="adminLevelOneArea"
                    control={control}
                    render={({ field }) => (
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-cyan' })
                        }
                        {...field}
                        mode="multiple"
                        className="search-input"
                        placeholder="eg., London"
                        //  onSearch={(e) =>
                        //    handleInputCity(e, getValues('countryCode') ?? 2359)
                        //  }
                        options={listCities
                          .filter(
                            (item, index, self) =>
                              self.findIndex((t) => t.value === item.value) ===
                              index
                          )
                          .map((ao) => ({
                            label: ao.label,
                            value: ao.value,
                            adminName1: ao,
                          }))}
                        loading={countyLoading}
                        disabled={countyLoading}
                      ></Select>
                    )}
                  />
                </Form.Item>
              </div>
              <div className="pb-6">
                <div className="font-medium text-gray-700">Date posted</div>
                <div
                  className="w-full grid-cols-10 grid"
                  style={{ marginTop: '12px' }}
                >
                  <Dropdown
                    className="w-full "
                    trigger={['click']}
                    menu={{
                      items: [
                        {
                          key: 'last_1_hour',
                          label: 'Last 1 hour',
                        },
                        {
                          key: 'last_one_days',
                          label: 'Last 1 day',
                        },
                        {
                          key: 'last_seven_days',
                          label: 'Last 7 days',
                        },
                        {
                          key: 'last_fourteen_days',
                          label: 'Last 14 days',
                        },
                        {
                          key: 'last_one_month',
                          label: 'Last one month',
                        },
                      ],
                      onClick: ({ key }) => {
                        setValue('datePosted', key);
                        switch (key) {
                          case 'last_1_hour':
                            // setSearchParams({
                            //   ...filter,
                            //   postedStartDate: dayjs().subtract(1, 'day'),
                            //   postedEndDate: dayjs(),
                            //   datePostedText: 'Last 1 hour',
                            // });
                            setDayLoad('Last 1 hour');
                            setValue('datePostedText', 'Last 1 hour');
                            // handleFormSubmit({
                            //   datePostedText: 'Last 1 hour',
                            //   postedWithin: [
                            //     dayjs().subtract(1, 'hour'),
                            //     dayjs(),
                            //   ],
                            // });
                            break;
                          case 'last_one_days':
                            // setSearchParams({
                            //   ...filter,
                            //   postedStartDate: dayjs().subtract(1, 'day'),
                            //   postedEndDate: dayjs(),
                            //   datePostedText: 'Last 1 day',
                            // });
                            setDayLoad('Last 1 day');
                            setValue('datePostedText', 'Last 1 day');
                            // handleFormSubmit({
                            //   datePostedText: 'Last 1 day',
                            //   postedWithin: [dayjs().subtract(1, 'day'), dayjs()],
                            // });
                            break;
                          case 'last_seven_days':
                            // setSearchParams({
                            //   ...filter,
                            //   postedStartDate: dayjs().subtract(6, 'day'),
                            //   postedEndDate: dayjs(),
                            //   datePostedText: 'Last 7 days',
                            // });
                            setDayLoad('Last 7 days');
                            setValue('datePostedText', 'Last 7 days');
                            // handleFormSubmit({
                            //   datePostedText: 'Last 7 days',
                            //   postedWithin: [dayjs().subtract(6, 'day'), dayjs()],
                            // });
                            break;
                          case 'last_fourteen_days':
                            // setSearchParams({
                            //   ...filter,
                            //   postedStartDate: dayjs().subtract(13, 'day'),
                            //   postedEndDate: dayjs(),
                            //   datePostedText: 'Last 14 days',
                            // });
                            setDayLoad('Last 14 days');
                            setValue('datePostedText', 'Last 14 days');
                            // handleFormSubmit({
                            //   datePostedText: 'Last 14 days',
                            //   postedWithin: [
                            //     dayjs().subtract(13, 'day'),
                            //     dayjs(),
                            //   ],
                            // });
                            break;
                          case 'last_one_month':
                            // setSearchParams({
                            //   ...filter,
                            //   postedStartDate: dayjs().subtract(1, 'month'),
                            //   postedEndDate: dayjs(),
                            //   datePostedText: 'Last one month',
                            // });
                            setDayLoad('Last one month');
                            setValue('datePostedText', 'Last one month');
                            // handleFormSubmit({
                            //   datePostedText: 'Last one month',
                            //   postedWithin: [
                            //     dayjs().subtract(1, 'month'),
                            //     dayjs(),
                            //   ],
                            // });
                            break;
                          default:
                            break;
                        }
                      },
                    }}
                  >
                    {getValues('datePostedText') === 'Date posted' ||
                    dayLoad === 'Date posted' ? (
                      <Button className="col-span-10">Date posted</Button>
                    ) : (
                      <Button className="col-span-7">
                        {getValues('datePostedText')}
                      </Button>
                    )}
                  </Dropdown>
                  {(getValues('datePostedText') || dayLoad) !==
                    'Date posted' && (
                    <Button
                      className="col-span-3"
                      style={{
                        transform: 'translateY(0px)',
                        marginLeft: '10px',
                      }}
                      onClick={() => {
                        // setSearchParams({
                        //   ...filter,
                        //   datePostedText: 'Date posted',
                        //   postedStartDate: '',
                        //   postedEndDate: '',
                        // });
                        setValue('datePosted', null);
                        setDayLoad('Date posted');
                        setValue('datePostedText', 'Date posted');
                        // setValue('postedWithin', [null, null]);
                        // handleFormSubmit({
                        //   datePostedText: 'Date posted',
                        //   postedWithin: null,
                        // });
                      }}
                    >
                      X
                    </Button>
                  )}
                </div>
              </div>
              <Form.Item label="Posted within" name="postedWithin">
                <Controller
                  name="postedWithin"
                  control={control}
                  render={({ field }) => <RangePicker {...field} />}
                />
              </Form.Item>
            </div>
            <Form.Item className="p-4 border-t border-gray-200 bg-gray-50 h-full">
              <Button
                loading={isLoadingSearchDrawer}
                htmlType="submit"
                className="w-full bg-cyan-500 hover:bg-cyan-600 text-white h-10"
              >
                <span className="font-medium">Submit</span>
              </Button>
            </Form.Item>
          </div>
        )}
      </Form>
      <Modal
        centered
        width={500}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Find Location"
        open={modalOpen}
        okText={'Confirm'}
        okButtonProps={{
          loading: confirmLoading,
        }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        We can not find a county for your input location.
        <br />
        Do you want to use city ({getValues().city}) instead?
      </Modal>
    </>
  );
};

export default NewSearchFormComponent;
