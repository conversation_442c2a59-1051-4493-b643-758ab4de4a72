/* eslint-disable react/prop-types */
import { Button, Form, Select } from 'antd';
import { Controller } from 'react-hook-form';

import React from 'react';

const NewSyncFormComponent = ({
  searches,
  onFinish,
  control,
  isLoadingSyncDrawer,
}) => {
  return (
    <Form layout="vertical" onFinish={onFinish}>
      <Form.Item label="Select searches" name="searchIds">
        <Controller
          name="searchIds"
          control={control}
          render={({ field }) => (
            <Select
              labelInValue
              mode="multiple"
              {...field}
              options={searches.map((val) => ({
                label: val.searchName,
                value: val.searchName,
                key: val.id,
              }))}
              filterOption={(inputValue, option) =>
                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) !==
                -1
              }
            />
          )}
        />
      </Form.Item>
      <Form.Item>
        <Button
          disabled={isLoadingSyncDrawer}
          htmlType="submit"
          type="primary"
          className={`w-full ${isLoadingSyncDrawer ? 'bg-gray-500' : 'bg-blue-500'}`}
        >
          <span>
            Submit
          </span>
        </Button>
      </Form.Item>
    </Form>
  );
};

export default NewSyncFormComponent;
