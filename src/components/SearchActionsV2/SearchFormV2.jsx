/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import {
  Drawer,
  Button,
  Space,
  Dropdown,
  notification,
  Input,
  Modal,
} from 'antd';
import { useForm } from 'react-hook-form';
import { deleteBulkSearches, postSearch } from '../../services/search';
import NewSearchFormComponent from './NewSearchFormComponent';
import {
  AppstoreAddOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  FolderAddOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { postSync } from '../../services/searchSync';
import NewSyncFormComponent from './NewSyncFormComponent';
import { useNavigate } from 'react-router-dom';
import { updateKeywordJobSearch } from '../../services/getJson';
import NewCloneFormComponent from './NewCloneFormComponent';
import useSearches from '../../hooks/useSearches';
import { X } from 'lucide-react';
import styled from 'styled-components';

const { Search } = Input;

export const RestyleDrawer = styled(Drawer)`
  .ant-drawer-body {
    padding: 0;
  }
`;

const useFormDrawer = (submitToAPIMethod) => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const handleDrawerOpen = () => {
    setDrawerVisible(true);
  };

  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  const onSubmit = async (payload) => {
    setIsLoading(true);
    try {
      const { data } = await submitToAPIMethod(payload);
      if (data?.result?.id) {
        setIsLoading(false);
        if (payload?.keywords)
          await updateKeywordJobSearch(payload?.keywords[0]);
        navigate(`${data?.result?.id}`);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      notification.error({ message: error?.response?.data?.message });
    }
    handleDrawerClose();
  };
  return {
    drawerVisible,
    handleDrawerOpen,
    handleDrawerClose,
    onSubmit,
    isLoading,
  };
};

const SearchActionsV2 = ({
  searches,
  handleSearch,
  loadingSearch = false,
  setSelectedRowKeys,
  selectedRowKeys,
  populateSearchData,
}) => {
  const [cloneValue, setCloneValue] = useState();
  const {
    handleSubmit: handleSubmitSearch,
    control: controlSearch,
    setValue,
    getValues,
    watch,
  } = useForm();
  const { handleSubmit: handleSubmitSync, control: controlSync } = useForm();
  const {
    drawerVisible: searchDrawerVisible,
    handleDrawerOpen: handleSearchDrawerOpen,
    handleDrawerClose: handleSearchDrawerClose,
    onSubmit: onSubmitSearchDrawer,
    isLoading: isLoadingSearchDrawer,
  } = useFormDrawer(postSearch);
  const {
    drawerVisible: syncDrawerVisible,
    handleDrawerOpen: handleSyncDrawerOpen,
    handleDrawerClose: handleSyncDrawerClose,
    onSubmit: onSubmitSyncDrawer,
    isLoading: isLoadingSyncDrawer,
  } = useFormDrawer(postSync);
  const {
    drawerVisible: cloneDrawlerVisible,
    handleDrawerOpen: handleCloneDrawerOpen,
    handleDrawerClose: handleCloneDrawerClose,
    // eslint-disable-next-line no-unused-vars
    onSubmit: onSubmitCloneDrawer,
    isLoading: isLoadingCloneDrawer,
  } = useFormDrawer(postSearch);

  const onSearch = (value, _e, info) => console.log(info?.source, value);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [loadingDeleteSearch, setLoadingDeleteSearch] = useState(false);

  const handleDeleteBulkSearches = async () => {
    try {
      setLoadingDeleteSearch(true);
      const { data } = await deleteBulkSearches({
        searchIds: selectedRowKeys,
      });
      if (data) {
        notification.success({ message: 'Delete Searches Success' });
        populateSearchData();
        setDeleteModalOpen(false);
        setLoadingDeleteSearch(false);
        setSelectedRowKeys([]);
      }
    } catch (e) {
      notification.error({ message: e?.response?.data?.message });
      setLoadingDeleteSearch(false);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
  };

  return (
    <>
      <div className="flex justify-between">
        <div className="flex gap-2">
          <Dropdown
            className="mb-4"
            placement="bottom"
            arrow
            menu={{
              items: [
                {
                  key: '1',
                  label: (
                    <a
                      className="Montserrat flex gap-2 items-center py-2"
                      onClick={(e) => {
                        e.preventDefault();
                        handleSearchDrawerOpen();
                      }}
                    >
                      <PlusOutlined />
                      <span>Create New Search</span>
                    </a>
                  ),
                },
                // {
                //   key: '2',
                //   label: (
                //     <a
                //       className="Montserrat flex gap-2 items-center py-2"
                //       onClick={(e) => {
                //         e.preventDefault();
                //         handleSyncDrawerOpen();
                //       }}
                //     >
                //       <AppstoreAddOutlined />
                //       <span>Add Searches to New Sync</span>
                //     </a>
                //   ),
                // },
                {
                  key: '3',
                  label: (
                    <a
                      className="Montserrat flex gap-2 items-center py-2"
                      onClick={(e) => {
                        e.preventDefault();
                        handleCloneDrawerOpen();
                      }}
                    >
                      <CopyOutlined />
                      <span>Clone Search</span>
                    </a>
                  ),
                },
              ],
            }}
          >
            <Space>
              <Button
                type="primary"
                className="!border-[#b2b8be] flex gap-2 items-center "
              >
                <p className="Montserrat">Create New Search</p>
                <DownOutlined />
              </Button>
            </Space>
          </Dropdown>

          {selectedRowKeys?.length > 0 && (
            <div>
              <Dropdown
                className="mb-4 animated fadeInDownBig"
                placement="bottom"
                arrow
                menu={{
                  items: [
                    {
                      key: 'delete-searchs',
                      label: (
                        <a
                          className="Montserrat flex gap-2 items-center py-2"
                          onClick={(e) => {
                            e.preventDefault();
                            setDeleteModalOpen(true);
                          }}
                        >
                          <DeleteOutlined />
                          <span>Bulk Delete</span>
                        </a>
                      ),
                    },
                    // {
                    //   key: 'clone-searchs',
                    //   label: (
                    //     <a
                    //       className="Montserrat flex gap-2 items-center py-2"
                    //       onClick={(e) => {
                    //         e.preventDefault();
                    //         handleCloneDrawerOpen();
                    //       }}
                    //     >
                    //       <CopyOutlined />
                    //       <span>Clone Search</span>
                    //     </a>
                    //   ),
                    // },
                  ],
                }}
              >
                <Space>
                  <Button
                    type="primary"
                    className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                  >
                    <p className="Montserrat">
                      {`${selectedRowKeys?.length} Selected`}
                    </p>
                    <DownOutlined />
                  </Button>
                </Space>
              </Dropdown>
            </div>
          )}
        </div>
        <div>
          <Search
            className="customize-search-container"
            allowClear
            placeholder="Search anything..."
            enterButton="Search"
            size="middle"
            loading={loadingSearch}
            onSearch={handleSearch}
          />
        </div>
      </div>
      <div className={'drawer-container Montserrat'}>
        {searchDrawerVisible && (
          <RestyleDrawer
            title={
              <div className="flex items-center justify-between bg-white">
                <h2 className="text-lg font-medium text-gray-900">
                  Create Search
                </h2>
                <button
                  onClick={handleSearchDrawerClose}
                  className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            }
            width={400}
            // onClose={handleSearchDrawerClose}
            closable={false}
            destroyOnClose={true}
            visible={searchDrawerVisible}
          >
            <NewSearchFormComponent
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              isLoadingSearchDrawer={isLoadingSearchDrawer}
              searchDrawerVisible={searchDrawerVisible}
              onFinish={handleSubmitSearch(async (data) => {
                if (
                  (data?.keywords?.length == 0 &&
                    data?.jobTitles?.length == 0) ||
                  (!data?.keywords && !data?.jobTitles)
                ) {
                  notification.error({
                    message: 'Please select at least 1 keyword or 1 job title',
                  });
                  return;
                }
                if (!data?.country) {
                  return notification?.warning({
                    description: 'Countries is required!',
                  });
                }
                const payload = {
                  ...data,
                  adminLevelOneArea: data?.adminLevelOneArea?.join(','),
                  keywords: data?.keywords?.map(
                    (keyword) => keyword?.value || keyword
                  ),
                  jobTitles: data?.jobTitles?.map(
                    (jobTitle) => jobTitle?.value || jobTitle
                  ),
                };
                await onSubmitSearchDrawer(payload);
              })}
              control={controlSearch}
            />
          </RestyleDrawer>
        )}
      </div>
      <div className={'drawer-container'}>
        <Drawer
          title="Add new sync"
          width={600}
          height={400}
          onClose={handleSyncDrawerClose}
          visible={syncDrawerVisible}
        >
          <NewSyncFormComponent
            isLoadingSyncDrawer={isLoadingSyncDrawer}
            searches={searches}
            onFinish={handleSubmitSync(async (data) => {
              if (
                (data?.keywords?.length == 0 && data?.jobTitles?.length == 0) ||
                (!data?.keywords && !data?.jobTitles)
              ) {
                notification.error({
                  message: 'Please select at least 1 keyword or 1 job title',
                });
                return;
              }
              let payload = { ...data };
              payload.searchIds = (payload.searchIds || []).map((a) => a?.key);
              await onSubmitSyncDrawer(payload);
            })}
            control={controlSync}
          />
        </Drawer>

        <div className={'drawer-container'}>
          <RestyleDrawer
            title={
              <div className="flex items-center justify-between bg-white">
                <h2 className="text-lg font-medium text-gray-900">
                  Clone Search
                </h2>
                <button
                  onClick={() => {
                    handleCloneDrawerClose();
                    setCloneValue(null);
                  }}
                  className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            }
            width={400}
            // onClose={handleSearchDrawerClose}
            closable={false}
            destroyOnClose={true}
            visible={cloneDrawlerVisible}
          >
            <NewCloneFormComponent
              setCloneValue={setCloneValue}
              cloneValue={cloneValue}
              isLoadingSyncDrawer={isLoadingCloneDrawer}
              openDrawler={cloneDrawlerVisible}
              searches={searches}
              fromCloneTab={true}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              onFinish={handleSubmitSearch(async (data) => {
                if (
                  (data?.keywords?.length == 0 &&
                    data?.jobTitles?.length == 0) ||
                  (!data?.keywords && !data?.jobTitles)
                ) {
                  notification.error({
                    message: 'Please select at least 1 keyword or 1 job title',
                  });
                  return;
                }
                const payload = {
                  ...data,
                  adminLevelOneArea: data?.adminLevelOneArea?.join(','),
                  keywords: data?.keywords?.map((keyword) => keyword?.value),
                  jobTitles: data?.jobTitles?.map(
                    (jobTitle) => jobTitle?.value || jobTitle
                  ),
                };
                delete payload?.searchIds;
                await onSubmitSearchDrawer(payload);
              })}
              control={controlSearch}
              handleSubmit={handleSubmitSearch}
            />
          </RestyleDrawer>
        </div>
      </div>
      <Modal
        centered
        width={1000}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Delete job search"
        open={deleteModalOpen}
        okText={'Confirm'}
        okButtonProps={{
          loading: loadingDeleteSearch,
        }}
        onOk={handleDeleteBulkSearches}
        onCancel={handleCancelDelete}
      >
        Are you sure to delete Searches?
      </Modal>
    </>
  );
};

export default SearchActionsV2;
