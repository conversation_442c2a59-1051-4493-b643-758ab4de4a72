import React from 'react';
import { Grid, Image } from 'antd';
import logo from '../../assets/img/welcome/logo.png';
import interview from '../../assets/img/welcome/interview.png';
const { useBreakpoint } = Grid;

export function WelcomeBanner() {
  const screens = useBreakpoint();
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: screens.md ? '100vh' : '15vh',
      }}
    >
      <div className="welcomeBanner" style={{ textAlign: 'center' }}>
        <Image src={logo} preview={false} width={300} />
        {screens.md && <Image src={interview} preview={false} width={600} />}
      </div>
    </div>
  );
}
