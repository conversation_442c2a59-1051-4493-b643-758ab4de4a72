/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { Form, Input, Button, Typography } from 'antd';
import { Controller, useForm } from 'react-hook-form';
import Title from 'antd/lib/typography/Title';
import Paragraph from 'antd/lib/typography/Paragraph';
import { getUserByEmail } from '../../services/auth';
import { useQuery } from '@tanstack/react-query';

const RegistrationForm = ({ onSubmit }) => {
  const { handleSubmit, control, errors, setValue, getValues } = useForm();
  const params = new URLSearchParams(window.location.search); // id=123
  useEffect(() => {
    setValue('email', params.get('invitedEmail'));
  }, []);

  useQuery(['USER_DETAIL_BY_EMAIL'], {
    queryFn: async () => {
      const { data } = await getUserByEmail(params.get('invitedEmail'));
      setValue('organization', data?.result?.organization?.name ?? ''); // set org
    },
  });

  const onFinish = (values) => {
    onSubmit(values);
  };
  return (
    <div
      style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Form onFinish={handleSubmit(onFinish)} style={{ width: '70%' }}>
        <Typography>
          <Title>Register</Title>
        </Typography>
        <Form.Item
          label="Email"
          name="email"
          labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
          wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
        >
          <Controller
            render={({ field }) => (
              <Input {...field} disabled={params.get('invitedEmail')} />
            )}
            name="email"
            control={control}
          />
        </Form.Item>

        <Form.Item
          label="Full Name"
          name="fullName"
          labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
          wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
        >
          <Controller
            render={({ field }) => (
              <Input {...field} />
            )}
            name="fullName"
            control={control}
          />
        </Form.Item>

        <Form.Item
          label="Username"
          name="username"
          labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
          wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
        >
          <Controller
            render={({ field }) => <Input {...field} />}
            name="username"
            control={control}
          />
        </Form.Item>

        <Form.Item
          label="Password"
          name="password"
          labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
          wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
        >
          <Controller
            render={({ field }) => <Input.Password {...field} />}
            name="password"
            control={control}
          />
        </Form.Item>

        <Form.Item
          label="Confirm Password"
          name="confirmPassword"
          labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
          wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
          rules={[
            {
              validate: (value) =>
                value === getValues().password || 'Passwords do not match',
            },
          ]}
          validateStatus={errors?.confirmPassword ? 'error' : ''}
          help={errors?.confirmPassword?.message}
        >
          <Controller
            render={({ field }) => <Input.Password {...field} />}
            name="confirmPassword"
            control={control}
          />
        </Form.Item>

        {params.get('invitedEmail') && getValues()?.organization !== '' ? (
          <Form.Item
            label="Organization"
            name="organization"
            labelCol={{ span: 24 }} // Set labelCol span to 24 to display the label on top
            wrapperCol={{ span: 24 }} // Set wrapperCol span to 24 to match the label
          >
            <Controller
              render={({ field }) => <Input {...field} disabled />}
              name="organization"
              control={control}
            />
          </Form.Item>
        ) : (
          ''
        )}

        <Form.Item>
          <Button type="primary" htmlType="submit">
            Register
          </Button>
          <Typography>
            <Paragraph>
              Already have an account? <a href={'/login'}>Login</a>
            </Paragraph>
          </Typography>
        </Form.Item>
      </Form>
    </div>
  );
};

export default RegistrationForm;
