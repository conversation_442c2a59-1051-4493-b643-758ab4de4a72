import React from 'react';
import { useEffect, useState } from 'react';

import './style.css';
import { Link, useSearchParams } from 'react-router-dom';
import success from '../../assets/img/success.png';
import fail from '../../assets/svg/fail-19.svg';
import SplashScreen from '../SplashScreen/index';

function UnipileCallback() {
  const [searchParams] = useSearchParams();
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const validate = async () => {
      try {
        setLoading(true);
        const isFailed = !!searchParams.get('failed');

        if (isFailed) {
          setError(true);
          setLoading(false);
          return;
        }

        await (new Promise(r => setTimeout(r, 3000)));
      } catch (error) {
        setError(true);
        setLoading(false);
      }
      setLoading(false);
    };

    validate();
  }, []);

  if (loading) return <SplashScreen />;

  return (
    <div className="unipile-callback">
      <div className="unipile-callback-header">
        {!error ? (
          <>
            <img src={success} className="unipile-callback-logo" alt="logo" />
            <b>YOU ARE DONE</b>
            <p>Now you can send sequence email</p>
            {window.opener ? (
              <Link
                onClick={() => window.close()}
                to="#"
                style={{ color: '#0891b2' }}
              >
                {' '}
                Close this window
              </Link>
            ) : (
              <Link to="/" style={{ color: '#0891b2' }}>
                {' '}
                Go to Zileo
              </Link>
            )}
          </>
        ) : (
          <>
            <img src={fail} className="unipile-callback-logo" alt="logo" />
            <b>OOPS!</b>
            <p>Some thing went wrong!</p>
            {window.opener ? (
              <Link
                onClick={() => window.close()}
                to="#"
                style={{ color: '#0891b2' }}
              >
                {' '}
                Close this window
              </Link>
            ) : (
              <Link to="/" style={{ color: '#0891b2' }}>
                {' '}
                Go to Zileo
              </Link>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default UnipileCallback;
