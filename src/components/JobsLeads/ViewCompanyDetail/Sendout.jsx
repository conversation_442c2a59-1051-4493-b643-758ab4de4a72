import { useEffect, useState } from 'react';
import Card from './Card';
import { searchBullhorn, searchBullhornData } from '../../../services/bullhorn';
import {
  Button,
  Drawer,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  DownOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';

const Sendout = (props) => {
  const { company, vacancyId, vacancy , totalShortList} = props;

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [openModalCreate, setOpenModalCreate] = useState(false);

  const handleGetContacts = async (count, start) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'Sendout',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      company?.id,
      null,
      null,
      null,
      false
    );

    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
  }, [vacancyId]);

  const columns = [
    // {
    //   title: '',
    //   dataIndex: '',
    //   key: '',
    //   defaultSortOrder: 'descend',
    //   render: (_, record) => {
    //     return (
    //       <div className="flex flex-col gap-2" style={{ width: '200px' }}>
    //         <Dropdown
    //           menu={{
    //             items: [
    //               {
    //                 label: <div>Edit</div>,
    //                 key: '0',
    //               },
    //               {
    //                 label: <div>Delete</div>,
    //                 key: '1',
    //               },
    //             ],
    //           }}
    //           trigger={['click']}
    //         >
    //           <a onClick={(e) => e.preventDefault()}>
    //             <Space>
    //               Action
    //               <DownOutlined />
    //             </Space>
    //           </a>
    //         </Dropdown>
    //       </div>
    //     );
    //   },
    // },
    {
      title: 'Candidate',
      dataIndex: 'candidate',
      key: 'candidate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.candidate?.firstName} {record?.candidate?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Date Sent',
      dataIndex: 'Date Added',
      key: 'Date Added',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px' }}>
            <div>
              {record?.dateAdded
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Sender',
      dataIndex: 'Sender',
      key: 'Sender',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.user?.firstName} {record?.user?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Contact',
      dataIndex: 'Contact',
      key: 'Contact',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.clientContact?.firstName} {record?.clientContact?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Email',
      dataIndex: 'Email',
      key: 'Email',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
          <div>
            {record?.email}
          </div>
        </div>
        );
      },
    },
    {
      title: 'Num Times Read',
      dataIndex: 'Num Times Read',
      key: 'Num Times Read',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
          <div>
            {record?.numTimesRead}
          </div>
        </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetContacts(
                          e,
                          '',
                          consultantId ? 'owner.id' : '',
                          consultantId,
                          consultantId ? 'EQUAL' : '',
                          consultantId ? 'INT' : ''
                        );
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div style={{display: "flex"}}>
                <Pagination
                style={{
                  marginLeft: "10px"
                }}
                total={totalShortList}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
            />
              </div>
            </div>
            <div className="company-view-notes">
              <Table
                loading={loadingContact}
                // showHeader={false}
                scroll={{
                  x: 1000,
                }}
                dataSource={listContact}
                columns={columns}
                pagination={false}
              />
            </div>
          </div>
        </div>
      </div>
        {/* <ModalAddCv openModal={openModalCreate} setOpenModal={setOpenModalCreate} handleGetContacts={handleGetContacts} vacancy={vacancy}/> */}
    </div>
  );
};

export default Sendout;