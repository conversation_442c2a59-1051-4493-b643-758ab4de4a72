import { useEffect, useState } from 'react';
import Card from './Card';
import {
  getDataTotal,
  searchBullhorn,
  searchBullhornData,
} from '../../../services/bullhorn';
import {
  Button,
  Input,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  CloseCircleOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import useInfiniteScrollWithSearch from '../../../hooks/useInfinitiveScroll';
import { isNull } from 'lodash';

const ContactTab = (props) => {
  const { company, companyId, totalContact, setChooseTab, setDefaultCollapse } =
    props;

  const [currentTotal, setCurrentTotal] = useState(null);


  const handleGetTotal = async (
    customSearchField,
    customSearchValue,
    optionCustom,
    typeCustom
  ) => {
    try {
      const { data } = await getDataTotal(
        'ClientContact',
        companyId,
        customSearchField,
        customSearchValue,
        optionCustom,
        typeCustom
      );
      setCurrentTotal(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  console.log(currentTotal);

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const handleGetContacts = async (
    count,
    start,
    customField,
    customFieldValue,
    option,
    type
  ) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'ClientContact',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      companyId,
      null,
      null,
      null,
      false,
      customField,
      customFieldValue,
      option,
      type
    );
    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const {
    options: contactSecondaryOwner,
    handlePopupScroll: handleContactSecondaryOwnerScroll,
    handleSearch: handleContactSecondaryOwnerSearch,
    isLoading: isLoadingContactSecondaryOwner,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    // populateReportedAgencies(page, pageSize);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
    // handleGetConsultant()
    handleContactSecondaryOwnerSearch(' ', null, 100);
  }, [companyId]);

  const getColumnSearchProps = (dataIndex, option, type) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setCurrentPage(1);
              handleGetContacts(
                numberPage,
                '',
                dataIndex,
                selectedKeys,
                option,
                type
              );
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            Search
          </Button>
          <Button
            onClick={() => setSelectedKeys(null)}
            size="small"
            style={{
              width: 90,
            }}
          >
            Reset
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              close();
            }}
          >
            close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filtered ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        // setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.id - b?.id,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>{record?.id}</div>
          </div>
        );
      },
      ...getColumnSearchProps('id', 'EQUAL', 'INT'),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.name.length - b?.name.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>{record?.name}</div>
          </div>
        );
      },
      ...getColumnSearchProps('name', 'LIKE', 'STRING'),
    },
    {
      title: 'Job Title',
      dataIndex: 'occupation',
      key: 'occupation',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.occupation?.length - b?.occupation?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>{record?.occupation}</div>
          </div>
        );
      },
      ...getColumnSearchProps('occupation', 'LIKE', 'STRING'),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.status?.length - b?.status?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>{record?.status}</div>
          </div>
        );
      },
      ...getColumnSearchProps('status', 'LIKE', 'STRING'),
    },
    {
      title: 'Work Phone',
      dataIndex: 'phone',
      key: 'phone',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.phone?.length - b?.phone?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <a href={`tel:${record?.phone}`}>{record?.phone}</a>
          </div>
        );
      },
      ...getColumnSearchProps('phone', 'EQUAL', 'STRING'),
    },
    {
      title: 'Company',
      dataIndex: 'clientCorporation.name',
      key: 'clientCorporation.name',
      defaultSortOrder: 'descend',
      sorter: (a, b) =>
        a?.clientCorporation?.name?.length - b?.clientCorporation?.name?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{
              width: '200px',
              color: '#4a89dc',
              fontWeight: '600',
              cursor: 'pointer',
            }}
            onClick={() => setChooseTab('overview')}
          >
            <div>{record?.clientCorporation?.name} </div>
          </div>
        );
      },
      ...getColumnSearchProps('clientCorporation.name', 'LIKE', 'STRING'),
    },
    {
      title: 'Consultant',
      dataIndex: 'owners',
      key: 'owners',
      defaultSortOrder: 'descend',
      sorter: (a, b) =>
        a?.owner?.firstName?.length - b?.owner?.lastName?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{
              width: '200px',
              color: '#4a89dc',
              fontWeight: '600',
              cursor: 'pointer',
            }}
          >
            <a>{record?.owner?.firstName + ' ' + record?.owner?.lastName}</a>
          </div>
        );
      },
      ...getColumnSearchProps('owner.firstName', 'EQUAL', 'STRING'),
    },
    {
      title: 'LinkedIn',
      dataIndex: 'linkedIn',
      key: 'linkedIn',
      defaultSortOrder: 'descend',
      sorter: (a, b) =>
        a?.linkedinProfileUrl?.length - b?.linkedinProfileUrl?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <a href={record?.linkedinProfileUrl}>
              <LinkedinOutlined
                style={{ fontSize: '30px', color: '#4a89dc' }}
              />
            </a>
          </div>
        );
      },
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      defaultSortOrder: 'descend',
      sorter: (a, b) =>
        a?.address?.address1?.length - b?.address?.address1?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div className="company-view-truncate">
              {record?.address?.address1}
            </div>
          </div>
        );
      },
      ...getColumnSearchProps('address.city', 'EQUAL', 'STRING'),
    },
    {
      title: 'Last Visit',
      dataIndex: 'lastVisit',
      key: 'lastVisit',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.dateLastVisit?.length - b?.dateLastVisit?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>
              {record?.dateLastVisit
                ? dayjs(record?.dateLastVisit).format('YYYY/MM/DD h:mm a')
                : null}
            </div>
          </div>
        );
      },
      //   ...getColumnSearchProps('dateLastVisit')
    },
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>
              {record?.dateAdded
                ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
                : null}
            </div>
          </div>
        );
      },
      //   ...getColumnSearchProps('dateAdded')
    },
  ];

  return (
    <div className="px-3 grid grid-cols-1 gap-3 pb-5">
      <div style={{ width: '100%' }}>
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            overflow: 'hidden',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                padding: '20px',
                lineHeight: '1.2',
                display: 'flex',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <div>CONSULTANT</div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Select
                    className="min-w-[140px]"
                    style={{
                      marginLeft: '10px',
                    }}
                    rootClassName="company-status-container"
                    onChange={(e) => {
                      setCurrentPage(1);
                      setConsultantId(e);
                      handleGetTotal('owner.id', e, 'EQUAL', 'INT');
                      handleGetContacts(
                        numberPage,
                        '',
                        'owner.id',
                        e,
                        'EQUAL',
                        'INT'
                      );
                    }}
                    value={
                      consultantId || { label: 'ALL USERS', value: 'all_user' }
                    }
                    options={contactSecondaryOwner?.map((item) => {
                      return {
                        label: item?.name,
                        value: item?.id,
                      };
                    })}
                  />
                  {consultantId && (
                    <div
                      onClick={() => {
                        setCurrentPage(1);
                        setConsultantId('');
                        handleGetTotal('owner.id', '', 'EQUAL', 'INT');
                        handleGetContacts(numberPage, '', '', '', '');
                        setCurrentPage(1);
                      }}
                      style={{ marginLeft: '10px', cursor: 'pointer' }}
                    >
                      <CloseCircleOutlined />
                    </div>
                  )}
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginLeft: '30px',
                }}
              >
                <div>ITEMS PER PAGE</div>
                <div>
                  <Select
                    className="min-w-[140px]"
                    style={{
                      marginLeft: '10px',
                    }}
                    defaultValue={'10'}
                    rootClassName="company-status-container"
                    onChange={(e) => {
                      setCurrentPage(1);
                      setNumberPage(e);
                      handleGetContacts(
                        e,
                        '',
                        consultantId ? 'owner.id' : '',
                        consultantId,
                        consultantId ? 'EQUAL' : '',
                        consultantId ? 'INT' : ''
                      );
                    }}
                    options={[
                      {
                        value: '10',
                        label: '10',
                      },
                      {
                        value: '25',
                        label: '25',
                      },
                      {
                        value: '50',
                        label: '50',
                      },
                      {
                        value: '100',
                        label: '100',
                      },
                    ]}
                  />
                </div>
              </div>
            </div>
            <div>
              <Pagination
                total={ !isNull(currentTotal) ? currentTotal : totalContact}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
              />
            </div>
          </div>
          <div className="company-view-contact">
            <Table
              scroll={{
                x: '2000px',
                y: '470px',
              }}
              loading={loadingContact}
              // showHeader={false}
              dataSource={listContact}
              columns={columns}
              pagination={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactTab;
