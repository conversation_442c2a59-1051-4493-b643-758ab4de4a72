import {
  CalendarOutlined,
  CaretRightOutlined,
  CheckSquareOutlined,
  EditOutlined,
  FolderOutlined,
  MailOutlined,
  StarOutlined,
  UnorderedListOutlined,
  UserOutlined,
  WifiOutlined,
} from '@ant-design/icons';
import { faStar } from '@fortawesome/free-regular-svg-icons';
import {
  faEarthOceania,
  faMicrophone,
  faPaperPlane,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Collapse, Spin, notification, theme } from 'antd';
import ShortListItem from '../ShortListItem';
import Sendout from '../Sendout';
import InterviewAppointments from '../InterviewAppointments';
import Placement from '../Placement';
import { getDataTotal } from '../../../../services/bullhorn';
import { useEffect, useState } from 'react';

const ActivityShortList = (props) => {
  const { token } = theme.useToken();
  const [totalCvSent, setTotalCVSent] = useState();
  const [totalAppointment, setTotalAppointment] = useState();
  const [totalPlace, setTotalPlace] = useState();
  const {
    company,
    companyId,
    defaultCollapse,
    setDefaultCollapse,
    totalShortLis,
    chooseCollapse,
    totalShortList,
    setChooseCollapse,
  } = props;

  const handleGetTotalShortList = async () => {
    try {
      const { data } = await getDataTotal('Sendout', company?.id, "", "", "" , "", "Company");

      setTotalCVSent(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Some things went wrong',
      });
    }
  };

  const handleGetTotalAppointment = async () => {
    try {
      const { data } = await getDataTotal('Appointment', company?.id, "", "", "" , "", "Company");

      setTotalAppointment(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Some things went wrong',
      });
    }
  };

  const handleGetTotalPlacement = async () => {
    try {
      const { data } = await getDataTotal('Placement', company?.id, "", "", "" , "", "Company");

      setTotalPlace(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Some things went wrong',
      });
    }
  };

  useEffect(() => {
    handleGetTotalShortList();
    handleGetTotalAppointment();
    handleGetTotalPlacement();
  }, [company]);

  return (
    <div className="custom-collapse-activity">
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        collapsible={totalShortLis <= 0 ? "disabled" : ""}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'shortList',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faStar} />
                  </div>
                  <span className="font-medium">
                    SHORTLIST ({totalShortList})
                  </span>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: (
              <div>
                <ShortListItem
                  company={company}
                  companyId={companyId}
                  totalShortList={totalShortList}
                />
              </div>
            ),
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        collapsible={totalCvSent <= 0 ? "disabled" : ""}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'CV Sent',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faPaperPlane} />
                  </div>
                  <div class="ml-2.5">CV Sent ({totalCvSent})</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: (
              <div>
                <Sendout
                  companyId={companyId}
                  company={company}
                  totalShortList={totalCvSent}
                />
              </div>
            ),
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible={totalAppointment == 0 ? "disabled" : ""}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'Interview',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faMicrophone} />
                  </div>
                  <div class="ml-2.5">Interview Appointments ({totalAppointment})</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: (
              <div>
                <InterviewAppointments
                  company={company}
                  companyId={companyId}
                  totalShortList={totalShortList}
                />
              </div>
            ),
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible={totalPlace == 0 ? "disabled" : ""}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'Placement',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faStar} />
                  </div>
                  <div class="ml-2.5">Placement ({totalPlace})</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: (
              <div>
                <Placement
                  companyId={companyId}
                  company={company}
                  totalShortList={totalShortList}
                />
              </div>
            ),
            showArrow: false,
          },
        ]}
      />
    </div>
  );
};

export default ActivityShortList;
