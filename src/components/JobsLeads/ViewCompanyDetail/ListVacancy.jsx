import { useEffect, useState } from 'react';
import Card from './Card';
import { searchBullhorn, searchBullhornData } from '../../../services/bullhorn';
import { Button, Drawer, Input, Modal, Pagination, Select, Space, Table, notification } from 'antd';
import dayjs from 'dayjs';
import { LinkedinOutlined, SearchOutlined } from '@ant-design/icons';
import ViewVacancyDetail from '../ViewVacancyDetail';

const ListVacancy = (props) => {
  const { company, companyId, totalData } = props;

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [vacancyIdRaw, setVacancyIdRaw] = useState();
  const [currentPage, setCurrentPage] = useState(1);

  const handleGetContacts = async (
    count,
    start,
    customField,
    customFieldValue,
    option,
    type
  ) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'JobOrder',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      companyId,
      null,
      null,
      null,
      false,
      customField,
      customFieldValue,
      option,
      type
    );
    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page)
    handleGetContacts(numberPage, page)
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
  }, [companyId]);


  const getColumnSearchProps = (dataIndex, option, type) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() =>{
              setCurrentPage(1)
              handleGetContacts(
                numberPage,
                '',
                dataIndex,
                selectedKeys,
                option,
                type
              )
              }
            }
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            Search
          </Button>
          <Button
            onClick={() => setSelectedKeys(null)}
            size="small"
            style={{
              width: 90,
            }}
          >
            Reset
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              close();
            }}
          >
            close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filtered ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        // setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });


  const [openVacancyViewer, setOpenVacancyViewer] = useState(false);
  const [vacancyDetail, setVacancyDetail] = useState(null);
  const [vacancyDetailLoading, setVacancyDetailLoading] = useState(false);

  const showVacancyDrawer = () => setOpenVacancyViewer(true);
  const closeVacancyDrawer = () => setOpenVacancyViewer(false);

  const getVacancyDetail = async (vacancyId) => {
    if (!vacancyId) return;
    setVacancyIdRaw(vacancyId)
    setVacancyDetailLoading(true);
    showVacancyDrawer();
    try {
      // const res = await getBHCompanyDetail(companyIdRaw);
      const { data } = await searchBullhorn(
        'JobOrder',
        0,
        1,
        '',
        '',
        vacancyId,
        ''
      );
      if (data?.result?.length > 0) {
        const { result } = data;
        setVacancyDetail({ ...result[0] });
      } else {
        notification.warning({
          description: 'Vacancy might be deleted by another user.',
        });
      }

      setVacancyDetailLoading(false);
    } catch (error) {
      setVacancyDetailLoading(false);
      notification.error({
        description: 'Network Error! Please try again later.',
      });
    }
  };


  const columns = [
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px' }}
          >
             {record?.dateAdded
                ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
                : null}
          </div>
        );
      },
    },
    {
      title: 'Job Title',
      dataIndex: 'title',
      key: 'title',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.title.length - b?.title.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600', cursor: "pointer" }}
            onClick={() => getVacancyDetail(record?.id)}
          >
            <div>{record?.title}</div>
          </div>
        );
      },
    //   ...getColumnSearchProps('name', 'LIKE', 'STRING'),
    },
    {
      title: 'Consultant',
      dataIndex: 'firstName',
      key: 'firstName',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.owner?.firstName?.length - b?.owner?.firstName?.length,
      render: (_, record) => {
        return (
            <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>
              {record?.owner?.firstName + ' ' + record?.owner?.lastName}
            </div>
          </div>
        );
      },
    //   ...getColumnSearchProps('occupation', 'LIKE', 'STRING'),
    },
    {
      title: 'Contact',
      dataIndex: 'status',
      key: 'status',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.clientContact?.firstName?.length - b?.clientContact?.firstName?.length,
      render: (_, record) => {
        return (
            <div
            className="flex flex-col gap-2"
            style={{ width: '200px' }}
          >
            <div>
              {record?.clientContact?.firstName + ' ' + record?.clientContact?.lastName}
            </div>
          </div>
        );
      },
    //   ...getColumnSearchProps('status', 'LIKE', 'STRING'),
    },
    {
      title: 'Employee Type',
      dataIndex: 'employmentType',
      key: 'employmentType',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.employmentType?.length - b?.employmentType?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px' }}
          >
           {record?.employmentType}
          </div>
        );
      },
    //   ...getColumnSearchProps('phone', 'EQUAL', 'STRING'),
    },
    {
      title: 'Open/Closed',
      dataIndex: 'clientCorporation.name',
      key: 'clientCorporation.name',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.clientCorporation?.name?.length - b?.clientCorporation?.name?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>
                {record?.isOpen ? "Open" : "Closed"}
            </div>
          </div>
        );
      },
    //   ...getColumnSearchProps('clientCorporation.name', 'LIKE', 'STRING'),
    },
    {
      title: 'Status',
      dataIndex: 'owners',
      key: 'owners',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.owner?.firstName?.length - b?.owner?.lastName?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px'}}
          >
            <div>
              {record?.status}
            </div>
          </div>
        );
      },
    //   ...getColumnSearchProps('owner.firstName', 'EQUAL', 'STRING'),
    },
    {
      title: 'Pay Rate',
      dataIndex: 'payRate',
      key: 'payRate',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.payRate - b?.payRate,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.payRate}
          </div>
        );
      },
    },
    {
      title: 'Bill Rate',
      dataIndex: 'Salary',
      key: 'Salary',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.salary - b?.salary,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div className="company-view-truncate">
              {record?.salary}
            </div>
          </div>
        );
      },
    //   ...getColumnSearchProps('address.city', 'EQUAL', 'STRING'),
    },
    {
      title: 'Perm Free',
      dataIndex: 'lastVisit',
      key: 'lastVisit',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a?.feeArrangement - b?.feeArrangement,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>
              {record?.feeArrangement}
            </div>
          </div>
        );
      },
      //   ...getColumnSearchProps('dateLastVisit')
    },
  ];

  return (
    <div>
          <div className="px-3 grid grid-cols-1 gap-3 pb-5">
      <div style={{ width: '100%' }}>
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            overflow: 'hidden',
          }}
        >
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          }}>
            <div
              style={{
                padding: '20px',
                lineHeight: '1.2',
                display: 'flex',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <div>ITEMS PER PAGE</div>
                <div>
                  <Select
                    className="min-w-[140px]"
                    style={{
                      marginLeft: '10px',
                    }}
                    defaultValue={'10'}
                    rootClassName="company-status-container"
                    onChange={(e) => {
                      setCurrentPage(1)
                      setNumberPage(e);
                      handleGetContacts(
                        e,
                        '',
                        consultantId ? 'owner.id' : '',
                        consultantId,
                        consultantId ? 'EQUAL' : '',
                        consultantId ? 'INT' : ''
                      );
                    }}
                    options={[
                      {
                        value: '10',
                        label: '10',
                      },
                      {
                        value: '25',
                        label: '25',
                      },
                      {
                        value: '50',
                        label: '50',
                      },
                      {
                        value: '100',
                        label: '100',
                      },
                    ]}
                  />
                </div>
              </div>
            </div>
            <div>
            <Pagination
                total={totalData}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
            />
            </div>
          </div>
          <div className="company-view-contact">
            <Table
              loading={loadingContact}
              // showHeader={false}
              dataSource={listContact}
              columns={columns}
              pagination={false}
            />
          </div>
        </div>
      </div>
    </div>

    <div>
      <Modal
          // title={
          //   <>
          //     <div className="mb-6 text-lg text-white">{lead?.title}</div>
          //   </>
          // }
          className="insights-vacancy"
          open={openVacancyViewer}
          onOk={closeVacancyDrawer}
          onCancel={() => {
            closeVacancyDrawer()
          }}
          style={{
            height: "1000px",
          }}
          width={1300}
          forceRender
        >
          {/* Company Drawer */}

         <Drawer
            rootClassName="view-company-container"
            placement="top"
            closable={false}
            // onClose={closeVacancyDrawer}
            open={true}
            getContainer={false}
            destroyOnClose={true}
            height={'100%'}
          >
            <ViewVacancyDetail
              onClose={closeVacancyDrawer}
              vacancy={vacancyDetail}
              vacancyId={vacancyIdRaw}
              onReload={getVacancyDetail}
              vacancyDetailLoading={vacancyDetailLoading}
            />
          </Drawer>
        </Modal>
      </div>
    </div>
  );
};

export default ListVacancy;
