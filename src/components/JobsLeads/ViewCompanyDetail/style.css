.company-status-container .ant-select-selector{
    padding: 0px !important;
    border: none !important;
    border-bottom: 1px solid #dbdbdb !important;
    border-radius: 0 !important;
}

.view-company-container .ant-drawer-body {
    background-color: #4a89dc15;
    padding: 0;
}


.view-company-container .ant-tabs-nav{
    background-color: white;
    padding: 0 20px
}

.company-view-contact .ant-table-cell {
    background: #fff !important;
    color: #000 !important
}

.company-view-contact .ant-table-cell::before {
   display: none;
}

.company-view-contact .ant-table-content {
    width: 2000px !important;
}

.company-view-contact .ant-table-container {
    overflow-y: scroll;
}


.company-view-contact .ant-table-container::-webkit-scrollbar {
    height: 7px;
}

.ant-table-body::-webkit-scrollbar {
    height: 7px;
}

.company-view-notes .ant-table-cell {
    background: #fff !important;
    color: #000 !important
}

.company-view-notes .ant-table-cell::before {
   display: none;
}


.company-view-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

.custom-collapse-activity .ant-collapse-header {
    background-color: #fff !important;
}
.ng-star-inserted {
    display: inline-block;
    background: #8cc152;
    color: #ffffff;
    border-radius: 100%;
    height: 15px;
    width: 15px;
}
.ng-star-inserted::before {
    display: inline-block; 
    font-weight: bold;
    margin-right: 5px;
    font-size: 10px;
    height: 5px;
    content: "\2713";
    vertical-align: top;
}
.is_active_green{
    border-color: #8cc152 !important;
}

.insights-vacancy .ant-modal-content {
    min-height: 1000px !important;
}

.ant-modal-content {
    overflow-y: auto;
}  
