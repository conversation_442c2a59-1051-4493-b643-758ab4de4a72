import React from 'react';
import './step.style.css';

const Step = ({listItem,onClick, stepHasValue}) => {

  return listItem.map((listItemEntry, index) => {
    const matchingItem = stepHasValue.find(obj => obj?.key === listItemEntry?.title);
    return (
      <div className="workflow-overview" key={index}>
        <div
          className={`workflow-item ${index === listItem.length - 1 ? 'is-last' : ''}`}
        >
          <div
            onClick={() => onClick(listItemEntry)}
            data-tooltip={listItemEntry?.text_tooltip}
            className={`tooltip ${matchingItem ? 'is_active_green' : ''} `}
          >
            {matchingItem ? <i class="bhi-check ng-star-inserted"></i> : <></>}
            <i className="bhi-inactive"></i>
            <p>{listItemEntry?.title}{matchingItem ? ` (${matchingItem.value})` : ""}</p>
          </div>
        </div>
      </div>
    );
  });
};

export default Step;
