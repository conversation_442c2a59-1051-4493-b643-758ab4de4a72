import { Button, notification, Select, Spin, Table } from 'antd';
import Card from './Card';
import Step from './Step';
import { useEffect, useState } from 'react';
import { getDataTotal, searchBullhorn, upladteBullhorn } from '../../../services/bullhorn';
import {
  AppstoreAddOutlined,
  CalendarOutlined,
  HeartOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  StarOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import BullHornJobSubmissionModal from '../../BullHorn/BullHornJobSubmissionModal';

const ENTITY_NAME = {
  VACANCY: 'JobOrder',
  OPPORTUNITY: 'Opportunity',
  LEAD: 'Lead',
};

const ACTION_KEY = {
  VACANCY: '1',
  OPPORTUNITY: '3',
  LEAD: '2',
};
const LIST_ITEM_STEPS = [
  {
    title: 'Terms Agreed',
    text_tooltip: 'Add Note',
  },
  {
    title: 'Credit Check',
    text_tooltip: 'Add Note',
  },
  {
    title: 'Vacancies',
    text_tooltip: 'View Vacancies',
  },
  {
    title: 'Shortlist',
    text_tooltip: 'Add Shortlist',
  },
  {
    title: 'CV Sent',
    text_tooltip: 'Add CV Sent',
  },
  {
    title: 'Interview',
    text_tooltip: 'Schedule Interview',
  },
  {
    title: 'Offer Extended',
    text_tooltip: 'Add Shortlist',
  },
  {
    title: 'Placement',
    text_tooltip: 'Add Placement',
  },
  {
    title: 'Active',
    text_tooltip: 'Add Placement',
  },
];

const Overview = ({ company, companyId, setCompanyDetail, setChooseTab, setDefaultCollapse }) => {
  const [opportunityData, setOpportunityData] = useState([]);
  const [vacancyData, setVacancyData] = useState([]);
  const [totalData, setTotalData] = useState(0)
  const [leadData, setLeadData] = useState([]);
  //   Loading

  const [opportunityLoading, setOpportunityLoading] = useState(false);
  const [vacancyLoading, setVacancyLoading] = useState(false);
  const [leadLoading, setLeadLoading] = useState(false);
  const [companyLoading, setCompanyLoading] = useState(false);

  //   Sync data
  const [actionKey, setActionKey] = useState('');
  const [isModalOpen, setModalOpen] = useState(false);

  const getData = async (entityName, setData, setLoading) => {
    setLoading(true);
    const { data } = await searchBullhorn(
      entityName,
      0,
      6,
      '',
      companyId,
      '',
      ''
    );

    if (data?.result?.length > 0) {
      setData([...data?.result]);
    }
    setLoading(false);
  };

  const updateCompanyStatus = async (newStatus) => {
    setCompanyLoading(true);
    try {
      const newCompany = {
        ...company,
        status: newStatus,
      };

      const payload = {
        ...newCompany,
        entityName: 'ClientCorporation',
      };
      const res = await upladteBullhorn(companyId, payload);

      setCompanyDetail({ ...newCompany });
      notification.success({
        description: 'Company updated!',
      });
      setCompanyLoading(false);
    } catch (error) {
      notification.error({
        description: 'Network error. Try again later!',
      });
      setCompanyLoading(false);
    }
  };

  const openSync = (actionkey) => {
    setActionKey(actionkey);
    setModalOpen(true);
  };
  const handleClickStep = (item) => {
   if(item?.title == "Vacancies") {
    setDefaultCollapse("vacancies")
    setChooseTab("activity")
   }
  };

  useEffect(() => {
    getData(ENTITY_NAME.OPPORTUNITY, setOpportunityData, setOpportunityLoading);
    getData(ENTITY_NAME.VACANCY, setVacancyData, setVacancyLoading);
    getData(ENTITY_NAME.LEAD, setLeadData, setLeadLoading);
  }, []);

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div>
              <a
                title={record?.title}
                className="text-[#4a89dc] font-semibold line-clamp-1"
                href="#"
              >
                {record?.id} | {record?.title}
              </a>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div className="text-[#4a89dc] font-medium text-sm">
                {`${record?.clientContact?.firstName} ${record?.clientContact?.lastName}`}
              </div>
              <div className="flex items-center gap-1">
                <UserOutlined />
                <span>{`${record?.owner?.firstName} ${record?.owner?.lastName}`}</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        return (
          <div className="flex items-center gap-1">
            <InfoCircleOutlined />
            <span>{status}</span>
          </div>
        );
      },
    },
    {
      title: 'Date',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      render: (dateAdded) => {
        return (
          <div className="flex items-center gap-1">
            <CalendarOutlined />
            <span
              className="line-clamp-1"
              title={dayjs(dateAdded).format('DD-MM-YYYY HH:mm')}
            >
              {dayjs(dateAdded).format('DD-MM-YYYY HH:mm')}
            </span>
          </div>
        );
      },
    },
  ];

  const handleGetTotal = async () => {
    try {
      const { data } = await getDataTotal('JobOrder', companyId, "", "", "", "","Company");
      setTotalData(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  useEffect(() => {
      handleGetTotal()
    }, [])

  return (
    <>
    <div className="step-overview-container">
       <Step listItem={LIST_ITEM_STEPS} onClick={handleClickStep} stepHasValue={[{key: "Vacancies", value: totalData}]}></Step>
    </div>
    <div className="px-5 grid grid-cols-2 gap-3 pb-5">
      <Card
        loading={false}
        title={'Details'}
        key={'details'}
        description={'Company details'}
      >
        <div className="flex flex-col gap-3">
          <div className="grid grid-cols-10 gap-3 border-b p-3">
            <div className="col-span-3">Company Name</div>
            <div className="col-span-7">{company?.name}</div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Status</div>
            <div className="col-span-7">
              <Select
                loading={companyLoading}
                className="min-w-[140px]"
                value={company?.status}
                rootClassName="company-status-container"
                onSelect={updateCompanyStatus}
                options={[
                  {
                    value: 'Prospect',
                    label: 'Prospect',
                  },
                  {
                    value: 'Active',
                    label: 'Active',
                  },
                  {
                    value: 'Passive Account',
                    label: 'Passive Account',
                  },
                  {
                    value: 'DNC',
                    label: 'DNC',
                  },
                  {
                    value: 'Archive',
                    label: 'Archive',
                  },
                  {
                    value: 'Live Lead',
                    label: 'Live Lead',
                  },
                ]}
              />
            </div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Company Website</div>
            <div className="col-span-7">
              <a
                className="text-[#4a89dc] font-medium"
                href={company?.companyURL}
              >
                {company?.companyURL}
              </a>
            </div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Address</div>
            <div className="col-span-7">
              {`${company?.address?.address1 || company?.address?.address2}, ${company?.address?.city}, ${company?.address?.state}, ${company?.address?.countryName}`}
            </div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Main Phone</div>
            <div className="col-span-7">
              {' '}
              <a
                className="text-[#4a89dc] font-medium"
                href={`tel:${company?.phone}`}
              >
                {company?.phone}
              </a>
            </div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Child Companies</div>
            <div className="col-span-7 flex flex-col">
              <ul className="list-disc text-[#4a89dc]">
                {company?.childClientCorporations?.data?.length > 0 &&
                  company?.childClientCorporations?.data?.map((corp) => (
                    <li>
                      <a className="font-medium" href="#">
                        {corp?.name}
                      </a>
                    </li>
                  ))}
              </ul>
            </div>
          </div>
          <div className="grid grid-cols-10 gap-3 border-b pb-3 px-3">
            <div className="col-span-3">Parent Company</div>
            <div className="col-span-7">{company?.parentClientCorporation}</div>
          </div>
        </div>
      </Card>

      <Card
        loading={opportunityLoading}
        refetch={() =>
          getData(
            ENTITY_NAME.OPPORTUNITY,
            setOpportunityData,
            setOpportunityLoading
          )
        }
        title={'Opportunities'}
        key={'opportunities'}
        description={'Company Opportunities'}
        addNew={() => openSync(ACTION_KEY.OPPORTUNITY)}
      >
        {opportunityLoading && (
          <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
            <Spin />
          </div>
        )}
        {!opportunityLoading &&
          (opportunityData?.length > 0 ? (
            <>
              <Table
                loading={opportunityLoading}
                showHeader={false}
                dataSource={opportunityData.splice(-1)}
                columns={columns}
                pagination={false}
              />
              {opportunityData?.length > 5 && (
                <div className="flex items-center gap-3 w-full justify-center py-5 hover:bg-[#f4f4f4] cursor-pointer">
                  <span className="text-xl font-medium text-[#4a89dc]">
                    View All
                  </span>
                  <AppstoreAddOutlined className="text-xl font-medium text-[#4a89dc]" />
                </div>
              )}
            </>
          ) : (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <HeartOutlined className="text-5xl text-[#cccdcc]" />
              <div className="text-base">
                This Company doesn't have any Opportunities.
              </div>
              <Button
                onClick={() => openSync(ACTION_KEY.OPPORTUNITY)}
                type="primary"
                className="flex gap-1 items-center bg-[#4a89dc]"
              >
                <span>ADD OPPORTUNITY</span>
                <PlusOutlined />
              </Button>
            </div>
          ))}
      </Card>

      <Card
        loading={vacancyLoading}
        refetch={() =>
          getData(ENTITY_NAME.VACANCY, setVacancyData, setVacancyLoading)
        }
        title={'Vacancies'}
        key={'vacancies'}
        description={'Company Vacancies'}
        addNew={() => openSync(ACTION_KEY.VACANCY)}
      >
        {vacancyLoading && (
          <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
            <Spin />
          </div>
        )}
        {!vacancyLoading &&
          (vacancyData?.length > 0 ? (
            <>
              <Table
                loading={vacancyLoading}
                showHeader={false}
                dataSource={vacancyData.slice(1)}
                columns={columns}
                pagination={false}
              />
              {vacancyData?.length > 5 && (
                <div className="flex items-center gap-3 w-full justify-center py-5 hover:bg-[#f4f4f4] cursor-pointer">
                  <span className="text-xl font-medium text-[#4a89dc]">
                    View All
                  </span>
                  <AppstoreAddOutlined className="text-xl font-medium text-[#4a89dc]" />
                </div>
              )}
            </>
          ) : (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <TrophyOutlined className="text-5xl text-[#cccdcc]" />
              <div className="text-base">
                This Company doesn't have any Vacancies.
              </div>
              <Button
                onClick={() => openSync(ACTION_KEY.VACANCY)}
                type="primary"
                className="flex gap-1 items-center bg-[#4a89dc]"
              >
                <span>ADD VACANCY</span>
                <PlusOutlined />
              </Button>
            </div>
          ))}
      </Card>

      <Card
        loading={leadLoading}
        refetch={() => getData(ENTITY_NAME.LEAD, setLeadData, setLeadLoading)}
        title={'Leads'}
        key={'leads'}
        description={'Company Leads'}
        addNew={() => openSync(ACTION_KEY.LEAD)}
      >
        {leadLoading && (
          <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
            <Spin />
          </div>
        )}
        {!leadLoading &&
          (leadData?.length > 0 ? (
            <>
              <Table
                loading={leadLoading}
                showHeader={false}
                dataSource={leadData.splice(-1)}
                columns={columns}
                pagination={false}
              />
              {leadData?.length > 5 && (
                <div className="flex items-center gap-3 w-full justify-center py-5 hover:bg-[#f4f4f4] cursor-pointer">
                  <span className="text-xl font-medium text-[#4a89dc]">
                    View All
                  </span>
                  <AppstoreAddOutlined className="text-xl font-medium text-[#4a89dc]" />
                </div>
              )}
            </>
          ) : (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <StarOutlined className="text-5xl text-[#cccdcc]" />
              <div className="text-base">
                This Company doesn't have any Leads.
              </div>
              <Button
                onClick={() => openSync(ACTION_KEY.LEAD)}
                type="primary"
                className="flex gap-1 items-center bg-[#4a89dc]"
              >
                <span>ADD LEADS</span>
                <PlusOutlined />
              </Button>
            </div>
          ))}
      </Card>
      {/*  */}
      {isModalOpen && (
        <BullHornJobSubmissionModal
          job={null}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
          actionKey={actionKey}
          defaultDataCompany={{
            company: company,
            companyId: companyId,
          }}
        />
      )}
    </div>
    </>
  );
};

export default Overview;
