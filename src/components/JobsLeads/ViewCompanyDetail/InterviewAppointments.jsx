import { useEffect, useState } from 'react';
import Card from './Card';
import { searchBullhorn, searchBullhornData } from '../../../services/bullhorn';
import {
  Button,
  Drawer,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  DownOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import ViewVacancyDetail from '../ViewVacancyDetail';

const InterviewAppointments = (props) => {
  const { company, vacancy , totalShortList} = props;

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [openModalCreate, setOpenModalCreate] = useState(false);

  const handleGetContacts = async (count, start) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'Appointment',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      company?.id,
      null,
      null,
      null,
      false
    );

    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
  }, [company]);

  const columns = [
    // {
    //   title: '',
    //   dataIndex: '',
    //   key: '',
    //   defaultSortOrder: 'descend',
    //   render: (_, record) => {
    //     return (
    //       <div className="flex flex-col gap-2" style={{ width: '200px' }}>
    //         <Dropdown
    //           menu={{
    //             items: [
    //               {
    //                 label: <div>Edit</div>,
    //                 key: '0',
    //               },
    //               {
    //                 label: <div>Delete</div>,
    //                 key: '1',
    //               },
    //             ],
    //           }}
    //           trigger={['click']}
    //         >
    //           <a onClick={(e) => e.preventDefault()}>
    //             <Space>
    //               Action
    //               <DownOutlined />
    //             </Space>
    //           </a>
    //         </Dropdown>
    //       </div>
    //     );
    //   },
    // },
    {
      title: 'Start Date',
      dataIndex: 'startData',
      key: 'startDate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px' }}>
            <div>
              {record?.dateBegin
              ? dayjs(record?.dateBegin).format('YYYY/MM/DD h:mm a')
              : null}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Candidate',
      dataIndex: 'candidate',
      key: 'candidate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.candidateReference?.firstName} {record?.candidateReference?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Appointments',
      dataIndex: 'appointments',
      key: 'appointments',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.subject}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Type',
      dataIndex: 'Type',
      key: 'Type',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.type}
          </div>
        );
      },
    },
    {
      title: 'Contact',
      dataIndex: 'contact',
      key: 'contact',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.clientContactReference?.firstName} {record?.clientContactReference?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Location',
      dataIndex: 'Location',
      key: 'Location',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
            <div>
              {record?.location}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Owner',
      dataIndex: 'Owner',
      key: 'Owner',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.owner?.firstName} {record?.owner?.lastName}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetContacts(
                          e,
                          '',
                          consultantId ? 'owner.id' : '',
                          consultantId,
                          consultantId ? 'EQUAL' : '',
                          consultantId ? 'INT' : ''
                        );
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div style={{display: "flex"}}>
                <Pagination
                style={{
                  marginLeft: "10px"
                }}
                total={totalShortList}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
            />
              </div>
            </div>
            <div className="company-view-notes">
              <Table
                loading={loadingContact}
                // showHeader={false}
                scroll={{
                  x: 1000,
                }}
                dataSource={listContact}
                columns={columns}
                pagination={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterviewAppointments;