import {
  BankOutlined,
  CloseOutlined,
  EnvironmentOutlined,
  GoogleOutlined,
} from '@ant-design/icons';
import { Button, notification, Select, Spin, Tabs } from 'antd';

import './style.css';
import Overview from './Overview';
import { useEffect, useState } from 'react';
import { getDataTotal, searchFileOfBullHorn, upladteBullhorn } from '../../../services/bullhorn';
import ContactTab from './ContacTab';
import ActivityTab from './ActivityTab';
import NoteTab from './ActivityComponent/NoteTab';
import FileTab from './ActivityComponent/FileTab';
import EmailTab from './ActivityComponent/EmailTab';
import ShortListItem from './ShortListItem';
import ShortLists from './ShortLists';

const ViewCompanyDetail = ({
  onClose,
  company,
  companyId,
  setCompanyDetail,
  totalContact,
}) => {
  const [companyLoading, setCompanyLoading] = useState(false);
  const [chooseTab, setChooseTab] = useState('overview');
  const [defaultCollapse, setDefaultCollapse] = useState();
  const [noteData, setNoteTotal] = useState(0)
  const [totalFile, setTotalFile] = useState(0)
  const [loadingFile, setLoadingFile] = useState(false)
  const [loadingShortList, setLoadingShortList] = useState(false)
  const [totalShortList, setTotalShortList] = useState(0)
  const [chooseCollapse, setChooseCollapse] = useState("overview")
  const [loadingNote, setLoadingNote] = useState(false)


  const handleGetFiles = async (count, start) => {
    try {
      setLoadingFile(true);
      const response = await searchFileOfBullHorn(
        'ClientCorporation',
        (start > 0 ? start - 1 : 0) * count || '',
        count || null,
        null,
        company?.id,
        null,
        null,
        null,
        false
      );
      setTotalFile(response?.data?.result?.total);
      setLoadingFile(false);
    } catch (err) {
      setLoadingFile(false);
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  useEffect(() => {
    handleNoteTotal();
    handleGetFiles();
    handleGetTotalShortList();
  }, [])

  const handleNoteTotal = async (
  ) => {
    try {
      setLoadingNote(true)
      const { data } = await getDataTotal(
        'Note',
        companyId,
        "",
        "",
        "",
        "",
        'Company'
      );
      setLoadingNote(false)
      setNoteTotal(data.result.total);
    } catch (err) {
      setLoadingNote(false)
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const handleGetTotalShortList = async () => {
    try {
      setLoadingShortList(true)
      const { data } = await getDataTotal('JobSubmission', company?.id, "", "", "" , "", "Company");
      setTotalShortList(data.result.total);
      setLoadingShortList(false)
    } catch (err) {
      setLoadingShortList(false)
      notification.error({
        message: 'Some things went wrong',
      });
    }
  };

  const tabItems = [
    {
      key: 'overview',
      label: <span className="font-medium">OVERVIEW</span>,
      children: (
        <Overview
          setCompanyDetail={setCompanyDetail}
          company={company}
          companyId={companyId}
          setChooseTab={setChooseTab}
          setDefaultCollapse={setDefaultCollapse}
        />
      ),
    },
    {
      key: 'note',
      label: (
        <span className="font-medium">
          NOTES ({loadingNote ? <Spin /> : noteData})
        </span>
      ),
      children: (
        <NoteTab
          company={company}
          companyId={companyId}
          totalContact={noteData}
          setChooseTab={setChooseTab}
          setDefaultCollapse={setDefaultCollapse}
          handleNoteTotal={handleNoteTotal}
        />
      ),
    },
    {
      key: 'contact',
      label: (
        <span className="font-medium">
          CONTACTS ({totalContact ? totalContact : <Spin />})
        </span>
      ),
      children: (
        <ContactTab
          company={company}
          companyId={companyId}
          totalContact={totalContact}
          setChooseTab={setChooseTab}
          setDefaultCollapse={setDefaultCollapse}
        />
      ),
    },{
      key: 'file',
      label: (
        <span className="font-medium">
          FILES ({!loadingFile ? totalFile : <Spin />})
        </span>
      ),
      children: (
        <FileTab
          company={company}
          companyId={companyId}
          totalFile={totalFile}
          setChooseTab={setChooseTab}
          setDefaultCollapse={setDefaultCollapse}
          handleGetTotal={handleGetFiles}
        />
      ),
    },
    {
      key: 'email',
      label: (
        <span className="font-medium">
          EMAIL
        </span>
      ),
      children: (
        <EmailTab
          company={company}
          companyId={companyId}
          totalFile={totalFile}
          setChooseTab={setChooseTab}
          setDefaultCollapse={setDefaultCollapse}
          handleGetTotal={handleGetFiles}
        />
      ),
    },
    {
      key: 'shortList',
      label: (
        <span className="font-medium">
          SHORTLIST ({!loadingShortList ? totalShortList : <Spin />})
        </span>
      ),
      children: (
        <ShortLists
          company={company}
          companyId={companyId}
          totalShortList={totalShortList}
          setChooseTab={setChooseTab}
          setChooseCollapse={setDefaultCollapse}
        />
      ),
    },
    {
      key: 'activity',
      label: <span className="font-medium">ACTIVITY</span>,
      children: (
        <ActivityTab
          company={company}
          companyId={companyId}
          defaultCollapse={defaultCollapse}
          setDefaultCollapse={setDefaultCollapse}
        />
      ),
    },
  ];

  const updateCompanyStatus = async (newStatus) => {
    setCompanyLoading(true);
    try {
      const newCompany = {
        ...company,
        status: newStatus,
      };

      const payload = {
        ...newCompany,
        entityName: 'ClientCorporation',
      };
      const res = await upladteBullhorn(companyId, payload);

      setCompanyDetail({ ...newCompany });
      notification.success({
        description: 'Company updated!',
      });
      setCompanyLoading(false);
    } catch (error) {
      notification.error({
        description: 'Network error. Try again later!',
      });
      setCompanyLoading(false);
    }
  };

  return (
    <div>
      <div className="flex flex-col gap-4 p-5 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex gap-3 items-center">
            <BankOutlined className="text-[#39d] text-3xl" />
            <span className="text-2xl font-medium">{companyId}</span>
            <span>|</span>
            <span className="text-2xl font-medium">{company?.name}</span>
            <div>
              <Button
                onClick={() =>
                  window?.open(
                    `https://www.google.com/search?q=%22${company?.name}%22`
                  )
                }
                type="text"
                icon={<GoogleOutlined />}
              />
              <Button
                onClick={() =>
                  window?.open(
                    `https://www.google.com/maps?q=${company?.name}+${company?.address?.address1}+${company?.address?.city}+${company?.address?.countryName}`
                  )
                }
                type="text"
                icon={<EnvironmentOutlined />}
              />
            </div>
          </div>
          <div>
            <Button onClick={onClose} type="text" icon={<CloseOutlined />} />
          </div>
        </div>
        <div>
          <div className="grid grid-cols-7 w-3/5 mb-1">
            <div className="col-span-1">ID</div>
            <div className="col-span-2">Company Name</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Main Phone</div>
          </div>
          <div className="grid grid-cols-7 w-3/5 items-center">
            <div className="col-span-1">{companyId}</div>
            <div className="col-span-2">{company?.name}</div>
            <div className="col-span-2">
              <Select
                className="min-w-[90px]"
                value={company?.status}
                loading={companyLoading}
                onSelect={updateCompanyStatus}
                rootClassName="company-status-container"
                options={[
                  {
                    value: 'Prospect',
                    label: 'Prospect',
                  },
                  {
                    value: 'Active',
                    label: 'Active',
                  },
                  {
                    value: 'Passive Account',
                    label: 'Passive Account',
                  },
                  {
                    value: 'DNC',
                    label: 'DNC',
                  },
                  {
                    value: 'Archive',
                    label: 'Archive',
                  },
                  {
                    value: 'Live Lead',
                    label: 'Live Lead',
                  },
                ]}
              />
            </div>
            <a
              className="col-span-2 text-[#4a89dc] font-medium"
              href={`tel:${company?.phone}`}
            >
              {company?.phone}
            </a>
          </div>
        </div>
      </div>
      <div className="w-full border-b-2 border-b-[#3399dd]"></div>
      <Tabs
        defaultActiveKey={chooseTab}
        activeKey={chooseTab}
        items={tabItems}
        onChange={(e) => setChooseTab(e)}
      />
    </div>
  );
};

export default ViewCompanyDetail;
