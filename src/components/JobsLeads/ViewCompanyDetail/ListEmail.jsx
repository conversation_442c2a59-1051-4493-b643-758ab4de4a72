import { useEffect, useState } from 'react';
import Card from './Card';
import { searchBullhorn, searchBullhornData } from '../../../services/bullhorn';
import {
  Button,
  Drawer,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  DownOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import ViewVacancyDetail from '../ViewVacancyDetail';

const ListEmail = (props) => {
  const { company, companyId, totalData } = props;

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [vacancyIdRaw, setVacancyIdRaw] = useState();
  const [currentPage, setCurrentPage] = useState(1);

  const handleGetContacts = async (
    count,
    start,
    customField,
    customFieldValue,
    option,
    type
  ) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'UserMessage',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      companyId,
      null,
      null,
      null,
      false,
      customField,
      customFieldValue,
      option,
      type
    );
    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
  }, [companyId]);

  const getColumnSearchProps = (dataIndex, option, type) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div
        style={{
          padding: 8,
        }}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <Input
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          style={{
            marginBottom: 8,
            display: 'block',
          }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setCurrentPage(1);
              handleGetContacts(
                numberPage,
                '',
                dataIndex,
                selectedKeys,
                option,
                type
              );
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{
              width: 90,
            }}
          >
            Search
          </Button>
          <Button
            onClick={() => setSelectedKeys(null)}
            size="small"
            style={{
              width: 90,
            }}
          >
            Reset
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              close();
            }}
          >
            close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined
        style={{
          color: filtered ? '#1677ff' : undefined,
        }}
      />
    ),
    onFilter: (value, record) =>
      record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        // setTimeout(() => searchInput.current?.select(), 100);
      }
    },
  });

  const [openVacancyViewer, setOpenVacancyViewer] = useState(false);
  const [vacancyDetail, setVacancyDetail] = useState(null);
  const [vacancyDetailLoading, setVacancyDetailLoading] = useState(false);

  const showVacancyDrawer = () => setOpenVacancyViewer(true);
  const closeVacancyDrawer = () => setOpenVacancyViewer(false);

  const getVacancyDetail = async (vacancyId) => {
    if (!vacancyId) return;
    setVacancyIdRaw(vacancyId);
    setVacancyDetailLoading(true);
    showVacancyDrawer();
    try {
      // const res = await getBHCompanyDetail(companyIdRaw);
      const { data } = await searchBullhorn(
        'JobOrder',
        0,
        1,
        '',
        '',
        vacancyId,
        ''
      );
      if (data?.result?.length > 0) {
        const { result } = data;
        setVacancyDetail({ ...result[0] });
      } else {
        notification.warning({
          description: 'Vacancy might be deleted by another user.',
        });
      }

      setVacancyDetailLoading(false);
    } catch (error) {
      setVacancyDetailLoading(false);
      notification.error({
        description: 'Network Error! Please try again later.',
      });
    }
  };

  const columns = [
    {
      title: '',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <Dropdown
              menu={{
                items:  [
                    {
                      label: <div>Edit</div>,
                      key: '0',
                    },
                    {
                      label: <div>Delete</div>,
                      key: '1',
                    },
                  ],
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  Action
                  <DownOutlined />
                </Space>
              </a>
            </Dropdown>
          </div>
        );
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.dateAdded
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
          </div>
        );
      },
    },
    {
      title: 'Created By',
      dataIndex: 'created_by',
      key: 'title',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) => a?.title?.length - b?.title?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2" style={{ width: '200px' }}
          >
            <div>{record?.commentingPerson?.firstName} {record?.commentingPerson?.lastName}</div>
          </div>
        );
      },
      //   ...getColumnSearchProps('name', 'LIKE', 'STRING'),
    },
    {
      title: 'About Who',
      dataIndex: 'about_who',
      key: 'about_who',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) =>
    //     a?.owner?.firstName?.length - b?.owner?.firstName?.length,
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>
                {record?.personReference?.firstName} {record?.personReference?.lastName}
            </div>
          </div>
        );
      },
      //   ...getColumnSearchProps('occupation', 'LIKE', 'STRING'),
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) =>
    //     a?.clientContact?.firstName?.length -
    //     b?.clientContact?.firstName?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>
              {record?.action}
            </div>
          </div>
        );
      },
      //   ...getColumnSearchProps('status', 'LIKE', 'STRING'),
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
      defaultSortOrder: 'descend',
    //   sorter: (a, b) => a?.employmentType?.length - b?.employmentType?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.employmentType}
          </div>
        );
      },
      //   ...getColumnSearchProps('phone', 'EQUAL', 'STRING'),
    },
  ];

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetContacts(
                          e,
                          '',
                          consultantId ? 'owner.id' : '',
                          consultantId,
                          consultantId ? 'EQUAL' : '',
                          consultantId ? 'INT' : ''
                        );
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div>
                {/* <Pagination
                total={totalData}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
            /> */}
              </div>
            </div>
            <div className="company-view-notes">
              <Table
                loading={loadingContact}
                // showHeader={false}
                expandable={{
                  expandedRowRender: (record) => (
                    <p style={{ margin: 0 }}>{record?.action}</p>
                  ),
                }}
                dataSource={listContact}
                columns={columns}
                pagination={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListEmail;
