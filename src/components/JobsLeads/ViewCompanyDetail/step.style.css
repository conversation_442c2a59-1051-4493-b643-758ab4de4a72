.ant-steps-item-tail {
  top: 12px !important;
}
.popover-custom {
  width: 90px !important;
  height: 30px !important;
  background-color: #f0f0f0 !important;
  border-radius: 5px !important;
}
.step-overview-container {
  display: flex;
  margin-left: 15px;
}

.step-vacancy-overview-container {
  display: flex;
  margin-left: 15px;
  width: 60vw;
}

.workflow-overview {
  width: 100%;
  display: flex;
  padding: 0;
  margin-bottom: 2rem;
  height: 35px;
  color: #3d464d;
  position: relative;
  z-index: 1;
  justify-content: center;
}

.workflow-overview .workflow-item {
  display: flex;
  flex: 0.25 1 15%;
  align-items: center;
  margin-right: 10px;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
  width: 120px;
}
.workflow-overview .workflow-item:after {
  content: '';
  width: 100%;
  border-bottom: 1px solid #b6b6b6;
  position: absolute;
  left: 90%;
  top: 50%;
  z-index: 1 !important;
}
.workflow-overview .workflow-item.is-last:after {
  border-bottom: none !important;
}
.workflow-overview .workflow-item > div {
  display: flex !important;
  background: #ffffff;
  height: 100%;
  border: 3px solid #ffffff;
  border-radius: 5px;
  width: 120px;
  padding: 0.8rem;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 1.1rem;
  cursor: pointer;
  position: relative;
  transition:
    color 250ms,
    background-color 250ms;
  box-shadow:
    0 -1px 3px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  z-index: 2;
}
.workflow-overview .workflow-item > div > p {
  font-size: 12px;
}

.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -50px; /* Adjust this value as needed */
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.2s,
    visibility 0.2s;
  z-index: 1;
  font-size: 12px;
  height: fit-content;
  box-shadow: 0 4px 8px rgba(62, 155, 9, 0.1);
}

.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

.tooltip::before {
  content: '';
  position: absolute;
  top: 100%; /* Position the arrow below the tooltip */
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #333 transparent; /* Arrow pointing down */
  margin-top: 2px; /* Adjust the margin to 2px */
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.2s,
    visibility 0.2s;
}

.tooltip:hover::before {
  opacity: 1;
  visibility: visible;
}
.workflow .workflow-item > div > i {
  display: inline-block;
  background: #8cc152;
  color: #ffffff;
  border-radius: 15px;
  padding: 3px;
  margin-right: 0.6rem;
  font-size: 1.3rem;
}
