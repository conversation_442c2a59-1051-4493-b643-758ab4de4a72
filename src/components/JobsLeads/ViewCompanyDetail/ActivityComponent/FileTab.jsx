import { DownOutlined, UploadOutlined } from '@ant-design/icons';
import {
  AutoComplete,
  Button,
  Dropdown,
  Pagination,
  Select,
  Space,
  Table,
  Upload,
  message,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import {
  deleteBullhornContact,
  deleteBullhornFile,
  searchBullhorn,
  searchFileOfBullHorn,
  upladteBullhorn,
  uploadBHFile,
} from '../../../../services/bullhorn';
import ModalConfirmDeleteBullhorn from '../../ViewVacancyDetail/ShortListTab/ModalConfirmDeleteBullhorn';
import { useForm } from 'react-hook-form';

const FileTab = (props) => {
  const { company, totalFile, handleGetTotal } = props;

  const [loadingContact, setLoadingContact] = useState(false);
  const [listData, setListData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [numberPage, setNumberPage] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [currentDeleteId, setCurrentDeleteId] = useState();
  const [currentSelect, setCurrentSelect] = useState();
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [isCreateNew, setIsCreateNew] = useState(false);

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetFiles(numberPage, page);
  };

  const handleGetFiles = async (count, start) => {
    setLoadingContact(true);
    const response = await searchFileOfBullHorn(
      'ClientCorporation',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      company?.id,
      null,
      null,
      null,
      false
    );
    setListData(response?.data?.result?.data || []);
    setLoadingContact(false);
  };

  useEffect(() => {
    handleGetFiles();
  }, [company]);

  const { getValues, setValue, watch } = useForm();

  const optionTypes = [
    { label: 'CV', value: 'CV' },
    { label: 'Formatted CV', value: 'Formatted CV' },
    { label: 'Submittal Cover Sheet', value: 'Submittal Cover Sheet' },
    { label: 'Cover Letter', value: 'Cover Letter' },
    { label: 'References', value: 'References' },
    { label: 'Contract Signed', value: 'Contract Signed' },
    { label: 'Proof of ID', value: 'Proof of ID' },
    { label: 'Visa', value: 'Visa' },
    { label: 'Passport', value: 'Passport' },
    { label: 'Medical Certificate', value: 'Medical Certificate' },
  ];

  const optionDistribution = [
    { label: 'Internal', value: 'Internal' },
    { label: 'General', value: 'General' },
  ];

  const propsUpdate = {
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      try {
        let formData = new FormData();
        formData.append('entityName', "ClientCorporation");
        formData.append('id', company?.id);
        formData.append("name", file?.name);
        formData.append('fileType', file?.type);
        formData.append('fileSize', file?.size);
        formData.append('distribution', "General");
        formData.append('file', file);
        const res = await uploadBHFile(formData);
        // console.log('res: ', res?.data?.result?.data?.imgId);
        handleGetTotal()
        handleGetFiles();
        onSuccess('ok');
      } catch (error) {
        console.log('error upload image: ', error);
        onError(error);
      }
    },
  };

  const actionDeleteFile = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await deleteBullhornFile({
        id: currentDeleteId,
        entity: 'ClientCorporation',
        parentId: company?.id
      });
      setLoadingDelete(false);
      setOpenModal(false);
      handleGetTotal()
      handleGetFiles();
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
      setLoadingDelete(false);
      setOpenModal(false);
    }
  };

  const columns = [
    {
      title: '',
      dataIndex: 'action_trigger',
      key: 'action_trigger',
      defaultSortOrder: 'descend',
      width: '20px',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '70px' }}>
            <Dropdown
              menu={{
                items: [
                  {
                    label: (
                      <div
                        onClick={() => {
                          setCurrentDeleteId(record.id), setOpenModal(true);
                        }}
                      >
                        Delete
                      </div>
                    ),
                    key: '1',
                  },
                ],
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  Action
                  <DownOutlined />
                </Space>
              </a>
            </Dropdown>
          </div>
        );
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
      width: '100px',
      align: 'center',
      sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.dateAdded
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
          </div>
        );
      },
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      defaultSortOrder: 'descend',
      //   sorter: (a, b) => a?.title?.length - b?.title?.length,
      render: (_, record) => {
        return (
          <a
            href={record?.url}
            target="_blank"
            className="flex flex-col gap-2"
            style={{ width: '350px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>{record?.name}</div>
          </a>
        );
      },
    },
    {
      title: 'File Type',
      dataIndex: 'file_type',
      key: 'file_type',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '150px' }}>
            <div>
              <div className="custom-vacancy-form">
                <Select
                  options={optionTypes}
                  defaultValue={record?.type}
                  onSelect={(value) => {
                    handleSaveData({
                      type: value,
                      id: record.id,
                    });
                  }}
                />
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Visibility',
      dataIndex: 'visibility',
      key: 'visibility',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '120px' }}>
            <div>{record?.isPrivate ? 'Private' : 'Public'}</div>
          </div>
        );
      },
    },
    {
      title: 'File Size',
      dataIndex: 'file_size',
      key: 'file_size',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>{record?.fileSize}</div>
          </div>
        );
      },
    },
  ];

  const handleSaveData = async (data) => {
    try {
      setLoadingContact(true);
      const payload = {
        type: data?.type,
        distribution: data?.distribution,
        entityName: 'ClientCorporationFileAttachment',
      };

      const dataUpdate = await upladteBullhorn(data?.id, payload);
      notification.success({
        message: 'Updated Success',
      });
      setLoadingContact(false);
    } catch (err) {
      setLoadingContact(false);
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetFiles(e, '');
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div>
                  <Upload {...propsUpdate}>
                    <Button icon={<UploadOutlined />}>Click to Upload</Button>
                  </Upload>
                </div>
                <Pagination
                  total={totalFile}
                  pageSize={numberPage}
                  showSizeChanger={false}
                  onChange={handlePagination}
                  current={currentPage}
                />
              </div>
            </div>
            <div className="company-view-contact">
              <Table
                columns={columns}
                loading={loadingContact}
                pagination={false}
                dataSource={listData}
              />
            </div>
          </div>
        </div>
      </div>
      <ModalConfirmDeleteBullhorn
        action={actionDeleteFile}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loadingDelete={loadingDelete}
        property="File"
      />
    </div>
  );
};

export default FileTab;
