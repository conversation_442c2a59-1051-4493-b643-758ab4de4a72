import { CaretRightOutlined, FolderOutlined, StarOutlined, UserOutlined } from '@ant-design/icons';
import { Collapse, notification, theme } from 'antd';
import ListVacancy from '../ListVacancy';
import { getDataTotal } from '../../../../services/bullhorn';
import { useEffect, useState } from 'react';

const RelatedRecords = (props) => {
  const {
    companyId,
    company,
    defaultCollapse,
    setDefaultCollapse
  } = props;
  const { token } = theme.useToken();
  const [totalData, setTotalData] = useState(0)

  const handleGetTotal = async () => {
    try {
      const { data } = await getDataTotal('JobOrder', companyId, "", "", "", "", "Company" );
      setTotalData(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  useEffect(() => {
    handleGetTotal()
  }, [])

  return (
    <div className="custom-collapse-activity">
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <UserOutlined />
                  </div>
                  <div class="ml-2.5">Contacts</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        onChange={(e) => {
          defaultCollapse == "vacancies" ? setDefaultCollapse(null) : setDefaultCollapse("vacancies")
        }}
        activeKey={defaultCollapse == "vacancies" ? ['1'] : false}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FolderOutlined />
                  </div>
                  <div class="ml-2.5">Vacancies ({totalData})</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: (
              <div>
                <ListVacancy company={company} companyId={companyId} totalData={totalData}/>
              </div>
            ),
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible="disabled"
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-lg font-medium">
                  <div>
                  <StarOutlined />
                  </div>
                  <div class="ml-2.5">Placements (0)</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
    </div>
  );
};

export default RelatedRecords;
