import { useEffect, useState } from 'react';

import {
  Button,
  Dropdown,
  Input,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  CloseCircleOutlined,
  DownOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { isNull } from 'lodash';
import {
  searchBullhornData,
  getDataTotal,
  searchBullhorn,
  deleteBullhornContact,
} from '../../../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../../../hooks/useInfinitiveScroll';
import ModalEditEmail from '../../ViewVacancyDetail/ShortListTab/ModalEditEmail';
import ModalConfirmDeleteBullhorn from '../../ViewVacancyDetail/ShortListTab/ModalConfirmDeleteBullhorn';

const EmailTab = (props) => {
  const { company, companyId, totalContact, setChooseTab, setDefaultCollapse } =
    props;

  const [currentTotal, setCurrentTotal] = useState(null);

  const handleGetTotal = async (
    customSearchField,
    customSearchValue,
    optionCustom,
    typeCustom
  ) => {
    try {
      const { data } = await getDataTotal(
        'Note',
        companyId,
        customSearchField,
        customSearchValue,
        optionCustom,
        typeCustom,
        'Company'
      );

      setCurrentTotal(data.result.total);
    } catch (err) {
      console.log(err);
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [currentSelect, setCurrentSelect] = useState();
  const [isCreateNew, setIsCreateNew] = useState(false);
  const [currentDeleteId, setCurrentDeleteId] = useState();
  const [openModal, setOpenModal] = useState(false);

  const handleGetContacts = async (
    count,
    start,
    customField,
    customFieldValue,
    option,
    type
  ) => {
    setLoadingContact(true);
    try {
      const response = await searchBullhorn(
        'UserMessage',
        (start > 0 ? start - 1 : 0) * count || '',
        count || null,
        null,
        companyId,
        null,
        null,
        null,
        false,
        customField,
        customFieldValue,
        option,
        type
      );
      setListContact(response?.data?.result || []);
      setLoadingContact(false);
    } catch (error) {
      console.log('error: ', error);
      setLoadingContact(false);
    }
  };

  const {
    options: contactSecondaryOwner,
    handlePopupScroll: handleContactSecondaryOwnerScroll,
    handleSearch: handleContactSecondaryOwnerSearch,
    isLoading: isLoadingContactSecondaryOwner,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    // populateReportedAgencies(page, pageSize);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
    // handleGetConsultant()
    handleContactSecondaryOwnerSearch(' ', null, 100);
    handleGetTotal();
  }, [companyId]);

  const actionDeleteNote = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await deleteBullhornContact({
        id: currentDeleteId,
        entity: 'Note',
      });
      setLoadingDelete(false);
      setOpenModal(false);
      handleGetTotal();
      handleGetContacts();
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
      setLoadingDelete(false);
      setOpenModal(false);
    }
  };

  const columns = [
    {
      title: '',
      dataIndex: 'action_trigger',
      key: 'action_trigger',
      defaultSortOrder: 'descend',
      width: '120px',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '70px' }}>
            <Dropdown
              menu={{
                items: [
                  {
                    label: (
                      <div
                        onClick={() => {
                          setCurrentSelect(record), setOpenEditModal(true);
                        }}
                      >
                        Edit
                      </div>
                    ),
                    key: '0',
                  },
                  {
                    label: (
                      <div
                        onClick={() => {
                          setCurrentDeleteId(record.id), setOpenModal(true);
                        }}
                      >
                        Delete
                      </div>
                    ),
                    key: '1',
                  },
                ],
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  Action
                  <DownOutlined />
                </Space>
              </a>
            </Dropdown>
          </div>
        );
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
      width: '120px',
      align: 'center',
      sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.dateAdded
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
          </div>
        );
      },
    },
    {
      title: 'Created By',
      dataIndex: 'created_by',
      key: 'title',
      defaultSortOrder: 'descend',
      //   sorter: (a, b) => a?.title?.length - b?.title?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '120px' }}>
            <div>
              {record?.commentingPerson?.firstName}{' '}
              {record?.commentingPerson?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'About Who',
      dataIndex: 'about_who',
      key: 'about_who',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '120px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>
              {record?.personReference?.firstName}{' '}
              {record?.personReference?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '120px' }}>
            <div>{record?.action}</div>
          </div>
        );
      },
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '400px' }}>
            <div>
              <div
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  marginLeft: '10px',
                }}
                dangerouslySetInnerHTML={{
                  __html: record?.comments,
                }}
              ></div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Primary Department',
      dataIndex: 'primary_department',
      key: 'primary_department',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>{record?.primaryDepartmentName?.[0]}</div>
          </div>
        );
      },
    },
  ];

  return (
    <div className="px-3 grid grid-cols-1 gap-3 pb-5">
      <div style={{ width: '100%' }}>
        <div
          style={{
            background: '#fff',
            borderRadius: '8px',
            overflow: 'hidden',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                padding: '20px',
                lineHeight: '1.2',
                display: 'flex',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <div>ITEMS PER PAGE</div>
                <div>
                  <Select
                    className="min-w-[140px]"
                    style={{
                      marginLeft: '10px',
                    }}
                    defaultValue={'10'}
                    rootClassName="company-status-container"
                    onChange={(e) => {
                      setCurrentPage(1);
                      setNumberPage(e);
                      handleGetContacts(
                        e,
                        '',
                        consultantId ? 'owner.id' : '',
                        consultantId,
                        consultantId ? 'EQUAL' : '',
                        consultantId ? 'INT' : ''
                      );
                    }}
                    options={[
                      {
                        value: '10',
                        label: '10',
                      },
                      {
                        value: '25',
                        label: '25',
                      },
                      {
                        value: '50',
                        label: '50',
                      },
                      {
                        value: '100',
                        label: '100',
                      },
                    ]}
                  />
                </div>
              </div>
            </div>
            <div
              style={{
                display: 'flex',
              }}
            >
              {/* <Button
                type="primary"
                onClick={() => {
                  setIsCreateNew(true);
                  setCurrentDeleteId(null), setOpenEditModal(true);
                }}
              >
                Add new Note
              </Button> */}
              <Pagination
                total={!isNull(currentTotal) ? currentTotal : totalContact}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
              />
            </div>
          </div>
          <div className="company-view-contact">
            <Table
              loading={loadingContact}
              // showHeader={false}
              dataSource={listContact}
              columns={columns}
              pagination={false}
            />
          </div>
        </div>
      </div>

      <ModalConfirmDeleteBullhorn
        action={actionDeleteNote}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loadingDelete={loadingDelete}
      />

      <ModalEditEmail
        openModal={openEditModal}
        setOpenModal={setOpenEditModal}
        loadingDelete={loadingDelete}
        defaultData={currentSelect}
        company={company}
        handleAfterLoading={() => {
          handleGetContacts(numberPage, 1);
          handleGetTotal();
        }}
        isCreateNew={isCreateNew}
        setIsCreateNew={setIsCreateNew}
        handleGetTotal={handleGetTotal}
      />
    </div>
  );
};

export default EmailTab;
