import { CalendarOutlined, CaretRightOutlined, CheckSquareOutlined, EditOutlined, FolderOutlined, MailOutlined, StarOutlined, UnorderedListOutlined, UserOutlined, WifiOutlined } from '@ant-design/icons';
import { Collapse, theme } from 'antd';
import ListEmail from '../ListEmail';

const ActivityCollap = (props) => {
  const { token } = theme.useToken();
  const {
    companyId,
    company,
    defaultCollapse,
    setDefaultCollapse
  } = props;

  return (
    <div className="custom-collapse-activity">
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                  <FolderOutlined />
                  </div>
                  <div class="ml-2.5">Vacancies Activities</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <MailOutlined />
                  </div>
                  <div class="ml-2.5">Email</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children:   <div>coming soon</div>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <UnorderedListOutlined />
                  </div>
                  <div class="ml-2.5">Notes</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible="disabled"
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-lg font-medium">
                  <div>
                  <CalendarOutlined />
                  </div>
                  <div class="ml-2.5">Appointments  (0)</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible="disabled"
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-lg font-medium">
                  <div>
                  <CheckSquareOutlined />
                  </div>
                  <div class="ml-2.5">Tasks  (0)</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                  <EditOutlined />
                  </div>
                  <div class="ml-2.5">Edit History</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
       <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                  <WifiOutlined
                   />
                  </div>
                  <div class="ml-2.5">Status History</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
    </div>
  );
};

export default ActivityCollap;
