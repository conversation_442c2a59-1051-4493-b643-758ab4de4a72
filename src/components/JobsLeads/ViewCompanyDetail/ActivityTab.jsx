import ActivityCollap from './ActivityComponent/ActivityCollap';
import RelatedRecords from './ActivityComponent/RelatedRecords';

const ActivityTab = (props) => {
  const { company, companyId, totalContact, defaultCollapse, setDefaultCollapse  } = props;
  return (
    <div className="p-2.5">
      <div className="font-semibold">RELATED RECORDS</div>
      <div className="p-3">
        <div>
          <RelatedRecords companyId={companyId} company={company} defaultCollapse={defaultCollapse} setDefaultCollapse={setDefaultCollapse}/>
        </div>
      </div>
      <div className="font-semibold mt-5">ACTIVITY</div>
      <div className="p-3">
        <div>
          <ActivityCollap companyId={companyId} company={company} defaultCollapse={defaultCollapse} setDefaultCollapse={setDefaultCollapse}/>
        </div>
      </div>
    </div>
  );
};

export default ActivityTab;
