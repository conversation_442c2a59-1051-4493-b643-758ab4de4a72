import { DownOutlined } from '@ant-design/icons';
import {
  Button,
  Dropdown,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import {
  deleteBullhornContact,
  searchBullhorn,
} from '../../../../services/bullhorn';
import ModalConfirmDeleteBullhorn from './ModalConfirmDeleteBullhorn';
import ModalEditEmail from './ModalEditEmail';

const NoteTab = (props) => {
  const {
    company,
    vacancy,
    totalContact,
    setChooseTab,
    setDefaultCollapse,
    totalNote,
    handleGetTotal
  } = props;

  const [loadingContact, setLoadingContact] = useState(false);
  const [listNote, setListNote] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [numberPage, setNumberPage] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [currentDeleteId, setCurrentDeleteId] = useState();
  const [currentSelect, setCurrentSelect] = useState();
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [isCreateNew, setIsCreateNew] = useState(false);

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetContacts(numberPage, page);
  };

  const handleGetContacts = async (count, start) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'Note',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      null,
      null,
      null,
      null,
      false,
      "", "", "", "",
      vacancy?.id,
    );
    setListNote(response?.data?.result || []);
    setLoadingContact(false);
  };

  useEffect(() => {
    handleGetContacts();
  }, [vacancy]);

  const actionDeleteNote = async () => {
    try {
      setLoadingDelete(true);
      const { data } = await deleteBullhornContact({
        id: currentDeleteId,
        entity: 'Note',
      });
      setLoadingDelete(false);
      setOpenModal(false);
      handleGetTotal()
      handleGetContacts();
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
      setLoadingDelete(false);
      setOpenModal(false);
    }
  };

  const columns = [
    {
      title: '',
      dataIndex: 'action_trigger',
      key: 'action_trigger',
      defaultSortOrder: 'descend',
      width: '20px',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '70px' }}>
            <Dropdown
              menu={{
                items: [
                  {
                    label: (
                      <div
                        onClick={() => {
                          setCurrentSelect(record), setOpenEditModal(true);
                        }}
                      >
                        Edit
                      </div>
                    ),
                    key: '0',
                  },
                  {
                    label: (
                      <div
                        onClick={() => {
                          setCurrentDeleteId(record.id), setOpenModal(true);
                        }}
                      >
                        Delete
                      </div>
                    ),
                    key: '1',
                  },
                ],
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()}>
                <Space>
                  Action
                  <DownOutlined />
                </Space>
              </a>
            </Dropdown>
          </div>
        );
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'dateAdded',
      key: 'dateAdded',
      defaultSortOrder: 'descend',
      width: '100px',
      align: 'center',
      sorter: (a, b) => a?.dateAdded - b?.dateAdded,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.dateAdded
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
          </div>
        );
      },
    },
    {
      title: 'Created By',
      dataIndex: 'created_by',
      key: 'title',
      defaultSortOrder: 'descend',
      //   sorter: (a, b) => a?.title?.length - b?.title?.length,
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '120px' }}>
            <div>
              {record?.commentingPerson?.firstName}{' '}
              {record?.commentingPerson?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'About Who',
      dataIndex: 'about_who',
      key: 'about_who',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div
            className="flex flex-col gap-2"
            style={{ width: '120px', color: '#4a89dc', fontWeight: '600' }}
          >
            <div>
              {record?.personReference?.firstName}{' '}
              {record?.personReference?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '120px' }}>
            <div>{record?.action}</div>
          </div>
        );
      },
    },
    {
      title: 'Comments',
      dataIndex: 'comments',
      key: 'comments',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '400px' }}>
            <div>
              <div
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  marginLeft: '10px',
                }}
                dangerouslySetInnerHTML={{
                  __html: record?.comments,
                }}
              ></div>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Primary Department',
      dataIndex: 'primary_department',
      key: 'primary_department',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            <div>{record?.primaryDepartmentName?.[0]}</div>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetContacts(e, '');
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div>
                  <Button
                    type="primary"
                    onClick={() => {
                      setIsCreateNew(true);
                      setCurrentDeleteId(null),
                      setOpenEditModal(true);
                    }}
                  >
                    Add new Note
                  </Button>
                </div>
                <Pagination
                  total={totalNote}
                  pageSize={numberPage}
                  showSizeChanger={false}
                  onChange={handlePagination}
                  current={currentPage}
                />
              </div>
            </div>
            <div className="company-view-contact">
              <Table
                columns={columns}
                loading={loadingContact}
                pagination={false}
                expandable={{
                  expandedRowRender: (record) => (
                    <p
                      style={{
                        margin: 0,
                      }}
                    >
                      <div
                        dangerouslySetInnerHTML={{
                          __html: record?.comments,
                        }}
                      ></div>
                    </p>
                  ),
                }}
                dataSource={listNote}
              />
            </div>
          </div>
        </div>
      </div>
      <ModalConfirmDeleteBullhorn
        action={actionDeleteNote}
        openModal={openModal}
        setOpenModal={setOpenModal}
        loadingDelete={loadingDelete}
      />
      <ModalEditEmail
        action={actionDeleteNote}
        openModal={openEditModal}
        setOpenModal={setOpenEditModal}
        loadingDelete={loadingDelete}
        defaultData={currentSelect}
        vacancy={vacancy}
        handleAfterLoading={() => {handleGetContacts(numberPage, 1); handleGetTotal()}}
        isCreateNew={isCreateNew}
        setIsCreateNew={setIsCreateNew}
        handleGetTotal={handleGetTotal}
        setCurrentSelect={setCurrentSelect}
      />
    </div>
  );
};

export default NoteTab;
