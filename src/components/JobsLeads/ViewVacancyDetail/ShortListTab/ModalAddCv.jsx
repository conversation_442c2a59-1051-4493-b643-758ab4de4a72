import React, { useEffect, useRef, useState } from 'react';
import {
  AutoComplete,
  Button,
  DatePicker,
  Input,
  Modal,
  Radio,
  Select,
  Spin,
  notification,
} from 'antd';
import {
  insertBullhorn,
  insertMassAdvance,
  searchBullhorn,
  searchBullhornData,
  upladteBullhorn,
} from '../../../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../../../hooks/useInfinitiveScroll';
import {
  CheckOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from '../../../BullHorn/BullhornSendEmailModal';

const ModalAddCv = (props) => {
  const {
    action,
    openModal,
    setOpenModal,
    loadingDelete,
    defaultData,
    vacancy,
    handleAfterLoading,
    isCreateNew,
    setIsCreateNew,
    handleGetContacts,
  } = props;

  const editorRef = useRef(null);
  const timeoutRef = useRef(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataContact, setDataContact] = useState([]);
  const [dataVacancy, setDataVacancy] = useState([]);
  const [dataCandidate, setDataCandidate] = useState([]);
  const [dataContent, setDataContent] = useState('');
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [dataCorporateUser, setDataCorporateUser] = useState([]);
  const [isLoading, setLoading] = useState(false);

  const { setValue, getValues, watch } = useForm();

  const handleSearchContact = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      10,
      searchText,
      null,
      isCreateNew ? '' : queryId
    );
    setDataContact(data?.result);
  };

  const handleSearchVacancy = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'JobOrder',
      0,
      10,
      searchText,
      null,
      queryId
    );
    setDataVacancy(data?.result);
  };

  const handleSearchCandidate = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn('Candidate', 0, 10, searchText, null);
    setDataCandidate(data?.result);
  };

  useEffect(() => {
    handleSearchContact(defaultData?.personReference?.id);
    handleSearchVacancy(vacancy?.id, 'a');
    handleSearchCandidate(vacancy?.id, 'a');
  }, []);

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  const handleSaveData = async () => {
    try {
      setLoading(true);
      const payload = {
        entity: 'Sendout',
        data: {
          candidate: [
            {
              id: 304318,
            },
          ],
          clientContact: [
            {
              id: 303525,
            },
          ],
          jobOrder: {
            id: 74770,
          },
          comments: 'hapo',
        },
      };
      const { data } = await insertMassAdvance(payload);
      notification.success({
        message: 'Data Update Success',
      });
      setLoading(false);
      setOpenModal(false);
      handleGetContacts();
    } catch (e) {
      setLoading(false);
      notification.error({
        message: 'Error inserting',
      });
    }
  };

  return (
    <>
      <Modal
        open={openModal}
        width={900}
        title={'Add CV Sent'}
        footer={
          <div>
            <Button onClick={() => setOpenModal(false)}>Cancel</Button>
            <Button
              type="primary"
              onClick={() => {
                handleSaveData();
              }}
              loading={isLoading}
            >
              Save
            </Button>
          </div>
        }
        onOk={() => {
          action();
          setOpenModal(false);
        }}
        onCancel={() => {
          setOpenModal(false);
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Candidate</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataCandidate.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                    </div>
                  ),
                }))}
                value={watch('candidate')}
                onSearch={(searchText) => {
                  setValue('candidateId', null);
                  setValue('candidate', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchCandidate(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataCandidate.find((co) => co.id === value);
                  setValue('candidateId', value);
                  setValue('candidate', contact?.name);
                }}
              >
                {getValues().candidate}
              </AutoComplete>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Contact</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Input
                value={`${vacancy?.clientContact?.firstName}  ${vacancy?.clientContact?.lastName}`}
                disabled={true}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Vacancy</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataVacancy.map((item) => ({
                  value: item?.id,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base">
                            {item?.title}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex">
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <PhoneOutlined /> {item?.phone ? item.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <InfoCircleOutlined />{' '}
                              {item?.status ? item.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <EnvironmentOutlined />
                              {item?.max_address_city ?? '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
                value={watch('vacancy')}
                onSearch={(searchText) => {
                  setValue('vacancyId', null);
                  setValue('vacancy', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchVacancy(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataVacancy.find((co) => co.id === value);
                  setValue('vacancyId', value);
                  setValue('vacancy', contact?.title);
                }}
              >
                {getValues().vacancy}
              </AutoComplete>
            </div>
          </div>
        </div>
        <div style={{ marginTop: '30px' }} className="mail-content-container">
          <div>Comments</div>
          <CKEditor
            ref={editorRef}
            editor={ClassicEditor}
            config={{
              ...editorConfig,
              toolbar: {
                ...editorConfig.toolbar,
                shouldNotGroupWhenFull: false,
                items: [...editorConfig.toolbar.items].filter(
                  (item) => item !== 'insertMergeField'
                ),
              },
              mergeFields: null
            }}
            data={dataContent || ''}
            onChange={handleCKEditorChange}
            onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
          />
        </div>
      </Modal>
    </>
  );
};
export default ModalAddCv;
