import React, { useState } from 'react';
import { Button, Modal } from 'antd';
const ModalConfirmDeleteBullhorn = (props) => {
  const { action, openModal, setOpenModal, loadingDelete, property = "Notes" } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Modal
        open={openModal}
        footer={
          <div>
            <Button onClick={() => setOpenModal(false)}>Cancel</Button>
            <Button
              type="primary"
              onClick={() => {
                action();
              }}
              loading={loadingDelete}
            >
              Delete
            </Button>
          </div>
        }
        onOk={() => {
          action();
          setOpenModal(false);
        }}
        onCancel={() => {
          setOpenModal(false);
        }}
      >
        <p>Are you sure you want to delete this {property}</p>
      </Modal>
    </>
  );
};
export default ModalConfirmDeleteBullhorn;
