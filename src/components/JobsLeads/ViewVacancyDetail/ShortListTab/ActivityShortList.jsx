import {
  CalendarOutlined,
  CaretRightOutlined,
  CheckSquareOutlined,
  EditOutlined,
  FolderOutlined,
  MailOutlined,
  StarOutlined,
  UnorderedListOutlined,
  UserOutlined,
  WifiOutlined,
} from '@ant-design/icons';
import { faStar } from '@fortawesome/free-regular-svg-icons';
import { faEarthOceania, faMicrophone, faPaperPlane } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Collapse, theme } from 'antd';
import ShortListItem from '../ShortListItem';
import InterviewAppointments from '../InterviewAppointments';
import Placement from '../Placement';
import Sendout from '../Sendout';

const ActivityShortList = (props) => {
  const { token } = theme.useToken();
  const { vacancyId, vacancy, defaultCollapse, setDefaultCollapse, totalShortLis, chooseCollapse, totalShortList, setChooseCollapse} = props;

  return (
    <div className="custom-collapse-activity">
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <FontAwesomeIcon icon={faEarthOceania} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        collapsible="disabled"
        items={[
          {
            key: '1',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faEarthOceania} />
                  </div>
                  <div class="ml-2.5">Online Response</div>
                </div>
                <div class="text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <p>{'coming soon'}</p>,
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'shortList',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faStar} />
                  </div>
                  <div class="ml-2.5">Shortlist</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <div><ShortListItem  vacancyId={vacancyId} vacancy={vacancy} totalShortList={totalShortList}/></div>,
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'CV Sent',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faPaperPlane} />
                  </div>
                  <div class="ml-2.5">CV Sent</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <div><Sendout vacancyId={vacancyId} vacancy={vacancy} totalShortList={totalShortList}/></div>,
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'Interview',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faMicrophone} />
                  </div>
                  <div class="ml-2.5">Interview Appointments</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <div><InterviewAppointments  vacancyId={vacancyId} vacancy={vacancy} totalShortList={totalShortList}/></div>,
            showArrow: false,
          },
        ]}
      />
      <Collapse
        bordered={false}
        expandIcon={({ isActive }) => (
          <CaretRightOutlined rotate={isActive ? 90 : 0} />
        )}
        style={{
          background: token.colorBgContainer,
        }}
        onChange={(e) => setChooseCollapse(e)}
        activeKey={chooseCollapse}
        items={[
          {
            key: 'Placement',
            label: (
              <div class="flex justify-between items-center">
                <div class="flex items-center text-blue-500 text-lg font-medium">
                  <div>
                    <FontAwesomeIcon icon={faStar} />
                  </div>
                  <div class="ml-2.5">Placement</div>
                </div>
                <div class="text-blue-500 text-xs font-medium">
                  <div>LAST ACTIVITY</div>
                  <div class="text-xs">09/08/2024</div>
                </div>
              </div>
            ),
            children: <div><Placement  vacancyId={vacancyId} vacancy={vacancy} totalShortList={totalShortList}/></div>,
            showArrow: false,
          },
        ]}
      />
    </div>
  );
};

export default ActivityShortList;