import React, { useEffect, useRef, useState } from 'react';
import {
  AutoComplete,
  Button,
  Input,
  Modal,
  Select,
  Spin,
  notification,
} from 'antd';
import {
  insertBullhorn,
  searchBullhorn,
  searchBullhornData,
  upladteBullhorn,
} from '../../../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../../../hooks/useInfinitiveScroll';
import {
  CheckOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { useForm } from 'react-hook-form';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from '../../../BullHorn/BullhornSendEmailModal';
import { CKEditor } from '@ckeditor/ckeditor5-react';

export const ACTION_NOTE = [
  { label: 'Dial', value: 'Dial' },
  { label: 'Prescreen', value: 'Prescreen' },
  { label: 'BD Call', value: 'BD Call' },
  { label: 'Interview Notes', value: 'Interview Notes' },
  { label: 'Job Response', value: 'Job Response' },
  { label: 'Left Voicemail', value: 'Left Voicemail' },
  { label: 'Email', value: 'Email' },
  { label: 'General', value: 'General' },
];

const ModalEditEmail = (props) => {
  const {
    openModal,
    setOpenModal,
    loadingDelete,
    defaultData,
    vacancy,
    handleAfterLoading,
    isCreateNew,
    setIsCreateNew,
    setCurrentSelect,
  } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const editorRef = useRef(null);
  const [dataContact, setDataContact] = useState([]);
  const [dataVacancy, setDataVacancy] = useState([]);
  const [dataContent, setDataContent] = useState('');
  const [loadingUpdate, setLoadingUpdate] = useState(false);

  const { setValue, getValues, watch } = useForm();

  const [initData, setInitData] = useState({
    aboutWho: '',
  });

  const timeoutRef = useRef(null);

  const handleSearchContact = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      10,
      searchText,
      null,
      isCreateNew ? '' : queryId
    );
    setDataContact(data?.result);
  };

  const handleSearchVacancy = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'JobOrder',
      0,
      10,
      searchText,
      null,
      queryId
    );
    setDataVacancy(data?.result);
  };

  useEffect(() => {
    if (!isCreateNew) {
      setDataContent(defaultData?.comments ?? '');
      setValue('action', defaultData?.action);
      handleSearchContact(defaultData?.personReference?.id);
      setValue('vacancy', vacancy?.title);
      setValue('vacancyId', vacancy?.id);
      setValue('contact', defaultData?.personReference?.name);
      setValue('contactId', defaultData?.personReference?.id);
    }
    handleSearchVacancy(vacancy?.id);
  }, [defaultData, vacancy, openModal]);

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  const handleSaveData = async (data) => {
    if (dataContent === '') {
      notification.error({
        message: 'Content cannot be empty',
      });
      return;
    }
    if (!watch('action')) {
      notification.error({
        message: 'Action cannot be empty',
      });
      return;
    }
    if (!watch('contactId')) {
      notification.error({
        message: 'Contact cannot be empty',
      });
      return;
    }
    try {
      setLoadingUpdate(true);
      const payload = {
        entityName: 'Note',
        action: watch('action'),
        clientContacts: { replaceAll: [watch('contactId')] },
        comments: dataContent,
        jobOrders: { replaceAll: [watch('vacancyId')] },
        personReference: {
          firstName: watch('firstName'),
          id: watch('contactId'),
          lastName: watch('lastName'),
          _subtype: 'ClientContact',
        },
        id: defaultData?.id,
      };

      const dataUpdate = await upladteBullhorn(defaultData?.id, payload);
      notification.success({
        message: 'Updated Success',
      });
      setLoadingUpdate(false);
      setOpenModal(false);
      handleResetForm();
      handleAfterLoading();
    } catch (err) {
      notification.error({
        message: 'Something went wrong',
      });
    }
    setLoadingUpdate(false);
  };

  const insertData = async () => {
    if (dataContent === '' || !dataContent) {
      notification.error({
        message: 'Content cannot be empty',
      });
      return;
    }
    if (!watch('action')) {
      notification.error({
        message: 'Action cannot be empty',
      });
      return;
    }
    if (!watch('contactId')) {
      notification.error({
        message: 'Contact cannot be empty',
      });
      return;
    }
    try {
      setLoadingUpdate(true);
      const payload = {
        entityName: 'Note',
        action: watch('action'),
        comments: dataContent,
        jobOrders: [{ id: watch('vacancyId') }],
        personReference: {
          firstName: watch('firstName'),
          id: watch('contactId'),
          lastName: watch('lastName'),
          _subtype: 'ClientContact',
        },
      };

      const dataUpdate = await insertBullhorn(payload);
      notification.success({
        message: 'Updated Success',
      });
      setLoadingUpdate(false);
      setOpenModal(false);
      setIsCreateNew(false);
      handleAfterLoading();
      handleResetForm();
    } catch (err) {
      setLoadingUpdate(false);
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleResetForm = () => {
    setValue('contactId', null);
    setValue('contact', null);
    setValue('action', null);
    setValue('vacancyId', null);
    setValue('vacancy', null);
    setValue('internalId', null);
    setValue('internal', null);
    setDataContent('');
    setLoadingUpdate(false);
    setIsCreateNew(false);
    setCurrentSelect(null);
    setDataContent('');
  };

  return (
    <>
      <Modal
        open={openModal}
        width={1300}
        footer={
          <div>
            <Button
              onClick={() => {
                setOpenModal(false);
                handleResetForm();
              }}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              onClick={() => {
                isCreateNew ? insertData() : handleSaveData();
              }}
              loading={loadingUpdate}
            >
              Save
            </Button>
          </div>
        }
        onOk={() => {
          handleResetForm();
          setOpenModal(false);
        }}
        onCancel={() => {
          handleResetForm();
          setOpenModal(false);
        }}
      >
        <div className="custom-vacancy-body mail-content-container">
          <div
            style={{
              width: '48%',
            }}
          >
            <CKEditor
              ref={editorRef}
              editor={ClassicEditor}
              config={{
                ...editorConfig,
                toolbar: {
                  ...editorConfig.toolbar,
                  shouldNotGroupWhenFull: false,
                  items: [...editorConfig.toolbar.items].filter(
                    (item) => item !== 'insertMergeField'
                  ),
                },
                mergeFields: null,
              }}
              data={dataContent || ''}
              onChange={handleCKEditorChange}
              onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
            />
          </div>
          <div style={{ width: '48%' }}>
            <div className="custom-vacancy-form">
              About Who <span style={{ color: 'red' }}>*</span>
              <AutoComplete
                options={dataContact.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <MailOutlined />
                            {option?.email ? option?.email : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <PhoneOutlined />
                            {option.phone ? option.phone : '-'}
                          </span>
                          <span className="text-gray-500 text-xs">
                            <ContactsOutlined />{' '}
                            {option.occupation ? option.occupation : '-'}
                          </span>
                        </div>
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <EnvironmentOutlined />
                            {option.address &&
                            option.address.city &&
                            option.address.state
                              ? `${option.address.city}, ${option.address.state}`
                              : option.address && option.address.city
                                ? option.address.city
                                : option.address && option.address.state
                                  ? option.address.state
                                  : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <InfoCircleOutlined />{' '}
                            {option.status ? option.status : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  email: option.email,
                }))}
                value={watch('contact')}
                onSearch={(searchText) => {
                  setValue('contactId', null);
                  setValue('contact', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchContact(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataContact.find((co) => co.id === value);
                  setValue('contactId', value);
                  setValue('contact', contact?.name);
                  setValue('firstName', contact?.firstName);
                  setValue('lastName', contact?.lastName);
                }}
              >
                {getValues().contact}
              </AutoComplete>
            </div>
            <div className="custom-vacancy-form mt-10">
              Action <span style={{ color: 'red' }}>*</span>
              <Select
                options={ACTION_NOTE}
                onSelect={(value, record) => setValue('action', value)}
                value={watch('action')}
              />
            </div>
            <div className="custom-vacancy-form mt-10">
              Additional References
              <AutoComplete
                options={dataVacancy.map((item) => ({
                  value: item?.id,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base">
                            {item?.title}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex">
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <PhoneOutlined /> {item?.phone ? item.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <InfoCircleOutlined />{' '}
                              {item?.status ? item.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <EnvironmentOutlined />
                              {item?.max_address_city ?? '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
                value={watch('vacancy')}
                onSearch={(searchText) => {
                  setValue('vacancyId', null);
                  setValue('vacancy', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchVacancy(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataVacancy.find((co) => co.id === value);
                  setValue('vacancyId', value);
                  setValue('vacancy', contact?.title);
                }}
              >
                {getValues().vacancy}
              </AutoComplete>
            </div>
            <div className="mt-10 custom-vacancy-form">
              Schedule Next Action
              <Select
                options={[
                  { label: 'None', value: 'none' },
                  { label: 'Appointment', value: 'appointment' },
                  { label: 'Task', value: 'task' },
                ]}
                defaultValue={'None'}
              />
            </div>
            <div className="custom-vacancy-form-title mt-10">
              Email Notification
            </div>
            <div className="custom-vacancy-form mt-10">
              Internal User
              <AutoComplete
                options={dataContact.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <MailOutlined />
                            {option?.email ? option?.email : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <PhoneOutlined />
                            {option.phone ? option.phone : '-'}
                          </span>
                          <span className="text-gray-500 text-xs">
                            <ContactsOutlined />{' '}
                            {option.occupation ? option.occupation : '-'}
                          </span>
                        </div>
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <EnvironmentOutlined />
                            {option.address &&
                            option.address.city &&
                            option.address.state
                              ? `${option.address.city}, ${option.address.state}`
                              : option.address && option.address.city
                                ? option.address.city
                                : option.address && option.address.state
                                  ? option.address.state
                                  : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <InfoCircleOutlined />{' '}
                            {option.status ? option.status : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  email: option.email,
                }))}
                value={watch('internal')}
                onSearch={(searchText) => {
                  setValue('internalId', null);
                  setValue('internal', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchContact(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataContact.find((co) => co.id === value);
                  setValue('internalId', value);
                  setValue('internal', contact?.name);
                }}
              >
                {getValues().internal}
              </AutoComplete>
            </div>
            {/* <div className="custom-vacancy-form mt-10">
              Distribution List
              <Select />
            </div> */}
          </div>
        </div>
      </Modal>
    </>
  );
};
export default ModalEditEmail;
