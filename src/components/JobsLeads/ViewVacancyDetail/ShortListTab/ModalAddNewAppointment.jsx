import React, { useEffect, useRef, useState } from 'react';
import {
  AutoComplete,
  Button,
  DatePicker,
  Input,
  Modal,
  Radio,
  Select,
  Spin,
  notification,
} from 'antd';
import {
  insertBullhorn,
  searchBullhorn,
  searchBullhornData,
  upladteBullhorn,
} from '../../../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../../../hooks/useInfinitiveScroll';
import {
  CheckOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from '../../../BullHorn/BullhornSendEmailModal';

const REMINDER_OPTIONS = [
  { label: 'Never', value: 0 },
  { label: '5 Min', value: 5 },
  { label: '10 Min', value: 10 },
  { label: '15 Min', value: 15 },
  { label: '30 Min', value: 30 },
  { label: '1 Hour', value: 60 },
  { label: '2 Hours', value: 2 * 60 },
  { label: '3 Hours', value: 3 * 60 },
  { label: '4 Hours', value: 4 * 60 },
  { label: '5 Hours', value: 5 * 60 },
  { label: '6 Hours', value: 6 * 60 },
  { label: '7 Hours', value: 7 * 60 },
  { label: '8 Hours', value: 8 * 60 },
  { label: '9 Hours', value: 9 * 60 },
  { label: '10 Hours', value: 10 * 60 },
  { label: '11 Hours', value: 11 * 60 },
  { label: '0.5 Days', value: 12 * 60 }, // 12 Hours
  { label: '1 Day', value: 24 * 60 }, // 24 Hours
  { label: '2 Days', value: 2 * 24 * 60 }, // 48 Hours
  { label: '3 Days', value: 3 * 24 * 60 }, // 72 Hours
  { label: '4 Days', value: 4 * 24 * 60 }, // 96 Hours
  { label: '5 Days', value: 5 * 24 * 60 }, // 120 Hours
  { label: '7 Days', value: 7 * 24 * 60 }, // 168 Hours
];

const TYPE_OPTIONS = [
  { label: 'Meeting', value: 'Meeting' },
  { label: 'Interview', value: 'Interview' },
  { label: 'Client Visit', value: 'Client Visit' },
  { label: 'Attendee', value: 'Attendee' },
  { label: 'Lunch', value: 'Lunch' },
  { label: 'Personal', value: 'Personal' },
  { label: 'Other', value: 'Other' },
];

const COMMUNICATION_OPTIONS = [
  { label: 'Phone', value: 'Phone' },
  { label: 'Offsite', value: 'Offsite' },
  { label: 'Onsite', value: 'Onsite' },
];

const ModalAddNewAppointment = (props) => {
  const {
    action,
    openModal,
    setOpenModal,
    loadingDelete,
    defaultData,
    vacancy,
    handleAfterLoading,
    isCreateNew,
    setIsCreateNew,
    handleGetContacts,
  } = props;

  const editorRef = useRef(null);
  const timeoutRef = useRef(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataContact, setDataContact] = useState([]);
  const [dataVacancy, setDataVacancy] = useState([]);
  const [dataContent, setDataContent] = useState('');
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [dataCorporateUser, setDataCorporateUser] = useState([]);
  const [isLoading, setLoading] = useState(false);

  const { setValue, getValues, watch } = useForm();

  const handleSearchContact = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      10,
      searchText,
      null,
      isCreateNew ? '' : queryId
    );
    setDataContact(data?.result);
  };

  const handleSearchVacancy = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'JobOrder',
      0,
      10,
      searchText,
      null,
      queryId
    );
    setDataVacancy(data?.result);
  };

  const handleSearchCorporateUser = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn(
      'CorporateUser',
      0,
      10,
      searchText?.trim() ? searchText : 'a',
      null,
      queryId
    );
    setDataCorporateUser(data?.result);
  };

  useEffect(() => {
    handleSearchContact(defaultData?.personReference?.id);
    handleSearchVacancy(vacancy?.id);
    handleSearchCorporateUser();
  }, [vacancy, defaultData]);

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  const handleSaveData = async () => {
    try {
      setLoading(true);
      const payload = {
        entityName: 'Appointment',
        subject: watch('subject'),
        type: watch('type'),
        isPrivate: getValues('visibility'),
        dateBegin: getValues('startDate'),
        dateEnd: getValues('endDate'),
        notificationMinutes: getValues('reminder'),
        communicationMethod: getValues('communication'),
        location: getValues('location'),
        description: dataContent,
        jobOrder: {
          id: getValues('vacancyId'),
        },
        notifyAttendees: getValues('notifyAttendees'),
        owner: {
          id: getValues('ownerId'),
        },
        guests: [
          {
            id: getValues('guestId'),
          },
        ],
      };
      const { data } = await insertBullhorn(payload);
      notification.success({
        message: 'Data Update Success',
      });
      setLoading(false);
      setOpenModal(false);
      handleGetContacts();
    } catch (e) {
      setLoading(false);
      notification.error({
        message: 'Error inserting',
      });
    }
  };

  return (
    <>
      <Modal
        open={openModal}
        width={900}
        title={'Add Appointment'}
        footer={
          <div>
            <Button onClick={() => setOpenModal(false)}>Cancel</Button>
            <Button
              type="primary"
              onClick={() => {
                handleSaveData();
              }}
              loading={isLoading}
            >
              Save
            </Button>
          </div>
        }
        onOk={() => {
          action();
          setOpenModal(false);
        }}
        onCancel={() => {
          setOpenModal(false);
        }}
      >
        <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
          <div style={{ width: '15%' }}>Notify Attendees</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <Radio.Group
              defaultValue="true"
              buttonStyle="solid"
              onChange={(e) =>
                setValue(
                  'notifyAttendees',
                  e.target.value === 'true' ? true : false
                )
              }
            >
              <Radio.Button value="true">Yes</Radio.Button>
              <Radio.Button value="false">No</Radio.Button>
            </Radio.Group>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Subject</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Input
                placeholder=""
                onChange={(e) => setValue('subject', e.target.value)}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Owner</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataCorporateUser.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <MailOutlined />
                            {option?.email ? option?.email : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  email: option.email,
                }))}
                value={watch('owner')}
                onSearch={(searchText) => {
                  setValue('ownerId', null);
                  setValue('owner', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchContact(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataCorporateUser.find(
                    (co) => co.id === value
                  );
                  setValue('ownerId', value);
                  setValue('owner', contact?.name);
                }}
              >
                {getValues().owner}
              </AutoComplete>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Attendees</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataContact.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <MailOutlined />
                            {option?.email ? option?.email : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <PhoneOutlined />
                            {option.phone ? option.phone : '-'}
                          </span>
                          <span className="text-gray-500 text-xs">
                            <ContactsOutlined />{' '}
                            {option.occupation ? option.occupation : '-'}
                          </span>
                        </div>
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <EnvironmentOutlined />
                            {option.address &&
                            option.address.city &&
                            option.address.state
                              ? `${option.address.city}, ${option.address.state}`
                              : option.address && option.address.city
                                ? option.address.city
                                : option.address && option.address.state
                                  ? option.address.state
                                  : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <InfoCircleOutlined />{' '}
                            {option.status ? option.status : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  email: option.email,
                }))}
                value={watch('guest')}
                onSearch={(searchText) => {
                  setValue('guestId', null);
                  setValue('guest', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchContact(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataContact.find((co) => co.id === value);
                  setValue('guestId', value);
                  setValue('guest', contact?.name);
                }}
              >
                {getValues().guest}
              </AutoComplete>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Vacancy</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataVacancy.map((item) => ({
                  value: item?.id,
                  label: (
                    <>
                      <div className="grid">
                        <div className="flex justify-between">
                          <span className="text-base font-base">
                            {item?.title}
                          </span>
                        </div>
                        <div className="contact-details">
                          <div className="flex">
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <PhoneOutlined /> {item?.phone ? item.phone : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <InfoCircleOutlined />{' '}
                              {item?.status ? item.status : '-'}
                            </span>
                            <span className="text-gray-500 text-xs min-w-[200px]">
                              <EnvironmentOutlined />
                              {item?.max_address_city ?? '-'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ),
                }))}
                value={watch('vacancy')}
                onSearch={(searchText) => {
                  setValue('vacancyId', null);
                  setValue('vacancy', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchVacancy(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataVacancy.find((co) => co.id === value);
                  setValue('vacancyId', value);
                  setValue('vacancy', contact?.title);
                }}
              >
                {getValues().vacancy}
              </AutoComplete>
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Type</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Select
                options={TYPE_OPTIONS}
                onChange={(e) => setValue('type', e)}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Visibility</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <Radio.Group
              defaultValue="yes"
              buttonStyle="solid"
              onChange={(e) =>
                setValue('visibility', e?.target?.value == 'yes' ? false : true)
              }
            >
              <Radio.Button value="yes">Public</Radio.Button>
              <Radio.Button value="no">Private</Radio.Button>
            </Radio.Group>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Start Date</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <DatePicker
                onChange={(date) =>
                  setValue('startDate', dayjs(date).valueOf())
                }
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>End Date</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <DatePicker
                onChange={(date) => setValue('endDate', dayjs(date).valueOf())}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Reminder</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Select
                options={REMINDER_OPTIONS}
                onChange={(e) => {
                  setValue('reminder', e);
                }}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Communication Method</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Select
                options={COMMUNICATION_OPTIONS}
                onChange={(e) => setValue('communication', e)}
              />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Location</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Input
                placeholder=""
                onChange={(e) => setValue('location', e.target.value)}
              />
            </div>
          </div>
        </div>
        <div style={{ marginTop: '30px' }} className="mail-content-container">
          <div>Comments</div>

          <CKEditor
            ref={editorRef}
            editor={ClassicEditor}
            config={{
              ...editorConfig,
              toolbar: {
                ...editorConfig.toolbar,
                shouldNotGroupWhenFull: false,
                items: [...editorConfig.toolbar.items].filter(
                  (item) => item !== 'insertMergeField'
                ),
              },
              mergeFields: null
            }}
            data={dataContent || ''}
            onChange={handleCKEditorChange}
            onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
          />
        </div>
      </Modal>
    </>
  );
};
export default ModalAddNewAppointment;
