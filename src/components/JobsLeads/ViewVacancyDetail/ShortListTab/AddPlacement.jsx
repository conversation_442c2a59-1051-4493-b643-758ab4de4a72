import React, { useEffect, useRef, useState } from 'react';
import {
  AutoComplete,
  Button,
  DatePicker,
  Input,
  Modal,
  Radio,
  Select,
  Spin,
  notification,
} from 'antd';
import {
  insertBullhorn,
  searchBullhorn,
  searchBullhornData,
  upladteBullhorn,
} from '../../../../services/bullhorn';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from '../../../BullHorn/BullhornSendEmailModal';

const AddPlacement = (props) => {
  const {
    action,
    openModal,
    setOpenModal,
    loadingDelete,
    defaultData,
    vacancy,
    handleAfterLoading,
    isCreateNew,
    setIsCreateNew,
    handleGetContacts,
  } = props;

  const editorRef = useRef(null);
  const timeoutRef = useRef(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataContact, setDataContact] = useState([]);
  const [dataVacancy, setDataVacancy] = useState([]);
  const [dataCandidate, setDataCandidate] = useState([]);
  const [dataContent, setDataContent] = useState('');
  const [loadingUpdate, setLoadingUpdate] = useState(false);
  const [dataCorporateUser, setDataCorporateUser] = useState([]);
  const [isLoading, setLoading] = useState(false);

  const { setValue, getValues, watch } = useForm();

  const handleSearchCandidate = async (queryId, searchText = '') => {
    const { data } = await searchBullhorn('Candidate', 0, 10, searchText, null);
    setDataCandidate(data?.result);
  };

  useEffect(() => {
    handleSearchCandidate(vacancy?.id, 'a');
  }, []);

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  const handleSaveData = async () => {
    try {
      setLoading(true);
      const payload = {
        entityName: 'Placement',
        candidate: {
          id: 304318,
        },
        jobOrder: {
          id: vacancy?.id,
        },
        id: vacancy?.id,
        employmentType: 'Permanent',
        salary: 0,
        payRate: 0,
        title: vacancy?.title,
        comments: dataContent,
        status: 'Placed',
        entity: 'Placement',
      };
      const { data } = await insertBullhorn(payload);
      notification.success({
        message: 'Data Update Success',
      });
      setLoading(false);
      setOpenModal(false);
      handleGetContacts();
    } catch (e) {
      setLoading(false);
      notification.error({
        message: 'Error inserting',
      });
    }
  };

  return (
    <>
      <Modal
        open={openModal}
        width={900}
        title={'Add CV Sent'}
        footer={
          <div>
            <Button onClick={() => setOpenModal(false)}>Cancel</Button>
            <Button
              type="primary"
              onClick={() => {
                handleSaveData();
              }}
              loading={isLoading}
            >
              Save
            </Button>
          </div>
        }
        onOk={() => {
          action();
          setOpenModal(false);
        }}
        onCancel={() => {
          setOpenModal(false);
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Reminder</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <Select value={'Placed'} disabled={true} />
            </div>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
            marginTop: '20px',
          }}
        >
          <div style={{ width: '15%' }}>Candidate</div>
          <div style={{ marginLeft: '30px', width: '80%' }}>
            <div className="custom-vacancy-form">
              <AutoComplete
                options={dataCandidate.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                    </div>
                  ),
                }))}
                value={watch('candidate')}
                onSearch={(searchText) => {
                  setValue('candidateId', null);
                  setValue('candidate', searchText);

                  if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                  }
                  timeoutRef.current = setTimeout(() => {
                    handleSearchCandidate(null, searchText);
                  }, 500);
                }}
                onSelect={(value, record) => {
                  const contact = dataCorporateUser.find(
                    (co) => co.id === value
                  );
                  setValue('candidateId', value);
                  setValue('candidate', contact?.name);
                }}
              >
                {getValues().owner}
              </AutoComplete>
            </div>
          </div>
        </div>

        <div style={{ marginTop: '30px' }} className="mail-content-container">
          <div>Comments</div>

          <CKEditor
            ref={editorRef}
            editor={ClassicEditor}
            config={{
              ...editorConfig,
              toolbar: {
                ...editorConfig.toolbar,
                items: [...editorConfig.toolbar.items].filter(
                  (item) => item !== 'insertMergeField'
                ),
                shouldNotGroupWhenFull: false,
              },
              mergeFields: null
            }}            
            data={dataContent || ''}
            onChange={handleCKEditorChange}
            onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
          />
        </div>
      </Modal>
    </>
  );
};
export default AddPlacement;
