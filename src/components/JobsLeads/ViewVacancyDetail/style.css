.company-status-container .ant-select-selector{
    padding: 0px !important;
    border: none !important;
    border-bottom: 1px solid #dbdbdb !important;
    border-radius: 0 !important;
}

.company-status-container-status .ant-select-selector{
    padding: 0px !important;
    border: none !important;
    border-bottom: 1px solid #dbdbdb !important;
    border-radius: 0 !important;
    width: 200px;
}

.company-status-container .ant-picker-input{
    padding: 0px !important;
    border: none !important;
    border-bottom: 1px solid #dbdbdb !important;
    border-radius: 0 !important;
    width: 200px;
}

.company-date-container{
    border: none !important;
    margin-left: -10px;
}

.view-company-container .ant-drawer-body {
    background-color: #4a89dc15;
    padding: 0;
}


.view-company-container .ant-tabs-nav{
    background-color: white;
    padding: 0 20px
}
.vacancy-status-container-status {
    width: 200px;
}

.vacancy-status-container-status  .ant-select-selector{
    padding: 0px !important;
    border: none !important;
    border-bottom: 1px solid #dbdbdb !important;
    border-radius: 0 !important;
    width: 200px;
}

.custom-vacancy-form .ant-select{
    width: 100%;
}

.custom-vacancy-form .ant-select-selector{
    border: none !important;
    border-bottom: 1px solid #ccc !important;
    border-radius: 0;
}


.custom-vacancy-form .ant-input {
    border: none !important;
    border-bottom: 1px solid #ccc !important;
    border-radius: 0;
}

.custom-vacancy-form .ant-picker  {
    border: none !important;
    border-bottom: 1px solid #ccc !important;
    border-radius: 0;
}

.custom-vacancy-form  input {
    border: none !important;
    border-radius: 0;
}

.custom-vacancy-form-title {
    padding: 10px;
    font-weight: 600;
    background-color: #eee;
}

.custom-vacancy-body {
    display: flex;
    justify-content: space-between;
}
