import {
  HolderOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Button, Tooltip } from 'antd';

const Card = ({
  title,
  children,
  refetch,
  loading,
  description,
  addNew = null,
}) => {
  return (
    <div className="bg-white rounded-md shadow-md">
      <div className="flex justify-between items-center border-b w-full p-3 ">
        <div className="flex gap-1 items-center">
          <Tooltip title={description}>
            <HolderOutlined className="text-[#dbdbdb] font-bold" />
          </Tooltip>
          <div className="text-xl font-medium">{title}</div>
        </div>
        <div className="flex items-center">
          {addNew && (
            <Tooltip title={`Add New ${title}`}>
              <Button
                disabled={loading}
                onClick={addNew}
                type="text"
                className="font-normal text-[#4a89dc] flex items-center inline"
              >
                <span>ADD</span>
                <PlusOutlined className="font-extrabold text-[#4a89dc]" />
              </Button>
            </Tooltip>
          )}
          {refetch && (
            <Tooltip title="Refresh data">
              <Button
                loading={loading}
                onClick={refetch}
                type="text"
                icon={<ReloadOutlined className="font-extrabold" />}
              ></Button>
            </Tooltip>
          )}
        </div>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default Card;
