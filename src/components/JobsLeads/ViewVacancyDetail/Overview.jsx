import { Button, DatePicker, Select, Spin, Table, notification } from 'antd';
import Card from './Card';
import { useEffect, useState } from 'react';
import { searchBullhorn, upladteBullhorn } from '../../../services/bullhorn';
import moment from 'moment';
import customParseFormat from 'dayjs/plugin/customParseFormat';

import {
  AppstoreAddOutlined,
  CalendarOutlined,
  HeartOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  UserOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import Step from '../ViewCompanyDetail/Step';

const ENTITY_NAME = {
  VACANCY: 'JobOrder',
  OPPORTUNITY: 'Opportunity',
  LEAD: 'Lead',
};

const LIST_ITEM_STEPS = [
  {
    title: 'Shortlist',
    text_tooltip: 'Add Shortlist',
  },
  {
    title: 'CV Sent',
    text_tooltip: 'Add CV Sent',
  },
  {
    title: 'Interview',
    text_tooltip: 'Schedule Interview',
  },
  {
    title: 'Offer Extended',
    text_tooltip: 'Add Shortlist',
  },
  {
    title: 'Placement',
    text_tooltip: 'Add Placement',
  },
];

const Overview = ({
  vacancy,
  vacancyId,
  loadingAll,
  setLoadingFetch,
  setUpdateData,
  updateData,
  keyActive,
  setKeyActive,
  totalShortList,
  setChooseCollapse
}) => {
  //   Loading
  const dateFormat = 'YYYY/MM/DD';
  dayjs.extend(customParseFormat);

  const handleUpdateData = async (payload) => {
    setLoadingFetch(true);
    const dataUpdate = {
      ...payload,
      entityName: 'JobOrder',
    };
    try {
      const { data } = await upladteBullhorn(vacancyId, dataUpdate);
      if (data) {
        notification.success({
          message: 'Updated Success',
        });
      }
    } catch (err) {
      notification.error({
        message: 'Something went wrong',
      });
    }
    setLoadingFetch(false);
  };

  const handleClickStep = (item) => {
    setKeyActive("shortList")
    if(item?.title == "Shortlist") {
      setChooseCollapse("shortList")
    }
    if(item?.title == "CV Sent") {
      setChooseCollapse("CV Sent")
     }
     if(item?.title == "Interview") {
      setChooseCollapse("Interview")
     }
     if(item?.title == "Offer Extended") {
      setChooseCollapse("shortList")
     }
     if(item?.title == "Placement") {
      setChooseCollapse("Placement")
     }
  };

  return (
    <div>
       <div className="step-vacancy-overview-container">
       <Step listItem={LIST_ITEM_STEPS} onClick={handleClickStep} stepHasValue={[{key: "Shortlist", value: totalShortList}]}></Step>
      </div>
      <div className="px-5 grid grid-cols-2 gap-3 pb-5">
        <Card
          loading={loadingAll}
          title={'Contact Details'}
          key={'details'}
          description={'Contact details'}
        >
          {loadingAll && (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <Spin />
            </div>
          )}
          {!loadingAll && (
            <div className="flex flex-col gap-3">
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Name</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.firstName +
                    ' ' +
                    vacancy?.clientContact?.lastName}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Company</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.clientCorporation?.name}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Mobile Phone</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.mobile}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Work Phone</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.phone}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Work Email</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.email}
                </div>
              </div>
            </div>
          )}
        </Card>

        <Card
          loading={loadingAll}
          // refetch={() =>
          //   getData(
          //     ENTITY_NAME.OPPORTUNITY,
          //     setOpportunityData,
          //     setOpportunityLoading
          //   )
          // }
          title={'Details'}
          key={'details'}
          description={'Details'}
          addNew={() => {}}
        >
          {loadingAll && (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <Spin />
            </div>
          )}
          {!loadingAll && (
            <div className="flex flex-col gap-3">
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Contact</div>
                <div className="col-span-7">
                  {vacancy?.clientContact?.firstName +
                    ' ' +
                    vacancy?.clientContact?.lastName}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Client</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.name}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Employment Type</div>
                <Select
                  className="min-w-[90px]"
                  defaultValue={vacancy?.employmentType}
                  rootClassName="vacancy-status-container-status"
                  onChange={(e) => {
                    handleUpdateData({
                      employmentType: e,
                    });
                  }}
                  options={[
                    {
                      value: 'Temporary',
                      label: 'Temporary',
                    },
                    {
                      value: 'Contract',
                      label: 'Contract',
                    },
                    {
                      value: 'Permanent',
                      label: 'Permanent',
                    },
                    {
                      value: 'Fixed Term',
                      label: 'Fixed Term',
                    },
                    {
                      value: 'Opportunity',
                      label: 'Opportunity',
                    },
                  ]}
                />
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Consultant</div>
                <div className="col-span-7">
                  {vacancy?.owner?.firstName + ' ' + vacancy?.owner?.lastName}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Status</div>
                <div className="col-span-7">
                  <Select
                    className="min-w-[90px]"
                    value={updateData?.status ?? vacancy?.status}
                    rootClassName="company-status-container-status"
                    onChange={(e) => {
                      setUpdateData({
                        ...updateData,
                        status: e,
                      });
                      handleUpdateData({
                        status: e,
                      });
                    }}
                    options={[
                      {
                        value: 'Accepting Candidates',
                        label: 'Accepting Candidates',
                      },
                      {
                        value: 'Lead',
                        label: 'Lead',
                      },
                      {
                        value: 'HOT Lead',
                        label: 'HOT Lead',
                      },
                      {
                        value: 'Offer Out',
                        label: 'Offer Out',
                      },
                      {
                        value: 'Placed',
                        label: 'Placed',
                      },
                      {
                        value: 'Filled by Client',
                        label: 'Filled by Client',
                      },
                      {
                        value: 'Lost To Competitor',
                        label: 'Lost To Competitor',
                      },
                      {
                        value: 'Archive',
                        label: 'Archive',
                      },
                      {
                        value: 'On Hold',
                        label: 'On Hold',
                      },
                      {
                        value: 'Cancelled',
                        label: 'Cancelled',
                      },
                    ]}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Open/Closed</div>
                <div className="col-span-7">
                  {' '}
                  <Select
                    className="min-w-[90px]"
                    value={updateData?.isOpen ? 'Open' : 'Closed'}
                    rootClassName="company-status-container"
                    onChange={(e) => {
                      setUpdateData({
                        ...updateData,
                        isOpen: e,
                      });
                      handleUpdateData({
                        isOpen: e,
                      });
                    }}
                    options={[
                      {
                        value: true,
                        label: 'Open',
                      },
                      {
                        value: false,
                        label: 'Closed',
                      },
                    ]}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3"># of Openings</div>
                <div className="col-span-7">{vacancy?.numOpenings}</div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Start Date</div>
                <div className="col-span-7">
                  {/* {dayjs(vacancy?.startDate).format('YYYY/MM/DD')} */}
                  <DatePicker
                    className="min-w-[90px]"
                    defaultValue={
                      vacancy?.startDate
                        ? dayjs(dayjs(vacancy?.startDate).format(dateFormat))
                        : false
                    }
                    format={dateFormat}
                    rootClassName="company-date-container"
                    onChange={(date, dateString) => {
                      handleUpdateData({
                        startDate: new Date(dateString),
                      });
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Scheduled End Date</div>
                <div className="col-span-7">
                  {/* {dayjs(vacancy?.dateEnd).format('YYYY/MM/DD')} */}
                  <DatePicker
                    className="min-w-[90px]"
                    defaultValue={
                      vacancy?.dateEnd
                        ? dayjs(vacancy?.dateEnd, dateFormat)
                        : false
                    }
                    format={dateFormat}
                    rootClassName="company-date-container"
                    onChange={(date, dateString) => {
                      handleUpdateData({
                        dateEnd: new Date(dateString),
                      });
                    }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Date Added</div>
                <div className="col-span-7">
                  {dayjs(vacancy?.dateAdded).format('YYYY/MM/DD')}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Job Category</div>
                <div className="col-span-7">
                  {vacancy?.categories?.data?.map((item) => {
                    return <div>{item?.name}</div>;
                  })}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Required Skills</div>
                <div className="col-span-7">
                  {vacancy?.skills?.data?.map((item) => {
                    return <div>{item?.name}</div>;
                  })}
                </div>
              </div>
            </div>
          )}
        </Card>

        <Card
          loading={loadingAll}
          title={'Company Details'}
          key={'company-details'}
          description={'Company Details'}
        >
          {loadingAll && (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <Spin />
            </div>
          )}
          {!loadingAll && (
            <div className="flex flex-col gap-3">
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Company Name</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.name}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Company Description</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.companyDescription}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Address</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.address?.address1 || ''}{' '}
                  {vacancy?.clientCorporation?.address?.city || ''}{' '}
                  {vacancy?.clientCorporation?.address?.countryName || ''}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Billing Address</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.billingAddress?.address1 || ''}{' '}
                  {vacancy?.clientCorporation?.billingAddress?.city || ''}{' '}
                  {vacancy?.clientCorporation?.billingAddress?.countryName ||
                    ''}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Billing Phone</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.billingPhone}
                </div>
              </div>
              <div className="grid grid-cols-10 gap-3 border-b p-3">
                <div className="col-span-3">Billing Contact</div>
                <div className="col-span-7">
                  {vacancy?.clientCorporation?.billingContact}
                </div>
              </div>
            </div>
          )}
        </Card>

        <Card
          title={'Internal Description'}
          key={'internalDescription'}
          description={'Internal Description'}
          loading={loadingAll}
        >
          {loadingAll && (
            <div className="w-full min-h-[15rem] flex flex-col items-center gap-5 content-center place-content-center">
              <Spin />
            </div>
          )}
          {!loadingAll && (
            <div
              style={{ padding: '10px' }}
              dangerouslySetInnerHTML={{
                __html: vacancy?.description,
              }}
            ></div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Overview;
