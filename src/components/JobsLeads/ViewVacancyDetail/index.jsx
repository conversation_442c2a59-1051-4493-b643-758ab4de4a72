import {
  BankOutlined,
  CloseOutlined,
  EnvironmentOutlined,
  GoogleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Button, Select, Spin, Tabs, notification } from 'antd';

import './style.css';
import Overview from './Overview';
import { getDataTotal, searchFileOfBullHorn, upladteBullhorn } from '../../../services/bullhorn';
import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBriefcase } from '@fortawesome/free-solid-svg-icons';
import ShortLists from './ShortLists';
import NoteTab from './ShortListTab/NoteTab';
import FileTab from './ShortListTab/FileTab';

const ViewVacancyDetail = ({ onClose, vacancy, vacancyId, onReload, vacancyDetailLoading }) => {
  const [loadingAll, setLoadingAll] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [loadingNote, setLoadingNote] = useState(false);
  const [loadingFile, setLoadingFile] = useState(false);
  const [totalNote, setTotalNote] = useState(0);
  const [totalFile, setTotalFile] = useState(0);
  const [loadingShortList, setLoadingShortList] = useState(false);
  const [totalShortList, setTotalShortList] = useState(0);
  const [keyActive, setKeyActive] = useState("overview")
  const [chooseCollapse, setChooseCollapse] = useState("overview")
  const [updateData, setUpdateData] = useState({
    status: vacancy?.status,
    isOpen: vacancy?.isOpen
  });

  const handleUpdateData = async (payload) => {
    setLoadingFetch(true);
    const dataUpdate = {
      ...payload,
      entityName: 'JobOrder',
    };
    try {
      const { data } = await upladteBullhorn(vacancyId, dataUpdate);
      if (data) {
        notification.success({
          message: 'Updated Success',
        });
      }
    } catch (err) {
      notification.error({
        message: 'Something went wrong',
      });
    }
    setLoadingFetch(false);
  };

  const handleGetTotalShortList = async () => {
    try {
      setLoadingShortList(true)
      const { data } = await getDataTotal('JobSubmission', vacancy?.id, '', '', '', '', "Vacancy");
      setTotalShortList(data.result.total);
      setLoadingShortList(false)
    } catch (err) {
      setLoadingShortList(false)
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const handleGetTotal = async () => {
    try {
      setLoadingNote(true)
      const { data } = await getDataTotal('Note', vacancy?.id);
      setTotalNote(data.result.total);
      setLoadingNote(false)
    } catch (err) {
      setLoadingNote(false)
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const handleGetFiles = async (count, start) => {
    try {
      setLoadingFile(true);
      const response = await searchFileOfBullHorn(
        'JobOrder',
        (start > 0 ? start - 1 : 0) * count || '',
        count || null,
        null,
        vacancy?.id,
        null,
        null,
        null,
        false
      );
      setTotalFile(response?.data?.result?.total);
      setLoadingFile(false);
    } catch (err) {
      setLoadingNote(false)
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  useEffect(() => {
    if(vacancy) {
      handleGetTotal()
      handleGetTotalShortList()
      handleGetFiles()
    }
    }, [vacancy, keyActive])

    const tabItems = [
      {
        key: 'overview',
        label: <span className="font-medium">OVERVIEW</span>,
        children: (
          <Overview
            vacancy={vacancy}
            vacancyId={vacancyId}
            loadingAll={loadingAll || vacancyDetailLoading}
            setLoadingFetch={setLoadingFetch}
            setUpdateData={setUpdateData}
            updateData={updateData}
            keyActive={keyActive}
            setKeyActive={setKeyActive}
            totalShortList={totalShortList}
            setChooseCollapse={setChooseCollapse}
          />
        ),
      },
      {
        key: 'note',
        label: <span className="font-medium">NOTES {loadingNote ? <><Spin /></> : <>({totalNote})</>} </span>,
        children: <NoteTab vacancyIdRaw={vacancyId} vacancy={vacancy} totalNote={totalNote} handleGetTotal={handleGetTotal}/>,
      },
      {
        key: 'file',
        label: <span className="font-medium">FILES {loadingFile ? <><Spin /></> : <>({totalFile})</>}</span>,
        children: <FileTab vacancyIdRaw={vacancyId} vacancy={vacancy} totalFile={totalFile} handleGetTotal={handleGetFiles}/>,
      },
      {
        key: 'shortList',
        label: <span className="font-medium">SHORTLISTS {loadingShortList ? <><Spin /></> : <>({totalShortList})</>}</span>,
        children: <ShortLists vacancyIdRaw={vacancyId} vacancy={vacancy} totalShortList={totalShortList} chooseCollapse={chooseCollapse} setChooseCollapse={setChooseCollapse}/>,
      },
      // {
      //   key: 'activity',
      //   label: <span className="font-medium">ACTIVITY</span>,
      //   children: 'coming soon...',
      // },
    ];

  return (
    <div>
      <div className="flex flex-col gap-4 p-5 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex gap-3 items-center">
            <FontAwesomeIcon icon={faBriefcase} style={{fontSize: "30px", color: "#b56"}}/>
            <span className="text-2xl font-medium">{vacancyId}</span>
            <span>|</span>
            <span className="text-2xl font-medium">{vacancy?.title}</span>
            <div>
              {/* <Button onClick={() => window?.open(`https://www.google.com/search?q=%22${company?.name}%22`)} type="text" icon={<GoogleOutlined />} />
              <Button onClick={() => window?.open(`https://www.google.com/maps?q=${company?.name}+${company?.address?.address1}+${company?.address?.city}+${company?.address?.countryName}`)} type="text" icon={<EnvironmentOutlined />} /> */}
            </div>
          </div>
          <div>
            {loadingFetch && <Spin size="small" />}
            <Button
              onClick={async () => {
                setLoadingAll(true);
                await onReload();
                setLoadingAll(false);
              }}
              type="text"
              icon={<ReloadOutlined />}
            />
            <Button onClick={onClose} type="text" icon={<CloseOutlined />} />
          </div>
        </div>
        <div>
          <div className="grid grid-cols-7 w-3/5 mb-1">
            <div className="col-span-1">ID</div>
            <div className="col-span-2">Job Title</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Open/Closed</div>
          </div>
          <div className="grid grid-cols-7 w-3/5 items-center">
            <div className="col-span-1">{vacancyId}</div>
            <div className="col-span-2">{vacancy?.title}</div>
            <div className="col-span-2">
              <Select
                className="min-w-[90px]"
                value={updateData?.status ?? vacancy?.status}
                rootClassName="company-status-container-status"
                onChange={(e) => {
                  setUpdateData({
                    ...updateData,
                    status: e,
                  })
                  handleUpdateData({
                    status: e,
                  });
                }}
                options={[
                  {
                    value: 'Accepting Candidates',
                    label: 'Accepting Candidates',
                  },
                  {
                    value: 'Lead',
                    label: 'Lead',
                  },
                  {
                    value: 'HOT Lead',
                    label: 'HOT Lead',
                  },
                  {
                    value: 'Offer Out',
                    label: 'Offer Out',
                  },
                  {
                    value: 'Placed',
                    label: 'Placed',
                  },
                  {
                    value: 'Filled by Client',
                    label: 'Filled by Client',
                  },
                  {
                    value: 'Lost To Competitor',
                    label: 'Lost To Competitor',
                  },
                  {
                    value: 'Archive',
                    label: 'Archive',
                  },
                  {
                    value: 'On Hold',
                    label: 'On Hold',
                  },
                  {
                    value: 'Cancelled',
                    label: 'Cancelled',
                  },
                ]}
              />
            </div>
            <div className="col-span-2">
              <Select
                className="min-w-[90px]"
                defaultValue={updateData?.isOpen ? 'Open' : 'Closed'}
                rootClassName="company-status-container"
                onChange={(e) => {
                  setUpdateData({
                    ...updateData,
                    isOpen: e,
                  })
                  handleUpdateData({
                    isOpen: e,
                  });
                }}
                options={[
                  {
                    value: true,
                    label: 'Open',
                  },
                  {
                    value: false,
                    label: 'Closed',
                  },
                ]}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="w-full border-b-2 border-b-[#b56]"></div>
      <Tabs defaultActiveKey="1" items={tabItems} activeKey={keyActive} onChange={(e) => setKeyActive(e)}/>
    </div>
  );
};

export default ViewVacancyDetail;
