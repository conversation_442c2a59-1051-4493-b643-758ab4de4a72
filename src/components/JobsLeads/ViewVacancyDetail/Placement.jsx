import { useEffect, useState } from 'react';
import Card from './Card';
import { searchBullhorn, searchBullhornData } from '../../../services/bullhorn';
import {
  Button,
  Drawer,
  Dropdown,
  Input,
  Modal,
  Pagination,
  Select,
  Space,
  Table,
  notification,
} from 'antd';
import dayjs from 'dayjs';
import {
  DownOutlined,
  LinkedinOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import ViewVacancyDetail from '../ViewVacancyDetail';
import ModalAddNewAppointment from './ShortListTab/ModalAddNewAppointment';
import AddPlacement from './ShortListTab/AddPlacement';

const Placement = (props) => {
  const { company, vacancyId, vacancy , totalShortList} = props;

  const [listContact, setListContact] = useState([]);
  const [loadingContact, setLoadingContact] = useState(false);
  const [numberPage, setNumberPage] = useState(10);
  const [consultantId, setConsultantId] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [openModalCreate, setOpenModalCreate] = useState(false);

  const handleGetContacts = async (count, start) => {
    setLoadingContact(true);
    const response = await searchBullhorn(
      'Placement',
      (start > 0 ? start - 1 : 0) * count || '',
      count || null,
      null,
      null,
      null,
      null,
      null,
      false,
      "",
      "",
      "",
      "",
      vacancy?.id
    );

    setListContact(response?.data?.result || []);
    setLoadingContact(false);
  };

  const handlePagination = (page, pageSize) => {
    setCurrentPage(page);
    handleGetContacts(numberPage, page);
  };

  useEffect(() => {
    handleGetContacts(numberPage, '', '', '', '');
  }, [vacancy]);

  const columns = [
    // {
    //   title: '',
    //   dataIndex: '',
    //   key: '',
    //   defaultSortOrder: 'descend',
    //   render: (_, record) => {
    //     return (
    //       <div className="flex flex-col gap-2" style={{ width: '200px' }}>
    //         <Dropdown
    //           menu={{
    //             items: [
    //               {
    //                 label: <div>Edit</div>,
    //                 key: '0',
    //               },
    //               {
    //                 label: <div>Delete</div>,
    //                 key: '1',
    //               },
    //             ],
    //           }}
    //           trigger={['click']}
    //         >
    //           <a onClick={(e) => e.preventDefault()}>
    //             <Space>
    //               Action
    //               <DownOutlined />
    //             </Space>
    //           </a>
    //         </Dropdown>
    //       </div>
    //     );
    //   },
    // },
    {
      title: 'Placement #',
      dataIndex: 'Placement',
      key: 'Placement',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            {record?.id}
          </div>
        );
      },
    },
    {
      title: 'Candidate',
      dataIndex: 'candidate',
      key: 'candidate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px', color: '#4a89dc', fontWeight: '600' }}>
            <div>
              {record?.candidate?.firstName} {record?.candidate?.lastName}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Date Added',
      dataIndex: 'Date Added',
      key: 'Date Added',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px' }}>
            <div>
              {record?.dateBegin
              ? dayjs(record?.dateBegin).format('YYYY/MM/DD h:mm a')
              : null}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Employments Type',
      dataIndex: 'Employments',
      key: 'Employments',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
            <div>
              {record?.employmentType}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Source',
      dataIndex: 'Source',
      key: 'Source',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.candidate?.source[0]}
          </div>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'Status',
      key: 'Status',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-2" style={{ width: '200px' }}>
            {record?.status}
          </div>
        );
      },
    },
    {
      title: 'Salary',
      dataIndex: 'Salary',
      key: 'Salary',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
            <div>
              {record?.salary}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Pay Rate',
      dataIndex: 'Pay Rate',
      key: 'Pay Rate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
          <div>
            {record?.payRate}
          </div>
        </div>
        );
      },
    },
    {
      title: 'Bill Rate',
      dataIndex: 'Bill Rate',
      key: 'Bill Rate',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px'}}>
          <div>
            {record?.fee}
          </div>
        </div>
        );
      },
    },
    {
      title: 'Start Date',
      dataIndex: 'Start Date',
      key: 'Start Date',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px' }}>
            <div>
              {record?.dateBegin
              ? dayjs(record?.dateBegin).format('YYYY/MM/DD h:mm a')
              : null}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Schedule End Date',
      dataIndex: 'Schedule End Date',
      key: 'Schedule End Date',
      defaultSortOrder: 'descend',
      render: (_, record) => {
        return (
          <div style={{ width: '200px' }}>
            <div>
              {record?.dateBegin
              ? dayjs(record?.dateAdded).format('YYYY/MM/DD h:mm a')
              : null}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <div className="px-3 grid grid-cols-1 gap-3 pb-5">
        <div style={{ width: '100%' }}>
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  padding: '20px',
                  lineHeight: '1.2',
                  display: 'flex',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div>ITEMS PER PAGE</div>
                  <div>
                    <Select
                      className="min-w-[140px]"
                      style={{
                        marginLeft: '10px',
                      }}
                      defaultValue={'10'}
                      rootClassName="company-status-container"
                      onChange={(e) => {
                        setCurrentPage(1);
                        setNumberPage(e);
                        handleGetContacts(
                          e,
                          '',
                          consultantId ? 'owner.id' : '',
                          consultantId,
                          consultantId ? 'EQUAL' : '',
                          consultantId ? 'INT' : ''
                        );
                      }}
                      options={[
                        {
                          value: '10',
                          label: '10',
                        },
                        {
                          value: '25',
                          label: '25',
                        },
                        {
                          value: '50',
                          label: '50',
                        },
                        {
                          value: '100',
                          label: '100',
                        },
                      ]}
                    />
                  </div>
                </div>
              </div>
              <div style={{display: "flex"}}>
                <div>
                  <Button type="primary" onClick={() => setOpenModalCreate(true)}>Create new Appointment</Button>
                </div>
                <Pagination
                style={{
                  marginLeft: "10px"
                }}
                total={totalShortList}
                pageSize={numberPage}
                showSizeChanger={false}
                onChange={handlePagination}
                current={currentPage}
            />
              </div>
            </div>
            <div className="company-view-notes">
              <Table
                loading={loadingContact}
                // showHeader={false}
                scroll={{
                  x: 1000,
                }}
                dataSource={listContact}
                columns={columns}
                pagination={false}
              />
            </div>
          </div>
        </div>
      </div>
        <AddPlacement openModal={openModalCreate} setOpenModal={setOpenModalCreate} vacancy={vacancy}/>
    </div>
  );
};

export default Placement;