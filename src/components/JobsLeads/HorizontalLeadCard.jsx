/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';

import _map from 'lodash/map';
import _find from 'lodash/find';
import _get from 'lodash/get';
import clsx from 'clsx';
import { CSS } from '@dnd-kit/utilities';

import {
  Button,
  Card,
  Col,
  Form,
  Image,
  Input,
  Modal,
  Row,
  Typography,
  message,
  Popconfirm,
  notification,
  Drawer,
  Spin,
  Dropdown,
  Tooltip,
  AutoComplete,
  Select,
  Tag,
} from 'antd';
import {
  BankFilled,
  CalendarOutlined,
  PoundOutlined,
  EnvironmentOutlined,
  FieldTimeOutlined,
  FileTextOutlined,
  SettingOutlined,
  StarOutlined,
  WalletOutlined,
  CloseOutlined,
  CheckOutlined,
  DragOutlined,
  MoreOutlined,
  EyeOutlined,
  DeleteOutlined,
  AuditOutlined,
  UserOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import { deleteLead } from '../../services/jobLeads';
import logo from '/logo_bull.webp';
import { Controller, useForm } from 'react-hook-form';
import { createComment, getLeadComment } from '../../services/comment';
import LeadCardCommentItems from './LeadCardCommentItem';
import { ExistingContactTable } from '../BullHorn/tables/ExistingContactTable';
import { getDataTotal, searchBullhorn } from '../../services/bullhorn';
import EmailFinderContainer from '../EmailFinder/EmailFinderComponent';

import {
  savePotentialLeadValue,
  selectAllUsers,
  selectPotentialLeadValue,
} from '../../store/common';
import { useSelector } from 'react-redux';
import handleRenderTime from '../../function/handleRenderTime';
import AutocompleteStatus from '../../common/AutocompleteStatus';
import BullhornSendEmail from '../BullHorn/BullhornSendEmailModal';
import { updateStatusLeadMailBox } from '../../services/myLead';
import { getEmailConfigInJobBoard, updateJobLogs } from '../../services/jobs';
import BullhornSequenceModal from '../BullHorn/BullhornSequenceModal';

import { deleteNewLead } from '../../services/jobLeads';
import JobDetail from '../JobDetail';
import { getJobDetail } from '../../services/jobLeads';
import { useQuery } from '@tanstack/react-query';
import { getPotentialLeadPercentValue } from '../../services/jobLeads';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import moment from 'moment';
import { useDispatch } from 'react-redux';
import { useSortable } from '@dnd-kit/sortable';
import { parsedLeadId } from '../../containers/LeadsDragDropV2';
import ViewCompanyDetail from './ViewCompanyDetail';
import ViewVacancyDetail from './ViewVacancyDetail';
import { getAllLeadSheets } from '../../services/leadSheet';
import AutocompleteAssign from '../../common/AutocompleteAssign';

const { Text } = Typography;

function LeadCard({
  lead,
  reloadJobLeads,
  isNotification,
  notificationType,
  componentNotification,
  isShowInMailbox,
  handleAfterUpdateStatusCompleted,
  sequenceStatus = false,
  review = false,
  fromExisting = false,
  onClickSync = false,
  onClickCheckId = false,
  setRefresh,
  refresh,
  fromExistingTab = false,
  style = '',
  index,
  horizontal = false,
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `${parsedLeadId(lead?.id)}`,
    data: {
      type: 'lead',
    },
  });

  const allUsers = useSelector(selectAllUsers);
  const [isModalDetailVisible, setIsModalDetailVisible] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [openModalSequence, setOpenSendSequence] = useState(false);
  const [statusChecked, setStatusChecked] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);

  const [emailConfigData, setEmailConfigData] = useState();
  const [openLeadDetail, setOpenJobDetail] = useState(false);
  const [jobLeadDetail, setJobLeadDetail] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  // Loading
  const [loading, setLoading] = useState(false);

  // View company
  const [companyDetail, setCompanyDetail] = useState(null);
  const [openCompanyViewer, setOpenCompanyViewer] = useState(false);
  const [openVacancyViewer, setOpenVacancyViewer] = useState(false);
  const [companyDetailLoading, setCompanyDetailLoading] = useState(false);
  const [vacancyDetailLoading, setVacancyDetailLoading] = useState(false);
  const [totalContact, setTotalContact] = useState();

  const [vacancyDetail, setVacancyDetail] = useState(null);

  // Lead Sheet Area
  const [leadSheets, setLeadSheets] = useState([]);
  const [newLeadSheetName, setNewLeadSheetName] = useState('');
  const [leadSheetId, setLeadSheetId] = useState('');
  const [leadSheetLoading, setLeadSheetLoading] = useState(true);

  // const onNewLeadSheetNameChange = (event) => {
  //   setNewLeadSheetName(event.target.value);
  // };

  // const addNewLeadSheet = async (e) => {
  //   if (!newLeadSheetName?.trim()) {
  //     notification.warning({
  //       description: 'Sheet Name is required!',
  //     });
  //     return;
  //   }
  //   e.preventDefault();
  //   try {
  //     const payload = {
  //       name: `LEAD: ${newLeadSheetName}`,
  //       leadStatusType: LEAD_SHEET_TYPE.LEAD,
  //     };
  //     setLeadSheetLoading(true);
  //     const { data } = await createNewLeadSheet(payload);

  //     if (data?.result) {
  //       setLeadSheets([{ ...data?.result }, ...leadSheets]);
  //       setNewLeadSheetName('');
  //     }

  //     setTimeout(() => {
  //       inputLeadSheetRef.current?.focus();
  //     }, 0);
  //     setLeadSheetLoading(false);
  //   } catch (error) {
  //     notification.error({
  //       description:
  //         error?.response?.data?.message ||
  //         'Something went wrong! Try again later',
  //     });
  //     console.log('Error in addNewLeadSheet: ', error);
  //     setLeadSheetLoading(false);
  //   }
  // };

  const onChangeLeadSheet = (value) => setLeadSheetId(value);

  const getLeadSheetOptions = async () => {
    try {
      const { data } = await getAllLeadSheets();
      if (data?.result?.length > 0) {
        setLeadSheets([...data?.result]);
      }
      setLeadSheetLoading(false);
    } catch (error) {
      console.log('Error in getLeadSheetOptions: ', error);
      setLeadSheetLoading(false);
    }
  };

  useEffect(() => {
    getLeadSheetOptions();
  }, []);

  // End Lead Sheet Area

  const showCompanyDrawer = () => setOpenCompanyViewer(true);
  const closeCompanyDrawer = () => setOpenCompanyViewer(false);

  const showVacancyDrawer = () => setOpenVacancyViewer(true);
  const closeVacancyDrawer = () => setOpenVacancyViewer(false);

  const companyIdRawList = lead?.company_id?.split('-');
  let companyIdRaw = '';
  if (companyIdRawList?.length > 0) {
    companyIdRaw = companyIdRawList[companyIdRawList?.length - 1];
  } else {
    companyIdRaw = lead?.company_id;
  }

  const vacancyIdRawList = lead?.job_lead_external_id?.split('-');
  let vacancyIdRaw = '';
  if (vacancyIdRawList?.length > 0) {
    vacancyIdRaw = vacancyIdRawList[vacancyIdRawList?.length - 1];
  } else {
    vacancyIdRaw = lead?.job_lead_external_id;
  }

  const getCompanyDetail = async () => {
    if (!companyIdRaw) return;
    setCompanyDetailLoading(true);

    try {
      // const res = await getBHCompanyDetail(companyIdRaw);
      const { data } = await searchBullhorn(
        'ClientCorporation',
        0,
        1,
        '',
        '',
        companyIdRaw,
        ''
      );
      if (data?.result?.length > 0) {
        const { result } = data;
        setCompanyDetail({ ...result[0] });
        showCompanyDrawer();
      } else {
        notification.warning({
          description: 'Company might be deleted by another user.',
        });
      }

      setCompanyDetailLoading(false);
      await handleGetTotal();
    } catch (error) {
      setCompanyDetailLoading(false);
      notification.error({
        description: 'Network Error! Please try again later.',
      });
    }
  };

  const handleGetTotal = async () => {
    try {
      const { data } = await getDataTotal('ClientContact', companyIdRaw);
      setTotalContact(data.result.total);
    } catch (err) {
      notification.error({
        message: 'Network Error! Please try again',
      });
    }
  };

  const getVacancyDetail = async () => {
    if (!vacancyIdRaw) return;
    setVacancyDetailLoading(true);
    showVacancyDrawer();
    try {
      // const res = await getBHCompanyDetail(companyIdRaw);
      const { data } = await searchBullhorn(
        'JobOrder',
        0,
        1,
        '',
        '',
        vacancyIdRaw,
        ''
      );
      if (data?.result?.length > 0) {
        const { result } = data;
        setVacancyDetail({ ...result[0] });
      } else {
        notification.warning({
          description: 'Vacancy might be deleted by another user.',
        });
      }

      setVacancyDetailLoading(false);
    } catch (error) {
      setVacancyDetailLoading(false);
      notification.error({
        description: 'Network Error! Please try again later.',
      });
    }
  };

  const dispatch = useDispatch();

  const { data: potentialLeadValue } = useQuery(['POTENTIAL_RETURN_PERCENT'], {
    queryFn: async () => {
      // const potentialLeadValue = useSelector(selectPotentialLeadValue);

      // if (potentialLeadValue) {
      //   return potentialLeadValue;
      // }

      const { data } = await getPotentialLeadPercentValue();

      dispatch(savePotentialLeadValue(data.result.potentialLeadValue));

      return data.result.potentialLeadValue;
    },
  });

  const getDataEmail = async () => {
    const { data } = await getEmailConfigInJobBoard(
      lead?.job_board_id || lead?.leadRawId || lead?.id
    );
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          key: index + 1,
          status: item?.status,
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, data?.result?.mails?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
    }
  };

  const showModalDetail = async () => {
    setLoading(true);
    updateExistingContacts().finally(() => {
      setLoading(false);
      setIsModalDetailVisible(!isModalDetailVisible);
      setIsModalVisible(false);
    });
  };

  const TextWithLineBreaks = (text) => {
    let newText = text.replace(/(?:\r\n|\r|\n)/g, '<br />');
    // replace all {keyword} with <mark>{keyword}</mark>

    return newText;
  };

  const handleClose = () => {
    setLeadSheetId('');
    setIsModalDetailVisible(false);
    fromExistingTab && setRefresh(false);
    reloadJobLeads();
  };

  const showModal = (e) => {
    // e.stopPropagation();
    setIsModalVisible(true);
    handleClose();
  };

  const showModalDelete = (e) => {
    // e.stopPropagation();
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    setIsLoading(true);
    await deleteLead(lead?.leadRawId || lead?.id);
    reloadJobLeads();
    setIsLoading(false);
    setIsModalVisible(false);
    message.success(`Success remove ${lead?.title}`);
  };
  const handleCancel = () => setIsModalVisible(false);

  const creator = _find(
    allUsers,
    (item) => item.id === _get(lead, 'creatorId')
  );

  const [showContent, setShowMoreContent] = useState(false);
  const [dataListComment, setDataListComment] = useState();
  const { handleSubmit, control, getValues, setValue, watch } = useForm();

  const handleCreateComment = async () => {
    if (getValues('comment')) {
      const data = await createComment(
        lead?.leadRawId || lead?.id,
        getValues('comment')
      );
      if (data) {
        setValue('comment', null);
        handleGetData();
      }
    }
  };

  const handleGetData = async () => {
    const data = await getLeadComment(lead?.leadRawId || lead?.id);
    setDataListComment(data?.data?.result);
  };

  useEffect(() => {
    if (isModalDetailVisible) {
      setLeadSheetId('');
      handleGetData();
    }
  }, [lead?.leadRawId || lead?.id, isModalDetailVisible]);

  const [isExistingContactOpen, setIsExistingContactOpen] = useState(false);
  const [isFindNewContactOpen, setIsFindNewContactOpen] = useState(false);

  const showExistingContactModal = () => {
    updateExistingContacts();
    setIsExistingContactOpen(true);
  };

  const handleOkExistingContactModal = () => {
    setIsExistingContactOpen(false);
  };

  const handleCancelExistingContactModal = () => {
    setIsExistingContactOpen(false);
  };

  const showFindNewContactModal = () => {
    setIsFindNewContactOpen(true);
  };

  const handleOkFindNewContactModal = () => {
    setIsFindNewContactOpen(false);
  };

  const handleCancelFindNewContactModal = () => {
    setIsFindNewContactOpen(false);
  };

  const loadingSequenceData = async () => {
    setNumberStep(0);
    setInputNumberStep([]);
    setValue('mailStep', null);
    await getDataEmail();
  };

  const [existingContacts, setExistingContacts] = useState([]);

  const updateExistingContacts = async () => {
    const response = await searchBullhorn(
      'ClientContact',
      null,
      null,
      null,
      lead?.company_id?.split('-')?.[3]
    );
    setExistingContacts(response?.data?.result || []);
  };

  // useEffect(() => {
  //   console.log('useEffect 3');
  //   updateExistingContacts();
  // }, [lead]);

  useEffect(() => {
    if (!lead?.leadSheetId) return;
    const tempLeadSheetId = lead?.leadSheetId;
    setLeadSheetId(tempLeadSheetId);
  }, [lead?.leadSheetId]);

  const mappingListenners =
    !review && !fromExisting && !loading ? listeners : {};
  const handleRenderLeftContent = () => {
    return (
      <div className="left-content-lead-custom pt-5 pl-5">
        {/* <Divider /> */}
        <div
          style={{
            fontSize: '20px',
            fontWeight: 500,
            cursor: 'pointer',
            marginBottom: '20px',
          }}
          className="vacancy-title-custom"
          onClick={getVacancyDetail}
        >
          {lead?.title} {vacancyDetailLoading && <Spin />}
        </div>
        <Row gutter={[8, 8]}>
          <Col span="auto" style={{ paddingLeft: '0px' }}>
            <Button
              className="mr-1 lead-tag"
              style={{
                display: 'flex',
                border: '1px solid #ccc',
                borderRadius: 2,
                justifyContent: 'flex-start',
                alignItems: 'center',
                padding: '0',
                paddingRight: '10px',
                height: '25px',
              }}
              icon={<Image width={'25px'} src={logo} preview={false} />}
              onClick={showExistingContactModal}
            >
              Existing contacts
            </Button>
          </Col>

          <Col span="auto" style={{ paddingLeft: '0px' }}>
            <Button
              className="mr-1 lead-tag"
              style={{
                display: 'flex',
                border: '1px solid #ccc',
                borderRadius: 2,
                justifyContent: 'flex-start',
                alignItems: 'center',
                padding: '0',
                paddingRight: '10px',
                height: '25px',
              }}
              icon={<Image width={'25px'} src={logo} preview={false} />}
              onClick={showFindNewContactModal}
            >
              Find new contacts
            </Button>
          </Col>

          <Col span="auto" style={{ paddingLeft: '0px' }} className="mb-10">
            <Button
              disabled={loading}
              className="mr-1 lead-tag"
              style={{
                display: 'flex',
                border: '1px solid #ccc',
                borderRadius: 2,
                justifyContent: 'flex-start',
                alignItems: 'center',
                padding: '0',
                paddingRight: '10px',
                height: '25px',
              }}
              icon={<Image width={'25px'} src={logo} preview={false} />}
              onClick={() => {
                setLoading(true);
                loadingSequenceData().finally(() => {
                  setLoading(false);
                  setOpenSendSequence(!openModalSendEmail);
                });
              }}
            >
              <div className="flex items-center gap-3">
                <div>Sequence</div>
                {loading && <Spin className="customized-spin" />}
              </div>
            </Button>
          </Col>

          <Modal
            title="Existing Contact"
            open={isExistingContactOpen}
            onOk={handleOkExistingContactModal}
            onCancel={handleCancelExistingContactModal}
            width={1500}
          >
            <ExistingContactTable
              updateExistingContacts={updateExistingContacts}
              dataSource={existingContacts}
            />
          </Modal>

          <Modal
            title="Find new contacts"
            open={isFindNewContactOpen}
            onOk={handleOkFindNewContactModal}
            onCancel={handleCancelFindNewContactModal}
            width={'98%'}
          >
            <EmailFinderContainer />
          </Modal>
        </Row>
        <div className="w-full text-base items-center text-[#71717A] dark:bg-gray-800 rounded-lg">
          <div className="p-6">
            <div className="grid grid-cols-8 gap-x-4">
              <div className="col-span-8 sm:col-span-4">
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <BankFilled className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Company</span>
                  </div>
                  <span
                    onClick={() => {
                      getCompanyDetail();
                    }}
                    className="pr-3 font-medium col-span-5 text-[#09090B] hover:underline cursor-pointer"
                  >
                    {lead?.company_name}
                    {companyDetailLoading && <Spin />}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <FileTextOutlined className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Job Type</span>
                  </div>
                  <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {lead?.job_type || lead?.jobtype || lead?.jobType || '-'}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <EnvironmentOutlined className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Location</span>
                  </div>
                  <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {`${lead?.address_city} ${lead?.address_country ? lead?.address_country : ''} ${lead?.address_line_1 ? lead?.address_line_1 : ''}`}
                  </span>
                </div>
              </div>
              <div className="col-span-8 sm:col-span-4">
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <PoundOutlined className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Salary</span>
                  </div>
                  {/* <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {`${_get(lead, 'minSalary') || '--'} - ${_get(lead, 'maxSalary') || '--'}`}
                  </span> */}
                  <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {`${_get(lead, 'salary') || '--'}`}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <SettingOutlined className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Source</span>
                  </div>
                  <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {lead?.source}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-5 flex items-center">
                    <CalendarOutlined className="pr-3 text-[#2563EB] text-base" />
                    <span className="pr-3 text-graySecondary">Date Posted</span>
                  </div>
                  <span className="pr-3 font-medium col-span-5 text-[#09090B]">
                    {moment(
                      lead?.date_added || lead?.dateAdded || lead?.createdAt
                    ).format('DD/MM/YYYY')}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <Col>
            <Text className="text-base pl-5 font-medium">Description</Text>
            <Col style={{ padding: '20px' }}>
              <div
                className="mb-2 flex items-baseline space-x-2"
                style={{ alignItems: 'center' }}
              >
                <Text>
                  <span
                    className="text-[#71717A] text-sm"
                    dangerouslySetInnerHTML={{
                      __html: !showContent
                        ? (lead?.cleanDescription
                            ? TextWithLineBreaks(lead?.cleanDescription)
                            : lead?.description
                          )?.slice(0, 500)
                        : lead?.cleanDescription
                          ? TextWithLineBreaks(lead?.cleanDescription)
                          : lead?.description,
                    }}
                  ></span>
                </Text>
              </div>
              <div style={{ textAlign: 'start' }}>
                <Button
                  onClick={() => setShowMoreContent(!showContent)}
                  className="bg-white text-cyan-600 text-base rounded-2xl px-9 py-2 border border-cyan-600 h-fit"
                >
                  {!showContent ? 'Show more' : 'Collapse'}
                </Button>
              </div>
            </Col>
          </Col>
        </div>

        <div style={{ marginTop: '30px' }} className="comment-container">
          <span className="text-base font-semibold">Add Comments</span>
          <Form
            layout="vertical"
            onFinish={handleSubmit(handleCreateComment)}
            className="bg-white rounded-2xl mt-3 border border-[#E4E4E7]"
          >
            <Row>
              <Col>
                <Image
                  style={{ borderRadius: '50%' }}
                  width={'50px'}
                  src={'./common_user.webp'}
                  className="mt-3 px-2"
                  preview={false}
                ></Image>
              </Col>
              <Col span={21}>
                <Form.Item name="comment">
                  <Controller
                    name="comment"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        className="search-input my-3 min-w-fit"
                        placeholder="Enter Comments"
                        onPressEnter={() => {
                          handleSubmit(handleCreateComment);
                        }}
                      />
                    )}
                  />
                </Form.Item>
                {/* <div style={{ fontSize: '12px', marginTop: '-15px' }}>
                  <span style={{ fontWeight: '600' }}>Pro Tip:</span> press{' '}
                  <span style={{ fontWeight: '600' }}>Enter</span> to comment
                </div> */}
              </Col>
            </Row>
          </Form>
        </div>

        <div className="mt-10">
          {_map(dataListComment, (item, idx) => (
            <React.Fragment key={idx.toString()}>
              <LeadCardCommentItems
                commentItem={item}
                handleGetData={handleGetData}
              />
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const handleRenderRightContent = () => {
    return (
      <>
        <Row gutter={[16, 16]} className="pt-5 pr-5">
          <Col span={24}>
            <div className="bg-white border-[#E4E4E7] font-semibold border-[1px] rounded-2xl flex flex-col gap-8 py-7 px-4 right-content-leads-custom">
              <Col span={24}>
                <Col span={8} className="min-w-full mb-2 text-base">
                  Lead Sheet
                </Col>
                <Col span={8} className="min-w-full text-base">
                  <Select
                    className="w-full"
                    defaultValue={{
                      label: leadSheets?.find(
                        (item) => item?.id === lead?.leadSheetId
                      )?.name,
                      value: lead?.leadSheetId,
                    }}
                    loading={leadSheetLoading}
                    placeholder="Select a Lead Sheet"
                    options={leadSheets.map((item) => ({
                      label: item?.name,
                      value: item?.id,
                    }))}
                    onChange={onChangeLeadSheet}
                  />
                </Col>
              </Col>

              <Col span={24}>
                <Col span={8} className="min-w-full mb-2 text-base">
                  Status
                </Col>
                <Col span={8} className="min-w-full text-base">
                  <AutocompleteStatus
                    handleUpdaterDo={
                      isShowInMailbox ? handleAfterUpdateStatusCompleted : ''
                    }
                    isUpdateMail={isShowInMailbox ? true : ''}
                    lead={lead}
                    isShowInMailbox={isShowInMailbox}
                    leadSheetId={leadSheetId}
                    setLeadSheetId={setLeadSheetId}
                  />
                </Col>
              </Col>

              <Col span={24}>
                <Col span={24} className="min-w-full mb-2 text-base">
                  Assignee
                </Col>
                <Col span={24} className="min-w-full">
                  <AutocompleteAssign
                    isModalDetailVisible={isModalDetailVisible}
                    lead={lead}
                  />
                </Col>
              </Col>

              <Col span={24}>
                <Col span={24} className="min-w-full mb-2 text-base">
                  Creator
                </Col>
                <Col span={24} className="min-w-full">
                  <div className="text-base w-full bg-white px-4 py-3 rounded-2xl cursor-pointer border border-[#E4E4E7]">
                    <span className="text-[#E4E4E7]">
                      {_get(creator, 'username') || _get(creator, 'email')}
                    </span>
                  </div>
                </Col>
              </Col>

              <Col span={24} className="flex justify-end">
                <Row>
                  <Col span={24}>
                    <Typography.Text className="text-[#71717A] text-base">
                      Updated {handleRenderTime(_get(lead, 'updatedAt'))}
                    </Typography.Text>
                  </Col>
                </Row>
              </Col>
            </div>
          </Col>
          <></>
        </Row>
      </>
    );
  };

  const onHandleDeleteNewLeads = async (jobId) => {
    await deleteNewLead(jobId)
      .then((res) => {
        notification.success({
          message: 'Job is deleted successfully',
        });
        reloadJobLeads();
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          message: 'There are something wrong, please try again later!',
        });
      });
  };

  const onHandleOpenNewLeads = async () => {
    if (!lead?.jobBoardId) {
      notification.error({
        description: 'There are something wrong, try again later!',
      });
      return;
    }
    setIsLoading(true);
    await getJobDetail(lead?.jobBoardId)
      .then((res) => {
        if (res?.data?.result?.item) {
          setJobLeadDetail({ ...res?.data?.result?.item });
          setOpenJobDetail(true);
        }
        console.log('res: ', res);
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          description: 'Job not found. Please contact to admin!',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleEditLogs = async () => {
    if (!jobLeadDetail?.job_id) return;
    const payload = {
      type: 'EDITING_BH_VACANCY_SUBMISSION',
      jobId: jobLeadDetail?.job_id,
    };
    await updateJobLogs(payload);
  };

  const onHandleCloseNewLeads = () => setOpenJobDetail(false);

  const handleAccessList = async () => {
    try {
      setRefresh(true);
      messageApi.open({
        type: 'loading',
        content: 'Action in progress..',
      });

      const { data } = await updateStatusLeadMailBox(getUserViewAs(), lead.id);
      messageApi.destroy();
      if (data) {
        // setRefresh(false);
        notification.success({
          message: 'Success',
          description: 'You have successfully updated the status',
        });
        setIsModalDetailVisible(true);
        !fromExistingTab && setRefresh(false);
      }
    } catch (err) {
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleRenderUI = () => {
    if (isNotification) {
      return (
        <div
          onClick={
            notificationType === 'ASSIGNED_LEAD' ? showModalDetail : () => {}
          }
        >
          {componentNotification}
        </div>
      );
    }
    return (
      <div
        ref={setNodeRef}
        // {...listeners}
        {...attributes}
        style={{
          transition,
          transform: CSS.Translate.toString(transform),
        }}
        className={clsx(
          'bg-white shadow-sm rounded-xl w-full  hover:border-gray-200 lead-card-container',
          isDragging && 'opacity-50',
          !fromExisting && 'cursor-pointer',
          fromExisting && !horizontal && 'lead-card',
          !horizontal && 'border border-transparent'
        )}
        data-label={`Assigned by: ${lead?.assigner?.fullName || lead?.assigner?.username}`}
      >
        <Card
          className={clsx(
            `w-full border ${openLeadDetail && 'open-lead-detail-mode'}`,
            !horizontal && 'border-cyan-600'
          )}
          size="small"
          // onClick={review ? null : showModalDetail}
          hoverable
          headStyle={{
            background: '#fff',
          }}
          onClick={(e) => {
            e.stopPropagation();
            setIsModalDetailVisible(true);
          }}
        >
          <div className={clsx('flex justify-between items-center relative ')}>
            {!isShowInMailbox && (
              <Tooltip title="Click to view lead detail">
                <span
                  className="text-sm font-semibold line-clamp-1 hover:underline cursor-pointer"
                  title={lead.title || ''}
                  onClick={showModalDetail}
                >
                  {lead.title || ''}
                </span>
              </Tooltip>
            )}
            {isShowInMailbox && (
              <span
                className="text-sm font-semibold line-clamp-1"
                title={lead.title || ''}
                onClick={showModalDetail}
              >
                {lead.title || ''}
              </span>
            )}
            <div>
              {fromExisting && (
                <Button
                  disabled={isLoading}
                  type="link"
                  style={{ marginRight: '10px' }}
                  icon={isLoading ? <Spin /> : <CheckOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsModalDetailVisible(true);
                    // handleAccessList();
                  }}
                />
              )}
              {!fromExisting &&
                (review ? (
                  <div className="flex gap-1">
                    <Button
                      disabled={isLoading}
                      type="link"
                      icon={isLoading ? <Spin /> : <CheckOutlined />}
                      onClick={onHandleOpenNewLeads}
                    />
                    <Popconfirm
                      placement="top"
                      title={'Confirmation'}
                      description={`Do you want to delete ${lead.title}`}
                      okText="Delete"
                      cancelText="Cancel"
                      onConfirm={() =>
                        onHandleDeleteNewLeads(lead?.leadRawId || lead?.id)
                      }
                    >
                      <Button type="text" icon={<CloseOutlined />} />
                    </Popconfirm>
                  </div>
                ) : !loading ? (
                  <div className="flex gap-1">
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'view-lead',
                            label: (
                              <span className="text-sm font-medium">View</span>
                            ),
                            icon: <EyeOutlined />,
                            onClick: showModalDetail,
                          },
                          {
                            key: 'delete-lead',
                            label: (
                              <span className="text-sm font-medium">
                                Delete
                              </span>
                            ),
                            icon: <DeleteOutlined />,
                            onClick: showModal,
                          },
                        ],
                      }}
                      placement="bottom"
                      arrow={{
                        pointAtCenter: true,
                      }}
                    >
                      <MoreOutlined className="text-base font-semibold" />
                    </Dropdown>
                    {/* <CloseOutlined className="ml-1" onClick={showModal} /> */}
                  </div>
                ) : (
                  <Spin />
                ))}
            </div>
          </div>

          {!horizontal && (
            <div {...mappingListenners}>
              <div className="flex items-center gap-2 justify-between">
                <div
                  title={lead?.address_country || lead?.address_city || '-'}
                  className="text-xs bg-[#dde9fb] py-2 px-3 w-fit rounded-2xl text-[#689fee] font-semibold mt-2 whitespace-nowrap line-clamp-1"
                >
                  {lead?.address_country ||
                    lead?.address_city ||
                    lead?.address_line_1 ||
                    lead?.companyName ||
                    '-'}
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div>{lead?.location}</div>
                  <div
                    title="Date Posted"
                    className="italic text-xs py-2 px-3 w-fit rounded-2xl text-[#8893a4] font-semibold line-clamp-1"
                  >
                    {moment(
                      lead?.date_added || lead?.dateAdded || lead?.createdAt
                    ).format('DD/MM/YYYY')}
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <div className="text-xs bg-[#dde9fb] py-2 px-3 w-fit rounded-2xl text-[#689fee] font-semibold mt-2 ">
                  {lead?.jobType || '-'}
                </div>
              </div>
              <div className="bg-[#f9faff] grid grid-cols-2 place-items-center p-2 border mt-2">
                <div className="text-[#8893a4] font-normal">SALARY</div>
                <div className="font-bold">{lead?.salary}</div>
              </div>
              <div className="bg-[#f9faff] grid grid-cols-2 place-items-center p-2 border mt-2">
                <div className="text-[#8893a4] font-normal">LEAD VALUE</div>
                <div className="font-bold">
                  {parseInt(lead?.salary) > 0
                    ? `$${parseFloat(parseInt(lead?.salary) * (potentialLeadValue / 100)).toFixed(2)}`
                    : 'N/A'}
                </div>
              </div>
            </div>
          )}
          {horizontal && (
            <div>
              <div className="flex items-center gap-1 justify-between">
                <div
                  title={lead?.address_country || lead?.address_city || '-'}
                  className="text-xs w-fit text-[#689fee] font-semibold mt-1 whitespace-nowrap line-clamp-1"
                >
                  {lead?.address_country ||
                    lead?.address_city ||
                    lead?.address_line_1 ||
                    lead?.companyName ||
                    '-'}
                </div>
                <div className="flex items-center gap-5">
                  {lead?.jobType && (
                    <Tooltip title={`Job Type`}>
                      <div className="border rounded-md flex items-center gap-2 px-2 py-1">
                        <AuditOutlined />
                        <span className="font-medium">{lead?.jobType}</span>
                      </div>
                    </Tooltip>
                  )}
                  {lead?.salary && (
                    <Tooltip title={`Salary`}>
                      <div className="bg-[#fee1d5] rounded-md flex items-center gap-2 px-2 py-1">
                        {lead?.address_country?.toLowerCase() == "united kingdom" ? <PoundOutlined /> : <DollarOutlined />}
                        <span className="font-medium">{lead?.salary}</span>
                      </div>
                    </Tooltip>
                  )}
                  {lead?.salary && (
                    <Tooltip title={`Lead Value`}>
                      <div className="bg-[#e9cefe] rounded-md flex items-center gap-2 px-2 py-1">
                        {lead?.address_country?.toLowerCase() == "united kingdom" ? <PoundOutlined /> : <DollarOutlined />}
                        <div className="font-medium">
                          {console.log(
                            'potentialLeadValue: ',
                            potentialLeadValue
                          )}
                          {parseInt(lead?.salary) > 0
                            ? `$${parseFloat(parseInt(lead?.salary) * (parseInt(potentialLeadValue) / 100)).toFixed(2)}`
                            : 'N/A'}
                        </div>
                      </div>
                    </Tooltip>
                  )}
                  {(lead?.assigner?.fullName || lead?.assigner?.username) && (
                    <Tooltip title={`Assigned by`}>
                      <div className="bg-[#c8dcff] rounded-md flex items-center gap-2 px-2 py-1">
                        <UserOutlined />
                        <div className="font-medium">
                          {lead?.assigner?.fullName || lead?.assigner?.username}
                        </div>
                      </div>
                    </Tooltip>
                  )}
                </div>
                <div
                  title="Date Posted"
                  className="italic text-xs py-2 px-3 w-fit rounded-2xl text-[#8893a4] font-semibold line-clamp-1"
                >
                  {moment(
                    lead?.date_added || lead?.dateAdded || lead?.createdAt
                  ).format('DD/MM/YYYY')}
                </div>
              </div>
            </div>
          )}
        </Card>

        {isModalVisible && (
          <Modal
            title="Remove lead"
            open={isModalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
            footer={[
              <Button key="back" onClick={handleCancel}>
                Cancel
              </Button>,
              <Button
                key="submit"
                type="primary"
                loading={isLoading}
                onClick={handleOk}
              >
                Yes
              </Button>,
            ]}
          >
            <p>Are you sure you want to remove this lead?</p>
          </Modal>
        )}
      </div>
    );
  };

  return (
    <div
      className="custom-new-ui-leads relative"
      key={index}
      style={style ? style : {}}
    >
      {handleRenderUI()}
      {contextHolder}

      {/* Job Detail modal */}
      {openLeadDetail && jobLeadDetail && (
        <Drawer
          title="Job Details"
          onClose={onHandleCloseNewLeads}
          open={openLeadDetail}
          width={'50%'}
          placement={'right'}
        >
          <JobDetail
            job={{
              ...jobLeadDetail,
              jobBoardId: lead?.jobBoardId,
              id: lead?.leadRawId || lead?.id,
            }}
            isFromSync={!notificationType}
            handleEditLogs={handleEditLogs}
            onClickSync={(job, key) => {
              lead?.jobBoardId && onClickSync(lead?.jobBoardId, key);
              lead?.id && onClickCheckId(lead?.leadRawId || lead?.id);
            }}
            closeDrawer={onHandleCloseNewLeads}
          />
        </Drawer>
      )}
      {/* Lead Detail modal */}
      {isModalDetailVisible && (
        <Modal
          className="lead-view-container"
          open={isModalDetailVisible}
          onCancel={handleClose}
          footer={
            <>
              <Button
                type="primary"
                onClick={showModalDelete}
                className="text-white text-base rounded-2xl px-9 py-2 border-2 border-whi h-fit border border-white"
              >
                Delete Lead
              </Button>
            </>
          }
          width={1300}
          forceRender
        >
          <Row gutter={[40, 16]} className="bg-transparent">
            <Col span={15}>{handleRenderLeftContent()}</Col>

            <Col span={9}>{handleRenderRightContent()}</Col>
          </Row>
          {/* Company Drawer */}

          <Drawer
            rootClassName="view-company-container"
            placement="top"
            closable={false}
            onClose={closeCompanyDrawer}
            open={openCompanyViewer}
            getContainer={false}
            destroyOnClose={true}
            height={'100%'}
          >
            <ViewCompanyDetail
              onClose={closeCompanyDrawer}
              company={companyDetail}
              setCompanyDetail={setCompanyDetail}
              companyId={companyIdRaw}
              totalContact={totalContact}
            />
          </Drawer>

          {/* Vacancy Drawer */}

          <Drawer
            rootClassName="view-company-container"
            placement="top"
            closable={false}
            onClose={closeVacancyDrawer}
            open={openVacancyViewer}
            getContainer={false}
            destroyOnClose={true}
            height={'100%'}
          >
            <ViewVacancyDetail
              onClose={closeVacancyDrawer}
              vacancy={vacancyDetail}
              vacancyId={vacancyIdRaw}
              onReload={getVacancyDetail}
              vacancyDetailLoading={vacancyDetailLoading}
            />
          </Drawer>
        </Modal>
      )}

      {/* New Mail modal */}
      {openModalSendEmail && (
        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
              <div style={{ marginRight: '50px' }}></div>
            </div>
          }
          open={openModalSendEmail}
          onCancel={() => {
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmail}
                setOpenSendEmail={setOpenSendEmail}
                listAddContactSelected={existingContacts}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                // functionContactClient={functionContactClient}
                job={lead}
                fromSequenseEmail={lead?.job_board_id ? false : true}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                // setHaveSendJob={setHaveSendJob}
                // setIsLockedSendJob={setIsLockedSendJob}
                // isLockedSendJob={isLockedSendJob}
                // checkBoxStatus={checkBoxStatus}
              />
            </Form>
          </div>
        </Modal>
      )}

      {/* Sequence modal */}
      {openModalSequence && (
        <Modal
          // width={600}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>Sequence</div>
              <div style={{ marginRight: '50px' }}></div>
            </div>
          }
          open={openModalSequence}
          onCancel={() => {
            setOpenSendSequence(false);
          }}
          footer={false}
        >
          <div className="my-lead-sequence-container">
            <Form layout="vertical">
              <BullhornSequenceModal
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSequence}
                setOpenSendEmail={setOpenSendSequence}
                listAddContactSelected={existingContacts}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                fromSequenseEmail={lead?.job_board_id ? false : true}
                // functionContactClient={functionContactClient}
                job={lead}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                // setHaveSendJob={setHaveSendJob}
                // setIsLockedSendJob={setIsLockedSendJob}
                // isLockedSendJob={isLockedSendJob}
                // checkBoxStatus={checkBoxStatus}
                newUpdatedSequence={true}
              />
            </Form>
          </div>
        </Modal>
      )}
    </div>
  );
}

export default LeadCard;
