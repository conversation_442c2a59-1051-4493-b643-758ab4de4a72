import { DeleteOutlined, EditOutlined, UserOutlined } from '@ant-design/icons';
import { Col, Form, Image, Input, Row } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '../../store/auth';
import { deleteComment, editComment } from '../../services/comment';
import handleRenderTime from '../../function/handleRenderTime';

const LeadCardCommentItems = (props) => {
  const {
    handleSubmit: handleSubmitEdit,
    control: controlEdit,
    getValues: getValueEdit,
    setValue: setValueEdit,
  } = useForm();
  const { commentItem, handleGetData } = props;
  const { profile } = useAuth();
  const [editing, setEditing] = useState(false);
  
  const handleOpenEdit = () => {
    setEditing(!editing);
    setValueEdit('comment', commentItem?.content);
  };

  const handleEditComment = async () => {
    if (getValueEdit('comment')) {
      const data = await editComment(commentItem.id, getValueEdit('comment'));
      if (data) {
        setEditing(!editing);
        setValueEdit('comment', null);
        handleGetData();
      }
    }
  };

  const handleDeleteComment = async () => {
    const data = await deleteComment(commentItem?.id);
    if (data) {
      handleGetData();
    }
  };
  return (
    <div
      style={{ marginTop: '10px' }}
      key={commentItem?.id}
      className="bg-white py-2 px-3 rounded-md border"
    >
      <Row>
        {/* <Col span={2}>
                    <Image width={"30px"} className="mt-2" style={{ borderRadius: "50%" }} src={"./common_user.webp"} preview={false}></Image>
                </Col> */}
        <Col span={24}>
          <div className="flex items-center w-full justify-between">
            <span
              //   style={{ fontWeight: '600', marginRight: '5px' }}
              className="flex items-center gap-1 font-semibold text-base"
            >
              <UserOutlined />
              {commentItem?.creator?.username}
            </span>

            <span
              style={{
                marginLeft: '5px',
                fontSize: '12px',
                fontStyle: 'italic',
              }}
            >
              {handleRenderTime(commentItem?.createdAt)}
            </span>
          </div>
          <div className="pt-2">
            {editing ? (
              <Form layout="vertical">
                <Form.Item name="comment" className="mb-2">
                  <Controller
                    name="comment"
                    control={controlEdit}
                    render={({ field }) => (
                      <Input
                        {...field}
                        className="search-input"
                        placeholder="Add a comment"
                        onPressEnter={() => {
                          handleEditComment();
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Form>
            ) : (
              <div className="font-semibold opacity-60">
                {commentItem?.content}
              </div>
            )}
          </div>
        </Col>
        <Col span={24} className="flex justify-end">
          {profile?.user?.id == commentItem?.creator?.id && (
            <>
              <EditOutlined
                onClick={() => handleOpenEdit()}
                style={{ cursor: 'pointer' }}
              />
              <DeleteOutlined
                onClick={() => handleDeleteComment()}
                style={{ cursor: 'pointer', marginLeft: '10px' }}
              />
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default LeadCardCommentItems;
