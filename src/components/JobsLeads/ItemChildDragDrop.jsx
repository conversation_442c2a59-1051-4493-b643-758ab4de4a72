/* eslint-disable react/prop-types */
import React from 'react';
import LeadCard from './LeadCard';

function getBackgroundColor(isDragging, isGroupedOver) {
  if (isDragging) {
    return '#ffb6c1';
  }

  if (isGroupedOver) {
    return '#EBECF0';
  }

  return '#FFFFFF';
}

function getBorderColor(isDragging, color) {
  return isDragging ? color : 'transparent';
}
 
function ItemChildDragDrop(props) {
  const { quote, isDragging, isGroupedOver, provided, index, reloadJobLeads, handleDeleteLead } =
    props;
  return (
    <div
      className={`${getBorderColor(isDragging, '#51074a')} bg-${getBackgroundColor(
        isDragging,
        isGroupedOver,
        '#51074a'
      )} ${isDragging ? 'shadow' : 'none'} box-border mb-4 select-none flex w-full`}
      style={{ color: '#091e42' }}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      data-is-dragging={isDragging}
      data-testid={quote.id}
      data-index={index}
      aria-label={`${quote.company_contact_name} quote ${quote.company_name}`}
      ref={provided.innerRef}
    >
      <LeadCard lead={quote} reloadJobLeads={reloadJobLeads} handleDeleteLead={handleDeleteLead} />
    </div>
  );
}

export default React.memo(ItemChildDragDrop);
