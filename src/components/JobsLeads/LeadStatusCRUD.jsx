/* eslint-disable react/prop-types */
// LeadStatusCRUD.js
import React from 'react';
import {
  Button,
  Col,
  ColorPicker,
  Input,
  List,
  Modal,
  Row,
  Select,
  Spin,
} from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import useLeadStatuses from '../../hooks/useLeadStatuses';

const customPanelRender = (_, { components: { Presets } }) => (
  <Row justify="space-between" wrap={false}>
    <Col>
      <Presets />
    </Col>
  </Row>
);

const LeadStatusCRUD = ({ onFinish, typeId, type }) => {
  const {
    statuses,
    handleStatusChange,
    handleStatusDelete,
    handleSubmit,
    handleAddStatus,
    isSubmitting,
    loading,
    nonDeletableStatuses,
    showDeleteModal,
  } = useLeadStatuses(onFinish, typeId, type);

  console.log('nonDeletableStatuses: ', nonDeletableStatuses);

  return (
    <div style={{ marginTop: 30 }}>
      <div className="mb-5">
        {loading && (
          <div className="w-full flex justify-center items-center h-5">
            <Spin />
          </div>
        )}
        {!loading &&
          statuses
            .filter((status) => !status.isDeleted)
            .map((status, index) => (
              <List.Item key={status.id}>
                <Row gutter={16}>
                  <Col span={10}>Name</Col>
                  <Col span={7}>Order of Display</Col>
                  <Col span={6}>Color</Col>
                </Row>
                <Row gutter={16}>
                  <Col span={10}>
                    <Input
                      required
                      value={status.name}
                      onChange={(e) =>
                        handleStatusChange(status.id, 'name', e.target.value)
                      }
                    />
                  </Col>
                  <Col span={7}>
                    <Select
                      className="w-full"
                      value={status.orderOfDisplay}
                      onChange={(value) =>
                        handleStatusChange(status.id, 'orderOfDisplay', value)
                      }
                      options={statuses?.map((_item, index) => ({
                        value: index + 1,
                        label: index + 1,
                      }))}
                    />
                    {/* <Input
                      value={status.orderOfDisplay}
                      onChange={(e) =>
                        handleStatusChange(
                          status.id,
                          'orderOfDisplay',
                          e.target.value
                        )
                      }
                    /> */}
                  </Col>
                  <Col span={6}>
                    <ColorPicker
                      presets={[
                        {
                          label: 'Standard Colors',
                          colors: [
                            '#f5f5f6',
                            '#F6F5F2',
                            '#F0EBE3',
                            '#F3D0D7',
                            '#E8EFCF',
                            '#D1BB9E',
                            '#B3C8CF',
                            '#B3C8CF',
                            '#F8F6E3',
                            '#FEFDED',
                            '#FF0000',
                            '#FFA500',
                            '#FFFF00',
                            '#008000',
                            '#0000FF',
                            '#800080',
                            '#A52A2A',
                            // '#000000',
                            '#808080',
                            '#FFFFFF',
                            '#8DBCC7'
                          ],
                        },
                      ]}
                      value={status?.color ? status?.color : '#000000'}
                      showText={(color) => (
                        <span>
                          {color.toHex().replace(',', '').replace('%', '')}
                        </span>
                      )}
                      onChange={(color) =>
                        handleStatusChange(
                          status.id,
                          'color',
                          `#${color.toHex().replace(',', '').replace('%', '')}`
                        )
                      }
                      panelRender={customPanelRender}
                    />
                  </Col>
                  <Col span={1}>
                    <MinusCircleOutlined
                      onClick={() => handleStatusDelete(status.id)}
                    />
                  </Col>
                </Row>
              </List.Item>
            ))}
      </div>
      <Button
        type="dashed"
        onClick={handleAddStatus}
        block
        icon={<PlusOutlined />}
        disabled={loading}
      >
        Add New Status
      </Button>
      <Button
        type="primary"
        loading={isSubmitting}
        onClick={() => handleSubmit(false)}
        style={{ marginTop: '16px', width: '100%' }}
        disabled={loading}
      >
        Submit
      </Button>
      <Modal
        open={showDeleteModal}
        title={'Delete Status'}
        footer={
          <div>
            <Button
              loading={isSubmitting}
              disabled={isSubmitting}
              type="primary"
              onClick={() => {
                handleSubmit(true);
              }}
            >
              Confirm
            </Button>
            <Button
              loading={isSubmitting}
              disabled={isSubmitting}
              onClick={() => {}}
            >
              Cancel
            </Button>
          </div>
        }
      >
        <div>
          <p>
            Are you sure you want to delete{' '}
            {nonDeletableStatuses?.map((status) => (
              <span key={status.id} style={{ fontWeight: '600' }}>
                {status.name},{' '}
              </span>
            ))}{' '}
            all leads will be deleted.
          </p>
          <p>This action cannot be undone.</p>
        </div>
      </Modal>
    </div>
  );
};

export default LeadStatusCRUD;
