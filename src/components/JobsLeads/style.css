.drag-item {
  left: auto !important;
  top: auto !important;
}

.leads-drag-drop-container .ant-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* new UI for view leads */
.custom-new-ui-leads {
  width: 100%;
}
.custom-new-ui-leads .ant-modal .ant-modal-content {
  background: transparent !important;
}

.right-content-leads-custom {
  font-size: medium !important;
}

.right-content-leads-custom input,
.right-content-leads-custom .ant-select-selector,
.right-content-leads-custom .ant-input,
.ant-input-group {
  color: gray;
  font-size: medium;
  border-radius: 1rem !important;
}
/* .ant-picker-input input,
.ant-input-affix-wrapper {
  color: #576f89;
}
.ant-input-affix-wrapper,
.ant-input-number,
.ant-picker,
.ant-select-selector,
.ant-input {
  border-radius: 1rem !important;
} */
.right-content-leads-custom .ant-select-selector {
  height: 2.7rem !important;
}
.left-content-lead-custom .lead-tag {
  /* background-color: #0891b2; */
  padding: 1.3rem 1.5rem !important;
  border-radius: 0.5rem !important;
  /* color: white !important; */
  /* border: none !important; */
  box-shadow: 2px 2px rgba(165, 162, 162, 0.2);
  font-weight: 600;
}

.lead-tag:hover {
  box-shadow: 5px 5px rgba(165, 162, 162, 0.2);
}
/* .left-content-lead-custom{
  background-color: #0891b2;
} */
/* .lead-view-container .ant-modal-content{
  background-color: #2B80C9;
} */
/* .lead-view-container .ant-modal-title{
  background-color: #2B80C9;
} */
.comment-container .search-input,
.comment-container .search-input:focus,
.comment-container .search-input:focus-within,
.comment-container .search-input:focus-visible {
  border: none !important;
}
.comment-container .ant-form-item {
  margin-bottom: 0px !important;
}

div[data-rbd-droppable-id='board'] {
  height: 90%;
  /* border-color: #0891b2; */
  /* border-width: 1px; */
  /* padding: 5px; */
  /* border-radius: 0.5rem; */
}

.ant-collapse-expand-icon {
  align-self: center;
}

/* .ant-collapse-header {
  background-color: #eff6ff;
} */

.ant-modal:has(.open-lead-detail-mode) {
  margin: 0 !important;
  width: 48% !important;
  top: 0 !important;
}

.ant-modal-content:has(.open-lead-detail-mode) {
  height: 100vh !important;
  overflow-y: auto !important;
}

.existing-leads-container:has(.open-lead-detail-mode) {
  max-height: 100% !important;
}

.new-leads-container:has(.open-lead-detail-mode) {
  max-height: 100% !important;
}

.ant-collapse-header:has(.job-pinned) {
  background-color: #3b82f6 !important;
  border-radius: 8px !important;
}

.customized-spin .ant-spin-dot-item {
  background-color: white !important;
}

.vacancy-title-custom {
  color: #4a89dc;
  font-weight: 700;
}

/* ribbon */
.lead-card::before{
  position: absolute;
  top: 6.6rem;
  right:-0.5rem;
  content: '';
  background: #283593;
  height: 28px;
  width: 28px;
  transform : rotate(45deg);
}

.lead-card::after{
  position: absolute;
  content: attr(data-label);
  top: 82px;
  right: -14px;
  padding: 0.5rem;
  width: fit-content;
  background: #3949ab;
  color: white;
  text-align: center;
  box-shadow: 4px 4px 15px rgba(26, 35, 126, 0.2);
}

.right-content-leads-custom .ant-select-arrow{
  margin-top: 0 !important;
}

.custom-on-loading {
  background-color: rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 60vh;
  position: absolute;
  z-index: 1000;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}