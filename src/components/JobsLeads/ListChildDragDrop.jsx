/* eslint-disable react/no-unknown-property */
/* eslint-disable react/prop-types */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { Droppable, Draggable } from 'react-beautiful-dnd';
import ItemChildDragDrop from './ItemChildDragDrop';
import { useCallback } from 'react';
import _get from 'lodash/get';
import { Collapse, Spin, Image, Checkbox } from 'antd';
import { CaretRightOutlined, LoadingOutlined } from '@ant-design/icons';
import { useLazySearchJobLeadsCompaniesQuery } from '../../app/myLeads';
import { HiOutlineOfficeBuilding } from 'react-icons/hi';
import './style.css';
import { isEmpty } from 'lodash';
import nextIcon from '../../assets/img/icons/next.png';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { getLinkS3 } from '../../services/aws';

export const getBackgroundColor = (isDraggingOver, isDraggingFrom) => {
  if (isDraggingOver) {
    return '#FFEBE6';
  }
  if (isDraggingFrom) {
    return '#E6FCFF';
  }
  return '#EBECF0';
};

const scrollContainerHeight = 250;

const panelStyle = {
  marginBottom: 8,
  background: 'white',
  borderRadius: 8,
  borderWidth: 1,
  boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  alignItems: 'center',
}; 

const CompanyList = React.memo(function InnerQuoteList(props) {
  const {
    leads,
    company_id,
    total_lead,
    logoCompany,
    isSelectionMode,
    onChangeCheckbox,
    selectedRowKeys,
    isDisableDnd,
    handleDeleteLead
  } = props;
  const [imageUrl, setImageUrl] = useState(null);

  const fetchImageUrl = async () => {
    try {
      const { data } = await getLinkS3(logoCompany);
      setImageUrl(data);
    } catch (error) {
      setImageUrl(zileoLogo);
    }
  };

  useEffect(() => {
    fetchImageUrl();
  }, []);

  const isChecked = () => {
    const comparingLeadIds = leads.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );

    if (leads?.length === comparingLeadIds?.length) {
      return true;
    } else {
      return false;
    }
  };

  // const [quotes, setQuotes] = useState([]);
  // const [searchJobLeadsCompaniesQuery, { isFetching }] =
  //   useLazySearchJobLeadsCompaniesQuery();

  // const handleSelectCompany = useCallback(
  //   async (id) => {
  //     const companyId = id.toString();
  //     const companyData = await searchJobLeadsCompaniesQuery({
  //       statusId: props?.statusId,
  //       companyId,
  //     });

  //     const convertData = await _get(companyData, 'data.result.items', []);
  //     setQuotes(convertData);
  //   },
  //   [props?.statusId, searchJobLeadsCompaniesQuery]
  // );
  const collapseItem = [
    {
      key: props?.company_id,
      label: (
        <div className="font-medium flex items-start flex flex-col">
          <div
            className={`relative grid ${isSelectionMode ? `grid-cols-7` : `grid-cols-7`} items-center gap-2 w-full justify-between`}
          >
            {isSelectionMode && (
              <Checkbox
                className="absolute -left-10 top-0"
                checked={isChecked()}
                value={`company_id/${props?.company_id}`}
                defaultChecked={true}
                onClick={(e) => e.stopPropagation()}
                onChange={onChangeCheckbox}
              />
            )}
            <div className="col-span-2">
              {!imageUrl && <Spin />}
              {imageUrl && (
                <Image
                  width={'35px'}
                  src={imageUrl}
                  onError={() => setImageUrl(zileoLogo)}
                />
              )}
            </div>
            <span
              className="text-[#379df1] line-clamp-1 col-span-5 w-full"
              title={props?.jl_company_name}
            >
              {props?.jl_company_name}
            </span>
          </div>
          {/* <span className="font-normal text-sm text-[#565961]">
            {props?.company_id}
          </span> */}

          <div className="flex items-center text-sm text-[#565961]">
            {/* <HiOutlineOfficeBuilding className="mr-3" />  */}
            {/* <span>{total_lead || 0} leads</span> */}
            <span>{leads?.length || 0} leads</span>
          </div>
        </div>
      ),
      children:
        // isFetching ? (
        //   <Spin
        //     className="flex justify-center"
        //     indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
        //   />
        // ) :
        !props?.isDragging ? (
          <InnerList
            isDisableDnd={isDisableDnd}
            reloadJobLeads={props?.reloadJobLeads}
            handleDeleteLead={handleDeleteLead}
            quotes={leads || []}
            // quotes={quotes}
            title={props?.title}
            dropProvided={props?.dropProvided}
            isDragging={props?.isDragging}
          />
        ) : null,
      style: panelStyle,
    },
  ];

  return (
    <Collapse
      bordered={false}
      expandIconPosition="end"
      expandIcon={({ isActive }) => (
        <Image
          src={nextIcon}
          preview={false}
          className={`transition delay-200 ${isActive && `rotate-90`}`}
          width={20}
        />
        // <CaretRightOutlined
        //   style={{ color: '#0079c2' }}
        //   rotate={isActive ? 90 : 0}
        // />
      )}
      items={collapseItem}
      style={{
        background: 'transparent',
      }}
      // onChange={handleSelectCompany}
    />
  );
});

const InnerQuoteList = React.memo(function InnerQuoteList(props) {
  return props.quotes.map((quote, index) => (
    <Draggable
      isDragDisabled={props.isDisableDnd}
      key={quote.id}
      draggableId={`${quote.company_id}/lead-${quote.id}`}
      // draggableId={`lead-${quote.id}`}
      index={index}
    >
      {(dragProvided, dragSnapshot) => (
        <ItemChildDragDrop
          reloadJobLeads={props.reloadJobLeads}
          handleDeleteLead={props.handleDeleteLead}
          key={quote.id}
          quote={quote}
          isDragging={dragSnapshot.isDragging}
          isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
          provided={dragProvided}
        />
      )}
    </Draggable>
  ));
});

function InnerList(props) {
  const { quotes, dropProvided, reloadJobLeads, isDisableDnd, handleDeleteLead } = props;
  const title = props.title ? <p>{props.title}</p> : null;
  return (
    <div>
      {title}
      <div
        className={`min-h-${scrollContainerHeight} pb-0 -mb-6 -mx-2`}
        ref={dropProvided.innerRef}
      >
        <InnerQuoteList
          isDisableDnd={isDisableDnd}
          quotes={quotes}
          reloadJobLeads={reloadJobLeads}
          handleDeleteLead={handleDeleteLead}
        />
        {dropProvided.placeholder}
      </div>
    </div>
  );
}

export default function ListChildDragDrop(props) {
  const {
    ignoreContainerClipping,
    internalScroll,
    isDropDisabled,
    isCombineEnabled,
    listId = 'LIST',
    listType,
    companies,
    title,
    statusId,
    reloadJobLeads,
    placeholderProps,
    isDragAndDrop,
    isSelectionMode,
    onChangeCheckbox,
    isDisableDnd,
    selectedRowKeys,
    handleDeleteLead
  } = props;
  return (
    <Droppable
      isDragDisabled={isDisableDnd || isSelectionMode}
      droppableId={listId}
      type={listType}
      ignoreContainerClipping={ignoreContainerClipping}
      isDropDisabled={isDropDisabled || isSelectionMode}
      isCombineEnabled={isCombineEnabled}
      renderClone={null}
    >
      {(dropProvided, dropSnapshot) => (
        <div
          className={`bg-${getBackgroundColor(props.isDraggingOver, props.isDraggingFrom)} ${props.isDropDisabled ? 'opacity-50' : 'opacity-100'} p-1 pb-0 transition duration-200 ease-in-out select-none flex flex-col w-250`}
          isDraggingOver={dropSnapshot.isDraggingOver}
          isDropDisabled={isDropDisabled || isSelectionMode}
          isDraggingFrom={Boolean(dropSnapshot.draggingFromThisWith)}
          {...dropProvided.droppableProps}
          ref={dropProvided.innerRef}
        >
          {internalScroll ? (
            <div>
              <div ref={dropProvided.innerRef} {...dropProvided.droppableProps}>
                {companies.length ? (
                  companies?.map((item, index) => (
                    <Draggable
                      isDragDisabled={isDisableDnd || isSelectionMode}
                      index={index}
                      key={item?.company_id}
                      draggableId={`company/${listId}/${item?.company_id}`}
                    >
                      {(provided, snapshot) => (
                        <div
                          className="select-none drag-item"
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          {!snapshot.isDragging &&
                          !isEmpty(placeholderProps) &&
                          isDragAndDrop ? null : (
                            <CompanyList
                              selectedRowKeys={selectedRowKeys}
                              isSelectionMode={isSelectionMode}
                              onChangeCheckbox={onChangeCheckbox}
                              key={item?.company_id}
                              statusId={statusId}
                              title={title}
                              handleDeleteLead={handleDeleteLead}
                              reloadJobLeads={reloadJobLeads}
                              dropProvided={dropProvided}
                              isDragging={snapshot.isDragging}
                              isDisableDnd={isDisableDnd}
                              {...item}
                            />
                          )}
                          {provided.placeholder}
                        </div>
                      )}
                    </Draggable>
                  ))
                ) : (
                  <div className="p-2 border-dashed border-2 rounded-md h-[580px] font-bold flex items-center justify-center">
                    Drag lead here
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div ref={dropProvided.innerRef} {...dropProvided.droppableProps}>
              {companies?.map(
                (item, index) =>
                  item?.leads?.length > 0 && (
                    <Draggable
                      isDragDisabled={isDisableDnd || isSelectionMode}
                      index={index}
                      key={item?.company_id}
                      draggableId={`company/${listId}/${item?.company_id}`}
                    >
                      {(provided) => (
                        <div
                          className="select-none bg-green"
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          <CompanyList
                            key={item?.company_id}
                            statusId={statusId}
                            title={title}
                            reloadJobLeads={reloadJobLeads}
                            dropProvided={dropProvided}
                            {...item}
                          />{' '}
                          {provided.placeholder}
                        </div>
                      )}
                    </Draggable>
                  )
              )}
            </div>
          )}
        </div>
      )}
    </Droppable>
  );
}
