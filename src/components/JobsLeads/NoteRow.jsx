import { BookOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Input, notification, Popover, Spin, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { createComment, getLeadComment } from '../../services/comment';

const NoteRow = ({ lead }) => {
  const [loading, setLoading] = useState(false);
  const [latestNote, setLatestNote] = useState('');
  const [note, setNote] = useState('');
  const [openNewNote, setOpenNewNote] = useState(false);

  const showNewNote = () => setOpenNewNote(true);
  const hideNewNote = () => setOpenNewNote(false);

  const handleCreateComment = async () => {
    setLoading(true);
    try {
      const data = await createComment(lead?.leadRawId || lead?.id, note);
      if (data) {
        setNote('');
        setLatestNote(note);
      }
      setLoading(false);
      hideNewNote();
    } catch (error) {
      setLoading(false);
      notification.error({
        message: 'Something went wrong!',
      });
    }
  };

  const getLatestNote = async () => {
    setLoading(true);
    try {
      const { data } = await getLeadComment(lead?.leadRawId || lead?.id);
      if (data?.result?.length > 0) {
        const latestNote = data.result[data.result.length - 1];
        setLatestNote(latestNote?.content);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const text = (
    <span
      onClick={(e) => e.stopPropagation()}
      className="text-base font-semibold text-cyan-600"
    >
      Add new note
    </span>
  );

  const content = (
    <div
      onClick={(e) => e.stopPropagation()}
      className="flex items-center gap-2"
    >
      <Input
        value={note}
        onChange={(e) => setNote(e.target.value)}
        className="search-input my-3 min-w-fit"
        placeholder="Enter your note here"
        onPressEnter={(e) => {
          handleCreateComment();
        }}
      />
      <Button
        loading={loading}
        type="primary"
        onClick={handleCreateComment}
        icon={<PlusCircleOutlined />}
      >
        Add
      </Button>
    </div>
  );

  useEffect(() => {
    getLatestNote();
  }, [lead]);

  return (
    <Tooltip title={latestNote}>
      <div className="relative flex items-center justify-start font-medium text-sm w-full relative border rounded-md p-2 border-cyan-600">
        {latestNote && (
          <>
            <span className="line-clamp-1 max-w-[18rem]">{latestNote}</span>
            {/* <Popover
              placement="left"
              title={text}
              content={content}
              open={openNewNote}
              destroyTooltipOnHide
              action="click"
              onOpenChange={setOpenNewNote}
            >
              <Button
                title="Quick Add Note"
                onClick={showNewNote}
                type="link"
                icon={<PlusCircleOutlined />}
              />
            </Popover> */}
          </>
        )}
        {!loading && !latestNote?.trim() && (
          <>
            <span className="text-gray-400 italic">No notes</span>
          </>
        )}
        {loading && (
          <span className="text-gray-400 italic">
            <Spin spinning />
          </span>
        )}
        {!loading && (
          <Popover
            placement="left"
            title={text}
            content={content}
            open={openNewNote}
            destroyTooltipOnHide
            action="click"
            onOpenChange={setOpenNewNote}
          >
            <Button
              title="Quick Add Note"
              className="absolute right-0 z-[9999]"
              type="link"
              icon={<PlusCircleOutlined />}
            />
          </Popover>
        )}
      </div>
    </Tooltip>
  );
};

export default NoteRow;
