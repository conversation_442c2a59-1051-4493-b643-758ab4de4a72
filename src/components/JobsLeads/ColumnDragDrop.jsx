/* eslint-disable react/no-unknown-property */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ArrowDownOutlined,
  BgColorsOutlined,
  CloseOutlined,
  FilterOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';

import _debounce from 'lodash/debounce';
import _get from 'lodash/get';
import _size from 'lodash/size';
import { isEmpty } from 'lodash';

import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  notification,
} from 'antd';
import { Draggable, Droppable } from 'react-beautiful-dnd';

import { useLazyGetJobLeadsCompaniesQuery } from '../../app/myLeads';
import { getPotentialLeadPercentValue } from '../../services/jobLeads';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import ListChildDragDrop from './ListChildDragDrop';
import {
  savePotentialLeadValue,
  selectPotentialLeadValue,
} from '../../store/common';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

const ColumnDragDrop = (props) => {
  const onSubmitBoardFilter = props.onSubmitBoardFilter;
  const selectedRowKeys = props.selectedRowKeys;
  const isSelectionMode = props.isSelectionMode;
  const onChangeCheckbox = props.onChangeCheckbox;
  const id = props.id;
  const title = props.title; 
  const index = props.index;
  const searchText = props.searchText ?? '';
  const jobLeadsWithStatus = props.jobLeadsWithStatus;
  const reloadJobLeads = props.reloadJobLeads;
  const rawFilters = jobLeadsWithStatus.filters;
  const handleDeleteLead = props.handleDeleteLead;
  const { isDragAndDrop, placeholderProps, setJobLeads, jobLeads } = props;

  const containerRef = useRef(null);
  const [isPendingLoadMore, setIsPendingLoadMore] = useState(false);
  const [page, setPage] = useState(1);
  const [totalInfor, setTotalInfor] = useState({
    leads: 0,
    bonus: 0,
  });

  // filter on board

  const [form] = Form.useForm();
  const filters = Form.useWatch('filters', form);

  const onFinish = async (values) => {
    let payload = {};
    if (values?.filters && values?.filters?.length > 0) {
      for await (const filter of values?.filters) {
        let value = filter?.value;
        if (!value) return;
        payload[filter?.field] = value;
      }
    }
    console.log('onFinish values: ', values);
    onSubmitBoardFilter(jobLeadsWithStatus.id, payload);
    setTotalFilter(Object.keys(payload).length);
    closeFilterModal();
  };
  const [totalFilters, setTotalFilter] = useState(0);
  const [filtersWatch, setFiltersWatch] = useState([]);
  const [openFilterModal, setOpenFilterModal] = useState(false);

  const showFilterModal = () => setOpenFilterModal(true);
  const closeFilterModal = () => setOpenFilterModal(false);

  const fieldItems = [
    {
      value: 'employment_type',
      label: 'Job Type',
    },
    {
      value: 'address_city',
      label: 'Address City',
    },
    {
      value: 'address_country',
      label: 'Address Country',
    },
    {
      value: 'source',
      label: 'Source',
    },
    {
      value: 'company_contact_name',
      label: 'Company Contact',
    },
    // {
    //   value: 'creator',
    //   label: 'Creator',
    // },
    {
      value: 'title',
      label: 'Job Title',
    },
  ];

  const jobTypeOptions = [
    {
      value: 'jobType',
      label: 'Job Type',
    },
    {
      value: 'location',
      label: 'Location',
    },
  ];

  const handleChangeFieldName = (key) => (value) => {
    // setValue(name, value);
    const newFiltersList = filtersWatch.map((filter) => {
      if (filter?.key === key) {
        return { ...filter, value };
      } else {
        return { ...filter };
      }
    });
    setFiltersWatch(newFiltersList);
  };

  const checkValueInput = (index) => {
    // if (!filtersWatch || filtersWatch?.length <= 0)
    return (
      <Input
        style={{
          width: 260,
        }}
      />
    );
    // const field = filtersWatch?.find(
    //   (_filter, filterIndex) => filterIndex === index
    // )?.field;

    // switch (field) {
    //   case 'jobType':
    //     return (
    //       <Select
    //         // mode="multiple"
    //         placeholder="select single or multiple"
    //         options={jobTypeOptions}
    //         style={{
    //           width: 260,
    //         }}
    //       />
    //     );
    //   // case 'salary':
    //   //   return (
    //   //     <RangePicker
    //   //       format={'DD-MM-YYYY'}
    //   //       style={{
    //   //         width: 230,
    //   //       }}
    //   //     />
    //   //   );
    //   case 'source':
    //     return (
    //       <Select
    //         mode="multiple"
    //         placeholder="select single or multiple"
    //         options={jobTypeOptions}
    //         style={{
    //           width: 260,
    //         }}
    //       />
    //     );
    //   case 'assignee':
    //     return (
    //       <Select
    //         mode="multiple"
    //         placeholder="select single or multiple"
    //         options={jobTypeOptions}
    //         style={{
    //           width: 260,
    //         }}
    //       />
    //     );
    //   case 'creator':
    //     return (
    //       <Select
    //         mode="multiple"
    //         placeholder="select single or multiple"
    //         options={jobTypeOptions}
    //         style={{
    //           width: 260,
    //         }}
    //       />
    //     );
    //   default:
    //     return (
    //       <Input
    //         style={{
    //           width: 260,
    //         }}
    //       />
    //     );
    // }
  };

  const handleFilter = () => {
    closeFilterModal();
  };

  const handleClearFilter = () => {
    form.resetFields();
    setTotalFilter(0);
    setFiltersWatch([]);
    closeFilterModal();
    onSubmitBoardFilter(jobLeadsWithStatus?.id, null);
  };

  const isChecked = () => {
    const jobLeadsIdWithStatusId = jobLeadsWithStatus?.companies.flatMap(
      (company) => company?.leads
    );
    const comparingLeadIds = jobLeadsIdWithStatusId.filter((item) =>
      selectedRowKeys.includes(item?.id)
    );

    if (jobLeadsIdWithStatusId?.length === comparingLeadIds?.length) {
      return true;
    } else {
      return false;
    }
  };

  const [getJobLeadsCompanies, { isFetching }] =
    useLazyGetJobLeadsCompaniesQuery();
  const [allData, setAllData] = useState([]);
  
  const dispatch = useDispatch();

  const { data: potentialLeadValue } = useQuery(['POTENTIAL_RETURN_PERCENT'], {
    queryFn: async () => {
      const potentialLeadValue = useSelector(selectPotentialLeadValue);

      if (potentialLeadValue) {
        return potentialLeadValue;
      }

      const { data } = await getPotentialLeadPercentValue();

      dispatch(savePotentialLeadValue(data.result.potentialLeadValue));

      return data.result.potentialLeadValue;
    },
  });

  const handleScroll = useCallback(
    _debounce(() => {
      const container = containerRef.current;
      if (container) {
        const isAtBottom =
          container.scrollHeight - container.scrollTop <=
          container.clientHeight + 1;
        if (isAtBottom) {
          setPage((prevPage) => prevPage + 1);
        }
      }
    }, 1000),
    []
  );

  // reset state if have searchText
  // useEffect(() => {
  //   setAllData([]);
  // }, [searchText]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       // Gọi API để lấy dữ liệu mới
  //       const newData = await getJobLeadsCompanies({
  //         statusId: id,
  //         page,
  //         search: searchText,
  //         limit: 10,
  //       });

  //       const convertData = await _get(newData, 'data.result.items', []);

  //       if (_size(convertData) < 1) {
  //         setIsPendingLoadMore(true);
  //       } else {
  //         // Dồn dữ liệu mới vào dữ liệu cũ
  //         setAllData((prevData) => [...prevData, ...convertData]);
  //         // const jobLeadsTemp = [...jobLeads];
  //         // const jobLeadIndex = jobLeads.findIndex(
  //         //   (status) => status.id === jobLeadsWithStatus.id
  //         // );
  //         // if (jobLeadIndex) {
  //         //   const jobLeadsTemp = jobLeads.map((jobLead, index) => {
  //         //     if (index === jobLeadIndex) {
  //         //       return {
  //         //         ...jobLead,
  //         //         companies: [...convertData],
  //         //       };
  //         //     }
  //         //     return jobLead;
  //         //   });
  //         //   setJobLeads([...jobLeadsTemp]);
  //         //   // setJobLeads([...jobLeads, ...[...jobLeadsTemp[jobLeadIndex]]]);
  //         // }

  //       }
  //     } catch (e) {
  //       notification.error({
  //         message: 'Error!',
  //         description: e,
  //       });
  //     }
  //   };

  //   fetchData();
  // }, []);
  // }, [id, page, searchText]);

  useEffect(() => {
    if (jobLeadsWithStatus?.companies?.length > 0) {
      const leadsListTemp =
        jobLeadsWithStatus?.companies?.flatMap((company) => company?.leads) ||
        [];
      const leadSalaryValue =
        leadsListTemp.reduce(
          (total, lead) => total + parseFloat(lead?.salary || 0),
          0
        ) || 0;

      // const bonusValue = jobLeadsWithStatus?.total_salary
      //   ? parseFloat(jobLeadsWithStatus?.total_salary).toFixed(2) *
      //     (potentialLeadValue / 100)
      //   : 0;
      const bonusValue = parseFloat(
        leadSalaryValue * (potentialLeadValue / 100)
      ).toFixed(2);
      // console.log("jobLeadsWithStatus: ", jobLeadsWithStatus)
      setTotalInfor({
        leads: leadsListTemp?.length,
        bonus: bonusValue,
      });
    } else {
      setTotalInfor({
        leads: 0,
        bonus: 0,
      });
    }
  }, [JSON.stringify(jobLeadsWithStatus)]);

  useEffect(() => {
    setFiltersWatch(filters);
  }, [filters]);

  useEffect(() => {
    const currFilters = [];
    // console.log('rawFilters: ', rawFilters);
    if (Object.keys(rawFilters || {}).length > 0) {
      for (const [field, value] of Object.entries(rawFilters)) {
        currFilters.push({ field, value });
      }
      form.setFieldValue('filters', [...currFilters]);
    } else {
      form.setFieldValue('filters', []);
    }
  }, [rawFilters, openFilterModal]);

  return (
    <>
      <Draggable
        isDragDisabled={
          Object.keys(rawFilters || {}).length > 0 || isSelectionMode
        }
        key={jobLeadsWithStatus.id}
        draggableId={jobLeadsWithStatus.id}
        draggableProps={jobLeadsWithStatus}
        index={index}
      >
        {(provided, snapshot) => (
          <Col
            // span={6}
            className="min-w-[18rem] w-full h-full bg-white"
            key={index.toString()}
            {...provided.draggableProps}
            ref={provided.innerRef}
          >
            <div>
              <h4
                style={{
                  background: jobLeadsWithStatus.color
                    ? jobLeadsWithStatus.color
                    : '#f5f5f6',
                }}
                className={'p-2 rounded-t-md font-semibold bg-[#f5f5f6]'}
                isDragging={snapshot.isDragging}
                {...provided.dragHandleProps}
                aria-label={`${title} quote list`}
              >
                {isDragAndDrop ? (
                  <div className="flex gap-2 text-center align-middle">
                    <ArrowDownOutlined />
                    <p>Drag your lead here</p>
                    <ArrowDownOutlined />
                  </div>
                ) : (
                  <div className="pl-3">
                    <div className="flex justify-between items-center relative">
                      <div className="flex items-center gap-2 justify-center">
                        <span className="block text-lg">{title}</span>
                        <Button
                          className=""
                          type="text"
                          icon={<FilterOutlined />}
                          onClick={showFilterModal}
                        >
                          {totalFilters > 0 ? `(${totalFilters})` : ''}
                        </Button>
                      </div>
                      {isSelectionMode && parseInt(totalInfor?.leads) > 0 && (
                        <div className="absolute top-0 -left-10">
                          <Checkbox
                            checked={isChecked()}
                            defaultChecked={true}
                            value={`lead_status_id/${jobLeadsWithStatus?.id}`}
                            onChange={onChangeCheckbox}
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2 text-[#565961] text-sm font-normal">
                      <span>{totalInfor?.leads || 0} leads</span>
                      <span>·</span>
                      <span title="Potential Lead Value">
                        {totalInfor?.bonus > 0
                          ? `$ ${totalInfor?.bonus}`
                          : 'N/A'}
                      </span>
                    </div>
                  </div>
                )}
              </h4>

              <div
                ref={containerRef}
                className={`max-h-[600px] h-[600px] ${isDragAndDrop ? 'border-dashed  border-opacity-70 border-2' : ''} bg-[#F5F7F8] `}
                onScroll={() => {
                  if (!isPendingLoadMore) {
                    handleScroll();
                  }
                }}
              >
                {/* {!isFetching &&  */}
                <Droppable
                  isDropDisabled={
                    Object.keys(rawFilters || {}).length > 0 || isSelectionMode
                  }
                  droppableId={id}
                  type={'GROUP'}
                  isCombineEnabled={props.isCombineEnabled}
                  renderClone={null}
                >
                  {(dropProvided, dropSnapshot) => (
                    <div
                      ref={dropProvided.innerRef}
                      {...dropProvided.droppableProps}
                    >
                      <ListChildDragDrop
                        listId={jobLeadsWithStatus.id}
                        listType="CHILD"
                        statusId={id}
                        companies={jobLeadsWithStatus?.companies || []}
                        // companies={allData}
                        internalScroll={props.isScrollable}
                        isCombineEnabled={Boolean(props.isCombineEnabled)}
                        reloadJobLeads={reloadJobLeads}
                        handleDeleteLead={handleDeleteLead}
                        placeholderProps={placeholderProps}
                        isDragAndDrop={isDragAndDrop}
                        isSelectionMode={isSelectionMode}
                        onChangeCheckbox={onChangeCheckbox}
                        selectedRowKeys={selectedRowKeys}
                        isDisableDnd={Object.keys(rawFilters || {}).length > 0}
                      />
                      {provided.placeholder}
                      {!isEmpty(placeholderProps) &&
                        dropSnapshot.isDraggingOver && (
                          <div
                            className="absolute bg-white rounded-md border-dashed border-blueSecondary border-opacity-70 border-2 shadow-md"
                            style={{
                              top:
                                placeholderProps.clientY +
                                placeholderProps.clientHeight +
                                placeholderProps.clientY * 0.08,
                              left: placeholderProps.clientX - 10,
                              height: placeholderProps.clientHeight * 1.4,
                              width: placeholderProps.clientWidth,
                            }}
                          />
                        )}
                    </div>
                  )}
                </Droppable>
                {/* } */}

                {/* {isFetching && (
                <Row>
                  <Col span={24}>
                    <LoadingAdvanced isSkeleton className="px-2 pt-2" />
                  </Col>
                </Row>
              )} */}
              </div>
            </div>
          </Col>
        )}
      </Draggable>
      {openFilterModal && (
        <Modal
          width={600}
          title={
            <div className="flex gap-2 items-center">
              <FilterOutlined />
              <span>Filters on</span>
              <div
                style={{
                  background: jobLeadsWithStatus.color
                    ? jobLeadsWithStatus.color
                    : '#f5f5f6',
                }}
                className="p-2 rounded-md"
              >
                {jobLeadsWithStatus?.name}
              </div>
            </div>
          }
          open={openFilterModal}
          footer={null}
          // onOk={handleFilter}
          onCancel={closeFilterModal}
          // okText='Filter boards'
          // cancelText='Clear'
        >
          <div className="flex flex-col gap-4">
            <div>
              <div className="border p-5 rounded-lg">
                <div className="text-base font-medium pb-2">
                  Add Filters (condition{' '}
                  <p className="font-bold inline">AND </p>
                  condition)
                </div>
                <Form
                  form={form}
                  name="dynamic_form_nest_item"
                  onFinish={onFinish}
                  autoComplete="off"
                  className="flex flex-col gap-2"
                >
                  <Form.List name="filters">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, ...restField }, index) => (
                          <Space
                            key={key}
                            style={{
                              display: 'flex',
                            }}
                            align="baseline"
                          >
                            <Form.Item
                              {...restField}
                              name={[name, 'field']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Please choose a field name',
                                },
                              ]}
                            >
                              <Select
                                showSearch
                                style={{
                                  width: 220,
                                }}
                                placeholder="Search to Select"
                                optionFilterProp="children"
                                filterOption={(input, option) =>
                                  (option?.label ?? '').includes(input)
                                }
                                filterSort={(optionA, optionB) =>
                                  (optionA?.label ?? '')
                                    .toLowerCase()
                                    .localeCompare(
                                      (optionB?.label ?? '').toLowerCase()
                                    )
                                }
                                options={fieldItems}
                                onChange={handleChangeFieldName(key)}
                              />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              rules={[
                                {
                                  required: true,
                                  message: 'Missing value',
                                },
                              ]}
                            >
                              {checkValueInput(index)}
                            </Form.Item>
                            <MinusCircleOutlined onClick={() => remove(name)} />
                          </Space>
                        ))}
                        <Form.Item>
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            icon={<PlusOutlined />}
                          >
                            Add a Field to Filter
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>

                  <div className="w-full grid grid-cols-2 gap-2">
                    <Button
                      htmlType="button"
                      // disabled={filtersWatch?.length === 0 || !filtersWatch}
                      onClick={handleClearFilter}
                      icon={<CloseOutlined />}
                      danger
                    >
                      Clear
                    </Button>
                    <Button
                      disabled={filtersWatch?.length === 0}
                      htmlType="submit"
                      icon={<BgColorsOutlined />}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </Form>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ColumnDragDrop;
