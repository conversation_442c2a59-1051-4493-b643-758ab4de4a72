/* eslint-disable react/prop-types */
import React, { useState, useMemo, useEffect } from 'react';

// import _debounce from 'lodash/debounce';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _range from 'lodash/range';

import { Col, notification } from 'antd';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';

import { useAuth } from '../../store/auth';
// import { SearchOutlined } from '@ant-design/icons';
import {
  useUpdateBulkLeadsMutation,
  useChangeLogOfJobLeadsMutation,
  useChangeStatusByCompanyMutation,
} from '../../app/myLeads';
import ColumnDragDrop from './ColumnDragDrop';

import LoadingAdvanced from '../../common/LoadingAdvanced';
import {
  useLazyGetJobLeadsCompaniesQuery,
  useLazySearchJobLeadsCompaniesQuery,
} from '../../app/myLeads';

const queryAttr = 'data-rbd-drag-handle-draggable-id';
const LEAD_ID = 'lead-';

const LeadsDragDrop = ({
  jobLeadsWithStatuses,
  reloadJobLeads,
  searchText,
  searchedLeads,
  setJobLeadsGlobal,
  rawSearchedLeads,
  isSelectionMode,
  onChangeCheckbox,
  setSelectedRowKeys,
  selectedRowKeys,
  rawJobLeadsWithStatuses,
  rawSelectedRowKeys,
  setDisableSearchByCompany,
}) => {
  const { profile } = useAuth();

  const [placeholderProps, setPlaceholderProps] = useState({});
  const [jobLeads, setJobLeads] = useState([]);
  const [isDragAndDrop, setDragAndDrop] = useState(false);

  const [leadLoading, setLeadLoading] = useState(true);

  const userId = useMemo(() => {
    return _get(profile, 'user.id') || '';
  }, [profile]);

  const [changeLogOfJobLeads] = useChangeLogOfJobLeadsMutation();
  const [changeStatusByCompany] = useChangeStatusByCompanyMutation();
  const [updateBulkLeadsMutation] = useUpdateBulkLeadsMutation();

  const [getJobLeadsCompanies] = useLazyGetJobLeadsCompaniesQuery();

  const [searchJobLeadsCompaniesQuery] = useLazySearchJobLeadsCompaniesQuery();

  const getLeads = async ({ statusId, companyId }) => {
    const companyData = await searchJobLeadsCompaniesQuery({
      statusId,
      companyId,
    });

    const convertData = await _get(companyData, 'data.result.items', []);
    return convertData;
  };

  const getCompanies = async () => {
    const getCompaniesPromises = await Promise.all(
      jobLeadsWithStatuses.map(async (jobLead) => {
        return await getJobLeadsCompanies({
          statusId: jobLead.id,
          page: 1,
          search: searchText || '',
          limit: 10,
        });
      })
    );

    const jobLeadsWithStatusesTemp = await Promise.all(
      jobLeadsWithStatuses.map(async (jobLead) => {
        const companies = getCompaniesPromises.find(
          (companyObj) => companyObj.originalArgs.statusId === jobLead.id
        );
        let companiesList = _get(companies, 'data.result.items', []);
        if (companiesList.length > 0) {
          const companiesListTemp = await Promise.all(
            companiesList.map(async (company) => {
              // if(company?.company_id === 'bullhorn-client-corporate-23583') return;
              const leadsTemp = await getLeads({
                statusId: jobLead.id,
                companyId: company.company_id,
              });
              if (leadsTemp.length > 0) {
                return { ...company, leads: leadsTemp };
              } else {
                return company;
              }
            })
          );
          companiesList = [...companiesListTemp];
        }

        if (companies) {
          return { ...jobLead, companies: companiesList };
        }
        return jobLead;
      })
    );

    return jobLeadsWithStatusesTemp;
  };

  useEffect(() => {
    if (jobLeadsWithStatuses.length > 0 && searchedLeads?.length === 0) {
      getCompanies().then((res) => {
        setJobLeads(res);
        setJobLeadsGlobal(res);
        setLeadLoading(false);
      });
    } else {
      setLeadLoading(false);
    }
  }, [searchText]);

  useEffect(() => {
    if (searchedLeads?.length > 0) {
      console.log('searchedLeads: ', searchedLeads);
      setJobLeads([...searchedLeads]);
    } else {
      setJobLeads(jobLeadsWithStatuses);
    }
  }, [searchedLeads]);

  const reorderCompanies = ({ source, destination, companyId }) => {
    // const { companyId, newStatusId } = payload;
    const dragCompany = [...jobLeads]
      .flatMap((status) => status.companies)
      .filter((company) => company?.company_id === companyId);
    let sourceStatus = jobLeads.find(
      (status) => status?.id === source?.droppableId
    );
    let destinationStatus = jobLeads.find(
      (status) => status?.id === destination?.droppableId
    );

    // Case there no existing company in status
    // source
    sourceStatus.companies =
      sourceStatus?.companies?.filter(
        (company) => company?.company_id !== companyId
      ) || [];
    // destination
    if (
      dragCompany?.length > 1 &&
      destinationStatus?.companies?.find(
        (company) => company?.company_id === companyId
      )
    ) {
      let combineLeads = [];
      for (var company of dragCompany) {
        combineLeads = combineLeads.concat(company?.leads);
      }

      const destinationCompaniesTemp = [...destinationStatus?.companies].map(
        (company) => {
          if (company.company_id === companyId) {
            return { ...company, leads: combineLeads };
          } else {
            return { ...company };
          }
        }
      );
      destinationStatus.companies = destinationCompaniesTemp;
    } else {
      const destinationCompaniesTemp = [...destinationStatus?.companies];
      destinationCompaniesTemp.push(dragCompany[0]);
      destinationStatus.companies = destinationCompaniesTemp;
    }

    // Case existing company in status -> combine leads
  };

  const reorderLeads = ({ source, destination, jobLeadId }) => {
    const ids = jobLeadId.split('/');
    const companyId = ids[0];
    const leadMovedId = ids[1];
    let sourceStatus = jobLeads.find(
      (status) => status?.id === source.droppableId
    );
    let destinationStatus = jobLeads.find(
      (status) => status?.id === destination.droppableId
    );
    if (!sourceStatus || !sourceStatus) return;
    const allCompanies = jobLeads.flatMap((status) => status?.companies || []);
    const allLeads = allCompanies.flatMap((status) => status?.leads);
    let sourceCompany = allCompanies.find(
      (company) => company.company_id === companyId
    );
    const leadToMove = allLeads?.find((lead) => lead?.id === leadMovedId);
    // let sourceCompany =

    // source company case length === 1
    // Remove company from status
    if (sourceCompany?.leads?.length === 1) {
      sourceStatus.companies = sourceStatus?.companies?.filter(
        (company) => company?.company_id !== companyId
      );
    }
    // source company case length !== 1
    // remove lead from company
    else if (sourceCompany) {
      const companyIndex = sourceStatus.companies.findIndex(
        (company) => company.company_id === companyId
      );
      sourceCompany.leads = sourceCompany?.leads?.filter(
        (lead) => lead?.id !== leadMovedId
      );
      sourceStatus.companies[companyIndex] = sourceCompany;
    }

    // destination company case have company object
    if (
      destinationStatus?.companies?.length > 0 &&
      destinationStatus?.companies?.find(
        (company) => company.company_id === companyId
      )
    ) {
      const newLead = { ...leadToMove };
      // console.log("newLead: ", newLead)
      const newDesCompanies = destinationStatus?.companies?.map((company) => {
        if (company.company_id === companyId) {
          const leadsTemp = [...company?.leads];
          leadsTemp.push(newLead);
          return { ...company, leads: leadsTemp };
        } else {
          return company;
        }
      });
      console.log('newDesCompanies: ', newDesCompanies);
      destinationStatus.companies = [...newDesCompanies];
    }
    // destination company case dont have company object
    else {
      const newLead = { ...leadToMove };
      const newCompany = { ...sourceCompany, leads: [newLead] };
      const newDesCompanies = [...destinationStatus.companies];
      newDesCompanies.push(newCompany);
      destinationStatus.companies = [...newDesCompanies];
    }
  };

  const handleChangeLogOfJobLeads = async (result) => {
    const isPARENT = _get(result, 'type') === 'PARENT';
    const isCHILD = _get(result, 'type') === 'CHILD';
    const destination = _get(result, 'destination');
    const source = _get(result, 'source');

    // if (source.droppableId === destination.droppableId) return;
    if (isPARENT) {
      try {
        // setLeadLoading(true)
        const destinationIndex = _get(result, 'destination.index');
        const sourceIndex = _get(result, 'source.index');

        const updatedJobLeads = [...jobLeads];
        // const updatedJobLeads = [...jobLeadsWithStatuses];
        // Lấy phần tử được di chuyển từ vị trí gốc
        const [movedItem] = updatedJobLeads.splice(sourceIndex, 1);

        // Chèn phần tử vào vị trí đích
        updatedJobLeads.splice(destinationIndex, 0, movedItem);

        setJobLeads(updatedJobLeads);

        // Tạo payload
        const payload = {
          data: updatedJobLeads.map((item, index) => ({
            id: item?.id,
            orderOfDisplay: index,
          })),
        };

        // const response = await updateBulkLeadsMutation({ payload });
        updateBulkLeadsMutation({ payload });
        // if (_get(response, 'data.message')) {
        //   notification.open({
        //     message: 'Success!',
        //     description: 'Changed leads status successfully!',
        //   });
        //   reloadJobLeads();
        // } else {
        //   notification.error({
        //     message: 'Error!',
        //     description: 'Changed leads status failed!',
        //   });
        // }
      } catch (e) {
        console.log('drag and drop issue: ', e);
        notification.error({
          message: 'Error!',
          description: 'Changed leads status failed!',
        });
      }
    }

    if (isCHILD) {
      try {
        let response = null;
        const payload = {
          newStatusId: _get(result, 'destination.droppableId'),
          updatedFor: userId,
        };
        const rawDraggableId = _get(result, 'draggableId');
        const draggableId = rawDraggableId.includes(LEAD_ID)
          ? rawDraggableId.replace(`${LEAD_ID}`, '')
          : rawDraggableId;
        if (draggableId.includes('company')) {
          payload.companyId = draggableId.split('/')[2];
          reorderCompanies({
            source,
            destination,
            companyId: payload.companyId,
          });
          // response = await changeStatusByCompany(payload);
          if (!searchedLeads || searchedLeads?.length === 0) {
            changeStatusByCompany(payload);
          } else {
            const leadsIdList = rawSearchedLeads
              .filter((lead) => lead?.company_id === payload.companyId)
              .map((lead) => lead?.id);

            leadsIdList.forEach((leadId) => {
              payload.jobLeadId = leadId;
              changeLogOfJobLeads(payload);
            });
          }
        } else if (draggableId) {
          payload.jobLeadId = draggableId.split('/')[1] || '';
          reorderLeads({
            source,
            destination,
            jobLeadId: draggableId,
          });
          // response = await changeLogOfJobLeads(payload);
          changeLogOfJobLeads(payload);
        }

        // if (_get(response, 'data.success')) {
        //   notification.open({
        //     message: 'Success!',
        //     description: 'Changed status successfully!',
        //   });
        //   reloadJobLeads();
        // } else {
        //   notification.error({
        //     message: 'Error!',
        //     description: 'Changed status failed!',
        //   });
        // }
      } catch (e) {
        console.log('drag and drop issue: ', e);
        // notification.error({
        //   message: 'Error!',
        //   description: 'There is something wrong, Please try again later!',
        // });
      }
    }
  };

  const onDragEnd = (result) => {
    setPlaceholderProps({});
    setDragAndDrop(false);

    if (!result.destination) return null;

    // setLeadLoading(true);
    // console.log('onDragEnd: ');
    handleChangeLogOfJobLeads(result);
  };

  const getDraggedDom = (draggableId) => {
    const domQuery = `[${queryAttr}='${draggableId}']`;
    const draggedDOM = document.querySelector(domQuery);

    return draggedDOM;
  };

  const handleDragStart = (event) => {
    const { draggableId, source } = event;

    setDragAndDrop(true);

    if (draggableId.includes(LEAD_ID)) return;

    const draggedDOM = getDraggedDom(draggableId);

    if (!draggedDOM) {
      return;
    }

    const { clientHeight, clientWidth } = draggedDOM;
    const sourceIndex = source.index;
    const clientY =
      parseFloat(window?.getComputedStyle(draggedDOM.parentNode).paddingTop) +
      [...draggedDOM.parentNode.children]
        .slice(0, sourceIndex)
        .reduce((total, curr) => {
          const style = curr?.currentStyle || window?.getComputedStyle(curr);
          const marginBottom = parseFloat(style.marginBottom);
          return total + curr.clientHeight + marginBottom;
        }, 0);

    setPlaceholderProps({
      clientHeight,
      clientWidth,
      clientY,
      clientX:
        parseFloat(
          window?.getComputedStyle(draggedDOM.parentNode).paddingLeft
        ) + 14,
    });
  };

  const handleDragUpdate = (event) => {
    const { draggableId, source, destination } = event;

    if (draggableId.includes(LEAD_ID)) return;

    if (!destination) {
      return;
    }

    const draggedDOM = getDraggedDom(draggableId);

    if (!draggedDOM) {
      return;
    }

    const { clientHeight, clientWidth } = draggedDOM;
    const destinationIndex = destination.index;
    const sourceIndex = source.index;

    const childrenArray = [...draggedDOM.parentNode.children];
    const movedItem = childrenArray[sourceIndex];
    childrenArray.splice(sourceIndex, 1);

    const updatedArray = [
      ...childrenArray.slice(0, destinationIndex),
      movedItem,
      ...childrenArray.slice(destinationIndex + 1),
    ];

    const clientY =
      parseFloat(window.getComputedStyle(draggedDOM.parentNode).paddingTop) +
      updatedArray.slice(0, destinationIndex).reduce((total, curr) => {
        if (curr) {
          const style =
            curr?.currentStyle || window?.getComputedStyle(curr || null);
          const marginBottom = parseFloat(style.marginBottom);
          return total + curr?.clientHeight + marginBottom;
        } else {
          return total;
        }
      }, 0);

    setPlaceholderProps({
      clientHeight,
      clientWidth,
      clientY,
      clientX:
        parseFloat(window.getComputedStyle(draggedDOM.parentNode).paddingLeft) +
        14,
    });
  };

  const handleFilterLeadsByCompanyName = () => {
    if (searchedLeads?.length > 0) {
      console.log(
        'handleFilterLeadsByCompanyName searchedLeads:',
        searchedLeads
      );
      const newJobLeadsWithStatuses = searchedLeads.map((status) => {
        if (status?.companies?.length === 0 || !status?.companies)
          return { ...status };
        const newCompanies = status.companies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
        return { ...status, companies: newCompanies };
      });
      // setJobLeadsGlobal([...newJobLeadsWithStatuses]);
      setJobLeads([...newJobLeadsWithStatuses]);
    } else {
      const newJobLeadsWithStatuses = rawJobLeadsWithStatuses.map((status) => {
        if (status?.companies?.length === 0 || !status?.companies)
          return { ...status };
        const newCompanies = status.companies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
        return { ...status, companies: newCompanies };
      });
      // setJobLeadsGlobal([...newJobLeadsWithStatuses]);
      setJobLeads([...newJobLeadsWithStatuses]);
    }
    const newSelectedRowKeys = rawSearchedLeads
      .filter(
        (lead) =>
          lead.company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase()) &&
          rawSelectedRowKeys.includes(lead?.id)
      )
      .map((lead) => lead.id);
    setSelectedRowKeys([...newSelectedRowKeys]);
    // console.log('searchText: ', searchText);
    // console.log('rawJobLeadsWithStatuses: ', rawJobLeadsWithStatuses);
  };

  // filter board

  const onSubmitBoardFilter = (filteringStatusId, query) => {
    console.log('onSubmitBoardFilter filteringStatusId: ', filteringStatusId);
    console.log('onSubmitBoardFilter query: ', query);
    console.log('onSubmitBoardFilter job: ', rawJobLeadsWithStatuses);
    console.log('onSubmitBoardFilter r: ', rawSearchedLeads);

    // haven't master refine and search by company

    // if (!searchText) {
    const newJobLeadsWithStatuses = jobLeads.map((status) => {
      if (status.id !== filteringStatusId) return { ...status };

      const leadsData =
        searchedLeads?.length > 0
          ? [...searchedLeads]
          : [...rawJobLeadsWithStatuses];

      const rawCompanies = leadsData.find(
        (status) => status.id === filteringStatusId
      ).companies;

      let newCompanies = rawCompanies
        .map((company) => {
          if (company?.leads?.length === 0 || !company?.leads || !query)
            return { ...company };
          const newLeads = company?.leads?.filter((lead) => {
            let score = 0;
            for (const [key, value] of Object.entries(query)) {
              if (
                lead[key]
                  ?.trim()
                  ?.toLowerCase()
                  ?.includes(value?.trim()?.toLowerCase())
              ) {
                score += 1;
              }
            }
            return score === Object.keys(query).length;
          });

          return { ...company, leads: newLeads };
        })
        .filter((company) => company?.leads?.length > 0);

      if (searchText) {
        newCompanies = newCompanies.filter((company) =>
          company?.jl_company_name
            ?.trim()
            .toLowerCase()
            .includes(searchText?.trim().toLowerCase())
        );
      }

      return { ...status, companies: newCompanies, filters: query };
    });

    setJobLeads([...newJobLeadsWithStatuses]);
    // }
  };

  useEffect(() => {
    handleFilterLeadsByCompanyName();
  }, [searchText]);

  useEffect(() => {
    const newSelectedRowKeys = jobLeads
      .flatMap((status) => status?.companies || [])
      .flatMap((company) => company?.leads || [])
      .map((lead) => lead?.id);

    const isDisableSearchByCompany = jobLeads.some(
      (status) => Object.keys(status?.filters || {}).length > 0
    );
    setDisableSearchByCompany(isDisableSearchByCompany);
    setSelectedRowKeys([...newSelectedRowKeys]);
  }, [JSON.stringify(jobLeads)]);

  const handleDeleteLead = (leadToDelete) => {
    const newJobLeads = jobLeads?.map((status) => {
      // if (!status?.companies?.length <= 0) return { ...status };
      const newCompanies = status?.companies
        ?.map((company) => {
          // if (!company?.leads?.length <= 0) return { ...company };
          const newLeads = company?.leads?.filter(
            (lead) => lead?.id !== leadToDelete?.id
          );
          return { ...company, leads: newLeads };
        })
        .filter((company) => company?.leads?.length > 0);

      return {
        ...status,
        companies: newCompanies,
      };
    });

    console.log('leadToDelete: ', leadToDelete);
    console.log('newJobLeads: ', newJobLeads);
    setJobLeads([...newJobLeads]);
  };

  return (
    <div className="leads-drag-drop-container h-full">
      {/* <Input
        placeholder="Search by company"
        className="max-w-xs mb-6"
        prefix={<SearchOutlined />}
        onChange={(e) => debouncedSetSearchText(e.target.value)}
      /> */}
      <DragDropContext
        className="h-full"
        onDragEnd={onDragEnd}
        onDragStart={handleDragStart}
        onDragUpdate={handleDragUpdate}
      >
        <Droppable
          droppableId="board"
          type="PARENT"
          direction="horizontal"
          ignoreContainerClipping={false}
          isCombineEnabled
        >
          {(provided) => (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              <div className="pl-6 h-full flex flex-row gap-7 overflow-hidden hover:overflow-auto">
                {leadLoading &&
                  _map(jobLeadsWithStatuses, (jobLeadsWithStatus, index) => (
                    <>
                      {_map(_range(1), (_, idx) => (
                        <React.Fragment key={idx.toString()}>
                          <Col span={6}>
                            <LoadingAdvanced isSkeleton />
                          </Col>
                        </React.Fragment>
                      ))}
                    </>
                  ))}
                {!leadLoading &&
                  _map(jobLeads, (jobLeadsWithStatus, index) => (
                    <>
                      {leadLoading ? (
                        <>
                          {_map(_range(4), (_, idx) => (
                            <React.Fragment key={idx.toString()}>
                              <Col span={6}>
                                <LoadingAdvanced isSkeleton />
                              </Col>
                            </React.Fragment>
                          ))}
                        </>
                      ) : (
                        <ColumnDragDrop
                          id={jobLeadsWithStatus?.id}
                          searchText={searchText}
                          key={index}
                          jobLeadsWithStatus={jobLeadsWithStatus}
                          index={index}
                          title={jobLeadsWithStatus.name}
                          isScrollable
                          isCombineEnabled
                          handleDeleteLead={handleDeleteLead}
                          reloadJobLeads={reloadJobLeads}
                          placeholderProps={placeholderProps}
                          isDragAndDrop={isDragAndDrop}
                          jobLeads={jobLeads}
                          setJobLeads={setJobLeads}
                          isSelectionMode={isSelectionMode}
                          onChangeCheckbox={onChangeCheckbox}
                          selectedRowKeys={selectedRowKeys}
                          onSubmitBoardFilter={onSubmitBoardFilter}
                        />
                        // </Col>
                      )}
                    </>
                  ))}
                {provided.placeholder}
              </div>
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};

export default LeadsDragDrop;
