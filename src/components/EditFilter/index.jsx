/* eslint-disable react/prop-types */
import { Select, DatePicker, Checkbox } from 'antd';
import { getJobs } from '../../services/jobs';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { citySelData } from './consts';

export default function EditFilterBar({
  editSearch,
  savedSearchData,
  setSavedSearchData,
  id,
  setSearchLoading,
}) {
  const [customKeywords, setCustomKeywords] = useState([]);
  const { RangePicker } = DatePicker;

  useEffect(() => {
    setCustomKeywords(JSON.parse(localStorage.getItem('keywords')));
  }, []);

  const onKeywordsChange = (value) => {
    if (
      value.length != 0 &&
      customKeywords.filter(
        (item) => item.value === value[value.length - 1].toLowerCase()
      ).length == 0
    ) {
      setCustomKeywords([
        ...customKeywords,
        {
          value: value[value.length - 1].toLowerCase(),
          label: value[value.length - 1],
        },
      ]);
      localStorage.setItem(
        'keywords',
        JSON.stringify([
          ...customKeywords,
          {
            value: value[value.length - 1].toLowerCase(),
            label: value[value.length - 1],
          },
        ])
      );
      setSavedSearchData(
        savedSearchData.map((item) =>
          item.id == id ? { ...item, keywords: value } : item
        )
      );
    } else {
      setSavedSearchData(
        savedSearchData.map((item) =>
          item.id == id ? { ...item, keywords: value } : item
        )
      );
    }
  };

  const onJobBoardChange = (e, id) => {
    if (
      editSearch[0].jobboards.filter((item) => item == e.target.value).length !=
      0
    )
      setSavedSearchData(
        savedSearchData.map((item) =>
          item.id == id
            ? {
                ...item,
                jobboards: item.jobboards.filter(
                  (item1) => item1 != e.target.value
                ),
              }
            : item
        )
      );
    else
      setSavedSearchData(
        savedSearchData.map((item) =>
          item.id == id
            ? {
                ...item,
                jobboards: [...item.jobboards, e.target.value],
              }
            : item
        )
      );
  };

  const onDateChange = (date, dateString) => {
    setSavedSearchData(
      savedSearchData.map((item) =>
        item.id == id ? { ...item, posted: dateString } : item
      )
    );
  };

  const onInputChange = (event) => {
    const { name, value } = event.target;
    setSavedSearchData(
      savedSearchData.map((item) =>
        item.id == id ? { ...item, [name]: value } : item
      )
    );
  };

  const onCityChange = (value) => {
    setSavedSearchData(
      savedSearchData.map((item) =>
        item.id == id ? { ...item, city: value } : item
      )
    );
  };

  const handleOk = () => {
    localStorage.setItem('savedSearch', JSON.stringify(savedSearchData));
    if (localStorage.getItem('savedSync') == null)
      localStorage.setItem('savedSync', JSON.stringify([]));
    const syncData = JSON.parse(localStorage.getItem('savedSync'));
    localStorage.setItem(
      'savedSync',
      JSON.stringify(
        syncData.map((item) =>
          item.id == id
            ? savedSearchData.filter((item1) => item1.id === id)[0]
            : item
        )
      )
    );
  };

  const onResearch = () => {
    setSearchLoading(true);
    const searchData = editSearch[0];
    getJobs({ searchData }).then((data) => {
      setSearchLoading(false);
      const jobData = data.data.jobs;
      setSavedSearchData(
        savedSearchData.map((item) =>
          item.id == id
            ? { ...item, data: jobData, totalCount: data.data.totalCount }
            : item
        )
      );
      localStorage.setItem(
        'savedSearch',
        JSON.stringify(
          savedSearchData.map((item) =>
            item.id == id
              ? { ...item, data: jobData, totalCount: data.data.totalCount }
              : item
          )
        )
      );
    });
  };

  if (editSearch.length == 0) return <></>;
  else
    return (
      <div className="outline outline-gray-100 outline-4 outline-offset-8 rounded-lg text-left">
        <div className="sm:col-span-3">
          <label
            htmlFor="jobTitle"
            className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
          >
            Job Title
          </label>
          <div className="mt-2">
            <input
              placeholder="Type Search Title"
              type="text"
              name="jobTitle"
              id="jobTitle"
              defaultValue={editSearch[0].jobTitle}
              onChange={onInputChange}
              autoComplete="given-name"
              value={editSearch[0].jobTitle}
              className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </div>
        </div>

        <div className="col-span-full mt-8">
          <label
            htmlFor="location"
            className="block text-sm font-medium leading-6 text-gray-90 dark:text-white"
          >
            Keyword
          </label>
          <div className="mt-2">
            <Select
              mode="tags"
              style={{
                width: '100%',
              }}
              placeholder="Select your keyword"
              onChange={onKeywordsChange}
              options={customKeywords}
              defaultValue={editSearch[0].keywords}
            />
          </div>
        </div>

        <div className="pt-8 w-full">
          <label
            htmlFor="city"
            className="block text-sm font-medium leading-6 text-gray-90"
          >
            City
          </label>
          <div className="mt-2">
            <Select
              mode="tags"
              style={{
                width: '100%',
              }}
              defaultValue={editSearch[0].city}
              placeholder="Select City"
              onChange={onCityChange}
              options={citySelData}
            />
          </div>
        </div>

        <div className="col-span-full mt-8 flex">
          <div className="sm:col-span-3 pt-8">
            <label
              htmlFor="minsalary"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              Min Salary
            </label>
            <div className="mt-2">
              <input
                type="text"
                name="minSalary"
                value={editSearch[0].minSalary}
                onChange={onInputChange}
                id="minsalary"
                autoComplete="given-name"
                className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>

          <div className="sm:col-span-3 pt-8 ps-3">
            <label
              htmlFor="maxsalery"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              Max Salary
            </label>
            <div className="mt-2">
              <input
                type="text"
                name="maxSalary"
                value={editSearch[0].maxSalary}
                onChange={onInputChange}
                id="maxsalery"
                autoComplete="family-name"
                className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>
        </div>

        <div className="w-full pt-8">
          <label
            htmlFor="maxsalery"
            className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
          >
            Date
          </label>
          <div className="mt-2">
            <div className="mt-3 flex justify-between">
              <RangePicker
                defaultValue={
                  editSearch[0].posted[0] != ''
                    ? [
                        dayjs(editSearch[0].posted[0], 'YYYY-MM'),
                        dayjs(editSearch[0].posted[1], 'YYYY-MM'),
                      ]
                    : [dayjs('2023-01', 'YYYY-MM'), dayjs('0000-00', 'YYYY-MM')]
                }
                picker="month"
                onChange={onDateChange}
              />
            </div>
          </div>
        </div>

        <div className="pt-10 col-span-2">
          <label
            htmlFor="jobTitle"
            className="block text-sm font-medium leading-6 text-gray-90"
          >
            Job Boards
          </label>
          <div className="">
            <div className="mt-[18px]">
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="technojobs"
                    value="technojobs"
                    checked={editSearch[0].jobboards.includes('technojobs')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="recuritment"
                    className="font-medium text-gray-90"
                  >
                    TechnoJobs
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="monster"
                    value="monster"
                    checked={editSearch[0].jobboards.includes('monster')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="directemployer"
                    className="font-medium text-gray-90"
                  >
                    Monster
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="cv-library"
                    value="cv-library"
                    checked={editSearch[0].jobboards.includes('cv-library')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    CV Library
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="reed"
                    value="reed"
                    checked={editSearch[0].jobboards.includes('reed')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    Reed
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="indeed"
                    value="indeed"
                    checked={editSearch[0].jobboards.includes('indeed')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    Indeed
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="linkedin"
                    value="linkedin"
                    checked={editSearch[0].jobboards.includes('linkedin')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    Linkedin
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="totaljobs"
                    value="totaljobs"
                    checked={editSearch[0].jobboards.includes('totaljobs')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    Totaljobs
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="jobsite"
                    value="jobsite"
                    checked={editSearch[0].jobboards.includes('jobsite')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    JobSite
                  </label>
                </div>
              </div>
              <div className="flex gap-x-1 mb-3">
                <div className="flex items-center">
                  <Checkbox
                    name="seek"
                    value="seek"
                    checked={editSearch[0].jobboards.includes('seek')}
                    onChange={(e) => onJobBoardChange(e, editSearch[0].id)}
                  ></Checkbox>
                </div>
                <div className="text-sm leading-6">
                  <label
                    htmlFor="graduate"
                    className="font-medium text-gray-90"
                  >
                    Seek
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex items-center justify-center gap-x-6">
          <button
            onClick={handleOk}
            className="rounded-md bg-[#e30075] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#e30075] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-[#e30075]"
          >
            Save Filter
          </button>
          <button
            onClick={onResearch}
            className="rounded-md bg-[#4058e2] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#4058e2] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:bg-[#4058e2]"
          >
            Research
          </button>
        </div>
      </div>
    );
}
