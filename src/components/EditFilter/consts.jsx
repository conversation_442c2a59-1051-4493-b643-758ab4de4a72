import cityData from './gb.json';

export const jobboards = [
  {
    id: 1,
    name: 'CV Library',
  },
  {
    id: 2,
    name: 'Indeed',
  },
  {
    id: 3,
    name: '<PERSON>',
  },
  {
    id: 4,
    name: '<PERSON><PERSON><PERSON> Jobs',
  },
  {
    id: 5,
    name: '<PERSON>',
  },
  {
    id: 6,
    name: 'CW Jobs',
  },
  {
    id: 7,
    name: 'Jobsite',
  },
  {
    id: 8,
    name: 'TechnoJobs',
  },
  {
    id: 9,
    name: 'Google Job Boards',
  },
  {
    id: 10,
    name: 'Total Jobs',
  },
];

export const initKeywords = [
  'HTML',
  'CSS',
  'JAVASCRIPT',
  'TYPESCRIPT',
  'PHP',
  'PYTHON',
  'REACT',
  'NODE',
  'VUE',
  'LARAVEL',
  'SHOPIFY',
];
export const citySelData = cityData.map((item) => ({
  value: item.city,
  label: item.city,
}));
