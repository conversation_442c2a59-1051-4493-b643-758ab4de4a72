import React from 'react';
import * as headings from './style';

const Heading = (props) => {
  const { as, children, className, id } = props;
  const StyledHeading = as ? headings[`${as.toUpperCase()}`] : headings.H1;
  // const StyledHeading = headings.H1;
  
  return (
    <StyledHeading className={className} id={id}>
      {children}
    </StyledHeading>
  );
};

Heading.defaultProps = {
  as: 'h1',
};

export default Heading;
