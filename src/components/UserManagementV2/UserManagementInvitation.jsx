/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import {
  AutoComplete,
  Button,
  Modal,
  Select,
  Tabs,
  notification,
  Input,
} from 'antd';
import { useForm } from 'react-hook-form';
import {
  inviteUser,
  getUserOrganizations,
  getUserRoles,
} from '../../services/users';
import { BullhornInvitationForm } from './BullhornInvitationForm';
import { RegularUserInvitationForm } from './RegularUserInvitationForm';
import TabPane from 'antd/es/tabs/TabPane';
import { useAuth } from '../../store/auth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getUserById } from '../../services/auth';
import { useViewAs } from '../../store/viewAs';
import { LeftOutlined, PlusOutlined } from '@ant-design/icons';
import { SUPER_ADMIN } from '../../constants/common.constant';

const { Search } = Input;

const useUserInvitation = (repopulateUsers) => {
  const queryClient = useQueryClient();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    getValues,
  } = useForm();

  const getDomain = (email) => {
    const regex = /@([^\.]+)\./;
    const match = email.match(regex);
    if (match) {
      const domain = match[1];
      return domain;
    } else {
      return null;
    }
  };

  const onSubmit = async (data) => {
    try {
      const profileToSet = profileUser || profileUserAuth;
      // Send API request to invite users with selected emails
      // console.log('Inviting users:', data.emails.map(a=>a.value));
      // console.log('Type User', data.type_user.key)
      const invitedUsers = [];
      data.emails.map((a) => {
        if (
          profileToSet?.user?.role?.keyCode !== SUPER_ADMIN &&
          getDomain(a?.value) !== getDomain(profileToSet?.email)
        ) {
          return notification.error({
            message: 'Errors',
            description: `Email: "${a?.value}" not correct`,
          });
        }
        invitedUsers.push({
          email: a.value,
          roleId: data?.type_user?.id,
          organizationId: !data?.organizationId ? '' : data?.organizationId,
          organizationName: data?.organization,
        });
      });
      await inviteUser(invitedUsers);
      reset();
      setIsModalVisible(false);
      repopulateUsers();
      if (!data?.organizationId)
        queryClient.invalidateQueries('LIST_ORGANIZATION');
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      notification.error({
        message: 'Error',
        description: errorMessage,
      });
    }
  };

  const openModal = () => {
    setIsModalVisible(true);
  };

  return {
    control,
    handleSubmit: handleSubmit(onSubmit),
    errors,
    isModalVisible,
    openModal,
    setValue,
    closeModal: () => {
      setIsModalVisible(false);
      repopulateUsers();
    },
    getValues,
  };
};

const UserManagementInvitation = ({
  rePopulateUsers,
  setSearchText,
  searchValue,
  setSearchValue,
  loading,
  selectedCompany = null,
  backToSelectCompany = null,
}) => {
  const {
    control,
    handleSubmit,
    isModalVisible,
    openModal,
    setValue,
    closeModal,
    getValues,
  } = useUserInvitation(rePopulateUsers);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const { data: listRoles } = useQuery(['LIST_ROLES'], {
    queryFn: async () => {
      const profileToSet = profileUser || profileUserAuth;
      // const viewNow = await getUserById(getV)
      const { data } = await getUserRoles();
      let filteredRoles;
      if (profileToSet?.user?.role?.keyCode === SUPER_ADMIN) {
        filteredRoles = data.result
          .filter((role) => role.keyCode === 'ADMIN')
          .map((role) => ({
            ...role,
            label: 'Admin',
          }));
      } else {
        filteredRoles = data.result
          .filter((role) => role.keyCode === 'BASIC_USER')
          .map((role) => ({
            ...role,
            label: 'Basic User',
          }));
      }
      return filteredRoles;
    },
  });

  const { data: listOrganization = [] } = useQuery(['LIST_ORGANIZATION'], {
    queryFn: async () => {
      const profileToSet = profileUser || profileUserAuth;
      if (profileToSet?.user?.role?.keyCode === SUPER_ADMIN) {
        const { data } = await getUserOrganizations();
        return data.result;
      }
      return [];
    },
  });

  const listOriginConfig = listOrganization.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });

  const handleSearch = (text) => {
    setSearchValue(text);
  };

  useEffect(() => {
    const clearOrg = () => {
      setValue('organization', '');
      setValue('organizationId', '');
      setSearchText('');
    };
    if (selectedCompany) {
      const orgId = selectedCompany?.id;
      const selectedCompanyName = listOriginConfig.find(
        (co) => co.value === orgId
      )?.label;
      setValue('organization', selectedCompanyName);
      setValue('organizationId', orgId);
      setSearchText(orgId);
    } else {
      clearOrg();
    }
    // return () => {
    //   clearOrg();
    // };
  }, [selectedCompany]);

  return (
    <div className="w-full grid grid-cols-10 justify-between gap-5 items-center">
      <div className="w-full col-span-8 flex items-center gap-4">
        {backToSelectCompany && (
          <Button
            onClick={backToSelectCompany}
            icon={<LeftOutlined />}
            className="bg-whie"
          >
            Back
          </Button>
        )}
        <Search
          className="customize-search-container"
          allowClear
          placeholder="Search..."
          enterButton="Search"
          size="middle"
          loading={loading}
          onSearch={handleSearch}
        />
      </div>

      {/* <div className="flex items-center gap-2"> */}
      {/* <Button
        className="Montserrat  flex items-center col-span-1"
        type="primary"
        onClick={openModal}
      >
        Invite User <PlusOutlined />
      </Button> */}
      <Select
        allowClear
        className="filter-by-org-container  Montserrat  flex items-center h-[2.45rem] col-span-2"
        // style={{
        //   width: "200px",
        //   marginLeft: "20px"
        // }}
        value={getValues('organizationId')}
        placeholder={'Filter By Organization'}
        options={listOriginConfig}
        onSearch={(searchText) => {
          setValue('organization', searchText);
          setValue('organizationId', null);
        }}
        onClear={() => {
          setValue('organization', '');
          setValue('organizationId', null);
          setSearchText();
        }}
        onSelect={(value) => {
          const consultantName = listOriginConfig.find(
            (co) => co.value === value
          )?.label;
          setSearchText(value);
          setValue('organization', consultantName);
          setValue('organizationId', value);
        }}
      ></Select>
      {/* </div> */}

      <Modal
        width={800}
        title={
          <div className="pb-2 border-b border-b-2">
            <span className="font-Montserrat">Invite User</span>
          </div>
        }
        open={isModalVisible}
        onCancel={closeModal}
        footer={
          <div className="flex justify-end gap-3">
            <Button
              className="Montserrat  flex items-center  col-span-1"
              key="cancel"
              onClick={closeModal}
            >
              Cancel
            </Button>
            <Button
              className="Montserrat  flex items-center  col-span-1"
              key="invite"
              type="primary"
              onClick={handleSubmit}
            >
              Send Invitation
            </Button>
          </div>
        }
      >
        <Tabs
          className="customized-invitation-tabs bg-[#f4f4f4] px-4 rounded-lg"
          defaultActiveKey="regularUser"
          onChange={() => {
            setValue('emails', []);
          }}
        >
          <TabPane tab="Regular User" key="regularUser">
            <RegularUserInvitationForm
              control={control}
              listRoles={listRoles}
              listOrganization={listOrganization}
              setValue={setValue}
            />
          </TabPane>
          <TabPane tab="Bullhorn User" key="bullhornUser">
            <BullhornInvitationForm
              control={control}
              listRoles={listRoles}
              listOrganization={listOrganization}
              setValue={setValue}
            />
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
};

export default UserManagementInvitation;
