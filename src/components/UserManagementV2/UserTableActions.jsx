/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { Button, Dropdown, Modal, notification } from 'antd';
import { deleteUser } from '../../services/users';
import { useViewAs } from '../../store/viewAs';
import { useNavigate } from 'react-router-dom';
import { getUserDetail } from '../../services/users';
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EllipsisOutlined,
} from '@ant-design/icons';
import { getUserViewAsPermissions } from '../../helpers/getUserViewAs';
import { FEATURES } from '../../constants/common.constant';

const UserTableActions = ({ user, reloadUserList, setLoading, loading }) => {
  const { setViewAs } = useViewAs();
  const navigate = useNavigate();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleOk = async () => {
    try {
      setConfirmLoading(true);
      const { data } = await deleteUser(user.id);
      setConfirmLoading(false);
      setDeleteModalOpen(false);
      notification.success({
        message: 'Success',
        description: 'User deleted successfully.',
      });
      reloadUserList();
    } catch (error) {
      setConfirmLoading(false);
      setDeleteModalOpen(false);
      console.error('Error deleting user:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to delete user. Please try again later.',
      });
    }
  };

  const currentPermissions = getUserViewAsPermissions();
  const isAllowViewAs = currentPermissions.includes(FEATURES.VIEW_AS);

  const handleCancel = () => {
    setDeleteModalOpen(false);
  };

  const items = [
    // {
    //   key: 'update',
    //   label: (
    //     <a
    //       className="w-full Montserrat"
    //       onClick={() => navigate(`/user-management/${user.id}/edit`)}
    //     >
    //       <EditOutlined /> Update
    //     </a>
    //   ),
    // },
    {
      key: 'delete',
      label: (
        <a
          className="w-full Montserrat"
          onClick={() => setDeleteModalOpen(true)}
        >
          <DeleteOutlined /> Delete
        </a>
      ),
    },
    ...(isAllowViewAs
      ? [
          {
            key: 'view-as',
            label: (
              <a
                className="w-full Montserrat"
                onClick={async () => {
                  setLoading(true);
                  const { data } = await getUserDetail(user.id);
                  setViewAs({ profileUser: data });
                  setLoading(false);
                  navigate('/dashboard');
                  window.location.reload(); // reload page to get new instance data
                }}
              >
                <EyeOutlined />
                {` View as ${user?.username || user?.email}`}
              </a>
            ),
          },
        ]
      : []),
  ];

  return (
    <>
      <Modal
        centered
        width={1000}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Delete user"
        open={deleteModalOpen}
        okText={'Confirm'}
        okButtonProps={{
          loading: confirmLoading,
        }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        Are you sure to delete this user?
      </Modal>
      <div className="flex flex-col md:flex-row justify-center">
        <Dropdown
          trigger={'click'}
          menu={{
            items,
          }}
          placement="bottom"
          arrow
        >
          <Button
            onClick={(e) => e.stopPropagation()}
            type="text"
            icon={<EllipsisOutlined />}
          ></Button>
        </Dropdown>

        {/* <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => {
            navigate(`/user-management/${user.id}/edit`);
          }}
        />
        <Button
          danger
          type="link"
          icon={<DeleteOutlined />}
          onClick={() => {
            setDeleteModalOpen(true);
          }}
        />
        <Button
          type="link"
          disabled={loading}
          icon={<EyeOutlined />}
          className="text-green-500"
          onClick={async () => {
            setLoading(true);
            const { data } = await getUserDetail(user.id);
            setViewAs({ profileUser: data });
            setLoading(false);
            navigate('/dashboard');
          }}
        /> */}
      </div>
    </>
  );
};

export default UserTableActions;
