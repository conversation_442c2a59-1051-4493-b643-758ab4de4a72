/* eslint-disable react/prop-types */
import { Form, Select, AutoComplete } from 'antd';
import { Controller } from 'react-hook-form';
import React, { useState } from 'react';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { licenseType, userRole } from '../../constants/common.constant';

export function RegularUserInvitationForm({
  control,
  listRoles,
  listOrganization,
  setValue,
}) {
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const [searchText, setSearchText] = useState('');
  const profileToSet = profileUser || profileUserAuth;

  return (
    <Form layout="vertical">
      <div className="grid grid-cols-10 gap-3">
        <Form.Item
          className="col-span-7"
          // label="Invite these emails to Zileo"
          name="emails"
          rules={[{ required: true }]}
        >
          <Controller
            name="emails"
            control={control}
            render={({ field }) => (
              <Select
                placeholder="Invite more workspace collaborators via email"
                labelInValue
                filterOption={false}
                mode="multiple"
                onSearch={(inputValue) => {
                  setSearchText(inputValue);
                }}
                {...field}
                options={[
                  { key: searchText, label: searchText, value: searchText },
                ]}
              />
            )}
          />
        </Form.Item>
        <Form.Item
          // label="Type User"
          className="col-span-3"
          name="type_user"
          rules={[{ required: true }]}
        >
          <Controller
            name="type_user"
            control={control}
            defaultValue={listRoles[0]}
            render={({ field }) => (
              <Select labelInValue disabled filterOption={false} {...field} />
            )}
          />
        </Form.Item>
      </div>

      <div className="grid grid-cols-10 gap-3">
        <Form.Item
          className="col-span-7"
          // label="Organization"
          name="organization"
          rules={[{ required: true }]}
        >
          <Controller
            name="organization"
            control={control}
            render={({ field }) => (
              <AutoComplete
                {...field}
                className="search-input custom-filter"
                options={listOrganization.map((option) => ({
                  value: option?.name,
                  label: option?.name,
                  id: option?.id,
                }))}
                disabled={profileToSet?.role?.keyCode === userRole.ADMIN}
                defaultValue={[
                  {
                    value: profileToSet?.organization?.name,
                    label: profileToSet?.organization?.name,
                    id: profileToSet?.organization?.id,
                  },
                ]}
                placeholder="Select organization"
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
                onSelect={(value, e) => {
                  setValue('organizationId', e.id);
                }}
              />
            )}
          />
        </Form.Item>

        <Form.Item
          className="col-span-3"
          name="licenseType"
          rules={[{ required: true }]}
        >
          <Controller
            name="licenseType"
            control={control}
            defaultValue={licenseType.CONNECTED}
            render={({ field }) => (
              <Select
                labelInValue
                filterOption={false}
                {...field}
                options={Object.values(licenseType).map((item) => ({
                  label: item,
                  value: item,
                }))}
              />
            )}
          />
        </Form.Item>
      </div>
    </Form>
  );
}
