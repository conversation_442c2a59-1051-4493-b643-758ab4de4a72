/* eslint-disable react/prop-types */
import React from 'react';
import { Form, Select, Spin, AutoComplete } from 'antd';
import { Controller } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import { licenseType } from '../../constants/common.constant';

export function BullhornInvitationForm({
  control,
  listRoles,
  listOrganization,
  setValue,
}) {
  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    isLoading: isLoadingConsultants,
    searchText,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const consultants = consultantOptions
    .filter((so) => so.email)
    .map((so) => ({
      ...so,
      label: so.name + ` (${so.email})`,
      value: so.email,
      key: so.id,
    }));
  const uniqueConsultants = Array.from(
    new Set(consultants.map((item) => item.id))
  ).map((id) => {
    return consultants.find((item) => item.id === id);
  });
  return (
    <Form layout="vertical">
      {/* Job Title */}
      <div className="grid grid-cols-10 gap-3">
        <Form.Item
          className="col-span-7"
          name="emails"
          rules={[{ required: true }]}
        >
          <Controller
            name="emails"
            control={control}
            render={({ field }) => (
              <Select
                placeholder="Invite more workspace collaborators via email"
                labelInValue
                filterOption={false}
                mode="multiple"
                onSearch={(searchText) => {
                  handleConsultantSearch(searchText);
                }}
                {...field}
                onPopupScroll={handleConsultantScroll}
                notFoundContent={
                  isLoadingConsultants ? <Spin size="small" /> : null
                }
                options={[
                  { key: searchText, label: searchText, value: searchText },
                  ...uniqueConsultants,
                ]}
              />
            )}
          />
        </Form.Item>
        <Form.Item
          className="col-span-3"
          // label="Type User"
          name="type_user"
          rules={[{ required: true }]}
        >
          <Controller
            name="type_user"
            control={control}
            defaultValue={listRoles[0]}
            render={({ field }) => (
              <Select labelInValue disabled filterOption={false} {...field} />
            )}
          />
        </Form.Item>
      </div>

      <div className="grid grid-cols-10 gap-3">
        <Form.Item
          className="col-span-7"
          // label="Organization"
          name="organization"
          rules={[{ required: true }]}
        >
          <Controller
            name="organization"
            control={control}
            render={({ field }) => (
              <AutoComplete
                {...field}
                className="search-input custom-filter"
                options={listOrganization.map((option) => ({
                  value: option?.name,
                  label: option?.name,
                  id: option?.id,
                }))}
                placeholder="Select organization"
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
                onSelect={(value, e) => {
                  setValue('organizationId', e.id);
                }}
              />
            )}
          />
        </Form.Item>

        <Form.Item
          className="col-span-3"
          name="licenseType"
          rules={[{ required: true }]}
        >
          <Controller
            name="licenseType"
            control={control}
            defaultValue={licenseType.STANDARD}
            render={({ field }) => (
              <Select
                labelInValue
                filterOption={false}
                {...field}
                options={Object.values(licenseType).map((item) => ({
                  label: item,
                  value: item,
                }))}
              />
            )}
          />
        </Form.Item>
      </div>
    </Form>
  );
}
