/* eslint-disable react/prop-types */
import {
  AutoComplete,
  Button,
  Form,
  Input,
  Modal,
  Spin,
  notification,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import {
  CheckOutlined,
  CloseCircleOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import { getUserDetail, updateConsultant } from '../../services/users';
import { getUserViewAs, isViewAs } from '../../helpers/getUserViewAs';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';

export function ButtonSendUserModal(props) {
  const {
    consultantOptions,
    handleConsultantScroll,
    isLoadingConsultants,
    handleConsultantSearch,
    record,
  } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { control, handleSubmit, setValue, getValues } = useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { setAuth, profile: profileUserAuth } = useAuth();
  const { profileUser, setViewAs } = useViewAs();

  const userToSet = profileUser || profileUserAuth;

  const showModal = async () => {
    setIsModalOpen(true);
  };
  const handleOk = async () => {
    setIsLoading(true);
    if (!getValues('currentConsultantId')) {
      notification.error({ message: 'Please chose Consultant' });
      setIsLoading(false);
    }
    const dataUpdate = {
      consultant_id: getValues('currentConsultantId').toString(),
      consultant_name: getValues('currentConsultantName'),
    };
    const { data } = await updateConsultant(dataUpdate, record.id);

    setIsLoading(false);
    if (data) {
      if (record.id === userToSet?.id) {
        const { data } = await getUserDetail(getUserViewAs());
        isViewAs()
          ? setViewAs({ profileUser: data })
          : setAuth({ profile: data });
      }
      notification.success({ message: 'Update Consultant Success' });
      setIsLoading(false);
      setIsModalOpen(false);
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // const {
  //   options: consultantOptions,
  //   handlePopupScroll: handleConsultantScroll,
  //   handleSearch: handleConsultantSearch,
  //   isLoading: isLoadingConsultants,
  // } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  return (
    <>
      <Button
        title="Link to bullhorn"
        type="text"
        icon={<LinkOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          showModal();
        }}
      ></Button>
      <Modal
        title="Link"
        open={isModalOpen}
        footer={false}
        onCancel={handleCancel}
      >
        <Form>
          <Form.Item
            label={
              <p>
                <span className="text-red-600">*</span> Consultant
              </p>
            }
            name="consultant"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  options={consultantOptions.map((option) => ({
                    value: option.id,
                    label: option.name,
                  }))}
                  onPopupScroll={handleConsultantScroll}
                  onSearch={(searchText) => {
                    setValue('consultant', searchText);
                    handleConsultantSearch(searchText);
                  }}
                  filterOption={(input, option) =>
                    option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onSelect={(value) => {
                    const consultantName = consultantOptions.find(
                      (co) => co.id == value
                    )?.name;
                    setValue('consultant', consultantName);
                    setValue('currentConsultantId', value);
                    setValue('currentConsultantName', consultantName);
                  }}
                ></AutoComplete>
              )}
              name="consultant"
              control={control}
            />
          </Form.Item>
        </Form>
        <Button
          loading={isLoading}
          disabled={isLoading}
          onClick={() => handleOk()}
          type="primary"
        >
          Save
        </Button>
      </Modal>
    </>
  );
}
