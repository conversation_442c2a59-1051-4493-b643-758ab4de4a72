/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { Switch, Form, Button, Col, Row } from 'antd';

const UserEditForm = ({ initialValues, onSubmit }) => {
  const { handleSubmit, control, reset } = useForm();
  useEffect(() => {
    reset({ permissions: initialValues });
  }, [initialValues]);

  const { fields } = useFieldArray({
    control,
    name: 'permissions',
  });

  const onSubmitHandler = (data) => {
    onSubmit(data.permissions);
  };
  return (
    <Form onFinish={handleSubmit(onSubmitHandler)}>
      {fields.map((field, index) => (
        <Form.Item key={field.id} label="">
          <b>{field.permission.name}</b>
          <Row gutter={[16, 16]}>
            <Col>
              <Controller
                name={`permissions[${index}].allowRead`}
                control={control}
                defaultValue={initialValues[index].allowRead}
                render={({ field }) => (
                  <>
                    Read{' '}
                    <Switch
                      {...field}
                      defaultChecked={initialValues[index].allowRead}
                      checkedChildren="Allow"
                      unCheckedChildren="Deny"
                    />
                  </>
                )}
              />
            </Col>
            <Col>
              <Controller
                name={`permissions[${index}].allowWrite`}
                control={control}
                defaultValue={initialValues[index].allowWrite}
                render={({ field }) => (
                  <>
                    Write{' '}
                    <Switch
                      {...field}
                      defaultChecked={initialValues[index].allowWrite}
                      title={'Allow Write'}
                      checkedChildren="Allow"
                      unCheckedChildren="Deny"
                    />
                  </>
                )}
              />
            </Col>
          </Row>

          <input
            type="hidden"
            {...field}
            name={`permissions[${index}].permissionId`}
          />
        </Form.Item>
      ))}
      <Form.Item>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UserEditForm;
