/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { Avatar, Button, Modal, Table, Tag } from 'antd';
import UserTableActions from './UserTableActions';
import { getUserDetail, updatePermissions } from '../../services/users';
import UserEditForm from './UserEditForm';
import { ButtonSendUserModal } from './ButtonSendUserModal';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import { BankOutlined, UserOutlined } from '@ant-design/icons';
import handleRenderTime from '../../function/handleRenderTime';
import { useNavigate } from 'react-router-dom';
import { USER_STATUS_COLOR } from '../../containers/CompanyManagement/ManageEmployees';
// import { getLinkS3 } from '../../services/aws';

export const getRoleName = (roleName) => {
  switch (roleName) {
    case 'Super Admin':
      return (
        <span class="px-2 py-1 border-[#9ce9cf] bg-[#e5faf4] text-[#006769] font-semibold">
          {roleName}
        </span>
      );
    case 'Admin':
      return (
        <span class="px-2 py-1 border-[#9ce9cf] bg-[#e5faf4] text-[#40A578] font-semibold">
          {roleName}
        </span>
      );
    case 'Basic User':
      return (
        <span class="px-2 py-1 border-[#9ce9cf] bg-[#e5faf4] text-[#9DDE8B] font-semibold">
          {roleName}
        </span>
      );
    case 'Sales':
      return (
        <span class="px-2 py-1 border-[#9ce9cf] bg-[#e5faf4] text-[#193f0f] font-semibold">
          {roleName}
        </span>
      );
    case 'Management':
      return (
        <span class="px-2 py-1 border-[#9ce9cf] bg-[#e5faf4] text-[#42a526] font-semibold">
          {roleName}
        </span>
      );
    default:
      return (
        <span class="px-2 py-1 border-[#EE4E4E] bg-white text-[#EE4E4E] font-semibold">
          Unassigned
        </span>
      );
  }
};

const UserManagementTable = ({
  users,
  reloadUserList,
  loadingTable,
  setSelectedUser,
}) => {
  const navigate = useNavigate();

  const [selectedUserId, setSelectedUserId] = useState(null);
  const [userDataById, setUserDataById] = useState(null);
  const [loading, setLoading] = useState(false);

  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    isLoading: isLoadingConsultants,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  useEffect(() => {
    const populate = async () => {
      const { data } = await getUserDetail(selectedUserId);
      setUserDataById(data);
    };
    if (selectedUserId) {
      populate();
    }
  }, [selectedUserId]);
  const handleEditCancel = () => {
    setSelectedUserId(null);
  };

  const handleSubmit = async (permissions) => {
    await updatePermissions({
      userId: selectedUserId,
      permissionsPayload: { permissions },
    });
    setSelectedUserId(null);
  };
  const columns = [
    {
      title: 'Name',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      align: 'left',
      render: (text, record) => {
        return (
          <div className="flex items-center gap-2">
            {record?.avatarUrl && <Avatar size={50} src={record?.avatarUrl} />}
            {!record?.avatarUrl && <Avatar size={50} icon={<UserOutlined />} />}
            <div className="flex flex-col">
              <span className="text-base font-semibold">
                {record?.fullName || '-'}
              </span>
              <span className="text-sm italic">{record?.email || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      width: '10%',
      align: 'center',
      render: (text, record) => getRoleName(record?.role?.name),
    },
    {
      title: 'Company',
      dataIndex: 'organization',
      key: 'organization',
      width: '20%',
      align: 'left',
      render: (text, record) => (
        <span className="font-semibold text-cyan-600">
          <BankOutlined />{' '}
          {record?.organization ? record?.organization?.name : '-'}
        </span>
      ),
    },
    {
      title: 'Linked Bullhorn User',
      dataIndex: 'linkToBullHorn',
      key: 'linkToBullHorn',
      width: '20%',
      align: 'left',
      render: (text, record) => (
        <>
          <span>{record?.consultantName ? record?.consultantName : '-'}</span>
        </>
      ),
    },
    {
      title: 'Last Active',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      width: '10%',
      align: 'left',
      render: (text, record) => (
        <>
          <span>{handleRenderTime(text)}</span>
        </>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      align: 'center',
      render: (status, record) => (
        <div className="w-full flex items-center justify-center">
          <Tag
            className="px-2 py-1 font-semibold"
            color={USER_STATUS_COLOR[status]}
          >
            {status?.toLowerCase()?.capitalize()}
          </Tag>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      width: '10%',
      render: (text, record) => (
        <div className="flex gap-1" onClick={(e) => e.stopPropagation()}>
          <ButtonSendUserModal
            record={record}
            consultantOptions={consultantOptions}
            handleConsultantScroll={handleConsultantScroll}
            isLoadingConsultants={isLoadingConsultants}
            handleConsultantSearch={handleConsultantSearch}
          />
          <UserTableActions
            setLoading={setLoading}
            loading={loading}
            user={record}
            reloadUserList={reloadUserList}
            setSelectedUserId={setSelectedUserId}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="mt-4 search-table-new-design-container">
      <Modal
        centered
        width={300}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Edit user"
        open={selectedUserId}
        okButtonProps={{
          hidden: true,
        }}
        cancelButtonProps={{
          hidden: true,
        }}
        onCancel={handleEditCancel}
      >
        <UserEditForm
          initialValues={userDataById?.userPermissions || []}
          onSubmit={handleSubmit}
        />
      </Modal>
      <Table
        dataSource={users}
        columns={columns}
        loading={loadingTable}
        onRow={(record, rowIndex) => {
          return {
            onClick: () => {
              setSelectedUser(record);
              // navigate(`/user-management/${record.id}/edit`);
            },
            style: { cursor: 'pointer' },
          };
        }}
      />
    </div>
  );
};

export default UserManagementTable;
