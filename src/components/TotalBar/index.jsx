/* eslint-disable react/prop-types */
import React from 'react';

function TotalBar({ verifiedData, count }) {
  return (
    <div className="px-3 grid grid-cols-7 mt-8 xl:me-[83px]">
      <div className="xl:col-span-4 col-span-7">
        <div className="text-zinc-500 text-[16px] font-normal mb-2 xl:ms-1 ms-4 font-[PoppinsRegular]"></div>
        <div className="sm:flex justify-around">
          <div className="w-[95%] h-[85px] w-min-[140px] ms-4 sm:ms-0 mb-5">
            <div
              className={
                'w-[95%] h-[85px]  bg-[#cc37a2] rounded-xl shadow pt-[11px] ps-[18px]'
              }
            >
              <div className="h-8 text-white text-[20px] font-normal font-[PoppinsRegular]">
                Total Emails
              </div>
              <div className="h-[37px] text-white text-[24px] font-[PoppinsBold]">
                {verifiedData.length == 0 ? count : verifiedData.length}
              </div>
            </div>
          </div>
          <div className="w-[95%] h-[85px] w-min-[140px] ms-4 sm:ms-0 mb-5">
            <div
              className={
                'w-[95%] h-[85px]  bg-[#508bff] rounded-xl shadow pt-[11px] ps-[18px]'
              }
            >
              <div className="h-8 text-white text-[20px] font-normal font-[PoppinsRegular]">
                Verified
              </div>
              <div className="h-[37px] text-white text-[24px] font-[PoppinsBold]">
                {
                  verifiedData.filter((item) => item.is_verified == 'True')
                    .length
                }
              </div>
            </div>
          </div>
          <div className="w-[95%] h-[85px] w-min-[140px] ms-4 sm:ms-0 mb-5">
            <div
              className={
                'w-[95%] h-[85px]  bg-[#495377] rounded-xl shadow pt-[11px] ps-[18px]'
              }
            >
              <div className="h-8 text-white text-[20px] font-normal font-[PoppinsRegular]">
                UnVerified
              </div>
              <div className="h-[37px] text-white text-[24px] font-[PoppinsBold]">
                {
                  verifiedData.filter((item) => item.is_verified != 'True')
                    .length
                }
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="xl:col-span-3 col-span-7">
        <div className="text-zinc-500 text-[16px] font-normal mb-2 xl:ms-[110px] ms-4 font-[PoppinsRegular]"></div>
        <div className="sm:flex justify-between">
          <div className="xl:w-[40%] w-[0] flex ms-4"></div>
          <div className="w-[95%] h-[85px] w-min-[140px] ms-4 sm:ms-0 mb-5">
            <div
              className={
                'w-[95%] h-[85px]  bg-amber-300 rounded-xl shadow pt-[11px] ps-[18px]'
              }
            >
              <div className="h-8 text-white text-[20px] font-normal font-[PoppinsRegular]">
                Syntax Error
              </div>
              <div className="h-[37px] text-white text-[24px] font-[PoppinsBold]">
                {verifiedData.filter((item) => item.is_syntax == 'True').length}
              </div>
            </div>
          </div>
          <div className="w-[95%] h-[85px] w-min-[140px] ms-4 sm:ms-0 mb-5">
            <div
              className={
                'w-[95%] h-[85px]  bg-indigo-500 rounded-xl shadow pt-[11px] ps-[18px]'
              }
            >
              <div className="h-8 text-white text-[20px] font-normal font-[PoppinsRegular]">
                High Risk
              </div>
              <div className="h-[37px] text-white text-[24px] font-[PoppinsBold]">
                {
                  verifiedData.filter((item) => item.is_high_risk == 'True')
                    .length
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TotalBar;
