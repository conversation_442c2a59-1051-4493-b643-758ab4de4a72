import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  CheckOutlined,
  DownOutlined,
  ClockCircleOutlined,
  MailOutlined,
  AppstoreAddOutlined,
  ForkOutlined,
  CloseCircleOutlined,
  OrderedListOutlined,
  BranchesOutlined,
  CopyOutlined,
  PhoneOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  LinkedinOutlined,
  UsergroupAddOutlined,
  UsergroupDeleteOutlined,
  RightSquareFilled,
  MailFilled,
  UserAddOutlined,
  FileExcelOutlined,
  FireOutlined,
  UploadOutlined,
  InboxOutlined,
  MoreOutlined,
  EllipsisOutlined,
  ClockCircleFilled,
  BookFilled,
  ScheduleFilled,
  DashboardFilled,
  MessageOutlined,
  CommentOutlined,
  SyncOutlined,
  ExclamationCircleFilled,
  HomeOutlined,
  SwapRightOutlined,
  LeftOutlined,
  RightOutlined,
  SwapLeftOutlined,
  RightSquareOutlined,
  CloseOutlined,
  MessageFilled,
  LikeFilled,
  NotificationFilled,
  ContactsFilled,
  EyeOutlined,
  SnippetsOutlined,
  AuditOutlined,
  BlockOutlined,
  StarOutlined,
  HourglassOutlined,
  ReconciliationOutlined,
  NodeIndexOutlined,
  LoadingOutlined,
  FileDoneOutlined,
  PushpinOutlined,
} from '@ant-design/icons';
import {
  Button,
  Form,
  notification,
  Image,
  Select,
  Popconfirm,
  Drawer,
  Input,
  Spin,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  Dropdown,
  Popover,
  Badge,
  Collapse,
  Upload,
  Checkbox,
  Alert,
} from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Controller } from 'react-hook-form';
import EmailEditContent from './EmailEditContent';
import {
  configEmail,
  getConfigEmail,
  getEmailConfigInJobBoard,
  saveDraftSequence,
  sendTestEmail,
  triggerEmail,
} from '../../services/jobs';
import _, { isEmpty, isNil } from 'lodash';

import ChronometerIcon from '../../assets/img/icons/chronometer.png';
import DoneIcon from '../../assets/img/icons/done.png';

import ModalCreateFromTemplate from '../../containers/Sequence/ModalCreateFromTemplate';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import BullhornSendEmail from './BullhornSendEmailModal';
import {
  checkHotListEmail,
  createNewEmailValid,
  getNewEmailValid,
  getShortlistById,
  searchBullhorn,
  searchBullhornData,
  searchCommonBulhorn,
} from '../../services/bullhorn';
import { v4 as uuid } from 'uuid';
import AddNoteJodit from './AddNoteJodit';

import { getSignatureDefault } from '../../services/signatures';
import ModalWarningSequence from '../JobListV2/ModalWarningSequence';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import LinkedInRequest from './LinkedinStep';
import {
  ADMIN_MAILS,
  arrayUniqueByKey,
  getLength,
  getOffSet,
  isValidJSON,
  replaceSignaturePlaceholder,
} from '../../utils/common';
import { getDataUniplieAccount } from '../../services/auth';
import {
  getUserViewAsLicenseType,
  isGrantedSequence,
  isViewAs,
} from '../../helpers/getUserViewAs';
import {
  createSequenceTemplate,
  updateSingleSequenceTemplate,
} from '../../services/sequenceTemplate';
import SequenceScheduleDate from './SequenceScheduleDate';
import dayjs from 'dayjs';
import moment from 'moment';
import ModalEditNameSequence from './ModalEditNameSequence';
import clsx from 'clsx';
import { PREVIEW_MODE } from '../../containers/SequenceTemplate/PreviewTemplate';
import ContactListModal from '../../containers/Sequence/ContactListModal';
import HotListModal from '../../containers/Sequence/HotListModal';
import Dragger from 'antd/es/upload/Dragger';
import * as XLSX from 'xlsx';
import {
  getDetailContactList,
  getExampleFile,
} from '../../services/contactList';
import {
  getContactLists,
  getHotLists,
  getShortLists,
} from '../../containers/Sequence/SequenceDetail';
import AddLinkedinTypeButton from './AddLinkedinType';
import {
  VALIDATE_STATUS_COLOR,
  VALIDATE_STATUS_ICON,
} from '../../containers/HotList/HotListTable';
import BulkEnrichData from '../../containers/HotList/BulkEnrich';
import { validListEmail } from '../../services/emailFinder';
import {
  MERTAGS_DEFINITION,
  IGNORE_MERGE_TAGS,
  licenseType,
} from '../../constants/common.constant';
import { getInvalidMessage } from '../../helpers/util';
import Loading from '../../containers/HotList/Loading';
import ChildSequence from './ChildSequence';
import ShortListModal from '../../containers/Sequence/ShortListModal';
import AddTaskStep from './AddTaskStep';
import ValidatingEmailProcess, {
  CLOSE_TYPES,
  VALIDATE_TYPES,
} from '../../containers/Sequence/ValidatingEmailProcess';

export const splitArray = (array, chunkSize) => {
  const result = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    result.push(array.slice(i, i + chunkSize));
  }
  return result;
};

export const VALID_EMAIL_STATUS = ['Valid', 'Risky'];
export const INVALID_EMAIL_STATUS = ['Invalid', 'Undefined'];

export const MAIL_STATUS_COLOR = {
  SENT: 'green',
  PENDING: '#FFFF99',
  ERROR: 'red',
  STOP: 'red',
  REPLIED: '#FFFF99',
  OFF: 'red',
};

export const STEP_STATUS_CLASS = {
  SENT: 'ribbon-sent',
  PENDING: 'ribbon-pending',
  ERROR: 'ribbon-error',
  STOPPED: 'ribbon-stopped',
  NOT_SENT: 'ribbon-notsent',
};

export const STEP_STATUS_TEXT = {
  SENT: 'Sent',
  PENDING: 'Pending',
  ERROR: 'Error',
  STOPPED: 'Stopped',
  NOT_SENT: 'Not sent',
};

export const STEP_STATUS_LINE = {
  SENT: 'border-[#00996d]',
  PENDING: 'border-[#FCDE70]',
  ERROR: 'border-[#F8463F]',
  STOPPED: 'border-[#C7253E]',
  NOT_SENT: 'border-[#F8463F]',
};

export const STEP_STATUS_BG = {
  SENT: 'bg-[#00996d]',
  PENDING: 'bg-[#FCDE70]',
  ERROR: 'bg-[#F8463F]',
  STOPPED: 'border-[#C7253E]',
  NOT_SENT: 'bg-[#F8463F]',
};

export const NOTE_ACTION = [
  { label: 'BD Call', value: 'BD Call' },
  { label: 'Dial', value: 'Dial' },
  { label: 'Email', value: 'Email' },
  { label: 'General', value: 'General' },
  { label: 'Interview Notes', value: 'Interview Notes' },
  { label: 'Job Response', value: 'Job Response' },
  { label: 'Left Voicemail', value: 'Left Voicemail' },
  { label: 'Prescreen', value: 'Prescreen' },
];

export const TASK_ACTION = [
  {
    label: 'Call',
    value: 'Call',
  },
  {
    label: 'Email',
    value: 'Email',
  },
  {
    label: 'LinkedIn',
    value: 'LinkedIn',
  },
  {
    label: 'Message',
    value: 'Message',
  },
  {
    label: 'Research',
    value: 'Research',
  },
  {
    label: 'Qualify',
    value: 'Qualify',
  },
  {
    label: 'Prospect',
    value: 'Prospect',
  },
  {
    label: 'Other',
    value: 'Other',
  },
];

export const TASK_ACTION_STATUS = {
  PENDING: 'Pending',
  COMPLETED: 'Completed',
  DECLINED: 'Declined',
};

export const TASK_ACTION_STATUS_ICON = {
  PENDING: <HourglassOutlined className="text-base font-medium" />,
  COMPLETED: <CheckOutlined className="text-base font-medium" />,
  DECLINED: <CloseOutlined className="text-base font-medium" />,
};

export const TASK_ACTION_STATUS_COLOR = {
  PENDING: 'warning',
  COMPLETED: 'success',
  DECLINED: 'error',
};

export const TASK_ACTION_ICONS = {
  Call: <PhoneOutlined className="text-base font-medium" />,
  Email: <MailOutlined className="text-base font-medium" />,
  LinkedIn: <LinkedinOutlined className="text-base font-medium" />,
  Message: <MessageOutlined className="text-base font-medium" />,
  Research: <SnippetsOutlined className="text-base font-medium" />,
  Qualify: <AuditOutlined className="text-base font-medium" />,
  Prospect: <BlockOutlined className="text-base font-medium" />,
  Other: <StarOutlined className="text-base font-medium" />,
};

export const ADD_STEP_TYPE = {
  ADD_WAIT: 'ADD_WAIT',
  TASK: 'TASK',
  SEND_MAIL: 'EMAIL',
  ADD_NOTE: 'NOTE',
  DUPLICATE_MAIL: 'DUPLICATE_MAIL',
  LINKEDIN_CONNECTION_REQUEST: 'LINKEDIN_CONNECTION_REQUEST',
};

export const ADD_STEP_TYPE_ICON = {
  ADD_WAIT: <ClockCircleOutlined />,
  TASK: <FileDoneOutlined />,
  EMAIL: <MailOutlined />,
  NOTE: <PushpinOutlined />,
  DUPLICATE_MAIL: <CopyOutlined />,
  LINKEDIN_CONNECTION_REQUEST: <LinkedinOutlined />,
};

export const TASK_CREATING_TYPE = {
  MANUAL: 'Manual',
  EXTENDED: 'Extended',
  SEQUENCE: 'Sequence',
};

export const TASK_CREATING_TYPE_COLOR = {
  Manual: 'magenta',
  Extended: 'geekblue',
  Sequence: 'lime',
};

export const TASK_CREATING_TYPE_ICON = {
  Manual: <ReconciliationOutlined className="text-xs" />,
  Extended: <HourglassOutlined className="text-xs" />,
  Sequence: <NodeIndexOutlined className="text-xs" />,
};

export const ADD_STEP_TYPE_NAME = {
  ADD_WAIT: 'Delay',
  TASK: 'Task',
  EMAIL: 'Email',
  NOTE: 'Note',
  DUPLICATE_MAIL: 'Duplicated Email',
  LINKEDIN_CONNECTION_REQUEST: 'Linkedin',
};

export const LINKINEDIN_REQUEST_TYPE = {
  INVITATION: 'INVITATION',
  MESSAGE: 'MESSAGE',
  INMAIL: 'INMAIL',
  NORMAL_MESSAGE: 'NORMAL_MESSAGE',
  REACT_POST: 'REACT_POST',
  FOLLOW: 'FOLLOW',
  VIEW_PROFILE: 'VIEW_PROFILE',
};

const LINKEDIN_TYPE_TEXTS = {
  INVITATION: 'Invitation',
  MESSAGE: 'Message',
  INMAIL: 'Inmail',
  NORMAL_MESSAGE: 'Message',
  REACT_POST: 'Like A Post',
  FOLLOW: 'Follow',
  VIEW_PROFILE: 'View Profile',
};

const LINKEDIN_TYPE_ICONS = {
  INVITATION: <ContactsFilled className="text-xs" />,
  MESSAGE: <MessageFilled className="text-xs" />,
  INMAIL: <MailFilled className="text-xs" />,
  NORMAL_MESSAGE: <MessageFilled className="text-xs" />,
  REACT_POST: <LikeFilled className="text-xs" />,
  FOLLOW: <NotificationFilled className="text-xs" />,
  VIEW_PROFILE: <EyeOutlined className="text-xs" />,
};

export const STOP_RULE = {
  INDIVIDUALLY: 'INDIVIDUALLY',
  COMPANY: 'COMPANY',
};

const isFloat = (n) => {
  return Number(n) === n && n % 1 !== 0;
};

const initialMailObject = {
  subject: '',
  content: '',
  fileList: [],
  step: -1,
};

export const replaceImagePlaceholders = (text, imgMetadata) => {
  let counter = 1;

  imgMetadata?.forEach(function (imgData) {
    text = text.replace(
      /<img[^>]*?src="(cid:[^"]*?|{{IMAGE_\d+}})"[^>]*?>/g,
      function (match) {
        const replacedMatch = match.replace(/<img/g, function (imgMatch) {
          return imgMatch + ' id="create-by-code-' + counter + '"';
        });
        counter++;
        return replacedMatch;
      }
    );
    const regex = new RegExp(
      'src="(cid:' + imgData.imgId + '|{{IMAGE_' + (counter - 1) + '}})"',
      'g'
    );
    text = text.replace(regex, 'src="' + imgData.link + '"');

    const crossoriginRegex = new RegExp('crossorigin="use-credentials"', 'g');
    text = text.replace(crossoriginRegex, '');
  });
  return text;
};

export const getContactsFromContactList = async (contactListId) => {
  let page = 1;
  let limit = 100;
  const result = { items: [] };
  let lastResult;
  do {
    try {
      const { data } = await getDetailContactList(
        contactListId,
        page,
        '',
        [],
        limit
      );
      lastResult = data;
      result.items = [...result.items, ...data?.result?.items];
      page++;
    } catch (e) {
      console.log('error: ', e);
    }
  } while (lastResult?.result?.items?.length > 0);
  return result;
};

export const getContactList = async (contactListIds) => {
  const list = await Promise.all(
    contactListIds?.map((id) => getContactsFromContactList(id))
  );
  return list?.map((item) => item);
};

const BullhornSendEmailModal = (props) => {
  const {
    control,
    watch = () => {},
    setValue,
    getValues,
    setConfirmStep,
    setNotDiscard,
    notDiscard,
    job,
    listEmailSend,
    sequenceStatus,
    openModalSendEmail,
    setNumberStep,
    numberStep,
    inputNumberStep,
    setInputNumberStep,
    setEmailConfigData,
    emailConfigData,
    fromSequenseEmail = false,
    formOnlySequense = false,
    dataContent,
    dataSubject,
    defaultUserSelected = [],
    handleChangeEmail,
    preview = false,
    previewMode = null,
    onSubmitTriggers = null,
    onHandleSeqId,
    emailSeqId,
    hotListData = [],
    fromCreatingScratchSequence = false,
    setSelectedStep = null,
    selectedStep = null,
    fromSequenceAI = false,
    notLoadingData = false,
    fromSequenseDetail = false,
    newUpdatedSequence = false,
    fromManualCreate = false,
    setContactListSelected,
    contactListSelected = [],
    closeAllModal = null,
    fromCreateFromTemplateModal = false,
    fromCreateByVacancy = false,
    setListContactSelected,
    listContactSelected,
    actionKey,
    selectedSequenceValue = null,
    fromCreateTemplate = false,
    templateId,
    setPreviewEmail = null,
    isDuplicateTemplate = false,
    handleReset,
    handleClose,
    templateNameModal,
    fromSync = false,
    closeCreateSequenceModal = () => {},
    fromChildSequence = false,
    parentSeqId = '',
    reloadSequenceDetail = () => {},
    fromCandidateSequence = false,
  } = props;

  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const sequenceRef = useRef(null);
  const {
    options: contactOptions,
    setOptions: contactSetOptions,
    handleScrollPopup: handleContactScroll,
    handleSearch: handleContactSearch,
    isLoading: isLoadingContacts,
    setLoading: setIsLoadingContacts,
    valueNotFound: valueNotFoundContacts,
    setCompanyId,
    setStart: contactSetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const [openEditNameModal, setOpenEditNameModal] = useState(false);

  const [isModalAddContactOpen, setIsModalAddContactOpen] = useState(false);
  const [draftContact, setDraftContact] = useState([]);
  const [openRemoveInValidEmail, setOpenRemoveInValidEmail] = useState(false);
  const [hotListCheckInValidData, setHotListCheckInValidData] = useState();

  const [isSelectAll, setSelectAll] = useState(true);

  const [isOpenSequenceOptions, setOpenSequenceOptions] = useState({
    first: false,
  });

  const [openSendMailOptions, setOpenSendMailOptions] = useState(false);
  const [mailObject, setMailObject] = useState(initialMailObject);
  const [openMailEditor, setOpenMailEditor] = useState(false);
  const [openMailTemplate, setOpenMailTemplate] = useState(false);
  const [openMailEditorFromStep, setOpenMailEditorFromStep] = useState(false);
  const [lockedStep, setLockedStep] = useState(false);
  const [createSendMailStatus, setCreateSendMailStatus] = useState(
    fromCreatingScratchSequence ? true : false
  );
  const [loadingSaveSequence, setLoadingSaveSequence] = useState(false);
  const [loadingSaveDraft, setLoadingSaveDraft] = useState(false);
  const [editingNotes, setEditingNotes] = useState(''); // step editting mode
  const [selectingStep, setSelectingStep] = useState(''); // step editting mode
  const [editingStepName, setEditingStepName] = useState('');
  const [isUseSequenceTemplateMode, setUseSequenceTemplateMode] =
    useState(false);
  const [openWarningModal, setOpenWarningModal] = useState(false);

  const [isLoading, setLoading] = useState(true);

  const [dataSignature, setDataSignature] = useState('');
  const [loadingSignature, setLoadingSignature] = useState(true);

  const [avatarUrl, setAvatarUrl] = useState(null);
  const [sequenceId, setSequenceId] = useState(null);
  const [loadingTrigger, setLoadingTrigger] = useState(false);
  const [openConfirmEnrichData, setOpenConfirmEnrichData] = useState(false);
  const [loadingEnrichEmails, setLoadingEnrichEmail] = useState(false);

  // CSV List modal
  const [listCSVTemp, setListCSVTemp] = useState([]);

  const [openParticipantModal, setOpenParticipantModal] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploadCSVLoading, setUploadCSVLoading] = useState(false);
  const [contactList, setContactList] = useState([]);
  const [shortList, setShortList] = useState([]);
  const [hotList, setHotList] = useState([]);
  const [sendToList, setSendToList] = useState([]);
  const [participantActiveKey, setParticipantActiveKey] = useState([]);
  const [loadingTestEmail, setLoadingTestEmail] = useState(false);
  const [isExpandParticipantBox, setExpandParticipantBox] = useState(false);

  const toggleParticipantBox = () =>
    setExpandParticipantBox(!isExpandParticipantBox);

  // Check contacts existing in other sequence
  const [
    contactsInActiveSequenceIgnoreList,
    setContactsInActiveSequenceIgnoreList,
  ] = useState([]);
  const { confirm } = Modal;

  // Scheduling sequence
  const [scheduleInformation, setScheduleInformation] = useState({
    // timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    // utc: 'UTC-12:00',
    // name: 'Etc/GMT+12',
    triggerAt: dayjs(new Date()).format('YYYY-MM-DDTHH:mm:ss'),
    isTriggeredNow: true,
  });
  const [openScheduleDate, setOpenScheduleDate] = useState(false);
  const [isSequenceScheduled, setSequenceScheduled] = useState(false);

  const showScheduleDate = () => {
    setScheduleInformation({
      ...scheduleInformation,
    });
    setOpenScheduleDate(true);
  };
  const closeScheduleDate = () => setOpenScheduleDate(false);

  // Save as a template

  const [loadingSaveAsASequence, setLoadingSaveAsASequence] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [templateName, setTemplateName] = useState('');
  const [loadingDuplicateSequence, setLoadingDuplicateSequence] =
    useState(false);

  // New stop rules
  const [stopRule, setStopRule] = useState('');

  // Bulk Enrich
  const [openBulkEnrichModal, setOpenBulkEnrichModal] = useState(false);

  const showBulkEnrichModal = () => setOpenBulkEnrichModal(true);
  const closeBulkEnrichModal = () => setOpenBulkEnrichModal(false);

  // Get fixed position for editting step
  const staticEditableItem = () => {
    if (selectingStep) {
      const el = document?.getElementById(selectingStep);
      if (!el) return;
      el?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'center',
      });
    }
  };

  const fixedPositionStep = () => {
    staticEditableItem();
  };

  // Mark for saving feature

  const [savedSteps, setSavedSteps] = useState([]);

  const addSavedSteps = (key) => {
    if (fromCreateTemplate) return;
    const newSavedSteps = [...savedSteps, key].filter((item) => item);
    const uniqueSaveSteps = [...new Set([...newSavedSteps])];
    setSavedSteps([...uniqueSaveSteps]);
  };

  //auto saving draft sequence
  const [autoSavingLoading, setAutoSavingLoading] = useState(false);
  const [autoSavingMessage, setAutoSavingMessage] = useState('');

  // Start new sequence
  const [startNewSequenceFlip, setStartNewSequenceFlip] = useState(false);

  // Get Sequence
  const [sequenceItem, setSequenceItem] = useState(null);

  // Validating Emails Process
  // Using for contact list
  const [openEmailsValidator, setOpenEmailsValidator] = useState(false);

  const showEmailsValidator = () => setOpenEmailsValidator(true);
  const closeEmailsValidator = () => setOpenEmailsValidator(false);

  // Using for hot list
  const [openHotListEmailsValidator, setOpenHotListEmailsValidator] =
    useState(false);

  const showHotListEmailsValidator = () => setOpenHotListEmailsValidator(true);
  const closeHotListEmailsValidator = () =>
    setOpenHotListEmailsValidator(false);

  // Using for Send To List
  const [openSendToEmailsValidator, setOpenSendToEmailsValidator] =
    useState(false);

  const showSendToEmailsValidator = () => setOpenSendToEmailsValidator(true);
  const closeSendToEmailsValidator = () => setOpenSendToEmailsValidator(false);

  const [validatingLoading, setValidatingLoading] = useState({
    contactList: false,
    hotList: false,
    sendToList: false,
  });

  const [validatingProcessLoading, setValidatingProcessLoading] = useState({
    contactList: false,
    hotList: false,
    sendToList: false,
  });

  const onStopRuleChange = (ev) => {
    const checked = ev?.target?.checked;
    const value = ev?.target?.value;

    if (checked) {
      setStopRule(value);
    } else {
      if (value === stopRule) {
        setStopRule('');
      }
    }
  };

  useEffect(() => {
    if (
      inputNumberStep?.length > 0 &&
      inputNumberStep[inputNumberStep?.length - 1].hasOwnProperty('delay')
    ) {
      setCreateSendMailStatus(false);
    } else {
      setCreateSendMailStatus(true);
    }
    handleGetDataEmail();
  }, []);

  useEffect(() => {
    if (isNil(getValues('companyId'))) return;

    setCompanyId(getValues('companyId'));
    contactSetOptions([]);
  }, [getValues('companyId')]);

  const { setAuth, profile: profileUserAuth } = useAuth();
  const { profileUser, setViewAs } = useViewAs();

  const userToSet = profileUser || profileUserAuth;
  const userToSetId = userToSet?.user?.id || userToSet?.id;

  useEffect(() => {
    if (!userToSet?.user?.linkedinAvatarUrl && !userToSet?.linkedinAvatarUrl)
      return;

    const avtUrl =
      userToSet?.user?.linkedinAvatarUrl || userToSet?.linkedinAvatarUrl;

    setAvatarUrl(avtUrl);
  }, [userToSet]);

  useEffect(() => {
    if (!window) return;
    const href = window.location.href;
    console.log('fromManualCreate: ', href);
    if (href.includes('manual-leads') || href.includes('my-leads/manual')) {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (
      emailConfigData ||
      fromCreatingScratchSequence ||
      fromSequenceAI ||
      fromChildSequence
    ) {
      setLoading(false);
    }
  }, [
    emailConfigData,
    fromCreatingScratchSequence,
    fromSequenceAI,
    fromChildSequence,
  ]);

  const timeoutRef = useRef(null);

  // Edit mail from step functions
  const showMailEditorFromStep = () => {
    setOpenMailEditorFromStep(true);
  };
  const closeMailEditorFromStep = () => {
    setOpenMailEditorFromStep(false);
  };

  const handleGetData = async () => {
    try {
      const { data } = await getSignatureDefault();
      if (data) {
        // const signatureEl = `<span class="signature-container"><span>${data?.result?.sigContent}</span></span>`;
        const signatureEl = data?.result?.sigContent;
        setDataSignature(signatureEl ?? '');
        setLoadingSignature(false);
      }
    } catch (error) {
      setLoadingSignature(false);
      console.log('newUrl error: ', error);
    }
  };

  useEffect(() => {
    // if (!notLoadingData) {
    // Handle get signature
    handleGetData();
    // }
  }, []);

  useEffect(() => {
    const jobDescriptionEl = document?.getElementById('job-detail-container');
    if (!jobDescriptionEl) return;

    if (openSendMailOptions) {
      jobDescriptionEl.classList.add('max-w-[46rem]');
    } else {
      jobDescriptionEl.classList.remove('max-w-[46rem]');
    }
  }, [openSendMailOptions]);

  // drawer functions
  const showSendMailOptionsDrawer = () => {
    setOpenSendMailOptions(true);
  };
  const closeSendMailOptionsDrawer = () => {
    setOpenSendMailOptions(false);
    closeMailEditorFromStep();
    setEditingNotes('');
    setEditingStepName('');
    setMailObject(initialMailObject);
  };

  // From RE: Threading drawer

  const [openSmartEmailDrawer, setSmartEmailDrawer] = useState(false);

  const showSmartEmailDrawer = () => setSmartEmailDrawer(true);
  const closeSmartEmailDrawer = () => setSmartEmailDrawer(false);

  const FROM_KEY = {
    1: 'VACANCY',
    2: 'LEAD',
    3: 'OPPORTUNITY',
  };

  const showAutoSavingMessage = (message = 'Saved.') => {
    setAutoSavingMessage(message);
    setTimeout(() => {
      setAutoSavingMessage('');
    }, 2000);
  };

  const handleAddMail = async (isDraft = false) => {
    const sequenceName = getValues('sequenceData')?.name?.trim() || '';
    if (inputNumberStep?.length === 0 || !inputNumberStep) return;
    if (
      !sequenceName &&
      !fromCreateTemplate &&
      isDraft !== true &&
      !fromChildSequence
    ) {
      notification.warning({
        description: `Sequence's name is required.`,
      });
      return;
    }

    const now = new Date(
      moment()
        .utcOffset(getOffSet(scheduleInformation?.utc))
        .format('YYYY-MM-DDTHH:mm:ss')
    );
    const schedulingDate = new Date(scheduleInformation?.triggerAt);

    if (
      now >= schedulingDate &&
      !scheduleInformation.isTriggeredNow &&
      isSequenceScheduled &&
      !fromCreateTemplate &&
      isDraft !== true
    ) {
      notification.error({
        description: `Scheduling Date is invalid. Please check again.`,
      });
      return;
    }

    if (inputNumberStep[inputNumberStep?.length - 1]?.delay) {
      notification.error({
        message: 'Latest step must be a mail or note',
      });
      return false;
    }
    const linkedinStep = inputNumberStep.find(
      (item) => item?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST
    );
    if (linkedinStep) {
      const linkedInStepTypes = JSON.parse(linkedinStep?.content);
      const inmailItem = linkedInStepTypes?.find(
        (item) => item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
      );

      const normalMessageItem = linkedInStepTypes?.find(
        (item) => item?.type === LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
      );

      if (inmailItem && !inmailItem?.message && isDraft !== true) {
        notification.warning({
          description: 'LinkedIn Step: InMail Message can NOT empty.',
        });
        return;
      }

      if (
        normalMessageItem &&
        !normalMessageItem?.message &&
        isDraft !== true
      ) {
        notification.warning({
          description: 'LinkedIn Step: Normal Message can NOT empty.',
        });
        return;
      }
    }

    // Get list delay
    const delayListTemp = [];
    let delayData = [];
    for (let i = 0; i < inputNumberStep?.length; i++) {
      if (inputNumberStep[i]?.subject || inputNumberStep[i]?.content) {
        delayListTemp.push(delayData);
        delayData = [];
      } else {
        delayListTemp.push([inputNumberStep[i]]);
        delayData = [...delayData, inputNumberStep[i]];
      }
    }

    const linkedinStepParticipants = getValues('linkedinStepParticipants');

    const arrToSave = inputNumberStep
      ?.map((item, index) => {
        const delaysTemp = delayListTemp[index] || [];
        const delays = delaysTemp?.map((item) => ({
          delay: item?.delay,
          unit: item?.unit === 'Hours' ? 'HOUR' : 'DAY',
          name:
            item?.name ||
            `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index}`,
        }));
        const contentPlaceHolders = item?.content?.match(/\{{(.*?)\}}/g);
        const subjectPlaceHolders = item?.subject?.match(/\{{(.*?)\}}/g);
        const returnItem = {
          ...item,
          delays,
          placeHolders: [
            ...(contentPlaceHolders || []),
            ...(subjectPlaceHolders || []),
          ],
          ...(item?.fileList?.length > 0
            ? {
                fileList: item?.fileList?.map(
                  ({
                    fileId,
                    name,
                    uid,
                    thumbUrl,
                    lastModifiedDate,
                    size,
                    type,
                  }) => ({
                    fileId,
                    name,
                    uid,
                    thumbUrl,
                    lastModifiedDate,
                    size,
                    type,
                  })
                ),
              }
            : {}),
        };

        const ownParticipants = linkedinStepParticipants?.find(
          (inputNumber) =>
            (item?.key && item?.key === inputNumber?.key) ||
            (item?.id && item?.id === inputNumber?.id)
        );
        if (ownParticipants) {
          const recipients = [...(ownParticipants?.recipients || [])]?.filter(
            (item) => item?.emailStatus === 'Valid'
          );
          returnItem.contactListIds = [];
          returnItem.hotlistIds = [];

          // const contactListIds = [...(ownParticipants?.contactListIds || [])];
          // const hotlistIds = [...(ownParticipants?.hotlistIds || [])];
          returnItem.recipients = recipients;
          // returnItem.hotlistIds = hotlistIds;
          // returnItem.contactListIds = contactListIds;
        }

        let newContent = item?.content;

        if (newContent) {
          const temp = document.createElement('div');
          temp.innerHTML = item?.content;
          const sigNode = temp.getElementsByClassName('signature-container');
          if (sigNode.length === 0) return returnItem;

          for (let i = 0; i < sigNode.length; i++) {
            const signEl = sigNode[i];
            const spanEl = document.createElement('span');
            spanEl.setAttribute('class', 'signature-place-holder');
            sigNode[i].replaceChild(spanEl, signEl.childNodes[0]);
          }
          newContent = temp.innerHTML;
          // Restyle for email

          const reStylePTagsEl = document.createElement('div');
          reStylePTagsEl.innerHTML = newContent;
          const allPTagsEl = reStylePTagsEl.getElementsByTagName('p');
          for (let i = 0; i < allPTagsEl.length; i++) {
            allPTagsEl[i].style.margin = '0';
            // allPTagsEl[i].style.fontFamily = 'Arial';
          }

          // const allDivTagsEl = reStylePTagsEl.getElementsByTagName('div');
          // for (let i = 0; i < allDivTagsEl.length; i++) {
          //   allDivTagsEl[i].style.fontFamily = 'Arial';
          // }

          // const allLiTagsEl = reStylePTagsEl.getElementsByTagName('li');
          // for (let i = 0; i < allLiTagsEl.length; i++) {
          //   allLiTagsEl[i].style.fontFamily = 'Arial';
          // }

          returnItem.content = reStylePTagsEl.innerHTML;
        }
        return returnItem;
      })
      .filter((item) => item !== null);

    const hotListIdsTemp = hotList?.map((list) => list?.id || list) || [];
    const shortListIdsTemp = shortList?.map((list) => list?.id || list) || [];
    const contactListIdsTemp =
      contactList?.map((list) => list?.id || list) || [];

    const emailStep = arrToSave?.filter((item) => !item?.delay);

    // Adding participants field
    const participantsTemp = {};
    const recipientsTemp =
      getValues('sendMail.listEmailSend') ||
      getValues('sendMail.mailStepParentMailTo') ||
      listEmailSend ||
      [];

    if (hotListIdsTemp?.length > 0) {
      participantsTemp.hotlistIds = [...hotListIdsTemp];
    }

    if (contactListIdsTemp?.length > 0) {
      participantsTemp.contactListIds = [...contactListIdsTemp];
    }

    if (shortListIdsTemp?.length > 0) {
      participantsTemp.shortListIds = [...shortListIdsTemp];
    }

    if (recipientsTemp?.length > 0) {
      participantsTemp.recipients = [
        ...recipientsTemp
          ?.map((item) => ({
            ...item,
            companyDetail: getValues('companyDetail'),
          }))
          ?.filter((item) => item?.emailStatus === 'Valid'),
      ];
    }

    const childSequence = {
      mails: emailStep?.map((item) => {
        return { ...item };
      }),
      skippedEmails: getValues('skippedEmails') || [],
      stopRules: stopRule
        ? [...(getValues('stopRules') || []), { stopBy: stopRule }]
        : null,
      scheduleInformation,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      participants: participantsTemp,
    };

    const payloadToSave = {
      name: getValues('sequenceData')?.name,
      emailSeqId: fromChildSequence ? parentSeqId : emailSeqId,
      createdFrom: [
        ...(getValues('sendEmail.createFrom') ?? []),
        FROM_KEY[actionKey],
      ].filter((action) => action),
      jobBoardId: fromSequenseEmail
        ? null
        : job?.job_id || job?.job_board_id || job?.id,
      jobBoardName: job?.jobtitle,
      isNotSendSequence: sequenceStatus,
      mails: [
        ...emailStep?.map((item) => ({
          ...item,
          recipients: item?.recipients?.map((jk) => ({
            ...jk,
            companyDetail: getValues('companyDetail'),
          })),
        })),
      ],
      externalJobId: fromSequenseEmail ? job?.id : null,
      stopRules: stopRule
        ? [...(getValues('stopRules') || []), { stopBy: stopRule }]
        : null,
      companyId: getValues('companySequenceContactId') ?? null,
      companyName: getValues('company') ?? null,
      scheduleInformation,
      sequenceTemplateId: templateId ? templateId : currentTemplate?.id || null,
      country: job?.address?.countryName || job?.state,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      ...(!isEmpty(participantsTemp) ? { participants: participantsTemp } : {}),
      skippedEmails: getValues('skippedEmails') || [],
      //
      ...(fromChildSequence && { childSequence }),
    };

    // submit add sequence template
    if (onSubmitTriggers) {
      onSubmitTriggers(payloadToSave);
      return;
    }
    setNotDiscard(true);
    if (isDraft === true) {
      setLoadingSaveDraft(true);
    } else {
      setLoadingSaveSequence(true);
    }

    try {
      const { data } =
        isDraft === true
          ? await saveDraftSequence(payloadToSave)
          : await configEmail(payloadToSave);
      if (data) {
        if (isDraft !== true) {
          notification.success({
            description:
              payloadToSave?.payloadToSave || payloadToSave?.emailSeqId
                ? 'Sequence updated'
                : 'Sequence created.',
          });
        }
        setValue('actionSaveEmail', true);

        setValue('firstMail', inputNumberStep[0]);
        setValue(
          'firstStepUnit',
          inputNumberStep[0]?.unit ?? arrToSave[0]?.unit
        );
        if (emailSeqId) {
          onHandleSeqId(emailSeqId);
          setSequenceId(emailSeqId);
          reloadSequenceDetail();
        }
        if (data?.result?.emailSeqId) {
          onHandleSeqId(data?.result?.emailSeqId);
          setSequenceId(data?.result?.emailSeqId);
        }
        setValue('actionSaveEmail', true);
      }
      if (isDraft === true) {
        setLoadingSaveDraft(false);
        showAutoSavingMessage();
      } else {
        setLoadingSaveSequence(false);
      }
      if (isDraft === true) return;
      if (closeAllModal) {
        closeAllModal();
      }
      closeCreateSequenceModal();
    } catch (e) {
      notification.error({
        message: e?.response?.data?.message ?? 'Mail add Failed',
        description: 'Mail Step add failed',
      });
      if (isDraft === true) {
        setLoadingSaveDraft(false);
      } else {
        setLoadingSaveSequence(false);
      }
    }
    setConfirmStep(false);
  };

  useEffect(() => {
    if ((job?.job_board_id || job?.job_id || job?.id) && !notLoadingData) {
      getDataEmail();
    } else {
      setLoading(false);
    }
  }, [job?.job_board_id, job?.job_id, job?.id]);

  useEffect(() => {
    if (
      !selectedSequenceValue?.scheduleInformation &&
      watch('participants')?.sendFromContact?.length === 0
    )
      return;

    if (selectedSequenceValue?.scheduleInformation) {
      setScheduleInformation({ ...selectedSequenceValue?.scheduleInformation });
    }

    const participants = getValues('participants');
    if (participants) {
      const { contactList, hotList, sendTo, shortList } = participants;
      if (contactList?.length > 0) {
        setContactList([...(contactList || [])]);

        if (contactList?.length > 0) {
          submitContactList(contactList, true);
        }
      }
      if (hotList?.length > 0) {
        setHotList([...(hotList || [])]);
      }
      if (sendTo?.length > 0) {
        setSendToList([...(sendTo || [])]);
      }
      if (shortList?.length > 0) {
        setShortList([...(shortList || [])]);
      }
    }
    if (selectedSequenceValue?.child) {
      setStartNewSequenceFlip(true);
    }
  }, [
    selectedSequenceValue?.scheduleInformation,
    watch('participants'),
    selectedSequenceValue?.child,
  ]);

  useEffect(() => {
    if (watch('sequenceData.stopRules')?.length > 0) {
      const rule = watch('sequenceData.stopRules')?.[0];

      setStopRule(rule?.stopBy);
    }
  }, [watch('sequenceData')]);

  useEffect(() => {
    const triggerItem = getValues('triggerItem') || {};

    if (!_.isEmpty(triggerItem)) {
      const rawSequence = _.isString(triggerItem?.rawSequence)
        ? JSON.parse(triggerItem?.rawSequence || '{}')
        : triggerItem?.rawSequence;

      if (
        rawSequence?.length > 0 &&
        rawSequence?.length > inputNumberStep?.length
      ) {
        const listFollowingMails = [...rawSequence] || [];
        setInputNumberStep([...listFollowingMails]);
        setNumberStep(listFollowingMails?.length);
        // setSelectedStep(listFollowingMails[0]);
      }
      setLoading(false);
    }
  }, [getValues('triggerItem')]);

  const handleAutoSavingSequenceDraft = async () => {
    setAutoSavingLoading(true);
    await handleAddMail(true).finally(() => {
      setAutoSavingLoading(false);
      showAutoSavingMessage();
    });
    console.log('Savinggg...', dayjs(new Date()).format('HH:mm:ss'));
  };

  useEffect(() => {
    const checkingSequence = selectedSequenceValue || sequenceItem;
    if (
      fromCreateTemplate ||
      fromSequenseDetail ||
      checkingSequence?.status ||
      checkingSequence?.child ||
      fromChildSequence
    )
      return;
    // Auto saving draft sequence every 1 minute
    let interval = setInterval(handleAutoSavingSequenceDraft, 1000 * 60);
    return () => {
      clearInterval(interval);
    };
  }, [inputNumberStep, emailSeqId, sequenceItem, selectedSequenceValue]);

  const getDataEmail = async () => {
    try {
      setLoading(true);
      const { data } = await getEmailConfigInJobBoard(
        job?.job_board_id || job?.job_id || job?.id,
        fromSequenseEmail
      );
      if (data) {
        const newValueArr = data?.result?.mails?.map((item) => {
          return { ...item };
        });

        if (emailSeqId || newValueArr[0]?.sequence?.id) {
          setSequenceItem(newValueArr?.[0]?.sequence || null);
          const stepsTemp = data?.result?.mails;
          // Mapping participants in Linkedin Step
          const linkedinStepParticipantsTemp =
            getValues('linkedinStepParticipants') || [];
          const linkedinStepParticipants = [];
          const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
            if (
              step?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
              (step?.recipients?.length > 0 ||
                step?.contactListIds?.length > 0 ||
                step?.hotlistIds?.length > 0)
            ) {
              const linkedinStepParticipantsObject = {
                id: step?.id || uuid(),
                key: step?.key || uuid(),
                contactListIds: step?.contactListIds || [],
                hotlistIds: step?.hotlistIds || [],
                recipients: step?.recipients || [],
              };
              linkedinStepParticipants.push(linkedinStepParticipantsObject);
            }

            return [
              ...step?.delays.map((delayItem) => ({
                ...delayItem,
                unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
                key: step?.id || uuid(),
              })),
              {
                ...step,
                id: step?.id,
                subject: step?.subject,
                content: step?.content,
                type: step?.type || '',
                key: step?.id || uuid(),
              },
            ];
          });

          setValue('linkedinStepParticipants', [
            ...linkedinStepParticipants,
            ...linkedinStepParticipantsTemp,
          ]);
          setInputNumberStep([...inputNumberStepTemp]);
          setNumberStep(inputNumberStepTemp?.length || 0);

          onHandleSeqId(newValueArr[0]?.sequence?.id);
          setValue('actionSaveEmail', true);
        } else {
          setInputNumberStep([]);
          setNumberStep(0);
          setValue('firstMail', null);
          setValue('sendMail', null);
        }
        // setInputNumberStep([...newData]);
        setValue(`sendMail.mailStepParent`, newValueArr?.[0]?.delay);
        setValue(
          `sendMail.mailStepParentMailTo`,
          data?.result?.mails?.[0]?.recipients ?? []
        );
        const recipients =
          data?.result?.mails?.[0]?.sequence?.participants || null;
        if (recipients) {
          const {
            contactListIds,
            hotListIds,
            recipients: sendToList,
            shortListIds,
          } = recipients;
          const list = await Promise.all([
            await getContactLists(contactListIds),
            await getHotLists(hotListIds),
            await getShortLists(shortListIds),
          ]);
          const contactListTemp = list[0] || [];
          const hotListtemp = list[1] || [];
          const shortListTemp = list[2] || [];
          if (contactListTemp?.length > 0) {
            submitContactList(contactListTemp, true);
          }
          setContactList([...contactListTemp]);
          setHotList([...hotListtemp]);
          setShortList([...shortListTemp]);
          setSendToList([...sendToList]);
          setValue('participants', {
            contactList: contactListTemp,
            hotList: hotListtemp,
            shortList: shortListTemp,
            sendTo: sendToList,
          });

          setValue('sendMail.listEmailSend', [...sendToList]);
          setValue('sendMail.mailStepParentMailTo', [...sendToList]);
          setValue(
            'sendMail.listEmail',
            [...sendToList]?.map((data) => data.email)
          );
        }
        const sequenceName = data?.result?.mails?.[0]?.sequence?.name || '';
        if (sequenceName) {
          setValue('sequenceData.name', sequenceName);
        }
        const scheduleInformation =
          data?.result?.mails?.[0]?.sequence?.scheduleInformation || null;
        if (scheduleInformation) {
          setScheduleInformation({ ...scheduleInformation });
        }
        setEmailConfigData(
          data?.result?.mails?.length > 0
            ? data?.result?.mails
            : emailConfigData ?? []
        );
        setLoading(false);
      }
    } catch (error) {
      console.log('error: ', error);
      setLoading(false);
    }
  };

  const handleEditMailStep = () => {
    if (!mailObject) return;
    const { step, content, subject, key, id, fileList } = mailObject;
    const mainKey = mailObject?.id || mailObject?.key;
    addSavedSteps(mainKey);

    const inputNumberStepTemp = [...inputNumberStep];
    const indexToUpdate = inputNumberStep.findIndex(
      (item) => item?.key === mainKey || item?.id === mainKey
    );
    inputNumberStepTemp[indexToUpdate] = {
      ...inputNumberStepTemp[indexToUpdate],
      content,
      subject,
      fileList,
    };

    const updatedSteps = [...inputNumberStep];

    const getNewContent = (currentContent, newContent) => {
      const temp = document.createElement('div');
      temp.innerHTML = currentContent;
      const threadingElements = temp.getElementsByClassName(
        'main-threading-email-content'
      );
      if (!threadingElements?.length) return currentContent;
      threadingElements[0].innerHTML = newContent;
      return temp.innerHTML;
    };

    // Helper function to recursively update threading emails
    const updateThreadingEmails = (
      steps,
      parentKey,
      updatedContent,
      updatedSubject
    ) => {
      return steps.map((step) => {
        if (step.threadId === parentKey) {
          // Update the threading email's subject and content
          const newThreadingContent = `
            <p style="font-family:Calibri; font-size: 11pt"><strong>From:</strong> <span class="w-fit">{{SENDER_NAME}}</span> <<a href="mailto:{{SENDER_EMAIL}}">{{SENDER_EMAIL}}</a>></p>
            <p style="font-family:Calibri; font-size: 11pt"><strong>Sent:</strong> <span class="w-fit">{{SENT_DATE_TIME}}</span></p>
            <p style="font-family:Calibri; font-size: 11pt"><strong>To:</strong> <span class="w-fit">{{RECIPIENT_NAME}}</span> <<a href="mailto:{{RECIPIENT_EMAIL}}">{{RECIPIENT_EMAIL}}</a>></p>
            <p style="font-family:Calibri; font-size: 11pt"><strong>Subject:</strong> ${updatedSubject}</p>
            <br>
            ${updatedContent}
          `;

          step.subject = `RE: ${updatedSubject}`;
          step.content = getNewContent(
            step.content,
            `<div class="main-threading-email-content">${newThreadingContent}</div>`
          );

          // Recursively update nested threading emails
          const nestedKey = step?.id || step?.key;
          const isNestedThreadingEmail = steps?.some(
            (item) => item?.threadId === nestedKey
          );
          if (isNestedThreadingEmail) {
            updateThreadingEmails(steps, nestedKey, step.content, step.subject);
          }

          // Ensure the current step is updated in the returned array
          return {
            ...step,
            content: step.content,
            subject: step.subject,
          };
        }
        return step;
      });
    };

    // Update the main email
    const mainEmailIndex = updatedSteps.findIndex(
      (step) => step.key === mainKey || step.id === mainKey
    );

    if (mainEmailIndex !== -1) {
      updatedSteps[mainEmailIndex] = {
        ...updatedSteps[mainEmailIndex],
        content,
        subject,
        fileList,
      };

      // Update all related threading emails
      updateThreadingEmails(updatedSteps, mainKey, content, subject);
    }
    setInputNumberStep([...updatedSteps]);

    closeMailEditorFromStep();
  };

  const handleUpsertEmails = async () => {
    setLoadingEnrichEmail(true);
    try {
      const payload = hotListCheckInValidData?.invalidEmails?.map((item) => ({
        email: item,
        status: 'Invalid',
      }));
      const { data } = await createNewEmailValid({ data: payload });
      if (data) {
        setOpenRemoveInValidEmail(false);
        setOpenConfirmEnrichData(false);
        setHotListCheckInValidData();

        notification.success({
          message: 'Data enrichment completed successfully.',
        });
      }
      setLoadingEnrichEmail(false);
    } catch (e) {
      notification.error({
        message: 'Something went wrong.',
      });
      setLoadingEnrichEmail(false);
    }
  };

  const handleAddNewStep = (
    step = '',
    type = null,
    mail = null,
    linkedinType = ''
  ) => {
    console.log('step: ', step);
    console.log('type: ', type);

    setNumberStep(numberStep + 1);

    const dataCreateStep = {
      key: uuid(),
      type,
      name: `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${step + 1}`,
    };

    if (type && type === ADD_STEP_TYPE.ADD_WAIT) {
      setCreateSendMailStatus(false);
      dataCreateStep.delay = 1;
      dataCreateStep.unit = 'Days';
    }

    if (type && type === ADD_STEP_TYPE.ADD_NOTE) {
      setLockedStep(true);
      dataCreateStep.note = true;
      dataCreateStep.saved = false;
      setEditingNotes(dataCreateStep.key);
    }

    if (type && type === ADD_STEP_TYPE.SEND_MAIL) {
      setCreateSendMailStatus(true);
      const content = `<div style="font-family:Calibri;font-size: 11pt">
                        <br><br><br><br><br><br><br><br><br><br><br><br>
                        ${dataSignature}
                      </div>`;

      dataCreateStep.subject = mailObject?.subject;
      dataCreateStep.content = mailObject?.content || content;
      if (mailObject?.threadId) {
        dataCreateStep.threadId = mailObject?.threadId;
      }
      // attachment feature
      dataCreateStep.fileList = [...mailObject?.fileList];
    }
    if (type && type === ADD_STEP_TYPE.DUPLICATE_MAIL) {
      const subject = `RE: ${mail?.subject}`;
      const content = `Hi, I am just following up on the Vacancy: <br/> ${mail?.content?.replaceAll('\n', '')}`;

      dataCreateStep.subject = subject;
      dataCreateStep.content = content;
      // attachment feature
      dataCreateStep.fileList = [...mailObject?.fileList];
    }

    if (type && type === ADD_STEP_TYPE.TASK) {
      dataCreateStep.creatingType = TASK_CREATING_TYPE.SEQUENCE;
    }

    if (type && type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST) {
      const subject = `LinkedIn Invitation`;
      const linkedinStep = {
        type: linkedinType,
        message: '',
      };
      const linkedinContent = [linkedinStep];

      dataCreateStep.subject = subject;
      dataCreateStep.content = JSON.stringify(linkedinContent);
      setEditingNotes(dataCreateStep?.key);

      // Add default contacts for Linkedin Step
      const linkedinStepParticipantsTemp =
        getValues('linkedinStepParticipants') || [];
      if (
        contactList?.length > 0 ||
        hotList?.length > 0 ||
        sendToList?.length > 0
      ) {
        const linkedinStepParticipantsObject = {
          id: dataCreateStep?.id || uuid(),
          key: dataCreateStep?.key || uuid(),
          // contactListIds: [...contactList?.map((item) => item?.id || item)],
          // hotlistIds: [...hotList?.map((item) => item?.id || item)],
          recipients: [...sendToList],
        };

        setValue('linkedinStepParticipants', [
          { ...linkedinStepParticipantsObject },
          ...linkedinStepParticipantsTemp,
        ]);
      }
    }

    if (step > 0) {
      const openSequenceOptionsTemp = {
        ...isOpenSequenceOptions,
      };
      openSequenceOptionsTemp[`step_${step}`] = false;
      setOpenSequenceOptions({ ...openSequenceOptionsTemp });
    } else {
      setOpenSequenceOptions({ ...isOpenSequenceOptions, first: false });
    }
    let newInputNumberStep = [...inputNumberStep];
    // if (step !== 0) {
    newInputNumberStep = [
      ...inputNumberStep.slice(0, step),
      dataCreateStep,
      ...inputNumberStep
        .slice(step)
        .map((item) => ({ ...item, key: item?.key + 1 })),
    ];

    setInputNumberStep([...newInputNumberStep]);
    setValue(
      `mailStep.step_${numberStep + 1}.delay`,
      inputNumberStep?.length + 1
    );

    setEditingNotes(dataCreateStep.key);
    setSelectingStep(dataCreateStep.key);
    setConfirmStep(true);
    setNotDiscard(false);
    addSavedSteps(dataCreateStep.key);
  };

  const handleDiscard = () => {
    setNumberStep(0);
    setInputNumberStep([]);
    setValue('mailStep', null);
    setOpenSequenceOptions({ first: false });
    setEditingStepName('');
    setEditingNotes('');
    setCurrentTemplate(null);
  };

  const updateObjectInArray = (arr, keyToUpdate, newTime) => {
    const indexToUpdate = arr.findIndex(
      (item, itemIndex) => itemIndex === keyToUpdate
    );
    if (indexToUpdate !== -1) {
      arr[indexToUpdate].delay = newTime;
    }
    return arr;
  };

  const durationUnitItems = [
    {
      value: 'Days',
      label: 'Days',
    },
    {
      value: 'Hours',
      label: 'Hours',
    },
  ];

  const onChooseMailTemplate = (chooseItem) => {
    const sequenceItemData = JSON.parse(chooseItem?.content);
    const { rawSequence: rawSequenceAllSteps } = sequenceItemData?.triggerItem;
    const { name, content, id, createdBy } = chooseItem;
    setCurrentTemplate({
      createdBy,
      name,
      content,
      id,
    });
    setNotDiscard(false);
    if (rawSequenceAllSteps?.length === 0 || !rawSequenceAllSteps) {
      notification.warning({
        description: "This template doesn't have any steps!",
      });
      return;
    }
    const rawSequence = rawSequenceAllSteps.filter(
      (item) => item?.type === 'EMAIL'
    );

    // Get all items in sequence component
    if (isUseSequenceTemplateMode) {
      const stepsTemp = sequenceItemData?.triggerItem?.rawSequence;

      const inputNumberStepTemp = stepsTemp
        ?.map((step) => [
          step?.delays && [
            ...step?.delays?.flatMap((delayItem) => ({
              ...delayItem,
              unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
              key: uuid(),
              status: null,
              // type: ADD_STEP_TYPE.ADD_WAIT
            })),
          ],
          {
            ...step,
            id: uuid(),
            subject: step?.subject,
            content: step?.content,
            type: step?.type || '',
            key: uuid(),
            rawKey: step?.key,
            rawId: step?.id,
            status: null,
          },
        ])
        .flat(Infinity)
        .filter((item) => item?.key);

      const updatedThreadingInputNumberStep = inputNumberStepTemp?.map(
        (item) => {
          const contentWithSig =
            item?.type === ADD_STEP_TYPE.SEND_MAIL
              ? replaceSignaturePlaceholder(item?.content, dataSignature)
              : item?.content;
          if (!item?.threadId) return { ...item, content: contentWithSig };

          const mainEmail = inputNumberStepTemp.find(
            (main) =>
              main?.rawId === item?.threadId || main?.rawKey === item?.threadId
          );
          const newThreadingId = mainEmail?.id;
          return {
            ...item,
            threadId: newThreadingId,
            content: contentWithSig,
          };
        }
      );
      console.log('on choosing template: ', updatedThreadingInputNumberStep);
      if (
        fromCreatingScratchSequence &&
        updatedThreadingInputNumberStep[0]?.type !== ADD_STEP_TYPE.SEND_MAIL
      ) {
        notification.warning({
          message: 'Can not use template',
          description: "The template doesn't have MAIL step in the first step!",
        });
        setUseSequenceTemplateMode(false);
        return;
      }
      // if (setSelectedStep) {
      //   setSelectedStep({
      //     ...updatedThreadingInputNumberStep[0],
      //   });
      // }

      setInputNumberStep(updatedThreadingInputNumberStep);
      setUseSequenceTemplateMode(false);
      return;
    }
    // Get subject and content template

    if (rawSequence) {
      if (rawSequence[0]?.content || rawSequence[0]?.subject) {
        const content =
          rawSequence[0]?.content ||
          sequenceItemData?.mailDefine?.sendMail?.content ||
          '';
        const subject =
          rawSequence[0]?.subject ||
          sequenceItemData?.mailDefine?.sendMail?.subject ||
          '';

        setMailObject({ ...mailObject, subject, content });
        setOpenMailEditor(true);
      }
    }
  };

  const handleCloseTemplateOptions = () => setOpenMailTemplate(false);
  const handleOpenTemplateOptions = () => setOpenMailTemplate(true);

  const handleChange = (value, options, isSelectAll = false) => {
    console.log('handleChange value: ', value);
    console.log('handleChange options: ', options);
    const dataSetTemp = options
      ?.map((obj) => ({
        ...obj,
        label: obj?.name || obj?.firstName || obj?.label,
        name: obj?.name || obj?.firstName,
        email: obj?.email,
        id: obj?.id || obj?.contactId || uuid(),
      }))
      .filter(
        (item) =>
          item?.label &&
          (item?.value?.includes('HOTLIST') ||
            item?.value?.includes('CONTACTLIST') ||
            (item?.name && item?.email))
      );
    const dataSet = arrayUniqueByKey(dataSetTemp, 'email');

    // setListEmailSend(dataSet);
    const createFromValues = getValues('sendEmail.createFrom') ?? [];
    if (!createFromValues.includes('VACANCY')) {
      setValue('sendEmail.createFrom', [...createFromValues, 'VACANCY']);
    }
    setValue('sendMail.listEmailSend', dataSet);
    setValue('sendMail.mailStepParentMailTo', dataSet);
    setValue(
      'sendMail.listEmail',
      dataSet?.map((data) => data?.email || data?.label)
    );
    setSendToList([...(dataSet.filter((item) => item?.email) || [])]);

    if (isSelectAll) {
      notification.success({
        description: `Added ${dataSet?.length} / ${options?.length} contacts.`,
      });
    }
    if (validatingProcessLoading.sendToList) {
      // remove all invalid contacts
      const invalidContacts = [...(dataSet || [])]?.filter(
        (contact) =>
          !VALID_EMAIL_STATUS.includes(contact?.emailStatus) ||
          !contact?.emailStatus
      );
      const invalidEmails = invalidContacts
        ?.map((item) => item?.email)
        ?.filter((email) => email);

      const currentSkippedEmails = getValues('skippedEmails') || [];
      setValue('skippedEmails', [...currentSkippedEmails, ...invalidEmails]);
    }

    // setValue('participants.sendTo', [...newSendToList]);
    // setSendToList([...newSendToList]);

    const contactsInActiveSequenceListTemp = options
      ?.filter(
        (contact) =>
          contact?.isInActiveSequence &&
          !contactsInActiveSequenceIgnoreList?.includes(contact?.email)
      )
      ?.map((item) => item?.email || data?.label);
    if (contactsInActiveSequenceListTemp?.length > 0) {
      confirm({
        title: 'Warning',
        icon: <ExclamationCircleFilled />,
        content: `We found ${contactsInActiveSequenceListTemp?.length} contact(s) you just selected are currently in an active sequence.`,
        onOk() {
          const newSendToList = [...options]?.filter(
            (contact) =>
              !contactsInActiveSequenceListTemp?.includes(contact?.email)
          );
          handleChange(null, newSendToList);
        },
        onCancel() {
          setContactsInActiveSequenceIgnoreList([
            ...contactsInActiveSequenceIgnoreList,
            ...contactsInActiveSequenceListTemp,
          ]);
        },
        okText: 'Remove',
        cancelText: 'Keep',
      });
    }
  };

  const handleGetDataEmail = async () => {
    try {
      const { data } = await getDataUniplieAccount();
      if (data.result.data.sources[0]?.status === 'OK') {
        actionSetStatus(data, 'CONNECTED');
      } else {
        actionSetStatus(data, 'DISCONNECTED');
      }
    } catch (e) {
      actionSetStatus(null, 'DISCONNECTED');
    }
  };

  const actionSetStatus = (data, status) => {
    if (isViewAs()) {
      setViewAs({
        profileUser: {
          ...profileUser,
          unipileAccountId: data?.result?.data?.id,
          unipileAccountStatus: status,
          linkedinAvatarUrl:
            status == 'CONNECTED'
              ? data?.result?.data?.dataLinkedInProfile?.profile_picture_url
              : profileUser?.linkedinAvatarUrl,
        },
      });
    } else {
      setAuth({
        profile: {
          ...profileUserAuth,
          user: {
            ...profileUserAuth?.user,
            unipileAccountId: data?.result?.data?.id,
            unipileAccountStatus: status,
            linkedinAvatarUrl:
              status == 'CONNECTED'
                ? data?.result?.data?.dataLinkedInProfile?.profile_picture_url
                : profileUser?.linkedinAvatarUrl,
          },
        },
      });
    }
  };

  const handleSaveContact = () => {
    const contactListSelected = getValues('sendMail.contactListSelected') || [];
    const listEmailSend = getValues('sendMail.listEmailSend') || [];
    setValue('sendMail.contactListSelectedSaved', [...contactListSelected]);
    setListContactSelected && setListContactSelected([...listEmailSend]);
    // Redesign contacts list
    setValue('participants.sendTo', [...listEmailSend]);
    setSendToList([...listEmailSend]);
    // const total = [...listEmailSend].length;
    // if (hotList?.length > 0) {
    //   notification.success({
    //     description: `Added ${hotList?.length} Hot Lists successfully!`,
    //   });
    // }
    // if (contactList?.length > 0)
    //   notification.success({
    //     description: `Added ${contactList?.length} Contact Lists successfully!`,
    //   });
    // if (total > 0) {
    //   notification.success({
    //     description: `Added ${total} Contacts successfully!`,
    //   });
    // }
    // setIsModalAddContactOpen(false);

    // Add default contacts for Linkedin Step
    const linkedinStepParticipantsTemp =
      getValues('linkedinStepParticipants') || [];
    const linkedinSteps = inputNumberStep?.filter(
      (item) => item?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST
    );

    const currentLinkedinStepParticipants =
      getValues('linkedinStepParticipants') || [];

    if (linkedinSteps?.length > 0) {
      linkedinSteps?.forEach((inputNumber) => {
        const linkedinStepParticipantsObject = {
          id: inputNumber?.id || uuid(),
          key: inputNumber?.key || uuid(),
          contactListIds: [...contactList?.map((item) => item?.id || item)],
          hotlistIds: [...hotList?.map((item) => item?.id || item)],
          recipients: [...listEmailSend],
        };

        setValue('linkedinStepParticipants', [
          { ...linkedinStepParticipantsObject },
          ...linkedinStepParticipantsTemp,
        ]);
      });
      const newLinkedinStepParticipants = arrayUniqueByKey(
        [...currentLinkedinStepParticipants, ...linkedinStepParticipantsTemp],
        'id'
      );
      const checkedAllowContacts = newLinkedinStepParticipants.map((item) => {
        // Allow list
        const allowContactListIds =
          contactList?.map((item) => item?.id || item) || [];
        const allowHotListIds = hotList?.map((item) => item?.id || item);
        const allowRecipients = listEmailSend?.map(
          (item) => item?.id || item?.key
        );

        const contactListIds =
          item?.contactListIds?.filter((item) =>
            allowContactListIds?.includes(item?.id || item)
          ) || [];
        const hotlistIds =
          item?.hotlistIds?.filter((item) =>
            allowHotListIds?.includes(item?.id || item)
          ) || [];
        const recipients =
          item?.recipients?.filter((item) =>
            allowRecipients?.includes(item?.id || item?.key)
          ) || [];

        return {
          ...item,
          contactListIds,
          hotlistIds,
          recipients,
        };
      });
      setValue('linkedinStepParticipants', [...checkedAllowContacts]);
    }
    setOpenParticipantModal(false);
    toggleParticipantBox();
    setParticipantActiveKey([]);
  };

  const isLinkedInStepBlocked =
    (userToSet &&
      !userToSet?.user?.unipileAccountId &&
      !userToSet?.unipileAccountId) ||
    userToSet?.user?.unipileAccountStatus === 'DISCONNECTED' ||
    userToSet?.unipileAccountStatus === 'DISCONNECTED';

  const handleSaveAsTemplate = async (isNew = false) => {
    setLoadingSaveAsASequence(true);
    if (inputNumberStep[inputNumberStep?.length - 1]?.delay) {
      notification.error({
        message: 'Latest step must be a mail or note',
      });
      return false;
    }

    const linkedinStep = inputNumberStep.find(
      (item) => item?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST
    );
    if (linkedinStep) {
      const linkedInStepTypes = JSON.parse(linkedinStep?.content);
      const inmailItem = linkedInStepTypes?.find(
        (item) => item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
      );

      const normalMessageItem = linkedInStepTypes?.find(
        (item) => item?.type === LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
      );

      if (inmailItem && !inmailItem?.message) {
        notification.warning({
          description: 'LinkedIn Step: InMail Message can NOT empty.',
        });
        return;
      }

      if (normalMessageItem && !normalMessageItem?.message) {
        notification.warning({
          description: 'LinkedIn Step: Normal Message can NOT empty.',
        });
        return;
      }
    }

    // Get list delay
    const delayListTemp = [];
    let delayData = [];
    for (let i = 0; i < inputNumberStep?.length; i++) {
      if (inputNumberStep[i]?.subject || inputNumberStep[i]?.content) {
        delayListTemp.push(delayData);
        delayData = [];
      } else {
        delayListTemp.push([inputNumberStep[i]]);
        delayData = [...delayData, inputNumberStep[i]];
      }
    }

    const arrToSave = inputNumberStep
      ?.map((item, index) => {
        const delaysTemp = delayListTemp[index] || [];
        const delays = delaysTemp?.map((item) => ({
          delay: item?.delay,
          unit: item?.unit === 'Hours' ? 'HOUR' : 'DAY',
        }));

        const placeHolders = item?.content?.match(/\{{(.*?)\}}/g);
        return {
          ...item,
          delays,
          placeHolders,
        };
      })
      .filter((item) => item !== null);

    const emailStep = arrToSave?.filter((item) => !item?.delay);

    const contentObj = {
      mailDefine: {
        triggerItem: {
          rawSequence: [...emailStep],
        },
        sendMail: {
          content: '',
          subject: '',
        },
      },
      triggerItem: {
        rawSequence: [...emailStep],
      },
    };
    const content = JSON.stringify(contentObj);
    setLoadingSaveAsASequence(true);

    if (currentTemplate?.id && !isNew) {
      const updatingTemplate = {
        ...currentTemplate,
        content,
      };
      await updateSingleSequenceTemplate(updatingTemplate)
        .then((res) => {
          if (res?.data) {
            // setCurrentTemplate(null);
            notification.success({
              message: 'Sucess',
              description: 'The template is updated!',
            });
          }
        })
        .catch((err) => {
          notification.error({
            message: 'Error',
            description: 'Please try again later',
          });
        })
        .finally(() => {
          setLoadingSaveAsASequence(false);
          setTemplateName('');
        });
      return;
    }

    const creatingTemplate = {
      name: isDuplicateTemplate ? templateNameModal : templateName,
      content,
      organizationId:
        userToSet?.user?.organizationId || userToSet?.organization?.id,
      modifiedBy: userToSet?.user?.id || userToSet?.id,
      createdBy: userToSet?.user?.id || userToSet?.id,
    };

    await createSequenceTemplate(creatingTemplate)
      .then((res) => {
        if (res?.data?.result) {
          setCurrentTemplate({ ...res?.data?.result });
          notification.success({
            message: 'Sucess',
            description: 'The template is added!',
          });
        }
      })
      .catch((err) => {
        notification.error({
          message: 'Error',
          description: 'Please try again later',
        });
      })
      .finally(() => {
        setLoadingSaveAsASequence(false);
        setTemplateName('');
        handleReset();
        handleClose();
      });
  };

  const submitContactList = async (contactList, isInBackground = false) => {
    contactSetOptions([]);
    setValue('participants.contactList', [...contactList]);
    setContactList([...contactList]);
    if (contactList?.length <= 0) return;
    if (!isInBackground) {
      setValidatingLoading({
        ...validatingLoading,
        contactList: true,
      });
    }

    setValidatingProcessLoading({
      ...validatingProcessLoading,
      contactList: true,
    });
    const contactListIds =
      contactList?.length > 0 ? contactList?.map((item) => item?.id) : [];

    if (contactListIds?.length <= 0) {
      setValidatingProcessLoading({
        ...validatingProcessLoading,
        contactList: false,
      });
      return;
    }

    try {
      const dataContactListRaw = await getContactList(contactListIds);
      const dataContactList = dataContactListRaw?.flatMap(
        (list) => list?.items
      );
      const contactListItems = [...(dataContactList || [])];
      const emailLists = contactListItems?.map((item) => item?.email);

      const { data: dataEmailFromDb } = await validListEmail({
        emails: emailLists,
      });
      const listEmailFormDb = dataEmailFromDb?.result?.map((item) => ({
        email: item?.email,
        status:
          (VALID_EMAIL_STATUS?.includes(item?.result) && 'Valid') || 'Invalid',
      }));

      const dataContactListWithStatus = dataContactList?.map((item) => ({
        ...item,
        status:
          listEmailFormDb.find(
            (email) =>
              email?.email?.toLowerCase() === item?.email?.toLowerCase()
          )?.status || 'Invalid',
      }));

      const newContactList = contactList?.map((item, index) => {
        const contactsOfList =
          item?.contacts?.length > 0
            ? item?.contacts
            : dataContactListRaw[index]?.items;
        return {
          ...item,
          contacts: contactsOfList?.map((contact) => {
            const contactWithStatus = dataContactListWithStatus.find(
              (email) => email?.id === contact?.id
            );
            return {
              ...contact,
              ...(contactWithStatus
                ? contactWithStatus
                : { status: 'Invalid' }),
            };
          }),
        };
      });
      // remove all invalid contacts
      const invalidContacts = newContactList
        ?.flatMap((list) => list?.contacts)
        ?.filter(
          (contact) =>
            !VALID_EMAIL_STATUS.includes(contact?.status) || !contact?.status
        );
      const invalidEmails = invalidContacts
        ?.map((item) => item?.email)
        ?.filter((email) => email);

      const currentSkippedEmails = getValues('skippedEmails') || [];
      setValue('skippedEmails', [...currentSkippedEmails, ...invalidEmails]);

      setContactList([...newContactList]);
      setValue('participants.contactList', [...newContactList]);

      setValidatingProcessLoading({
        ...validatingProcessLoading,
        contactList: false,
      });
    } catch (e) {
      console.log('error', e);
      setValidatingProcessLoading({
        ...validatingProcessLoading,
        contactList: false,
      });
      notification.error({ message: 'Error' });
    }
  };

  const submitShortList = async (shortListTemp) => {
    const newShortList = arrayUniqueByKey(
      [...shortList, ...shortListTemp],
      'id'
    );
    setValue('participants.shortList', [...newShortList]);
    setShortList([...newShortList]);
    const shortListIds = newShortList?.map((item) => item?.id);
    if (shortListIds?.length <= 0) return;
    const listShortListRes = await Promise.all(
      shortListIds?.map((id) => getShortlistById(id))
    );
    const candidates = listShortListRes?.flatMap((item) => {
      const shortListData = item?.data?.result || [];
      const shortListCandidates = shortListData?.map((list) => list?.candidate);

      return shortListCandidates;
    });

    const emailLists = candidates?.map((item) => item?.email);
    const { data: dataEmailFromDb } = await getNewEmailValid({
      emails: emailLists,
    });
    const listEmailFormDb = dataEmailFromDb?.result?.map((item) => ({
      email: item?.email,
      status: item?.status,
    }));
    const listEmailIgnore = dataEmailFromDb?.result?.map((item) => item?.email);
    const emailToCheck = emailLists.filter(
      (email) => !listEmailIgnore.includes(email)
    );

    try {
      const emailChunks = splitArray(emailToCheck, 7);
      const totalChunks = emailChunks.length;
      let allValidEmails = [];
      allValidEmails = allValidEmails.concat(listEmailFormDb);
      const invalidEmails = allValidEmails
        ?.filter((item) => item.status === 'Invalid')
        ?.map((it) => it.email);
      const invalidCount =
        candidates?.filter((contact) => invalidEmails?.includes(contact?.email))
          ?.length || 0;
      const dataUpload = { invalidEmails, invalidCount };
      if (invalidCount && +invalidCount > 0) {
        setHotListCheckInValidData(dataUpload);
        await handleCreateNewEmailValid(
          allValidEmails?.map((item) => ({
            email: item?.email,
            status: item?.status,
          }))
        );
        setOpenRemoveInValidEmail(true);
      }
      console.log('allValidEmails', allValidEmails);
    } catch (e) {
      console.log('error', e);
      setIsLoadingContacts(false);
      notification.error({ message: 'Error' });
    }
    setIsLoadingContacts(false);
    const total = [...shortListTemp]?.length;
    notification.success({
      description: `Added Shortlist successfull!`,
    });
  };

  const handleGetContacts = async (hotListId) => {
    const fields = [
      'id',
      'name',
      'email',
      'customText1',
      // 'status',
      'phone',
      'occupation',
      'address',
      'clientCorporation',
    ].join(',');
    let query = `tearsheets.id=${hotListId}`;

    let start = 0;
    let limit = 500;
    let paginationTotal = 500; // Default total for the first call
    const emailList = [];
    const fullDataEmail = [];
    try {
      // First API call
      const { data } = await searchCommonBulhorn(
        'ClientContact',
        query,
        fields,
        start,
        limit
      );

      const fetchedEmails = data?.result?.data?.map((item) => item.email);
      emailList.push(...fetchedEmails);
      fullDataEmail.push(...data?.result?.data);

      paginationTotal = data?.result?.total || data?.result?.count || 0;

      start += limit;

      for (let i = 1; i < Math.ceil(paginationTotal / limit); i++) {
        const { data } = await searchCommonBulhorn(
          'ClientContact',
          query,
          fields,
          start,
          limit
        );

        const fetchedEmails = data?.result?.data?.map((item) => item.email);
        emailList.push(...fetchedEmails);
        fullDataEmail.push(...data?.result?.data);

        start += limit;
      }

      return {
        emailList,
        fullDataEmail: fullDataEmail?.map((item) => ({ ...item, hotListId })),
      };
    } catch (e) {
      notification.error({
        message: 'Something went wrong',
      });
    }
  };

  const handleGetCandidates = async (hotListId) => {
    let start = 0;
    let limit = 500;
    const emailList = [];
    const fullDataEmail = [];
    let lastResult;
    do {
      try {
        // First API call
        const { data } = await searchBullhorn(
          'Candidate',
          start,

          limit,
          '',
          null,
          '',
          '',
          hotListId
        );

        lastResult = data || { result: [] };
        const fetchedEmails = data?.result?.map((item) => item?.email);
        emailList.push(...fetchedEmails);
        fullDataEmail.push(...data?.result);

        start += limit;
      } catch (e) {
        console.log('error: ', e);
        notification.error({
          message: 'Something went wrong',
        });
      }
    } while (lastResult?.result?.length > 0);

    return {
      emailList,
      fullDataEmail: fullDataEmail?.map((item) => ({ ...item, hotListId })),
    };
  };

  const handleCreateNewEmailValid = async (body) => {
    try {
      const { data } = await createNewEmailValid({ data: body });
    } catch (e) {
      notification.error({
        description: 'Network error! Try again later.',
      });
    }
  };

  const getHotList = async (hotList) => {
    const list = await Promise.all(
      hotList?.map((list) =>
        +list?.candidateCount > 0
          ? handleGetCandidates(list?.id)
          : handleGetContacts(list?.id)
      )
    );
    return list;
  };

  const submitHotList = async (hotList) => {
    contactSetOptions([]);
    setValue('participants.hotList', [...hotList]);
    setHotList([...hotList]);

    if (hotList?.length <= 0) return;
    setValidatingProcessLoading({
      ...validatingProcessLoading,
      hotList: true,
    });

    setValidatingLoading({
      ...validatingLoading,
      hotList: true,
    });
    // showHotListEmailsValidator();
    try {
      const hotListData = await getHotList(hotList);

      const emailList = hotListData?.flatMap((item) => item?.emailList);
      const fullDataEmails = hotListData?.flatMap(
        (item) => item?.fullDataEmail
      );

      const { data: dataEmailFromDb } = await validListEmail({
        emails: emailList,
      });
      const listEmailFormDb = dataEmailFromDb?.result?.map((item) => ({
        email: item?.email,
        status:
          (VALID_EMAIL_STATUS?.includes(item?.result) && 'Valid') || 'Invalid',
      }));

      const dataHotListWithStatus = fullDataEmails?.map((item) => ({
        ...item,
        status:
          listEmailFormDb.find(
            (email) =>
              email?.email?.toLowerCase() === item?.email?.toLowerCase()
          )?.status || 'Invalid',
      }));

      const newHotList = hotList?.map((item) => ({
        ...item,
        contacts: dataHotListWithStatus?.filter(
          (status) => status?.hotListId === item?.id
        ),
      }));

      setHotList([...newHotList]);
      setValue('participants.hotList', [...newHotList]);
      setValidatingProcessLoading({
        ...validatingProcessLoading,
        hotList: false,
      });

      // remove all invalid contacts
      const invalidContacts = newHotList
        ?.flatMap((list) => list?.contacts)
        ?.filter(
          (contact) =>
            !VALID_EMAIL_STATUS.includes(contact?.status) || !contact?.status
        );
      const invalidEmails = invalidContacts
        ?.map((item) => item?.email)
        ?.filter((email) => email);

      const currentSkippedEmails = getValues('skippedEmails') || [];
      setValue('skippedEmails', [...currentSkippedEmails, ...invalidEmails]);
    } catch (e) {
      console.log('error: ', e);
      setValidatingProcessLoading({
        ...validatingProcessLoading,
        hotList: false,
      });
      notification.error({ message: 'Error' });
    }
  };

  const submitCSVList = (csvList) => {
    setValue('participants.csvList', [...csvList]);
  };

  const handleContactsUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const workbook = XLSX.read(event.target.result, { type: 'binary' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const sheetData = XLSX.utils.sheet_to_json(sheet);
      console.log('sheetData: ', sheetData);
      setListCSVTemp([...sheetData]);
    };

    reader.readAsBinaryString(file);
  };

  const handleDownloadExampleFile = async () => {
    setUploadCSVLoading(true);
    const response = await getExampleFile();
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'example.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    setUploadCSVLoading(false);
  };

  const dragProps = useMemo(() => ({
    name: 'file',
    multiple: false,
    action: '',
    accept: '.xlsx, .csv',
    maxCount: 1,
    // action: '/api/upload',
    onChange(info) {
      const { status } = info?.file;

      if (status === 'removed') {
        setListCSVTemp([]);
        setFileList([]);
        return;
      }

      const isExcel =
        info?.file.type === 'text/csv' ||
        info?.file.type === 'text/slsx' ||
        info?.file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        info.file.type === 'application/vnd.ms-excel';

      if (!isExcel) {
        notification.error({
          description: 'You can only upload .xlsx or csv',
        });
        return;
      }
      const isLt5M = info?.file.size < 5242880;
      if (!isLt5M) {
        notification.error({
          description: 'Please upload image less than 5mb',
        });
        return;
      }

      setFileList([info?.file]);
      if (status !== 'uploading') {
        handleContactsUpload(info.file);
      }
    },
    showUploadList: {
      showRemoveIcon: true,
      removeIcon: <DeleteOutlined onClick={() => setListCSVTemp([])} />,
    },
  }));
  const handleLoadCSV = () => {
    const uniqueEmails = [
      ...new Set(sendToList.map((contact) => contact?.email)),
    ];
    const addedContacts = listCSVTemp
      .map((contact) => ({
        ...contact,
        id: uuid(),
        name: `${contact?.firstName} ${contact?.lastName}`,
        value: `${contact?.firstName} ${contact?.lastName}`,
        label: contact?.email,
        phone: contact?.phoneNumber,
      }))
      .filter((contact) => !uniqueEmails?.includes(contact?.email));
    if (addedContacts?.length === 0) {
      notification.warning({
        description: `This CSV file has been already inserted!`,
      });
      return;
    }
    const newContacts = [...addedContacts];
    setValue('sendMail.contactListSelected', [
      ...(getValues('sendMail.contactListSelected') || []),
      ...newContacts,
    ]);
    setValue('sendMail.listEmailSend', [
      ...(getValues('sendMail.listEmailSend') || []),
      ...newContacts,
    ]);

    setValue('sendMail.listEmail', [
      ...(getValues('sendMail.listEmail') || []),
      ...newContacts?.map((contact) => contact?.email),
    ]);
    setListContactSelected &&
      setListContactSelected([...(listContactSelected || []), ...newContacts]);
    setValue('participants.sendTo', [
      ...(getValues('participants.sendTo') || []),
      ...newContacts,
    ]);
    setSendToList([...sendToList, ...newContacts]);

    setListCSVTemp([]);
    setFileList([]);

    notification.success({
      description: `Added ${addedContacts?.length} contacts!`,
    });
  };

  const duplicateStep = (step) => {
    if (step?.type === ADD_STEP_TYPE.SEND_MAIL) {
      const lastStep = inputNumberStep[inputNumberStep?.length - 1];
      const isNotAllowDuplicate = lastStep?.type === ADD_STEP_TYPE.SEND_MAIL;
      if (isNotAllowDuplicate) {
        notification.warning({
          description: 'Must have a wait step first!',
        });
        return;
      }
    }
    const newId = uuid();
    const newStep = {
      ...step,
      id: newId,
      key: newId,
    };
    const newInputNumberStep = [...inputNumberStep, { ...newStep }];
    setInputNumberStep([...newInputNumberStep]);
    notification.success({
      description: 'Step duplicated!',
    });
  };

  const onChangeStepName = (inputNumber, newName) => {
    const name = newName;

    const newInputNumberStep = [...inputNumberStep];
    const updatingIndex = newInputNumberStep.findIndex(
      (item) => item?.key === inputNumber?.key
    );

    newInputNumberStep[updatingIndex] = {
      ...newInputNumberStep[updatingIndex],
      name,
    };

    setInputNumberStep([...newInputNumberStep]);
  };

  const handleSendTestMail = async () => {
    setLoadingTestEmail(true);
    try {
      const testingMail = {
        subject: mailObject.subject,
        content: mailObject.content,
        fileList: mailObject.fileList,
      };

      // Restyle for email
      let newContent = testingMail?.content;
      const temp = document.createElement('div');
      temp.innerHTML = testingMail?.content;
      const sigNode = temp.getElementsByClassName('signature-container');

      for (let i = 0; i < sigNode.length; i++) {
        const signEl = sigNode[i];
        const spanEl = document.createElement('span');
        spanEl.setAttribute('class', 'signature-place-holder');
        sigNode[i].replaceChild(spanEl, signEl.childNodes[0]);
      }
      newContent = temp.innerHTML;

      const reStylePTagsEl = document.createElement('div');
      reStylePTagsEl.innerHTML = newContent;
      const allPTagsEl = reStylePTagsEl.getElementsByTagName('p');
      for (let i = 0; i < allPTagsEl.length; i++) {
        allPTagsEl[i].style.margin = '0';
      }

      testingMail.content = reStylePTagsEl.innerHTML;

      const payload = {
        mail: {
          ...testingMail,
        },
        job: {
          ...job,
          title: job?.title || job?.jobtitle,
          company_name: job?.company_name || job?.company,
          address_country:
            job?.address?.countryName ||
            job?.address_country ||
            job?.joblocationcity,
          address_city: job?.address_city || job?.state || job?.address?.city,
          address_line_1: job?.address_line_1 || job?.joblocationcity,
          employment_type: job?.employment_type || job?.jobtype,
        },
        companyDetail: getValues('companyDetail'),
      };

      const { data } = await sendTestEmail(payload);

      if (data) {
        notification.success({
          message: 'Send test email success !!!',
        });
        setLoadingTestEmail(false);
      }
    } catch (e) {
      setLoadingTestEmail(false);
      notification.error({
        message: 'Error',
      });
    }
  };

  const scrollToEl = (elementId) =>
    document?.getElementById(elementId)?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });

  const processNextStep = () => {
    closeSendMailOptionsDrawer();
    const currentKey = selectingStep || mailObject?.key || editingNotes;
    const currentIndex = inputNumberStep?.findIndex((item) => {
      const key = item?.key || item?.id;
      return key === currentKey;
    });

    const nextStep = inputNumberStep[currentIndex + 1] || inputNumberStep[0];
    const nextStepType = nextStep?.type;

    addSavedSteps(currentKey);
    scrollToEl(nextStep?.key);

    if (currentIndex + 1 === inputNumberStep?.length) {
      notification.warning({
        description: "You're in last step!",
      });
      return;
    }

    switch (nextStepType) {
      case ADD_STEP_TYPE.SEND_MAIL:
        setMailObject({ ...nextStep });
        setSelectingStep(nextStep?.key);
        showMailEditorFromStep();
        showSendMailOptionsDrawer();
        break;

      default:
        setEditingNotes(nextStep?.key || nextStep?.id);
        setSelectingStep(nextStep?.key || nextStep?.id);
        break;
    }
  };

  return (
    <div className="max-h-[47rem] overflow-y-scroll min-w-[25rem] pr-2">
      {isLoading && (
        <div className="w-full h-full flex items-center justify-center">
          <Spin size="large" />
        </div>
      )}
      {(loadingSaveDraft || autoSavingLoading) && (
        <div className="absolute bottom-3 right-3 flex items-center gap-2 border rounded-md px-1 py-2 bg-cyan-50">
          <Spin size="small" />
          <div className="text-sm font-medium">saving...</div>
        </div>
      )}
      {autoSavingMessage && (
        <div className="absolute bottom-3 right-3 flex items-center gap-2 border rounded-md px-1 py-2 bg-green-50">
          {autoSavingMessage?.includes('Error') ? (
            <CloseCircleOutlined className="text-red-500" size={'small'} />
          ) : (
            <CheckOutlined size={'small'} className="text-green-700" />
          )}
          <div className="text-sm font-medium text-green-700">Saved.</div>
        </div>
      )}
      {!isLoading && (
        <>
          <div className="w-full flex flex-col items-center justify-center">
            {!onSubmitTriggers &&
              !fromCreateFromTemplateModal &&
              !selectedSequenceValue?.isMarkedAsCompleted && (
                <div className="w-full flex justify-between pb-2 px-3 sticky w-[25rem]">
                  <div>
                    {inputNumberStep?.length > 1 && (
                      <Tooltip title="Move to next step.">
                        <Button
                          onClick={processNextStep}
                          type="dashed"
                          icon={<RightSquareOutlined />}
                          disabled={loadingSignature}
                          className="bg-white"
                        >
                          Next
                        </Button>
                      </Tooltip>
                    )}
                  </div>
                  <Button
                    onClick={() => {
                      setUseSequenceTemplateMode(true);
                      handleOpenTemplateOptions();
                    }}
                    type="dashed"
                    icon={<OrderedListOutlined />}
                    disabled={loadingSignature}
                  >
                    Use Template
                  </Button>
                </div>
              )}
            <div className="flex w-full flex-col gap-0 items-center max-w-[25rem] self-center">
              <div className="w-full p-2 bg-white  rounded-md border border-[#ccc] shadow-md">
                <div
                  className={clsx(
                    'grid gap-1 grid-cols-10 items-center gap-2',
                    'mb-0'
                  )}
                >
                  <div className="flex gap-1 items-center col-span-10 items-center justify-center">
                    {!openEditNameModal && !fromChildSequence && (
                      <>
                        <span
                          className={clsx(
                            'text-sm font-medium line-clamp-1 max-w-lg',
                            fromCreateTemplate
                              ? '!text-xl font-semibold py-2'
                              : 'text-sm'
                          )}
                          title={getValues('sequenceData')?.name}
                        >
                          {!newUpdatedSequence
                            ? 'Trigger'
                            : getValues('sequenceData')?.name ||
                              (previewMode && 'Start') ||
                              'Name'}
                        </span>
                        {!fromCreateTemplate && (
                          <Tooltip title="Click to edit Sequence name.">
                            <Button
                              type="text"
                              className=" bg-[#fff] font-semibold hover:text-[#2684c7] text-[#5a5f7d]"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenEditNameModal(!openEditNameModal);
                              }}
                              icon={<EditOutlined className="text-cyan-600" />}
                            ></Button>
                          </Tooltip>
                        )}
                      </>
                    )}
                    {openEditNameModal && !fromChildSequence && (
                      <ModalEditNameSequence
                        getValues={getValues}
                        setValue={setValue}
                        setOpenEditNameModal={setOpenEditNameModal}
                        openEditNameModal={openEditNameModal}
                        isDependency={
                          selectedSequenceValue?.isMarkedAsCompleted
                        }
                        sequenceId={selectedSequenceValue?.id || emailSeqId}
                      />
                    )}
                  </div>
                  <div className="col-span-6 w-full h-full">
                    {!fromCreateTemplate && (
                      <Popover
                        rootClassName="trigger-container"
                        placement="bottom"
                        // title="Schedule Sequence."
                        content={
                          <SequenceScheduleDate
                            scheduleInformation={scheduleInformation}
                            setScheduleInformation={setScheduleInformation}
                            closeScheduleDate={closeScheduleDate}
                            setSequenceScheduled={setSequenceScheduled}
                            emailSeqId={
                              emailSeqId || selectedSequenceValue?.id || null
                            }
                          />
                        }
                        destroyTooltipOnHide
                        action="click"
                        open={openScheduleDate}
                        onOpenChange={(visible) => setOpenScheduleDate(visible)}
                      >
                        <Tooltip title="Schedule sequence start">
                          <Button
                            onClick={showScheduleDate}
                            className="!border-none flex gap-3 items-center bg-[#fff] font-semibold hover:text-[#2684c7] text-[#5a5f7d] w-full h-full justify-center"
                          >
                            <Image
                              width={'30px'}
                              src={ChronometerIcon}
                              preview={false}
                            />
                            <div className="px-2 py-1 rounded-md bg-[#2596be] text-white text-xs">
                              {scheduleInformation.isTriggeredNow
                                ? 'Immediately'
                                : dayjs(scheduleInformation?.triggerAt).format(
                                    'HH:mm - Do. MMM YY'
                                  )}
                            </div>
                          </Button>
                        </Tooltip>
                      </Popover>
                    )}
                  </div>
                  <div
                    className={clsx(
                      'flex flex-col items-center gap-2 justify-center pr-4',
                      fromCreateTemplate ? 'col-span-10' : 'col-span-4'
                    )}
                  >
                    <span className="text-base font-medium">
                      {!onSubmitTriggers &&
                        (!newUpdatedSequence ? (
                          'Send emails to Recipients'
                        ) : (
                          <div>
                            <Badge
                              count={
                                (arrayUniqueByKey(sendToList, 'email')
                                  ?.length || 0) +
                                (hotList?.length || 0) +
                                (contactList?.length || 0) +
                                (shortList?.length || 0)
                              }
                            >
                              <Button
                                type="dashed"
                                className="flex items-center font-medium text-xs"
                                onClick={() => {
                                  if (
                                    fromCreateByVacancy ||
                                    fromSync ||
                                    fromManualCreate
                                  ) {
                                    handleContactSearch(
                                      '',
                                      getValues('companyId'),
                                      10
                                    );
                                  }
                                  setOpenParticipantModal(
                                    !openParticipantModal
                                  );
                                  toggleParticipantBox();
                                  setParticipantActiveKey([]);
                                }}
                              >
                                <span>Participants</span>
                                {openParticipantModal ? (
                                  <DownOutlined />
                                ) : (
                                  <PlusOutlined />
                                )}
                              </Button>
                            </Badge>
                          </div>
                        ))}
                    </span>
                  </div>
                  <div className="col-span-10" id="expand-container">
                    <div
                      className={clsx(
                        'w-full',
                        isExpandParticipantBox && 'expanded collapsed'
                      )}
                      id="expand-contract"
                    >
                      {!fromCreateTemplate && (
                        <div className="col-span-10 pb-2">
                          <div>
                            <div className="flex flex-col gap-1">
                              {!fromCreateByVacancy &&
                                !fromSync &&
                                (contactList?.length > 0 ||
                                  hotList?.length > 0) && (
                                  <div className="grid grid-cols-2 gap-2 stop-rule-container">
                                    <div
                                      className={clsx(
                                        'px-2 py-1 rounded-md border flex items-center gap-2 cursor-pointer',
                                        stopRule === STOP_RULE.INDIVIDUALLY &&
                                          ' border-red-500 text-red-500'
                                      )}
                                    >
                                      <Checkbox
                                        value={STOP_RULE.INDIVIDUALLY}
                                        onChange={onStopRuleChange}
                                        checked={
                                          stopRule === STOP_RULE.INDIVIDUALLY
                                        }
                                      />
                                      <span>Stop For Individual</span>
                                    </div>
                                    <div
                                      className={clsx(
                                        'px-2 py-1 rounded-md border flex items-center gap-2 cursor-pointer',
                                        stopRule === STOP_RULE.COMPANY &&
                                          ' border-red-500 text-red-500'
                                      )}
                                    >
                                      <Checkbox
                                        value={STOP_RULE.COMPANY}
                                        onChange={onStopRuleChange}
                                        checked={stopRule === STOP_RULE.COMPANY}
                                      />
                                      <span>Stop For Company</span>
                                    </div>
                                  </div>
                                )}
                              <div className="flex items-center w-full mt-2">
                                <p>
                                  <span className="text-red-600">*</span> Send
                                  To ( email )
                                </p>
                                {!fromSequenceAI &&
                                  !fromCreateFromTemplateModal &&
                                  !fromCreatingScratchSequence && (
                                    <div
                                      style={{ marginLeft: '10px' }}
                                      className="flex items-center gap-2"
                                    >
                                      {isSelectAll && (
                                        <Tooltip
                                          title={
                                            !getValues('companyId')
                                              ? 'Please select a company first.'
                                              : 'Select all contacts of the company.'
                                          }
                                        >
                                          <Button
                                            loading={isLoadingContacts}
                                            icon={<UsergroupAddOutlined />}
                                            onClick={async () => {
                                              // showSendToEmailsValidator();
                                              await handleContactSearch(
                                                '',
                                                getValues('companyId'),
                                                1000,
                                                handleChange
                                              );

                                              setValidatingLoading({
                                                ...validatingLoading,
                                                sendToList: true,
                                              });

                                              setSelectAll(false);
                                            }}
                                            disabled={!getValues('companyId')}
                                            size="small"
                                          >
                                            Select all
                                          </Button>
                                        </Tooltip>
                                      )}

                                      {!isSelectAll && (
                                        <Tooltip
                                          title={'Unselect all contacts'}
                                        >
                                          <Button
                                            size="small"
                                            disabled={
                                              isLoadingContacts ||
                                              validatingLoading.sendToList
                                            }
                                            icon={<UsergroupDeleteOutlined />}
                                            onClick={async () => {
                                              handleChange(null, []);
                                              setSelectAll(true);
                                            }}
                                          >
                                            Unselect all
                                          </Button>
                                        </Tooltip>
                                      )}
                                    </div>
                                  )}
                                <Tooltip title={'Enrich data of contact(s).'}>
                                  <Tag
                                    onClick={() => {
                                      if (isLoadingContacts) {
                                        return;
                                      }
                                      if (sendToList?.length < 1) {
                                        notification.warning({
                                          description:
                                            'Please select at least one contact.',
                                        });
                                        return;
                                      }
                                      showBulkEnrichModal();
                                    }}
                                    className="ml-2"
                                    style={{ cursor: 'pointer' }}
                                    icon={
                                      <SyncOutlined spin={isLoadingContacts} />
                                    }
                                    color="#55acee"
                                  >
                                    Enrich
                                  </Tag>
                                </Tooltip>
                              </div>
                            </div>
                            <Form.Item
                              className="add-contact-container !my-2"
                              name="sendMail.listEmail"
                            >
                              <Controller
                                render={({ field }) => (
                                  <div>
                                    <Select
                                      // disabled={isLoadingContacts}
                                      mode="multiple"
                                      placeholder="Please select"
                                      onChange={handleChange}
                                      onPopupScroll={(e) => {
                                        if (isLoadingContacts) {
                                          return;
                                        } else {
                                          handleContactScroll(
                                            e,
                                            'ClientContact',
                                            getValues('companyId') ?? ''
                                          );
                                        }
                                      }}
                                      notFoundContent={
                                        isLoadingContacts ? (
                                          <div className="w-full flex justify-center py-4">
                                            <Spin size="default" />
                                          </div>
                                        ) : null
                                      }
                                      // loading={isLoadingContacts}
                                      options={arrayUniqueByKey(
                                        [
                                          ...contactOptions,
                                          ...(sendToList || []),
                                        ]
                                          // ?.concat(
                                          //   getValues(
                                          //     'sendMail.listEmailSend'
                                          //   ) ?? []
                                          // )
                                          ?.map((option) => {
                                            return (
                                              (option?.id || option?.email) && {
                                                ...option,
                                                value:
                                                  (option?.id || option.email) +
                                                  '_' +
                                                  option.name,
                                                label:
                                                  option?.name || option?.email,
                                                email: option?.email,
                                                phone: option?.phone,
                                                address: option?.address,
                                                status: option?.status,
                                                occupation: option?.occupation,
                                                name: option?.name,
                                                contactId: option?.id,
                                              }
                                            );
                                          }) || [],
                                        'email'
                                      )
                                        .filter(
                                          (item, index, self) =>
                                            item?.value &&
                                            index ===
                                              self?.findIndex(
                                                (t) =>
                                                  t?.email + '_' + t?.name ===
                                                  item?.email + '_' + item?.name
                                              )
                                        )
                                        .concat([
                                          ...hotList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id || list}/HOTLIST`,
                                          })),
                                          ...contactList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id || list}/CONTACTLIST`,
                                          })),
                                          ...shortList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id || list}/SHORTLIST`,
                                          })),
                                        ])}
                                      value={arrayUniqueByKey(
                                        [
                                          ...contactOptions,
                                          ...(sendToList || []),
                                        ]
                                          // ?.concat(
                                          //   getValues(
                                          //     'sendMail.listEmailSend'
                                          //   ) ?? []
                                          // )
                                          ?.map((option) => {
                                            return (
                                              (option?.id || option?.email) && {
                                                ...option,
                                                value:
                                                  (option?.id || option.email) +
                                                  '_' +
                                                  option.name,
                                                label:
                                                  option?.name || option?.email,
                                                email: option?.email,
                                                phone: option?.phone,
                                                address: option?.address,
                                                status: option?.status,
                                                occupation: option?.occupation,
                                                name: option?.name,
                                                contactId: option?.id,
                                              }
                                            );
                                          })
                                          .filter(
                                            (option) =>
                                              option?.email &&
                                              option?.value &&
                                              getValues(
                                                'sendMail.listEmail'
                                              )?.includes(option?.email)
                                          ) || [],
                                        'email'
                                      )
                                        .filter((item, index, self) =>
                                          !item?.value?.includes(
                                            'CONTACTLIST'
                                          ) &&
                                          !item?.value?.includes('HOTLIST') &&
                                          !item?.value?.includes('SHORTLIST')
                                            ? index ===
                                              self.findIndex(
                                                (t) => t.email === item.email
                                              )
                                            : true
                                        )
                                        .concat([
                                          ...hotList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id || list}/HOTLIST`,
                                          })),
                                          ...contactList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id}/CONTACTLIST`,
                                          })),
                                          ...shortList.map((list) => ({
                                            label: list?.name,
                                            value: `${list?.id}/SHORTLIST`,
                                          })),
                                        ])}
                                      optionLabelProp="name"
                                      tagRender={(props) => {
                                        const {
                                          label,
                                          closable,
                                          onClose,
                                          value,
                                        } = props;
                                        const options = (
                                          defaultUserSelected?.filter(
                                            (item) =>
                                              item?.email !== 'notChoseEmail'
                                          ) ?? []
                                        )
                                          ?.concat([...contactOptions])
                                          ?.concat(
                                            getValues(
                                              'sendMail.listEmailSend'
                                            ) || []
                                          );
                                        const optionSelect = options.find(
                                          (item) =>
                                            (item?.id || item?.email) +
                                              '_' +
                                              item?.name ===
                                            value
                                        );
                                        const isInList =
                                          value?.includes('CONTACTLIST') ||
                                          value?.includes('HOTLIST') ||
                                          value?.includes('SHORTLIST');

                                        const isLoading =
                                          (value?.includes('CONTACTLIST') &&
                                            validatingLoading?.contactList) ||
                                          (value?.includes('HOTLIST') &&
                                            validatingLoading?.hotList) ||
                                          (!isInList &&
                                            validatingLoading?.sendToList);
                                        return (
                                          <Tag
                                            className="mb-1 max-w-[20rem]"
                                            closable={!isLoading}
                                            onClose={() => {
                                              if (isInList) {
                                                const newContactList =
                                                  contactList?.filter(
                                                    (item) =>
                                                      !value?.includes(item?.id)
                                                  );
                                                setContactList([
                                                  ...newContactList,
                                                ]);
                                                const newHotList =
                                                  hotList?.filter(
                                                    (item) =>
                                                      !value?.includes(item?.id)
                                                  );
                                                setHotList([...newHotList]);

                                                const newShortList =
                                                  shortList?.filter(
                                                    (item) =>
                                                      !value?.includes(item?.id)
                                                  );
                                                setShortList([...newShortList]);
                                              }
                                              onClose();
                                            }}
                                            style={{ marginRight: 3 }}
                                            color={
                                              typeof value === 'number'
                                                ? '#f50'
                                                : value?.includes('CONTACTLIST')
                                                  ? 'cyan'
                                                  : value?.includes('HOTLIST')
                                                    ? 'orange'
                                                    : value?.includes(
                                                          'SHORTLIST'
                                                        )
                                                      ? 'yellow'
                                                      : optionSelect?.emailStatus
                                                        ? VALIDATE_STATUS_COLOR[
                                                            optionSelect
                                                              ?.emailStatus
                                                          ]
                                                        : undefined
                                            }
                                            title={label || value}
                                          >
                                            {!isInList && (
                                              <span className="mr-2 bg-blue-500 rounded-full w-2 h-2 inline-block"></span>
                                            )}
                                            {label || value}
                                          </Tag>
                                        );
                                      }}
                                      filterOption={false}
                                      onSearch={async (searchText) => {
                                        if (timeoutRef.current) {
                                          clearTimeout(timeoutRef.current);
                                        }
                                        timeoutRef.current = setTimeout(
                                          async () => {
                                            await handleContactSearch(
                                              searchText,
                                              getValues('companyId')
                                            );
                                          },
                                          500
                                        );
                                      }}
                                      // loading={loadingSearch}
                                      optionRender={(opt) => {
                                        const { data: option } = opt;
                                        const optionFilter = (
                                          defaultUserSelected?.filter(
                                            (item) =>
                                              item?.email !== 'notChoseEmail'
                                          ) ?? []
                                        )
                                          .concat(
                                            getValues(
                                              'sendMail.listEmailSend'
                                            ) || []
                                          )
                                          ?.concat([
                                            ...contactOptions?.filter(
                                              (item) =>
                                                !defaultUserSelected
                                                  ?.map((itc) => itc.email)
                                                  .includes(item.email)
                                            ),
                                          ]);
                                        const lastOption =
                                          [...optionFilter][
                                            optionFilter?.length - 1
                                          ] || {};
                                        const isLastOption =
                                          lastOption &&
                                          lastOption?.id === option?.id;
                                        return (
                                          <div>
                                            <div className="grid">
                                              <div className="flex items-center gap-1">
                                                <span className="text-base">
                                                  {option.name || option?.label}
                                                </span>
                                                {option?.emailStatus && (
                                                  <Tag
                                                    icon={
                                                      VALIDATE_STATUS_ICON[
                                                        option?.emailStatus
                                                      ]
                                                    }
                                                    color={
                                                      VALIDATE_STATUS_COLOR[
                                                        option?.emailStatus
                                                      ]
                                                    }
                                                  >
                                                    {option?.emailStatus}
                                                  </Tag>
                                                )}
                                              </div>
                                              <div className="contact-details">
                                                {option?.email && (
                                                  <div className="flex">
                                                    <span className="text-gray-700 text-xs min-w-[200px]  flex gap-1 items-center">
                                                      <MailOutlined />
                                                      {option.email
                                                        ? option.email
                                                        : '-'}
                                                    </span>
                                                    <span className="text-gray-700 text-xs min-w-[200px] font-medium flex gap-1 items-center">
                                                      <HomeOutlined />
                                                      {
                                                        option.clientCorporation
                                                          ?.name
                                                      }
                                                    </span>
                                                  </div>
                                                )}
                                                {option?.occupation && (
                                                  <div className="flex">
                                                    <span className="text-gray-700 text-xs font-medium flex gap-1 items-center">
                                                      <ContactsOutlined />{' '}
                                                      {option.occupation
                                                        ? option.occupation
                                                        : '-'}
                                                    </span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            {isLastOption &&
                                              isLoadingContacts && (
                                                <div className="w-full flex justify-center py-4">
                                                  <Spin size="default" />
                                                </div>
                                              )}
                                          </div>
                                        );
                                      }}
                                    />
                                  </div>
                                )}
                                name="sendMail.listEmail"
                                control={control}
                              />
                            </Form.Item>
                            {validatingLoading?.contactList && (
                              <ValidatingEmailProcess
                                skippedEmails={watch('skippedEmails')}
                                loading={validatingProcessLoading.contactList}
                                rawList={contactList}
                                emails={contactList?.flatMap(
                                  (list) => list?.contacts
                                )}
                                revert={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) {
                                    notification.warning({
                                      description:
                                        'Can not revert contact without email',
                                    });
                                    return;
                                  }
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  const newSkippedEmails =
                                    currentSkippedEmails.filter(
                                      (email) => email !== invalidContact?.email
                                    );
                                  setValue('skippedEmails', [
                                    ...newSkippedEmails,
                                  ]);
                                }}
                                remove={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) return;
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  setValue('skippedEmails', [
                                    ...currentSkippedEmails,
                                    invalidContact?.email,
                                  ]);
                                }}
                                closeModal={(
                                  closeType = CLOSE_TYPES.CLOSE_AND_SAVE
                                ) => {
                                  if (
                                    closeType === CLOSE_TYPES.CLOSE_AND_SAVE
                                  ) {
                                    setValidatingLoading({
                                      ...validatingLoading,
                                      contactList: false,
                                    });
                                  }
                                  closeEmailsValidator();
                                }}
                              />
                            )}
                            {validatingLoading?.hotList && (
                              <ValidatingEmailProcess
                                remove={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) return;
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  setValue('skippedEmails', [
                                    ...currentSkippedEmails,
                                    invalidContact?.email,
                                  ]);
                                }}
                                revert={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) {
                                    notification.warning({
                                      description:
                                        'Can not revert contact without email',
                                    });
                                    return;
                                  }
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  const newSkippedEmails =
                                    currentSkippedEmails.filter(
                                      (email) =>
                                        email?.toLowerCase() !==
                                        invalidContact?.email?.toLowerCase()
                                    );
                                  setValue('skippedEmails', [
                                    ...newSkippedEmails,
                                  ]);
                                }}
                                skippedEmails={watch('skippedEmails')}
                                loading={validatingProcessLoading.hotList}
                                rawList={hotList}
                                emails={hotList?.flatMap(
                                  (list) => list?.contacts
                                )}
                                type={VALIDATE_TYPES.HOT_LIST}
                                closeModal={(
                                  closeType = CLOSE_TYPES.CLOSE_AND_SAVE
                                ) => {
                                  if (
                                    closeType === CLOSE_TYPES.CLOSE_AND_SAVE
                                  ) {
                                    setValidatingLoading({
                                      ...validatingLoading,
                                      hotList: false,
                                    });
                                  }
                                  closeHotListEmailsValidator();
                                }}
                              />
                            )}
                            {validatingLoading?.sendToList && (
                              <ValidatingEmailProcess
                                revert={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) {
                                    notification.warning({
                                      description:
                                        'Can not revert contact without email',
                                    });
                                    return;
                                  }
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  const newSkippedEmails =
                                    currentSkippedEmails.filter(
                                      (email) => email !== invalidContact?.email
                                    );
                                  setValue('skippedEmails', [
                                    ...newSkippedEmails,
                                  ]);

                                  // Mark contact's email to be valid
                                  const newSendtoList = sendToList?.map(
                                    (item) => ({
                                      ...item,
                                      emailStatus:
                                        item?.id === invalidContact?.id
                                          ? 'Valid'
                                          : item?.emailStatus,
                                    })
                                  );
                                  setSendToList(newSendtoList);

                                  const newContactOptions = contactOptions?.map(
                                    (item) => ({
                                      ...item,
                                      emailStatus:
                                        item?.id === invalidContact?.id
                                          ? 'Valid'
                                          : item?.emailStatus,
                                    })
                                  );
                                  contactSetOptions(newContactOptions);
                                }}
                                remove={(invalidContact) => {
                                  if (!invalidContact?.email?.trim()) return;
                                  const currentSkippedEmails =
                                    getValues('skippedEmails') || [];
                                  setValue('skippedEmails', [
                                    ...currentSkippedEmails,
                                    invalidContact?.email,
                                  ]);
                                }}
                                skippedEmails={watch('skippedEmails')}
                                loading={isLoadingContacts}
                                rawList={sendToList}
                                emails={contactOptions}
                                type={VALIDATE_TYPES.SELECT_ALL}
                                closeModal={(
                                  closeType = CLOSE_TYPES.CLOSE_AND_SAVE
                                ) => {
                                  if (
                                    closeType === CLOSE_TYPES.CLOSE_AND_SAVE
                                  ) {
                                    setValidatingLoading({
                                      ...validatingLoading,
                                      sendToList: false,
                                    });
                                  }
                                  closeSendToEmailsValidator();
                                }}
                              />
                            )}
                            <div className="flex justify-center flex-col items-center">
                              <Button
                                disabled={
                                  sendToList?.length === 0 &&
                                  contactList?.length === 0 &&
                                  hotList?.length === 0 &&
                                  shortList?.length === 0
                                }
                                title="Select participant(s)"
                                className="w-fit mt-2"
                                type="primary"
                                onClick={handleSaveContact}
                              >
                                Save
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                      <Collapse
                        onChange={(key) => setParticipantActiveKey(key)}
                        activeKey={participantActiveKey}
                        accordion
                        className="!text-xs"
                        items={[
                          (fromCandidateSequence || fromSequenseDetail) &&
                            !isStandardUser && {
                              key: 'shortlist',
                              label: (
                                <span className="text-sm font-medium">
                                  {`Shortlist (${shortList?.length || 0})`}
                                </span>
                              ),
                              extra: <ContactsOutlined />,
                              children: (
                                <ShortListModal
                                  submitList={submitShortList}
                                  shortList={shortList}
                                  setParticipantActiveKey={
                                    setParticipantActiveKey
                                  }
                                />
                              ),
                            },
                          !fromCandidateSequence && {
                            key: 'contact-list',
                            label: (
                              <span className="text-sm font-medium">
                                {`Contact List (${contactList?.length || 0})`}
                              </span>
                            ),
                            extra: <ContactsOutlined />,
                            children: (
                              <ContactListModal
                                submitList={submitContactList}
                                contactList={contactList}
                                setParticipantActiveKey={
                                  setParticipantActiveKey
                                }
                                disabled={validatingLoading?.contactList}
                              />
                            ),
                          },
                          ...(isStandardUser
                            ? []
                            : [
                                {
                                  key: 'hot-list',
                                  label: (
                                    <span className="text-sm font-medium">
                                      {`Hot List (${hotList?.length || 0})`}
                                    </span>
                                  ),
                                  extra: <FireOutlined />,
                                  children: (
                                    <HotListModal
                                      disabled={validatingLoading?.hotList}
                                      submitList={submitHotList}
                                      hotList={hotList}
                                      setParticipantActiveKey={
                                        setParticipantActiveKey
                                      }
                                    />
                                  ),
                                },
                              ]),
                          {
                            key: 'csv',
                            label: (
                              <span className="text-sm font-medium">
                                {`Insert CSV ${getLength(getValues('participants.csv')) ? `(${getLength(getValues('participants.csv'))})` : ''}`}
                              </span>
                            ),
                            extra: <FileExcelOutlined />,
                            children: (
                              <div>
                                <div className="flex w-full justify-start pb-2 gap-2">
                                  <a onClick={handleDownloadExampleFile}>
                                    CSV template
                                  </a>
                                  {uploadCSVLoading && <Spin size="small" />}
                                </div>
                                <div>
                                  <Dragger
                                    disabled={uploadCSVLoading}
                                    fileList={fileList}
                                    {...dragProps}
                                    beforeUpload={() => false}
                                  >
                                    <p className="text-3xl">
                                      <InboxOutlined />
                                    </p>
                                    <p className="text-sm">
                                      Click or Drag your contacts file.
                                    </p>
                                  </Dragger>
                                </div>
                                {/* {fileList.length > 0 && ( */}
                                <div className="pt-2">
                                  <Button
                                    onClick={handleLoadCSV}
                                    disabled={
                                      !fileList.length > 0 || uploadCSVLoading
                                    }
                                    type="primary"
                                  >
                                    {listCSVTemp?.length > 0
                                      ? `Insert ${listCSVTemp?.length} contacts.`
                                      : 'Load'}
                                  </Button>
                                </div>
                                {/* )} */}
                              </div>
                            ),
                          },
                        ].filter(
                          (item) =>
                            !isEmpty(item) &&
                            (fromCreateByVacancy || fromSync
                              ? item?.key !== 'csv' &&
                                item?.key !== 'hot-list' &&
                                item?.key !== 'contact-list'
                              : true)
                        )}
                        ghost
                      />
                    </div>
                  </div>
                </div>
              </div>
              {inputNumberStep?.length === 0 && (
                <div className="flex justify-center relative min-h-[4rem] items-center line-container py-4 ">
                  <div
                    className={clsx(
                      'border border-[#ccc] absolute border-dashed z-1 top-0 h-1/2'
                    )}
                  />
                  {!isOpenSequenceOptions?.first && (
                    <Button
                      disabled={
                        (preview && previewMode === PREVIEW_MODE.VIEW) ||
                        openSendMailOptions ||
                        editingNotes
                      }
                      title={
                        openSendMailOptions &&
                        'Please save the current step before adding new.'
                      }
                      onClick={() => {
                        const isAllowToSequence =
                          userToSet && isGrantedSequence();

                        if (!isAllowToSequence) {
                          setOpenWarningModal(true);
                        } else {
                          setOpenSequenceOptions({
                            ...isOpenSequenceOptions,
                            first: !isOpenSequenceOptions?.first,
                          });
                        }
                      }}
                      type="text"
                      shape="circle"
                      className={clsx('bg-white')}
                      icon={
                        <PlusCircleOutlined
                          className="text-[#7CF5FF]"
                          style={{ fontSize: '20px' }}
                        />
                      }
                    ></Button>
                  )}
                  {isOpenSequenceOptions?.first && (
                    <div className="flex flex-col items-center bg-white rounded-md px-3 pb-3 border shadow-md z-10 animated fadeInDownBig border-[#ccc] add-new-step-container">
                      <Button
                        onClick={() =>
                          setOpenSequenceOptions({
                            ...isOpenSequenceOptions,
                            first: !isOpenSequenceOptions?.first,
                          })
                        }
                        type="text"
                        shape="circle"
                        icon={
                          <CloseCircleOutlined
                            style={{ fontSize: '20px', color: 'red' }}
                          />
                        }
                      />
                      <div className="relative mb-3 font-medium">
                        <span>Add New Step</span>
                      </div>
                      <div className="grid grid-cols-2 gap-5 relative">
                        <div className="col-span-1 flex flex-col gap-3">
                          <div className="flex justify-start">
                            <Button
                              onClick={() =>
                                handleAddNewStep(0, ADD_STEP_TYPE.ADD_WAIT)
                              }
                              icon={<ClockCircleOutlined />}
                              className="w-40 add-new-step-button"
                            >
                              Add Wait
                            </Button>
                          </div>
                          <div className="flex justify-start">
                            <Button
                              disabled={openSendMailOptions}
                              onClick={() => {
                                setMailObject({
                                  ...initialMailObject,
                                  step: 0,
                                });
                                setOpenMailEditor(false);
                                showSendMailOptionsDrawer();
                                setOpenSequenceOptions({
                                  ...isOpenSequenceOptions,
                                  first: !isOpenSequenceOptions?.first,
                                });
                              }}
                              icon={<MailOutlined />}
                              className="w-40 add-new-step-button"
                            >
                              Send Mail
                            </Button>
                          </div>

                          <div className="flex justify-start">
                            <Button
                              onClick={() =>
                                handleAddNewStep(0, ADD_STEP_TYPE.TASK)
                              }
                              icon={<AppstoreAddOutlined />}
                              className="w-40 add-new-step-button"
                            >
                              Add Task
                            </Button>
                          </div>
                        </div>
                        <div className="col-span-1 flex flex-col gap-3">
                          <div className="flex justify-center">
                            {isLinkedInStepBlocked ? (
                              <Popconfirm
                                rootClassName="customize-tooltip-widget"
                                placement="top"
                                title="Warning"
                                description={
                                  <div>
                                    Please click{' '}
                                    <a
                                      href="/settings?tab=linkedin"
                                      className="text-navy-300 font-medium"
                                    >
                                      here
                                    </a>{' '}
                                    to link your LinkedIn account before using
                                    this feature
                                  </div>
                                }
                              >
                                <Button
                                  className="w-40 add-new-step-button"
                                  style={{
                                    color: '#********',
                                    background: '#0000000a',
                                  }}
                                  icon={
                                    <LinkedinOutlined className="text-[#0288d1]" />
                                  }
                                >
                                  LinkedIn
                                </Button>
                              </Popconfirm>
                            ) : (
                              <AddLinkedinTypeButton
                                handleAddNewStep={handleAddNewStep}
                                index={'first'}
                              />
                            )}
                          </div>
                          <div className="flex justify-start">
                            <Button
                              className="w-40 add-new-step-button"
                              onClick={() => {
                                handleAddNewStep(0, ADD_STEP_TYPE.ADD_NOTE);
                              }}
                              icon={<ForkOutlined />}
                            >
                              Add Note
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="w-full">
                {inputNumberStep?.map((inputNumber, index) => (
                  <div
                    id={inputNumber?.key}
                    className="w-full flex flex-col gap-0 items-center relative"
                  >
                    {index === 0 && (
                      <div className="h-[1.5rem] relative">
                        <div
                          className={clsx(
                            'border absolute border-dashed z-1 top-0 h-full',
                            STEP_STATUS_LINE[inputNumber?.status] ||
                              STEP_STATUS_LINE[
                                inputNumberStep?.[index + 1]?.status
                              ] ||
                              'border-[#ccc]'
                          )}
                        />
                      </div>
                    )}
                    {inputNumber?.type === ADD_STEP_TYPE.SEND_MAIL && (
                      <div className="w-full">
                        <div
                          style={{
                            width: '100%',
                            position: 'relative',
                          }}
                        >
                          <div
                            className="rounded-md"
                            onClick={(e) => {
                              const selectedMailObject = {
                                ...inputNumber,
                                subject: inputNumber?.subject,
                                content: inputNumber?.content || '',
                                key: inputNumber?.key,
                              };
                              if (
                                preview &&
                                previewMode === PREVIEW_MODE.VIEW
                              ) {
                                if (loadingSignature) {
                                  notification.warning({
                                    description:
                                      'Signature is loading! Please try again!',
                                  });
                                  return;
                                }
                                const contentWithSig =
                                  replaceSignaturePlaceholder(
                                    inputNumber?.content,
                                    dataSignature
                                  );
                                // Migratin' the old data
                                setPreviewEmail({
                                  ...mailObject,
                                  ...selectedMailObject,
                                  content: contentWithSig,
                                });
                              } else {
                                setMailObject({
                                  ...mailObject,
                                  ...selectedMailObject,
                                });
                                setSelectingStep(selectedMailObject?.key);
                                showMailEditorFromStep();
                                showSendMailOptionsDrawer();
                              }
                            }}
                          >
                            <div
                              className={clsx(
                                `ribbon cursor-pointer hover:shadow-lg hover:shadow-[#92C7CF] hover:rounded-md`
                              )}
                            >
                              {inputNumber?.status &&
                                !fromCreateTemplate &&
                                !fromCreateFromTemplateModal && (
                                  <span
                                    class={clsx(
                                      'ribbon-base',
                                      inputNumber?.status &&
                                        `${STEP_STATUS_CLASS[inputNumber?.status]}`
                                    )}
                                  >
                                    <span>
                                      {STEP_STATUS_TEXT[inputNumber?.status]}
                                    </span>
                                  </span>
                                )}
                              <div
                                className={clsx(
                                  'relative flex justify-between items-center px-2 py-1 rounded-tl-md rounded-tr-md bg-white',
                                  mailObject?.key === inputNumber?.key ||
                                    selectingStep === inputNumber?.key
                                    ? 'border-b border-t-2 border-l-2 border-r-2 border-t-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF]'
                                    : 'border border-t-[#ccc] border-l-[#ccc] border-r-[#ccc]'
                                )}
                              >
                                <div className="flex gap-3 items-center justify-center">
                                  <MailFilled
                                    className={'text-[#2596be] text-lg'}
                                  />
                                  {editingStepName !== inputNumber?.key && (
                                    <div className="flex items-center gap-1">
                                      <span
                                        className="font-semibold text-sm"
                                        title={
                                          inputNumber?.name ||
                                          `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                        }
                                      >
                                        {inputNumber?.name ||
                                          `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`}
                                      </span>
                                      <Tooltip
                                        title={`Click to edit Step\'s name.`}
                                      >
                                        <Button
                                          type="text"
                                          icon={
                                            <EditOutlined className="text-cyan-600" />
                                          }
                                          onClick={(e) => {
                                            e?.stopPropagation();
                                            setEditingStepName(
                                              inputNumber?.key
                                            );
                                          }}
                                        ></Button>
                                      </Tooltip>
                                    </div>
                                  )}
                                  {editingStepName === inputNumber?.key && (
                                    <div className="flex items-center">
                                      <Input
                                        defaultValue={
                                          inputNumber?.name ||
                                          `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                        }
                                        value={inputNumber?.name}
                                        className="!border-t-0 !border-r-0 !border-l-0 max-w-[10rem]"
                                        maxLength={20}
                                        showCount
                                        onClick={(e) => e.stopPropagation()}
                                        onChange={(evt) =>
                                          onChangeStepName(
                                            inputNumber,
                                            evt.target.value
                                          )
                                        }
                                      />
                                      <Button
                                        type="text"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setEditingStepName('');
                                        }}
                                        icon={<CheckOutlined />}
                                      />
                                    </div>
                                  )}
                                </div>
                                <div className="flex gap-2">
                                  {(!fromCreatingScratchSequence ||
                                    fromCreatingScratchSequence ||
                                    fromCreateFromTemplateModal) &&
                                    (!preview ||
                                      previewMode === PREVIEW_MODE.EDIT) && (
                                      <Dropdown
                                        menu={{
                                          items: [
                                            {
                                              key: 'delete-email',
                                              label: <span>Delete Step</span>,
                                              icon: <DeleteOutlined />,
                                              onClick: () => {
                                                const newArr =
                                                  inputNumberStep.filter(
                                                    (item, itemIndex) =>
                                                      itemIndex !== index
                                                  );
                                                setInputNumberStep([...newArr]);
                                                setOpenSequenceOptions({
                                                  first: false,
                                                });
                                                setEditingNotes('');
                                              },
                                            },
                                            {
                                              key: 'duplicate-email',
                                              label: (
                                                <span>Duplicate Step</span>
                                              ),
                                              icon: <CopyOutlined />,
                                              onClick: (e) => {
                                                e?.domEvent?.stopPropagation();
                                                duplicateStep(inputNumber);
                                              },
                                            },
                                            // {
                                            //   key: 'edit-step-name',
                                            //   label: (
                                            //     <span>Edit Step Name</span>
                                            //   ),
                                            //   icon: <EditOutlined />,
                                            //   onClick: (e) => {
                                            //     e?.domEvent?.stopPropagation();
                                            //     setEditingNotes(
                                            //       inputNumber?.key
                                            //     );
                                            //   },
                                            // },
                                          ],
                                        }}
                                        arrow
                                        placement="bottomCenter"
                                        trigger={'click'}
                                      >
                                        <Button
                                          type="text"
                                          icon={
                                            <EllipsisOutlined className="text-xl" />
                                          }
                                          shape="circle"
                                          size="small"
                                          onClick={(event) => {
                                            event.preventDefault();
                                            event.stopPropagation();
                                          }}
                                        />
                                      </Dropdown>
                                    )}
                                </div>
                              </div>

                              <div
                                className={clsx(
                                  'bg-white p-5 rounded-bl-lg rounded-br-lg grid grid-cols-5 gap-1 shadow-sm flex items-center',
                                  mailObject?.key === inputNumber?.key ||
                                    selectingStep === inputNumber?.key
                                    ? 'border-2 border-t-0 border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
                                    : 'border border-[#ccc]'
                                )}
                              >
                                {(savedSteps?.includes(inputNumber?.key) ||
                                  savedSteps?.includes(inputNumber?.id)) &&
                                  editingNotes !== inputNumber?.key &&
                                  editingStepName !== inputNumber?.key &&
                                  mailObject?.key !== inputNumber?.key && (
                                    <div
                                      className="absolute bottom-1 right-2"
                                      title={
                                        getInvalidMessage(
                                          `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                        ) || 'Email saved.'
                                      }
                                    >
                                      {getInvalidMessage(
                                        `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                      ) ? (
                                        <CloseOutlined className="text-red-600" />
                                      ) : (
                                        <CheckOutlined className="text-green-600" />
                                      )}
                                    </div>
                                  )}
                                <div className="col-span-5 line-clamp-1 text-[#2596be] font-semibold">
                                  {(inputNumberStep[index]?.subject?.includes(
                                    'RE:'
                                  ) &&
                                    'Threading Email') ||
                                    (index === 0 && 'First Email') ||
                                    'Following Email'}
                                </div>
                                <div
                                  className="col-span-5 line-clamp-1 italic max-w-xs"
                                  title={inputNumberStep[index]?.subject}
                                >
                                  {inputNumberStep[index]?.subject}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {_.has(inputNumber, 'delay') && (
                      <>
                        <div
                          className={clsx(
                            'w-full relative',
                            editingNotes !== inputNumber?.key &&
                              'cursor-pointer hover:shadow-lg hover:shadow-[#92C7CF] hover:rounded-md'
                          )}
                          onClick={() => {
                            if (
                              editingNotes &&
                              editingNotes === inputNumber?.key
                            )
                              return;

                            setEditingNotes(
                              inputNumber?.key || inputNumber?.id
                            );
                            setSelectingStep(
                              inputNumber?.key || inputNumber?.id
                            );
                          }}
                        >
                          <div
                            className={clsx(
                              'flex gap-4 items-center bg-white px-2 py-1 rounded-tl-md rounded-tr-md justify-between',
                              editingNotes === inputNumber?.key ||
                                selectingStep === inputNumber?.key
                                ? 'border-b border-t-2 border-l-2 border-r-2 border-t-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF]'
                                : 'border border-t-[#ccc] border-l-[#ccc] border-r-[#ccc]'
                            )}
                          >
                            <div className="flex gap-2 items-center">
                              <ClockCircleFilled
                                className={'text-[#2596be] text-lg'}
                              />
                              {editingStepName !== inputNumber?.key && (
                                <div className="flex items-center gap-1">
                                  <span
                                    className="font-semibold text-sm"
                                    title={
                                      inputNumber?.name ||
                                      `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                    }
                                  >
                                    {inputNumber?.name ||
                                      `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`}
                                  </span>
                                  <Tooltip
                                    title={`Click to edit Step\'s name.`}
                                  >
                                    <Button
                                      type="text"
                                      icon={
                                        <EditOutlined className="text-cyan-600" />
                                      }
                                      onClick={(e) => {
                                        e?.stopPropagation();
                                        setEditingStepName(inputNumber?.key);
                                      }}
                                    />
                                  </Tooltip>
                                </div>
                              )}
                              {editingStepName === inputNumber?.key && (
                                <div className="flex items-center">
                                  <Input
                                    defaultValue={
                                      inputNumber?.name ||
                                      `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                    }
                                    value={inputNumber?.name}
                                    className="!border-t-0 !border-r-0 !border-l-0 max-w-[10rem]"
                                    maxLength={20}
                                    showCount
                                    onClick={(e) => e.stopPropagation()}
                                    onChange={(evt) =>
                                      onChangeStepName(
                                        inputNumber,
                                        evt.target.value
                                      )
                                    }
                                  />
                                  <Button
                                    type="text"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setEditingStepName('');
                                    }}
                                    icon={<CheckOutlined />}
                                  />
                                </div>
                              )}
                            </div>
                            {editingNotes !== inputNumber?.key ? (
                              <div className="gap-2">
                                <Dropdown
                                  menu={{
                                    items: [
                                      {
                                        key: 'edit-wait',
                                        label: <span>Edit Step</span>,
                                        icon: <EditOutlined />,
                                        onClick: () => {
                                          setEditingNotes('');
                                        },
                                      },
                                      {
                                        key: 'duplicate-wait',
                                        label: <span>Duplicate Step</span>,
                                        icon: <CopyOutlined />,
                                        onClick: (e) => {
                                          e?.domEvent?.stopPropagation();
                                          duplicateStep(inputNumber);
                                        },
                                      },
                                    ],
                                  }}
                                  arrow
                                  placement="bottomCenter"
                                  trigger={'click'}
                                >
                                  <Button
                                    type="text"
                                    icon={
                                      <EllipsisOutlined className="text-xl" />
                                    }
                                    size="small"
                                    shape="circle"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </Dropdown>
                              </div>
                            ) : (
                              <Tooltip title="Duplicate step">
                                <Button
                                  type="text"
                                  icon={<CopyOutlined className="text-xl" />}
                                  shape="circle"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    duplicateStep(inputNumber);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>

                          <div
                            className={clsx(
                              'bg-white rounded-bl-lg rounded-br-lg shadow-sm flex flex-col p-5',
                              editingNotes === inputNumber?.key ||
                                selectingStep === inputNumber?.key
                                ? 'border-2 border-t-0 border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
                                : 'border border-[#ccc]'
                            )}
                          >
                            <div className="line-clamp-1 text-[#2596be] font-semibold">
                              Add Delay
                            </div>
                            <div className="font-medium">
                              {editingNotes &&
                              editingNotes === inputNumber?.key ? (
                                <div className="flex items-center">
                                  <Controller
                                    render={({ field }) => (
                                      <input
                                        pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}"
                                        max={
                                          getValues(
                                            `sendMail.mailStep_${index}Unit`
                                          ) === 'Hours'
                                            ? 23
                                            : 999
                                        }
                                        min={1}
                                        type="number"
                                        className="mr-2 border"
                                        style={{
                                          width: '45%',
                                          background: '#fff',
                                          padding: '3px',
                                          borderRadius: '5px',
                                        }}
                                        value={inputNumber['delay']}
                                        onChange={(e) => {
                                          let valueTemp = e.target.value
                                            // .replaceAll('.', '')
                                            .replaceAll('-', '');

                                          if (
                                            getValues(
                                              `sendMail.mailStep_${index}Unit`
                                            ) === 'Hours' &&
                                            parseInt(valueTemp) > 23
                                          ) {
                                            notification.warning({
                                              message: 'Warning',
                                              description:
                                                'Delay duration can NOT be greater than 23 in Hours!',
                                            });
                                            return;
                                          }

                                          setConfirmStep(true);
                                          const newData = updateObjectInArray(
                                            inputNumberStep,
                                            index,
                                            +valueTemp
                                          );
                                          setInputNumberStep([...newData]);

                                          setValue(
                                            `mailStep.step_${index}Order`,
                                            +valueTemp
                                          );
                                        }}
                                      />
                                    )}
                                    name={`mailStep.step_${index}Order`}
                                    control={control}
                                  />
                                  <Select
                                    className="w-1/2"
                                    defaultValue={inputNumber['unit'] || 'Days'}
                                    style={{
                                      width: 120,
                                    }}
                                    onChange={(value) => {
                                      const currInputItems = [
                                        ...inputNumberStep,
                                      ];
                                      const curWaitItem = {
                                        ...inputNumber,
                                        unit: value,
                                      };
                                      const i = currInputItems.findIndex(
                                        (x) => x.key === curWaitItem.key
                                      );
                                      currInputItems[i] = curWaitItem;
                                      setInputNumberStep([...currInputItems]);
                                    }}
                                    options={durationUnitItems}
                                  />
                                  <Tooltip
                                    title={
                                      inputNumberStep[index - 1]?.type ===
                                        ADD_STEP_TYPE.SEND_MAIL &&
                                      inputNumberStep[index + 1]?.type ===
                                        ADD_STEP_TYPE.SEND_MAIL
                                        ? 'Not allow to delete'
                                        : 'Delete the delay'
                                    }
                                  >
                                    <Button
                                      onClick={() => {
                                        const newArr = inputNumberStep.filter(
                                          (item, itemIndex) =>
                                            itemIndex !== index
                                        );
                                        setInputNumberStep([...newArr]);
                                        setOpenSequenceOptions({
                                          first: false,
                                        });
                                        setEditingNotes('');
                                      }}
                                      icon={<DeleteOutlined />}
                                      shape="circle"
                                      type="text"
                                      disabled={
                                        inputNumberStep[index - 1]?.type ===
                                          ADD_STEP_TYPE.SEND_MAIL &&
                                        inputNumberStep[index + 1]?.type ===
                                          ADD_STEP_TYPE.SEND_MAIL
                                      }
                                      danger
                                    />
                                  </Tooltip>
                                  <Tooltip title="Save the delay">
                                    <Button
                                      onClick={() => {
                                        setEditingNotes('');
                                        addSavedSteps(
                                          inputNumber?.key || inputNumber?.id
                                        );
                                      }}
                                      icon={<CheckOutlined />}
                                      shape="circle"
                                      type="text"
                                    />
                                  </Tooltip>
                                </div>
                              ) : (
                                <>
                                  {(savedSteps?.includes(inputNumber?.key) ||
                                    savedSteps?.includes(inputNumber?.id)) &&
                                    editingNotes !== inputNumber?.key &&
                                    editingStepName !== inputNumber?.key && (
                                      <div
                                        className="absolute bottom-1 right-2"
                                        title={
                                          getInvalidMessage(
                                            `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                          ) || 'Step saved.'
                                        }
                                      >
                                        {getInvalidMessage(
                                          `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                        ) ? (
                                          <CloseOutlined className="text-red-600" />
                                        ) : (
                                          <CheckOutlined className="text-green-600" />
                                        )}
                                      </div>
                                    )}
                                  <span className="mr-1 text-[#2596be] font-semibold">
                                    {inputNumber['delay'] ||
                                      getValues(
                                        `mailStep.step_${index}Order`
                                      )?.toString()}
                                  </span>
                                  <span>
                                    {inputNumber['unit'] ||
                                      getValues(
                                        `sendMail.mailStep_${index}Unit`
                                      ) ||
                                      'Days'}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                    {(inputNumber?.type === 'NOTE' || inputNumber?.note) && (
                      <div
                        className={clsx(
                          'flex justify-center items-center w-full relative',
                          inputNumber?.key !== editingNotes &&
                            'cursor-pointer hover:shadow-lg hover:shadow-[#92C7CF] hover:rounded-md'
                        )}
                        onClick={() => {
                          if (inputNumber?.key === editingNotes) return;
                          setEditingNotes(inputNumber?.key);
                          setSelectingStep(inputNumber?.key);
                        }}
                      >
                        <div className="ribbon w-full">
                          <div className="flex justify-center items-center max-w-full flex-col">
                            {inputNumber?.status && !fromCreateTemplate && (
                              <span
                                class={clsx(
                                  'ribbon-base ribbon-pending',
                                  inputNumber?.status &&
                                    `${STEP_STATUS_CLASS[inputNumber?.status]}`
                                )}
                              >
                                <span>
                                  {STEP_STATUS_TEXT[inputNumber?.status]}
                                </span>
                              </span>
                            )}
                            <div
                              className={clsx(
                                'flex bg-white gap-4 items-center px-2 py-1 rounded-tl-md rounded-tr-md justify-between w-full border',
                                editingNotes === inputNumber?.key ||
                                  selectingStep === inputNumber?.key
                                  ? 'border-t-2 border-l-2 border-r-2 border-t-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF]'
                                  : ' border-t-[#ccc] border-l-[#ccc] border-r-[#ccc]'
                              )}
                            >
                              <div className="flex gap-2 items-center">
                                <ScheduleFilled
                                  className={'text-[#2596be] text-lg'}
                                />
                                {editingStepName !== inputNumber?.key && (
                                  <div className="flex items-center gap-1">
                                    <span
                                      className="font-semibold text-sm"
                                      title={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                    >
                                      {inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`}
                                    </span>
                                    <Tooltip
                                      title={`Click to edit Step\'s name.`}
                                    >
                                      <Button
                                        type="text"
                                        icon={
                                          <EditOutlined className="text-cyan-600" />
                                        }
                                        onClick={(e) => {
                                          e?.stopPropagation();
                                          setEditingStepName(inputNumber?.key);
                                        }}
                                      />
                                    </Tooltip>
                                  </div>
                                )}
                                {editingStepName === inputNumber?.key && (
                                  <div className="flex items-center">
                                    <Input
                                      defaultValue={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                      value={inputNumber?.name}
                                      className="!border-t-0 !border-r-0 !border-l-0 max-w-[10rem]"
                                      maxLength={20}
                                      showCount
                                      onClick={(e) => e.stopPropagation()}
                                      onChange={(evt) =>
                                        onChangeStepName(
                                          inputNumber,
                                          evt.target.value
                                        )
                                      }
                                    />
                                    <Tooltip
                                      title={`Click to edit Step\'s name.`}
                                    >
                                      <Button
                                        type="text"
                                        icon={<CheckOutlined />}
                                        onClick={(e) => {
                                          e?.stopPropagation();
                                          setEditingStepName('');
                                        }}
                                      />
                                    </Tooltip>
                                  </div>
                                )}
                              </div>
                              {(inputNumber?.saved == true ||
                                inputNumber?.saved == null) &&
                              inputNumber?.key !== editingNotes &&
                              !preview &&
                              previewMode !== PREVIEW_MODE.VIEW ? (
                                <Dropdown
                                  menu={{
                                    items: [
                                      {
                                        key: 'edit-wait',
                                        label: (
                                          <span>
                                            {editingNotes !== inputNumber?.key
                                              ? 'Edit Step'
                                              : 'Save Step'}
                                          </span>
                                        ),
                                        icon:
                                          editingNotes === inputNumber?.key ? (
                                            <CheckOutlined />
                                          ) : (
                                            <EditOutlined />
                                          ),
                                        onClick: () => {
                                          setEditingNotes(inputNumber?.key);
                                          setSelectingStep(inputNumber?.key);
                                        },
                                        disabled: !(
                                          !preview ||
                                          previewMode !== PREVIEW_MODE.VIEW
                                        ),
                                      },
                                      {
                                        key: 'delete-note',
                                        label: <span>Delete Step</span>,
                                        icon: <DeleteOutlined />,
                                        onClick: () => {
                                          const newArr = inputNumberStep.filter(
                                            (item, itemIndex) =>
                                              itemIndex !== index
                                          );
                                          setInputNumberStep([...newArr]);
                                          setLockedStep(false);
                                        },
                                      },
                                      ,
                                      {
                                        key: 'duplicate-note',
                                        label: <span>Duplicate Step</span>,
                                        icon: <CopyOutlined />,
                                        onClick: (e) => {
                                          e?.domEvent?.stopPropagation();
                                          duplicateStep(inputNumber);
                                        },
                                      },
                                    ],
                                  }}
                                  arrow
                                  placement="bottomCenter"
                                  trigger={'click'}
                                >
                                  <Button
                                    type="text"
                                    icon={
                                      <EllipsisOutlined className="text-xl" />
                                    }
                                    size="small"
                                    shape="circle"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </Dropdown>
                              ) : (
                                <Tooltip title="Duplicate step">
                                  <Button
                                    type="text"
                                    icon={<CopyOutlined className="text-xl" />}
                                    shape="circle"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      duplicateStep(inputNumber);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            </div>
                            {(inputNumber?.saved == false ||
                              inputNumber?.key === editingNotes) && (
                              <AddNoteJodit
                                index={index}
                                setLockedStep={setLockedStep}
                                inputNumber={inputNumber}
                                inputNumberStep={inputNumberStep}
                                setInputNumberStep={setInputNumberStep}
                                comments={inputNumber?.content ?? ''}
                                actions={inputNumber?.subject}
                                isEdit={inputNumber?.key === editingNotes}
                                setEditingNotes={setEditingNotes}
                                mergeFieldsBasedOnPlace={{
                                  definitions: [...MERTAGS_DEFINITION]
                                    .filter((item) =>
                                      fromCreatingScratchSequence ||
                                      fromCreateFromTemplateModal ||
                                      fromSequenceAI
                                        ? (!item.id.includes('VACANCY') &&
                                            !item.id.includes('COMPANY')) ||
                                          item.id === 'COMPANY_NAME'
                                        : true
                                    )
                                    .filter((item) =>
                                      fromCreatingScratchSequence
                                        ? !IGNORE_MERGE_TAGS.includes(item?.id)
                                        : true
                                    ),
                                  initialPreviewMode: '$defaultValues',
                                }}
                                addSavedSteps={addSavedSteps}
                                endEdittingStep={fixedPositionStep}
                              />
                            )}
                            {(inputNumber?.saved == true ||
                              inputNumber?.saved == null) &&
                              inputNumber?.key != editingNotes && (
                                <div
                                  className={clsx(
                                    'bg-white !py-4 px-5 rounded-bl-lg rounded-br-lg shadow-sm border w-full',
                                    editingNotes === inputNumber?.key ||
                                      selectingStep === inputNumber?.key
                                      ? 'border-2 border-t border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
                                      : 'border border-[#ccc]'
                                  )}
                                >
                                  {(savedSteps?.includes(inputNumber?.key) ||
                                    savedSteps?.includes(inputNumber?.id)) &&
                                    editingNotes !== inputNumber?.key &&
                                    editingStepName !== inputNumber?.key && (
                                      <div
                                        className="absolute bottom-1 right-2"
                                        title={
                                          getInvalidMessage(
                                            `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                          ) || 'Step saved.'
                                        }
                                      >
                                        {getInvalidMessage(
                                          `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                        ) ? (
                                          <CloseOutlined className="text-red-600" />
                                        ) : (
                                          <CheckOutlined className="text-green-600" />
                                        )}
                                      </div>
                                    )}
                                  <div>
                                    <div className="line-clamp-1 text-[#2596be] font-semibold">
                                      Note
                                    </div>
                                    <div style={{ display: 'flex' }}>
                                      <div style={{ width: '100px' }}>
                                        Comments:{' '}
                                      </div>
                                      <div
                                        style={{
                                          display: '-webkit-box',
                                          WebkitLineClamp: 1,
                                          WebkitBoxOrient: 'vertical',
                                          overflow: 'hidden',
                                          marginLeft: '10px',
                                        }}
                                        className="text-[#2596be] font-medium"
                                        dangerouslySetInnerHTML={{
                                          __html: inputNumber?.content,
                                        }}
                                      ></div>
                                    </div>
                                    <div style={{ display: 'flex' }}>
                                      <div style={{ width: '100px' }}>
                                        Action:{' '}
                                      </div>
                                      <div
                                        className="text-[#2596be] font-medium"
                                        style={{ marginLeft: '10px' }}
                                      >
                                        {inputNumber?.subject}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                          </div>
                        </div>
                      </div>
                    )}
                    {inputNumber?.type === ADD_STEP_TYPE.TASK && (
                      <div
                        className={clsx(
                          'flex justify-center items-center w-full relative',
                          inputNumber?.key !== editingNotes &&
                            'cursor-pointer hover:shadow-lg hover:shadow-[#92C7CF] hover:rounded-md'
                        )}
                        onClick={() => {
                          if (inputNumber?.key === editingNotes) return;
                          setEditingNotes(inputNumber?.key);
                          setSelectingStep(inputNumber?.key);
                        }}
                      >
                        <div className="ribbon w-full">
                          <div className="flex justify-center items-center max-w-full flex-col">
                            {inputNumber?.status && !fromCreateTemplate && (
                              <span
                                class={clsx(
                                  'ribbon-base ribbon-pending',
                                  inputNumber?.status &&
                                    `${STEP_STATUS_CLASS[inputNumber?.status]}`
                                )}
                              >
                                <span>
                                  {STEP_STATUS_TEXT[inputNumber?.status]}
                                </span>
                              </span>
                            )}
                            <div
                              className={clsx(
                                'flex bg-white gap-4 items-center px-2 py-1 rounded-tl-md rounded-tr-md justify-between w-full border',
                                editingNotes === inputNumber?.key ||
                                  selectingStep === inputNumber?.key
                                  ? 'border-t-2 border-l-2 border-r-2 border-t-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF]'
                                  : ' border-t-[#ccc] border-l-[#ccc] border-r-[#ccc]'
                              )}
                            >
                              <div className="flex gap-2 items-center">
                                <ScheduleFilled
                                  className={'text-[#2596be] text-lg'}
                                />
                                {editingStepName !== inputNumber?.key && (
                                  <div className="flex items-center gap-1">
                                    <span
                                      className="font-semibold text-sm"
                                      title={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                    >
                                      {inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`}
                                    </span>
                                    <Tooltip
                                      title={`Click to edit Step\'s name.`}
                                    >
                                      <Button
                                        type="text"
                                        icon={
                                          <EditOutlined className="text-cyan-600" />
                                        }
                                        onClick={(e) => {
                                          e?.stopPropagation();
                                          setEditingStepName(inputNumber?.key);
                                        }}
                                      />
                                    </Tooltip>
                                  </div>
                                )}
                                {editingStepName === inputNumber?.key && (
                                  <div className="flex items-center">
                                    <Input
                                      defaultValue={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                      value={inputNumber?.name}
                                      className="!border-t-0 !border-r-0 !border-l-0 max-w-[10rem]"
                                      maxLength={20}
                                      showCount
                                      onClick={(e) => e.stopPropagation()}
                                      onChange={(evt) =>
                                        onChangeStepName(
                                          inputNumber,
                                          evt.target.value
                                        )
                                      }
                                    />
                                    <Tooltip
                                      title={`Click to edit Step\'s name.`}
                                    >
                                      <Button
                                        type="text"
                                        icon={<CheckOutlined />}
                                        onClick={(e) => {
                                          e?.stopPropagation();
                                          setEditingStepName('');
                                        }}
                                      />
                                    </Tooltip>
                                  </div>
                                )}
                              </div>
                              {inputNumber?.key !== editingNotes &&
                              !preview &&
                              previewMode !== PREVIEW_MODE.VIEW ? (
                                <Dropdown
                                  menu={{
                                    items: [
                                      {
                                        key: 'edit-task',
                                        label: (
                                          <span>
                                            {editingNotes !== inputNumber?.key
                                              ? 'Edit Step'
                                              : 'Save Step'}
                                          </span>
                                        ),
                                        icon:
                                          editingNotes === inputNumber?.key ? (
                                            <CheckOutlined />
                                          ) : (
                                            <EditOutlined />
                                          ),
                                        onClick: () => {
                                          setEditingNotes(inputNumber?.key);
                                          setSelectingStep(inputNumber?.key);
                                        },
                                        disabled: !(
                                          !preview ||
                                          previewMode !== PREVIEW_MODE.VIEW
                                        ),
                                      },
                                      {
                                        key: 'delete-task',
                                        label: <span>Delete Step</span>,
                                        icon: <DeleteOutlined />,
                                        onClick: () => {
                                          const newArr = inputNumberStep.filter(
                                            (item, itemIndex) =>
                                              itemIndex !== index
                                          );
                                          setInputNumberStep([...newArr]);
                                        },
                                      },
                                      ,
                                      {
                                        key: 'duplicate-task',
                                        label: <span>Duplicate Step</span>,
                                        icon: <CopyOutlined />,
                                        onClick: (e) => {
                                          e?.domEvent?.stopPropagation();
                                          duplicateStep(inputNumber);
                                        },
                                      },
                                    ],
                                  }}
                                  arrow
                                  placement="bottomCenter"
                                  trigger={'click'}
                                >
                                  <Button
                                    type="text"
                                    icon={
                                      <EllipsisOutlined className="text-xl" />
                                    }
                                    size="small"
                                    shape="circle"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </Dropdown>
                              ) : (
                                <Tooltip title="Duplicate step">
                                  <Button
                                    type="text"
                                    icon={<CopyOutlined className="text-xl" />}
                                    shape="circle"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      duplicateStep(inputNumber);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            </div>
                            {(inputNumber?.saved == false ||
                              inputNumber?.key === editingNotes) && (
                              <AddTaskStep
                                index={index}
                                getGlobalValues={getValues}
                                inputNumber={inputNumber}
                                inputNumberStep={inputNumberStep}
                                setInputNumberStep={setInputNumberStep}
                                comments={inputNumber?.content || ''}
                                actions={inputNumber?.subject}
                                isEdit={inputNumber?.key === editingNotes}
                                setEditingNotes={setEditingNotes}
                                mergeFieldsBasedOnPlace={{
                                  definitions: [...MERTAGS_DEFINITION]
                                    .filter((item) =>
                                      fromCreatingScratchSequence ||
                                      fromCreateFromTemplateModal ||
                                      fromSequenceAI
                                        ? (!item.id.includes('VACANCY') &&
                                            !item.id.includes('COMPANY')) ||
                                          item.id === 'COMPANY_NAME'
                                        : true
                                    )
                                    .filter((item) =>
                                      fromCreatingScratchSequence
                                        ? !IGNORE_MERGE_TAGS.includes(item?.id)
                                        : true
                                    ),
                                  initialPreviewMode: '$defaultValues',
                                }}
                                addSavedSteps={addSavedSteps}
                                endEdittingStep={fixedPositionStep}
                              />
                            )}
                            {inputNumber?.key !== editingNotes && (
                              <div
                                className={clsx(
                                  'bg-white !py-4 px-5 rounded-bl-lg rounded-br-lg shadow-sm border w-full',
                                  editingNotes === inputNumber?.key ||
                                    selectingStep === inputNumber?.key
                                    ? 'border-2 border-t border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
                                    : 'border border-[#ccc]'
                                )}
                              >
                                {(savedSteps?.includes(inputNumber?.key) ||
                                  savedSteps?.includes(inputNumber?.id)) &&
                                  editingNotes !== inputNumber?.key &&
                                  editingStepName !== inputNumber?.key && (
                                    <div
                                      className="absolute bottom-1 right-2"
                                      title={
                                        getInvalidMessage(
                                          `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                        ) || 'Step saved.'
                                      }
                                    >
                                      {getInvalidMessage(
                                        `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                      ) ? (
                                        <CloseOutlined className="text-red-600" />
                                      ) : (
                                        <CheckOutlined className="text-green-600" />
                                      )}
                                    </div>
                                  )}
                                <div>
                                  <div className="line-clamp-1 text-[#2596be] font-semibold">
                                    Task
                                  </div>
                                  <div style={{ display: 'flex' }}>
                                    <div style={{ width: '100px' }}>
                                      Description:{' '}
                                    </div>
                                    <div
                                      style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 1,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        marginLeft: '10px',
                                      }}
                                      className="text-[#2596be] font-medium"
                                      dangerouslySetInnerHTML={{
                                        __html: inputNumber?.content,
                                      }}
                                    ></div>
                                  </div>
                                  <div style={{ display: 'flex' }}>
                                    <div style={{ width: '100px' }}>
                                      Action:{' '}
                                    </div>
                                    <div
                                      className="text-[#2596be] font-medium"
                                      style={{ marginLeft: '10px' }}
                                    >
                                      {inputNumber?.subject}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                    {(_.has(inputNumber, 'inmailMessage') ||
                      inputNumber?.type ===
                        ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST) && (
                      <>
                        <div
                          className={clsx(
                            'ribbon w-full relative',
                            inputNumber?.key !== editingNotes &&
                              'cursor-pointer hover:shadow-lg hover:shadow-[#92C7CF] hover:rounded-md'
                          )}
                          onClick={() => {
                            if (inputNumber?.key === editingNotes) return;
                            setEditingNotes(inputNumber?.key);
                            setSelectingStep(inputNumber?.key);
                          }}
                        >
                          {inputNumber?.status && !fromCreateTemplate && (
                            <span
                              class={clsx(
                                'ribbon-base ribbon-pending',
                                inputNumber?.status &&
                                  `${STEP_STATUS_CLASS[inputNumber?.status]}`
                              )}
                            >
                              <span>
                                {STEP_STATUS_TEXT[inputNumber?.status]}
                              </span>
                            </span>
                          )}
                          <div
                            className={clsx(
                              'flex bg-white gap-4 items-center px-2 py-1 rounded-tl-md rounded-tr-md justify-between w-full',
                              editingNotes === inputNumber?.key ||
                                selectingStep === inputNumber?.key
                                ? 'border-b border-t-2 border-l-2 border-r-2 border-t-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF]'
                                : 'border border-t-[#ccc] border-l-[#ccc] border-r-[#ccc]'
                            )}
                          >
                            <div className="flex justify-between items-center w-full">
                              <div className="flex gap-2 items-center w-full pt-1">
                                <LinkedinOutlined
                                  className={'text-[#2596be] text-lg'}
                                />
                                {editingStepName !== inputNumber?.key && (
                                  <div className="flex items-center gap-1">
                                    <span
                                      className="font-semibold text-sm"
                                      title={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                    >
                                      {inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`}
                                    </span>
                                    <Tooltip
                                      title={`Click to edit Step\'s name.`}
                                    >
                                      <Button
                                        type="text"
                                        icon={
                                          <EditOutlined className="text-cyan-600" />
                                        }
                                        onClick={(e) => {
                                          e?.stopPropagation();
                                          setEditingStepName(inputNumber?.key);
                                        }}
                                      />
                                    </Tooltip>
                                  </div>
                                )}
                                {editingStepName === inputNumber?.key && (
                                  <div className="flex items-center">
                                    <Input
                                      defaultValue={
                                        inputNumber?.name ||
                                        `Step ${selectedSequenceValue?.currentNumberStepCount && fromChildSequence ? `${selectedSequenceValue?.currentNumberStepCount + 1}.` : `1.`}${index + 1}`
                                      }
                                      value={inputNumber?.name}
                                      className="!border-t-0 !border-r-0 !border-l-0 max-w-[10rem]"
                                      maxLength={20}
                                      showCount
                                      onClick={(e) => e.stopPropagation()}
                                      onChange={(evt) =>
                                        onChangeStepName(
                                          inputNumber,
                                          evt.target.value
                                        )
                                      }
                                    />
                                    <Button
                                      type="text"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setEditingStepName('');
                                      }}
                                      icon={<CheckOutlined />}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getValues('linkedinStepParticipants')?.find(
                                (item) =>
                                  item?.key === inputNumber?.key ||
                                  item?.id === inputNumber?.id
                              ) && (
                                <Badge
                                  count={
                                    [
                                      ...(getValues(
                                        'linkedinStepParticipants'
                                      )?.find(
                                        (item) =>
                                          item?.key === inputNumber?.key ||
                                          item?.id === inputNumber?.id
                                      )?.recipients || []),
                                    ]?.length
                                  }
                                >
                                  {/* <Tooltip
                                    title={`There are ${
                                      [
                                        ...(getValues(
                                          'linkedinStepParticipants'
                                        )?.find(
                                          (item) =>
                                            item?.key === inputNumber?.key ||
                                            item?.id === inputNumber?.id
                                        )?.recipients || []),
                                        ...(getValues(
                                          'linkedinStepParticipants'
                                        )?.find(
                                          (item) =>
                                            item?.key === inputNumber?.key ||
                                            item?.id === inputNumber?.id
                                        )?.contactListIds || []),
                                        ...(getValues(
                                          'linkedinStepParticipants'
                                        )?.find(
                                          (item) =>
                                            item?.key === inputNumber?.key ||
                                            item?.id === inputNumber?.id
                                        )?.hotlistIds || []),
                                      ]?.length
                                    } participant(s) in this step.`}
                                  > */}
                                  <Avatar
                                    size={'small'}
                                    icon={<UsergroupAddOutlined />}
                                  />
                                  {/* </Tooltip> */}
                                </Badge>
                              )}
                              {inputNumber?.key != editingNotes &&
                              !preview &&
                              previewMode !== PREVIEW_MODE.VIEW ? (
                                <Dropdown
                                  menu={{
                                    items: [
                                      {
                                        key: 'edit-linkedin',
                                        label: <span>Edit Step</span>,
                                        icon: <EditOutlined />,
                                        onClick: () => {
                                          setEditingNotes(inputNumber?.key);
                                          setSelectingStep(inputNumber?.key);
                                        },
                                        disabled: !isValidJSON(
                                          inputNumber?.content
                                        ),
                                      },
                                      {
                                        key: 'delete-linkedin',
                                        label: <span>Delete Step</span>,
                                        icon: <DeleteOutlined />,
                                        onClick: () => {
                                          const newArr = inputNumberStep.filter(
                                            (item, itemIndex) =>
                                              itemIndex !== index
                                          );
                                          setInputNumberStep([...newArr]);
                                          setLockedStep(false);
                                        },
                                      },
                                      ,
                                      {
                                        key: 'duplicate-linkedin',
                                        label: <span>Duplicate Step</span>,
                                        icon: <CopyOutlined />,
                                        onClick: (e) => {
                                          e?.domEvent?.stopPropagation();
                                          duplicateStep(inputNumber);
                                        },
                                      },
                                    ],
                                  }}
                                  arrow
                                  placement="bottomCenter"
                                  trigger={'click'}
                                >
                                  <Button
                                    type="text"
                                    icon={
                                      <EllipsisOutlined className="text-xl" />
                                    }
                                    size="small"
                                    shape="circle"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </Dropdown>
                              ) : (
                                <Tooltip title="Duplicate step">
                                  <Button
                                    type="text"
                                    icon={<CopyOutlined className="text-xl" />}
                                    shape="circle"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      duplicateStep(inputNumber);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            </div>
                          </div>
                          {(inputNumber?.saved == false ||
                            inputNumber?.key === editingNotes) && (
                            <LinkedInRequest
                              validatingLoading={
                                validatingProcessLoading.contactList
                              }
                              contactList={contactList}
                              index={index}
                              inputNumber={inputNumber}
                              inputNumberStep={inputNumberStep}
                              setInputNumberStep={setInputNumberStep}
                              setEditingNotes={setEditingNotes}
                              getGlobalValues={getValues}
                              fromCreateByVacancy={fromCreateByVacancy}
                              fromSync={fromSync}
                              setGlobalValue={setValue}
                              isEdit={inputNumber?.key === editingNotes}
                              mergeFieldsBasedOnPlace={{
                                definitions: [...MERTAGS_DEFINITION]
                                  .filter((item) =>
                                    fromCreatingScratchSequence ||
                                    fromCreateFromTemplateModal ||
                                    fromSequenceAI
                                      ? (!item.id.includes('VACANCY') &&
                                          !item.id.includes('COMPANY')) ||
                                        item.id === 'COMPANY_NAME'
                                      : true
                                  )
                                  .filter((item) =>
                                    fromCreatingScratchSequence
                                      ? !IGNORE_MERGE_TAGS.includes(item?.id)
                                      : true
                                  ),
                                initialPreviewMode: '$defaultValues',
                              }}
                              addSavedSteps={addSavedSteps}
                              endEdittingStep={fixedPositionStep}
                            />
                          )}
                          {inputNumber?.key != editingNotes && (
                            <div
                              className={clsx(
                                'bg-white p-5 rounded-bl-lg rounded-br-lg shadow-sm border w-full',
                                editingNotes === inputNumber?.key ||
                                  selectingStep === inputNumber?.key
                                  ? 'border-2 border-t border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
                                  : 'border border-[#ccc]'
                              )}
                            >
                              {(savedSteps?.includes(inputNumber?.key) ||
                                savedSteps?.includes(inputNumber?.id)) &&
                                editingNotes !== inputNumber?.key &&
                                editingStepName !== inputNumber?.key && (
                                  <div
                                    className="absolute bottom-1 right-2"
                                    title={
                                      getInvalidMessage(
                                        `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                      ) || 'Step saved.'
                                    }
                                  >
                                    {getInvalidMessage(
                                      `${inputNumberStep[index]?.content} ${inputNumberStep[index]?.subject}`
                                    ) ? (
                                      <CloseOutlined className="text-red-600" />
                                    ) : (
                                      <CheckOutlined className="text-green-600" />
                                    )}
                                  </div>
                                )}
                              <div className="pb-2 flex items-center gap-1 font-semibold text-sm">
                                <span>From:</span>
                                <div className="flex items-center gap-1 ">
                                  {avatarUrl && (
                                    <Avatar
                                      size={'default'}
                                      src={avatarUrl}
                                    ></Avatar>
                                  )}
                                  <span className="line-clamp-1 text-[#2596be] italic">
                                    {userToSet?.fullName ||
                                      userToSet?.user?.fullName ||
                                      '-'}
                                  </span>
                                </div>
                              </div>
                              {isValidJSON(inputNumber?.content) &&
                                JSON.parse(inputNumber?.content)?.[0]?.type ===
                                  LINKINEDIN_REQUEST_TYPE.INMAIL && (
                                  <div className="pb-2 flex items-center gap-1 font-semibold text-sm">
                                    <span>Subject:</span>
                                    <div className="flex items-center gap-1 ">
                                      <span className="line-clamp-1 text-[#2596be]">
                                        {JSON.parse(inputNumber?.content)?.[0]
                                          ?.subject || '-'}
                                      </span>
                                    </div>
                                  </div>
                                )}
                              <div>
                                <div
                                  className={`grid grid-cols-${isValidJSON(inputNumber?.content) ? JSON.parse(inputNumber?.content)?.length : '1'} gap-2`}
                                >
                                  {isValidJSON(inputNumber?.content) ? (
                                    JSON.parse(inputNumber?.content)?.map(
                                      (item) => (
                                        <Button
                                          icon={
                                            LINKEDIN_TYPE_ICONS[item?.type] || (
                                              <RightSquareFilled className="text-xs" />
                                            )
                                          }
                                          className={`text-xs text-white bg-[#0867c4]`}
                                        >
                                          {LINKEDIN_TYPE_TEXTS[item?.type]}
                                        </Button>
                                      )
                                    )
                                  ) : (
                                    <div>
                                      Content is outdated!{' '}
                                      <a
                                        href={`mailTo:${ADMIN_MAILS}?subject=Outdated LinkedIn Step in Sequence ID: ${emailSeqId}`}
                                        className="italic text-navy-300"
                                      >
                                        Contact admin
                                      </a>{' '}
                                      to update latest version.
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    )}
                    <div
                      className={clsx(
                        'flex justify-center relative min-h-[1rem] items-center line-container py-3',
                        index + 1 !== inputNumberStep?.length &&
                          'overflow-hidden'
                      )}
                    >
                      <div
                        className={clsx(
                          'border absolute border-dashed z-1 top-0 animated fadeInDownBig',
                          !inputNumberStep[index + 1] ? 'h-1/2' : 'h-full',
                          STEP_STATUS_LINE[inputNumber?.status] ||
                            STEP_STATUS_LINE[
                              inputNumberStep?.[index + 1]?.status
                            ] ||
                            'border-[#ccc]'
                        )}
                      />
                      {/* {!isOpenSequenceOptions[`step_${index + 1}`] &&
                        // selectedSequenceValue?.isMarkedAsCompleted &&
                        // index + 1 !== inputNumberStep?.length && 
                        } */}
                      {isOpenSequenceOptions[`step_${index + 1}`] && (
                        <div className="flex flex-col items-center bg-white rounded-md px-3 pb-3 border shadow-md z-10 animated fadeInDownBig border-[#ccc] add-new-step-container">
                          <Button
                            onClick={() => {
                              const openSequenceOptionsTemp = {
                                ...isOpenSequenceOptions,
                              };
                              openSequenceOptionsTemp[`step_${index + 1}`] =
                                !openSequenceOptionsTemp[`step_${index + 1}`];
                              setOpenSequenceOptions({
                                ...openSequenceOptionsTemp,
                              });
                            }}
                            type="text"
                            shape="circle"
                            icon={
                              <CloseCircleOutlined
                                style={{ fontSize: '20px', color: 'red' }}
                              />
                            }
                          />
                          <div className="relative mb-3 font-medium relative">
                            <span>Add New Step</span>
                          </div>
                          <div className="grid grid-cols-2 gap-5 relative">
                            <div className="col-span-1 flex flex-col gap-3">
                              <div className="flex justify-start">
                                <Button
                                  onClick={() => {
                                    handleAddNewStep(
                                      index + 1,
                                      ADD_STEP_TYPE.ADD_WAIT
                                    );
                                  }}
                                  icon={<ClockCircleOutlined />}
                                  className="w-40 add-new-step-button"
                                >
                                  Add Wait
                                </Button>
                              </div>
                              <div className="flex justify-start">
                                <Tooltip
                                  placement="bottom"
                                  title={
                                    inputNumberStep[index]?.delay
                                      ? ''
                                      : 'Please add wait step'
                                  }
                                >
                                  <Button
                                    disabled={
                                      (!inputNumberStep[index]?.delay ||
                                      inputNumberStep[index + 1]?.type ===
                                        ADD_STEP_TYPE.SEND_MAIL
                                        ? true
                                        : false) || openSendMailOptions
                                    }
                                    onClick={() => {
                                      setMailObject({
                                        ...initialMailObject,
                                        step: index + 1,
                                      });
                                      setOpenMailEditor(false);
                                      showSendMailOptionsDrawer();
                                      const openSequenceOptionsTemp = {
                                        ...isOpenSequenceOptions,
                                      };
                                      openSequenceOptionsTemp[
                                        `step_${index + 1}`
                                      ] =
                                        !openSequenceOptionsTemp[
                                          `step_${index + 1}`
                                        ];
                                      setOpenSequenceOptions({
                                        ...openSequenceOptionsTemp,
                                      });
                                    }}
                                    icon={<MailOutlined />}
                                    className="w-40 add-new-step-button"
                                  >
                                    Send Mail
                                  </Button>
                                </Tooltip>
                              </div>

                              <div className="flex justify-start">
                                <Button
                                  onClick={() =>
                                    handleAddNewStep(
                                      index + 1,
                                      ADD_STEP_TYPE.TASK
                                    )
                                  }
                                  icon={<AppstoreAddOutlined />}
                                  className="w-40 add-new-step-button"
                                >
                                  Add Task
                                </Button>
                              </div>
                            </div>
                            <div className="col-span-1 flex flex-col gap-3 ">
                              <div className="flex justify-center">
                                {isLinkedInStepBlocked ? (
                                  <Popconfirm
                                    rootClassName="customize-tooltip-widget warning"
                                    placement="top"
                                    title="Warning"
                                    description={
                                      <div>
                                        Please click{' '}
                                        <a
                                          href="/settings?tab=linkedin"
                                          className="text-navy-300 font-medium"
                                        >
                                          here
                                        </a>{' '}
                                        to link your LinkedIn account before
                                        using this feature
                                      </div>
                                    }
                                  >
                                    <Button
                                      className="w-40 add-new-step-button"
                                      style={{
                                        color: '#********',
                                        background: '#0000000a',
                                      }}
                                      icon={
                                        <LinkedinOutlined className="text-[#0288d1]" />
                                      }
                                    >
                                      LinkedIn
                                    </Button>
                                  </Popconfirm>
                                ) : (
                                  <AddLinkedinTypeButton
                                    handleAddNewStep={handleAddNewStep}
                                    index={index}
                                  />
                                )}
                              </div>
                              <div className="flex justify-start">
                                <Button
                                  className="w-40 add-new-step-button"
                                  onClick={() => {
                                    handleAddNewStep(
                                      index + 1,
                                      ADD_STEP_TYPE.ADD_NOTE
                                    );
                                  }}
                                  icon={<ForkOutlined />}
                                >
                                  Add Note
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {!isOpenSequenceOptions[`step_${index + 1}`] &&
                        (selectedSequenceValue?.isMarkedAsCompleted &&
                        index + 1 === inputNumberStep?.length ? (
                          <div
                            className={clsx(
                              'w-[25rem] h-12 rounded-md bg-[#e6fff8] border z-50 flex items-center p-3 shadow-md border border-[#00996d]',
                              !startNewSequenceFlip ||
                                (startNewSequenceFlip &&
                                  !selectedSequenceValue?.child)
                                ? 'justify-between'
                                : 'justify-center'
                            )}
                          >
                            <div className="flex items-center gap-2">
                              {/* <Image
                                width={'25px'}
                                src={DoneIcon}
                                preview={false}
                              /> */}
                              <span className="text-[#00996d] font-semibold text-sm">
                                Completed
                              </span>
                            </div>
                            <div>
                              {!startNewSequenceFlip && (
                                <div className="flex items-center justify-center gap-1">
                                  <p
                                    onClick={() =>
                                      setStartNewSequenceFlip(true)
                                    }
                                    className="flex items-center gap-1 hover:underline cursor-pointer p-1 bg-[#c1fceb] text-xs rounded-sm shadow-sm text-[#00996d] px-3"
                                  >
                                    Start New Sequence
                                    <SwapRightOutlined />{' '}
                                  </p>
                                </div>
                              )}
                              {startNewSequenceFlip &&
                                !selectedSequenceValue?.child && (
                                  <p
                                    onClick={() =>
                                      setStartNewSequenceFlip(false)
                                    }
                                    className="flex items-center gap-1 hover:underline cursor-pointer p-1 bg-[#c1fceb] text-xs text-[#00996d] px-3"
                                  >
                                    Stop sequence
                                    <CloseOutlined />
                                  </p>
                                )}
                            </div>
                          </div>
                        ) : (
                          <Button
                            disabled={
                              lockedStep ||
                              (preview && previewMode === PREVIEW_MODE.VIEW) ||
                              openSendMailOptions ||
                              selectedSequenceValue?.isMarkedAsCompleted ||
                              editingNotes
                            }
                            title={
                              openSendMailOptions &&
                              'Please save the current step before adding new.'
                            }
                            onClick={() => {
                              {
                                const isAllowToSequence =
                                  userToSet && isGrantedSequence();

                                if (!isAllowToSequence) {
                                  setOpenWarningModal(true);
                                } else {
                                  const openSequenceOptionsTemp = {
                                    ...isOpenSequenceOptions,
                                  };
                                  openSequenceOptionsTemp[`step_${index + 1}`] =
                                    !openSequenceOptionsTemp[
                                      `step_${index + 1}`
                                    ];
                                  setOpenSequenceOptions({
                                    ...openSequenceOptionsTemp,
                                  });
                                }
                              }
                            }}
                            type="text"
                            shape="circle"
                            className={clsx('bg-white')}
                            icon={
                              <PlusCircleOutlined
                                className="text-[#7CF5FF]"
                                style={{ fontSize: '20px' }}
                              />
                            }
                          />
                        ))}
                    </div>
                  </div>
                ))}
              </div>

              {startNewSequenceFlip && (
                <ChildSequence
                  parentSeqId={emailSeqId}
                  companyId={watch('companyId') || ''}
                  defaultChildSequence={{
                    ...selectedSequenceValue?.child,
                    currentNumberStepCount:
                      selectedSequenceValue?.currentNumberStepCount,
                    ignoreParticipantsIds:
                      selectedSequenceValue?.ignoreParticipantsIds,
                  }}
                />
              )}
            </div>
          </div>
          {(!preview || previewMode !== PREVIEW_MODE.VIEW) &&
            !selectedSequenceValue?.isMarkedAsCompleted &&
            !startNewSequenceFlip && (
              <div className="flex items-center flex-col gap-4 p-2 justify-center w-full mt-4">
                <div className="flex items-center w-full justify-center">
                  {!fromSequenseDetail && !isDuplicateTemplate && (
                    <Button
                      onClick={handleAddMail}
                      type="primary"
                      loading={loadingSaveSequence}
                      disabled={
                        (!fromChildSequence &&
                          !fromCreateTemplate &&
                          !getValues('sequenceData.name')?.trim()) ||
                        editingNotes ||
                        openEmailsValidator ||
                        openSendToEmailsValidator ||
                        openHotListEmailsValidator ||
                        validatingProcessLoading.contactList ||
                        validatingProcessLoading.hotList ||
                        validatingProcessLoading.sendToList
                      }
                      title={
                        !fromCreateTemplate &&
                        !getValues('sequenceData.name')?.trim()
                          ? 'Please add sequence name first!'
                          : editingNotes
                            ? 'Please save the current step before saving.'
                            : ''
                      }
                    >
                      {fromCreateTemplate ? 'Save' : 'Start'}
                    </Button>
                  )}
                  {isDuplicateTemplate && (
                    <Button
                      onClick={handleSaveAsTemplate}
                      disabled={
                        openEmailsValidator ||
                        openSendToEmailsValidator ||
                        openHotListEmailsValidator
                      }
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      loading={loadingDuplicateSequence}
                    >
                      Duplicate
                    </Button>
                  )}

                  {fromSequenseDetail && (
                    <Button
                      disabled={
                        loadingSaveDraft ||
                        openEmailsValidator ||
                        openSendToEmailsValidator ||
                        openHotListEmailsValidator
                      }
                      onClick={handleAddMail}
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      loading={loadingSaveSequence}
                    >
                      Save
                    </Button>
                  )}
                  {!onSubmitTriggers &&
                    !preview &&
                    previewMode !== PREVIEW_MODE.VIEW &&
                    !fromCreateFromTemplateModal &&
                    !fromCreatingScratchSequence && (
                      <Popconfirm
                        title="What is the template's name?"
                        description={
                          <div className="pt-2 pb-1">
                            <Input
                              value={currentTemplate?.name || templateName}
                              maxLength={100}
                              showCount
                              onChange={(ev) => {
                                const newTemplateName = ev.target.value;

                                if (currentTemplate?.name) {
                                  setCurrentTemplate({
                                    ...currentTemplate,
                                    name: newTemplateName,
                                  });
                                  return;
                                }

                                setTemplateName(newTemplateName);
                              }}
                            ></Input>
                          </div>
                        }
                        okText="Save"
                        showCancel={!!currentTemplate?.id}
                        cancelText="Save new"
                        onCancel={() => handleSaveAsTemplate(true)}
                        okButtonProps={{
                          disabled:
                            !templateName.trim() &&
                            !currentTemplate?.name?.trim(),
                          loading: loadingSaveAsASequence,
                        }}
                        onConfirm={handleSaveAsTemplate}
                      >
                        <Tooltip
                          title={
                            currentTemplate?.createdBy &&
                            currentTemplate?.createdBy !== userToSetId
                              ? "Can not update other's template."
                              : 'Save as a template'
                          }
                        >
                          <Button
                            type="primary"
                            style={{ marginLeft: '10px' }}
                            disabled={
                              inputNumberStep?.length === 0 ||
                              loadingSaveSequence ||
                              (currentTemplate?.createdBy &&
                                currentTemplate?.createdBy !== userToSetId)
                            }
                          >
                            {currentTemplate?.id
                              ? 'Update Template'
                              : 'Save New Template'}
                          </Button>
                        </Tooltip>
                      </Popconfirm>
                    )}
                  <Button
                    onClick={handleDiscard}
                    type="primary"
                    disabled={
                      notDiscard ||
                      loadingSaveDraft ||
                      loadingSaveSequence ||
                      openEmailsValidator ||
                      openSendToEmailsValidator ||
                      openHotListEmailsValidator
                    }
                    style={{ marginLeft: '10px' }}
                  >
                    Discard
                  </Button>
                </div>
                <div>
                  {selectedSequenceValue?.status ||
                  selectedSequenceValue?.status === 'LIVE' ||
                  selectedSequenceValue?.child ||
                  sequenceItem?.status ||
                  sequenceItem?.status === 'LIVE' ||
                  sequenceItem?.child ||
                  fromCreateTemplate ||
                  fromChildSequence ? null : (
                    <Button
                      onClick={() => handleAddMail(true)}
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      disabled={
                        loadingSaveDraft ||
                        autoSavingLoading ||
                        openEmailsValidator ||
                        openSendToEmailsValidator ||
                        openHotListEmailsValidator
                      }
                    >
                      Save Draft
                    </Button>
                  )}
                </div>
              </div>
            )}
          {openSendMailOptions &&
            !preview &&
            !fromCreatingScratchSequence &&
            !fromCreateFromTemplateModal && (
              <Drawer
                getContainer={false}
                maskClosable={false}
                title={<span className="text-2xl ">{`Email Form`}</span>}
                placement={'right'}
                width={fromSequenseDetail ? 860 : 900}
                onClose={closeSendMailOptionsDrawer}
                open={openSendMailOptions}
                footer={
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        if (openMailEditorFromStep) {
                          handleEditMailStep();
                        } else {
                          setValue(
                            'sendMail.listEmailSendChoose',
                            getValues('sendMail.listEmailSend')
                          );
                          handleAddNewStep(
                            mailObject.step,
                            ADD_STEP_TYPE.SEND_MAIL
                          );
                        }
                        closeSendMailOptionsDrawer();
                      }}
                      disabled={loadingTestEmail}
                    >
                      Save
                    </Button>
                    <Button
                      disabled={loadingTestEmail}
                      onClick={closeSendMailOptionsDrawer}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSendTestMail}
                      loading={loadingTestEmail}
                      icon={<MailOutlined />}
                      type="primary"
                    >
                      {' '}
                      Send Test Email
                    </Button>
                  </div>
                }
              >
                <>
                  {!openMailEditor && !openMailEditorFromStep && (
                    <div className="grid grid-cols-2 gap-5">
                      <div
                        onClick={() => {
                          setMailObject({
                            ...mailObject,
                            subject: '',
                            content: '',
                          });
                          setOpenMailEditor(true);
                        }}
                        className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                      >
                        <span className="block text-xl font-medium mb-1">
                          From Scratch - Plain Text
                        </span>
                        <span className="text-base text-[#797979]">
                          Create a simple email like you'd send from your
                          personal email using plain text
                        </span>
                      </div>
                      <div
                        onClick={() => showSmartEmailDrawer()}
                        className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                      >
                        <span className="block text-xl font-medium mb-1">
                          From RE: Threading
                        </span>
                        <span className="text-base text-[#797979]">
                          Create a following email that based on another mail
                          with purpose to remind recipient.
                        </span>
                      </div>
                    </div>
                  )}
                  {((openMailEditor && !openMailTemplate) ||
                    openMailEditorFromStep) && (
                    <div>
                      <Form layout="vertical">
                        <BullhornSendEmail
                          control={control}
                          setValue={setValue}
                          getValues={getValues}
                          listAddContactSelected={defaultUserSelected}
                          functionContactClient={{
                            contactOptions,
                            contactSetOptions,
                            handleContactScroll,
                            handleContactSearch,
                            isLoadingContacts,
                            setIsLoadingContacts,
                            valueNotFoundContacts,
                            setCompanyId,
                            contactSetStart,
                          }}
                          job={job}
                          fromBullHornSubmissionModal={true}
                          fromSequenseEmail={fromSequenseEmail} // have change - need check
                          isStepMode={true}
                          mailObject={mailObject}
                          setMailObject={setMailObject}
                          isFromVacancy={true}
                          fromCreatingScratchSequence={
                            fromCreatingScratchSequence
                          }
                          fromSequenseDetail={fromSequenseDetail}
                          newUpdatedSequence={true}
                          fromCreateFromTemplateModal={
                            fromCreateFromTemplateModal
                          }
                        />
                      </Form>
                    </div>
                  )}
                </>
                {openSmartEmailDrawer && (
                  <Drawer
                    maskClosable={false}
                    title={
                      <span className="text-2xl ">Choose a existing mail</span>
                    }
                    placement={'right'}
                    width={1000}
                    onClose={closeSmartEmailDrawer}
                    open={openSmartEmailDrawer}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      {inputNumberStep?.length > 0 ? (
                        inputNumberStep.map(
                          (item) =>
                            item?.type === ADD_STEP_TYPE.SEND_MAIL && (
                              // !item?.subject?.includes('RE:') &&
                              <div
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (!item['subject'] || !item['content']) {
                                    notification.warning({
                                      description:
                                        'Can not create a following mail based on empty mail!',
                                    });
                                    return;
                                  }
                                  // const { subject, content } = item;
                                  console.log('dataSignature: ', dataSignature);
                                  const subject = `RE: ${item?.subject}`;
                                  const content = `<br> <br> <br> <br> <br>
                                                ${dataSignature}
                                                <br>
                                                <hr style="border-top:1px solid #98DED9ccc;background:#98DED9ccc;width:100%;margin-top:10px">
                                                <div class="main-threading-email-content">
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>From:</strong> <span class=" w-fit">{{SENDER_NAME}}</span> <<a href="mailto:{{SENDER_EMAIL}}">{{SENDER_EMAIL}}</a>></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>Sent:</strong> <span class=" w-fit">{{SENT_DATE_TIME}}</span></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>To:</strong> <span class=" w-fit">{{RECIPIENT_NAME}}</span> <<a href="mailto:{{RECIPIENT_EMAIL}}">{{RECIPIENT_EMAIL}}</a>></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>Subject:</strong> ${item?.subject}</p>
                                                  <br>
                                                  ${item?.content}      
                                                </div>
                                                `;
                                  setMailObject({
                                    ...mailObject,
                                    subject,
                                    content,
                                    threadId: item?.id || item?.key,
                                  });
                                  setOpenMailEditor(true);
                                  // handleAddNewStep(
                                  //   mailObject.step,
                                  //   ADD_STEP_TYPE.DUPLICATE_MAIL,
                                  //   item
                                  // );
                                  // closeSendMailOptionsDrawer();
                                  closeSmartEmailDrawer();
                                }}
                                className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                              >
                                <span
                                  className="block text-xl font-medium mb-1 line-clamp-1"
                                  title={item?.subject}
                                >
                                  {item?.subject}
                                </span>
                                <div
                                  className="max-h-[20rem] overflow-y-auto"
                                  dangerouslySetInnerHTML={{
                                    __html: item?.content,
                                  }}
                                ></div>
                              </div>
                            )
                        )
                      ) : (
                        <span>There are no existing mails to choose.</span>
                      )}
                    </div>
                  </Drawer>
                )}
              </Drawer>
            )}

          {((openSendMailOptions &&
            preview &&
            previewMode === PREVIEW_MODE.EDIT) ||
            fromCreatingScratchSequence ||
            fromCreateFromTemplateModal) && (
            <Drawer
              maskClosable={false}
              title={
                (fromCreatingScratchSequence ||
                  fromCreateFromTemplateModal) && (
                  <span className="text-2xl ">{`Email Form`}</span>
                )
              }
              closable={false}
              placement={'left'}
              getContainer={false}
              width={fromCreatingScratchSequence ? 890 : 860}
              onClose={closeSendMailOptionsDrawer}
              open={openSendMailOptions}
              footer={
                <div className="flex gap-2 w-full justify-end">
                  <Button
                    onClick={handleSendTestMail}
                    loading={loadingTestEmail}
                    icon={<MailOutlined />}
                    type="primary"
                  >
                    {' '}
                    Send Test Email
                  </Button>
                  <Button
                    type="dashed"
                    className="bg-white"
                    onClick={closeSendMailOptionsDrawer}
                    disabled={loadingTestEmail}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      if (openMailEditorFromStep) {
                        handleEditMailStep();
                      } else {
                        setValue(
                          'sendMail.listEmailSendChoose',
                          getValues('sendMail.listEmailSend')
                        );
                        handleAddNewStep(
                          mailObject.step,
                          ADD_STEP_TYPE.SEND_MAIL
                        );
                      }
                      closeSendMailOptionsDrawer();
                    }}
                    disabled={loadingTestEmail}
                  >
                    Save
                  </Button>
                </div>
              }
            >
              <>
                {!openMailEditor && !openMailEditorFromStep && (
                  <div className="grid grid-cols-2 gap-5 p-5">
                    {/* <div
                      onClick={() => handleOpenTemplateOptions()}
                      className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                    >
                      <span className="block text-xl font-medium mb-1">
                        From Template
                      </span>
                      <span className="text-base text-[#797979]">
                        Create an email starting with an email from your
                        template library
                      </span>
                    </div> */}
                    <div
                      onClick={() => {
                        setMailObject({
                          ...mailObject,
                          subject: '',
                          content: '',
                        });
                        setOpenMailEditor(true);
                      }}
                      className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                    >
                      <span className="block text-xl font-medium mb-1">
                        From Scratch - Plain Text
                      </span>
                      <span className="text-base text-[#797979]">
                        Create a simple email like you'd send from your personal
                        email using plain text
                      </span>
                    </div>
                    <div
                      onClick={() => showSmartEmailDrawer()}
                      className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                    >
                      <span className="block text-xl font-medium mb-1">
                        From RE: Threading
                      </span>
                      <span className="text-base text-[#797979]">
                        Create a following email that based on another mail with
                        purpose to remind recipient.
                      </span>
                    </div>
                  </div>
                )}
                {((openMailEditor && !openMailTemplate) ||
                  openMailEditorFromStep) && (
                  <div>
                    <Form layout="vertical">
                      <BullhornSendEmail
                        control={control}
                        setValue={setValue}
                        getValues={getValues}
                        listAddContactSelected={defaultUserSelected}
                        functionContactClient={{
                          contactOptions,
                          contactSetOptions,
                          handleContactScroll,
                          handleContactSearch,
                          isLoadingContacts,
                          setIsLoadingContacts,
                          valueNotFoundContacts,
                          setCompanyId,
                          contactSetStart,
                        }}
                        job={job}
                        fromBullHornSubmissionModal={true}
                        fromSequenseEmail={fromSequenseEmail} // have change - need check
                        isStepMode={true}
                        mailObject={mailObject}
                        setMailObject={setMailObject}
                        isFromVacancy={true}
                        fromCreatingScratchSequence={
                          fromCreatingScratchSequence
                        }
                        fromSequenseDetail={fromSequenseDetail}
                        newUpdatedSequence={true}
                        previewMode={previewMode}
                        fromCreateFromTemplateModal={
                          fromCreateFromTemplateModal
                        }
                      />
                    </Form>
                  </div>
                )}
              </>
              {openSmartEmailDrawer && (
                <Drawer
                  maskClosable={false}
                  title={
                    <span className="text-2xl ">Choose a existing mail</span>
                  }
                  placement={'right'}
                  width={1000}
                  onClose={closeSmartEmailDrawer}
                  open={openSmartEmailDrawer}
                >
                  <div className="grid grid-cols-2 gap-4">
                    {inputNumberStep?.length > 0 ? (
                      inputNumberStep.map(
                        (item) =>
                          item?.type === ADD_STEP_TYPE.SEND_MAIL && (
                            // !item?.subject?.includes('RE:') &&
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                if (!item['subject'] || !item['content']) {
                                  notification.warning({
                                    description:
                                      'Can not create a following mail based on empty mail!',
                                  });
                                  return;
                                }
                                // const { subject, content } = item;
                                console.log('dataSignature: ', dataSignature);
                                const subject = `RE: ${item?.subject}`;
                                const content = `<br> <br> <br> <br> <br>
                                                ${dataSignature}
                                                <br>
                                                <hr style="border-top:1px solid #98DED9ccc;background:#98DED9ccc;width:100%;margin-top:10px">
                                                <div class="main-threading-email-content">
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>From:</strong> <span class=" w-fit">{{SENDER_NAME}}</span> <<a href="mailto:{{SENDER_EMAIL}}">{{SENDER_EMAIL}}</a>></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>Sent:</strong> <span class=" w-fit">{{SENT_DATE_TIME}}</span></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>To:</strong> <span class=" w-fit">{{RECIPIENT_NAME}}</span> <<a href="mailto:{{RECIPIENT_EMAIL}}">{{RECIPIENT_EMAIL}}</a>></p>
                                                  <p style="font-family:Calibri; font-size: 11pt"><strong>Subject:</strong> ${item?.subject}</p>
                                                  <br>
                                                  ${item?.content}
                                                </div>
                                                `;
                                setMailObject({
                                  ...mailObject,
                                  subject,
                                  content,
                                  threadId: item?.id || item?.key,
                                });
                                setOpenMailEditor(true);
                                closeSmartEmailDrawer();
                              }}
                              className="p-4 border border-[#ccc] cursor-pointer rounded-md bg-white shadow-lg hover:shadow-navy-100"
                            >
                              <span
                                className="block text-xl font-medium mb-1 line-clamp-1"
                                title={item?.subject}
                              >
                                {item?.subject}
                              </span>
                              <div
                                className="max-h-[20rem] overflow-y-scroll pr-2"
                                dangerouslySetInnerHTML={{
                                  __html: item?.content,
                                }}
                              ></div>
                            </div>
                          )
                      )
                    ) : (
                      <span>There are no existing mails to choose.</span>
                    )}
                  </div>
                </Drawer>
              )}
            </Drawer>
          )}

          {/* Modal */}
          {openMailTemplate && (
            <ModalCreateFromTemplate
              onChooseTemplate={onChooseMailTemplate}
              fromBullhornSendEmailModal={true}
              handleCloseTemplateOptions={handleCloseTemplateOptions}
            />
          )}
        </>
      )}
      <ModalWarningSequence
        setOpenWarningModal={setOpenWarningModal}
        openWarningModal={openWarningModal}
      />
      <Modal
        open={openRemoveInValidEmail}
        onCancel={() => setOpenRemoveInValidEmail(false)}
        footer={
          <div>
            <Button
              type="primary"
              onClick={() => {
                setValue(
                  'skippedEmails',
                  hotListCheckInValidData?.invalidEmails
                );
                // setHotListCheckInValidData();
                setOpenRemoveInValidEmail(false);
                setOpenConfirmEnrichData(true);
              }}
            >
              Yes
            </Button>
            <Button
              onClick={() => {
                setHotListCheckInValidData();
                setOpenRemoveInValidEmail(false);
              }}
            >
              No
            </Button>
          </div>
        }
      >
        {/* Thanks for selecting your list we have found{' '}
        {hotListCheckInValidData?.invalidCount} invalid emails would you like to
        remove them from the list */}
        There are {hotListCheckInValidData?.invalidCount} invalid emails. Would
        you like to remove it from sequence?
      </Modal>
      <Modal
        open={openConfirmEnrichData}
        footer={
          <div>
            <Button
              type="primary"
              onClick={() => {
                handleUpsertEmails();
              }}
              loading={loadingEnrichEmails}
            >
              Yes
            </Button>
            <Button
              onClick={() => {
                // setHotListCheckInValidData();
                setOpenConfirmEnrichData(false);
              }}
              disabled={loadingEnrichEmails}
            >
              No
            </Button>
          </div>
        }
      >
        Invalid Contacts removed would you like to enrich these contacts ?
      </Modal>

      {/* Enrich data */}
      <Modal
        title="Bulk Enrich contacts data"
        open={openBulkEnrichModal}
        footer={null}
        width={1200}
        closable={false}
        destroyOnClose={true}
      >
        <BulkEnrichData
          selectedContacts={[
            ...(sendToList ?? [])
              // ?.concat(contactOptions)
              // ?.concat(getValues('sendMail.listEmailSend') ?? [])
              ?.map((option) => {
                return (
                  (option?.id || option?.email) && {
                    ...option,
                    value: (option?.id || option.email) + '_' + option.name,
                    label: option?.name || option?.email,
                    email: option?.email,
                    phone: option?.phone,
                    address: option?.address,
                    status: option?.status,
                    occupation: option?.occupation,
                    name: option?.name,
                    contactId: option?.id,
                  }
                );
              })
              .filter(
                (item, index, self) =>
                  index === self?.findIndex((t) => t?.email === item?.email)
              ),
          ]}
          reloadData={() => {
            handleContactSearch('', getValues('companyId'), 10);
          }}
          closeBulkEnrichModal={closeBulkEnrichModal}
        />
      </Modal>
    </div>
  );
};

export default BullhornSendEmailModal;
