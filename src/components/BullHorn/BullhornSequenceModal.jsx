/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */

import { Button, Form, Input, Select, notification, Tag, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '../../store/auth';
import { getUserById } from '../../services/auth';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import {
  CloseCircleOutlined,
  ContactsOutlined,
  DeleteOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import {
  generateEmailData,
  generateSubject,
  generateContent,
  generateContentChildContent,
  generateContentChildSubject,
} from '../../services/search';
import { CKEditor } from '@ckeditor/ckeditor5-react';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { Spin } from 'antd';
import './bullhornSendEmailModal.scss';
import { sendEmail } from '../../services/notification';
import React from 'react';
import BullhornSendEmailModal from './EmailtriggerStep';

const { TextArea } = Input;

function BullhornSequenceModal(props) {
  const {
    sendToEmail,
    mailTitle,
    openModalSendEmail,
    setOpenSendEmail,
    handleSubmit,
    control,
    setValue,
    getValues,
    listAddContactSelected,
    functionContactClient,
    job,
    setHaveSendJob,
    setIsLockedSendJob,
    isLockedSendJob,
    checkBoxStatus,
    sequenceStatus = false,
    setNumberStep,
    numberStep,
    inputNumberStep,
    setInputNumberStep,
    setEmailConfigData,
    emailConfigData,
    fromSequenseEmail = false,
    newUpdatedSequence = false
  } = props;
  const { profile } = useAuth();
  const [currentViewUser, setCurrentViewUser] = useState();
  const [listEmailSend, setListEmailSend] = useState([]);
  const teleRef = useRef(null);
  const [dataContent, setDataContent] = useState(
    'The content is being processed'
  );
  const [dataSubject, setDataSubject] = useState(
    'The content is being processed'
  );
  // const [isLoading, setIsLoading] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isSubjectLoading, setIsSubjectLoading] = useState(false);
  const [notConfirmStep, setConfirmStep] = useState(false);
  const [notDiscard, setNotDiscard] = useState(true);
  const [emailSeqId, setEmailSeqId] = useState();
  const [contactListSelected, setContactListSelected] = useState([]);

  useEffect(() => {
    handleLoadEffect();
  }, [job, inputNumberStep]);

  const handleLoadEffect = async () => {
    setValue('sendMail.title', mailTitle);
    // handleGetUser(getUserViewAs());
    // if (getValues('sendMail.mailStepParentSubject')) {
    //   setDataContent(
    //     getValues('sendMail.mailStepParentContent') ??
    //       `The content is being processed`
    //   );
    //   setDataSubject(
    //     getValues('sendMail.mailStepParentSubject') ??
    //       `The content is being processed`
    //   );
    //   setValue(
    //     'sendMail.content',
    //     getValues('sendMail.mailStepParentContent') ??
    //       `The content is being processed`
    //   );
    //   setValue(
    //     'sendMail.subject',
    //     getValues('sendMail.mailStepParentSubject') ??
    //       `The content is being processed`
    //   );
    // } else {
    //   await getDataGen();
    // }
    setValue('sendMail.listEmails', [sendToEmail]);
  };

  const handleGetUser = async (id) => {
    const { data } = await getUserById(id);

    setCurrentViewUser(data?.email);
    setValue('sendMail.from', data?.email);
  };

  const defaultUserSelected = (
    listAddContactSelected?.length > 0
      ? listAddContactSelected
      : functionContactClient?.contactOptions
  )?.map((obj) => ({
    label: obj.name,
    value: obj.name,
    disabled: obj.massMailOptOut || obj.email === '' || obj.email === undefined,
    ...obj,
  }));

  useEffect(() => {
    if (getValues('sendMail.mailStepParentMailTo')) {
      setListEmailSend(getValues('sendMail.mailStepParentMailTo') ?? []);
      setValue(
        'sendMail.listEmail',
        getValues('sendMail.mailStepParentMailTo')?.map((data) => data.email)
      );
    }
  }, getValues('sendMail.mailStepParentMailTo'));

  const handleChange = (value, options) => {
    const dataSet = options?.map((obj) => ({
      name: obj.label,
      email: obj.email,
    }));

    setListEmailSend(dataSet);
    setValue(
      'sendMail.listEmail',
      dataSet?.map((data) => data.email)
    );
  };

  const getDataGen = async () => {
    try {
      setIsSubjectLoading(true);
      setIsContentLoading(true);
      if (fromSequenseEmail) {
        const [{ data: subjectData }, { data: contentData }] =
          await Promise.all([
            generateContentChildSubject({
              title: job?.title ?? 'random',
            }),
            generateContentChildContent({
              title: job?.title ?? 'random',
              content: job?.description,
              company: job?.clientCorporation?.name,
              address: job?.address?.address1,
            }),
          ]);
        if (contentData || subjectData) {
          setDataContent(
            contentData?.result?.content ?? `The content is being processed`
          );
          setDataSubject(
            subjectData?.result?.subject ?? `The content is being processed`
          );
          setValue(
            'sendMail.content',
            contentData?.result?.content ?? `The content is being processed`
          );
          setValue(
            'sendMail.subject',
            subjectData?.result?.subject ?? `The content is being processed`
          );
        }
      } else {
        const [{ data: subjectData }, { data: contentData }] =
          await Promise.all([
            generateSubject(job?.job_id || job?.job_board_id || job?.id),
            generateContent({
              jobBoardId: job.job_id || job?.job_board_id || job?.id,
            }),
          ]);
        if (subjectData || contentData) {
          setDataContent(
            contentData?.result?.content ?? `The content is being processed`
          );
          setDataSubject(
            subjectData?.result?.subject ?? `The content is being processed`
          );
          setValue(
            'sendMail.content',
            contentData?.result?.content ?? `The content is being processed`
          );
          setValue(
            'sendMail.subject',
            subjectData?.result?.subject ?? `The content is being processed`
          );
        }
      }
      setIsSubjectLoading(false);
      setIsContentLoading(false);
    } catch (e) {
      setValue('sendMail.subject', `Error occurred when generating`);
      setValue('sendMail.content', `Error occurred when generating`);
      setDataContent(`Error occurred when generating`);
      setDataSubject(`Error occurred when generating`);
      setIsSubjectLoading(false);
      setIsContentLoading(false);
      if ((e?.response?.data?.message || '').includes('should not be empty')) {
        notification.warning({
          message: 'Warning!',
          description: 'Please input the active contact',
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Something went wrong!',
        });
      }
    }
  };
  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  // const handleSendEmail = async () => {
  //   const payload = {
  //     jobBoardId: fromSequenseEmail
  //       ? null
  //       : job?.job_board_id || job?.job_id || job?.id,
  //     recipients: listEmailSend,
  //     isNotSendSequence: sequenceStatus,
  //     primaryMail: {
  //       subject: getValues('sendMail.subject'),
  //       content: getValues('sendMail.content'),
  //       delay: sequenceStatus ? getValues('sendMail.mailStepParent') : 1,
  //     },
  //     externalJobId: fromSequenseEmail ? job?.id : null,
  //   };

  //   try {
  //     // call API emails/send
  //     const { data } = await sendEmail(payload);
  //     if (data?.result?.emailSeqId) {
  //       setEmailSeqId(data?.result?.emailSeqId);
  //     }

  //     if (data?.result?.isRegistered) {
  //       notification.success({
  //         message: 'Success',
  //         description: 'Email has been sent!',
  //       });
  //     } else {
  //       notification.warning({
  //         duration: 10,
  //         message: 'Warning',
  //         description:
  //           'Mail has been sent, but seem your email has not been granted permission to send sequence email. We sent you a guideline to your email. Please follow it then try again',
  //       });
  //     }
  //   } catch (e) {
  //     if ((e?.response?.data?.message || '').includes('should not be empty')) {
  //       notification.error({
  //         message: 'Error!',
  //         description: 'Can not send to inactive contact',
  //       });
  //     } else {
  //       notification.error({
  //         message: 'Error!',
  //         description: e?.response?.data?.message,
  //       });
  //     }
  //   }
  // };

  return (
    <>
      <BullhornSendEmailModal
        control={control}
        setValue={setValue}
        getValues={getValues}
        sequenceStatus={sequenceStatus}
        setConfirmStep={setConfirmStep}
        setNotDiscard={setNotDiscard}
        notDiscard={notDiscard}
        job={job}
        listEmailSend={listEmailSend}
        openModalSendEmail={openModalSendEmail}
        setNumberStep={setNumberStep}
        numberStep={numberStep}
        inputNumberStep={inputNumberStep}
        setInputNumberStep={setInputNumberStep}
        setEmailConfigData={setEmailConfigData}
        emailConfigData={emailConfigData}
        fromSequenseEmail={fromSequenseEmail}
        defaultUserSelected={defaultUserSelected}
        handleChangeEmail={handleChange}
        formOnlySequense={true}
        onHandleSeqId={handleEmailSeqId}
        emailSeqId={emailSeqId}
        newUpdatedSequence={newUpdatedSequence}
        setContactListSelected={setContactListSelected}
        contactListSelected={contactListSelected}
      />

      {/* <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '-35px',
        }}
      >
        <div></div>
        <div className="">
          <Button
            onClick={() => {
              // setSendEmailStatus(false);
              setOpenSendEmail(false);
            }}
            className="ml-auto border-red-500 text-red-500"
          >
            Cancel
          </Button>
          {checkBoxStatus && (
            <Button
              onClick={() => {
                if (checkBoxStatus) {
                  setIsLockedSendJob(false);
                  setOpenSendEmail(false);
                }
              }}
              style={{ marginLeft: '10px' }}
              className="ml-auto border-red-500 text-red-500"
            >
              Save
            </Button>
          )}
          {!checkBoxStatus && (
            <Tooltip
              title={notConfirmStep ? 'Please Confirm your email step' : ''}
              placement="bottom"
            >
              <Button
                htmlType="button"
                onClick={handleSendEmail}
                type="primary"
                style={{ marginLeft: '10px' }}
                disabled={notConfirmStep}
                // loading={sendEmailStatus}
              >
                Send Email{isContentLoading || isSubjectLoading}
              </Button>
            </Tooltip>
          )}
        </div>
      </div> */}
    </>
  );
}

export default BullhornSequenceModal;
