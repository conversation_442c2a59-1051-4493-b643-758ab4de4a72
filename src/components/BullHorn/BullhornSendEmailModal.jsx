/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */

import {
  Button,
  Form,
  Input,
  Select,
  notification,
  Tag,
  Tooltip,
  Modal,
  Dropdown,
  Drawer,
  Space,
  Tabs,
  Radio,
  Table as AntdTable,
  Upload,
  Image as AntdImage,
  Mentions,
} from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '../../store/auth';
import { getUserById } from '../../services/auth';
import { getUserViewAs } from '../../helpers/getUserViewAs';
import {
  CloseCircleOutlined,
  ContactsOutlined,
  FireOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SyncOutlined,
  CopyOutlined,
  ReloadOutlined,
  RocketOutlined,
  CheckOutlined,
  SmallDashOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  FormOutlined,
  UploadOutlined,
  RobotOutlined,
  // WarningOutlined
} from '@ant-design/icons';
import {
  generateEmailData,
  generateSubject,
  generateContent,
  generateContentChildContent,
  generateContentChildSubject,
  checkContactWarning,
  updateSequence,
  generateMailContentOptionsV2,
} from '../../services/search';
import { Spin } from 'antd';
import './bullhornSendEmailModal.scss';
import { sendEmail } from '../../services/notification';
import React from 'react';
import BullhornSendEmailModal, { ADD_STEP_TYPE } from './EmailtriggerStep';
import ModalCreateFromTemplate from '../../containers/Sequence/ModalCreateFromTemplate';
import { searchBullhornData } from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import SignaturesCreateForm from './SignaturesCreateForm';
import { getSignatureDefault, getSignatures } from '../../services/signatures';
import ModalHotList from './ModalHotList';
import aiImage from '../../assets/img/ai.png';
import ModalShowListInContact from '../../containers/Sequence/ModalShowListInContact';
import ReactDOM from 'react-dom';
import { generateAIAssistantMessage } from '../../services/search';
import { v4 as uuid } from 'uuid';
import NylasConfigurations from '../NylasConfigurations';
import { useNavigate } from 'react-router-dom';
import { calendarSvg } from '../../assets/svg/calendar';
import {
  cleanText,
  decodeHtmlEntities,
  replaceSignaturePlaceholder,
  stripHTMLTags,
} from '../../utils/common';
import clsx from 'clsx';
import uploadFileIcon from '../../assets/img/icons/file.png';

import { CKEditor } from '@ckeditor/ckeditor5-react';
import viewToPlainText from '@ckeditor/ckeditor5-clipboard/src/utils/viewtoplaintext';

import {
  Alignment,
  Autoformat,
  Bold,
  CKBox,
  Code,
  Italic,
  Strikethrough,
  Subscript,
  Superscript,
  Underline,
  BlockQuote,
  CloudServices,
  CodeBlock,
  Essentials,
  FindAndReplace,
  Font,
  Heading,
  Highlight,
  HorizontalLine,
  GeneralHtmlSupport,
  AutoImage,
  Image,
  ImageCaption,
  ImageInsert,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  Base64UploadAdapter,
  PictureEditing,
  Indent,
  IndentBlock,
  TextPartLanguage,
  AutoLink,
  Link,
  LinkImage,
  List,
  ListProperties,
  TodoList,
  MediaEmbed,
  Mention,
  PageBreak,
  Paragraph,
  RemoveFormat,
  SpecialCharacters,
  SpecialCharactersEssentials,
  Style,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableProperties,
  TableToolbar,
  TextTransformation,
  WordCount,
  PasteFromOffice,
  ClassicEditor,
  Enter,
  icons,
  BalloonEditor,
} from 'ckeditor5';

import {
  AIAssistant,
  CaseChange,
  ExportPdf,
  ExportWord,
  FormatPainter,
  ImportWord,
  MergeFields,
  MultiLevelList,
  OpenAITextAdapter,
  PasteFromOfficeEnhanced,
  TableOfContents,
} from 'ckeditor5-premium-features';

import 'ckeditor5/ckeditor5.css';
import 'ckeditor5-premium-features/ckeditor5-premium-features.css';

import {
  IGNORE_MERGE_TAGS,
  LICENSE_KEY,
  MERTAGS_DEFINITION,
  REDUCED_MATERIAL_COLORS,
  ZileoCommands,
} from '../../constants/common.constant';
import { uploadFile } from '../../services/users';
import { CustomAITextAdapter } from '../CKEditorPlugins/ZileoAITextAdapter';
import { getLinkS3 } from '../../services/aws';
import { AITags } from '../CKEditorPlugins/AITags';
import { FindLocation } from '../CKEditorPlugins/FindLocation';
import { Location } from '../CKEditorPlugins/InsertLocation';
import { SentencesSKills } from '../CKEditorPlugins/SentencesSKills';
import { BulletPointSkills } from '../CKEditorPlugins/BulletPointSkills';
import { RewriteByZileoAI } from '../CKEditorPlugins/RewriteByZileoAI';
import { GenContent } from '../CKEditorPlugins/GenContent';
import { GenContentScratch } from '../CKEditorPlugins/GenContentScratch';
import { GenSubject } from '../CKEditorPlugins/GenSubject';
import { GenSubjectScratch } from '../CKEditorPlugins/GenSubjectScratch';
import { CustomizedPaste } from '../CKEditorPlugins/CustomizedPaste';
import { EmailAnalyze } from '../CKEditorPlugins/EmailAnalyze';
import ModalAnalyze from './ModalAnalyze';

const MOCK_DATA = {
  '@': ['afc163', 'zombiej', 'yesmeck'],
  '#': ['1.0', '2.0', '3.0'],
};

export const LIMIT_ATTACHMENTS_SIZE = 20000000; // 20mb

export const editorConfig = {
  toolbar: {
    items: [
      // --- Document-wide tools ----------------------------------------------------------------------
      'undo',
      'redo',
      '|',
      'insertMergeField',
      // 'previewMergeFields',
      '|',
      'importWord',
      'exportWord',
      'exportPdf',
      '|',
      'formatPainter',
      'caseChange',
      'findAndReplace',
      // 'selectAll',
      // 'wproofreader',
      // '|',
      // 'insertTemplate',
      'tableOfContents',
      '|',

      // --- "Insertables" ----------------------------------------------------------------------------

      'link',
      'insertImage',
      // 'ckbox',
      'insertTable',
      'blockQuote',
      'mediaEmbed',
      'codeBlock',
      'pageBreak',
      'horizontalLine',
      'specialCharacters',
      '-',

      // --- Block-level formatting -------------------------------------------------------------------
      'heading',
      'style',
      '|',

      // --- Basic styles, font and inline formatting -------------------------------------------------------
      'bold',
      'italic',
      'underline',
      'strikethrough',
      {
        label: 'Basic styles',
        icon: 'text',
        items: [
          'fontSize',
          'fontFamily',
          'fontColor',
          'fontBackgroundColor',
          'highlight',
          'superscript',
          'subscript',
          'code',
          '|',
          'textPartLanguage',
          '|',
        ],
      },
      'removeFormat',
      '|',

      // --- Text alignment ---------------------------------------------------------------------------
      'alignment',
      '|',

      // --- Lists and indentation --------------------------------------------------------------------
      'bulletedList',
      'numberedList',
      'multilevelList',
      // 'todoList',
      '|',
      'outdent',
      'indent',
      '|',
      'aiCommands',
      'aiAssistant',
      '|',
    ],
    shouldNotGroupWhenFull: true,
  },
  plugins: [
    CustomizedPaste,
    GenContent,
    GenContentScratch,
    GenSubject,
    GenSubjectScratch,
    RewriteByZileoAI,
    EmailAnalyze,
    Location,
    SentencesSKills,
    BulletPointSkills,
    FindLocation,
    AITags,
    Enter,
    AIAssistant,
    CustomAITextAdapter,
    Alignment,
    Autoformat,
    AutoImage,
    AutoLink,
    BlockQuote,
    Bold,
    CloudServices,
    Code,
    CodeBlock,
    FindAndReplace,
    Font,
    GeneralHtmlSupport,
    Heading,
    Highlight,
    HorizontalLine,
    Image,
    ImageCaption,
    ImageInsert,
    ImageResize,
    ImageStyle,
    ImageToolbar,
    ImageUpload,
    Base64UploadAdapter,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    ListProperties,
    MediaEmbed,
    Mention,
    PageBreak,
    Paragraph,
    PictureEditing,
    PasteFromOffice,
    RemoveFormat,
    SpecialCharacters,
    SpecialCharactersEssentials,
    Strikethrough,
    Style,
    Subscript,
    Superscript,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextPartLanguage,
    TextTransformation,
    TodoList,
    Underline,
    WordCount,
    ...(LICENSE_KEY
      ? [
          CaseChange,
          ExportPdf,
          ExportWord,
          FormatPainter,
          ImportWord,
          PasteFromOfficeEnhanced,
          MultiLevelList,
          // SlashCommand,
          TableOfContents,
          // Template,
          MergeFields,
        ]
      : []),
    // SaveButtonPlugin,
    // SpecialCharacters,
    // SpecialCharactersEssentials,
    // Essentials,
  ],
  fontFamily: {
    supportAllValues: true,
    options: [
      'Calibri',
      'Montserrat',
      'Arial, Helvetica, sans-serif',
      'Courier New, Courier, monospace',
      'Georgia, serif',
      'Lucida Sans Unicode, Lucida Grande, sans-serif',
      'Tahoma, Geneva, sans-serif',
      'Times New Roman, Times, serif',
      'Trebuchet MS, Helvetica, sans-serif',
      'Verdana, Geneva, sans-serif',
    ],
  },
  fontSize: {
    options: [10, 11, 12, 13, 14, 15, 'default', 17, 18, 19, 20, 22],
    supportAllValues: true,
  },
  fontColor: {
    columns: 12,
    colors: REDUCED_MATERIAL_COLORS,
  },
  fontBackgroundColor: {
    columns: 12,
    colors: REDUCED_MATERIAL_COLORS,
  },
  heading: {
    options: [
      { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
      {
        model: 'heading1',
        view: 'h1',
        title: 'Heading 1',
        class: 'ck-heading_heading1',
      },
      {
        model: 'heading2',
        view: 'h2',
        title: 'Heading 2',
        class: 'ck-heading_heading2',
      },
      {
        model: 'heading3',
        view: 'h3',
        title: 'Heading 3',
        class: 'ck-heading_heading3',
      },
      {
        model: 'heading4',
        view: 'h4',
        title: 'Heading 4',
        class: 'ck-heading_heading4',
      },
      {
        model: 'heading5',
        view: 'h5',
        title: 'Heading 5',
        class: 'ck-heading_heading5',
      },
      {
        model: 'heading6',
        view: 'h6',
        title: 'Heading 6',
        class: 'ck-heading_heading6',
      },
    ],
  },
  htmlSupport: {
    allow: [
      // Enables all HTML features.
      {
        name: /.*/,
        attributes: true,
        classes: true,
        styles: true,
      },
    ],
    disallow: [
      {
        attributes: [
          { key: /^on(.*)/i, value: true },
          {
            key: /.*/,
            value: /(\b)(on\S+)(\s*)=|javascript:|(<\s*)(\/*)script/i,
          },
          { key: /.*/, value: /data:(?!image\/(png|jpeg|gif|webp))/i },
        ],
      },
      { name: 'script' },
    ],
  },
  licenseKey: LICENSE_KEY,
  image: {
    resizeOptions: [
      {
        name: 'resizeImage:original',
        label: 'Default image width',
        value: null,
      },
      {
        name: 'resizeImage:50',
        label: '50% page width',
        value: '50',
      },
      {
        name: 'resizeImage:75',
        label: '75% page width',
        value: '75',
      },
    ],
    toolbar: [
      'imageTextAlternative',
      'toggleImageCaption',
      '|',
      'imageStyle:inline',
      'imageStyle:wrapText',
      'imageStyle:breakText',
      '|',
      'resizeImage',
    ],
    insert: {
      integrations: ['url'],
    },
  },
  list: {
    properties: {
      styles: true,
      startIndex: true,
      reversed: true,
    },
  },
  link: {
    decorators: {
      toggleDownloadable: {
        mode: 'manual',
        label: 'Downloadable',
        attributes: {
          download: 'file',
        },
      },
    },
    addTargetToExternalLinks: true,
    defaultProtocol: 'https://',
  },
  placeholder: 'Type or paste your content here!',
  style: {
    definitions: [
      {
        name: 'Title',
        element: 'h1',
        classes: ['document-title'],
      },
      {
        name: 'Subtitle',
        element: 'h2',
        classes: ['document-subtitle'],
      },
      {
        name: 'Callout',
        element: 'p',
        classes: ['callout'],
      },
      {
        name: 'Side quote',
        element: 'blockquote',
        classes: ['side-quote'],
      },
      {
        name: 'Needs clarification',
        element: 'span',
        classes: ['needs-clarification'],
      },
      {
        name: 'Wide spacing',
        element: 'span',
        classes: ['wide-spacing'],
      },
      {
        name: 'Small caps',
        element: 'span',
        classes: ['small-caps'],
      },
      {
        name: 'Code (dark)',
        element: 'pre',
        classes: ['stylish-code', 'stylish-code-dark'],
      },
      {
        name: 'Code (bright)',
        element: 'pre',
        classes: ['stylish-code', 'stylish-code-bright'],
      },
    ],
  },
  table: {
    contentToolbar: [
      'tableColumn',
      'tableRow',
      'mergeTableCells',
      'tableProperties',
      'tableCellProperties',
      'toggleTableCaption',
    ],
  },
  // menuBar: {
  //   isVisible: true,
  // },
  mergeFields: {
    definitions: [...MERTAGS_DEFINITION],
    initialPreviewMode: '$defaultValues',
  },
  ai: {
    openAI: {
      apiUrl: '/open-ai/generate',
    },
    aiAssistant: {
      commands: [...ZileoCommands],
    },
  },
};

const { TextArea } = Input;
function BullhornSendEmail(props) {
  const {
    sendToEmail,
    mailTitle,
    openModalSendEmail,
    setOpenSendEmail,
    handleSubmit,
    control,
    setValue,
    getValues,
    listAddContactSelected,
    functionContactClient,
    job,
    setHaveSendJob,
    setIsLockedSendJob,
    isLockedSendJob,
    checkBoxStatus,
    sequenceStatus = false,
    setNumberStep,
    numberStep,
    inputNumberStep,
    setInputNumberStep,
    setEmailConfigData,
    emailConfigData,
    fromSequenseEmail = false,
    loadingDataEmail = false,
    preview = false,
    previewMode = null,
    onSubmitTemplate = null,
    onlyEdit = false,
    seqId,
    fromCreateFromTemplateModal = false,
    fromManualCreate = false,
    fromBullHornSubmissionModal = false,
    isStepMode = false,
    mailObject = null,
    setMailObject = null,
    isFromVacancy = false,
    hostListOther = null,
    fromSequenseDetail = false,
    fromCreatingScratchSequence = false,
    fromSequenceAI = false,
    notLoadingData = false,
    newUpdatedSequence = false,
    closeAllModal = null,
    fromCreateByVacancy = false,
    selectedSequenceValue = null,
    fromCreateTemplate = false,
    templateId = false,
    setPreviewEmail = null,
    isDuplicateTemplate = false,
    handleReset,
    handleClose,
    templateName,
    watch,
    closeCreateSequenceModal,
    reloadSequenceDetail = () => {},
    fromCandidateSequence = false,
  } = props;
  const [prefix, setPrefix] = useState('@');

  const onSearch = (_, newPrefix) => {
    setPrefix(newPrefix);
  };

  const timeoutRef = useRef(null);
  const jobLocationCity = job?.joblocationcity?.includes(',')
    ? job?.joblocationcity?.split(',')[0]
    : job?.joblocationcity;

  const [emailSeqId, setEmailSeqId] = useState(null);
  const [dataSignature, setDataSignature] = useState('');
  const [loadingSignature, setLoadingSignature] = useState(true);
  const [openModalAnalyze, setOpenModalAnalyze] = useState(false);
  const [plainTextContent, setPlainTextContent] = useState('');
  const editor = useRef(null);

  useEffect(() => {
    if (!seqId) return;
    const sequenceId = !isDuplicateTemplate ? seqId : null;
    setEmailSeqId(sequenceId);
    return () => {};
  }, [seqId]);

  const onSubmitTriggers = (triggerItem) => {
    if (onSubmitTemplate) {
      const templateContent = {
        triggerItem: { ...triggerItem },
        mailDefine: getValues(),
      };

      onSubmitTemplate(templateContent);
    }
  };

  const { profile } = useAuth();
  const [currentViewUser, setCurrentViewUser] = useState();
  const [listEmailSend, setListEmailSend] = useState([]);
  const teleRef = useRef(null);
  const [dataContent, setDataContent] = useState('');
  const [dataSubject, setDataSubject] = useState();
  // const [isLoading, setIsLoading] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isSubjectLoading, setIsSubjectLoading] = useState(false);
  const [notConfirmStep, setConfirmStep] = useState(false);
  const [notDiscard, setNotDiscard] = useState(true);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [dataEmailWarning, setDataEmailWarning] = useState([]);
  const [dataEmailWarningSelect, setDataEmailWarningSelect] = useState([]);
  const [dataDomainWarning, setDataDomainWarning] = useState([]);
  const [dataDomainWarningSelect, setDataDomainWarningSelect] = useState([]);
  const [dataDomainWarningLabel, setDataDomainWarningLabel] = useState('');
  const [dataEmailWarningLabel, setDataEmailWarningLabel] = useState('');
  const [openListDomainEmail, setOpenListDomainEmail] = useState(false);
  const [listEmailDomainDuplicate, setListEmailDomainDuplicate] = useState([]);
  const [contactListSelected, setContactListSelected] = useState([]);
  const [listContactSelected, setListContactSelected] = useState([]);
  // const [contactListSelected, setContactListSelected] = useState([]);

  // Sequence Template features
  const [openSequenceTemplateOptions, setOpenSequenceTemplateOptions] =
    useState(false);
  const [isContentOnly, setContentOnly] = useState(true);

  // Find locations function
  const [locationList, setLocationlist] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [isFindLocationOpen, setFindLocationOpen] = useState(false);

  const showFindLocationModal = () => setFindLocationOpen(true);
  const closeFindLocationModal = () => setFindLocationOpen(false);

  const handleFindLocationOk = () => {
    closeFindLocationModal();
  };
  const handleFindLocationCancel = () => {
    closeFindLocationModal();
  };
  // Warning modal
  const [isWarningModalOpen, setWarningModalOpen] = useState(false);

  const showWarningModal = () => setWarningModalOpen(true);
  const closeWarningModal = () => setWarningModalOpen(false);

  const handleWarningModalOk = () => {
    closeWarningModal();
  };

  //Scheduling editor modal
  const [schedulingEditorModalOpen, setSchedulingEditorModalOpen] =
    useState(false);
  const showSchedulingEditorModal = () => setSchedulingEditorModalOpen(true);
  const closeSchedulingEditorModal = () => setSchedulingEditorModalOpen(false);

  // Attachment features

  const [fileList, setFileList] = useState([]);

  const uploadAttachments = async (options) => {
    const { onSuccess, onError, file } = options;

    const fileListTemp = [...mailObject?.fileList];
    const fileSizeList = fileListTemp?.map((item) => parseInt(item?.size || 0));
    const totalSize = fileSizeList.reduce(
      (accumulator, currentValue) => accumulator + currentValue,
      0
    );
    const isAllow = totalSize < LIMIT_ATTACHMENTS_SIZE;
    if (!isAllow) {
      const uploadedFile = {
        uid: uuid(),
        name: file?.name || uuid(),
        status: 'failed',
      };
      onError(
        {
          event: { status: 500 },
        },
        { ...uploadedFile }
      );
      notification.error({
        description: 'Reaching size limitation (20mb)!',
      });
      return;
    }
    try {
      let formData = new FormData();

      formData.append('file', file);
      const { data } = await uploadFile(formData);
      const responsFile = data?.result?.data || null;

      if (responsFile) {
        const uploadedFile = {
          uid: uuid(),
          name: file?.name || uuid(),
          status: 'done',
          url: responsFile?.url,
          fileId: responsFile?.fileId,
        };
        onSuccess({
          ...uploadedFile,
        });
      } else {
        onError({ event: { status: 500 } });
      }
    } catch (error) {
      onError({ event: error });
    }
  };

  const onChangeAttachments = ({ file, fileList }) => {
    const fileListTemp = [...fileList];
    const fileSizeList = fileListTemp?.map((item) => parseInt(item?.size || 0));
    const totalSize = fileSizeList.reduce(
      (accumulator, currentValue) => accumulator + currentValue,
      0
    );
    const isAllow = totalSize < LIMIT_ATTACHMENTS_SIZE;
    if (!isAllow) {
      notification.error({
        description: 'Reaching size limitation (20mb)!',
      });
      return;
    }
    if (file?.status === 'removed' || file?.response?.status === 'failed') {
      const newFileList = [...fileList]?.filter(
        (item) => item?.uid !== file?.uid
      );
      setMailObject({
        ...mailObject,
        fileList: [...newFileList],
      });
      return;
    }
    const uploadedFileList = fileList
      ?.map((item) => ({
        ...item,
        url: item?.response?.url || '',
        fileId: item?.response?.fileId || '',
      }))
      ?.filter((item) => item?.response?.status !== 'failed');
    setMailObject({
      ...mailObject,
      fileList: [...uploadedFileList],
    });
  };

  // AI generator state
  const startedEmail =
    job?.jobtype !== 'remote' && !job?.jobtype?.includes('remote')
      ? `Should be an introduction, It will tell the recruiter that "I am currently working with a ${job?.jobtitle || ''} who is located around 30 minutes away from your office in ${jobLocationCity || ''} and has asked me to find a suitable position which is local".`
      : `Should be an introduction, It will tell the recruiter that "I am currently collaborating with a highly skilled in this field (as the description) who is eager to explore new opportunities.
     Candidate has expressed a preference for a remote work setup, and I am actively searching for a position that aligns with the candidate expertise and the flexibility of working remotely`;

  const aiGeneratorList = {
    genSubject: {
      title: 'Zileo Subject Generator',
      prompt: `I need the email subject. The subject needs to come from resource provider to recruiter or companies who are recruiting so we have candidates to to fill this vacancy that the company has posted on the job platform and related to the field: ${job?.jobtitle || job?.title || ''}. It should be professional, succinct, attractive but still humble.
      The field's description may contain role. You have to analyze the field's description and do not focus on roles. Let focus on the position
      NOTE: raw subject without the word subject at the beginning and double quotes or special character`,
      replace: 'subject',
      type: 'JOB_REVIEW',
      addonAfter: '',
    },
    genContent: {
      title: 'Zileo Content Generator',
      prompt: `Act as an email provider. Create an email from bellow pure template HTML.\n
      This email needs to come from resource provider to recruiter or companies who are recruiting so we have candidates to to fill this vacancy that the company has posted on the job platform and related to the field.\n
        {<div>
          <p class="bg-purple-400 w-fit">Dear {{RECIPIENT_NAME}},
          <p>$startedEmail</p>
          <p><strong>A few main points on this candidate's skill set:</strong></p>
          <br>
          <ul id="skills-id" class="list-disc" style="padding: 0 0 0 30px;">
              $keyStrengthsList: must be 8 bullet points highlighting my key strengths and qualifications for applying to this company, and make it more professional than the unfinished content.
          </ul>
          <br>
          <p> $activeLocation </p>
          <p> $arrangement </p>
          <p>Best Regards,</p>
          <br>
        </div>}
        \n
        (end the template)
        \n
        Here is job description:\n
        ${job?.description}
        \n\n
        Here are requirements:\n
        $startedEmail: ${startedEmail}\n
        $activeLocation: generate this part which tell the recruiter about candidate is actively interviewing in ${jobLocationCity || ''} and extremely suitable for ${job?.position || ''} position which they looking for. express by your own way\n
        $arrangement: generate this part which politely tell the recruiter that you are so happy if they can arrange time to discuss about this candidate if possible. Use formal and professional intonation\n
        Replace $keyStrengthsList, $startedEmail, $activeLocation, $arrangement with generated results, only return the fulfilled template with out {} and do not include "\n" character to my html email
        No yapping!
        Note: Remember ONLY <li> tag must have class="bg-red-500 w-fit" and all others html tag (except for <ul> tag) have to have a
        `,
      replace: 'content',
      type: 'JOB_REVIEW',
      addonAfter: '',
    },
    skills: {
      title: 'bullet-point-skills',
      prompt: `Extract the raw skills from this vacancy description: ${job?.description || ''} without adding additional information
      Note: Remember the result that return to me must follow template with html tag start with <ul id="skills-id" class="list-disc" style="padding: 0 0 0 30px;"> tag and 
      all skill descripion in <li> tag (and every <li> tag have to have a class="bg-red-500 w-fit", only return the fulfilled template with out {} and do not include "\n" character to my html email
      No yapping!
      `,
      replace: null,
      addonAfter: '',
    },
    sentencesSkills: {
      title: 'sentences-skills',
      prompt: `Extract the raw skills from this vacancy description: ${job?.description || ''} then give me a greater way to describe about vacancy skills. \n
      Note: Remember the result that return to me must be covered by <p id="skills-id" class="bg-red-500 w-fit"></p> tag without adding additional information. \n`,
      //and must follow template covered by <p class="bg-red-500 w-fit"></p> tag without adding additional information
      replace: null,
      addonAfter: '',
    },
    genSubjectScratch: {
      title: 'Zileo Subject Generator',
      prompt: '',
      replace: 'subject',
      type: 'SCRATCH',
      addonAfter: '\n Remember:No yapping!',
    },
    genContentScratch: {
      title: 'Zileo Content Generator',
      prompt: '',
      replace: 'content',
      type: 'SCRATCH',
      addonAfter:
        '\n Note: Remember the result that return to me must follow template with html tag start with <div> tag and there are no colored on them \n No yapping! ',
    },
  };
  const [isModalAIGeneratorOpen, setIsModalAIGeneratorOpen] = useState(false);
  const [isModalAIGeneratorLoading, setIsModalAIGeneratorLoading] =
    useState(true);

  const [openPromptModal, setOpenPromptModal] = useState(false);
  const showPromptModal = () => setOpenPromptModal(true);
  const closePromptModal = () => setOpenPromptModal(false);

  const [aiGeneratorInfor, setAIGeneratorInfor] = useState({
    title: '',
    editor: null,
    prompt: '',
    results: [],
    tone: 'Natural',
    type: 'Soft',
    language: 'English',
    replace: '',
    addonAfter: '',
  });
  const [firstOption, setFirstOption] = useState('');
  const [secondOption, setSecondOption] = useState('');
  const [thirdOption, setThirdOption] = useState('');
  const [loadingStatusUpdateSequence, setLoadingStatusUpdateSequence] =
    useState(false);

  const generateAIAssistantMessageFunc = (
    aiGeneratorInforProps,
    regeneratedOPtionIndex = -1
  ) => {
    setIsModalAIGeneratorLoading(true);
    const addonBefore =
      aiGeneratorInfor.replace === 'content'
        ? `Act as an email provider. Create an email from bellow pure template HTML. \n`
        : '';
    const messages = [
      {
        role: 'user',
        content:
          `${aiGeneratorInforProps?.replace && `${addonBefore} Edit this Email ${aiGeneratorInforProps.replace} ${aiGeneratorInforProps.replace === 'content' ? '(Note: only generate the email content not include email subject and must be in html format no yapping)' : ''} only to give it a ${aiGeneratorInforProps.type} and ${aiGeneratorInforProps.tone} tone in ${aiGeneratorInforProps.language} language :`} ${aiGeneratorInforProps.prompt}` +
          `\n${aiGeneratorInforProps.addonAfter}`,
      },
    ];

    const payload = {
      messages,
      numOfChoice: regeneratedOPtionIndex !== -1 ? 1 : 3,
      // stream: true
    };
    generateAIAssistantMessage(payload)
      .then((res) => {
        const { data } = res;
        if (data?.choices?.length > 0) {
          const { choices } = data;
          const results = choices.map((result) => {
            const jobLocation = jobLocationCity || '';
            const content =
              result?.message?.content
                ?.replaceAll('"', '')
                .replaceAll('```html', '')
                .replaceAll('```', '') || '';

            if (!jobLocation) {
              return aiGeneratorInforProps?.replace === 'subject'
                ? stripHTMLTags(content)
                : content;
            } else {
              return content.replaceAll(
                jobLocation,
                `&nbsp;<span id="location-id" class="bg-red-500 w-fit">${job?.joblocationcity?.toString()}</span>&nbsp;`
              );
            }
          });
          if (regeneratedOPtionIndex !== -1) {
            switch (regeneratedOPtionIndex) {
              case 0:
                setFirstOption(results[0]);
                break;
              case 1:
                setSecondOption(results[0]);
                break;
              case 2:
                setThirdOption(results[0]);
                break;
            }
            notification.success({
              description: 'Reload successfully!',
            });
          } else {
            setAIGeneratorInfor({ ...aiGeneratorInforProps, results });
            setFirstOption(results[0]);
            setSecondOption(results[1]);
            setThirdOption(results[2]);
          }
        }
      })
      .catch((err) => {
        console.error(err);
        notification.error({
          message: 'Please try again later!',
        });
      })
      .finally(() => {
        if (regeneratedOPtionIndex !== -1) {
          const iconId = `reload-icon-${regeneratedOPtionIndex}`;
          toggleSpinIcon(iconId);
        }
        setIsModalAIGeneratorLoading(false);
        setOpenPromptModal(false);
      });
  };

  const processToMailContentOptions = (
    data,
    regeneratedOPtionIndex,
    aiGeneratorInforProps
  ) => {
    if (data?.result?.length > 0) {
      const { result } = data;

      const coloredResulted = result.map((item) => {
        const dearPart = `Dear <span class="merge-tag-display">{{RECIPIENT_NAME}}</span>,`;
        const startingPart = `${item?.introduction}`
          .replaceAll(
            '{job_title}',
            job?.jobtitle ||
              getValues('jobtitle') ||
              `*FILL_YOUR_JOB_TITLE_HERE*`
          )
          .replaceAll(
            '{job_location}',
            `&nbsp;<span id="location-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${job?.joblocationcity?.toString() || getValues('address1') || getValues('address2') || `*FILL_YOUR_JOB_LOCATION_HERE*`}</span>&nbsp;`
          );
        const breaker = ` <strong>A few main points on this candidate's skill set:</strong>`;
        const skillsPart = `<ul id="skills-id" style="padding: 0 0 0 30px;list-style-type: disc;" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">
                        ${
                          item?.skills?.length > 0
                            ? item?.skills
                                ?.filter((skill) => skill)
                                ?.map((skill) => `<li>${skill}</li>`)
                                .join('')
                            : '{{JOB_REQUIRED_SKILLS_HERE}}'
                        }
                    </ul>`;

        const endingPart = item?.conclusion?.includes('\n')
          ? item?.conclusion
              ?.split(`\n`)
              .map((text, index) =>
                `${text}`
                  .replaceAll(
                    '{job_title}',
                    job?.job_title ||
                      getValues('jobtitle') ||
                      `*FILL_YOUR_JOB_TITLE_HERE*`
                  )
                  .replaceAll(
                    '{job_location}',
                    `&nbsp;<span id="location-id"  class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${job?.joblocationcity?.toString() || getValues('address1') || getValues('address2') || `*FILL_YOUR_JOB_LOCATION_HERE*`}</span>&nbsp;&nbsp;`
                  )
              )
              .join('')
          : `${item?.conclusion}`
              .replaceAll(
                '{job_title}',
                job?.job_title ||
                  getValues('jobtitle') ||
                  `*FILL_YOUR_JOB_TITLE_HERE*`
              )
              .replaceAll(
                '{job_location}',
                `&nbsp;<span id="location-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${job?.joblocationcity?.toString() || getValues('address1') || getValues('address2') || `*FILL_YOUR_JOB_LOCATION_HERE*`}</span>&nbsp;&nbsp;`
              );
        const regardPart = `Best Regards,`;
        const option = `<div  class="outline outline-[1px] outline-purple-400 hover:outline-purple-600 hover:outline-[2px] p-1">${dearPart}<br><br>  ${startingPart}<br><br> ${breaker}<br> ${skillsPart}<br> ${endingPart}<br><br> ${regardPart}</div>`;
        return option;
      });

      const results = [...coloredResulted];
      if (regeneratedOPtionIndex !== -1) {
        switch (regeneratedOPtionIndex) {
          case 0:
            setFirstOption(results[0]);
            break;
          case 1:
            setSecondOption(results[0]);
            break;
          case 2:
            setThirdOption(results[0]);
            break;
        }
        notification.success({
          description: 'Reload successfully!',
        });
      } else {
        setAIGeneratorInfor({ ...aiGeneratorInforProps, results });
        setFirstOption(results[0]);
        setSecondOption(results[1]);
        setThirdOption(results[2]);
      }
    } else {
      notification.error({
        message: 'Please try again later!',
      });
    }
  };

  const generateMailContentOptionFromManualLeads = async (
    mailOptionPayload,
    regeneratedOPtionIndex,
    aiGeneratorInforProps
  ) => {
    try {
      // create lead from Manual lead
      const requiredSkills = getValues('skills')?.map((skill) => skill?.label);
      const isExistingRequeredSkills = requiredSkills?.length > 0;
      const prompt = `Extract the raw skill from this vacancy description: ${getValues('description')} ${isExistingRequeredSkills && `with adding additional required skills: ${requiredSkills.join(', ')}\n`}
  Note: Return an array of text only.\n
  No yapping!`;

      const messages = [
        {
          role: 'user',
          content: prompt,
        },
      ];

      const payload = {
        messages,
        numOfChoice: 1,
      };

      const { data } = await generateAIAssistantMessage(payload);

      const { choices } = data;
      const results = choices.map((result) => result?.message?.content || '');

      const parsedResults = JSON.parse(results);
      const isResultCorrect = parsedResults?.length > 0;
      let genMailOptionsPayload;
      if (isResultCorrect) {
        setValue('jobskills', parsedResults);
        genMailOptionsPayload = {
          ...mailOptionPayload,
          skills: [...parsedResults],
        };
      } else {
        genMailOptionsPayload = {
          ...mailOptionPayload,
          skills: getValues('skills').map((skill) => skill?.label),
        };
      }
      generateContentLightVersion(
        genMailOptionsPayload,
        regeneratedOPtionIndex,
        aiGeneratorInforProps
      );
    } catch (error) {
      notification.error({
        description: 'There something wrong! Contact admins',
      });
      setIsModalAIGeneratorLoading(false);
    }
  };

  const generateContentLightVersion = async (
    data,
    regeneratedOPtionIndex = -1,
    aiGeneratorInforProps
  ) => {
    setIsModalAIGeneratorLoading(true);
    if (!data.skills) {
      generateMailContentOptionFromManualLeads(
        data,
        regeneratedOPtionIndex,
        aiGeneratorInforProps
      );
    } else {
      await generateMailContentOptionsV2(data)
        .then(({ data }) =>
          processToMailContentOptions(
            data,
            regeneratedOPtionIndex,
            aiGeneratorInforProps
          )
        )
        .catch((err) => {
          console.log('errerrerr: ', err);
          notification.error({
            message: 'Please try again later!',
          });
        })
        .finally(() => {
          if (regeneratedOPtionIndex !== -1) {
            const iconId = `reload-icon-${regeneratedOPtionIndex}`;
            toggleSpinIcon(iconId);
          }
          setIsModalAIGeneratorLoading(false);
        });
    }
  };

  const showAIGeneratorModal = () => {
    setIsModalAIGeneratorOpen(true);
  };
  const handleAIGeneratorOk = () => {
    generateAIAssistantMessageFunc(aiGeneratorInfor);
    // setIsModalAIGeneratorOpen(false);
  };
  const handleAIGeneratorCancel = () => {
    setIsModalAIGeneratorOpen(false);
  };

  const fetchFileUrl = async (fileId) => {
    if (!fileId) return '';
    try {
      const { data } = await getLinkS3(fileId);
      return data;
    } catch (error) {
      return '';
    }
  };

  const updateUrlFiles = async () => {
    const fileListTemp = [...(mailObject?.fileList || [])];
    const isMissingUrl = fileListTemp.some((file) => !file?.url);
    if (isMissingUrl) {
      const fileListWithUrl = await Promise.all(
        fileListTemp.map(async (file) => {
          if (file?.url) return { ...file };
          const url = await fetchFileUrl(file?.fileId);
          return { ...file, url };
        })
      );

      setMailObject({
        ...mailObject,
        fileList: fileListWithUrl,
      });
    }
  };

  useEffect(() => {
    updateUrlFiles();
  }, [JSON.stringify(mailObject?.fileList)]);

  useEffect(() => {
    if (!loadingDataEmail && emailConfigData && !preview && !onSubmitTemplate) {
      handleLoadEffect();
    }
    return () => {};
  }, [job, openModalSendEmail, emailConfigData]);

  useEffect(() => {
    if (!dataContent) return;
    setValue('sendMail.content', dataContent);
    setValue('sendMail.mailStepParentContent', dataContent);
    if (mailObject) {
      setMailObject({
        ...mailObject,
        content: dataContent,
      });
    }
    return () => {};
  }, [dataContent]);

  useEffect(() => {
    if (!dataSubject) return;
    setValue('sendMail.subject', dataSubject);
    setValue('sendMail.mailStepParentSubject', dataSubject);
    if (mailObject) {
      setMailObject({
        ...mailObject,
        subject: dataSubject,
      });
    }
    return () => {};
  }, [dataSubject]);

  const handleLoadEffectEditTemplateContent = () => {
    setDataContent(getValues('sendMail.mailStepParentContent') || '');
    setDataSubject(getValues('sendMail.subject') || '');
    setValue(
      'sendMail.content',
      getValues('sendMail.mailStepParentContent') || ''
    );
    setValue('sendMail.subject', getValues('sendMail.subject') || '');
    setValue('sendMail.from', getValues('sendMail.from'));
    // setValue('sendMail.listEmails', [sendToEmail]);
  };

  useEffect(() => {
    if (preview || onSubmitTemplate) {
      handleLoadEffectEditTemplateContent();
    }
    return () => {};
  }, [preview]);

  useEffect(() => {
    if (fromManualCreate) {
      setDataContent(dataSignature ?? '');
      setDataSubject('');
      setValue('sendMail.content', dataSignature ?? '');
      setValue('sendMail.subject', '');
    }
    return () => {};
  }, [fromManualCreate]);

  const handleGetData = async () => {
    setLoadingSignature(true);
    try {
      const { data } = await getSignatureDefault();
      if (data) {
        // const newUrl = replaceImagePlaceholders(
        //   data?.result?.sigContent,
        //   data?.result?.imgMetadata
        // );
        // const signatureEl = `<span class="signature-container"><span>${data?.result?.sigContent}</span></span>`;
        const signatureEl = data?.result?.sigContent;
        setDataSignature(signatureEl || '');
        setLoadingSignature(false);
      }
    } catch (error) {
      setLoadingSignature(false);
    }
  };

  useEffect(() => {
    if (!notLoadingData) {
      handleGetData();
    }
    return () => {};
  }, []);

  useEffect(() => {
    if (mailObject?.content && dataSignature) {
      let newContent = replaceSignaturePlaceholder(
        mailObject?.content || '',
        dataSignature
      );
      // Migratin' the old data
      setDataContent(newContent);
    }
  }, [mailObject?.id || mailObject?.key]);

  const handleLoadEffect = async () => {
    setValue('sendMail.title', mailTitle);
    handleGetUser(getUserViewAs());
    if (emailConfigData && emailConfigData?.length > 0) {
      setDataContent(
        getValues('sendMail.mailStepParentContent') ??
          `The content is being processed`
      );
      setDataSubject(
        getValues('sendMail.mailStepParentSubject') ||
          getValues('sendMail.subject') ||
          `The content is being processed`
      );
      setValue(
        'sendMail.content',
        getValues('sendMail.mailStepParentContent') ??
          `The content is being processed`
      );
      setValue(
        'sendMail.subject',
        getValues('sendMail.mailStepParentSubject') ??
          `The content is being processed`
      );
    } else {
      if (getValues('sendMail.content') || getValues('sendMail.subject')) {
        setDataContent(getValues('sendMail.content') || '');
        setDataSubject(getValues('sendMail.subject') || '');
        return;
      }
      // await getDataGen();
    }
    setValue('sendMail.listEmails', [sendToEmail]);
  };

  const handleGetUser = async (id) => {
    const { data } = await getUserById(id);

    setCurrentViewUser(data?.email);
    setValue('sendMail.from', data?.email);
  };

  useEffect(() => {
    if (getValues('sendMail.mailStepParentMailTo')) {
      if (getValues('sendMail.mailStepParentMailTo')?.length > 0) {
        setValue(
          'sendMail.listEmail',
          getValues('sendMail.mailStepParentMailTo')?.map((data) => data.email)
        );
        setListEmailSend(getValues('sendMail.mailStepParentMailTo') ?? []);
        setValue(
          'sendMail.listEmailSend',
          getValues('sendMail.mailStepParentMailTo') ?? []
        );
      }
    }

    return () => {};
  }, [getValues('sendMail.mailStepParentMailTo')]);

  const defaultUserSelected = (
    getValues('sendMail.listEmailSendChoose') &&
    getValues('sendMail.listEmailSendChoose')?.length > 0
      ? getValues('sendMail.listEmailSendChoose')
      : listContactSelected && listContactSelected?.length > 0
        ? listContactSelected
        : listAddContactSelected?.length > 0
          ? listAddContactSelected
          : functionContactClient?.contactOptions
  )?.map((obj) => ({
    label: obj.name,
    value: obj.name,
    disabled: obj.massMailOptOut || obj.email === '' || obj.email === undefined,
    ...obj,
  }));

  const refreshContent = async () => {
    const dataToGen = {
      jobBoardId: job?.job_id || job?.job_board_id || job?.id,
      unfinishedContent: dataContent,
    };

    try {
      setIsContentLoading(true);
      let data;
      if (fromSequenseEmail) {
        data = await generateContentChildContent({
          title: (job?.title || job?.jobtitle) ?? 'random',
          content: job?.description ?? 'random',
          company: job?.clientCorporation?.name,
          address: job?.address?.address1,
        });
      } else {
        data = await generateContent(dataToGen);
      }
      setDataContent(
        (data?.data?.result?.content ?? `The content is being processed`) +
          (dataSignature ?? '')
      );
      if (mailObject) {
        setMailObject({
          ...mailObject,
          content:
            (data?.data?.result?.content ?? `The content is being processed`) +
            (dataSignature ?? ''),
        });
      }
      setValue(
        'sendMail.content',
        data?.data?.result?.content
          ? data?.data?.result?.content + (dataSignature ?? '')
          : `The content is being processed`
      );
      setIsContentLoading(false);
    } catch (e) {
      setIsContentLoading(true);
      setValue('sendMail.content', `The content is being processed`);
      notification.error({
        message: 'Error!',
        description: 'Something went wrong!',
      });
    }
  };

  const refreshSubject = async () => {
    try {
      setIsSubjectLoading(true);
      setValue('sendMail.subject', `The content is being processed`);
      let data;
      if (fromSequenseEmail) {
        data = await generateContentChildSubject({
          title: (job?.title || job?.jobtitle) ?? 'random',
        });
      } else {
        data = await generateSubject(
          job?.job_id || job?.job_board_id || job?.id
        );
      }
      setDataSubject(
        data?.data?.result?.subject ?? `The content is being processed`
      );
      if (mailObject) {
        setMailObject({
          ...mailObject,
          subject:
            data?.data?.result?.subject ?? `The content is being processed`,
        });
      }
      setValue(
        'sendMail.subject',
        data?.data?.result?.subject ?? `The content is being processed`
      );
      setIsSubjectLoading(false);
    } catch (e) {
      setIsSubjectLoading(true);
      setValue('sendMail.subject', `The content is being processed`);
      notification.error({
        message: 'Error!',
        description: 'Something went wrong!',
      });
    }
  };

  const handleCKEditorChange = async (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);

    const parser = new DOMParser();
    const doc = parser?.parseFromString(data, 'text/html');
    const signatureContainers = doc?.querySelector(
      'figure.signature-container'
    );
    if (!doc || !signatureContainers) {
      setPlainTextContent('');
      return;
    }
    signatureContainers?.remove();
    const plainText = doc?.body?.textContent || '';
    setPlainTextContent(plainText);
  };

  const handleSubjectInput = async (data) => {
    // const data =
    //   event?.target?.value || (typeof event === 'string' && event) || '';
    setDataSubject(data);
    setValue('sendMail.subject', data);
    setValue('sendMail.mailStepParentSubject', data);
    if (mailObject) {
      setMailObject({ ...mailObject, subject: data });
    }
  };

  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  useEffect(() => {
    if (
      dataSignature &&
      !mailObject?.content &&
      (emailConfigData ? emailConfigData?.length == 0 : true) &&
      !fromCreateFromTemplateModal
    ) {
      setDataContent(
        `<div style="font-family:Calibri;font-size: 11pt">
       ${
         (mailObject?.content || dataContent) +
         `<br><br><br><br><br><br>${dataSignature || ''}`
       }</div>`
      );
    }

    if (dataSignature && mailObject?.content) {
      let newContent = replaceSignaturePlaceholder(
        mailObject?.content || '',
        dataSignature
      );
      // Migratin' the old data
      setDataContent(newContent);
    }
    return () => {};
  }, [dataSignature]);

  const handleCloseTemplateOptions = () =>
    setOpenSequenceTemplateOptions(false);

  const onChooseTemplate = (chooseItem) => {
    const sequenceItemData = JSON.parse(chooseItem?.content);

    const { rawSequence: rawSequenceAllSteps } = sequenceItemData?.triggerItem;

    if (rawSequenceAllSteps?.length === 0 || !rawSequenceAllSteps) {
      notification.warning({
        description: "This template doesn't have any steps!",
      });
      return;
    }
    const rawSequence = rawSequenceAllSteps?.filter(
      (item) => item?.type === 'EMAIL'
    );
    if (rawSequence?.length < 1) {
      notification.warning({
        message: 'Can not use template',
        description: "The template doesn't have MAIL step in the first step!",
      });
      return;
    }
    const content = rawSequence[0]?.content || '';
    const subject = rawSequence[0]?.subject || '';

    if (isContentOnly) {
      setValue(`sendMail.content`, content);
      setDataContent(content);
      setValue(`sendMail.subject`, subject);
      setDataSubject(subject);
    }
    // else {
    //   if (sequenceItemData?.mailDefine?.sendMail) {
    //     const sendMailItem = { ...sequenceItemData?.mailDefine?.sendMail };
    //     delete sendMailItem?.from;
    //     const firstEmailSequence = {
    //       ...sendMailItem,
    //       recipients: [],
    //       delay: 1,
    //     };
    //     const dataListEmailSequence = [firstEmailSequence].concat([
    //       ...sequenceItemData?.triggerItem?.mails,
    //     ]);
    //     setValue(`sendMail`, sendMailItem);
    //     // setDataListEmailSequence(dataListEmailSequence)
    //     setEmailConfigData(dataListEmailSequence);
    //   }

    //   setInputNumberStep(sequenceItemData?.triggerItem?.mails || []);

    //   setValue(`sendMail.mailStepParentMailTo`, []);

    //   setNumberStep(sequenceItemData?.triggerItem?.mails?.length || 0);
    // }
  };

  // Hot List
  const [openHotList, setOpenHotList] = useState(false);
  const handleCloseHotList = () => setOpenHotList(false);
  const [warningModal, setWarningModal] = useState(false);

  const [hotListData, setHotListData] = useState([]);
  const [listEmailInGroup, setListEmailInGroup] = useState([]);
  const removeHotList = (id) => {
    setHotListData([]);
    setValue(`sendMail.hotList`, []);
  };
  const onChooseHotList = async (chooseItem) => {
    // setWarningModal(true)
    const listLabel = `Contact from ${chooseItem.name}`;
    const item = {
      name: listLabel,
      label: listLabel,
      value: chooseItem.id,
      hotListId: chooseItem.id,
    };

    setValue(`sendMail.tearsheetId`, chooseItem?.id);

    // setValue("sendEmail.createFrom", [...(getValues("sendEmail.createFrom") ?? []), 'HOT_LIST'])
    const createFromValues = getValues('sendEmail.createFrom') ?? [];
    if (!createFromValues.includes('HOT_LIST')) {
      setValue('sendEmail.createFrom', [...createFromValues, 'HOT_LIST']);
    }
    setHotListData([item]);
    setValue(`sendMail.hotList`, [item]);

    // handle check warnings
    const payloads = {
      emails: getValues('sendMail.listEmail') ?? [],
      tearsheetIds: [chooseItem?.id],
    };

    const { data } = await checkContactWarning(payloads);
    if (data) {
      setDataEmailWarning(data?.emailWarnings);
      setDataDomainWarning(data?.domainWarnings);
      if (data?.domainWarnings?.length > 0 || data?.emailWarnings?.length > 0) {
        setWarningModal(true);
        notification.warning({
          message: 'Please review recipient warnings',
        });
      }
    }
  };

  const RepeatedEmailsContainer = () => {
    const dataSource = dataEmailWarning?.map((item, index) => {
      return {
        no: index + 1,
        email: item?.email,
        repeatedTimes: item?.repeatedTimes,
      };
    });

    const handleChangeEmail = (e, domain) => {
      const payload = {
        key: domain,
        value: e,
        label: e?.includes('ignore') ? 'ignore' : 'select-one',
      };

      const existingIndex = dataEmailWarningSelect.findIndex(
        (item) => item.key === domain
      );

      if (existingIndex !== -1) {
        const newDataEmailWarningSelect = [...dataEmailWarningSelect];
        newDataEmailWarningSelect.splice(existingIndex, 1, payload);
        setDataEmailWarningSelect(newDataEmailWarningSelect);
      } else {
        setDataEmailWarningSelect((prevData) => [...prevData, payload]);
      }
    };

    const onChangeParentStep = (e) => {
      setDataEmailWarningLabel(e.target.value);
      const value = e.target.value;
      if (value === 'ignore-all') {
        const payload = dataEmailWarning?.map((item, index) => {
          return {
            key: item?.email,
            value: `ignore-${index + 1}`,
            label: e.target.value?.includes('ignore') ? 'ignore' : 'select-one',
          };
        });
        setDataEmailWarningSelect(payload);
      } else {
        const payload = dataEmailWarning?.map((item, index) => {
          return {
            key: item?.email,
            value: `select-one-${index + 1}`,
            label: e.target.value?.includes('ignore') ? 'ignore' : 'select-one',
          };
        });
        setDataEmailWarningSelect(payload);
      }
    };

    const columns = [
      {
        title: 'No',
        dataIndex: 'no',
        key: 'no',
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
      },
      {
        title: 'Repeated Times',
        dataIndex: 'repeatedTimes',
        key: 'repeated-times',
      },
      {
        title: 'Action',
        dataIndex: 'action',
        key: 'action',
        width: '15%',
        render: (_, record) => (
          <div className="grid grid-cols-2">
            <div className="flex flex-col text-base self-center">
              <span>Ignore</span>
              <span>Select One</span>
            </div>
            <Radio.Group
              onChange={(e) => handleChangeEmail(e.target.value, record?.email)}
              value={
                dataEmailWarningSelect.find(
                  (item) => item?.key === record?.email
                )?.value
              }
            >
              <Space direction="vertical">
                <Radio value={`ignore-${record?.no}`}></Radio>
                <Radio value={`select-one-${record?.no}`}></Radio>
              </Space>
            </Radio.Group>
          </div>
        ),
      },
    ];

    return (
      <div className="p-10 flex flex-col gap-5 text-base">
        <div className="text-lg font-medium" style={{ color: '#eebf00' }}>
          Repeated Emails could lead to repeated sequence to one specific
          recipient
        </div>
        <div className="grid grid-cols-3 max-w-lg">
          <div className="font-medium">Apply action for all:</div>
          <div className="flex flex-col">
            <span>Ignore</span>
            <span>Select One</span>
          </div>
          <Radio.Group
            onChange={onChangeParentStep}
            value={dataEmailWarningLabel}
          >
            <Space direction="vertical">
              <Radio value={'ignore-all'}></Radio>
              <Radio value={'select-one-all'}></Radio>
            </Space>
          </Radio.Group>
        </div>
        <Table dataSource={dataSource} columns={columns} />
      </div>
    );
  };

  const SameDomainContainer = () => {
    const dataSource = dataDomainWarning?.map((item, index) => {
      return {
        no: index + 1,
        domain: item?.domain,
        numberOfEmails: item?.repeatedTimes,
        emails: item?.emails,
      };
    });

    const handleChangeDomain = (e, domain) => {
      const payload = {
        key: domain,
        value: e,
        label: e?.includes('stop-for-one-reply-only')
          ? 'stop-for-one'
          : 'stop-for-all',
      };

      const existingIndex = dataDomainWarningSelect.findIndex(
        (item) => item.key === domain
      );

      if (existingIndex !== -1) {
        const newDataDomainWarningSelect = [...dataDomainWarningSelect];
        newDataDomainWarningSelect.splice(existingIndex, 1, payload);
        setDataDomainWarningSelect(newDataDomainWarningSelect);
      } else {
        setDataDomainWarningSelect((prevData) => [...prevData, payload]);
      }
    };

    const onChangeParentStep = (e) => {
      const value = e.target.value;
      setDataDomainWarningLabel(value);
      if (value === 'stop-for-one-reply-only-all') {
        const payload = dataDomainWarning?.map((item, index) => {
          return {
            key: item?.domain,
            value: `stop-for-one-reply-only-${index + 1}`,
            label: e.target.value?.includes('stop-for-one-reply-only')
              ? 'stop-for-one'
              : 'stop-for-all',
          };
        });
        setDataDomainWarningSelect(payload);
      } else {
        const payload = dataDomainWarning?.map((item, index) => {
          return {
            key: item?.domain,
            value: `stop-for-${index + 1}`,
            label: e.target.value?.includes('stop-for-one-reply-only')
              ? 'stop-for-one'
              : 'stop-for-all',
          };
        });
        setDataDomainWarningSelect(payload);
      }
    };

    const columns = [
      {
        title: 'No',
        dataIndex: 'no',
        key: 'no',
      },
      {
        title: 'Domain',
        dataIndex: 'domain',
        key: 'domain',
        render: (_, record) => <span>{record?.domain}</span>,
      },
      {
        title: 'Number of emails',
        dataIndex: 'numberOfEmails',
        key: 'number-of-emails',
        render: (_, record) => (
          <>
            <div
              style={{
                color: 'blue',
                textDecoration: 'underline',
                cursor: 'pointer',
              }}
              onClick={() => {
                setOpenListDomainEmail(true);
                setListEmailDomainDuplicate(record?.emails);
              }}
              className="grid grid-cols-3"
            >
              {record?.numberOfEmails}
            </div>
          </>
        ),
      },
      {
        title: 'Action',
        dataIndex: 'action',
        key: 'action',
        width: '25%',
        render: (_, record) => (
          <div className="grid grid-cols-3">
            <div className="flex flex-col col-span-2 text-base self-center">
              <span>Stop for one replying only</span>
              <span>Stop for all</span>
            </div>
            <Radio.Group
              onChange={(e) =>
                handleChangeDomain(e.target.value, record?.domain)
              }
              value={
                dataDomainWarningSelect.find(
                  (item) => item?.key === record?.domain
                )?.value
              }
            >
              <Space direction="vertical">
                <Radio value={`stop-for-one-reply-only-${record?.no}`}></Radio>
                <Radio value={`stop-for-${record?.no}`}></Radio>
              </Space>
            </Radio.Group>
          </div>
        ),
      },
    ];

    return (
      <div className="p-10 flex flex-col gap-5 text-base">
        <div className="text-lg font-medium" style={{ color: '#eebf00' }}>
          Many emails from domain could lead to repeated work for recipients'
          company
        </div>
        <div className="grid grid-cols-5 max-w-lg">
          <div className="font-medium col-span-2">Apply action for all:</div>
          <div className="flex flex-col col-span-2">
            <span>Stop for one replying only</span>
            <span>Stop for all</span>
          </div>
          <Radio.Group
            onChange={onChangeParentStep}
            value={dataDomainWarningLabel}
          >
            <Space direction="vertical">
              <Radio value={'stop-for-one-reply-only-all'}></Radio>
              <Radio value={'stop-for-all'}></Radio>
            </Space>
          </Radio.Group>
        </div>
        <AntdTable dataSource={dataSource} columns={columns} />;
      </div>
    );
  };

  const warnningItems = [
    {
      label: `Repeated Emails (${dataEmailWarning?.length})`,
      key: 'repeated-emails',
      children: <RepeatedEmailsContainer />,
    },
    {
      label: `Same Domain (${dataDomainWarning?.length})`,
      key: 'same-domain',
      children: <SameDomainContainer />,
    },
  ];

  //----------------------------------------------------------------

  const toggleSpinIcon = (iconId) => {
    if (!document) return;
    const element = document.getElementById(iconId);
    const classList = element.classList;
    if (classList.contains('anticon-spin')) {
      classList.remove('anticon-spin');
    } else {
      classList.add('anticon-spin');
    }
  };

  const handlePilotAIClick = (key) => {
    // const { key } = e;

    const generator = aiGeneratorList[key];
    if (!generator) return;

    const { title, prompt, replace, type, addonAfter } = generator;

    const aiGeneratorInforTemp = {
      editor,
      title: title,
      prompt: `${prompt} ${
        replace === 'content' && type === 'JOB_REVIEW'
          ? ` class="bg-purple-400 w-fit"`
          : ''
      }`,
      replace,
      tone: 'Natural',
      type: 'Soft',
      language: 'English',
      addonAfter,
    };

    if (!prompt?.trim()) {
      setAIGeneratorInfor({ ...aiGeneratorInforTemp });
      showAIGeneratorModal();
      setIsModalAIGeneratorLoading(false);
      showPromptModal();
      return;
    }

    if (key === 'genContent') {
      const contentGenerationData = {
        skills:
          job?.skills || getValues('jobskills') || getValues('skills') || [],
        jobtype: job?.jobtype || cleanText(getValues('jobtype')) || '',
        location:
          jobLocationCity || getValues('address1') || getValues('address2'),
        numOfChoice: 3,
      };
      const jobTitle = job?.jobtitle || getValues('jobtitle')?.trim();
      const description = job?.description || getValues('description')?.trim();
      if (
        !contentGenerationData.location ||
        !jobTitle ||
        !description ||
        !contentGenerationData?.skills ||
        contentGenerationData?.skills?.length === 0
      ) {
        notification.warning({
          description:
            'Please fill job required skills, job title, job description and job location at Step 1: Job Review!',
        });
        return;
      }

      generateContentLightVersion(
        contentGenerationData,
        -1,
        aiGeneratorInforTemp
      );
    } else {
      generateAIAssistantMessageFunc(aiGeneratorInforTemp);
    }

    setAIGeneratorInfor({ ...aiGeneratorInforTemp });
    showAIGeneratorModal();
  };

  const regenerateFindLocation = (locationText) => {
    setIsContentLoading(true);
    const messages = [
      {
        role: 'user',
        content:
          `Help me find 10 names of areas near ` +
          locationText +
          ` Note: give me the name only, follow this format ["location one", "location two",...]`,
      },
    ];

    const payload = {
      messages,
      numOfChoice: 1,
    };
    generateAIAssistantMessage(payload)
      .then((res) => {
        const { data } = res;
        if (data?.choices?.length > 0) {
          const { choices } = data;
          const results = choices.map(
            (result) => result?.message?.content || ''
          );
          if (results.length > 0) {
            if (JSON.parse(results[0]).length > 0) {
              setLocationlist(JSON.parse(results[0]));
              showFindLocationModal();
            } else {
              notification.warning({
                message: 'Can not find any locations, Please try again!',
              });
            }
          } else {
            notification.warning({
              message: 'Can not find any locations, Please try again!',
            });
          }
        }
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.error({
          message: 'Please try again later!',
        });
      })
      .finally(() => {
        setIsContentLoading(false);
      });
  };

  const analyzeEmail = () => {
    setOpenModalAnalyze(true);
  };

  const rewriteByZileoAI = (selectText) => {
    setIsContentLoading(true);
    const messages = [
      {
        role: 'system',
        content: `It needs to be refined for better clarity, coherence, and overall quality. Please replace all of them with others but enhance the writing style while keeping the original meaning and language intact with the same format.
                  \nNote: No yapping!`,
      },
      {
        role: 'user',
        content: JSON.stringify(selectText),
      },
    ];

    const payload = {
      messages,
      numOfChoice: 1,
    };
    generateAIAssistantMessage(payload)
      .then((res) => {
        const { data } = res;
        if (data?.choices?.length > 0) {
          const { choices } = data;
          const results = choices.map(
            (result) => result?.message?.content || ''
          );
          const resultList = JSON.parse(results?.[0] || '[]');
          let rawContent = editor.current?.props?.data || '';
          if (resultList?.length > 0) {
            if (selectText?.length > 0) {
              selectText.forEach((oldItem, index) => {
                const newItem = resultList[index];
                rawContent = rawContent?.replace(oldItem, newItem);
              });
            } else {
              rawContent = rawContent?.replace(selectText[0], resultList);
            }

            setDataContent(rawContent);
            setMailObject({
              ...mailObject,
              content: rawContent,
            });
            notification.success({
              message: 'New content was replaced!',
            });
          } else {
            notification.warning({
              message: 'Server is busy. Please try again later!',
            });
          }
        }
      })
      .catch((err) => {
        console.log('err: ', err);
        notification.warning({
          message: 'Server is busy. Please try again later!',
        });
      })
      .finally(() => {
        setIsContentLoading(false);
      });
  };

  //----------------------------------------------------------------
  useEffect(() => {
    if (hostListOther) {
      setHotListData(hostListOther);
    }
    return () => {};
  }, [hostListOther]);

  const handleSaveRules = () => {
    const payloadDomain = dataDomainWarningSelect?.map((item) => {
      return {
        domain: item?.key,
        stopAll: item?.label == 'stop-for-all' ? true : false,
      };
    });

    const payloadEmail = dataEmailWarningSelect?.map((item) => {
      return {
        email: item?.key,
        stopAll: item?.label == 'select-one' ? true : false,
      };
    });

    const payload = [...payloadDomain, ...payloadEmail];

    setValue('stopRules', payload);
    closeWarningModal();
  };

  const handleOpenWarning = () => {
    setWarningModalOpen(true);
  };

  const onSubmitEvent = (configuration) => {
    const eventUrl = `${window.location.origin}/schedule?config_id=${configuration?.id}`;
    // handle add content to CK editor

    // const mailContentEl = document.getElementsByClassName('jodit-wysiwyg');

    // const currContent =
    //   mailContentEl[mailContentEl?.length - 1]?.innerHTML || dataContent;
    // const eventUrlHtmlText = `<a href=${eventUrl}>${configuration?.event_booking?.title}</a>`;
    // const newDataContent = eventUrlHtmlText + currContent;
    // setDataContent(newDataContent);
    // closeSchedulingEditorModal();
  };

  const handleUpdateStatus = async () => {
    setLoadingStatusUpdateSequence(true);
    const body = {
      status: getValues('sequenceData')?.status === 'LIVE' ? 'STOP' : 'LIVE',
    };

    try {
      const { data } = await updateSequence(
        getValues('sequenceData')?.id,
        body
      );

      notification.success({ message: 'Sequence updated successfully' });
      setLoadingStatusUpdateSequence(false);
    } catch (e) {
      console.log(e);
      setLoadingStatusUpdateSequence(false);
      const message =
        e?.response?.data?.message || 'Something went wrong happened';

      notification.error({ message });
    }
  };

  const handleApplyAnalyzeContent = (mailContent, subjectContent) => {
    const dataMailContent = `<div style="font-family:Calibri;font-size: 11pt">
     ${mailContent + `<br><br><br><br><br><br>${dataSignature || ''}`}</div>`;

    setDataContent(dataMailContent);
    setDataSubject(subjectContent);
    setMailObject({
      ...mailObject,
      subject: subjectContent,
      content: dataMailContent,
    });
  };

  return (
    (mailObject || fromSequenseEmail || fromManualCreate || fromSequenceAI) && (
      <>
        {!checkBoxStatus && (
          <div
            className={clsx(
              'flex gap-2 min-h-[27rem] sequence-container',
              preview && 'justify-end'
            )}
          >
            <div
              ref={teleRef}
              className={`first-mail-container ${!onSubmitTemplate && !preview && !fromCreatingScratchSequence && !fromCreateFromTemplateModal && !fromCreateByVacancy && !fromSequenseDetail && 'min-w-[52rem]'}`}
              style={{
                position: 'relative',
              }}
            >
              {((!onSubmitTemplate &&
                !fromCreatingScratchSequence &&
                !fromCreateFromTemplateModal &&
                !fromCreateByVacancy &&
                !fromSequenseDetail) ||
                isStepMode) && (
                <>
                  {warningModal && (
                    <div
                      style={{
                        position: 'absolute',
                        right: '0',
                        marginTop: '53px',
                        zIndex: '100',
                        marginRight: '10px',
                        cursor: 'pointer',
                      }}
                    >
                      <WarningOutlined
                        onClick={() => handleOpenWarning()}
                        style={{ fontSize: '20px', color: '#ffcc00' }}
                      />
                    </div>
                  )}

                  <label
                    for="sendMail.subject"
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}
                  >
                    <div className="flex gap-2 items-center merge-tags-subject">
                      <p>
                        <span className="text-red-600">*</span> Subject
                      </p>
                    </div>
                    {!onSubmitTemplate && (
                      <Button
                        type="text"
                        shape="circle"
                        onClick={refreshSubject}
                        style={{ background: '#ffffff' }}
                        icon={
                          <SyncOutlined
                            spin={isSubjectLoading}
                            style={{ color: '#4096ff' }}
                          />
                        }
                      />
                    )}
                  </label>

                  <Form.Item
                    name="sendMail.subject"
                    style={{ marginTop: '10px' }}
                  >
                    <Input.Group compact>
                      <Controller
                        render={({ field }) => (
                          <Mentions
                            style={{
                              paddingLeft: isSubjectLoading ? '40px' : '10px',
                            }}
                            className="font-Montserrat"
                            required
                            {...field}
                            value={mailObject?.subject || dataSubject}
                            onChange={handleSubjectInput}
                            placeholder="Input {{ to start add merge tags"
                            prefix={['{{']}
                            // onSearch={onSearch}
                            options={MERTAGS_DEFINITION.filter((item) =>
                              fromCreatingScratchSequence ||
                              fromCreateFromTemplateModal ||
                              fromSequenceAI
                                ? item.id === 'COMPANY_NAME' ||
                                  (!item.id.includes('VACANCY') &&
                                    !item.id.includes('COMPANY'))
                                : true
                            )
                              .filter((item) =>
                                fromCreatingScratchSequence
                                  ? !IGNORE_MERGE_TAGS.includes(item?.id)
                                  : true
                              )
                              .map(({ id, label }) => ({
                                key: `${id}}}`,
                                value: `${id}}}`,
                                label,
                              }))}
                          />
                        )}
                        name="sendMail.subject"
                        control={control}
                        data={mailObject?.subject || dataSubject}
                      />
                      {isSubjectLoading && (
                        <div style={{ marginTop: '-30px', marginLeft: '10px' }}>
                          {<Spin />}
                        </div>
                      )}
                    </Input.Group>
                  </Form.Item>

                  <label
                    for="sendMail.content"
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}
                  >
                    <div className="flex gap-2 items-center justify-between">
                      <p className="self-center">
                        <span className="text-red-600">*</span> Content
                      </p>
                    </div>
                    {!onSubmitTemplate && (
                      <div>
                        <Button
                          type="text"
                          onClick={refreshContent}
                          style={{ background: '#ffffff' }}
                          icon={
                            <SyncOutlined
                              spin={isContentLoading}
                              style={{ color: '#4096ff' }}
                            />
                          }
                        />
                      </div>
                    )}
                  </label>

                  <Form.Item
                    style={{ marginTop: '10px' }}
                    name="sendMail.contentText"
                  >
                    <Controller
                      render={({ field }) => (
                        <div className="mail-content-container">
                          <div
                            className="editor-container editor-container_classic-editor editor-container_include-style editor-container_include-block-toolbar"
                            // ref={editorContainerRef}
                          >
                            <div className="editor-container__editor">
                              <div>
                                {!loadingSignature ? (
                                  <CKEditor
                                    ref={editor}
                                    editor={ClassicEditor}
                                    config={{
                                      ...editorConfig,
                                      toolbar: {
                                        ...editorConfig.toolbar,
                                        items: [
                                          ...editorConfig.toolbar.items,
                                          // Pilot AI
                                          {
                                            label: 'Pilot AI',
                                            withText: true,
                                            tooltip: true,
                                            icon: false,
                                            class: 'customized-tool-bar-btn',
                                            items:
                                              fromCreatingScratchSequence ||
                                              fromCreateFromTemplateModal
                                                ? [
                                                    'gencontentscratch',
                                                    'gensubjectscratch',
                                                  ]
                                                : ['gencontent', 'gensubject'],
                                          },
                                          !fromCreatingScratchSequence &&
                                            !fromCreateFromTemplateModal && {
                                              label: 'AI Tags',
                                              withText: true,
                                              tooltip: true,
                                              icon: false,
                                              class: 'customized-tool-bar-btn',
                                              items: [
                                                'location',
                                                'bulletpointskills',
                                                'sentencesskills',
                                                'findlocation',
                                              ],
                                            },
                                          'rewritebyzileoai',
                                          'analyzeemail',
                                        ],
                                      },
                                      mergeFields: {
                                        definitions: [...MERTAGS_DEFINITION]
                                          .filter((item) =>
                                            fromCreatingScratchSequence ||
                                            fromCreateFromTemplateModal ||
                                            fromSequenceAI
                                              ? item.id === 'COMPANY_NAME' ||
                                                (!item.id.includes('VACANCY') &&
                                                  !item.id.includes('COMPANY'))
                                              : true
                                          )
                                          .filter((item) =>
                                            fromCreatingScratchSequence
                                              ? !IGNORE_MERGE_TAGS.includes(
                                                  item?.id
                                                )
                                              : true
                                          ),
                                        initialPreviewMode: '$defaultValues',
                                      },
                                      myProps: {
                                        location: jobLocationCity || '',
                                        skills:
                                          job?.skills ||
                                          getValues('jobskills') ||
                                          getValues('skills') ||
                                          [],
                                        findLocation: (locationText) => {
                                          setSelectedLocation(locationText);
                                          regenerateFindLocation(locationText);
                                        },
                                        rewriteByZileoAI,
                                        handlePilotAIClick,
                                        analyzeEmail,
                                      },
                                    }}
                                    data={
                                      mailObject?.content || dataContent || ''
                                    }
                                    onChange={handleCKEditorChange}
                                    onAfterDestroy={(editor) =>
                                      console.log('destroyyy: ', editor)
                                    }
                                  />
                                ) : (
                                  <div className="h-[28rem] w-full flex justify-center items-center bg-white border">
                                    <Spin />
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      name="sendMail.contentText"
                      control={control}
                    />
                  </Form.Item>

                  <div className="w-full attachment-container bg-white rounded-md py-4 px-8 border flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-base font-semibold">
                          Attachments{' '}
                          {`(${(mailObject?.fileList?.map((item) => parseInt(item?.size || 0))?.reduce((a, b) => a + b, 0) / 1024 / 1024).toFixed(2)} MB)`}
                        </span>
                        <span className="text-xs font-medium text-[#aeb2b8]">
                          Upload and attach files to this email.
                        </span>
                      </div>
                      <UploadOutlined className="text-xl" />
                    </div>
                    <div className="w-full flex justify-center">
                      <Upload.Dragger
                        multiple
                        showUploadList={{ previewIcon: true }}
                        customRequest={uploadAttachments}
                        // beforeUpload={beforeUploadValidator}
                        onChange={onChangeAttachments}
                        fileList={mailObject?.fileList || []}
                        listType="picture"
                        className="w-full flex gap-4 items-center"
                      >
                        <AntdImage
                          src={uploadFileIcon}
                          className="mb-2"
                          width="30px"
                          height="30px"
                          preview={false}
                        />
                        <div className="flex items-center justify-center gap-1 text-sm font-medium mb-1">
                          <span className="underline">Click to upload</span>
                          <span>or drag and drop</span>
                        </div>
                        <div className="text-xs text-gray-800">
                          Maximun file(s) size 20 MB.
                        </div>
                      </Upload.Dragger>
                    </div>
                  </div>

                  <div>
                    <ModalAnalyze
                      openModal={openModalAnalyze}
                      setOpenModal={setOpenModalAnalyze}
                      plainTextContent={plainTextContent}
                      subjectContent={dataSubject}
                      handleApplyAnalyzeContent={handleApplyAnalyzeContent}
                    />
                  </div>
                </>
              )}
            </div>
            {!isStepMode && (
              <BullhornSendEmailModal
                fromCreateFromTemplateModal={fromCreateFromTemplateModal}
                closeAllModal={closeAllModal}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sequenceStatus={sequenceStatus}
                setConfirmStep={setConfirmStep}
                setNotDiscard={setNotDiscard}
                notDiscard={notDiscard}
                job={job}
                watch={watch}
                listEmailSend={listEmailSend}
                openModalSendEmail={openModalSendEmail}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromSequenseEmail={fromSequenseEmail}
                preview={preview}
                previewMode={previewMode}
                isDuplicateTemplate={isDuplicateTemplate}
                onSubmitTriggers={onSubmitTemplate ? onSubmitTriggers : null}
                dataSubject={dataSubject}
                onHandleSeqId={handleEmailSeqId}
                emailSeqId={emailSeqId}
                hotListData={hotListData}
                defaultUserSelected={
                  fromCreateByVacancy
                    ? defaultUserSelected?.length > 0
                      ? defaultUserSelected
                      : []
                    : defaultUserSelected
                }
                fromCreatingScratchSequence={fromCreatingScratchSequence}
                notLoadingData={notLoadingData}
                fromSequenseDetail={fromSequenseDetail || newUpdatedSequence}
                newUpdatedSequence={true}
                setContactListSelected={setContactListSelected}
                contactListSelected={contactListSelected}
                fromCreateByVacancy={fromCreateByVacancy}
                setListContactSelected={setListContactSelected}
                selectedSequenceValue={selectedSequenceValue}
                fromCreateTemplate={fromCreateTemplate}
                fromCandidateSequence={fromCandidateSequence}
                templateId={templateId}
                setPreviewEmail={setPreviewEmail}
                handleReset={handleReset}
                handleClose={handleClose}
                templateNameModal={templateName}
                closeCreateSequenceModal={closeCreateSequenceModal}
                reloadSequenceDetail={reloadSequenceDetail}
              />
            )}
          </div>
        )}
        {checkBoxStatus && <div className="block h-12"></div>}
        {!preview && !onSubmitTemplate && (
          <div
            style={{
              display: 'flex',
              justifyContent:
                fromSequenseDetail || newUpdatedSequence ? '' : 'space-between',
              // marginTop:
              //   fromSequenseDetail || newUpdatedSequence ? '20px' : '-35px',
            }}
            className="sequence-action-container"
          >
            {fromSequenseDetail &&
              !getValues('sequenceData.isMarkedAsCompleted') &&
              selectedSequenceValue?.status !== 'DRAFT' && (
                <Tooltip
                  title={'Click here to Resume Sequence'}
                  placement="bottom"
                >
                  <Button
                    htmlType="button"
                    onClick={handleUpdateStatus}
                    type="primary"
                    style={{ marginLeft: '10px' }}
                    loading={loadingStatusUpdateSequence}
                  >
                    Resume Sequence
                  </Button>
                </Tooltip>
              )}
          </div>
        )}

        {/* Send Mail Options Modal */}
        {openSequenceTemplateOptions && (
          <Modal
            title="Send Mail Options"
            open={openSequenceTemplateOptions}
            // onOk={}
            onCancel={handleCloseTemplateOptions}
            footer=""
          >
            <div
              style={{
                // padding: '20px',
                // display: 'flex',
                // justifyContent: 'space-between',
                textAlign: '-webkit-center',
              }}
            >
              <ModalCreateFromTemplate
                fromBullhornSendEmailModal={true}
                onChooseTemplate={onChooseTemplate}
                handleCloseTemplateOptions={handleCloseTemplateOptions}
              />
            </div>
          </Modal>
        )}

        {/* Hot List Modal */}
        {openHotList && (
          <Modal
            title="Hot List"
            open={openHotList}
            onCancel={handleCloseHotList}
          >
            <div
              style={{
                textAlign: '-webkit-center',
              }}
            >
              <ModalHotList
                selectedHotList={hotListData}
                onChooseItem={onChooseHotList}
                handleClose={handleCloseHotList}
              />
            </div>
          </Modal>
        )}

        {/* AI generator Modal */}

        {isModalAIGeneratorOpen && (
          <Drawer
            className="ai-generator-container"
            onClose={handleAIGeneratorCancel}
            open={isModalAIGeneratorOpen}
            width={'100%'}
            height={'100%'}
            placement={fromCreatingScratchSequence ? 'top' : 'right'}
            getContainer={false}
            footer={
              fromCreatingScratchSequence ? null : (
                <div className="flex gap-2">
                  <Button onClick={handleAIGeneratorCancel}>Close</Button>
                </div>
              )
            }
          >
            <div className="pb-2 mail-generator-container h-full">
              <div className="mb-3 text-2xl font-semibold">
                {aiGeneratorInfor?.title}
              </div>
              <div className="text-base mb-4 text-[#636363]">
                Enter keywords or short description that describes your email
                content. Additionally, select tone of voice, campaign type and
                your email language to get the most accurate results.
              </div>
              <div>
                <div className="flex justify-between mb-4">
                  <span className="text-base font-medium">Prompt</span>
                  {!openPromptModal && (
                    <div>
                      {!fromCreatingScratchSequence && (
                        <Select
                          size="small"
                          className=""
                          disabled={
                            isModalAIGeneratorLoading ||
                            !aiGeneratorInfor?.replace
                          }
                          title={
                            !aiGeneratorInfor?.replace &&
                            `Can not select language in this generator`
                          }
                          onChange={(value) =>
                            setAIGeneratorInfor({
                              ...aiGeneratorInfor,
                              language: value,
                            })
                          }
                          value={aiGeneratorInfor.language}
                          defaultValue="English"
                          options={[
                            {
                              value: 'English',
                              label: 'English',
                            },
                            {
                              value: 'Spanish',
                              label: 'Spanish',
                            },
                            {
                              value: 'Chinese',
                              label: 'Chinese',
                            },
                            {
                              value: 'Japanese',
                              label: 'Japanese',
                            },
                          ]}
                        />
                      )}
                      {fromCreatingScratchSequence && (
                        <Button
                          type="primary"
                          onClick={showPromptModal}
                          icon={<FormOutlined />}
                        >
                          Change Prompt
                        </Button>
                      )}
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-10 mb-3">
                  <Select
                    title={
                      !aiGeneratorInfor?.replace &&
                      `Can not select tone in this generator`
                    }
                    disabled={
                      isModalAIGeneratorLoading || !aiGeneratorInfor?.replace
                    }
                    value={aiGeneratorInfor.tone}
                    onChange={(value) =>
                      setAIGeneratorInfor({
                        ...aiGeneratorInfor,
                        tone: value,
                      })
                    }
                    className="col-span-2 !rounded-none"
                    defaultValue="Natural"
                    options={[
                      {
                        label: <span>Tone</span>,
                        title: 'Tone',
                        options: [
                          {
                            value: 'Natural',
                            label: 'Natural',
                          },
                          {
                            value: 'Catchy',
                            label: 'Catchy',
                          },
                          {
                            value: 'Professional',
                            label: 'Professional',
                          },
                          {
                            value: 'Persuasive',
                            label: 'Persuasive',
                          },
                        ],
                      },
                    ]}
                  />
                  <Select
                    title={
                      !aiGeneratorInfor?.replace &&
                      `Can not select type in this generator`
                    }
                    disabled={
                      isModalAIGeneratorLoading || !aiGeneratorInfor?.replace
                    }
                    value={aiGeneratorInfor.type}
                    onChange={(value) =>
                      setAIGeneratorInfor({
                        ...aiGeneratorInfor,
                        type: value,
                      })
                    }
                    className="col-span-2 !rounded-md"
                    defaultValue="Soft"
                    options={[
                      {
                        label: <span>Campaign Type</span>,
                        title: 'Type',
                        options: [
                          {
                            value: 'Soft',
                            label: 'Soft',
                          },
                          {
                            value: 'Medium',
                            label: 'Medium',
                          },
                          {
                            value: 'Hard',
                            label: 'Hard',
                          },
                        ],
                      },
                    ]}
                  />
                  {!openPromptModal && (
                    <Input
                      disabled={isModalAIGeneratorLoading}
                      onChange={(event) =>
                        setAIGeneratorInfor({
                          ...aiGeneratorInfor,
                          prompt: event.target.value,
                        })
                      }
                      value={aiGeneratorInfor.prompt}
                      placeholder="Type your description here"
                      className="col-span-6"
                    />
                  )}
                </div>
              </div>

              {!openPromptModal && (
                <div>
                  <div className="mail-generator-content-container">
                    <span className="text-base font-medium">
                      Result Options
                    </span>
                    <div
                      className={`flex mt-3 ${aiGeneratorInfor?.replace === 'content' ? 'grid grid-cols-3 gap-2' : 'flex-col gap-2'}`}
                    >
                      {isModalAIGeneratorLoading &&
                      (aiGeneratorInfor?.results?.length == 0 ||
                        !aiGeneratorInfor?.results) ? (
                        <Spin></Spin>
                      ) : (
                        aiGeneratorInfor?.results?.map((opt, optIndex) => (
                          <div className="col-span-1">
                            {aiGeneratorInfor?.replace === 'content' ? (
                              <div class=" w-full border bg-white border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 ">
                                <div class="flex justify-between pt-2 pr-4">
                                  <div className="flex items-center gap-2">
                                    {/* <MailOutlined className="text-lg font-bold text-white" />
                                    <span className='text-lg font-medium'>Suggestion</span> */}
                                  </div>
                                  <div className="flex items-center">
                                    <Button
                                      // disabled={isModalAIGeneratorLoading}
                                      className="w-1"
                                      title="Replace current by this option"
                                      onClick={() => {
                                        const { replace } = aiGeneratorInfor;
                                        if (replace === 'content') {
                                          const content = opt;
                                          setDataContent(
                                            content + (dataSignature || '')
                                          );
                                        }
                                        if (replace === 'subject') {
                                          setDataSubject(opt);
                                        }
                                        notification.success({
                                          description: `Replace ${replace} successfull!`,
                                        });
                                        handleAIGeneratorCancel();
                                      }}
                                      size="small"
                                      type="link"
                                      icon={<CheckOutlined />}
                                    />
                                    {!fromCreatingScratchSequence && (
                                      <Button
                                        size="small"
                                        onClick={() => {
                                          const contentGenerationData = {
                                            skills:
                                              job?.skills ||
                                              getValues('jobskills') ||
                                              getValues('skills'),
                                            jobtype: job?.jobtype || 'remote',
                                            location:
                                              jobLocationCity ||
                                              getValues('jobLocationCity'),
                                            numOfChoice: 1,
                                          };
                                          generateContentLightVersion(
                                            contentGenerationData,
                                            optIndex
                                          );
                                          const iconId = `reload-icon-${optIndex}`;
                                          toggleSpinIcon(iconId);
                                        }}
                                        type="link"
                                        title={`Reloading this option`}
                                        icon={
                                          <ReloadOutlined
                                            id={`reload-icon-${optIndex}`}
                                          />
                                        }
                                      />
                                    )}
                                  </div>
                                </div>
                                <div class="flex flex-col items-center px-4 pb-5 mail-content-container">
                                  <div
                                    id={`generator-options-${optIndex}`}
                                    className="option-container"
                                    dangerouslySetInnerHTML={{
                                      __html:
                                        (optIndex === 0 && firstOption) ||
                                        (optIndex === 1 && secondOption) ||
                                        (optIndex === 2 && thirdOption),
                                    }}
                                  ></div>
                                </div>
                              </div>
                            ) : (
                              <div className="grid border border-[#969696] grid-cols-10 px-2 py-3 items-center border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
                                <span
                                  className="line-clamp-1 col-span-9 text-[#636363]"
                                  title={opt}
                                >
                                  {opt}
                                </span>
                                <div className="col-span-1 justify-self-end">
                                  {aiGeneratorInfor.replace && (
                                    <Button
                                      title="Replace current by this option"
                                      className="mr-2"
                                      onClick={() => {
                                        const { replace } = aiGeneratorInfor;
                                        if (replace === 'content') {
                                          setDataContent(
                                            opt + (dataSignature ?? '')
                                          );
                                        }
                                        if (replace === 'subject') {
                                          setDataSubject(opt);
                                        }
                                        handleAIGeneratorCancel();
                                      }}
                                      type="link"
                                      icon={<CheckOutlined />}
                                    />
                                  )}
                                  <Button
                                    onClick={() => {
                                      navigator.clipboard.writeText(opt);
                                    }}
                                    type="link"
                                    title={`Save clipboard's contents`}
                                    icon={<CopyOutlined />}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  {!isModalAIGeneratorLoading && (
                    <div className="text-sm text-[#636363] italic mt-4">
                      Didn't find the result you like? Change the factors then
                      regenerate.
                    </div>
                  )}
                </div>
              )}

              {openPromptModal && (
                <div className="flex flex-col gap-3 w-full ">
                  <div>Description Box</div>
                  <TextArea
                    rows={10}
                    placeholder="Start writing...."
                    name="description"
                    onChange={(ev) =>
                      setAIGeneratorInfor({
                        ...aiGeneratorInfor,
                        prompt: ev.target.value,
                      })
                    }
                    value={aiGeneratorInfor.prompt}
                  />
                  <div className="w-full flex items-center justify-between">
                    <div className="text-sm text-[#636363] italic">
                      <Tag
                        icon={<ExclamationCircleOutlined />}
                        color="warning"
                        className="font-semibold uppercase"
                      >
                        Please Note:
                      </Tag>
                      <span className="text-sm text-[#636363] italic">
                        Pilot AI can make mistakes. Check important info.
                      </span>
                    </div>
                    <Button
                      type="primary"
                      icon={<FireOutlined />}
                      loading={isModalAIGeneratorLoading}
                      disabled={!aiGeneratorInfor.prompt.trim()}
                      onClick={() =>
                        generateAIAssistantMessageFunc({ ...aiGeneratorInfor })
                      }
                    >
                      Generate
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Drawer>
        )}

        {/* Find Location Drawer */}
        {isFindLocationOpen && (
          <Drawer
            placement="top"
            onClose={handleFindLocationCancel}
            open={isFindLocationOpen}
            height={'42%'}
            getContainer={false}
            footer={
              <div className="w-full grid grid-cols-2 gap-2">
                <Button
                  disabled={isContentLoading}
                  onClick={handleFindLocationCancel}
                  type="default"
                >
                  Cancel
                </Button>
                <Button
                  // disabled={isContentLoading}
                  onClick={() => regenerateFindLocation(selectedLocation)}
                  type="primary"
                  icon={<FireOutlined />}
                  loading={isContentLoading}
                >
                  Regenerate
                </Button>
              </div>
            }
          >
            <div className="">
              <div className="flex flex-col">
                <span className="text-2xl font-semibold mb-2">
                  Find Locations
                </span>
                <span className="font-medium text-[#6e6f72]">
                  Flexible finder that grows with you.
                </span>
              </div>
              <div className="flex flex-col gap-5 mt-4">
                {locationList?.map((location) => (
                  <div className="grid grid-cols-6 px-5 py-3 items-center border-2 border-[#f8f8f8] rounded-xl">
                    <div className="col-span-1 flex justify-center w-full h-full text-lg">
                      <EnvironmentOutlined />
                    </div>
                    <div
                      className="col-span-4 line-clamp-1 text-[#344054] text-base"
                      title={location}
                    >
                      {location}
                    </div>
                    <div className="col-span-1 flex justify-center">
                      <Button
                        disabled={isContentLoading}
                        title={`Replace ${selectedLocation} by ${location}`}
                        className="mr-2"
                        onClick={() => {
                          const locationEl =
                            document?.getElementById('location-id');

                          if (locationEl) {
                            const locationElText = dataContent.match(
                              '&nbsp;<span id="location-id"  class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">(.*)</span>&nbsp;&nbsp;'
                            );

                            if (locationElText && locationElText.length > 0) {
                              const newContent = dataContent.replaceAll(
                                locationElText[0],
                                `&nbsp;<span id="location-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${location}</span>&nbsp;&nbsp;`
                              );
                              setDataContent(newContent);
                              notification.success({
                                description: `Replace ${selectedLocation} by ${location} successfully!`,
                              });
                              closeFindLocationModal();
                            } else if (selectedLocation) {
                              const newContent = dataContent.replaceAll(
                                selectedLocation,
                                `&nbsp;<span id="location-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${location}</span>&nbsp;&nbsp;`
                              );
                              setDataContent(newContent);
                              notification.success({
                                description: `Replace ${selectedLocation} by ${location} successfully!`,
                              });
                              closeFindLocationModal();
                            }
                          } else {
                            const newContent = dataContent.replaceAll(
                              selectedLocation,
                              ` ${location} `
                            );

                            setDataContent(newContent);
                            notification.success({
                              description: `Replace ${selectedLocation} by ${location} successfully!`,
                            });
                            closeFindLocationModal();
                          }
                        }}
                        type="text"
                        icon={<CheckOutlined />}
                      />
                      <Button
                        disabled={isContentLoading}
                        onClick={() => {
                          navigator.clipboard.writeText(location);
                          notification.success({
                            description: 'Copied!',
                          });
                        }}
                        type="text"
                        title={`Save clipboard's contents`}
                        icon={<CopyOutlined />}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Drawer>
        )}

        <Modal
          title="Emails Duplicate"
          open={openListDomainEmail}
          // onOk={() => setOpenListDomainEmail(false)}
          // onCancel={() => setOpenListDomainEmail(false)}
          footer={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div></div>
              <Button onClick={() => setOpenListDomainEmail(false)}>
                Cancel
              </Button>
            </div>
          }
        >
          <div>
            {listEmailDomainDuplicate?.map((item) => (
              <div style={{ marginTop: '10px' }}>{item}</div>
            ))}
          </div>
        </Modal>

        {/* Warning modal */}
        {isWarningModalOpen && (
          <Drawer
            placement="top"
            onClose={closeWarningModal}
            open={isWarningModalOpen}
            height={'100%'}
            getContainer={false}
            title="Recipient Warnings"
            footer={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div></div>
                <div>
                  <Button onClick={() => closeWarningModal()} type="default">
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => handleSaveRules()}
                    style={{ marginLeft: '20px', width: '130px' }}
                  >
                    Save
                  </Button>
                </div>
              </div>
            }
          >
            <Tabs
              // onChange={onChange}
              type="card"
              items={warnningItems}
            />
          </Drawer>
        )}

        {/* Scheduling modal */}
        {schedulingEditorModalOpen && (
          <Drawer
            width={'100%'}
            open={schedulingEditorModalOpen}
            onClose={closeSchedulingEditorModal}
            footer={
              <>
                <Button onClick={closeSchedulingEditorModal}>Close</Button>
              </>
            }
            getContainer={false}
          >
            <NylasConfigurations onSubmitEvent={onSubmitEvent} />
          </Drawer>
        )}
      </>
    )
  );
}

export default BullhornSendEmail;
