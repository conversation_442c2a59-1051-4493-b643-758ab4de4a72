import { useQuery } from '@tanstack/react-query';
import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Collapse,
  Flex,
  Form,
  Input,
  Pagination,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
  notification,
  Modal,
  Segmented,
  InputNumber,
  DatePicker,
  Image,
  Card,
  List,
  Tooltip,
  Tabs,
  Spin,
  Dropdown,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  employeeFinderSearchSignals,
  employeeFinderSearchTag,
  getListCompanies,
  getListCompany,
  getListEmployee,
  employeeFinderIntentCategory,
  getRevenue,
} from '../../services/employee';
import { getDetailCompanyById, getFacets } from '../../services/companyFinder';
import {
  createSavedSearch,
  getSavedSearch,
  updateSavedSearch,
  deleteSavedSearch,
} from '../../services/emailFinder';
import {
  EnvironmentOutlined,
  FacebookOutlined,
  LinkOutlined,
  LinkedinOutlined,
  PhoneOutlined,
  SearchOutlined,
  TwitterOutlined,
  FileSearchOutlined,
  UserOutlined,
  CopyOutlined,
  DeleteOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { FaRegBuilding } from 'react-icons/fa';
import IntentCollap from './IntentCollap';
import logoZielo from '../../assets/img/no_company.png';
import logo from '/logo_bull.webp';
import BullHornJobSubmissionCompany from './BullhornJobSubmissionCompany';
import { insertBullhorn, upladteBullhorn } from '../../services/bullhorn';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { UserGroupIcon } from '../Sidebar/consts';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowsToDot, faBriefcase, faBuilding, faChartLine, faCodeBranch, faDollarSign, faIndustry, faLocationDot, faMedal, faMicrochip, faMoneyBills, faTowerBroadcast, faUserGroup } from '@fortawesome/free-solid-svg-icons';
import CompanyDetailModal from './CompanyDetail';
import { COMMON_STRINGS } from '../../constants/common.constant';

const OptionBuyIntent = [
  { label: 'High', value: 'high' },
  { label: 'Medium', value: 'mid' },
  { label: 'Low', value: 'low' },
  { label: 'None', value: 'none' },
];

export const HENLEY = 'Henley';

const EmailFinderCompanyTab = () => {
  const [debouncedSearchTextCompany, setDebouncedSearchTextCompany] =
    useState('');
  const [
    debouncedLocationFindCompanyText,
    setDebouncedLocationFindCompanyText,
  ] = useState('');
  const [
    debouncedSearchTextIndustryCompany,
    setDebouncedSearchTextIndustryCompany,
  ] = useState('');
  const [showIncludeKeywordCompany, setShowIncludeKeywordCompany] =
    useState(false);
  const [showIncludeAllKeywordCompany, setShowIncludeAllKeywordCompany] =
    useState(false);
  const [showExcludeKeywordsCompany, setShowExcludeKeywordsCompany] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [listCompanies, setListCompanies] = useState([]);
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [debouncedSearchTextTitle, setDebouncedSearchTextTitle] = useState('');
  const [listCompaniesPagination, setListCompaniesPagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isDetailEmployeCompany, setIsDetailEmployeeCompany] = useState(false);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [listEmployeeCompany, setListEmployeeCompany] = useState([]);
  const [showCompany, setShowCompany] = useState(false);
  const [handleCloseClient, setHandleCloseClient] = useState(false);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
    useState({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
  const [dataTechnologies, setDataTechnologies] = useState([]);
  const [dataParentIntent, setDataParentIntent] = useState([]);
  const [showModalAddTopic, setShowModalAddTopic] = useState(false);

  const [listDataIntentSetting, setListDataIntentSetting] = useState([]);
  const [dataListTopicTitle, setDataListTopicTitle] = useState([]);
  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [listDataFunding, setListDataFunding] = useState([]);
  const [fundingSize, setFundingSize] = useState(true);
  const [dataPersonTitle, setDataPersonTitle] = useState([]);
  const [dataLocation, setDataLocation] = useState([]);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const [showNotAnyOfCompany, setShowNotAnyOfCompany] = useState(false)
  const [showIncludeCompany, setShowIncludeCompany] = useState(false)

  // Saved searches state
  const [loadingGetSavedSearchData, setLoadingGetSavedSearchData] = useState(false);
  const [searchNameToSave, setSearchNameToSave] = useState();
  const [idSearchNameToSave, setIdSearchNameToSave] = useState();
  const [idSearchNameToDelete, setIdSearchNameToDelete] = useState();
  const [listSavedDataDefault, setListSavedDataDefault] = useState([]);
  const [listSavedData, setListSavedData] = useState([]);
  const [openEditSavedSearch, setOpenEditSavedSearch] = useState(false);
  const [loadingEditSearchName, setLoadingEditSearchName] = useState(false);
  const [loadingSaveAsASequence, setLoadingSaveAsASequence] = useState(false);
  const [openSavedSearch, setOpenSavedSearch] = useState(false);
  const [loadingSavedSearch, setLoadingSavedSearch] = useState(false);

  const userToSet = profileUser || profileUserAuth;
  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    formState,
    getValues,
  } = useForm({
    defaultValues: {
      searchSignalIds: [],
      accountStageIds: [],
      notAccountStageIds: [],
    },
  });

  const handleGetTechno = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'technology',
    });
    setDataTechnologies(data?.tags);
  };

  const handleDeleteIntent = async (value) => {
    const index = listDataIntentSetting.find((obj) => obj.id === value);
    if (index) {
      const newArr = listDataIntentSetting.filter((obj) => obj.id !== value);
      setListDataIntentSetting(newArr);
    }
  };

  const [showFullText, setShowFullText] = useState(false);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const handleChangeSettingIntent = async () => {
    setDataParentIntent(listDataIntentSetting);
    setShowModalAddTopic(false);
    localStorage.setItem(
      'listIntentChoose',
      JSON.stringify(listDataIntentSetting)
    );
  };

  const handleGetIntentCategory = async () => {
    const data = await employeeFinderIntentCategory();
    setDataListTopicTitle(data?.data?.categories);
  };

  useEffect(() => {
    handleGetIntentCategory();
  }, [showModalAddTopic]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFindCompany'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFindCompany')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany( watch('companyFinderNotAny'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderNotAny')]);

  const handleGetRevenue = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataRevenue(data?.faceting?.organization_trading_status_facets);
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFindCompanyText(watch('locationFindCompanyText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  const { data: companyList = [] } = useQuery(
    ['GET_LIST_COMPANY', debouncedSearchTextCompany],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompany,
      });
      return data.organizations;
    },
    { enabled: !!debouncedSearchTextCompany }
  );

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);

    switch (keys[0]) {
      case '6':
        refetchListSignals();
        break;
      case '7':
        refetchListFacets();
        break;
      default:
        break;
    }
  };

  const { data: locationListCompany = [] } = useQuery(
    ['GET_LIST_LOCATION', debouncedLocationFindCompanyText],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedLocationFindCompanyText,
        type: 'location',
      });
      return data.tags;
    },
    { enabled: !!debouncedLocationFindCompanyText }
  );

  const { data: employeeList = [] } = useQuery(
    ['GET_LIST_EMPLOYEE'],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: '',
        type: 'employee',
      });
      return data.faceting.num_employees_facets;
    },
    { enabled: true }
  );

  const { data: industryListCompany = [] } = useQuery(
    ['GET_LIST_INDUSTRYCompany', debouncedSearchTextIndustryCompany],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextIndustryCompany,
        type: 'linkedin_industry',
      });
      return data.tags;
    },
    { enabled: !!debouncedSearchTextIndustryCompany }
  );

  const { data: titleList = [] } = useQuery(
    ['GET_LIST_TITLE', debouncedSearchTextTitle],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextTitle,
        type: 'person_title',
      });
      return data.tags;
    },
    { enabled: !!debouncedSearchTextTitle }
  );

  const { data: listSignals, refetch: refetchListSignals } = useQuery(
    ['GET_LIST_SIGNALS'],
    async () => {
      const { data } = await employeeFinderSearchSignals();
      return data?.templates || [];
    },
    { enabled: false }
  );

  const { data: listFacets, refetch: refetchListFacets } = useQuery(
    ['GET_LIST_FACETS'],
    async () => {
      const { data } = await getFacets();
      return data?.faceting?.account_stage_facets || [];
    },
    { enabled: false }
  );

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustryCompany(
        watch('industryKeywordCompanyText')
      );
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  const handleGetFunding = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataFunding(data?.faceting?.latest_funding_stage_facets);
  };

  const handleGetPersonalTitles = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'person_title',
    });
    setDataPersonTitle(data?.tags);
  };

  const handleGetLocation = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'location',
    });
    setDataLocation(data?.tags);
  };

  useEffect(() => {
    handleGetTechno();
    handleGetPersonalTitles();
    handleGetLocation();
    handleGetRevenue({
      openFactorNames: ['organization_trading_status'],
    });
    handleGetFunding({
      openFactorNames: ['organization_latest_funding_stage_cd'],
    });
  }, []);

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});
  const [detailOrganization, setDetailOrganization] = useState({});
  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };

  const handlePaginationListCompany = (page) => {
    handleFindCompany({ page: page });
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextTitle(watch('titleFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  const formatNumber = (number) => {
    const suffixes = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    let suffixIndex = 0;
    while (number >= 1000) {
      number /= 1000;
      suffixIndex++;
    }
    return number.toFixed(1) + suffixes[suffixIndex];
  };

  // Saved search functions
  const handleGetListSavedCompanies = async () => {
    setLoadingGetSavedSearchData(true);
    try {
      const { data } = await getSavedSearch('SAVED_COMPANY_SEARCHES_V1');
      setListSavedData(data?.result?.data || []);
      setListSavedDataDefault(data?.result?.data || []);
    } catch (error) {
      console.error('Error fetching saved searches:', error);
      setListSavedData([]);
      setListSavedDataDefault([]);
    }
    setLoadingGetSavedSearchData(false);
  };

  const handleEditSavedSearch = async () => {
    const payload = {
      searchName: searchNameToSave,
    };
    setLoadingEditSearchName(true);

    try {
      await updateSavedSearch(idSearchNameToSave, payload);
      await handleGetListSavedCompanies();
      setLoadingEditSearchName(false);
      setOpenEditSavedSearch(false);
      notification.success({
        message: 'Edit saved search successfully !!!',
      });
    } catch (error) {
      setLoadingEditSearchName(false);
      notification.error({
        message: 'Failed to edit saved search',
      });
    }
  };

  const handleDeleteSavedSearch = async (id) => {
    try {
      await deleteSavedSearch(id);
      await handleGetListSavedCompanies();
      notification.success({
        message: 'Delete saved search successfully !!!',
      });
    } catch (error) {
      notification.error({
        message: 'Failed to delete saved search',
      });
    }
  };

  const handleSaveSearch = async (searchName) => {
    let filterFields = {
      // Search terms
      searchCompany: watch('searchCompany'),

      // Company filters
      companyFindCompany: watch('companyFindCompany'),
      companyFindCompanySelect: watch('companyFindCompanySelect'),
      companyFindCompanyId: watch('companyFindCompanyId'),

      // Location filters
      locationFindCompany: watch('locationFindCompany'),
      locationFindCompanyText: watch('locationFindCompanyText'),

      // Employee filters
      employeesFindCompany: watch('employeesFindCompany'),
      employeesFindCompanyText: watch('employeesFindCompanyText'),

      // Industry filters
      industryKeywordCompany: watch('industryKeywordCompany'),
      industryKeywordCompanyText: watch('industryKeywordCompanyText'),

      // Keywords filters
      includeKeywordCompany: watch('includeKeywordCompany'),
      includeKeywordCompanyText: watch('includeKeywordCompanyText'),
      includeAllKeywordCompany: watch('includeAllKeywordCompany'),
      includeAllKeywordCompanyText: watch('includeAllKeywordCompanyText'),
      excludeKeywordsCompany: watch('excludeKeywordsCompany'),
      excludeKeywordsCompanyText: watch('excludeKeywordsCompanyText'),

      // Signals filters
      searchSignalIds: watch('searchSignalIds'),

      // Account stage filters
      accountStageIds: watch('accountStageIds'),
      notAccountStageIds: watch('notAccountStageIds'),

      // Score filters
      contactMinimumScore: watch('contactMinimumScore'),

      // Technologies filters
      listTechnologies: watch('listTechnologies'),

      // Revenue filters
      revenueStatus: watch('revenueStatus'),
      revenueStatusItem: watch('revenueStatusItem'),

      // Funding filters
      fundingStatusItem: watch('fundingStatusItem'),
      fundingMin: watch('fundingMin'),
      fundingMax: watch('fundingMax'),

      // Buying intent filters
      contactBuyingIntentScore: watch('contactBuyingIntentScore'),
      contactBuyingIntentIds: watch('contactBuyingIntentIds'),

      // Job posting filters
      contactJobLocated: watch('contactJobLocated'),
      listCurrentlyHiring: watch('listCurrentlyHiring'),
      organizationNumJobsRangeMin: watch('organizationNumJobsRangeMin'),
      organizationNumJobsRangeMax: watch('organizationNumJobsRangeMax'),
      organizationJobPostedAtRangeMin: watch('organizationJobPostedAtRangeMin'),
      organizationJobPostedAtRangeMax: watch('organizationJobPostedAtRangeMax'),

      // Accordion state
      keyAcordionCompany: watch('keyAcordionCompany'),
    };

    const payload = {
      searchName: searchName,
      searchType: 'SAVED_COMPANY_SEARCHES_V1',
      filterFields: filterFields,
    };

    try {
      await createSavedSearch(payload);
      notification.success({
        message: 'Create Saved search success !!!',
      });
      handleGetListSavedCompanies();
    } catch (error) {
      notification.error({
        message: 'Failed to save search',
      });
    }
  };

  const functionSaveSearch = async () => {
    setLoadingSavedSearch(true);
    try {
      await handleSaveSearch(searchNameToSave);
      setLoadingSavedSearch(false);
      setOpenSavedSearch(false);
    } catch (error) {
      setLoadingSavedSearch(false);
    }
  };

  const handleLoadSavedSearch = (filterFields) => {
    // Set all filter values using setValue
    if (filterFields) {
      Object.keys(filterFields).forEach(key => {
        if (filterFields[key] !== undefined && filterFields[key] !== null) {
          setValue(key, filterFields[key]);
        }
      });
    }
  };

  // useEffect to load saved searches on component mount
  useEffect(() => {
    setListSavedData([]);
    setListSavedDataDefault([]);
    handleGetListSavedCompanies();
  }, []);

  const filterColumnCompany = () => {
    return (
      <div className='w-full flex flex-col gap-2 max-h-[70vh] overflow-y-auto'>
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['keywords']}
          items={[
            {
              key: 'keywords',
              label: (
                <div className="flex items-center gap-2">
                  <FileSearchOutlined className="text-lg" />
                  <span>Saved Searches</span>(
                  {loadingGetSavedSearchData ? (
                    <Spin size="small"></Spin>
                  ) : (
                    listSavedData.length
                  )}
                  )
                </div>
              ),
              children: (
                <div>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Type here to search ..."
                      onChange={(e) => {
                        const searchText = e.target.value;
                        const newOptions =
                          listSavedDataDefault?.filter((v) =>
                            v?.searchName
                              ?.toString()
                              .toLocaleLowerCase()
                              .includes(searchText.toLocaleLowerCase())
                          ) ?? [];
                        setListSavedData(newOptions);
                      }}
                    />
                  </div>
                  <div
                    style={{
                      maxHeight: '200px',
                      overflowY: 'scroll',
                    }}
                  >
                    {listSavedData?.map((item) => (
                      <div
                        key={item.id}
                        style={{
                          cursor: 'pointer',
                          width: '100%',
                          height: '30px',
                          display: 'flex',
                          alignItems: 'center',
                          marginTop: '10px',
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <Dropdown
                            rootClassName="font-Montserrat"
                            menu={{
                              items: [
                                {
                                  label: 'Edit Name',
                                  key: 'edit_name',
                                  icon: <CopyOutlined />,
                                },
                                {
                                  label: 'Delete',
                                  key: 'delete',
                                  icon: <DeleteOutlined />,
                                },
                              ],
                              onClick: (e) => {
                                if (e.key === 'edit_name') {
                                  setSearchNameToSave(item?.searchName);
                                  setOpenEditSavedSearch(true);
                                  setIdSearchNameToSave(item?.id);
                                }

                                if (e.key === 'delete') {
                                  setIdSearchNameToDelete(item?.id);
                                  handleDeleteSavedSearch(item?.id);
                                }
                              },
                            }}
                            disabled={loadingSaveAsASequence}
                          >
                            <MoreOutlined
                              style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                              }}
                            />
                          </Dropdown>
                          <UserOutlined style={{ marginLeft: '10px' }} />
                          <span
                            onClick={() => {
                              handleLoadSavedSearch(item?.filterFields);
                            }}
                            style={{ marginLeft: '10px' }}
                          >
                            {item?.searchName}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['company-name']}
          items={[
            {
              key: 'company-name',
              label: (
                <div className="inline-grid">
                  <span>Company</span>
                  {watch('companyFindCompanyId') !== '' &&
                    watch('companyFindCompanyId') && (
                      <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                        {watch('companyFindCompany')}
                      </span>
                    )}
                </div>
              ),
              children: (
                <div>
                  <Form.Item label="Company" name="companyFindCompany">
                    <Controller
                      render={({ field }) => (
                        <AutoComplete
                          style={{ width: '250px' }}
                          {...field}
                          options={companyList.map((option) => ({
                            value: option.id,
                            label: (
                              <div className="grid p-2">
                                <div className="flex justify-between">
                                  <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                    {option.name}
                                    <br />
                                    <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                      {option.domain || '-'}
                                    </span>
                                  </span>
                                  <img
                                    className="absolute right-3"
                                    src={option?.logo_url ? `${option?.logo_url}` : ''}
                                    width={50}
                                    height={50}
                                    alt="Logo"
                                  />
                                </div>
                              </div>
                            ),
                          }))}
                          onSearch={(value) => {
                            setValue('companyFindCompany', value);
                            setValue('companyFindCompanySelect', null);
                            setValue('companyFindCompanyId', null);
                          }}
                          onSelect={async (selectedCompanyId) => {
                            const selectedCompany = companyList.find(
                              (ao) => ao.id == selectedCompanyId
                            );
                            setValue('companyFindCompany', selectedCompany.name);
                            setValue('companyFindCompanySelect', selectedCompanyId);
                            setValue('companyFindCompanyId', selectedCompanyId);
                          }}
                        >
                          <Input />
                        </AutoComplete>
                      )}
                      name="companyFindCompany"
                      control={control}
                    />
                  </Form.Item>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['account-location']}
          items={[
            {
              key: 'account-location',
              label: (
                <div className="inline-grid">
                  <span>Account Location</span>
                  {watch('locationFindCompany') &&
                    watch('locationFindCompany')?.length > 0 && (
                      <Row>
                        {watch('locationFindCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Account Location" name="locationFindCompany">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          setValue('locationFindCompanyText', searchText);
                        }}
                        {...field}
                        notFoundContent={null}
                        options={locationListCompany.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="locationFindCompany"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['employees']}
          items={[
            {
              key: 'employees',
              label: (
                <div className="inline-grid">
                  <span>Employees</span>
                  {watch('employeesFindCompany') &&
                    watch('employeesFindCompany')?.length > 0 && (
                      <Row>
                        {watch('employeesFindCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Employees" name="employeesFindCompany">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          if (employeeList.length == 0) {
                            setValue('employeesFindCompanyText', searchText);
                          }
                        }}
                        {...field}
                        notFoundContent={null}
                        options={employeeList.map((so) => ({
                          ...so,
                          label: so.display_name,
                          value: so.value,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="employeesFindCompany"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['industry-keyword']}
          items={[
            {
              key: 'industry-keyword',
              label: (
                <div className="inline-grid">
                  <span>Industry & Keyword</span>
                  {watch('industryKeywordCompany') &&
                    watch('industryKeywordCompany')?.length > 0 && (
                      <Row>
                        <span className="text-xs my-auto mr-2">Industry: </span>
                        {watch('industryKeywordCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <>
                  <Form.Item label="Industry" name="industryKeywordCompany">
                    <Controller
                      render={({ field }) => (
                        <Select
                          labelInValue
                          mode="multiple"
                          onSearch={(searchText) => {
                            setValue('industryKeywordCompanyText', searchText);
                          }}
                          {...field}
                          notFoundContent={null}
                          options={industryListCompany.map((so) => ({
                            ...so,
                            label: so.cleaned_name,
                            value: so.id,
                          }))}
                          filterOption={(inputValue, option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) !== -1
                          }
                        />
                      )}
                      name="industryKeywordCompany"
                      control={control}
                    />
                  </Form.Item>
                  <Checkbox
                    className="w-full"
                    checked={showIncludeKeywordCompany}
                    onChange={(e) => {
                      setShowIncludeKeywordCompany(e.target.checked);
                      setValue('includeKeywordCompany', []);
                      setValue('includeKeywordCompanyText', '');
                    }}
                  >
                    Include Keywords
                  </Checkbox>
                  {showIncludeKeywordCompany && (
                    <Form.Item name="includeKeywordCompany">
                      <Controller
                        name="includeKeywordCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('includeKeywordCompanyText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeKeywordCompanyText'),
                                label: watch('includeKeywordCompanyText'),
                                value: watch('includeKeywordCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showIncludeAllKeywordCompany}
                    onChange={(e) => {
                      setShowIncludeAllKeywordCompany(e.target.checked);
                      setValue('includeAllKeywordCompany', []);
                      setValue('includeAllKeywordCompanyText', '');
                    }}
                  >
                    Include ALL
                  </Checkbox>
                  {showIncludeAllKeywordCompany && (
                    <Form.Item name="includeAllKeywordCompany">
                      <Controller
                        name="includeAllKeywordCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('includeAllKeywordCompanyText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeAllKeywordCompanyText'),
                                label: watch('includeAllKeywordCompanyText'),
                                value: watch('includeAllKeywordCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showExcludeKeywordsCompany}
                    onChange={(e) => {
                      setShowExcludeKeywordsCompany(e.target.checked);
                      setValue('excludeKeywordsCompany', []);
                      setValue('excludeKeywordsCompanyText', '');
                    }}
                  >
                    Exclude Keywords
                  </Checkbox>
                  {showExcludeKeywordsCompany && (
                    <Form.Item name="excludeKeywordsCompany">
                      <Controller
                        name="excludeKeywordsCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('excludeKeywordsCompanyText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('excludeKeywordsCompanyText'),
                                label: watch('excludeKeywordsCompanyText'),
                                value: watch('excludeKeywordsCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                </>
              ),
            },
          ]}
        />
      </div>
    );
  };


  const columnsCompany = [
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '200px',
      // width: '25%',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              className=""
              src={record?.logo_url ? `${record?.logo_url}` : ''}
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  setIsDetailEmployeeCompany(true);
                  const { data } = await getListEmployee({
                    organizationId: record.id,
                    page: 1,
                  });
                  if (data.result.people.length === 0)
                    return notification.error({ message: 'Data Not Found' });
                  setValue('keyAcordionPeople', '6');
                  setValue('companyFinderId', record.id);
                  setListEmployeeCompany(data?.result?.people);
                  setListEmployeePaginationCompany(data?.result?.pagination);
                  setIsLoadingEmployee(false);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'estimated_num_employees',
      key: 'estimated_num_employees',
      align: 'left',
      // width: '25%',
      render: (allowRead, record) => (
        <Button
          type="text"
          className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
          onClick={async (e) => {
            e.stopPropagation();
            handleDetailCompany(record?.organization);
          }}
        >
          <UserGroupIcon className="w-4 h-4" />
          {record?.estimated_num_employees}
          <span>Employees</span>
        </Button>
        // <Row gutter={16} className="w-[5rem]">
        //   <p>{record?.estimated_num_employees}</p>
        // </Row>
      ),
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industries',
      key: 'industries',
      width: '350px',
      render: (allowRead, record) =>
        {
          return (
            <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={record?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {record?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < record.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
          )
        }
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      width: '25%',
      render: (allowRead, record) =>
      {
        return (
          <div
          style={{
            fontWeight: '600',
          }}
        >
          <div>
            <Tooltip
              placement="topLeft"
              title={record?.keywords?.map((item, index) => (
                <span key={index}>
                  {item}
                  {index < record.keywords.length - 1 && ', '}
                </span>
              ))}
            >
              <span
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                }}
              >
                {record?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.keywords.length - 1 && ', '}
                  </span>
                ))}
              </span>
            </Tooltip>
          </div>
        </div>
        )
      }
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'left',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              setValue('clientCorporation.name', record?.name);
              setValue('clientCorporation.state', record?.state);
              setValue('clientCorporation.billingZip', record?.postal_code);
              setValue('clientCorporation.zip', record?.postal_code);
              setValue('clientCorporation.companyWebsite', record?.website_url);
              setValue('clientCorporation.mainPhone', record?.sanitized_phone);
              setValue('clientCorporation.companyAddress', record?.raw_address);
              // await handleAddContact(record?.name, record);
              setShowCompany(true);
              // setHandleCloseClient(true);
              console.log(record);
            }}
            // type="text"
            // style={{ borderRadius: '0' }}
            className="flex items-center gap-2 !rounded-md border border-[#e2642a]"
          >
            <div>Add new</div>
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.LOCATION,
      dataIndex: 'location',
      key: 'location',
      width: '250px',
      render: (allowRead, record) =>
      {
        return (
          <div style={{ fontWeight: 600, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {record?.country} {record?.country ? `, ${record?.country}` : ""}
          </div>
        );
      }
    },
  ];

  const dataCompanyNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingCompanies(false);
  };

  const handleSubmitCompanyFind = async ({ page = 1 }) => {
    if (watch('searchCompany') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindCompany({ page });
  };

  const handleSubmitCompany = async (e) => {
    const companyPayload = getValues().clientCorporation;
    const companyId = getValues().clientContact?.companyId;

    const {
      name,
      status,
      mainPhone,
      companyAddress,
      stateId,
      countySelect,
      zip,
      standardFeeArrangement,
      industries,
    } = companyPayload;

    if (!name) {
      return notification.error({ message: 'Company name is required.' });
    } else if (!status) {
      return notification.error({ message: 'Status is required.' });
    } else if (checkHenley ? false : industries.length === 0) {
      return notification.error({ message: 'Industries is required.' });
    } else if (!mainPhone) {
      return notification.error({ message: 'Main phone number is required.' });
    } else if (!companyAddress) {
      return notification.error({ message: 'Company address is required.' });
    } else if (!stateId) {
      return notification.error({ message: 'Country is required.' });
    } else if (!countySelect) {
      return notification.error({ message: 'County is required.' });
    } else if (!zip) {
      return notification.error({ message: 'Postcode is required.' });
    }

    if (
      companyPayload?.companyAddress?.length > 100 ||
      companyPayload?.companySecondAddress?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const newCompanyPayload = {
      entityName: 'ClientCorporation',
      name: companyPayload?.name,
      status: companyPayload?.status,
      parentClientCorporation: {
        id:
          companyPayload?.parentCompanyId === ''
            ? undefined
            : companyPayload?.parentCompanyId,
      },
      customText2: companyPayload?.companyOwnerId,
      businessSectorList: !companyPayload.industries
        ? []
        : companyPayload?.industries.length === 0
          ? []
          : companyPayload.industries
              .filter((industry) => industry && industry.label)
              .map((industry) => industry.label),
      notes: companyPayload?.companyComments,
      companyDescription: companyPayload?.companyDescription,
      companyURL: companyPayload?.companyWebsite,
      phone: companyPayload?.mainPhone,
      address: {
        countryName: companyPayload?.state,
        countryID: Number(companyPayload?.stateId),
        address1: companyPayload?.companyAddress,
        address2: companyPayload?.companySecondAddress,
        city: companyPayload?.city,
        zip: companyPayload?.zip,
        state: companyPayload?.county,
        timezone: null,
      },
      billingContact: companyPayload?.billingContact,
      billingPhone: companyPayload?.billingPhone,
      invoiceFormat: companyPayload?.invoiceFormatInformation,
      feeArrangement: companyPayload?.standardFeeArrangement,
      billingAddress: {
        state: companyPayload?.billingZip,
        address1: companyPayload?.billingAddress,
        city: companyPayload?.billingCity,
        zip: companyPayload?.billingZip,
        county: companyPayload?.billingCounty,
      },
    };

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newCompanyPayloadCleaned = cleanPayload(newCompanyPayload);
    newCompanyPayloadCleaned.address.address2 = '';
    newCompanyPayloadCleaned.address.countryCode = '';
    newCompanyPayloadCleaned.address.timezone = null;
    const { data } = await insertBullhorn(newCompanyPayloadCleaned);
    // newCompanyPayload
    // setAddressSearchText(companyPayload?.companyAddress);
    // setValue("address1", companyPayload?.companyAddress);
    // setValue("city", companyPayload?.city)
    // setValue("county", companyPayload?.county)
    // setValue("countySelect", companyPayload?.county)
    // setValue("zip", companyPayload?.zip)
    // setValue("state", companyPayload?.state)
    // setValue("stateId", Number(companyPayload?.stateId))
    // setValue("stateSelected", Number(companyPayload?.stateId))

    setShowCompany(false);
    if (data) {
      notification.success({
        message: 'Success',
        description: 'Company has been created',
      });
      setHandleCloseClient(true);
    }
  };

  const handleFindCompany = async ({ page = 1 }) => {
    try {
      setIsDetailEmployeeCompany(false);
      setIsLoadingCompanies(true);
      const locations = getValues().locationFindCompany
        ? getValues().locationFindCompany.map((item) => item.label)
        : [];
      const employeeRanges = getValues().employeesFindCompany
        ? getValues().employeesFindCompany.map((item) => item.label)
        : [];
      const personTitles = getValues().titleFinder
        ? getValues().titleFinder.map((item) => item.label)
        : [];
      const industryTagIds = getValues().industryKeywordCompany
        ? getValues().industryKeywordCompany.map((item) => item.value)
        : [];
      const searchSignalIds = getValues().searchSignalIds
        ? getValues().searchSignalIds
        : [];
      const accountStageIds = getValues().accountStageIds
        ? getValues().accountStageIds
        : [];
      const notAccountStageIds = getValues().notAccountStageIds
        ? getValues().notAccountStageIds
        : [];
      const containOneKeywords = watch('includeKeywordCompany')
        ? watch('includeKeywordCompany').map((item) => item.label)
        : [];
      const containAllKeyWords = watch('includeAllKeywordCompany')
        ? watch('includeAllKeywordCompany').map((item) => item.label)
        : [];
      const excludeKeyWords = watch('excludeKeywordsCompany')
        ? watch('excludeKeywordsCompany').map((item) => item.label)
        : [];
      const currentlyUsingAnyOfTechnologyUids =
        getValues('listTechnologies') ?? [];
      const recommendationScoresMinTranche =
        getValues().contactMinimumScore ?? null;

      const intentStrengths = getValues().contactBuyingIntentScore
        ? getValues().contactBuyingIntentScore
        : [];
      const intentIds = getValues().contactBuyingIntentIds
        ? getValues().contactBuyingIntentIds
        : [];
      const notExistFields =
        getValues('revenueStatus') === 'is_un_known'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const existFields =
        getValues('revenueStatus') === 'is_know'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const organizationJobLocations = getValues('contactJobLocated') ?? [];
      const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
      const organizationNumJobsRangeMin =
        getValues('organizationNumJobsRangeMin') ?? null;
      const organizationNumJobsRangeMax =
        getValues('organizationNumJobsRangeMax') ?? null;
      const organizationLatestFundingStageCd =
        getValues('fundingStatusItem') ?? [];
      const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
      const totalFundingRangeMin = getValues('fundingMin') ?? null;
      const totalFundingRangeMax = getValues('fundingMax') ?? null;
      const organizationJobPostedAtRangeMin =
        getValues('organizationJobPostedAtRangeMin') ?? null;
      const organizationJobPostedAtRangeMax =
        getValues('organizationJobPostedAtRangeMax') ?? null;
      const notOrganizationIds = getValues('companyFinderNotAnySelect') ?? []

      const { data } = await getListCompanies({
        organizationId: watch('companyFindCompanyId'),
        locations,
        personTitles,
        employeeRanges,
        industryTagIds,
        page,
        searchText: watch('searchCompany'),
        containOneKeywords,
        containAllKeyWords,
        excludeKeyWords,
        searchSignalIds,
        accountStageIds,
        notAccountStageIds,
        recommendationScoresMinTranche,
        currentlyUsingAnyOfTechnologyUids,
        intentStrengths,
        intentIds,
        existFields,
        notExistFields,
        organizationTradingStatus,
        organizationLatestFundingStageCd,
        totalFundingRangeMax,
        totalFundingRangeMin,
        organizationJobLocations,
        qOrganizationJobTitles,
        organizationNumJobsRangeMin,
        organizationNumJobsRangeMax,
        organizationJobPostedAtRangeMin,
        organizationJobPostedAtRangeMax,
        notOrganizationIds
      });
      if (data.length === 0) return dataCompanyNotFound();
      if (data.organizations.length === 0) return dataCompanyNotFound();
      setListCompanies(data?.organizations);
      setListCompaniesPagination(data?.pagination);
      setIsLoadingCompanies(false);
    } catch (err) {
      setListCompanies([]);
      setListCompaniesPagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingCompanies(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const updateArrayByKey = (key, checked, optionKey) => {
    const newValues = checked
      ? [...getValues()[key], optionKey]
      : getValues()[key].filter((value) => value !== optionKey);

    setValue(key, newValues);
  };

  return (
    <div>
      <Row>
        <Col flex={'300px'}>
          <Row gutter={16}>
            <Form
              className="w-full pr-4"
              layout="vertical"
              onFinish={handleSubmit(handleSubmitCompanyFind)}
            >
              <Col className="w-full mr-4">
                <Form.Item
                  label="Search"
                  name="searchCompany"
                  className="mb-2 mt-2"
                >
                  <Controller
                    render={({ field }) => (
                      <Input
                        prefix={<SearchOutlined />}
                        {...field}
                        placeholder="Search Company ..."
                      />
                    )}
                    name="searchCompany"
                    control={control}
                  />
                </Form.Item>
              </Col>
            </Form>
            <Form
              className="w-full pr-4"
              layout="vertical"
              onFinish={handleSubmit(handleFindCompany)}
            >
              <Col className="w-full">
                {filterColumnCompany()}
              </Col>
              <Col className="w-full mr-4">
                <Button
                  disabled={isLoading}
                  loading={isLoading}
                  htmlType="submit"
                  type="primary"
                  className="bg-cyan-600 font-semibold text-white"
                  style={{
                        width: '100%',
                        height: '40px',
                        padding: '0 20px',
                        marginTop: "20px"
                  }}
                >
                  Search
                </Button>

                <Button
                  type="primary"
                  className="font-semibold text-white"
                  style={{
                    width: '100%',
                    height: '40px',
                    padding: '0 20px',
                    marginTop: '10px',
                  }}
                  onClick={() => {
                    setOpenSavedSearch(true);
                  }}
                >
                  Save this search
                </Button>
              </Col>
            </Form>
          </Row>
        </Col>
        <Col flex="auto" className="w-2/3 search-table-new-design-container customTable">
        <Tabs
              defaultActiveKey="1"
              items={[
                {
                  key: '1',
                  label: `Total (${formatNumber(listCompaniesPagination.total_entries)})`,
                },
              ]}
            />
          <Table
            // scroll={{ x: true, y: '70vh' }}
            scroll={{
              x: 1700,
              y: "65vh"
            }}
            loading={isLoadingCompanies}
            pagination={false}
            columns={columnsCompany}
            dataSource={listCompanies}
            onRow={(record, rowIndex) => {
              return {
                onClick: (e) => {
                  handleDetailCompany(record);
                },
                style: { cursor: 'pointer' },
              };
            }}
          />
          <Pagination
            className="mt-3"
            defaultCurrent={listCompaniesPagination.page}
            total={listCompaniesPagination.total_entries}
            showSizeChanger={false}
            onChange={handlePaginationListCompany}
            current={listCompaniesPagination.page}
          />
        </Col>
      </Row>

      <CompanyDetailModal showDetailCompany={showDetailCompany} setShowDetailCompany={setShowDetailCompany} detailCompany={detailCompany} detailOrganization={detailOrganization}/>
      
      {/* Saved Search Modals */}
      <Modal
        title="Edit your Search name"
        open={openEditSavedSearch}
        footer={false}
        onCancel={() => setOpenEditSavedSearch(false)}
      >
        <Input
          value={searchNameToSave}
          onChange={(e) => setSearchNameToSave(e.target.value)}
          placeholder="Input your search name ..."
        />
        <Button
          loading={loadingEditSearchName}
          onClick={() => handleEditSavedSearch()}
          style={{ width: '100%', marginTop: '10px' }}
          type="primary"
        >
          Save
        </Button>
      </Modal>

      <Modal
        title="Save your Search"
        open={openSavedSearch}
        footer={false}
        onCancel={() => setOpenSavedSearch(false)}
      >
        <Input
          value={searchNameToSave}
          onChange={(e) => setSearchNameToSave(e.target.value)}
          placeholder="Input your search name ..."
        />
        <Button
          loading={loadingSavedSearch}
          onClick={() => functionSaveSearch(searchNameToSave)}
          style={{ width: '100%', marginTop: '10px' }}
          type="primary"
        >
          Save
        </Button>
      </Modal>
      
      {/* section company add & edit */}
      <Modal
        open={showCompany}
        onCancel={() => {
          setShowCompany(false);
        }}
        width={1000}
        footer={false}
      >
        <div className="bullhorn-job-submission-form-container bg-cyan-600 p-5">
          <Form layout="vertical">
            <Form.Item>
              <Card className=" max-w-full mx-auto bg-[#BEDAFD33] shadow-lg rounded-2xl overflow-hidden hover:shadow-xl">
                <div className="w-full bg-[#BEDAFD] pl-6 py-3 font-semibold text-base">
                  <span>Add New Company</span>
                </div>
                <div className="p-6">
                  <div className="mb-6 border-b-2 border-b-white pb-2">
                    <span className="font-medium text-base text-white ">
                      Add Company
                    </span>
                  </div>
                  <BullHornJobSubmissionCompany
                    isEditCompany={false}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    handleCloseClient={handleCloseClient}
                    setHandleCloseClient={setHandleCloseClient}
                    watch={watch}
                  />
                  <div className="left-0 bottom-0 w-full">
                    <div className="flex gap-4 mr-8">
                      <Button
                        onClick={() => {
                          setShowCompany(false);
                          // functionCompany.companySetStart(0);
                          setValue('companySelect', null);
                          setHandleCloseClient(true);
                          // setValue('companyId', null);
                          // setValue('company', '');
                          // functionContactClient.contactSetOptions([]);
                          // setValue('contactId', null);
                          // setValue('contact', null);
                          // setValue('contactSelect', null);
                          // setValue('email', '');
                          // functionCompany.handleCompanySearch(' ');
                          setIsAddCompany(false);
                        }}
                        className={`bg-[#BEDAFD33] `}
                      >
                        Cancel
                      </Button>
                      <Button
                        htmlType="button"
                        onClick={handleSubmitCompany}
                        type="primary"
                        className={`bg-white text-cyan-600 `}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default EmailFinderCompanyTab;
