import {
  ContactsOutlined,
  DeleteOutlined,
  DownOutlined,
  InfoCircleOutlined,
  MailOutlined,
  MessageOutlined,
  PlusOutlined,
  SaveOutlined,
  SendOutlined,
  UsergroupAddOutlined,
  CommentOutlined,
  HomeOutlined,
  NotificationFilled,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Checkbox,
  Collapse,
  Form,
  Input,
  notification,
  Select,
  Spin,
  Tag,
  Tooltip,
} from 'antd';
import * as React from 'react';
import { useRef } from 'react';
import {
  getContactList,
  LINKINEDIN_REQUEST_TYPE,
  VALID_EMAIL_STATUS,
} from './EmailtriggerStep';
import { useState } from 'react';
import { useEffect } from 'react';
import {
  decodeHtmlEntities,
  stripHTMLTags,
  compactWhitespace,
  arrayUniqueByKey,
} from '../../utils/common';
import clsx from 'clsx';
import { Controller, useForm } from 'react-hook-form';
import { searchBullhornData } from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import ContactListModal from '../../containers/Sequence/ContactListModal';
import HotListModal from '../../containers/Sequence/HotListModal';
import { v4 as uuid } from 'uuid';
import {
  getContactLists,
  getHotLists,
} from '../../containers/Sequence/SequenceDetail';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from './BullhornSendEmailModal';
import {
  VALIDATE_STATUS_COLOR,
  VALIDATE_STATUS_ICON,
} from '../../containers/HotList/HotListTable';

const initialPeriod = {
  unit: '',
  period: 1,
};

const LinkedInRequest = ({
  index,
  inputNumber,
  inputNumberStep,
  setInputNumberStep,
  setEditingNotes,
  getGlobalValues,
  fromCreateByVacancy,
  fromSync,
  setGlobalValue,
  isEdit,
  mergeFieldsBasedOnPlace,
  addSavedSteps,
  endEdittingStep = () => {},
  contactList = [],
  validatingLoading,
}) => {
  const { control, handleSubmit, setValue } = useForm();

  const [linkedinStepData, setLinkedinStepData] = useState(null);
  const [isUsingOwn, setUsingOwn] = useState(true);
  const [activeMessageKeys, setActiveMessageKeys] = useState([
    'normal-message',
  ]);

  const [activeInvitationKeys, setActiveInvitationKeys] = useState([
    'invitation',
  ]);

  const [activeInmailKeys, setActiveInmailKeys] = useState(['inmail']);

  const [openParticipantModal, setOpenParticipantModal] = useState(false);
  const [sendToList, setSendToList] = useState([]);

  // contact options from list

  const [topContactOptions, setTopContactOptions] = useState([]);
  // const [loadingContacts, setLoadingContacts] = useState(true);

  const onActiveMessageKeyChange = (key) => {
    setActiveMessageKeys([...key]);
  };

  console.log('contactList', contactList);

  const onActiveInvitationKeyChange = (key) => {
    setActiveInvitationKeys([...key]);
  };

  const onActiveInMailKeyChange = (key) => {
    setActiveInmailKeys([...key]);
  };
  const handleMappingData = async () => {
    try {
      const contactsFromContactList = handleGetTopContactOptions();
      const { content } = inputNumber;
      const parsedContent = JSON.parse(content);
      setLinkedinStepData({ ...inputNumber, content: parsedContent });
      const usingOwnParticipants =
        getGlobalValues('linkedinStepParticipants') || [];

      const sendToTemp = usingOwnParticipants?.find(
        (item) => item?.key === inputNumber?.key || item?.id === inputNumber?.id
      )?.recipients || [...(inputNumber?.recipients || [])];
      const uniqueSendToTemp = arrayUniqueByKey(sendToTemp, 'email');

      setSendToList([
        ...uniqueSendToTemp.map((it) => ({
          ...it,
          label: it.name,
          value: (it?.id || it.email) + '_' + it.name,
        })),
      ]);

      // Mapping participants data
      if (usingOwnParticipants?.length > 0) {
        const participantObj = usingOwnParticipants?.find(
          (item) =>
            item?.key === inputNumber?.key || item?.id === inputNumber?.id
        );
        if (!participantObj) return;

        setUsingOwn(true);
      }

      // Mapping unfollow duration
      const isActiveBasedOnData = parsedContent?.find(
        (item) => item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
      )?.unit;
      if (isActiveBasedOnData) {
        setActiveInmailKeys(['follow']);
      }
      if (contactList?.length > 0 && contactsFromContactList?.length === 0)
        return;

      // Update selected contacts
      const linkedinStepParticipantsTemp = [...uniqueSendToTemp];
      const allowContactIds = contactsFromContactList?.map((item) => item?.id);
      const filteredSendToList = linkedinStepParticipantsTemp?.filter((item) =>
        allowContactIds?.includes(item?.id)
      );
      setValue(
        'linkedin.participants',
        filteredSendToList?.map((data) => data?.email)
      );
      setSendToList(
        arrayUniqueByKey(
          [...sendToList, ...(filteredSendToList || [])],
          'email'
        )
      );
      // setLoadingContacts(false);
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const handleMappingList = (list) =>
    list?.map((contact) => ({
      ...contact,
      occupation: contact?.contactTitle,
      clientCorporation: { name: contact?.companyName },
      name: contact?.name || contact?.firstName,
      email: contact?.email,
      id: contact?.id,
      emailStatus: contact?.status,
      firstName: contact?.firstName || contact?.name,
      linkedinProfileUrl: contact?.linkedinProfileUrl,
      customText1: contact?.linkedinProfileUrl,
    }));

  const handleGetTopContactOptions = () => {
    let result = [];
    try {
      const rawContactsFromContactList = [...contactList];
      const contactsFromContactList = rawContactsFromContactList
        ?.flatMap((list) => handleMappingList(list?.contacts))
        ?.filter(
          (item) => item && VALID_EMAIL_STATUS.includes(item?.emailStatus)
        );
      setTopContactOptions([...contactsFromContactList]);
      result = contactsFromContactList;
      return result;
    } catch (error) {
      console.log('error: ', error);
      return result;
    }
  };

  useEffect(() => {
    handleMappingData();
  }, [inputNumber, contactList]);

  const handleSave = () => {
    const newItem = [...inputNumberStep];
    const index = newItem.findIndex((item) => item.key === inputNumber?.key);

    const inmailItem = linkedinStepData?.content?.find(
      (item) => item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
    );

    const normalMessageItem = linkedinStepData?.content?.find(
      (item) => item?.type === LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
    );

    if (inmailItem && !inmailItem?.message) {
      notification.warning({
        description: 'InMail Message can NOT empty.',
      });
      return;
    }
    if (normalMessageItem && !normalMessageItem?.message) {
      notification.warning({
        description: 'Message can NOT empty.',
      });
      return;
    }
    if (index !== -1) {
      const stripedHtmlContent = linkedinStepData?.content?.map((step) => ({
        ...step,
        message: decodeHtmlEntities(step?.message.replaceAll('\n', '')),
      }));
      newItem[index] = {
        ...linkedinStepData,
        content: compactWhitespace(JSON.stringify(stripedHtmlContent)),
      };
      //  Participants box  for Linkedin Step
      const linkedinStepParticipantsTemp =
        getGlobalValues('linkedinStepParticipants') || [];

      if (isUsingOwn) {
        const linkedinStepParticipantsObject = {
          id: inputNumber?.id || uuid(),
          key: inputNumber?.key || uuid(),
          recipients: [...sendToList],
        };

        setGlobalValue('linkedinStepParticipants', [
          { ...linkedinStepParticipantsObject },
          ...linkedinStepParticipantsTemp,
        ]);
      } else if (!isUsingOwn) {
        if (linkedinStepParticipantsTemp?.length > 0) {
          const newLinkedinStepParticipantsTemp =
            linkedinStepParticipantsTemp?.filter(
              (item) =>
                item?.key !== inputNumber?.key && item?.id !== inputNumber?.id
            );
          setGlobalValue('linkedinStepParticipants', [
            ...newLinkedinStepParticipantsTemp,
          ]);
        }
      }
    }

    setEditingNotes(null);
    setInputNumberStep(newItem);
    addSavedSteps(newItem[index]?.key || newItem[index]?.id);
    endEdittingStep();
  };

  const handleDeleteStep = () => {
    const newArr = inputNumberStep.filter(
      (item, itemIndex) => itemIndex !== index
    );
    setInputNumberStep([...newArr]);
    setEditingNotes(null);
  };

  const { options: contactOptions, isLoading: isLoadingContacts } =
    useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const handleChange = (value, options, isSelectAll = false) => {
    const dataSet = options
      ?.map((obj) => ({
        ...obj,
        name: obj?.name || obj?.firstName,
        email: obj.email,
        id: obj.contactId,
      }))
      .filter((item) => item?.name && item?.email);

    setValue(
      'linkedin.participants',
      dataSet?.map((data) => data?.email)
    );
    setSendToList([...(dataSet || [])]);
  };

  return (
    <div
      className={clsx(
        'bg-white py-3 px-2 rounded-bl-lg rounded-br-lg shadow-xl mail-content-container w-full',
        isEdit
          ? 'border-2 border-t-0 border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
          : 'border border-[#ccc]'
      )}
    >
      <div className="w-full grid grid-cols-5 gap-3">
        <div className="col-span-5 flex justify-center flex-col gap-2 linkedin-step-container">
          <div
            className={clsx(
              'flex items-center flex-col border rounded-md py-3 px-2 w-full gap-2',
              isUsingOwn && 'border-[#7CF5FF] border-2'
            )}
          >
            <div className="flex items-center justify-between w-full">
              <Checkbox checked={isUsingOwn}></Checkbox>
              {console.log('sendToList', sendToList)}
              <Badge showZero className="w-3/5" count={sendToList?.length || 0}>
                <Button
                  type="dashed"
                  disabled={!isUsingOwn}
                  className="flex items-center font-medium w-full justify-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenParticipantModal(!openParticipantModal);
                  }}
                >
                  <span>Participants</span>
                  {openParticipantModal ? <DownOutlined /> : <PlusOutlined />}
                </Button>
              </Badge>
              <div>
                <UsergroupAddOutlined />
              </div>
            </div>
            {isUsingOwn && (
              <>
                {openParticipantModal && (
                  <div className="w-full linkedin-step-participant-container">
                    <div className="w-full pb-2">
                      <div>
                        <div className="flex items-center w-full mt-2">
                          <p>
                            <span className="text-red-600">*</span> Send To (
                            email )
                          </p>
                        </div>
                        <div>
                          <Form.Item
                            className="add-contact-container !my-2"
                            name="linkedin.participants"
                          >
                            <Controller
                              render={({ field }) => (
                                <Select
                                  onChange={handleChange}
                                  className="w-full"
                                  disabled={
                                    isLoadingContacts || validatingLoading
                                  }
                                  mode="multiple"
                                  placeholder="Please select"
                                  notFoundContent={
                                    isLoadingContacts || validatingLoading ? (
                                      <div className="w-full flex justify-center py-4">
                                        <Spin size="default" />
                                      </div>
                                    ) : null
                                  }
                                  loading={
                                    isLoadingContacts || validatingLoading
                                  }
                                  options={arrayUniqueByKey(
                                    [
                                      ...(getGlobalValues(
                                        'participants.sendTo'
                                      ) || []),
                                      ...topContactOptions,
                                      ...sendToList,
                                    ]?.map((option) => ({
                                      ...option,
                                      value:
                                        (option?.id || option?.email) +
                                        '_' +
                                        option?.name,
                                      label: option?.name || option?.email,
                                      email: option?.email,
                                      phone: option?.phone,
                                      address: option?.address,
                                      status: option?.status,
                                      occupation: option?.occupation,
                                      name: option?.name,
                                      contactId: option?.id,
                                      disabled:
                                        (!option?.name && !option?.firstName) ||
                                        !option?.email,
                                    })),
                                    'email'
                                  )}
                                  value={arrayUniqueByKey(
                                    [...sendToList],
                                    'email'
                                  )}
                                  optionLabelProp="name"
                                  tagRender={(props) => {
                                    const { label, closable, onClose, value } =
                                      props;
                                    const options = arrayUniqueByKey(
                                      [
                                        ...topContactOptions,
                                        ...contactOptions,
                                        ...sendToList,
                                      ]?.concat(
                                        getGlobalValues(
                                          'sendMail.listEmailSend'
                                        ) || []
                                      ),
                                      'id'
                                    );
                                    const optionSelect = options.find(
                                      (item) =>
                                        (item?.id || item.email) +
                                          '_' +
                                          item.name ===
                                        value
                                    );
                                    return (
                                      <Tag
                                        className="mb-1 max-w-[20rem]"
                                        closable={closable}
                                        onClose={onClose}
                                        style={{ marginRight: 3 }}
                                        color={
                                          typeof value === 'number'
                                            ? '#f50'
                                            : value?.includes('CONTACTLIST')
                                              ? 'cyan'
                                              : value?.includes('HOTLIST')
                                                ? 'orange'
                                                : optionSelect?.emailStatus
                                                  ? VALIDATE_STATUS_COLOR[
                                                      optionSelect?.emailStatus
                                                    ]
                                                  : undefined
                                        }
                                        title={label || value}
                                      >
                                        {label || value}
                                      </Tag>
                                    );
                                  }}
                                  optionRender={(opt) => {
                                    const { data: option } = opt;
                                    const lastOption =
                                      [...contactOptions][
                                        contactOptions?.length - 1
                                      ] || {};
                                    const isLastOption =
                                      lastOption &&
                                      lastOption?.id === option?.id;
                                    return (
                                      option?.name && (
                                        <div>
                                          <div className="grid">
                                            <div className="flex items-center gap-1">
                                              <span className="text-base">
                                                {option?.name || option?.label}
                                              </span>
                                              {option?.emailStatus ? (
                                                <Tag
                                                  icon={
                                                    VALIDATE_STATUS_ICON[
                                                      option?.emailStatus
                                                    ]
                                                  }
                                                  color={
                                                    VALIDATE_STATUS_COLOR[
                                                      option?.emailStatus
                                                    ]
                                                  }
                                                >
                                                  {option?.emailStatus}
                                                </Tag>
                                              ) : (
                                                <Tag
                                                  icon={<InfoCircleOutlined />}
                                                >
                                                  Undefined
                                                </Tag>
                                              )}
                                            </div>
                                            <div className="contact-details">
                                              {option?.email && (
                                                <div className="flex">
                                                  <span className="text-gray-700 text-xs min-w-[200px] flex gap-1 items-center">
                                                    <MailOutlined />
                                                    {option?.email
                                                      ? option?.email
                                                      : '-'}
                                                  </span>
                                                  <span className="text-gray-700 text-xs min-w-[200px] flex gap-1 items-center">
                                                    <HomeOutlined />
                                                    {
                                                      option?.clientCorporation
                                                        ?.name
                                                    }
                                                  </span>
                                                </div>
                                              )}
                                              {option?.occupation && (
                                                <div className="flex text-gray-700 text-xs font-medium gap-1 items-center">
                                                  <ContactsOutlined />
                                                  {option?.occupation
                                                    ? option?.occupation
                                                    : '-'}
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                          {isLastOption &&
                                            isLoadingContacts && (
                                              <div className="w-full flex justify-center py-4">
                                                <Spin size="default" />
                                              </div>
                                            )}
                                        </div>
                                      )
                                    );
                                  }}
                                />
                              )}
                              name="linkedin.participants"
                              control={control}
                            />
                          </Form.Item>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          {linkedinStepData?.content?.find(
            (item) => item?.type === LINKINEDIN_REQUEST_TYPE.INVITATION
          ) && (
            <Collapse
              rootClassName={`${
                linkedinStepData?.content
                  ?.map((item) => item.type)
                  ?.includes(LINKINEDIN_REQUEST_TYPE.INVITATION)
                  ? 'linkedin-type-choosen'
                  : ''
              }`}
              accordion
              activeKey={activeInvitationKeys}
              items={[
                {
                  key: 'invitation',
                  label: (
                    <div>
                      <span>Invitation</span>
                      <MailOutlined className="text-base ml-2" />
                    </div>
                  ),
                  children: (
                    <div className="flex flex-col gap-2 mail-content-container max-h-80 overflow-y-auto">
                      <div className="flex items-center gap-2 send-message-text">
                        <SendOutlined className="text-xs" />
                        <div className="font-medium">{`Send Invitation Message`}</div>
                      </div>
                      <CKEditor
                        editor={ClassicEditor}
                        config={{
                          ...editorConfig,
                          toolbar: {
                            ...editorConfig.toolbar,
                            shouldNotGroupWhenFull: false,
                          },
                          mergeFields: {
                            ...mergeFieldsBasedOnPlace,
                          },
                        }}
                        disabled={
                          !linkedinStepData?.content
                            ?.map((item) => item.type)
                            ?.includes(LINKINEDIN_REQUEST_TYPE.INVITATION)
                        }
                        data={
                          linkedinStepData?.content?.find(
                            (item) =>
                              item?.type === LINKINEDIN_REQUEST_TYPE.INVITATION
                          )?.message || ''
                        }
                        onChange={(_event, editor) => {
                          const value = editor?.getData() || '';
                          const content = [...linkedinStepData?.content];

                          const changedItemIndex = content?.findIndex(
                            (item) =>
                              item?.type === LINKINEDIN_REQUEST_TYPE.INVITATION
                          );
                          if (changedItemIndex < 0) return;
                          content[changedItemIndex].message = value;

                          setLinkedinStepData({
                            ...linkedinStepData,
                            content,
                          });
                        }}
                        onAfterDestroy={(editor) =>
                          console.log('destroyyy: ', editor)
                        }
                      />
                    </div>
                  ),
                },
              ]}
              onChange={onActiveInvitationKeyChange}
            />
          )}
          {linkedinStepData?.content?.find(
            (item) => item?.type === LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
          ) && (
            <Collapse
              rootClassName={`${
                linkedinStepData?.content
                  ?.map((item) => item.type)
                  ?.includes(LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE)
                  ? 'linkedin-type-choosen'
                  : ''
              }`}
              accordion
              activeKey={activeMessageKeys}
              items={[
                {
                  key: 'normal-message',
                  label: (
                    <div>
                      <span>Message</span>
                      <MessageOutlined className="text-base ml-2" />
                    </div>
                  ),
                  children: (
                    <div className="flex flex-col gap-2 mail-content-container max-h-96 overflow-y-auto">
                      <div className="flex items-center gap-2 send-message-text">
                        <SendOutlined className="text-xs" />
                        <div className="font-medium">{`Send Message.`}</div>
                      </div>
                      <CKEditor
                        editor={ClassicEditor}
                        config={{
                          ...editorConfig,
                          toolbar: {
                            ...editorConfig.toolbar,
                            shouldNotGroupWhenFull: false,
                          },
                          mergeFields: {
                            ...mergeFieldsBasedOnPlace,
                          },
                        }}
                        disabled={
                          !linkedinStepData?.content
                            ?.map((item) => item.type)
                            ?.includes(LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE)
                        }
                        data={
                          linkedinStepData?.content?.find(
                            (item) =>
                              item?.type ===
                              LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
                          )?.message || ''
                        }
                        onChange={(_event, editor) => {
                          const value = editor?.getData() || '';
                          const content = [...linkedinStepData?.content];

                          const changedItemIndex = content?.findIndex(
                            (item) =>
                              item?.type ===
                              LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
                          );
                          if (changedItemIndex < 0) return;
                          content[changedItemIndex].message = value;

                          setLinkedinStepData({
                            ...linkedinStepData,
                            content,
                          });
                        }}
                        onAfterDestroy={(editor) =>
                          console.log('destroyyy: ', editor)
                        }
                      />
                    </div>
                  ),
                },
              ]}
              onChange={onActiveMessageKeyChange}
            />
          )}
          {linkedinStepData?.content?.find(
            (item) => item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
          ) && (
            <Collapse
              rootClassName={`${
                linkedinStepData?.content
                  ?.map((item) => item.type)
                  ?.includes(LINKINEDIN_REQUEST_TYPE.INMAIL)
                  ? 'linkedin-type-choosen'
                  : ''
              }`}
              accordion
              activeKey={activeInmailKeys}
              items={[
                {
                  key: 'inmail',
                  label: (
                    <div>
                      <span>InMail</span>
                      <CommentOutlined className="text-base ml-2" />
                    </div>
                  ),
                  children: (
                    <div className="flex flex-col gap-2 mail-content-container max-h-96 overflow-y-auto">
                      <div className="flex items-center gap-2 send-message-text">
                        <SendOutlined className="text-xs" />
                        <div className="font-medium">{`Send InMail Message (If not connected).`}</div>
                      </div>
                      <div>
                        <Input
                          placeholder="Subject"
                          className="custom-input-inemail"
                          style={{ borderRadius: 'none !important' }}
                          defaultValue={
                            linkedinStepData?.content?.find(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
                            )?.subject
                          }
                          onChange={(e) => {
                            const content = [...linkedinStepData?.content];
                            const changedItemIndex = content?.findIndex(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
                            );
                            if (changedItemIndex < 0) return;
                            content[changedItemIndex].subject = e.target.value;
                            setLinkedinStepData({
                              ...linkedinStepData,
                              content,
                            });
                          }}
                        />
                      </div>

                      <CKEditor
                        editor={ClassicEditor}
                        config={{
                          ...editorConfig,
                          toolbar: {
                            ...editorConfig.toolbar,
                            shouldNotGroupWhenFull: false,
                          },
                          mergeFields: {
                            ...mergeFieldsBasedOnPlace,
                          },
                        }}
                        disabled={
                          !linkedinStepData?.content
                            ?.map((item) => item.type)
                            ?.includes(LINKINEDIN_REQUEST_TYPE.INMAIL)
                        }
                        data={
                          linkedinStepData?.content?.find(
                            (item) =>
                              item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
                          )?.message || ''
                        }
                        onChange={(_event, editor) => {
                          const value = editor?.getData() || '';
                          const content = [...linkedinStepData?.content];

                          const changedItemIndex = content?.findIndex(
                            (item) =>
                              item?.type === LINKINEDIN_REQUEST_TYPE.INMAIL
                          );
                          if (changedItemIndex < 0) return;
                          content[changedItemIndex].message = value;

                          setLinkedinStepData({
                            ...linkedinStepData,
                            content,
                          });
                        }}
                        onAfterDestroy={(editor) =>
                          console.log('destroyyy: ', editor)
                        }
                      />
                    </div>
                  ),
                  // extra: genInMailExtra(),
                },
              ]}
              onChange={onActiveInMailKeyChange}
            />
          )}
          {linkedinStepData?.content?.find(
            (item) => item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
          ) && (
            <Collapse
              rootClassName={`${
                linkedinStepData?.content
                  ?.map((item) => item.type)
                  ?.includes(LINKINEDIN_REQUEST_TYPE.FOLLOW)
                  ? 'linkedin-type-choosen'
                  : ''
              }`}
              expandIcon={({ isActive }) => {
                const isActiveBasedOnData = linkedinStepData?.content?.find(
                  (item) => item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                )?.period;
                return <Checkbox checked={isActiveBasedOnData || isActive} />;
              }}
              accordion
              activeKey={activeInmailKeys}
              items={[
                {
                  key: 'follow',
                  label: (
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <NotificationFilled className="text-base" />
                        <span className="text-base">Follow Period </span>
                        <span className="text-xs font-medium text-cyan-600 italic">
                          (Optional)
                        </span>
                      </div>
                    </div>
                  ),
                  children: (
                    <div className="flex items-center justify-center gap-4 mail-content-container max-h-96 overflow-y-auto">
                      <>
                        <span>Unfollow after</span>
                        <Input
                          pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}"
                          max={
                            linkedinStepData?.content?.find(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                            )?.unit === 'HOUR'
                              ? 23
                              : 999
                          }
                          min={1}
                          type="number"
                          style={{
                            width: '35%',
                            background: '#fff',
                            padding: '3px',
                            borderRadius: '5px',
                          }}
                          value={
                            linkedinStepData?.content?.find(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                            )?.period
                          }
                          onChange={(e) => {
                            let valueTemp = e.target.value.replaceAll('-', '');

                            if (
                              linkedinStepData?.content?.find(
                                (item) =>
                                  item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                              )?.unit === 'HOUR' &&
                              parseInt(valueTemp) > 23
                            ) {
                              notification.warning({
                                message: 'Warning',
                                description:
                                  'Delay duration can NOT be greater than 23 in Hours!',
                              });
                              return;
                            }
                            const content = [...linkedinStepData?.content];

                            const changedItemIndex = content?.findIndex(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                            );
                            if (changedItemIndex < 0) return;
                            content[changedItemIndex].period =
                              parseInt(valueTemp);

                            setLinkedinStepData({
                              ...linkedinStepData,
                              content,
                            });
                          }}
                        />
                        <Select
                          defaultValue={
                            linkedinStepData?.content?.find(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                            )?.unit || 'DAY'
                          }
                          onChange={(unit) => {
                            const content = [...linkedinStepData?.content];

                            const changedItemIndex = content?.findIndex(
                              (item) =>
                                item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                            );
                            if (changedItemIndex < 0) return;
                            content[changedItemIndex].unit = unit;

                            setLinkedinStepData({
                              ...linkedinStepData,
                              content,
                            });
                          }}
                          options={[
                            {
                              value: 'DAY',
                              label: 'Days',
                            },
                            {
                              value: 'HOUR',
                              label: 'Hours',
                            },
                          ]}
                        />
                      </>
                    </div>
                  ),
                },
              ]}
              onChange={(key) => {
                const content = [...linkedinStepData?.content];

                const changedItemIndex = content?.findIndex(
                  (item) => item?.type === LINKINEDIN_REQUEST_TYPE.FOLLOW
                );
                if (changedItemIndex < 0) return;
                if (key?.length === 0) {
                  delete content[changedItemIndex].unit;
                  delete content[changedItemIndex].period;
                  // setFollowDuration(null);
                } else if (key?.[0] === 'follow') {
                  content[changedItemIndex].unit = 'DAY';
                  content[changedItemIndex].period = 1;
                }

                setLinkedinStepData({
                  ...linkedinStepData,
                  content,
                });
                onActiveInMailKeyChange(key);
              }}
            />
          )}

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Tooltip title="Delete">
              <Button
                onClick={handleDeleteStep}
                icon={<DeleteOutlined className="text-xs" />}
              ></Button>
            </Tooltip>
            <Tooltip title="Save">
              <Button
                onClick={handleSave}
                icon={<SaveOutlined className="text-xs" />}
              ></Button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinkedInRequest;
