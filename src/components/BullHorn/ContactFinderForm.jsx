/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable react/no-unknown-property */
/* eslint-disable react/jsx-key */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */

import _get from 'lodash/get';
import _find from 'lodash/find';

import { useForm, Controller } from 'react-hook-form';
import {
  Input,
  Switch,
  DatePicker,
  Select,
  Button,
  InputNumber,
  Form,
  Divider,
  AutoComplete,
  Modal,
  Spin,
  Table,
  Row,
  Col,
  Space,
  notification,
  ConfigProvider,
  Radio,
  Card,
  Pagination,
  Collapse,
  Tabs,
  Checkbox,
  List,
  Empty,
  Dropdown,
  Image,
  Popover,
  Flex,
  Typography,
  Tag,
  Segmented,
  Popconfirm,
  Tooltip,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import {
  searchBullhorn,
  insertBullhorn,
  upladteBullhorn,
  deleteBullhornContact,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  FileTextOutlined,
  BankFilled,
  StarOutlined,
  WalletOutlined,
  SettingOutlined,
  CheckOutlined,
  PlusOutlined,
  MailOutlined,
  CloseCircleOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  ContactsOutlined,
  EditOutlined,
  LinkedinOutlined,
  FacebookOutlined,
  TwitterOutlined,
  LinkOutlined,
  SearchOutlined,
  BankOutlined,
  PoundOutlined,
  CopyOutlined,
  CaretDownOutlined,
  MenuUnfoldOutlined,
  SendOutlined,
  SmallDashOutlined,
  DownOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  employeeFinderSearchTag,
  getLDetailEmployee,
  getListCompany,
  getListEmployee,
  getListCompanies,
  employeeFinderIntentCategory,
  employeeFinderSearchSignals,
  getRevenue,
  getOrganizationsSnippet,
} from '../../services/employee';

import BullHornJobSubmissionCompany from './BullhornJobSubmissionCompany';
import BullhornSubmissionContact from './BullhornSubmissionContact';
import { getDetailCompanyById, getFacets } from '../../services/companyFinder';
import { Paper, TableContainer } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectConfigForm } from '../../store/common';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import BullhornSendEmail from './BullhornSendEmailModal';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import logo from '/logo_bull.webp';
import ModalShowListExitSequence from '../../containers/Sequence/ModalShowListExitSequence';
import ModalListUserGroup from '../../containers/Sequence/ModalListUserGroup';
import ExistingContactItems from './ExistingContactItems';
import { validListEmail } from '../../services/emailFinder';
import deleteIcon from '../../assets/img/icons/delete-icon.png';
import IntentCollap from './IntentCollap';
import { FaRegBuilding, FaRegUser } from 'react-icons/fa';
import { UserGroupIcon } from '../Sidebar/consts';
import BullhornBulkAddContactModal from './BullhornBulkAddContactModal';
import CompanyDetailModal from './CompanyDetail';
import { COMMON_STRINGS } from '../../constants/common.constant';

// const { Option } = Select;
const { Meta } = Card;
export const HENLEY = 'Henley';
function ContactFinderForm({
  control,
  setValue,
  getValues,
  job,
  setHandleCloseContact,
  functionContactClient,
  setIsSearchCompany,
  watch,
  onClose = false,
  isModalStaffOpen,
  setIsModalStaffOpen,
  isAddContactForm,
  setIsAddContactForm,
  isAddContactFormBulk,
  setIsAddContactFormBulk,
  showDetailContact,
  setShowDetailContact,
  setDetailDataContact,
}) {
  const configForm = useSelector(selectConfigForm);
  // Custom hooks for search with debounce
  const { handleSubmit } = useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // const [isModalStaffOpen, setIsModalStaffOpen] = useState(false);
  const [isContactActionLoading, setContactActionLoading] = useState(false);
  const [startCreateContact, setStartCreateContact] = useState(false);

  const [listEmployee, setListEmployee] = useState([]);
  const [listEmployeePagination, setListEmployeePagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [sequenceStatus, setSequenceStatus] = useState(false);
  const [listEmail, setListEmail] = useState([]);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [dataParentIntent, setDataParentIntent] = useState([]);
  const [listDataIntentSetting, setListDataIntentSetting] = useState([]);
  const [dataListTopicTitle, setDataListTopicTitle] = useState([]);
  const [dataTechnologies, setDataTechnologies] = useState([]);
  const [dataIndustryList, setDataIndustryList] = useState([]);
  const [dataEmployeeList, setDataEmployeeList] = useState([]);
  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [listDataFunding, setListDataFunding] = useState([]);
  const [fundingSize, setFundingSize] = useState(true);

  const [listEmployeeCompany, setListEmployeeCompany] = useState([]);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
    useState({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });

  const [listCompanies, setListCompanies] = useState([]);
  const [listCompaniesPagination, setListCompaniesPagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isDetailEmployeCompany, setIsDetailEmployeeCompany] = useState(false);

  const [isEditCompany, setIsEditCompany] = useState(false);
  const [isAddCompany, setIsAddCompany] = useState(false);
  const [isAddContact, setIsAddContact] = useState(false);
  const [flagDetailContact, setFlagDetailContact] = useState(false);
  const [flagEditContact, setFlagEditContact] = useState(false);
  const [isSubmitForm, setIsSubmitForm] = useState(false);
  const [openExistingSequence, setOpenExistingSequence] = useState(false);
  const [openExistingUserGroup, setOpenExistingUserGroup] = useState(false);
  const [listCurrentOrg, setListCurrentOrg] = useState([]);
  const [currentContact, setCurrentContact] = useState();
  const [bulkContacts, setBulkContacts] = useState([]);
  const [employeesChecked, setEmployeesChecked] = useState([]);

  const [
    debouncedSearchTextCompanyPeople,
    setDebouncedSearchTextCompanyPeople,
  ] = useState('');
  const [debouncedSearchTextCompany, setDebouncedSearchTextCompany] =
    useState('');
  const [debouncedLocationFinderText, setDebouncedLocationFinderText] =
    useState('');
  const [
    debouncedLocationFindCompanyText,
    setDebouncedLocationFindCompanyText,
  ] = useState('');
  const [debouncedSearchTextIndustry, setDebouncedSearchTextIndustry] =
    useState('');
  const [
    debouncedSearchTextIndustryCompany,
    setDebouncedSearchTextIndustryCompany,
  ] = useState('');
  const [debouncedSearchTextTitle, setDebouncedSearchTextTitle] = useState('');
  const [listDetailEmployee, setListDetailEmployee] = useState([]);
  const [showIncludeKeywordPeople, setShowIncludeKeywordPeople] =
    useState(false);
  const [showIncludeAllKeywordPeople, setShowIncludeAllKeywordPeople] =
    useState(false);
  const [showExcludeKeywordsPeople, setShowExcludeKeywordsPeople] =
    useState(false);
  const [showIncludeKeywordCompany, setShowIncludeKeywordCompany] =
    useState(false);
  const [showIncludeAllKeywordCompany, setShowIncludeAllKeywordCompany] =
    useState(false);
  const [showExcludeKeywordsCompany, setShowExcludeKeywordsCompany] =
    useState(false);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [openModalSendEmailContact, setOpenSendEmailContact] = useState(false);
  const [sendEmailStatus, setSendEmailStatus] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [showModalAddTopic, setShowModalAddTopic] = useState(false);
  const [dataPersonTitle, setDataPersonTitle] = useState([]);
  const [dataLocation, setDataLocation] = useState([]);
  let searchTimer;

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  useEffect(() => {
    setValue(
      'consultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'consultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
    setValue(
      'consultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
  }, [userToSet]);

  const handleGetMetaMode = async (body) => {
    const { data } = await getListEmployee({
      ...body,
      metaMode: 'metadata_mode',
    });

    return data;
  };

  const handleFindEmails = async ({ page = 1 }) => {
    try {
      const currentType = watch('keyAcordionPeople') || 0;
      setIsLoadingEmployee(true);
      let resultData = [];
      let bodyToSearch = null;
      if (currentType !== '6') {
        const locations = getValues().locationFinder
          ? getValues().locationFinder.map((item) => item.label)
          : [];
        const personTitles = getValues().titleFinder
          ? getValues().titleFinder.map((item) => item.label)
          : [];
        const employeeRanges = getValues().employeeFinder
          ? getValues().employeeFinder.map((item) => item.label)
          : [];
        const industryTagIds = getValues().industryFinder
          ? getValues().industryFinder.map((item) => item.value)
          : [];
        const contactEmailStatus = getValues().contactEmailStatus
          ? getValues().contactEmailStatus
          : [];
        const contactEmailOpened = getValues().emailOpenedStatus ?? null;
        const contactEmailOpenedAtLeast =
          getValues().contactEmailOpenedTime ?? null;
        const contactEmailOpenedAtDateRangeMin =
          getValues().contactEmailOpenedTimeMin &&
          getValues().contactEmailOpenedTimeMin !== ''
            ? getValues().contactEmailOpenedTimeMin
            : null;
        const contactEmailOpenedAtDateRangeMax =
          getValues().contactEmailOpenedTimeMax &&
          getValues().contactEmailOpenedTimeMax !== ''
            ? getValues().contactEmailOpenedTimeMax
            : null;
        const intentStrengths = getValues().contactBuyingIntentScore
          ? getValues().contactBuyingIntentScore
          : [];
        const intentIds = getValues().contactBuyingIntentIds
          ? getValues().contactBuyingIntentIds
          : [];
        const searchSignalIds = getValues().searchSignalIds
          ? getValues().searchSignalIds
          : [];
        const recommendationScoresMinTranche = getValues().contactMinimumScore
          ? getValues().contactMinimumScore
          : null;
        const containOneKeywords = watch('includeKeywordPeople')
          ? watch('includeKeywordPeople').map((item) => item.label)
          : [];
        const containAllKeyWords = watch('includeAllKeywordPeople')
          ? watch('includeAllKeywordPeople').map((item) => item.label)
          : [];
        const excludeKeyWords = watch('excludeKeywordsPeople')
          ? watch('excludeKeywordsPeople').map((item) => item.label)
          : [];
        const organizationLatestFundingStageCd =
          getValues('fundingStatusItem') ?? [];
        const currentlyUsingAnyOfTechnologyUids =
          getValues('listTechnologies') ?? [];
        const notExistFields =
          getValues('revenueStatus') === 'is_un_known'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const existFields =
          getValues('revenueStatus') === 'is_know'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const organizationJobLocations = getValues('contactJobLocated') ?? [];
        const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
        const personName = getValues('nameFinderText') ?? null;
        const organizationNumJobsRangeMin =
          getValues('organizationNumJobsRangeMin') ?? null;
        const organizationNumJobsRangeMax =
          getValues('organizationNumJobsRangeMax') ?? null;
        const organizationJobPostedAtRangeMin =
          getValues('organizationJobPostedAtRangeMin') ?? null;
        const organizationJobPostedAtRangeMax =
          getValues('organizationJobPostedAtRangeMax') ?? null;
        const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
        const totalFundingRangeMin = getValues('fundingMin') ?? null;
        const totalFundingRangeMax = getValues('fundingMax') ?? null;
        bodyToSearch = {
          organizationId: watch('companyFinderId'),
          locations,
          personTitles,
          employeeRanges,
          industryTagIds,
          page,
          searchText: watch('searchPeople'),
          containOneKeywords,
          containAllKeyWords,
          excludeKeyWords,
          contactEmailStatus,
          contactEmailOpened,
          contactEmailOpenedAtLeast,
          contactEmailOpenedAtDateRangeMin,
          contactEmailOpenedAtDateRangeMax,
          intentStrengths,
          intentIds,
          searchSignalIds,
          recommendationScoresMinTranche,
          currentlyUsingAnyOfTechnologyUids,
          existFields,
          notExistFields,
          organizationTradingStatus,
          organizationLatestFundingStageCd,
          totalFundingRangeMax,
          totalFundingRangeMin,
          personName,
          organizationJobLocations,
          qOrganizationJobTitles,
          organizationNumJobsRangeMin,
          organizationNumJobsRangeMax,
          organizationJobPostedAtRangeMin,
          organizationJobPostedAtRangeMax,
        };
        const { data } = await getListEmployee(bodyToSearch);
        resultData = data;
      } else if (currentType === '6') {
        const { data } = await getListEmployee({
          organizationId: watch('companyFinderId'),
          page,
        });
        resultData = data;
        const people = resultData?.result?.people || [];
        const contacts = resultData?.result?.contacts || [];
        setListEmployeeCompany([...people, ...contacts]);
        setListEmployeePaginationCompany(resultData?.result?.pagination);
        setIsLoadingEmployee(false);
        return;
      }
      if (resultData.length === 0) return dataEmployeeNotFound();
      const people = resultData?.result?.people || [];
      const contacts = resultData?.result?.contacts || [];
      const listData = [...people, ...contacts];
      setListEmployee(listData);
      setIsLoadingEmployee(false);
      const dataPagination = await handleGetMetaMode(bodyToSearch);
      // console.log("dataPagination", dataPagination?.result?.pagination)
      setListEmployeePagination(
        dataPagination?.result?.pagination || resultData?.result?.pagination
      );
      const listOrgIds = listData
        ?.map((item) => item?.organization_id)
        .filter((id) => id != null);

      const uniqueOrgIds = [...new Set(listOrgIds)];
      const dataOrg = await getOrganizationsSnippet({
        ids: uniqueOrgIds,
      });

      setListCurrentOrg(dataOrg.data.organizations);
    } catch (err) {
      setListEmployee([]);
      setListEmployeePagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingEmployee(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const handleFindCompany = async ({ page = 1 }) => {
    try {
      setIsDetailEmployeeCompany(false);
      setIsLoadingCompanies(true);
      const locations = getValues().locationFindCompany
        ? getValues().locationFindCompany.map((item) => item.label)
        : [];
      const employeeRanges = getValues().employeesFindCompany
        ? getValues().employeesFindCompany.map((item) => item.label)
        : [];
      const personTitles = getValues().titleFinder
        ? getValues().titleFinder.map((item) => item.label)
        : [];
      const industryTagIds = getValues().industryKeywordCompany
        ? getValues().industryKeywordCompany.map((item) => item.value)
        : [];
      const searchSignalIds = getValues().searchSignalIds
        ? getValues().searchSignalIds
        : [];
      const accountStageIds = getValues().accountStageIds
        ? getValues().accountStageIds
        : [];
      const notAccountStageIds = getValues().notAccountStageIds
        ? getValues().notAccountStageIds
        : [];
      const containOneKeywords = watch('includeKeywordCompany')
        ? watch('includeKeywordCompany').map((item) => item.label)
        : [];
      const containAllKeyWords = watch('includeAllKeywordCompany')
        ? watch('includeAllKeywordCompany').map((item) => item.label)
        : [];
      const excludeKeyWords = watch('excludeKeywordsCompany')
        ? watch('excludeKeywordsCompany').map((item) => item.label)
        : [];
      const currentlyUsingAnyOfTechnologyUids =
        getValues('listTechnologies') ?? [];
      const recommendationScoresMinTranche =
        getValues().contactMinimumScore ?? null;

      const intentStrengths = getValues().contactBuyingIntentScore
        ? getValues().contactBuyingIntentScore
        : [];
      const intentIds = getValues().contactBuyingIntentIds
        ? getValues().contactBuyingIntentIds
        : [];
      const notExistFields =
        getValues('revenueStatus') === 'is_un_known'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const existFields =
        getValues('revenueStatus') === 'is_know'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const organizationJobLocations = getValues('contactJobLocated') ?? [];
      const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
      const organizationNumJobsRangeMin =
        getValues('organizationNumJobsRangeMin') ?? null;
      const organizationNumJobsRangeMax =
        getValues('organizationNumJobsRangeMax') ?? null;
      const organizationLatestFundingStageCd =
        getValues('fundingStatusItem') ?? [];
      const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
      const totalFundingRangeMin = getValues('fundingMin') ?? null;
      const totalFundingRangeMax = getValues('fundingMax') ?? null;
      const organizationJobPostedAtRangeMin =
        getValues('organizationJobPostedAtRangeMin') ?? null;
      const organizationJobPostedAtRangeMax =
        getValues('organizationJobPostedAtRangeMax') ?? null;
      const { data } = await getListCompanies({
        organizationId: watch('companyFindCompanyId'),
        locations,
        personTitles,
        employeeRanges,
        industryTagIds,
        page,
        searchText: watch('searchCompany'),
        containOneKeywords,
        containAllKeyWords,
        excludeKeyWords,
        searchSignalIds,
        accountStageIds,
        notAccountStageIds,
        recommendationScoresMinTranche,
        currentlyUsingAnyOfTechnologyUids,
        intentStrengths,
        intentIds,
        existFields,
        notExistFields,
        organizationTradingStatus,
        organizationLatestFundingStageCd,
        totalFundingRangeMax,
        totalFundingRangeMin,
        organizationJobLocations,
        qOrganizationJobTitles,
        organizationNumJobsRangeMin,
        organizationNumJobsRangeMax,
        organizationJobPostedAtRangeMin,
        organizationJobPostedAtRangeMax,
      });
      if (data?.length === 0) return dataCompanyNotFound();
      if (data?.organizations?.length === 0) return dataCompanyNotFound();
      setListCompanies(data?.organizations);
      setListCompaniesPagination(data?.pagination);
      setIsLoadingCompanies(false);
    } catch (err) {
      setListCompanies([]);
      setListCompaniesPagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingCompanies(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const dataEmployeeNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingEmployee(false);
  };

  const dataCompanyNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingCompanies(false);
  };

  const handleSubmitPeople = async ({ page = 1 }) => {
    if (watch('searchPeople') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindEmails({ page });
  };

  const handleSearchPeople = async () => {
    functionContactClient.setIsLoadingContacts(true);
    if (watch('searchExistsPeople') === '') {
      functionContactClient.handleContactSearch(' ');
    } else {
      functionContactClient.handleContactSearch(watch('searchExistsPeople'));
    }
    functionContactClient.setIsLoadingContacts(false);
  };

  const handleSubmitCompanyFind = async ({ page = 1 }) => {
    if (watch('searchCompany') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindCompany({ page });
  };

  const resetFormFindEmails = () => {
    setIsLoading(false);
  };

  const handlePaginationListEmployee = (page) => {
    handleFindEmails({ page: page });
  };

  const handlePaginationListCompany = (page) => {
    handleFindCompany({ page: page });
  };

  const handleAddContact = async (fullName, dataContact) => {
    setIsLoading(true);
    await handleSetDataContact(dataContact);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      dataContact?.email
    );
    if (data?.result.length === 0) {
      setIsLoading(false);
      setIsAddContactForm(true);
    } else {
      const isDataExist = listAddContactSelected?.some(
        (item) => item?.id === data?.result[0].id
      );
      if (!isDataExist) {
        setListAddContactSelected([
          ...listAddContactSelected,
          { ...data?.result[0] },
        ]);
      }
      functionContactClient?.handleContactSearch(data?.result[0].name, '');
      setIsLoading(false);
      setFlagEditContact(false);
      setIsAddContactForm(false);
      notification.success({
        message: `This contact already existed on Bullhorn`,
      });
    }
  };

  const handleEditContact = async (fullName, email) => {
    setIsLoading(true);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      email
    );
    if (data?.result.length === 0) {
      notification.success({
        message: `This contact not existed on Bullhorn`,
      });
    } else {
      handleSetDataEditContact(data.result[0]);
      // setIsLoading(false);
      // setIsAddContactForm(true);
    }
  };

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleSetDataEditContact = async (data) => {
    setDetailDataContact(data);
    setFlagEditContact(true);
  };

  const handleGetRevenue = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataRevenue(data?.faceting?.organization_trading_status_facets);
  };

  const handleGetFunding = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataFunding(data?.faceting?.latest_funding_stage_facets);
  };

  const handleSubmitAddContact = async () => {
    setIsSubmitForm(true);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      categories,
    } = getValues()?.clientContact;
    console.log('handlle add contact', surename);
    if (!companyId) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      setIsSubmitForm(false);
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Address is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (checkHenley ? skills.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (categories.length === 0) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    }

    if (
      address?.length > 100 ||
      getValues()?.clientContact?.address2?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      // lastName: getValues()?.clientContact?.lastname,
      lastName: getValues()?.clientContact?.surename,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        address2: getValues()?.clientContact?.address2,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      categories: {
        replaceAll: categories?.map((obj) => obj.value),
      },
      skills: {
        replaceAll: skills?.map((obj) => obj.value),
      },
      customText1: getValues()?.clientContact.linkedProfileUrl,
    };

    if (
      getValues()?.clientContact?.address > 100 ||
      getValues()?.clientContact?.address2 > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newContactPayloadCleaned = cleanPayload(payload);
    let data;
    try {
      data = flagEditContact
        ? await upladteBullhorn(
            getValues()?.clientContact?.id,
            newContactPayloadCleaned
          )
        : await insertBullhorn(newContactPayloadCleaned);
    } catch (err) {
      notification.error(err);
      setIsSubmitForm(false);
    }
    functionContactClient.handleContactSearch('', getValues().companyId);
    const isDataExist = listAddContactSelected.some(
      (item) => item?.id === data?.result?.changedEntityId
    );
    if (!isDataExist) {
      setListAddContactSelected([
        ...listAddContactSelected,
        { ...data?.result?.data, id: data?.result?.changedEntityId },
      ]);
    }
    setIsSubmitForm(false);
    setIsLoading(false);
    setIsAddContactForm(false);
    notification.success({
      message: `Success ${flagEditContact ? 'edit' : 'add'} contact ${data?.result?.data?.name}`,
    });
    setFlagEditContact(false);
  };

  const updateArrayByKey = (key, checked, optionKey) => {
    const newValues = checked
      ? [...(getValues(key) ?? []), optionKey]
      : (getValues(key) ?? [])?.filter((value) => value !== optionKey);

    setValue(key, newValues);
  };

  const handleDeleteIntent = async (value) => {
    const index = listDataIntentSetting.find((obj) => obj.id === value);
    if (index) {
      const newArr = listDataIntentSetting.filter((obj) => obj.id !== value);
      setListDataIntentSetting(newArr);
    }
  };

  const handleGetLocation = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'location',
    });
    setDataLocation(data?.tags);
  };

  const handleGetDetailEmployee = async (id) => {
    try {
      const { data } = await getLDetailEmployee({ employeeId: id });
      if (data) {
        setListDetailEmployee([...listDetailEmployee, { ...data }]);
        setIsLoading(true);
        setListEmployee([...listEmployee]);
        setListEmployeePagination({ ...listEmployeePagination });
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error in getting employee detail', error);
      if (error?.response?.status == 402) {
        notification.error({
          message: 'Your account is running out of credits. Please contact to your admin for support!',
        });
      } else {
        notification.error({
          message: 'Something went wrong. Please try again in 5 minutes',
        });
      }
    }
  };

  const handleAccordionChangePeople = (keys) => {
    setValue('keyAcordionPeople', keys[0]);
    if (keys[0] === '10') {
      refetchListSignals();
    }
  };

  const checkboxGroups = {
    'Safe to send': [
      { key: 'likely_to_engage', label: 'Likely to engage' },
      { key: 'verified', label: 'Verified' },
    ],
    'Send with caution': [{ key: 'unverified', label: 'Unverified' }],
    'Do not send': [
      { key: 'update_required', label: 'Update required' },
      { key: 'unavailable', label: 'Unavailable' },
    ],
  };

  const handleGetIntentCategory = async () => {
    const data = await employeeFinderIntentCategory();
    setDataListTopicTitle(data.data.categories);
  };
  useEffect(() => {
    if (!onClose) {
      handleGetIntentCategory();
    }
  }, [showModalAddTopic]);

  const handleGetTechno = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'technology',
    });
    setDataTechnologies(data?.tags);
  };

  const resetAllPeople = () => {
    setValue('companyFinder', '');
    setValue('companyFinderId', '');
    setValue('locationFinderText', ' ');
    setValue('locationFinder', []);
    setValue('titleFinderText', ' ');
    setValue('titleFinder', []);
    setValue('employeeFinderText', '');
    setValue('employeeFinder', []);
    setValue('industryFinderText', ' ');
    setValue('industryFinder', []);
    setShowIncludeKeywordPeople(false);
    setShowIncludeAllKeywordPeople(false);
    setShowExcludeKeywordsPeople(false);
    setValue('includeKeywordPeople', []);
    setValue('includeKeywordPeopleText', '');
    setValue('includeAllKeywordPeople', []);
    setValue('includeAllKeywordPeopleText', '');
    setValue('excludeKeywordsPeople', []);
    setValue('excludeKeywordsPeopleText', '');
  };

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);

    switch (keys[0]) {
      case '6':
        refetchListSignals();
        break;
      case '7':
        refetchListFacets();
        break;
      default:
        break;
    }
  };

  const resetAllCompany = () => {
    setValue('companyFindCompany', '');
    setValue('companyFindCompanyId', '');
    setValue('locationFindCompanyText', ' ');
    setValue('locationFindCompany', []);
    setValue('employeesFindCompanyText', ' ');
    setValue('employeesFindCompany', []);
    setValue('industryKeywordCompanyText', ' ');
    setValue('industryKeywordCompany', []);
    setShowIncludeKeywordCompany(false);
    setShowIncludeAllKeywordCompany(false);
    setShowExcludeKeywordsCompany(false);
    setValue('includeKeywordCompany', []);
    setValue('includeKeywordCompanyText', '');
    setValue('includeAllKeywordCompany', []);
    setValue('includeAllKeywordCompanyText', '');
    setValue('excludeKeywordsCompany', []);
    setValue('excludeKeywordsCompanyText', '');
  };

  const { data: listSignals, refetch: refetchListSignals } = useQuery(
    ['GET_LIST_SIGNALS'],
    async () => {
      console.log('employeeFinderSearchSignals', 333);
      const { data } = await employeeFinderSearchSignals();
      return data?.templates || [];
    },
    { enabled: false }
  );

  const EmailOpenedStatus = [
    { key: 0, value: 'yes', label: 'Yes' },
    { key: 0, value: 'no', label: 'No' },
  ];

  const OptionBuyIntent = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'mid' },
    { label: 'Low', value: 'low' },
    { label: 'None', value: 'none' },
  ];

  const handleChangeSettingIntent = async () => {
    setDataParentIntent(listDataIntentSetting);
    setShowModalAddTopic(false);
    localStorage.setItem(
      'listIntentChoose',
      JSON.stringify(listDataIntentSetting)
    );
  };

  useEffect(() => {
    const storedData = localStorage?.getItem('listIntentChoose');
    setDataParentIntent(storedData ? JSON.parse(storedData) : []);
    setListDataIntentSetting(storedData ? JSON.parse(storedData) : []);
  }, []);

  const formatNumber = (number) => {
    const suffixes = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    let suffixIndex = 0;
    while (number >= 1000) {
      number /= 1000;
      suffixIndex++;
    }
    return number.toFixed(1) + suffixes[suffixIndex];
  };

  const handleGetPersonalTitles = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'person_title',
    });
    setDataPersonTitle(data?.tags);
  };

  const { data: listFacets, refetch: refetchListFacets } = useQuery(
    ['GET_LIST_FACETS'],
    async () => {
      const { data } = await getFacets();
      return data?.faceting?.account_stage_facets || [];
    },
    { enabled: false }
  );

  const filterAddContact = [
    {
      key: '1',
      label: (
        <div className="inline-grid">
          <span>Company</span>
          {watch('companyFinderId') !== '' && watch('companyFinderId') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('companyFinder')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Company Name" name="companyFinder">
          <Controller
            render={({ field }) => (
              <AutoComplete
                style={{ width: '250px' }}
                {...field}
                options={companyListPeople.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid p-2">
                      <div className="flex justify-between">
                        <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                          {option.name}
                          <br />
                          <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                            {option.domain || '-'}
                          </span>
                        </span>
                        <img
                          className="absolute right-3"
                          src={option?.logo_url ? `${option?.logo_url}` : ''}
                          width={50}
                          height={50}
                          alt="Logo"
                        />
                      </div>
                    </div>
                  ),
                }))}
                onSearch={(value) => {
                  setValue('companyFinder', value);
                  setValue('companyFinderSelect', null);
                  setValue('companyFinderId', null);
                }}
                onSelect={async (selectedCompanyId) => {
                  const selectedCompany = companyListPeople.find(
                    (ao) => ao.id == selectedCompanyId
                  );
                  setValue('companyFinder', selectedCompany.name);
                  setValue('companyFinderSelect', selectedCompanyId);
                  setValue('companyFinderId', selectedCompanyId);
                }}
              >
                <Input />
              </AutoComplete>
            )}
            name="companyFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '2',
      label: (
        <div className="inline-grid">
          <span>Location</span>
          {watch('locationFinder') && watch('locationFinder')?.length > 0 && (
            <Row>
              {watch('locationFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Location Name" name="locationFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('locationFinderText') === '' ||
                  !watch('locationFinderText')
                    ? locationListPeople?.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('locationFinderText'),
                          value: watch('locationFinderText'),
                        },
                        ...locationListPeople?.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      label: (
        <div className="inline-grid">
          <span>Title</span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('titleFinderText'),
                          value: watch('titleFinderText'),
                        },
                        ...titleList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      label: (
        <div className="inline-grid">
          <span>Employees</span>
          {watch('employeeFinder') && watch('employeeFinder')?.length > 0 && (
            <Row>
              {watch('employeeFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Predefined Range" name="employeeFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (dataEmployeeList.length == 0) {
                    setValue('employeeFinderText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={dataEmployeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeeFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      label: (
        <div className="inline-grid">
          <span>Industry & Keywords</span>
          {watch('industryFinder') && watch('industryFinder')?.length > 0 && (
            <Row>
              <span className="text-xs my-auto mr-2">Industry: </span>
              {watch('industryFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryFinder">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryFinderText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={dataIndustryList.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryFinder"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordPeople}
            onChange={(e) => {
              setShowIncludeKeywordPeople(e.target.checked);
              setValue('includeKeywordPeople', []);
              setValue('includeKeywordPeopleText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordPeople && (
            <Form.Item name="includeKeywordPeople">
              <Controller
                name="includeKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordPeopleText'),
                        label: watch('includeKeywordPeopleText'),
                        value: watch('includeKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordPeople}
            onChange={(e) => {
              setShowIncludeAllKeywordPeople(e.target.checked);
              setValue('includeAllKeywordPeople', []);
              setValue('includeAllKeywordPeopleText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordPeople && (
            <Form.Item name="includeAllKeywordPeople">
              <Controller
                name="includeAllKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordPeopleText'),
                        label: watch('includeAllKeywordPeopleText'),
                        value: watch('includeAllKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsPeople}
            onChange={(e) => {
              setShowExcludeKeywordsPeople(e.target.checked);
              setValue('excludeKeywordsPeople', []);
              setValue('excludeKeywordsPeopleText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsPeople && (
            <Form.Item name="excludeKeywordsPeople">
              <Controller
                name="excludeKeywordsPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsPeopleText'),
                        label: watch('excludeKeywordsPeopleText'),
                        value: watch('excludeKeywordsPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '7',
      label: (
        <div className="inline-grid">
          <span>Email Status</span>
          {watch('contactEmailStatus') &&
            watch('contactEmailStatus')?.length > 0 && (
              <Row>
                {watch('contactEmailStatus').map((emailStatusKey, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {
                      Object.entries(checkboxGroups)
                        .find(([_, options]) =>
                          options.find(
                            (option) => option.key === emailStatusKey
                          )
                        )
                        .at(1)
                        .find((option) => option.key === emailStatusKey).label
                    }
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <>
          <Form.Item name="contactEmailStatus">
            <Controller
              name="contactEmailStatus"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {Object.entries(checkboxGroups).map(([group, options]) => (
                    <div key={group}>
                      <Typography.Title level={5}>{group}</Typography.Title>
                      <Flex vertical gap={4}>
                        {options.map((option, index) => (
                          <Checkbox
                            key={`email-satus-${option.key}-${index}`}
                            checked={field?.value?.includes(option.key)}
                            onChange={(e) =>
                              updateArrayByKey(
                                'contactEmailStatus',
                                e.target.checked,
                                option.key
                              )
                            }
                          >
                            {option.label}
                          </Checkbox>
                        ))}
                      </Flex>
                    </div>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '8',
      label: (
        <div className="inline-grid">
          <span>Email Opened</span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTimeMin') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time min:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTimeMin')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTimeMax') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time min:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTimeMax')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="contactEmailOpened" label="Opened Status">
            <Controller
              name="contactEmailOpened"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={EmailOpenedStatus.map((option) => ({
                    value: option.value,
                    label: <>{option.label}</>,
                  }))}
                  onSelect={async (value) => {
                    setValue('contactEmailOpened', value);
                    setValue('emailOpenedStatus', value);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
            />
          </Form.Item>

          <Form.Item
            name="contactEmailOpenedTime"
            label="Opened at least __ times"
          >
            <Controller
              name="contactEmailOpenedTime"
              control={control}
              render={({ field }) => (
                <InputNumber
                  min={1}
                  max={100000}
                  style={{ width: '100%' }}
                  onChange={(e) => {
                    setValue('contactEmailOpenedTime', e);
                  }}
                />
              )}
            />
          </Form.Item>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="contactEmailOpenedTimeMin" label="Time min">
                <Controller
                  name="contactEmailOpenedTimeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      onChange={(date, dateString) => {
                        setValue('contactEmailOpenedTimeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="contactEmailOpenedTimeMax" label="Time Max">
                <Controller
                  name="contactEmailOpenedTimeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      onChange={(date, dateString) => {
                        setValue('contactEmailOpenedTimeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: '9',
      label: (
        <div className="inline-grid">
          <span>Buying Intent</span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '10',
      label: (
        <>
          <Flex>
            <span>Signals</span>
            {watch('searchSignalIds') &&
              watch('searchSignalIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {watch('searchSignalIds')?.length}
                </Tag>
              )}
          </Flex>
        </>
      ),
      children: (
        <>
          <Form.Item name="searchSignalIds">
            <Controller
              name="searchSignalIds"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {listSignals?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value?.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      {option.modality === 'people' ? (
                        <Tag
                          className="flex items-center justify-center"
                          color="processing"
                          icon={<FaRegUser />}
                        />
                      ) : (
                        <Tag
                          className="flex items-center justify-center"
                          color="success"
                          icon={<FaRegBuilding />}
                        />
                      )}
                    </Flex>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '11',
      label: (
        <div className="inline-grid">
          <span>Score</span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '12',
      label: (
        <div className="inline-grid">
          <span>Technologies</span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '13',
      label: (
        <div className="inline-grid">
          <span>Revenue</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '14',
      label: (
        <div className="inline-grid">
          <span>Funding</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '15',
      label: (
        <div className="inline-grid">
          <span>Name</span>
          {watch('nameFinderText') !== '' && watch('nameFinderText') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('nameFinderText')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Name" name="nameFinder">
          <Controller
            render={({ field }) => (
              <Input
                placeholder="name"
                onChange={(e) => setValue('nameFinderText', e.target.value)}
              />
            )}
            name="nameFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '16',
      label: (
        <div className="inline-grid">
          <span>Job Postings</span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const itemsDropdown = [
    {
      key: '1',
      label: <div>Edit</div>,
    },
    {
      key: '2',
      label: <div>Delete</div>,
    },
  ];

  const handleGenderSupportBar = (record, name = null) => {
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                      }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                        setValue('email', record?.email);
                        setValue('sendMail.mailStepParentMailTo', [
                          { email: record?.email, name: record?.name },
                        ]);
                        setValue('optionContactSelect', record);
                        setOpenSendEmailContact(true);
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <div>
                      {record?.email}{' '}
                      <CopyOutlined
                        style={{ marginLeft: '10px' }}
                        onClick={() => {
                          navigator.clipboard.writeText(record?.email),
                            notification.success({
                              message: 'Copy To Clipboard success',
                            });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => setOpenExistingUserGroup(true)}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const filterCompany = [
    {
      key: '1',
      label: (
        <div className="inline-grid">
          <span>Company</span>
          {watch('companyFindCompanyId') !== '' &&
            watch('companyFindCompanyId') && (
              <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                {watch('companyFindCompany')}
              </span>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Company Name" name="companyFindCompany">
          <Controller
            render={({ field }) => (
              <AutoComplete
                style={{ width: '250px' }}
                {...field}
                options={companyList.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid p-2">
                      <div className="flex justify-between">
                        <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                          {option.name}
                          <br />
                          <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                            {option.domain || '-'}
                          </span>
                        </span>
                        <img
                          className="absolute right-3"
                          src={option?.logo_url ? `${option?.logo_url}` : ''}
                          width={50}
                          height={50}
                          alt="Logo"
                        />
                      </div>
                    </div>
                  ),
                }))}
                onSearch={(value) => {
                  setValue('companyFindCompany', value);
                  setValue('companyFindCompanySelect', null);
                  setValue('companyFindCompanyId', null);
                }}
                onSelect={async (selectedCompanyId) => {
                  const selectedCompany = companyList.find(
                    (ao) => ao.id == selectedCompanyId
                  );
                  setValue('companyFindCompany', selectedCompany.name);
                  setValue('companyFindCompanySelect', selectedCompanyId);
                  setValue('companyFindCompanyId', selectedCompanyId);
                }}
              >
                <Input />
              </AutoComplete>
            )}
            name="companyFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '2',
      label: (
        <div className="inline-grid">
          <span>Account Location</span>
          {watch('locationFindCompany') &&
            watch('locationFindCompany')?.length > 0 && (
              <Row>
                {watch('locationFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Account Location" name="locationFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFindCompanyText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={locationListCompany.map((so) => ({
                  ...so,
                  label: so.cleaned_name,
                  value: so.id,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      label: (
        <div className="inline-grid">
          <span>Title</span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('titleFinderText'),
                          value: watch('titleFinderText'),
                        },
                        ...titleList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      label: (
        <div className="inline-grid">
          <span>Employees</span>
          {watch('employeesFindCompany') &&
            watch('employeesFindCompany')?.length > 0 && (
              <Row>
                {watch('employeesFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Employees" name="employeesFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (dataEmployeeList.length == 0) {
                    setValue('employeesFindCompanyText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={dataEmployeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeesFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      label: (
        <div className="inline-grid">
          <span>Industry & Keywords</span>
          {watch('industryKeywordCompany') &&
            watch('industryKeywordCompany')?.length > 0 && (
              <Row>
                <span className="text-xs my-auto mr-2">Industry: </span>
                {watch('industryKeywordCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryKeywordCompany">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryKeywordCompanyText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={industryListCompany.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryKeywordCompany"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordCompany}
            onChange={(e) => {
              setShowIncludeKeywordCompany(e.target.checked);
              setValue('includeKeywordCompany', []);
              setValue('includeKeywordCompanyText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordCompany && (
            <Form.Item name="includeKeywordCompany">
              <Controller
                name="includeKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordCompanyText'),
                        label: watch('includeKeywordCompanyText'),
                        value: watch('includeKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordCompany}
            onChange={(e) => {
              setShowIncludeAllKeywordCompany(e.target.checked);
              setValue('includeAllKeywordCompany', []);
              setValue('includeAllKeywordCompanyText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordCompany && (
            <Form.Item name="includeAllKeywordCompany">
              <Controller
                name="includeAllKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordCompanyText'),
                        label: watch('includeAllKeywordCompanyText'),
                        value: watch('includeAllKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsCompany}
            onChange={(e) => {
              setShowExcludeKeywordsCompany(e.target.checked);
              setValue('excludeKeywordsCompany', []);
              setValue('excludeKeywordsCompanyText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsCompany && (
            <Form.Item name="excludeKeywordsCompany">
              <Controller
                name="excludeKeywordsCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsCompanyText'),
                        label: watch('excludeKeywordsCompanyText'),
                        value: watch('excludeKeywordsCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '6',
      label: (
        <Flex>
          <span>Signals</span>
          {watch('searchSignalIds') && watch('searchSignalIds')?.length > 0 && (
            <Tag className="ml-auto" color="volcano">
              {watch('searchSignalIds')?.length}
            </Tag>
          )}
        </Flex>
      ),
      children: (
        <Form.Item name="searchSignalIds">
          <Controller
            name="searchSignalIds"
            control={control}
            render={({ field }) => (
              <Flex vertical gap={8}>
                {listSignals
                  ?.filter((item) => item.modality === 'companies')
                  ?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value?.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      <Tag
                        className="flex items-center justify-center"
                        color="success"
                        icon={<FaRegBuilding />}
                      />
                    </Flex>
                  ))}
              </Flex>
            )}
          />
        </Form.Item>
      ),
    },
    {
      key: '7',
      label: (
        <Flex>
          <span>Stage</span>
          {(watch('accountStageIds') && watch('accountStageIds')?.length > 0) ||
            (watch('notAccountStageIds') &&
              watch('notAccountStageIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {(watch('accountStageIds')?.length || 0) +
                    (watch('notAccountStageIds')?.length || 0)}
                </Tag>
              ))}
        </Flex>
      ),
      children: (
        <Form.Item name="accountStageIds">
          <Controller
            name="accountStageIds"
            control={control}
            render={({ field }) => (
              <Flex vertical>
                <Typography.Title level={5}>
                  Include account stages
                </Typography.Title>
                {listFacets?.map((option, index, arr) => (
                  <Checkbox
                    key={`signal-${option.value}`}
                    checked={field?.value.includes(option.value)}
                    className={`pt-2 ${index !== arr.length - 1 ? 'border-b pb-2' : undefined}`}
                    onChange={(e) =>
                      updateArrayByKey(
                        'accountStageIds',
                        e.target.checked,
                        option.value
                      )
                    }
                  >
                    {option.display_name}
                  </Checkbox>
                ))}
                <Typography.Title className="mt-4" level={5}>
                  Exclude account stages
                </Typography.Title>
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%' }}
                  placeholder="Please select"
                  options={listFacets?.map((item) => ({
                    label: item.display_name,
                    value: item.value,
                  }))}
                  onChange={(values) => {
                    console.log(`selected ${values}`);
                    setValue('notAccountStageIds', values);
                  }}
                />
              </Flex>
            )}
          />
        </Form.Item>
      ),
    },
    {
      key: '8',
      label: (
        <div className="inline-grid">
          <span>Score</span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '9',
      label: (
        <div className="inline-grid">
          <span>Technologies</span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '10',
      label: (
        <div className="inline-grid">
          <span>Buying Intent</span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '11',
      label: (
        <div className="inline-grid">
          <span>Revenue</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '12',
      label: (
        <div className="inline-grid">
          <span>Funding</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '13',
      label: (
        <div className="inline-grid">
          <span>Job Postings</span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  const columnsCompany = [
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '200px',
      align: 'left',
      // width: '25%',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              className=""
              src={record?.logo_url ? `${record?.logo_url}` : ''}
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  setIsDetailEmployeeCompany(true);
                  const { data } = await getListEmployee({
                    organizationId: record.id,
                    page: 1,
                  });
                  if (data?.result.people.length === 0)
                    return notification.error({ message: 'Data Not Found' });
                  setValue('keyAcordionPeople', '6');
                  setValue('companyFinderId', record.id);
                  setListEmployeeCompany(data?.result?.people);
                  setListEmployeePaginationCompany(data?.result?.pagination);
                  setIsLoadingEmployee(false);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'estimated_num_employees',
      key: 'estimated_num_employees',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => (
        <Button
          type="text"
          className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
          onClick={async (e) => {
            e.stopPropagation();
            handleDetailCompany(record?.organization);
          }}
        >
          <UserGroupIcon className="w-4 h-4" />
          {record?.estimated_num_employees}
          <span>Employees</span>
        </Button>
        // <Row gutter={16} className="w-[5rem]">
        //   <p>{record?.estimated_num_employees}</p>
        // </Row>
      ),
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industries',
      key: 'industries',
      width: '25%',
      render: (allowRead, record) => {
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={record?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {record?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < record.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      width: '25%',
      render: (allowRead, record) => {
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={record?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.keywords.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {record?.keywords?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < record.keywords.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listSelectSection, setListSelectSection] = useState([]);

  const handleClickSignal = (id) => {
    if (!selectedRowKeys.includes(id)) {
      setSelectedRowKeys([...selectedRowKeys, id]);
      const dataCheck = listEmployee.find((item) => item.id == id);
      setEmployeesChecked([...employeesChecked, dataCheck]);
    } else {
      setSelectedRowKeys(selectedRowKeys.filter((item) => item !== id));
      setEmployeesChecked(employeesChecked.filter((item) => item.id !== id));
    }
  };

  const handleCheckAll = () => {
    if (!listSelectSection.includes(listEmployeePagination.page)) {
      setListSelectSection([
        ...listSelectSection,
        listEmployeePagination?.page,
      ]);
      const data = listEmployee.map((item) => item.id);
      setSelectedRowKeys([...new Set([...selectedRowKeys, ...data])]);
      setEmployeesChecked([...employeesChecked, ...listEmployee]);
    } else {
      const data = listEmployee.map((item) => item.id);
      const updatedArray = selectedRowKeys.filter((id) => !data.includes(id));
      const updatedData = employeesChecked.filter(
        (item) => !data.includes(item.id)
      );
      setEmployeesChecked(updatedData);
      setSelectedRowKeys(updatedArray);
      setListSelectSection(
        listSelectSection.filter(
          (item) => item !== listEmployeePagination?.page
        )
      );
    }
  };

  const columnsPeople = [
    {
      title: (
        <>
          <Checkbox
            disabled={listEmployee.length == 0}
            checked={listSelectSection.includes(listEmployeePagination.page)}
            onChange={() => handleCheckAll()}
            indeterminate={
              selectedRowKeys.length > 0 &&
              selectedRowKeys.length < listEmployee.length &&
              listEmployee
                .map((item) => item.id)
                .filter((element) => selectedRowKeys.includes(element)).length >
                0
            }
          ></Checkbox>
        </>
      ),
      dataIndex: 'checkbox',
      key: 'checkbox',
      fixed: 'left',
      width: '50px',
      render: (allowWrite, record) => (
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <>
            <Checkbox
              checked={selectedRowKeys.includes(record.id)}
              onChange={(e) => {
                e.stopPropagation();
                handleClickSignal(record?.id);
              }}
            ></Checkbox>
          </>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.FULL_NAME,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '150px',
      render: (allowWrite, record) => (
        <>
          <Row gutter={16}>
            <p className="font-semibold mr-2">{record?.name}</p>
            {record?.linkedin_url && (
              <Row gutter={16}>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.linkedin_url);
                    }}
                    title={`Access to linkedin account`}
                    className="cursor-pointer text-[#0288d1]"
                  />
                </Col>
              </Row>
            )}
          </Row>
        </>
      ),
    },
    {
      title: COMMON_STRINGS.JOB_TITLE,
      dataIndex: 'title',
      key: 'title',
      width: '400px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p>{record?.title}</p>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'organization.name',
      key: 'organization.name',
      width: '300px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              onClick={async (e) => {
                e.stopPropagation();
                handleDetailCompany(record?.organization);
              }}
              src={
                record.organization?.logo_url
                  ? `${record.organization?.logo_url}`
                  : ''
              }
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  handleDetailCompany(record?.organization);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.organization?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: '350px',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          {record?.email &&
          record?.email !== '<EMAIL>' ? (
            <>{handleGenderSupportBar(record)}</>
          ) : listDetailEmployee.some(
              (item) =>
                item.person_id === record.id ||
                item.person_id === record.person_id
            ) ? (
            listDetailEmployee
              .filter(
                (item) =>
                  item.person_id === record.id ||
                  item.person_id === record.person_id
              )
              .map((item, index) => (
                <>{handleGenderSupportBar(item, record?.name)}</>
              ))
          ) : (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleGetDetailEmployee(record.id || record.person_id);
              }}
              type="primary"
              icon={<MailOutlined />}
            >
              Access Email
            </Button>
          )}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone_numbers',
      key: 'phone_numbers',
      align: 'center',
      width: '150px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{ color: 'blue', textAlign: 'center' }}
        >
          <a href={`tel:${record?.phone_numbers?.[0]?.sanitized_number}`}>
            {record?.phone_numbers?.[0]?.sanitized_number}
          </a>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.CONTACT_LOCATION,
      dataIndex: 'contact-location',
      key: 'contact-location',
      align: 'left',
      width: '300px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{
            fontWeight: '600',
          }}
        >
          {record?.present_raw_address ||
            (record?.city ?? '') +
              (record?.city ? ', ' : '') +
              (record?.country ?? '')}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'employees',
      key: 'employees',
      width: '320px',
      align: 'center',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <Button
            type="text"
            className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
            onClick={async (e) => {
              e.stopPropagation();
              handleDetailCompany(record?.organization);
            }}
          >
            <UserGroupIcon className="w-4 h-4" />
            {items?.estimated_num_employees}
            <span>Employees</span>
          </Button>
        );
      },
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industry',
      key: 'industry',
      width: '350px',
      align: 'center',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={items?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {items?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < items.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      align: 'center',
      width: '300px',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <Tooltip
              placement="topLeft"
              title={items?.keywords?.map((item, index) => (
                <span key={index}>
                  {item}
                  {index < items.keywords.length - 1 && ', '}
                </span>
              ))}
            >
              <span
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                }}
              >
                {items?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.keywords.length - 1 && ', '}
                  </span>
                ))}
              </span>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleValidEmail = async () => {
    setContactActionLoading(true);
    const listEmailToSave = functionContactClient.contactOptions
      .filter((item) => {
        return listEmail?.includes(item?.id);
      })
      .map((item) => item?.email);

    const { data } = await validListEmail({
      emails: listEmailToSave,
    });

    setContactActionLoading(false);
    setListEmailChecked(data?.result);
  };

  const columnsExistingContacs = [
    // {
    //   title: '',
    //   dataIndex: 'index',
    //   key: 'index',
    //   render: (text, record) => (
    //     <>
    //       <Checkbox
    //         checked={listEmail.includes(record?.email)}
    //         onChange={() => changeListEmail(record?.email)}
    //       ></Checkbox>
    //     </>
    //   ),
    // },
    {
      title: COMMON_STRINGS.NAME,
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="font-semibold">{text}</span>,
    },
    {
      title: COMMON_STRINGS.EMAIL,
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (text, record) => (
        <ExistingContactItems
          record={record}
          listEmailChecked={listEmailChecked}
        />
      ),
    },
    {
      title: COMMON_STRINGS.OCCUPATION,
      dataIndex: 'occupation',
      key: 'occupation',
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone',
      width: '15%',
      key: 'phone',
    },
    {
      title: COMMON_STRINGS.STATUS,
      width: '10%',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: COMMON_STRINGS.ACTION,
      key: 'action',
      align: 'center',
      render: (text, record) => (
        <Space size="small">
          <Popconfirm
            title="Delete the contact"
            description="Are you sure to delete this contact?"
            onConfirm={async () => {
              const { id } = record;
              try {
                const res = await deleteBullhornContact({
                  id,
                  entity: 'ClientContact',
                });
                const newContactOptions = [
                  ...functionContactClient.contactOptions.filter(
                    (contact) => contact?.id !== id
                  ),
                ];
                functionContactClient.contactSetOptions([...newContactOptions]);
                notification.success({
                  description: 'Contact deleted!',
                });
              } catch (error) {
                console.log('error delete contact: ', error);
                notification.error({
                  description: 'Network error! Try again later!',
                });
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button className="text-red-400 border-red-300">
              <DeleteOutlined />
            </Button>
          </Popconfirm>
          <Button
            disabled={isLoading}
            onClick={async () => {
              handleEditContact(record.name, record.email).finally(() => {
                setIsAddContactForm(true);
                setFlagEditContact(true);
                setIsLoading(false);
              });
            }}
          >
            <EditOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setListEmail(newSelectedRowKeys);
  };

  const rowTableExistingMailsSelection = {
    selectedRowKeys: listEmail,
    onChange: onSelectChange,
  };

  const itemsTableEmailFinder = [
    {
      key: '1',
      label: 'Existing Contacts',
      children: (
        <>
          <Row>
            <Col flex="100%">
              <Row gutter={16}>
                <div className="pb-4">
                  {/* <Button onClick={handleAddAllEmail}>Select all</Button> */}
                  {/* <Button
                    style={{ marginLeft: '10px' }}
                    onClick={handleValidEmail}
                  >
                    Validate Email
                  </Button> */}
                  <Button
                    loading={isContactActionLoading}
                    disabled={listEmail?.length === 0}
                    onClick={handleValidEmail}
                    type="primary"
                    className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                  >
                    <p className="Montserrat">
                      {`Validate ${listEmail?.length} Email(s)`}
                    </p>
                    <CheckOutlined />
                  </Button>
                </div>
                <TableContainer
                  className="search-table-new-design-container"
                  component={Paper}
                  style={{ maxHeight: '600px', overflowY: 'auto' }}
                  onScroll={(e) => {
                    functionContactClient.handleContactScroll(
                      e,
                      'ClientContact',
                      getValues().companyId
                    );
                  }}
                >
                  <Table
                    rowKey={(record) => record.id}
                    rowSelection={rowTableExistingMailsSelection}
                    columns={columnsExistingContacs}
                    dataSource={
                      functionContactClient?.valueNotFoundContacts
                        ? [
                            {
                              name: functionContactClient?.valueNotFoundContacts,
                            },
                            ...(functionContactClient.contactOptions || []),
                          ]
                        : functionContactClient.contactOptions || []
                    }
                    locale={{
                      emptyText: (
                        <Empty
                          description="No contacts found"
                          className="w-full"
                        />
                      ),
                    }}
                    pagination={false}
                    footer={() =>
                      functionContactClient.isLoadingContacts ? (
                        <Spin className="w-full mx-auto" />
                      ) : (
                        ''
                      )
                    }
                  />
                </TableContainer>
              </Row>
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: '2',
      label: 'People',
      children: (
        <div>
          <div style={{ float: 'right' }}>
            {selectedRowKeys?.length > 0 && (
              <div>
                <Dropdown
                  className="mb-4 animated fadeInDownBig"
                  placement="bottom"
                  arrow
                  menu={{
                    items: [
                      {
                        key: 'delete-searchs',
                        label: (
                          <a
                            className="Montserrat flex gap-2 items-center py-2"
                            onClick={(e) => {
                              e.preventDefault();
                              setIsAddContactFormBulk(true);
                            }}
                          >
                            <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
                          </a>
                        ),
                      },
                      {
                        key: 'bulk-add-contact-lists',
                        label: (
                          <a
                            className="Montserrat flex gap-2 items-center py-2"
                            onClick={async (e) => {
                              e.preventDefault();
                              const { accessedContacts, lockedAccessContacts } =
                                employeesChecked.reduce(
                                  (acc, item) => {
                                    if (!selectedRowKeys.includes(item.id)) {
                                      return acc;
                                    }
                                    if (
                                      item.email &&
                                      item.email !==
                                        '<EMAIL>'
                                    ) {
                                      acc.accessedContacts.push(item);
                                    } else {
                                      acc.lockedAccessContacts.push(item);
                                    }

                                    return acc;
                                  },
                                  {
                                    accessedContacts: [],
                                    lockedAccessContacts: [],
                                  }
                                );
                              if (lockedAccessContacts.length > 0) {
                                setIsLoadingEmployee(true);
                                const requestAccessContacts = [];
                                for (let lockedAccessContact of lockedAccessContacts) {
                                  try {
                                    const { data } = await getLDetailEmployee({
                                      employeeId: lockedAccessContact.id,
                                    });
                                    accessedContacts.push(data);
                                    requestAccessContacts.push(data);
                                  } catch (error) {
                                    // Do nothing
                                  }
                                }
                                if (requestAccessContacts.length) {
                                  const requestAccessContactIds =
                                    requestAccessContacts.map(
                                      (item) => item.id
                                    );
                                  setListDetailEmployee([
                                    ...listDetailEmployee.filter(
                                      (item) =>
                                        !requestAccessContactIds.includes(
                                          item.id
                                        )
                                    ),
                                    ...requestAccessContacts,
                                  ]);
                                  setListEmployee([
                                    ...listEmployee.filter(
                                      (item) =>
                                        !requestAccessContactIds.includes(
                                          item.id
                                        )
                                    ),
                                    ...requestAccessContacts,
                                  ]);
                                }
                                setIsLoadingEmployee(false);
                              }

                              if (accessedContacts.length === 0) {
                                notification.error({
                                  message:
                                    'Please choose at least one contact who has accessed email!',
                                });

                                return;
                              }

                              setOpenExistingUserGroup(true);
                              setCurrentContact();
                              setBulkContacts(accessedContacts);
                            }}
                          >
                            <span>
                              {COMMON_STRINGS.BULK_ADD_TO_CONTACT_LIST}
                            </span>
                          </a>
                        ),
                      },
                    ],
                  }}
                >
                  <Space>
                    <Button
                      type="primary"
                      className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                    >
                      <p className="Montserrat">
                        {`${selectedRowKeys?.length} Selected`}
                      </p>
                      <DownOutlined />
                    </Button>
                  </Space>
                </Dropdown>
              </div>
            )}
          </div>
          <div style={{ clear: 'both' }}></div>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleSubmitPeople)}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchPeople"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            prefix={<SearchOutlined />}
                            {...field}
                            placeholder="Search People ..."
                          />
                        )}
                        name="searchPeople"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindEmails)}
                >
                  <Col className="w-full customize-collapse">
                    <Form.Item label="Filter">
                      <Collapse
                        accordion
                        items={filterAddContact}
                        onChange={handleAccordionChangePeople}
                      />
                    </Form.Item>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      <span>
                        Search
                      </span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            <Col
              flex="auto"
              className="w-2/3 search-table-new-design-container"
            >
              <div className="customTable">
                <Table
                  scroll={{
                    x: 3000,
                    y: 1000,
                  }}
                  rowKey={(record) => record.id}
                  loading={isLoadingEmployee}
                  pagination={false}
                  columns={columnsPeople}
                  bordered
                  dataSource={listEmployee}
                  onRow={(record, rowIndex) => {
                    return {
                      onClick: () => {
                        handleDetailContact(record);
                      },
                      style: { cursor: 'pointer' },
                    };
                  }}
                  rowClassName="custom-row"
                  className="custom-table"
                  // rowSelection={rowSelection}
                />
              </div>
              <Pagination
                className="mt-3"
                defaultCurrent={listEmployeePagination.page}
                total={listEmployeePagination.total_entries}
                showSizeChanger={false}
                onChange={handlePaginationListEmployee}
              />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: '3',
      label: 'Companies',
      children: (
        <>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleSubmitCompanyFind)}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchCompany"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            prefix={<SearchOutlined />}
                            {...field}
                            placeholder="Search Company ..."
                          />
                        )}
                        name="searchCompany"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindCompany)}
                >
                  <Col className="w-full">
                    <Form.Item label="Filter" className={'custom-collapse'}>
                      <Collapse
                        accordion
                        items={filterCompany}
                        onChange={handleAccordionChangeCompany}
                      />
                    </Form.Item>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      <span>
                        Search
                      </span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            {isDetailEmployeCompany ? (
              <Col
                flex="auto"
                className="w-2/3 search-table-new-design-container"
              >
                <div className="customTable">
                  <Table
                    scroll={{
                      y: '80vh',
                    }}
                    loading={isLoadingEmployee}
                    pagination={false}
                    columns={columnsPeople}
                    dataSource={listEmployeeCompany}
                  />
                </div>
                <Pagination
                  className="mt-3"
                  defaultCurrent={listEmployeePaginationCompany.page}
                  total={listEmployeePaginationCompany.total_entries}
                  showSizeChanger={false}
                  onChange={handlePaginationListEmployee}
                />
              </Col>
            ) : (
              <Col
                flex="auto"
                className="w-2/3 search-table-new-design-container"
              >
                <div className="customTable">
                  <Table
                    scroll={{
                      x: 1500,
                      y: '80vh',
                    }}
                    loading={isLoadingCompanies}
                    pagination={false}
                    columns={columnsCompany}
                    dataSource={listCompanies}
                    onRow={(record, rowIndex) => {
                      return {
                        onClick: () => {
                          handleDetailCompany(record);
                        },
                        style: { cursor: 'pointer' },
                      };
                    }}
                  />
                </div>
                <Pagination
                  className="mt-3"
                  defaultCurrent={listCompaniesPagination.page}
                  total={listCompaniesPagination.total_entries}
                  showSizeChanger={false}
                  onChange={handlePaginationListCompany}
                />
              </Col>
            )}
          </Row>
        </>
      ),
    },
  ];

  // START DETAIL CONTACT
  const [detailContactStaff, setDetailContactStaff] = useState({});
  const [detailOrganization, setDetailOrganization] = useState({});
  const [showFullText, setShowFullText] = useState(false);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const handleDetailContact = async (record) => {
    setShowDetailContact(true);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.organization?.id,
      });
      setDetailOrganization(data?.organization);
      setDetailContactStaff(record);
    }
  };
  // END DETAIL CONTACT

  // START DETAIL COMPANY
  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});
  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };
  // END DETAIL COMPANY

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinder'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinder')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFindCompany'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFindCompany')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFinderText(watch('locationFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFindCompanyText(watch('locationFindCompanyText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustry(watch('industryFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustryCompany(
        watch('industryKeywordCompanyText')
      );
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextTitle(watch('titleFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  const { data: companyListPeople = [] } = useQuery(
    ['GET_LIST_COMPANY_PEOPLE', debouncedSearchTextCompanyPeople],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompanyPeople,
      });
      return data?.organizations;
    },
    { enabled: !!debouncedSearchTextCompanyPeople }
  );

  const { data: companyList = [] } = useQuery(
    ['GET_LIST_COMPANY', debouncedSearchTextCompany],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompany,
      });
      return data.organizations;
    },
    { enabled: !!debouncedSearchTextCompany }
  );

  const { data: locationListPeople = [] } = useQuery(
    ['GET_LIST_LOCATION_PEOPLE', debouncedLocationFinderText],
    async () => {
      if (onClose) return;
      const { data } = await employeeFinderSearchTag({
        searchText:
          debouncedLocationFinderText.trim() === ''
            ? ''
            : debouncedLocationFinderText,
        type: 'location',
      });
      return data?.tags;
    },
    { enabled: !!debouncedLocationFinderText }
  );

  const { data: locationListCompany = [] } = useQuery(
    ['GET_LIST_LOCATION', debouncedLocationFindCompanyText],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedLocationFindCompanyText,
        type: 'location',
      });
      return data?.tags;
    },
    { enabled: !!debouncedLocationFindCompanyText }
  );

  const handleGetIndustry = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: debouncedSearchTextIndustry,
      type: 'technology',
    });
    setDataIndustryList(data?.tags);
  };

  const handleGetEmployeeList = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'employee',
    });
    setDataEmployeeList(data?.faceting.num_employees_facets);
  };

  const { data: industryListCompany = [] } = useQuery(
    ['GET_LIST_INDUSTRYCompany', debouncedSearchTextIndustryCompany],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextIndustryCompany,
        type: 'linkedin_industry',
      });
      return data?.tags;
    },
    { enabled: !!debouncedSearchTextIndustryCompany }
  );

  const { data: titleList = [] } = useQuery(
    ['GET_LIST_TITLE', debouncedSearchTextTitle],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextTitle,
        type: 'person_title',
      });
      return data?.tags;
    },
    { enabled: !!debouncedSearchTextTitle }
  );

  const timeoutRef = useRef(null);

  useEffect(() => {
    if (!onClose) {
      // setIndustrySearchText(' ');
      // setCategorySearchText(' ');
      // setSkillSearchText(' ');
      functionContactClient.handleContactSearch('', '');
      setHandleCloseContact(false);
      setIsSearchCompany(false);
    }
  }, []);

  useEffect(() => {
    if (startCreateContact) {
      handleGetTechno();
      handleGetRevenue({
        openFactorNames: ['organization_trading_status'],
      });
      handleGetFunding({
        openFactorNames: ['organization_latest_funding_stage_cd'],
      });
      handleGetPersonalTitles();
      handleGetLocation();
      handleGetIndustry();
      handleGetEmployeeList();
    }
  }, [startCreateContact]);

  const handleChangeTab = (e) => {
    if (e == 1) {
      functionContactClient.handleContactSearch('', getValues().companyId);
    }
  };

  return (
    <div>
      <Modal
        width={1600}
        // style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        // title="List Staff"
        className="top-10"
        rootClassName="contact-modal-container"
        open={isModalStaffOpen}
        onCancel={() => {
          setIsModalStaffOpen(false);
          setIsLoadingEmployee(false);
          setValue('searchCompany', '');
          setValue('searchPeople', '');
          resetAllPeople();
          resetAllCompany();
        }}
        footer={false}
      >
        <Tabs
          defaultActiveKey="1"
          onChange={(e) => handleChangeTab(e)}
          items={itemsTableEmailFinder}
        />
        <Button
          className="flex ml-auto mt-3"
          htmlType="button"
          onClick={() => {
            setIsModalStaffOpen(false);
            setIsLoadingEmployee(false);
            setValue('searchCompany', '');
            setValue('searchPeople', '');
            resetAllPeople();
            resetAllCompany();
          }}
          type="primary"
        >
          Done
        </Button>
      </Modal>
      <CompanyDetailModal
        showDetailCompany={showDetailCompany}
        setShowDetailCompany={setShowDetailCompany}
        detailCompany={detailCompany}
        detailOrganization={detailOrganization}
      />
      <Modal
        width={1600}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title="Detail Company"
        open={showDetailCompany}
        onCancel={() => {
          setShowDetailCompany(false);
        }}
        footer={false}
      >
        <div className="grid gap-4">
          <Card>
            <Row className="flex justify-center">
              <Col>
                <img
                  className="mr-2 rounded w-8"
                  src={detailCompany?.logo_url}
                  alt={detailCompany?.name}
                />
              </Col>
              <span className="text-lg my-auto">
                {detailCompany.name}{' '}
                <span className="px-2 py-1 bg-gray-200 rounded-md text-sm ml-1">
                  N/A
                </span>
              </span>
            </Row>
            <Row className="flex gap-2 mt-1 justify-center">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.website_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-gray-700 text-lg"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.linkedin_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#0288d1] text-lg"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.facebook_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#3f51b5] text-lg"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.twitter_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#03a9f4] text-lg"
                />
              </Col>
              <div class="border-r border-solid"></div>
              <Col>
                {/* text={this.state.value} */}
                <CopyToClipboard
                  text={detailCompany?.phone}
                  onCopy={() => {
                    notification.success({ message: 'Success Copy Number' });
                  }}
                >
                  <Button icon={<PhoneOutlined />}>
                    {detailCompany?.phone}
                  </Button>
                </CopyToClipboard>
              </Col>
            </Row>
          </Card>
          <Card>
            <span className="font-semibold">
              {showFullText
                ? detailOrganization?.short_description
                : detailOrganization?.short_description?.slice(0, 200)}
            </span>
            {detailOrganization?.short_description ? (
              detailOrganization?.short_description.length > 200 ? (
                <button
                  className="text-blue-500 text-sm font-normal ml-1"
                  onClick={toggleText}
                >
                  {showFullText ? 'Show Less' : '... Show More'}
                </button>
              ) : (
                ''
              )
            ) : (
              ''
            )}
            <p className="mt-6 font-semibold text-base">
              Company Keyword <br />
              <Row className="mt-2">
                {detailOrganization?.keywords
                  ? detailOrganization?.keywords.map((item, i) => (
                      <Col className="mt-1 my-2">
                        <span
                          className="px-3 py-1 bg-gray-200 rounded mr-2 font-normal"
                          key={i}
                        >
                          {item}
                        </span>
                      </Col>
                    ))
                  : ''}
              </Row>
            </p>
          </Card>
          <Card>
            <Row>
              <Col className="grid gap-3" span={12}>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Industry</span>
                  </Col>
                  <Col span={18}>
                    <span className="flex flex-wrap">
                      {detailOrganization?.industries
                        ? detailOrganization?.industries.map((item, i) => (
                            <span
                              className="rounded mr-2 text-base font-semibold text-blue-500"
                              key={i}
                            >
                              {item}
                              {i === detailOrganization?.industries?.length - 1
                                ? '.'
                                : ','}
                            </span>
                          ))
                        : ''}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">
                      Founding Year
                    </span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.founded_year}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Employees</span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.estimated_num_employees}
                    </span>
                  </Col>
                </Row>
                {detailOrganization?.publicly_traded_exchange && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">Trading</span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.publicly_traded_exchange?.toUpperCase()}{' '}
                        :{' '}
                        {detailOrganization?.publicly_traded_symbol?.toUpperCase()}
                        .
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.market_cap && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Market Cap
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.market_cap}
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.annual_revenue_printed && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Annual Revenue
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        ${detailOrganization?.annual_revenue_printed}
                      </span>
                    </Col>
                  </Row>
                )}
              </Col>
              <Col span={12}>
                <Row className="items-center py-1">
                  <Col span={4} className="flex items-center mb-auto">
                    <EnvironmentOutlined className="pr-3 text-blue-400" />
                    <span className="pr-3 text-graySecondary text-white text-base font-semibold">
                      Location
                    </span>
                  </Col>
                  <Col span={18} className="pr-3 text-base">{`${
                    detailOrganization?.raw_address
                      ? detailOrganization?.raw_address + ','
                      : ''
                  } ${
                    detailOrganization?.city
                      ? detailOrganization?.city + ','
                      : ''
                  } ${
                    detailOrganization?.state
                      ? detailOrganization?.state + ','
                      : ''
                  } ${
                    detailOrganization?.zip ? detailOrganization?.zip + ',' : ''
                  } ${
                    detailOrganization?.country
                      ? detailOrganization?.country + '.'
                      : ''
                  }`}</Col>
                </Row>
              </Col>
            </Row>
          </Card>
          <Card style={{ width: 600 }} className="my-auto mx-auto">
            <p className="text-base font-semibold mb-3">
              Technology Insights (
              {detailOrganization.current_technologies
                ? detailOrganization?.current_technologies.length || 0
                : 0}
              )
            </p>
            <List
              style={{ overflowY: 'scroll', maxHeight: '500px' }}
              header={false}
              footer={false}
              bordered
              dataSource={detailOrganization?.current_technologies || []}
              renderItem={(item) => (
                <List.Item>
                  <Row>
                    <Col className=" text-base font-semibold" span={24}>
                      {item?.name}
                    </Col>
                    {item?.category}
                  </Row>
                </List.Item>
              )}
            />
          </Card>
        </div>
      </Modal>
    </div>
  );
}

export default ContactFinderForm;
