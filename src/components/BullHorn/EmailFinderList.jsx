/* eslint-disable */
import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import {
  Input,
  Select,
  Button,
  Form,
  AutoComplete,
  Modal,
  Table,
  Row,
  Col,
  notification,
  Card,
  Pagination,
  Collapse,
  Tabs,
  Checkbox,
  List,
  Typography,
  Flex,
  InputNumber,
  DatePicker,
  Tag,
  Radio,
  Space,
  Segmented,
  Spin,
  Dropdown,
  Image,
  Popconfirm,
  Popover,
  Tooltip,
  message,
  Badge,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import {
  searchBullhornData,
  // getCounties,
  // getCountries,
  searchBullhorn,
  insertBullhorn,
  // upladteBullhorn,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { useState, useEffect, useMemo } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  <PERSON>edinOutlined,
  FacebookOutlined,
  TwitterOutlined,
  <PERSON>Outlined,
  SearchOutlined,
  CloudDownloadOutlined,
  BankOutlined,
  CaretDownOutlined,
  CopyOutlined,
  MenuUnfoldOutlined,
  SendOutlined,
  SmallDashOutlined,
  DeleteOutlined,
  DownOutlined,
  CaretRightOutlined,
  StarOutlined,
  SyncOutlined,
  EnterOutlined,
  UserOutlined,
  FileSearchOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  employeeFinderSearchTag,
  getLDetailEmployee,
  getListCompany,
  getListEmployee,
  getListCompanies,
  employeeFinderIntentCategory,
  employeeFinderSearchSignals,
  getRevenue,
  getOrganizationsSnippet,
  getRecommendedContactList,
  handleBulkEnrichContactData,
  handleGetEnrichData,
} from '../../services/employee';

import BullhornSubmissionContact from './BullhornSubmissionContact';
import { getDetailCompanyById } from '../../services/companyFinder';
import IntentCollap from './IntentCollap';
// import { MdOutlinePerson } from 'react-icons/md';
import { FaRegBuilding, FaRegUser } from 'react-icons/fa';
import logoNoCompany from '../../assets/img/no_company.png';
import logo from '/logo_bull.webp';
import BullhornSendEmail from './BullhornSendEmailModal';
import ModalShowListExitSequence from '../../containers/Sequence/ModalShowListExitSequence';
import ModalListUserGroup from '../../containers/Sequence/ModalListUserGroup';
import BullhornBulkAddContactModal from './BullhornBulkAddContactModal';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import logoZielo from '../../assets/img/no_company.png';
import {
  faArrowsToDot,
  faBriefcase,
  faBuildingUn,
  faChartLine,
  faDollarSign,
  faEnvelope,
  faEnvelopeCircleCheck,
  faImagePortrait,
  faIndustry,
  faLocationDot,
  faMedal,
  faMicrochip,
  faMoneyBills,
  faTowerBroadcast,
  faUserGroup,
} from '@fortawesome/free-solid-svg-icons';
import {
  faBuilding,
  faEnvelopeOpen,
} from '@fortawesome/free-regular-svg-icons';
import { UserGroupIcon } from '../Sidebar/consts';
import CompanyDetailModal from './CompanyDetail';
import { handleGenerateNotificationBulkAddContact } from '../../helpers/util';
import { COMMON_STRINGS } from '../../constants/common.constant';
import EventSourceRender from '../EventSource';
import Loading from '../../containers/HotList/Loading';
import { isEmpty } from 'lodash';
import { ENRICH_DATA_STATUS } from '../../containers/HotList/HotListTable';
import handleRenderTime from '../../function/handleRenderTime';
import { v4 as uuid } from 'uuid';
import {
  createSavedSearch,
  getSavedSearch,
  updateSavedSearch,
} from '../../services/emailFinder';

export const CONTACTS_LIST_PAGE_SIZE = 25;
export const TIME_ENRICH_EACH_FIELD = 35 * 1000; // seconds

export const initialListEmployeePagination = {
  page: 0,
  per_page: 0,
  total_entries: 0,
  total_pages: 0,
};

const { Meta } = Card;
function ListEmailFinder({
  control,
  setValue,
  getValues,
  job,
  handleCloseClient,
  setHandleCloseClient,
  handleCloseContact,
  setHandleCloseContact,
  functionContactClient,
  handleGetDefaultAddress,
  setIsSearchCompany,
  watch,
}) {
  // Custom hooks for search with debounce
  const { handleSubmit } = useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [listEmails, setListEmails] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalStaffOpen, setIsModalStaffOpen] = useState(false);
  const [dataListTopicTitle, setDataListTopicTitle] = useState([]);
  const [listDataIntentSetting, setListDataIntentSetting] = useState([]);
  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [listDataFunding, setListDataFunding] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [openExistingSequence, setOpenExistingSequence] = useState(false);
  const [openExistingUserGroup, setOpenExistingUserGroup] = useState(false);
  const [currentContact, setCurrentContact] = useState();
  const [listCurrentOrg, setListCurrentOrg] = useState([]);
  const [bulkContacts, setBulkContacts] = useState([]);
  const [employeesChecked, setEmployeesChecked] = useState([]);
  const [showNotAnyOfCompany, setShowNotAnyOfCompany] = useState(false);
  const [showIncludeCompany, setShowIncludeCompany] = useState(false);

  const [listEmployee, setListEmployee] = useState([]);
  const [listEmployeePagination, setListEmployeePagination] = useState({
    ...initialListEmployeePagination,
  });
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);

  const [listEmployeeCompany, setListEmployeeCompany] = useState([]);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
    useState({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });

  const [listCompanies, setListCompanies] = useState([]);
  const [listCompaniesPagination, setListCompaniesPagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isDetailEmployeCompany, setIsDetailEmployeeCompany] = useState(false);

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const [showFindEmail, setShowFindEmail] = useState(false);
  const [isAddContactForm, setIsAddContactForm] = useState(false);
  const [isAddContactFormBulk, setIsAddContactFormBulk] = useState(false);
  const [isLoadingAddContactFormBulk, setIsLoadingAddContactFormBulk] =
    useState(false);
  const [loadingGetBulkData, setLoadingBulkData] = useState(false);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [flagDetailContact, setFlagDetailContact] = useState(false);
  const [fundingSize, setFundingSize] = useState(true);

  const [
    debouncedSearchTextCompanyPeople,
    setDebouncedSearchTextCompanyPeople,
  ] = useState('');
  const [debouncedSearchTextCompany, setDebouncedSearchTextCompany] =
    useState('');
  const [debouncedLocationFinderText, setDebouncedLocationFinderText] =
    useState('');
  const [
    debouncedLocationFindCompanyText,
    setDebouncedLocationFindCompanyText,
  ] = useState('');
  const [debouncedSearchTextIndustry, setDebouncedSearchTextIndustry] =
    useState('');
  const [
    debouncedSearchTextIndustryCompany,
    setDebouncedSearchTextIndustryCompany,
  ] = useState('');
  const [debouncedSearchTextTitle, setDebouncedSearchTextTitle] = useState('');
  const [listDetailEmployee, setListDetailEmployee] = useState([]);
  const [showIncludeKeywordPeople, setShowIncludeKeywordPeople] =
    useState(false);
  const [showIncludeAllKeywordPeople, setShowIncludeAllKeywordPeople] =
    useState(false);
  const [showExcludeKeywordsPeople, setShowExcludeKeywordsPeople] =
    useState(false);
  const [showIncludeKeywordCompany, setShowIncludeKeywordCompany] =
    useState(false);
  const [showIncludeAllKeywordCompany, setShowIncludeAllKeywordCompany] =
    useState(false);
  const [showExcludeKeywordsCompany, setShowExcludeKeywordsCompany] =
    useState(false);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  const [dataTechnologies, setDataTechnologies] = useState([]);
  const [dataPersonTitle, setDataPersonTitle] = useState([]);
  const [dataLocation, setDataLocation] = useState([]);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [openModalSendEmail, setOpenSendEmail] = useState(false);

  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    isLoading: isLoadingConsultants,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const { handleSearch: setSkillSearchText } = useInfiniteScrollWithSearch(
    searchBullhornData('Skill')
  );

  const { handleSearch: setCategorySearchText } = useInfiniteScrollWithSearch(
    searchBullhornData('Category')
  );

  const { handleSearch: setIndustrySearchText } = useInfiniteScrollWithSearch(
    searchBullhornData('BusinessSector')
  );

  const [showModalAddTopic, setShowModalAddTopic] = useState(false);
  const [dataParentIntent, setDataParentIntent] = useState([]);

  const handleGetMetaMode = async (body) => {
    const { data } = await getListEmployee({
      ...body,
      // metaMode: 'metadata_mode',
    });

    return data;
  };

  const getRecommendedContacts = async (bodyToSearch) => {
    const filter = {
      company: getValues('companyFinder'),
      page: bodyToSearch?.page || 1,
      pageSize: bodyToSearch?.pageSize || 25,
      ...bodyToSearch,
    };
    const result = { items: [], count: 0 };
    try {
      const { data } = await getRecommendedContactList(filter);
      result.count = data?.result?.count || 0;

      if (data?.result?.items?.length > 0) {
        const recommendedContactList =
          data?.result?.items?.map((item) => ({
            ...item,
            isRecommended: true,
          })) || [];
        result.items = [...recommendedContactList];
        return result;
      } else {
        return result;
      }
    } catch (error) {
      return result;
    }
  };

  const handleFindEmails = async ({ page = 1 }) => {
    try {
      setListEmployee([]);
      const currentType = watch('keyAcordionPeople') || 0;
      setIsLoadingEmployee(true);
      let resultData = [];
      let bodyToSearch = null;
      let recommendedContactsTemp = [];
      let rcmContactsCount = 0;
      if (currentType !== '6') {
        const locations = getValues().locationFinder
          ? getValues().locationFinder.map((item) => item.label)
          : [];
        const personTitles = getValues().titleFinder
          ? getValues().titleFinder.map((item) => item.label)
          : [];
        const employeeRanges = getValues().employeeFinder
          ? getValues().employeeFinder.map((item) => item.value)
          : [];
        const industryTagIds = getValues().industryFinder
          ? getValues().industryFinder.map((item) => item.value)
          : [];
        const contactEmailStatus = getValues().contactEmailStatus
          ? getValues().contactEmailStatus
          : [];
        const contactEmailOpened = getValues().emailOpenedStatus ?? null;
        const contactEmailOpenedAtLeast =
          getValues().contactEmailOpenedTime ?? null;
        const contactEmailOpenedAtDateRangeMin =
          getValues().contactEmailOpenedTimeMin &&
            getValues().contactEmailOpenedTimeMin !== ''
            ? getValues().contactEmailOpenedTimeMin
            : null;
        const contactEmailOpenedAtDateRangeMax =
          getValues().contactEmailOpenedTimeMax &&
            getValues().contactEmailOpenedTimeMax !== ''
            ? getValues().contactEmailOpenedTimeMax
            : null;
        const intentStrengths = getValues().contactBuyingIntentScore
          ? getValues().contactBuyingIntentScore
          : [];
        const intentIds = getValues().contactBuyingIntentIds
          ? getValues().contactBuyingIntentIds
          : [];
        const searchSignalIds = getValues().searchSignalIds
          ? getValues().searchSignalIds
          : [];
        const recommendationScoresMinTranche = getValues().contactMinimumScore
          ? getValues().contactMinimumScore
          : null;
        const containOneKeywords = watch('includeKeywordPeople')
          ? watch('includeKeywordPeople').map((item) => item.label)
          : [];
        const containAllKeyWords = watch('includeAllKeywordPeople')
          ? watch('includeAllKeywordPeople').map((item) => item.label)
          : [];
        const excludeKeyWords = watch('excludeKeywordsPeople')
          ? watch('excludeKeywordsPeople').map((item) => item.label)
          : [];
        const organizationLatestFundingStageCd =
          getValues('fundingStatusItem') ?? [];
        const currentlyUsingAnyOfTechnologyUids =
          getValues('listTechnologies') ?? [];
        const notExistFields =
          getValues('revenueStatus') === 'is_un_known'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const existFields =
          getValues('revenueStatus') === 'is_know'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const organizationJobLocations = getValues('contactJobLocated') ?? [];
        const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
        const personName = getValues('nameFinderText') ?? null;
        const organizationNumJobsRangeMin =
          getValues('organizationNumJobsRangeMin') ?? null;
        const organizationNumJobsRangeMax =
          getValues('organizationNumJobsRangeMax') ?? null;
        const organizationJobPostedAtRangeMin =
          getValues('organizationJobPostedAtRangeMin') ?? null;
        const organizationJobPostedAtRangeMax =
          getValues('organizationJobPostedAtRangeMax') ?? null;
        const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
        const personPastOrganizationIds =
          getValues('companyFinderInclude') ?? [];
        const notOrganizationIds = getValues('companyFinderNotAnySelect') ?? [];
        const totalFundingRangeMin = getValues('fundingMin') ?? null;
        const totalFundingRangeMax = getValues('fundingMax') ?? null;
        bodyToSearch = {
          organizationId: watch('companyFinderId'),
          locations,
          personTitles,
          employeeRanges,
          industryTagIds,
          page,
          searchText: watch('searchPeople'),
          containOneKeywords,
          containAllKeyWords,
          excludeKeyWords,
          contactEmailStatus,
          contactEmailOpened,
          contactEmailOpenedAtLeast,
          contactEmailOpenedAtDateRangeMin,
          contactEmailOpenedAtDateRangeMax,
          intentStrengths,
          intentIds,
          searchSignalIds,
          recommendationScoresMinTranche,
          currentlyUsingAnyOfTechnologyUids,
          existFields,
          notExistFields,
          organizationTradingStatus,
          organizationLatestFundingStageCd,
          totalFundingRangeMax,
          totalFundingRangeMin,
          personName,
          organizationJobLocations,
          qOrganizationJobTitles,
          organizationNumJobsRangeMin,
          organizationNumJobsRangeMax,
          organizationJobPostedAtRangeMin,
          organizationJobPostedAtRangeMax,
          notOrganizationIds,
          personPastOrganizationIds,
        };
        if (
          bodyToSearch?.organizationId?.trim() ||
          bodyToSearch?.locations?.length > 0 ||
          bodyToSearch?.personTitles?.length > 0 ||
          bodyToSearch?.personName?.trim()
        ) {
          const { items, count } = await getRecommendedContacts(bodyToSearch);
          recommendedContactsTemp = [...items];
          rcmContactsCount += count;
        }

        if (recommendedContactsTemp.length < CONTACTS_LIST_PAGE_SIZE) {
          const rcmPageCost = Math.ceil(
            rcmContactsCount / CONTACTS_LIST_PAGE_SIZE
          );
          const { data } = await getListEmployee({
            ...bodyToSearch,
            page: page - rcmPageCost > 0 ? page - rcmPageCost : 1,
          });
          resultData = data;
        }
      } else if (currentType === '6') {
        if (bodyToSearch?.organizationId?.trim()) {
          const { items, count } = await getRecommendedContacts(bodyToSearch);
          recommendedContactsTemp = [...items];
          rcmContactsCount += count;
        }

        if (recommendedContactsTemp.length < CONTACTS_LIST_PAGE_SIZE) {
          const rcmPageCost = Math.ceil(
            rcmContactsCount / CONTACTS_LIST_PAGE_SIZE
          );
          const { data } = await getListEmployee({
            organizationId: watch('companyFinderId'),
            page: page - rcmPageCost > 0 ? page - rcmPageCost : 1,
          });
          resultData = data;
        }

        const people = resultData?.result?.people || [];
        const contacts = resultData?.result?.contacts || [];
        setListEmployeeCompany([...people, ...contacts]);
        setListEmployeePaginationCompany(resultData?.result?.pagination);
        setIsLoadingEmployee(false);
        return;
      }
      if (resultData.length === 0 && recommendedContactsTemp.length === 0)
        return dataEmployeeNotFound();
      const people = resultData?.result?.people || [];
      const contacts = resultData?.result?.contacts || [];
      const recommendedCOntactsList = [...recommendedContactsTemp];
      const listData =
        recommendedCOntactsList.length >= CONTACTS_LIST_PAGE_SIZE
          ? [...recommendedCOntactsList]
          : [...recommendedCOntactsList, ...people, ...contacts].splice(
            0,
            CONTACTS_LIST_PAGE_SIZE
          );
      setListEmployee(listData);
      setIsLoadingEmployee(false);
      const dataPagination = await handleGetMetaMode(bodyToSearch);
      const pagination =
        dataPagination?.result?.pagination ||
        resultData?.result?.pagination ||
        initialListEmployeePagination;
      const combinedPagination = {
        ...pagination,
        total_entries: +pagination?.total_entries + rcmContactsCount,
      };
      setListEmployeePagination({ ...combinedPagination });
      const listOrgIds = listData
        ?.map((item) => item?.organization_id)
        .filter((id) => id != null);

      const uniqueOrgIds = [...new Set(listOrgIds)];
      const dataOrg = await getOrganizationsSnippet({
        ids: uniqueOrgIds,
      });

      setListCurrentOrg(dataOrg.data.organizations);
    } catch (err) {
      setListEmployee([]);
      setListEmployeePagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      console.log(err);
      setIsLoadingEmployee(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const handleFindCompany = async ({ page = 1 }) => {
    try {
      setIsDetailEmployeeCompany(false);
      setIsLoadingCompanies(true);
      const locations = getValues().locationFindCompany
        ? getValues().locationFindCompany.map((item) => item.label)
        : [];
      const employeeRanges = getValues().employeesFindCompany
        ? getValues().employeesFindCompany.map((item) => item.value)
        : [];
      const industryTagIds = getValues().industryKeywordCompany
        ? getValues().industryKeywordCompany.map((item) => item.value)
        : [];
      const containOneKeywords = watch('includeKeywordCompany')
        ? watch('includeKeywordCompany').map((item) => item.label)
        : [];
      const containAllKeyWords = watch('includeAllKeywordCompany')
        ? watch('includeAllKeywordCompany').map((item) => item.label)
        : [];
      const excludeKeyWords = watch('excludeKeywordsCompany')
        ? watch('excludeKeywordsCompany').map((item) => item.label)
        : [];
      const { data } = await getListCompanies({
        organizationId: watch('companyFindCompanyId'),
        locations,
        employeeRanges,
        industryTagIds,
        page,
        searchText: watch('searchCompany'),
        containOneKeywords,
        containAllKeyWords,
        excludeKeyWords,
      });
      if (data.length === 0) return dataCompanyNotFound();
      if (data.organizations.length === 0) return dataCompanyNotFound();
      setListCompanies(data?.organizations);
      setListCompaniesPagination(data?.pagination);
      setIsLoadingCompanies(false);
    } catch (err) {
      setListCompanies([]);
      setListCompaniesPagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingCompanies(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const dataEmployeeNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingEmployee(false);
  };

  const dataCompanyNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingCompanies(false);
  };

  const handleSubmitPeople = async ({ page = 1 }) => {
    if (watch('searchPeople') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindEmails({ page });
  };

  const handleSubmitCompanyFind = async ({ page = 1 }) => {
    if (watch('searchCompany') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindCompany({ page });
  };

  const resetFormFindEmails = () => {
    setListEmails([]);
    setSelectedEmployee('');
    setIsLoading(false);
  };

  const handlePaginationListEmployee = (page) => {
    handleFindEmails({ page: page });
  };

  const handlePaginationListCompany = (page) => {
    handleFindCompany({ page: page });
  };

  const handleAddContact = async (fullName, dataContact) => {
    try {
      const enrichedRecord = enrichData?.find(
        (item) =>
          item.linkedin_url === dataContact.linkedin_url ||
          dataContact.id === item.record_id
      );
      if (
        (!dataContact?.email || dataContact?.email === '') &&
        (!enrichedRecord?.valid_work_email || !enrichedRecord?.work_email_1)
      ) {
        notification.error({
          message: `This contact has no email yet`,
        });
        return;
      }
      setIsLoading(true);
      await handleSetDataContact({
        ...dataContact,
        email: enrichedRecord?.valid_work_email || dataContact?.email,
      });
      const { data } = await searchBullhorn(
        'ClientContact',
        0,
        5,
        '',
        '',
        '',
        dataContact?.email ||
        enrichedRecord?.valid_work_email ||
        enrichedRecord?.work_email_1,
        null,
        true
      );

      if (data.result.length === 0) {
        setIsLoading(false);
        setIsAddContactForm(true);
      } else {
        const isDataExist = listAddContactSelected.some(
          (item) => item.id === data?.result[0].id
        );
        if (!isDataExist) {
          setListAddContactSelected([
            ...listAddContactSelected,
            { ...data.result[0] },
          ]);
        }
        functionContactClient.handleContactSearch(data.result[0].name, '');
        setIsLoading(false);
        notification.success({
          message: `This contact already existed on Bullhorn`,
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleSubmitAddContact = async () => {
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      company,
      categories,
    } = getValues()?.clientContact;

    if (!companyId) {
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      return notification.error({ message: 'Address is required.' });
    } else if (industries.length === 0) {
      return notification.error({ message: 'Industries is required.' });
    }
    // Based on format of BH
    const rawInformation = {
      ...(getValues()?.rawInformation?.[0] || {}),
      organization_name: company,
      first_name: getValues()?.clientContact?.firstName,
      last_name: getValues()?.clientContact?.surename,
      name: `${getValues()?.clientContact?.firstName} ${getValues()?.clientContact?.surename
        }`,
      owner_id: getValues()?.clientContact.consultantId,
    };
    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      // lastName: getValues()?.clientContact?.lastName,
      lastName: getValues()?.clientContact?.surename,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${getValues()?.clientContact?.surename
        }`,
      customText1: getValues()?.clientContact.linkedProfileUrl,
      skills: {
        replaceAll: getValues()?.clientContact?.skills?.map(
          (item) => item?.value
        ),
      },
      categories: {
        replaceAll: getValues()?.clientContact?.categories?.map(
          (item) => item?.value
        ),
      },
      rawInformation,
      requestId: uuid(),
    };

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newContactPayloadCleaned = cleanPayload(payload);
    const { data } = await insertBullhorn(newContactPayloadCleaned);
    const isDataExist = listAddContactSelected.some(
      (item) => item.id === data?.result?.changedEntityId
    );
    if (!isDataExist) {
      setListAddContactSelected([
        ...listAddContactSelected,
        { ...data?.result?.data, id: data?.result?.changedEntityId },
      ]);
    }
    setValue('clientContact.companyId', null);
    setIsLoading(false);
    setIsAddContactForm(false);
    notification.success({
      message: `Success add contact ${data.result.data.name}`,
    });
  };

  const handleBulkAddContact = async () => {
    await handleSubmitBulkAddContact();
  };

  const handleSubmitBulkAddContact = async () => {
    if (!getValues()?.clientContactBulk?.companyId) {
      notification.error({
        message: 'Company is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.status) {
      notification.error({
        message: 'Status is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.industries) {
      notification.error({
        message: 'Industries is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.categories) {
      notification.error({
        message: 'Categories is required.',
      });
      return;
    }
    setLoadingBulkData(true);
    setIsLoadingAddContactFormBulk(true);
    const checkListContact = [];
    const dataToInsert = [];
    const unretrievableContactIds = [];
    const checkListContactIds = [];
    for (let id of selectedRowKeys) {
      try {
        const { data } = await getLDetailEmployee({ employeeId: id });
        checkListContact.push({ ...data, rawId: id });
        // setDataContacts(prevDataContacts => [data, ...prevDataContacts]);
      } catch (error) {
        unretrievableContactIds.push(id);
      }
    }

    const emailList = checkListContact
      .filter(
        (contact) => contact.email !== null || contact?.email?.trim() != null
      )
      .map((contact) => contact.email);

    const emailNotFound = checkListContact
      .filter(
        (contact) => contact.email == null || contact?.email?.trim() == null
      )
      .map((contact) => contact.email);

    if (emailList?.length == 0) {
      notification.warning({
        message: 'No email available',
      });
      setIsAddContactFormBulk(false);
      setIsLoadingAddContactFormBulk(false);
      setSelectedRowKeys([]);
      return;
    }
    for (let itemCheck of checkListContact) {
      try {
        const { data } = await searchBullhorn(
          'ClientContact',
          0,
          5,
          '',
          '',
          '',
          itemCheck?.email,
          null,
          true
        );
        if (data.result.length === 0) {
          dataToInsert.push(itemCheck);
          checkListContactIds.push(itemCheck?.rawId);
        }
      } catch (error) { }
    }

    setLoadingBulkData(false);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      status,
      categories,
    } = getValues()?.clientContactBulk;

    setIsLoadingAddContactFormBulk(true);

    const arrContact = dataToInsert?.map((item) => {
      const addressParts = [
        item?.city,
        item?.country,
        item?.county,
        item?.state,
      ].filter(Boolean);

      const combinedAddress = addressParts.join(', ');
      const requestId = uuid();
      return {
        entityName: 'ClientContact',
        firstName: item?.first_name,
        // lastName: getValues()?.clientContact?.lastName,
        status: status.label,
        lastName: item?.last_name,
        owner: {
          id: userToSet?.user?.consultantId || userToSet?.consultantId,
        },
        // type: getValues()?.clientContact?.type,
        // secondaryOwners: {
        //   replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
        // },
        clientCorporation: {
          id: companyId,
        },
        // division: getValues()?.clientContact?.department,
        occupation: item?.title,
        email: item?.email,
        phone: item?.sanitized_phone,
        mobile: getValues()?.clientContactBulk?.mobilePhone,
        phone2: getValues()?.clientContactBulk?.otherPhone,
        fax: item?.clientContact?.fax,
        address: {
          countryID: getValues()?.clientContactBulk?.stateId,
          countryName: getValues()?.clientContactBulk?.state,
          state: getValues()?.clientContactBulk?.county,
          address1: getValues()?.clientContactBulk?.address,
          city: getValues()?.clientContactBulk?.city,
          zip: getValues()?.clientContactBulk?.zip,
        },
        businessSectors: {
          replaceAll: industries.map((obj) => obj.value),
        },
        // comments: getValues()?.clientContact?.generalCommets,
        // referredByPerson: {
        //   id: getValues().clientContact.referredById || null,
        // },
        skills: {
          replaceAll: skills?.map((obj) => obj.value),
        },
        categories: {
          replaceAll: categories?.map((obj) => obj.value),
        },
        options: {
          skills: {
            replaceAll: skills?.map((obj) => obj.value),
          },
          categories: {
            replaceAll: categories?.map((obj) => obj.value),
          },
        },
        name: `${item?.first_name} ${item?.last_name}`,
        customText1: item?.linkedin_url,
        requestId: requestId,
        rawInformation: {
          ...item,
          organization:
            item?.organization ||
            listEmployee.find((emp) => checkListContactIds.includes(emp?.id))
              ?.organization,
        },
      };
    });

    try {
      for (let itemI of arrContact) {
        try {
          const { data } = await insertBullhorn(itemI);
        } catch (error) { }
      }
    } catch (e) {
      // notification.error({
      //   message: "Some things went wrong"
      // })
    }

    handleGenerateNotificationBulkAddContact(
      arrContact,
      selectedRowKeys,
      unretrievableContactIds,
      emailNotFound
    );

    setIsAddContactFormBulk(false);
    setIsLoadingAddContactFormBulk(false);
    setSelectedRowKeys([]);
  };

  const [dataContacts, setDataContacts] = useState([]);

  const handleResetFormAddContact = async () => {
    setValue('clientContact.namePrefixSelect', null);
    setValue('clientContact.namePrefix', null);
    setValue('clientContact.firstName', null);
    setValue('clientContact.middleName', null);
    setValue('clientContact.surename', null);
    setValue('clientContact.consultantId', null);
    setValue('clientContact.consultant', null);
    setValue('clientContact.consultantSelect', null);
    setValue('clientContact.statusSelect', null);
    setValue('clientContact.type', null);
    setValue('clientContact.secondaryOwnerSelect', null);
    setValue('clientContact.companyId', null);
    setValue('clientContact.department', null);
    setValue('clientContact.jobTitle', null);
    setValue('clientContact.workEmail', null);
    setValue('clientContact.personalEmail', null);
    setValue('clientContact.workPhone', null);
    setValue('clientContact.mobilePhone', null);
    setValue('clientContact.otherPhone', null);
    setValue('clientContact.fax', null);
    setValue('clientContact.stateId', null);
    setValue('clientContact.state', null);
    setValue('clientContact.county', null);
    setValue('clientContact.countySelect', null);
    setValue('clientContact.address', null);
    setValue('clientContact.city', null);
    setValue('clientContact.zip', null);
    setValue('clientContact.industries', []);
    setValue('clientContact.generalCommets', null);
    setValue('clientContact.referredById', null);
  };

  const handleGetDetailEmployee = async (id) => {
    try {
      const { data } = await getLDetailEmployee({ employeeId: id });
      setListDetailEmployee([...listDetailEmployee, { ...data }]);
      setIsLoading(true);
      setListEmployee([...listEmployee]);
      setListEmployeePagination({ ...listEmployeePagination });
      setIsLoading(false);
    } catch (error) {
      console.error('Error in getting employee detail', error);
      if (error?.response?.status == 402) {
        notification.error({
          message:
            'Your account is running out of credits. Please contact to your admin for support!',
        });
      } else {
        notification.error({
          message: 'Something went wrong. Please try again in 5 minutes',
        });
      }
    }
  };

  const handleAccordionChangePeople = (keys) => {
    setValue('keyAcordionPeople', keys[0]);
    if (keys[0] === '10') {
      refetchListSignals();
    }
  };

  const resetAllPeople = () => {
    setValue('companyFinder', '');
    setValue('companyFinderId', '');
    setValue('locationFinderText', ' ');
    setValue('locationFinder', []);
    setValue('titleFinderText', ' ');
    setValue('titleFinder', []);
    setValue('employeeFinderText', '');
    setValue('employeeFinder', []);
    setValue('industryFinderText', ' ');
    setValue('industryFinder', []);
    setShowIncludeKeywordPeople(false);
    setShowIncludeAllKeywordPeople(false);
    setShowExcludeKeywordsPeople(false);
    setValue('includeKeywordPeople', []);
    setValue('includeKeywordPeopleText', '');
    setValue('includeAllKeywordPeople', []);
    setValue('includeAllKeywordPeopleText', '');
    setValue('excludeKeywordsPeople', []);
    setValue('excludeKeywordsPeopleText', '');
  };

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);
  };

  const resetAllCompany = () => {
    setValue('companyFindCompany', '');
    setValue('companyFindCompanyId', '');
    setValue('locationFindCompanyText', ' ');
    setValue('locationFindCompany', []);
    setValue('employeesFindCompanyText', ' ');
    setValue('employeesFindCompany', []);
    setValue('industryKeywordCompanyText', ' ');
    setValue('industryKeywordCompany', []);
    setShowIncludeKeywordCompany(false);
    setShowIncludeAllKeywordCompany(false);
    setShowExcludeKeywordsCompany(false);
    setValue('includeKeywordCompany', []);
    setValue('includeKeywordCompanyText', '');
    setValue('includeAllKeywordCompany', []);
    setValue('includeAllKeywordCompanyText', '');
    setValue('excludeKeywordsCompany', []);
    setValue('excludeKeywordsCompanyText', '');
  };

  const updateArrayByKey = (key, checked, optionKey) => {
    const newValues = checked
      ? [...getValues()[key], optionKey]
      : getValues()[key].filter((value) => value !== optionKey);

    setValue(key, newValues);
  };

  const handleGetIntentCategory = async () => {
    const data = await employeeFinderIntentCategory();
    setDataListTopicTitle(data.data.categories);
  };
  useEffect(() => {
    handleGetIntentCategory();
  }, [showModalAddTopic]);

  const checkboxGroups = {
    'Safe to send': [
      { key: 'likely_to_engage', label: 'Likely to engage' },
      { key: 'verified', label: 'Verified' },
    ],
    'Send with caution': [{ key: 'unverified', label: 'Unverified' }],
    'Do not send': [
      { key: 'update_required', label: 'Update required' },
      { key: 'unavailable', label: 'Unavailable' },
    ],
  };

  const EmailOpenedStatus = [
    { key: 0, value: 'yes', label: 'Yes' },
    { key: 0, value: 'no', label: 'No' },
  ];

  const OptionBuyIntent = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'mid' },
    { label: 'Low', value: 'low' },
    { label: 'None', value: 'none' },
  ];

  const handleDeleteIntent = async (value) => {
    const index = listDataIntentSetting.find((obj) => obj.id === value);
    if (index) {
      const newArr = listDataIntentSetting.filter((obj) => obj.id !== value);
      setListDataIntentSetting(newArr);
    }
  };

  const handleChangeSettingIntent = async () => {
    setDataParentIntent(listDataIntentSetting);
    setShowModalAddTopic(false);
    localStorage.setItem(
      'listIntentChoose',
      JSON.stringify(listDataIntentSetting)
    );
  };

  const handleGetTechno = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'technology',
    });
    setDataTechnologies(data?.tags);
  };

  const handleGetPersonalTitles = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'person_title',
    });
    setDataPersonTitle(data?.tags);
  };

  const handleGetLocation = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'location',
    });
    setDataLocation(data?.tags);
  };

  const formatNumber = (number) => {
    const suffixes = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    let suffixIndex = 0;
    while (number >= 1000) {
      number /= 1000;
      suffixIndex++;
    }
    return number?.toFixed(1) + suffixes[suffixIndex];
  };

  const handleGetRevenue = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataRevenue(data?.faceting?.organization_trading_status_facets);
  };

  const handleGetFunding = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataFunding(data?.faceting?.latest_funding_stage_facets);
  };

  useEffect(() => {
    handleGetTechno();
    handleGetPersonalTitles();
    handleGetLocation();
    handleGetRevenue({
      openFactorNames: ['organization_trading_status'],
    });
    handleGetFunding({
      openFactorNames: ['organization_latest_funding_stage_cd'],
    });
  }, []);

  const itemsDropdown = [
    {
      key: '1',
      label: <div>Edit</div>,
    },
    {
      key: '2',
      label: <div>Delete</div>,
    },
  ];

  const [enrichLoading, setEnrichLoading] = useState(false);
  const [estimationDuration, setEstimationDuration] = useState('0');
  const [enrichingData, setEnrichingData] = useState([]);
  const [updatingData, setUpdatingData] = useState({});
  const [enrichData, setEnrichData] = useState([]);

  const handleEnrichRecord = async (record) => {
    const enrichContacts = [
      {
        fullName: record?.name || '',
        recordId: record?.id,
        companyName: record?.organization_name,
        linkedInUrl: record?.linkedin_url,
      },
    ];

    toggleLoading();
    const delay = parseInt(1 * TIME_ENRICH_EACH_FIELD, 10);
    var timeout = new Date().getTime() + delay;

    const payload = {
      data: enrichContacts,
      listId: 'email-finder-uid',
      type: 'EMAIL_FINDER',
    };

    setEnrichingData([...enrichContacts]);

    await handleBulkEnrichContactData(payload);
  };

  const toggleLoading = (type = 'add') => {
    const el = document.getElementById('expand-contract-bulk-enrich');
    if (!el) return;
    if (type === 'add') {
      setEnrichLoading(true);
      el.classList.add('expanded');
      el.classList.add('collapsed');
    } else if (type === 'remove') {
      setEnrichLoading(false);
      el.classList.remove('expanded');
      el.classList.remove('collapsed');
    }
  };

  const endProcess = () => {
    toggleLoading('remove');
  };

  const getEnrichData = async (query) => {
    try {
      const { data } = await handleGetEnrichData('email-finder-uid', query);
      return data?.result?.items || [];
    } catch (error) {
      return [];
    }
  };

  const checkEnrichingData = async () => {
    const query = {
      type: 'EMAIL_FINDER',
      // status: 'IN_PROGRESS',
    };
    const enrichedDataTemp = await getEnrichData(query);

    if (enrichedDataTemp?.length > 0) {
      const inprogressList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.IN_PROGRESS
      );
      const sucessfullList = enrichedDataTemp?.filter(
        (item) => item?.status === ENRICH_DATA_STATUS.SUCCESSFUL
      );
      const enrichedData = sucessfullList?.map((contact) => ({
        ...contact?.enrichContact,
        createdAt: contact?.createdAt,
      }));

      if (inprogressList?.length > 0) {
        setEnrichingData(
          [...inprogressList]?.map((item) => item?.contactId?.toString())
        );
        toggleLoading();
      }
      setEnrichData([...enrichedData]);
    }
  };

  const handleUpdateEnrichData = async () => {
    const newDataSource = [
      ...enrichData,
      { ...updatingData, createdAt: new Date() },
    ];

    setEnrichData([...newDataSource]);
    setUpdatingData({});
    const query = {
      type: 'EMAIL_FINDER',
      status: ENRICH_DATA_STATUS.IN_PROGRESS,
    };
    const enrichedData = await getEnrichData(query);
    if (enrichedData?.length === 0) {
      endProcess();
      message.success('Enriching process finished!');
    }
  };

  const updateRecentlyData = ({ data }) => {
    const enrichedDataObject =
      typeof data === 'string' ? JSON.parse(data) : data;
    setUpdatingData({ ...enrichedDataObject });
    message.success(`Enriched 1 contact successfully!`);
  };

  useEffect(() => {
    if (isEmpty(updatingData)) return;
    handleUpdateEnrichData();
  }, [updatingData]);

  useEffect(() => {
    checkEnrichingData();
  }, []);

  const handleGenderSupportBar = (
    record,
    name = null,
    rawInformation = null
  ) => {
    const nameOptions = enrichData?.find(
      (item) =>
        item.linkedin_url === record.linkedin_url ||
        record.id === item.record_id
    );
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Popover
            placement="top"
            title={false}
            content={<div>Enrich Data</div>}
          >
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleEnrichRecord(record);
              }}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SyncOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              if (rawInformation) {
                setValue('rawInformation', [rawInformation]);
              }
              await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                        setCurrentContact(record);
                      }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                        setValue('email', record?.email);
                        setValue('sendMail.mailStepParentMailTo', [
                          { email: record?.email, name: record?.name },
                        ]);
                        setValue('optionContactSelect', record);
                        setOpenSendEmail(true);
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    {nameOptions?.valid_work_email || record?.email ? (
                      <div style={nameOptions ? { color: '#1677ff' } : {}}>
                        {nameOptions?.valid_work_email || record?.email}{' '}
                        <CopyOutlined
                          style={{ marginLeft: '10px' }}
                          onClick={() => {
                            navigator.clipboard.writeText(
                              nameOptions?.valid_work_email || record?.email
                            ),
                              notification.success({
                                message: 'Copy To Clipboard success',
                              });
                          }}
                        />
                      </div>
                    ) : (
                      <div>
                        <Tag
                          onClick={() => {
                            handleEnrichRecord(record);
                          }}
                          style={{ cursor: 'pointer' }}
                          icon={<SyncOutlined />}
                          color="#55acee"
                        >
                          Enrich
                        </Tag>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            }
          // okText={'Close'}
          // cancelText={<></>}
          // showCancel={false}
          // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
          // okText={'Close'}
          // cancelText={<></>}
          // showCancel={false}
          // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => {
                        setOpenExistingUserGroup(true);
                        setCurrentContact(record);
                        setBulkContacts([]);
                      }}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
          // okText={'Close'}
          // cancelText={<></>}
          // showCancel={false}
          // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const filterAddContact = [
    {
      key: '1',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBuilding}
              style={{ marginRight: '10px' }}
            />
            Company
          </span>
          {watch('companyFinderId') !== '' && watch('companyFinderId') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('companyFinder')}
            </span>
          )}
        </div>
      ),
      children: (
        <div>
          <div
            style={{
              color: '#737373',
              fontSize: '14px',
              fontWeight: '600',
            }}
          >
            Company name
          </div>
          <Form.Item label="" name="companyFinder">
            <Controller
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={companyListPeople.map((option) => ({
                    value: option.id,
                    label: (
                      <div className="grid p-2">
                        <div className="flex justify-between">
                          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                            {option.name}
                            <br />
                            <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                              {option.domain || '-'}
                            </span>
                          </span>
                          <img
                            className="absolute right-3"
                            src={option?.logo_url ? `${option?.logo_url}` : ''}
                            width={50}
                            height={50}
                            alt="Logo"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = logoZielo;
                              e.target.alt = 'Logo';
                            }}
                          />
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={(value) => {
                    setValue('companyFinder', value);
                    setValue('companyFinderSelect', null);
                    setValue('companyFinderId', null);
                  }}
                  onSelect={async (selectedCompanyId) => {
                    const selectedCompany = companyListPeople.find(
                      (ao) => ao.id == selectedCompanyId
                    );
                    setValue('companyFinder', selectedCompany.name);
                    setValue('companyFinderSelect', selectedCompanyId);
                    setValue('companyFinderId', selectedCompanyId);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
              name="companyFinder"
              control={control}
            />
          </Form.Item>

          <div>
            <Checkbox
              onChange={(e) => {
                setShowNotAnyOfCompany(!showNotAnyOfCompany);
              }}
            >
              Is not any of
            </Checkbox>
            {showNotAnyOfCompany && (
              <Form.Item label="" name="companyFinderNotAny">
                <Controller
                  render={({ field }) => (
                    <AutoComplete
                      style={{ width: '250px' }}
                      {...field}
                      options={companyListPeople.map((option) => ({
                        value: option.id,
                        label: (
                          <div className="grid p-2">
                            <div className="flex justify-between">
                              <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                {option.name}
                                <br />
                                <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                  {option.domain || '-'}
                                </span>
                              </span>
                              <img
                                className="absolute right-3"
                                src={
                                  option?.logo_url ? `${option?.logo_url}` : ''
                                }
                                width={50}
                                height={50}
                                alt="Logo"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = logoZielo;
                                  e.target.alt = 'Logo';
                                }}
                              />
                            </div>
                          </div>
                        ),
                      }))}
                      onSearch={(value) => {
                        setValue('companyFinderNotAny', value);
                        setValue('companyFinderNotAnySelect', null);
                        setValue('companyFinderNotAnyId', null);
                      }}
                      onSelect={async (selectedCompanyId) => {
                        const selectedCompany = companyListPeople.find(
                          (ao) => ao.id == selectedCompanyId
                        );
                        setValue('companyFinderNotAny', selectedCompany.name);
                        setValue('companyFinderNotAnySelect', [
                          selectedCompanyId,
                        ]);
                        setValue('companyFinderNotAnyId', [selectedCompanyId]);
                      }}
                    >
                      <Input />
                    </AutoComplete>
                  )}
                  name="companyFinderNotAny"
                  control={control}
                />
              </Form.Item>
            )}
          </div>
          <div>
            <Checkbox
              onChange={(e) => {
                setShowIncludeCompany(!showIncludeCompany);
              }}
            >
              Include past company
            </Checkbox>
            {showIncludeCompany && (
              <Form.Item label="" name="companyFinderInclude">
                <Controller
                  render={({ field }) => (
                    <AutoComplete
                      style={{ width: '250px' }}
                      {...field}
                      options={companyListPeople.map((option) => ({
                        value: option.id,
                        label: (
                          <div className="grid p-2">
                            <div className="flex justify-between">
                              <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                {option.name}
                                <br />
                                <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                  {option.domain || '-'}
                                </span>
                              </span>
                              <img
                                className="absolute right-3"
                                src={
                                  option?.logo_url ? `${option?.logo_url}` : ''
                                }
                                width={50}
                                height={50}
                                alt="Logo"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.src = logoZielo;
                                  e.target.alt = 'Logo';
                                }}
                              />
                            </div>
                          </div>
                        ),
                      }))}
                      onSearch={(value) => {
                        setValue('companyFinderInclude', value);
                        setValue('companyFinderIncludeSelect', null);
                        setValue('companyFinderIncludeId', null);
                      }}
                      onSelect={async (selectedCompanyId) => {
                        const selectedCompany = companyListPeople.find(
                          (ao) => ao.id == selectedCompanyId
                        );
                        setValue('companyFinderInclude', selectedCompany.name);
                        setValue('companyFinderIncludeSelect', [
                          selectedCompanyId,
                        ]);
                        setValue('companyFinderIncludeId', [selectedCompanyId]);
                      }}
                    >
                      <Input />
                    </AutoComplete>
                  )}
                  name="companyFinderInclude"
                  control={control}
                />
              </Form.Item>
            )}
          </div>
        </div>
      ),
    },
    {
      key: '2',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faLocationDot}
              style={{ marginRight: '10px' }}
            />{' '}
            Location
          </span>
          {watch('locationFinder') && watch('locationFinder')?.length > 0 && (
            <Row>
              {watch('locationFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Location Name" name="locationFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('locationFinderText') === '' ||
                    !watch('locationFinderText')
                    ? locationListPeople.map((so) => ({
                      ...so,
                      label: so.cleaned_name,
                      value: so.id,
                    }))
                    : [
                      {
                        label: watch('locationFinderText'),
                        value: watch('locationFinderText'),
                      },
                      ...locationListPeople.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      })),
                    ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon icon={faMedal} style={{ marginRight: '10px' }} />
            Title
          </span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      showArrow: false,
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                      ...so,
                      label: so.cleaned_name,
                      value: so.id,
                    }))
                    : [
                      {
                        label: watch('titleFinderText'),
                        value: watch('titleFinderText'),
                      },
                      ...titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      })),
                    ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faUserGroup}
              style={{ marginRight: '10px' }}
            />
            Employees
          </span>
          {watch('employeeFinder') && watch('employeeFinder')?.length > 0 && (
            <Row>
              {watch('employeeFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      showArrow: false,
      children: (
        <Form.Item label="Predefined Range" name="employeeFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (employeeList.length == 0) {
                    setValue('employeeFinderText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={employeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeeFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faIndustry}
              style={{ marginRight: '10px' }}
            />
            Industry & Keywords
          </span>
          {watch('industryFinder') && watch('industryFinder')?.length > 0 && (
            <Row>
              <span className="text-xs my-auto mr-2">Industry: </span>
              {watch('industryFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      showArrow: false,
      children: (
        <>
          <Form.Item label="Industry" name="industryFinder">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryFinderText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={
                    watch('industryFinderText') === '' ||
                      !watch('industryFinderText')
                      ? industryList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                      : [
                        {
                          label: watch('industryFinderText'),
                          value: watch('industryFinderText'),
                        },
                        ...industryList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                  }
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryFinder"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordPeople}
            onChange={(e) => {
              setShowIncludeKeywordPeople(e.target.checked);
              setValue('includeKeywordPeople', []);
              setValue('includeKeywordPeopleText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordPeople && (
            <Form.Item name="includeKeywordPeople">
              <Controller
                name="includeKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordPeopleText'),
                        label: watch('includeKeywordPeopleText'),
                        value: watch('includeKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordPeople}
            onChange={(e) => {
              setShowIncludeAllKeywordPeople(e.target.checked);
              setValue('includeAllKeywordPeople', []);
              setValue('includeAllKeywordPeopleText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordPeople && (
            <Form.Item name="includeAllKeywordPeople">
              <Controller
                name="includeAllKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordPeopleText'),
                        label: watch('includeAllKeywordPeopleText'),
                        value: watch('includeAllKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsPeople}
            onChange={(e) => {
              setShowExcludeKeywordsPeople(e.target.checked);
              setValue('excludeKeywordsPeople', []);
              setValue('excludeKeywordsPeopleText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsPeople && (
            <Form.Item name="excludeKeywordsPeople">
              <Controller
                name="excludeKeywordsPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsPeopleText'),
                        label: watch('excludeKeywordsPeopleText'),
                        value: watch('excludeKeywordsPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '9',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faChartLine}
              style={{ marginRight: '10px' }}
            />
            Buying Intent
          </span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '10',
      showArrow: false,
      label: (
        <>
          <Flex>
            <span>
              <FontAwesomeIcon
                icon={faTowerBroadcast}
                style={{ marginRight: '10px' }}
              />
              Signals
            </span>
            {watch('searchSignalIds') &&
              watch('searchSignalIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {watch('searchSignalIds')?.length}
                </Tag>
              )}
          </Flex>
        </>
      ),
      children: (
        <>
          <Form.Item name="searchSignalIds">
            <Controller
              name="searchSignalIds"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {listSignals?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      {option.modality === 'people' ? (
                        <Tag
                          className="flex items-center justify-center"
                          color="processing"
                          icon={<FaRegUser />}
                        />
                      ) : (
                        <Tag
                          className="flex items-center justify-center"
                          color="success"
                          icon={<FaRegBuilding />}
                        />
                      )}
                    </Flex>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '11',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faArrowsToDot}
              style={{ marginRight: '10px' }}
            />
            Score
          </span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '12',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMicrochip}
              style={{ marginRight: '10px' }}
            />
            Technologies
          </span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '13',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faDollarSign}
              style={{ marginRight: '10px' }}
            />
            Revenue
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '14',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMoneyBills}
              style={{ marginRight: '10px' }}
            />
            Funding
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '15',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faImagePortrait}
              style={{ marginRight: '10px' }}
            />
            Name
          </span>
          {watch('nameFinderText') !== '' && watch('nameFinderText') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('nameFinderText')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Name" name="nameFinder">
          <Controller
            render={({ field }) => (
              <Input
                placeholder="name"
                onChange={(e) => setValue('nameFinderText', e.target.value)}
              />
            )}
            name="nameFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '16',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBriefcase}
              style={{ marginRight: '10px' }}
            />
            Job Postings
          </span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const filterCompany = [
    {
      key: '1',
      label: (
        <div className="inline-grid">
          <span>Company</span>
          {watch('companyFindCompanyId') !== '' &&
            watch('companyFindCompanyId') && (
              <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                {watch('companyFindCompany')}
              </span>
            )}
        </div>
      ),
      children: (
        <div>
          <Form.Item label="Company" name="companyFindCompany">
            <Controller
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={companyList.map((option) => ({
                    value: option.id,
                    label: (
                      <div className="grid p-2">
                        <div className="flex justify-between">
                          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                            {option.name}
                            <br />
                            <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                              {option.domain || '-'}
                            </span>
                          </span>
                          <img
                            className="absolute right-3"
                            src={option?.logo_url ? `${option?.logo_url}` : ''}
                            width={50}
                            height={50}
                            alt="Logo"
                          />
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={(value) => {
                    setValue('companyFindCompany', value);
                    setValue('companyFindCompanySelect', null);
                    setValue('companyFindCompanyId', null);
                  }}
                  onSelect={async (selectedCompanyId) => {
                    const selectedCompany = companyList.find(
                      (ao) => ao.id == selectedCompanyId
                    );
                    setValue('companyFindCompany', selectedCompany.name);
                    setValue('companyFindCompanySelect', selectedCompanyId);
                    setValue('companyFindCompanyId', selectedCompanyId);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div className="inline-grid">
          <span>Account Location</span>
          {watch('locationFindCompany') &&
            watch('locationFindCompany')?.length > 0 && (
              <Row>
                {watch('locationFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Account Location" name="locationFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFindCompanyText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={locationListCompany.map((so) => ({
                  ...so,
                  label: so.cleaned_name,
                  value: so.id,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      label: (
        <div className="inline-grid">
          <span>Employees</span>
          {watch('employeesFindCompany') &&
            watch('employeesFindCompany')?.length > 0 && (
              <Row>
                {watch('employeesFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Employees" name="employeesFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (employeeList.length == 0) {
                    setValue('employeesFindCompanyText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={employeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeesFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      label: (
        <div className="inline-grid">
          <span>Industry & Keyword</span>
          {watch('industryKeywordCompany') &&
            watch('industryKeywordCompany')?.length > 0 && (
              <Row>
                <span className="text-xs my-auto mr-2">Industry: </span>
                {watch('industryKeywordCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryKeywordCompany">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryKeywordCompanyText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={industryListCompany.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryKeywordCompany"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordCompany}
            onChange={(e) => {
              setShowIncludeKeywordCompany(e.target.checked);
              setValue('includeKeywordCompany', []);
              setValue('includeKeywordCompanyText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordCompany && (
            <Form.Item name="includeKeywordCompany">
              <Controller
                name="includeKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordCompanyText'),
                        label: watch('includeKeywordCompanyText'),
                        value: watch('includeKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordCompany}
            onChange={(e) => {
              setShowIncludeAllKeywordCompany(e.target.checked);
              setValue('includeAllKeywordCompany', []);
              setValue('includeAllKeywordCompanyText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordCompany && (
            <Form.Item name="includeAllKeywordCompany">
              <Controller
                name="includeAllKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordCompanyText'),
                        label: watch('includeAllKeywordCompanyText'),
                        value: watch('includeAllKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsCompany}
            onChange={(e) => {
              setShowExcludeKeywordsCompany(e.target.checked);
              setValue('excludeKeywordsCompany', []);
              setValue('excludeKeywordsCompanyText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsCompany && (
            <Form.Item name="excludeKeywordsCompany">
              <Controller
                name="excludeKeywordsCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsCompanyText'),
                        label: watch('excludeKeywordsCompanyText'),
                        value: watch('excludeKeywordsCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
  ];

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  // Saved searches
  const [loadingGetSavedSearchData, setLoadingGetSavedSearchData] = useState(
    []
  );
  const [searchNameToSave, setSearchNameToSave] = useState();
  const [idSearchNameToSave, setIdSearchNameToSave] = useState();
  const [idSearchNameToDelete, setIdSearchNameToDelete] = useState();
  const [listSavedDataDefault, setListSavedDataDefault] = useState([]);
  const [listSavedData, setListSavedData] = useState([]);
  const [openEditSavedSearch, setOpenEditSavedSearch] = useState(false);
  const [loadingEditSearchName, setLoadingEditSearchName] = useState(false);
  const [savedCompany, setSavedCompany] = useState();
  const [loadingSaveAsASequence, setLoadingSaveAsASequence] = useState(false);
  const [openSavedSearch, setOpenSavedSearch] = useState(false);
  const [loadingSavedSearch, setLoadingSavedSearch] = useState(false);

  const handleGetListSavedContacts = async () => {
    setLoadingGetSavedSearchData(true);
    const { data } = await getSavedSearch('SAVED_CONTACT_SEARCHES_V1');
    setListSavedData(data?.result?.data);
    setListSavedDataDefault(data?.result?.data);
    setLoadingGetSavedSearchData(false);
  };

  useEffect(() => {
    setListSavedData([]);
    setListSavedDataDefault([]);
    handleGetListSavedContacts();
    // setFilter(initialFilter);
  }, []);

  const handleEditSavedSearch = async (e) => {
    const payload = {
      searchName: searchNameToSave,
    };
    setLoadingEditSearchName(true);

    const data = await updateSavedSearch(idSearchNameToSave, payload);
    await handleGetListSavedContacts();
    setLoadingEditSearchName(false);
    setOpenEditSavedSearch(false);
    notification.success({
      message: 'Edit saved search successfully !!!',
    });
  };

  const handleSaveSearch = async (searchName, cursor) => {
    let filterFields = {
      // Search terms
      searchPeople: watch('searchPeople'),

      // Company filters
      companyFinder: watch('companyFinder'),
      companyFinderSelect: watch('companyFinderSelect'),
      companyFinderId: watch('companyFinderId'),
      companyFinderNotAny: watch('companyFinderNotAny'),
      companyFinderNotAnySelect: watch('companyFinderNotAnySelect'),
      companyFinderNotAnyId: watch('companyFinderNotAnyId'),
      companyFinderInclude: watch('companyFinderInclude'),
      companyFinderIncludeSelect: watch('companyFinderIncludeSelect'),
      companyFinderIncludeId: watch('companyFinderIncludeId'),

      // Location filters
      locationFinder: watch('locationFinder'),
      locationFinderText: watch('locationFinderText'),

      // Title filters
      titleFinder: watch('titleFinder'),
      titleFinderText: watch('titleFinderText'),

      // Employee filters
      employeeFinder: watch('employeeFinder'),
      employeeFinderText: watch('employeeFinderText'),

      // Industry filters
      industryFinder: watch('industryFinder'),
      industryFinderText: watch('industryFinderText'),

      // Keywords filters
      includeKeywordPeople: watch('includeKeywordPeople'),
      includeKeywordPeopleText: watch('includeKeywordPeopleText'),
      includeAllKeywordPeople: watch('includeAllKeywordPeople'),
      includeAllKeywordPeopleText: watch('includeAllKeywordPeopleText'),
      excludeKeywordsPeople: watch('excludeKeywordsPeople'),
      excludeKeywordsPeopleText: watch('excludeKeywordsPeopleText'),
      
      // Email status filters
      contactEmailStatus: watch('contactEmailStatus'),
      contactEmailOpened: watch('contactEmailOpened'),
      contactEmailOpenedTime: watch('contactEmailOpenedTime'),
      contactEmailOpenedTimeMin: watch('contactEmailOpenedTimeMin'),
      contactEmailOpenedTimeMax: watch('contactEmailOpenedTimeMax'),
      
      // Buying intent filters
      contactBuyingIntentScore: watch('contactBuyingIntentScore'),
      contactBuyingIntentIds: watch('contactBuyingIntentIds'),
      
      // Signals filters
      searchSignalIds: watch('searchSignalIds'),
      
      // Score filters
      contactMinimumScore: watch('contactMinimumScore'),
      
      // Technologies filters
      listTechnologies: watch('listTechnologies'),
      
      // Revenue filters
      revenueStatus: watch('revenueStatus'),
      revenueStatusItem: watch('revenueStatusItem'),
      
      // Funding filters
      fundingStatusItem: watch('fundingStatusItem'),
      fundingMin: watch('fundingMin'),
      fundingMax: watch('fundingMax'),
      
      // Name filter
      nameFinderText: watch('nameFinderText'),
      
      // Job posting filters
      currentlyHiring: watch('currentlyHiring'),
      listCurrentlyHiring: watch('listCurrentlyHiring'),
      contactJobLocated: watch('contactJobLocated'),
      organizationNumJobsRangeMin: watch('organizationNumJobsRangeMin'),
      organizationNumJobsRangeMax: watch('organizationNumJobsRangeMax'),
      organizationJobPostedAtRangeMin: watch('organizationJobPostedAtRangeMin'),
      organizationJobPostedAtRangeMax: watch('organizationJobPostedAtRangeMax'),
      
      // Accordion state
      keyAcordionPeople: watch('keyAcordionPeople'),
    };

    const payload = {
      searchName: searchName,
      searchType: 'SAVED_CONTACT_SEARCHES_V1',
      filterFields: filterFields,
    };

    const data = await createSavedSearch(payload);
    notification.success({
      message: 'Create Saved search success !!!',
    });
    handleGetListSavedContacts();
    return data;
  };

  const functionSaveSearch = async () => {
    setLoadingSavedSearch(true);
    const data = await handleSaveSearch(searchNameToSave);
    setLoadingSavedSearch(false);
    setOpenSavedSearch(false);
  };

  const handleLoadSavedSearch = (filterFields) => {
    // Set all filter values using setValue
    if (filterFields) {
      Object.keys(filterFields).forEach(key => {
        if (filterFields[key] !== undefined && filterFields[key] !== null) {
          setValue(key, filterFields[key]);
        }
      });
    }
  };

  const filterColumnCompany = () => {
    return (
      <div className="w-full flex flex-col gap-2 max-h-[70vh] overflow-y-auto">
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['keywords']}
          items={[
            {
              key: 'keywords',
              label: (
                <div className="flex items-center gap-2">
                  <FileSearchOutlined className="text-lg" />
                  <span>Saved Searches</span>(
                  {loadingGetSavedSearchData ? (
                    <Spin size="small"></Spin>
                  ) : (
                    listSavedData.length
                  )}
                  )
                </div>
              ),
              children: (
                <div>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Type here to search ..."
                      onChange={(e) => {
                        const searchText = e.target.value;
                        const newOptions =
                          listSavedDataDefault?.filter((v) =>
                            v?.searchName
                              ?.toString()
                              .toLocaleLowerCase()
                              .includes(searchText.toLocaleLowerCase())
                          ) ?? [];
                        setListSavedData(newOptions);
                      }}
                    />
                  </div>
                  <div
                    style={{
                      maxHeight: '200px',
                      overflowY: 'scroll',
                    }}
                  >
                    {listSavedData?.map((item) => (
                      <div
                        style={{
                          cursor: 'pointer',
                          width: '100%',
                          height: '30px',
                          display: 'flex',
                          alignItems: 'center',
                          marginTop: '10px',
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <Dropdown
                            rootClassName="font-Montserrat"
                            menu={{
                              items: [
                                {
                                  label: 'Edit Name',
                                  key: 'edit_name',
                                  icon: <CopyOutlined />,
                                },
                                {
                                  label: 'Delete',
                                  key: 'delete',
                                  icon: <DeleteOutlined />,
                                },
                              ],
                              onClick: (e) => {
                                // e?.stopPropagation();
                                if (e.key === 'edit_name') {
                                  setSearchNameToSave(item?.searchName);
                                  setOpenEditSavedSearch(true);
                                  setIdSearchNameToSave(item?.id);
                                }

                                if (e.key === 'delete') {
                                  setIdSearchNameToDelete(item?.id);
                                  handleDeleteSavedSearch(item?.id);
                                }
                              },
                            }}
                            disabled={loadingSaveAsASequence}
                          >
                            <MoreOutlined
                              style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                              }}
                            />
                          </Dropdown>
                          <UserOutlined style={{ marginLeft: '10px' }} />
                          <span
                            onClick={() => {
                              handleLoadSavedSearch(item?.filterFields);
                            }}
                            style={{ marginLeft: '10px' }}
                          >
                            {item?.searchName}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['company-name']}
          items={[
            {
              key: 'company-name',
              label: (
                <div className="inline-grid">
                  <span>Company</span>
                  {watch('companyFindCompanyId') !== '' &&
                    watch('companyFindCompanyId') && (
                      <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                        {watch('companyFindCompany')}
                      </span>
                    )}
                </div>
              ),
              children: (
                <div>
                  <Form.Item label="Company" name="companyFindCompany">
                    <Controller
                      render={({ field }) => (
                        <AutoComplete
                          style={{ width: '250px' }}
                          {...field}
                          options={companyList.map((option) => ({
                            value: option.id,
                            label: (
                              <div className="grid p-2">
                                <div className="flex justify-between">
                                  <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                    {option.name}
                                    <br />
                                    <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                      {option.domain || '-'}
                                    </span>
                                  </span>
                                  <img
                                    className="absolute right-3"
                                    src={
                                      option?.logo_url
                                        ? `${option?.logo_url}`
                                        : ''
                                    }
                                    width={50}
                                    height={50}
                                    alt="Logo"
                                  />
                                </div>
                              </div>
                            ),
                          }))}
                          onSearch={(value) => {
                            setValue('companyFindCompany', value);
                            setValue('companyFindCompanySelect', null);
                            setValue('companyFindCompanyId', null);
                          }}
                          onSelect={async (selectedCompanyId) => {
                            const selectedCompany = companyList.find(
                              (ao) => ao.id == selectedCompanyId
                            );
                            setValue(
                              'companyFindCompany',
                              selectedCompany.name
                            );
                            setValue(
                              'companyFindCompanySelect',
                              selectedCompanyId
                            );
                            setValue('companyFindCompanyId', selectedCompanyId);
                          }}
                        >
                          <Input />
                        </AutoComplete>
                      )}
                      name="companyFindCompany"
                      control={control}
                    />
                  </Form.Item>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['account-location']}
          items={[
            {
              key: 'account-location',
              label: (
                <div className="inline-grid">
                  <span>Account Location</span>
                  {watch('locationFindCompany') &&
                    watch('locationFindCompany')?.length > 0 && (
                      <Row>
                        {watch('locationFindCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Account Location" name="locationFindCompany">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          setValue('locationFindCompanyText', searchText);
                        }}
                        {...field}
                        notFoundContent={null}
                        options={locationListCompany.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="locationFindCompany"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['employees']}
          items={[
            {
              key: 'employees',
              label: (
                <div className="inline-grid">
                  <span>Employees</span>
                  {watch('employeesFindCompany') &&
                    watch('employeesFindCompany')?.length > 0 && (
                      <Row>
                        {watch('employeesFindCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Employees" name="employeesFindCompany">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          if (employeeList.length == 0) {
                            setValue('employeesFindCompanyText', searchText);
                          }
                        }}
                        {...field}
                        notFoundContent={null}
                        options={employeeList.map((so) => ({
                          ...so,
                          label: so.display_name,
                          value: so.value,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="employeesFindCompany"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['industry-keyword']}
          items={[
            {
              key: 'industry-keyword',
              label: (
                <div className="inline-grid">
                  <span>Industry & Keyword</span>
                  {watch('industryKeywordCompany') &&
                    watch('industryKeywordCompany')?.length > 0 && (
                      <Row>
                        <span className="text-xs my-auto mr-2">Industry: </span>
                        {watch('industryKeywordCompany').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <>
                  <Form.Item label="Industry" name="industryKeywordCompany">
                    <Controller
                      render={({ field }) => (
                        <Select
                          labelInValue
                          mode="multiple"
                          onSearch={(searchText) => {
                            setValue('industryKeywordCompanyText', searchText);
                          }}
                          {...field}
                          notFoundContent={null}
                          options={industryListCompany.map((so) => ({
                            ...so,
                            label: so.cleaned_name,
                            value: so.id,
                          }))}
                          filterOption={(inputValue, option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) !== -1
                          }
                        />
                      )}
                      name="industryKeywordCompany"
                      control={control}
                    />
                  </Form.Item>
                  <Checkbox
                    className="w-full"
                    checked={showIncludeKeywordCompany}
                    onChange={(e) => {
                      setShowIncludeKeywordCompany(e.target.checked);
                      setValue('includeKeywordCompany', []);
                      setValue('includeKeywordCompanyText', '');
                    }}
                  >
                    Include Keywords
                  </Checkbox>
                  {showIncludeKeywordCompany && (
                    <Form.Item name="includeKeywordCompany">
                      <Controller
                        name="includeKeywordCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('includeKeywordCompanyText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeKeywordCompanyText'),
                                label: watch('includeKeywordCompanyText'),
                                value: watch('includeKeywordCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showIncludeAllKeywordCompany}
                    onChange={(e) => {
                      setShowIncludeAllKeywordCompany(e.target.checked);
                      setValue('includeAllKeywordCompany', []);
                      setValue('includeAllKeywordCompanyText', '');
                    }}
                  >
                    Include ALL
                  </Checkbox>
                  {showIncludeAllKeywordCompany && (
                    <Form.Item name="includeAllKeywordCompany">
                      <Controller
                        name="includeAllKeywordCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue(
                                'includeAllKeywordCompanyText',
                                inputValue
                              );
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeAllKeywordCompanyText'),
                                label: watch('includeAllKeywordCompanyText'),
                                value: watch('includeAllKeywordCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showExcludeKeywordsCompany}
                    onChange={(e) => {
                      setShowExcludeKeywordsCompany(e.target.checked);
                      setValue('excludeKeywordsCompany', []);
                      setValue('excludeKeywordsCompanyText', '');
                    }}
                  >
                    Exclude Keywords
                  </Checkbox>
                  {showExcludeKeywordsCompany && (
                    <Form.Item name="excludeKeywordsCompany">
                      <Controller
                        name="excludeKeywordsCompany"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue(
                                'excludeKeywordsCompanyText',
                                inputValue
                              );
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('excludeKeywordsCompanyText'),
                                label: watch('excludeKeywordsCompanyText'),
                                value: watch('excludeKeywordsCompanyText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                </>
              ),
            },
          ]}
        />
      </div>
    );
  };

  const columnsCompany = [
    {
      title: 'Company',
      dataIndex: 'name',
      key: 'name',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              className=""
              src={record?.logo_url ? `${record?.logo_url}` : ''}
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async () => {
                  setIsDetailEmployeeCompany(true);
                  const { data } = await getListEmployee({
                    organizationId: record.id,
                    page: 1,
                  });
                  if (data.result.people.length === 0)
                    return notification.error({ message: 'Data Not Found' });
                  setValue('keyAcordionPeople', '6');
                  setValue('companyFinderId', record.id);
                  setListEmployeeCompany(data?.result?.people);
                  setListEmployeePaginationCompany(data?.result?.pagination);
                  setIsLoadingEmployee(false);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={() => {
                    handleGotoWebsite(record?.website_url);
                  }}
                  className="cursor-pointer text-gray-600"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={() => {
                    handleGotoWebsite(record?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={() => {
                    handleGotoWebsite(record?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={() => {
                    handleGotoWebsite(record?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: 'Employees',
      dataIndex: 'estimated_num_employees',
      key: 'estimated_num_employees',
      render: (allowRead, record) => (
        <Row gutter={16} className="w-[5rem]">
          <p>{record?.estimated_num_employees}</p>
        </Row>
      ),
    },
    {
      title: 'Industry',
      dataIndex: 'industries',
      key: 'industries',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p className="whitespace-nowrap max-w-lg">
            {record?.industries.join(', ')}
          </p>
        </Row>
      ),
    },
    {
      title: 'Keywords',
      dataIndex: 'keywords',
      key: 'keywords',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p className="whitespace-break max-w-lg">
            {record?.keywords.join(', ')}
          </p>
        </Row>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listSelectSection, setListSelectSection] = useState([]);

  const handleClickSignal = (id) => {
    if (!selectedRowKeys.includes(id)) {
      setSelectedRowKeys([...selectedRowKeys, id]);
      const dataCheck = listEmployee.find((item) => item.id == id);
      setEmployeesChecked([...employeesChecked, dataCheck]);
    } else {
      setSelectedRowKeys(selectedRowKeys.filter((item) => item !== id));
      setEmployeesChecked(employeesChecked.filter((item) => item.id !== id));
    }
  };

  const renderOption = (option) => {
    const data = option?.data || null;
    if (!data) return <div>{option?.label}</div>;

    return data?.value?.includes('linkedin') ? (
      <div className="flex items-start flex-col">
        <span className="flex items-center gap-1">
          <LinkedinOutlined className="text-[#0288d1]" />
          <span className="italic">{`${option?.label}`}</span>
        </span>
        <span className="text-xs italic text-gray-600">
          {handleRenderTime(data?.createdAt)}
        </span>
      </div>
    ) : (
      <div className="flex items-start flex-col">
        <span>{option?.label}</span>
        <span className="text-xs italic text-gray-600">
          {handleRenderTime(data?.createdAt)}
        </span>
      </div>
    );
  };

  const handleCheckAll = () => {
    if (!listSelectSection.includes(listEmployeePagination?.page)) {
      setListSelectSection([
        ...listSelectSection,
        listEmployeePagination?.page,
      ]);
      const data = listEmployee.map((item) => item.id);
      setSelectedRowKeys([...new Set([...selectedRowKeys, ...data])]);
      setEmployeesChecked([...employeesChecked, ...listEmployee]);
    } else {
      const data = listEmployee.map((item) => item.id);
      const updatedArray = selectedRowKeys.filter((id) => !data.includes(id));
      const updatedData = employeesChecked.filter(
        (item) => !data.includes(item.id)
      );
      setEmployeesChecked(updatedData);
      setSelectedRowKeys(updatedArray);
      setListSelectSection(
        listSelectSection.filter(
          (item) => item !== listEmployeePagination?.page
        )
      );
    }
  };

  const columnsPeople = [
    {
      title: (
        <div style={{ width: '50px' }}>
          <Checkbox
            disabled={listEmployee?.length == 0}
            checked={listSelectSection?.includes(listEmployeePagination?.page)}
            onChange={() => handleCheckAll()}
            indeterminate={
              selectedRowKeys.length > 0 &&
              selectedRowKeys.length < listEmployee.length &&
              listEmployee
                .map((item) => item.id)
                .filter((element) => selectedRowKeys.includes(element)).length >
              0
            }
          ></Checkbox>
        </div>
      ),
      dataIndex: 'checkbox',
      key: 'checkbox',
      fixed: 'left',
      width: '50px',
      render: (allowWrite, record) => (
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <>
            <Checkbox
              checked={selectedRowKeys.includes(record.id)}
              onChange={(e) => {
                e.stopPropagation();
                handleClickSignal(record?.id);
              }}
            ></Checkbox>
          </>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.FULL_NAME,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '150px',
      render: (allowWrite, record) => {
        const nameOptions = enrichData?.find(
          (item) =>
            item.linkedin_url === record.linkedin_url ||
            record.id === item.record_id
        );
        return (
          <>
            <Row gutter={16} className="full-name-cell-container">
              {record?.isRecommended && (
                <button
                  title="Contact was audited by Zileo staff."
                  class="button ant-btn-primary text-xs font-medium"
                >
                  Recommended <StarOutlined />
                </button>
              )}
              <p
                className="font-semibold mr-2"
                style={nameOptions ? { color: '#1677ff' } : {}}
              >
                {nameOptions?.person?.trim()
                  ? nameOptions?.person
                  : record?.name}
              </p>
              {record?.linkedin_url && (
                <Row gutter={16}>
                  <Col>
                    <LinkedinOutlined
                      onClick={(e) => {
                        e.stopPropagation();
                        handleGotoWebsite(record?.linkedin_url);
                      }}
                      title={`Access to linkedin account`}
                      className="cursor-pointer text-[#0288d1]"
                    />
                  </Col>
                </Row>
              )}
            </Row>
            {nameOptions && (
              <Tag
                style={{ marginLeft: '-10px', marginTop: '5px' }}
                color="processing"
              >
                Enriched
              </Tag>
            )}
          </>
        );
      },
    },
    {
      title: COMMON_STRINGS.JOB_TITLE,
      dataIndex: 'title',
      key: 'title',
      width: '400px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p>{record?.title}</p>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'organization.name',
      key: 'organization.name',
      width: '300px',
      render: (allowRead, record) => {
        const nameOptions = enrichData?.find(
          (item) =>
            item.linkedin_url === record.linkedin_url ||
            record.id === item.record_id
        );
        return (
          <Row gutter={16}>
            <Col>
              <img
                onClick={async (e) => {
                  e.stopPropagation();
                  handleDetailCompany(record?.organization);
                }}
                src={
                  record.organization?.logo_url
                    ? `${record.organization?.logo_url}`
                    : ''
                }
                width={50}
                height={50}
                alt="Logo"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = logoNoCompany;
                  e.target.alt = 'Logo';
                }}
              />
            </Col>
            <Col>
              <Row>
                <p
                  onClick={async (e) => {
                    e.stopPropagation();
                    handleDetailCompany(record?.organization);
                  }}
                  className="font-semibold cursor-pointer hover:text-blue-700"
                  style={
                    nameOptions?.company &&
                      nameOptions?.company !== record?.organization?.name
                      ? { color: '#1677ff' }
                      : {}
                  }
                >
                  {nameOptions?.company || record?.organization?.name}
                </p>
              </Row>
              <Row className="flex gap-2">
                <Col>
                  <LinkOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.organization?.website_url);
                    }}
                    className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.organization?.linkedin_url);
                    }}
                    className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <FacebookOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.organization?.facebook_url);
                    }}
                    className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                  />
                </Col>
                <Col>
                  <TwitterOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.organization?.twitter_url);
                    }}
                    className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                  />
                </Col>
              </Row>
            </Col>
          </Row>
        );
      },
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'left',
      width: '300px',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          {record?.email &&
            record?.email !== '<EMAIL>' ? (
            <>{handleGenderSupportBar(record, '', record)}</>
          ) : listDetailEmployee.some(
            (item) =>
              item.person_id === record.id ||
              item.person_id === record.person_id
          ) ? (
            listDetailEmployee
              .filter(
                (item) =>
                  item.person_id === record.id ||
                  item.person_id === record.person_id
              )
              .map((item, index) => (
                <>{handleGenderSupportBar(item, record?.name, record)}</>
              ))
          ) : (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleGetDetailEmployee(record.id || record.person_id);
              }}
              type="primary"
              icon={<MailOutlined />}
            >
              Access Email
            </Button>
          )}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone',
      key: 'phone',
      width: '150px',
      align: 'left',
      render: (allowRead, record) => (
        <div className="flex gap-2" style={{ color: 'blue' }}>
          <a
            onClick={(e) => {
              e.stopPropagation();
            }}
            href={`tel:${record?.phone_numbers?.[0]?.sanitized_number}`}
          >
            {record?.phone_numbers?.[0]?.sanitized_number}
          </a>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.CONTACT_LOCATION,
      dataIndex: 'contact-location',
      key: 'contact-location',
      align: 'left',
      width: '300px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{
            fontWeight: '600',
          }}
        >
          {record?.present_raw_address ||
            (record?.city ?? '') +
            (record?.city ? ', ' : '') +
            (record?.country ?? '')}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'employees',
      key: 'employees',
      width: '200px',
      align: 'left',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <Button
            type="text"
            className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
            onClick={async (e) => {
              e.stopPropagation();
              handleDetailCompany(record?.organization);
            }}
          >
            <UserGroupIcon className="w-4 h-4" />
            {items?.estimated_num_employees}
            <span>Employees</span>
          </Button>
        );
      },
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industry',
      key: 'industry',
      width: '300px',
      align: 'center',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={items?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {items?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < items.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      align: 'center',
      width: '300px',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: 600,
            }}
          >
            <Tooltip
              placement="topLeft"
              title={items?.keywords?.map((item, index) => (
                <span key={index}>
                  {item}
                  {index < items.keywords.length - 1 && ', '}
                </span>
              ))}
            >
              <span
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                }}
              >
                {items?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.keywords.length - 1 && ', '}
                  </span>
                ))}
              </span>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const itemsTableEmailFinder = [
    {
      key: '1',
      label: 'People',
      children: (
        <Row>
          <Col flex="300px">
            <Row gutter={16}>
              <Form
                className="w-full pr-4"
                layout="vertical"
                onFinish={handleSubmit(handleSubmitPeople)}
              >
                <Col className="w-full mr-4">
                  <Form.Item
                    label="Search"
                    name="searchPeople"
                    className="mb-2 mt-2"
                  >
                    <Controller
                      render={({ field }) => (
                        <Input
                          prefix={<SearchOutlined />}
                          {...field}
                          placeholder="Search People ..."
                          style={{
                            width: '280px',
                          }}
                        />
                      )}
                      name="searchPeople"
                      control={control}
                    />
                  </Form.Item>
                </Col>
              </Form>
              <Form
                className="w-full pr-4"
                layout="vertical"
                onFinish={handleSubmit(handleFindEmails)}
              >
                <Col className="w-full">
                  {/* <Form.Item label="Filter" className={'custom-collapse'}>
                    <Collapse
                      // expandIcon={<div>123123</div>}
                      accordion
                      items={filterAddContact}
                      onChange={handleAccordionChangePeople}
                    />
                  </Form.Item> */}
                </Col>
                <Col className="w-full mr-4">
                  <Button
                    type="primary"
                    disabled={isLoading}
                    loading={isLoading}
                    htmlType="submit"
                    className="bg-cyan-600 font-semibold text-white"
                    style={{
                      width: '100%',
                      height: '40px',
                      padding: '0 20px',
                    }}
                  >
                    <span>Search</span>
                  </Button>
                </Col>
              </Form>
            </Row>
          </Col>
          <div className="customTable">
            <Col flex="auto" className="w-2/3">
              <Table
                scroll={{
                  x: 1300,
                  y: '65vh',
                }}
                loading={isLoadingEmployee}
                pagination={false}
                columns={columnsPeople}
                dataSource={listEmployee}
                bordered
              />
              <Pagination
                className="mt-3"
                defaultCurrent={listEmployeePagination?.page}
                total={listEmployeePagination?.total_entries}
                showSizeChanger={false}
                defaultPageSize={25}
                onChange={handlePaginationListEmployee}
              />
            </Col>
          </div>
        </Row>
      ),
    },
    {
      key: '2',
      label: 'Companies',
      children: (
        <>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleSubmitCompanyFind)}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchCompany"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            prefix={<SearchOutlined />}
                            {...field}
                            placeholder="Search Company ..."
                          />
                        )}
                        name="searchCompany"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindCompany)}
                >
                  <Col className="w-full">
                    <div>{filterColumnCompany()}</div>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      <span>Search</span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            {isDetailEmployeCompany ? (
              <div className="customTable">
                <Col flex="auto" className="w-2/3">
                  <Table
                    scroll={{ x: 1300, y: '65vh' }}
                    loading={isLoadingEmployee}
                    pagination={false}
                    columns={columnsPeople}
                    bordered
                    dataSource={listEmployeeCompany}
                  />
                  <Pagination
                    className="mt-3"
                    defaultCurrent={listEmployeePaginationCompany.page}
                    total={listEmployeePaginationCompany.total_entries}
                    showSizeChanger={false}
                    defaultPageSize={25}
                    onChange={handlePaginationListEmployee}
                  />
                </Col>
              </div>
            ) : (
              <Col flex="auto" className="w-2/3">
                <div className="customTable">
                  <Table
                    scroll={{ y: '65vh' }}
                    loading={isLoadingCompanies}
                    pagination={false}
                    columns={columnsCompany}
                    dataSource={listCompanies}
                  />
                </div>
                <Pagination
                  className="mt-3"
                  defaultCurrent={listCompaniesPagination.page}
                  total={listCompaniesPagination.total_entries}
                  showSizeChanger={false}
                  defaultPageSize={25}
                  onChange={handlePaginationListCompany}
                />
              </Col>
            )}
          </Row>
        </>
      ),
    },
  ];

  useMemo(() => {
    if (getValues()?.joblocationcity)
      handleGetDefaultAddress(getValues()?.joblocationcity);
  }, [job, getValues()?.joblocationcity]);

  useEffect(() => {
    resetFormFindEmails();
    setValue('companyFinder', getValues().company);
    setValue('fullName', '');
  }, [isModalOpen]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinder'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinder')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinderInclude'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderInclude')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinderNotAny'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderNotAny')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFindCompany'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFindCompany')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFinderText(watch('locationFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFindCompanyText(watch('locationFindCompanyText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustry(watch('industryFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustryCompany(
        watch('industryKeywordCompanyText')
      );
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextTitle(watch('titleFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  useEffect(() => {
    const storedData = localStorage?.getItem('listIntentChoose');
    setDataParentIntent(storedData ? JSON.parse(storedData) : []);
    setListDataIntentSetting(storedData ? JSON.parse(storedData) : []);
  }, []);

  const { data: companyListPeople = [] } = useQuery(
    ['GET_LIST_COMPANY_PEOPLE', debouncedSearchTextCompanyPeople],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompanyPeople,
      });
      return data.organizations;
    },
    { enabled: !!debouncedSearchTextCompanyPeople }
  );

  const { data: companyList = [] } = useQuery(
    ['GET_LIST_COMPANY', debouncedSearchTextCompany],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompany,
      });
      return data.organizations;
    },
    { enabled: !!debouncedSearchTextCompany }
  );

  const { data: locationListPeople = [] } = useQuery(
    ['GET_LIST_LOCATION_PEOPLE', debouncedLocationFinderText],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText:
          debouncedLocationFinderText.trim() === ''
            ? 'a'
            : debouncedLocationFinderText,
        type: 'location',
      });
      return data.tags;
    },
    { enabled: !!debouncedLocationFinderText }
  );

  const { data: locationListCompany = [] } = useQuery(
    ['GET_LIST_LOCATION', debouncedLocationFindCompanyText],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedLocationFindCompanyText,
        type: 'location',
      });
      return data.tags;
    },
    { enabled: !!debouncedLocationFindCompanyText }
  );

  const { data: employeeList = [] } = useQuery(
    ['GET_LIST_EMPLOYEE'],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: '',
        type: 'employee',
      });
      return data.faceting.num_employees_facets;
    },
    { enabled: true }
  );

  const { data: industryList = [] } = useQuery(
    ['GET_LIST_INDUSTRY', debouncedSearchTextIndustry],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextIndustry,
        type: 'linkedin_industry',
      });
      return data.tags;
    },
    { enabled: !!debouncedSearchTextIndustry }
  );

  const { data: industryListCompany = [] } = useQuery(
    ['GET_LIST_INDUSTRYCompany', debouncedSearchTextIndustryCompany],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextIndustryCompany,
        type: 'linkedin_industry',
      });
      return data.tags;
    },
    { enabled: !!debouncedSearchTextIndustryCompany }
  );

  const { data: titleList = [] } = useQuery(
    ['GET_LIST_TITLE', debouncedSearchTextTitle],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextTitle,
        type: 'person_title',
      });
      return data.tags;
    },
    { enabled: !!debouncedSearchTextTitle }
  );

  const { data: listSignals, refetch: refetchListSignals } = useQuery(
    ['GET_LIST_SIGNALS'],
    async () => {
      const { data } = await employeeFinderSearchSignals();
      return data?.templates || [];
    },
    { enabled: false }
  );

  useEffect(() => {
    setIndustrySearchText(' ');
    setCategorySearchText(' ');
    setSkillSearchText(' ');
    functionContactClient.handleContactSearch('', '');
    setHandleCloseContact(false);
    setIsSearchCompany(false);
  }, []);

  // START DETAIL CONTACT
  const [showDetailContact, setShowDetailContact] = useState(false);
  const [detailContactStaff, setDetailContactStaff] = useState({});
  const [detailOrganization, setDetailOrganization] = useState({});
  const [showFullText, setShowFullText] = useState(false);
  const [currentHistoryWork, setCurrentHistoryWork] = useState([]);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const onSelectChange = (newSelectedRowKeys) => {
    // const newArr = [...new Set([...newSelectedRowKeys, ...selectedRowKeys])];
    console.log(newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleDetailContact = async (record) => {
    setShowDetailContact(true);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.organization?.id,
      });
      setDetailOrganization(data?.organization);
      setDetailContactStaff(record);

      const listHistoryCompany = record?.employment_history?.map(
        (item) => item?.organization_id
      );
      const dataHistory = await getOrganizationsSnippet({
        ids: listHistoryCompany,
      });

      setCurrentHistoryWork(dataHistory?.data?.organizations);
    }
  };

  // END DETAIL CONTACT

  // START DETAIL COMPANY
  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});
  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };
  // END DETAIL COMPANY

  const handleBulkEnrich = async () => {
    const enrichContacts = employeesChecked.map((record) => ({
      fullName: record?.name || '',
      recordId: record?.id,
      companyName: record?.organization_name,
      linkedInUrl: record?.linkedin_url,
    }));

    toggleLoading();
    const delay = parseInt(1 * TIME_ENRICH_EACH_FIELD, 10);
    var timeout = new Date().getTime() + delay;
    setEstimationDuration(timeout);

    const payload = {
      data: enrichContacts,
      listId: 'email-finder-uid',
      type: 'EMAIL_FINDER',
    };

    setEnrichingData([...enrichContacts]);

    await handleBulkEnrichContactData(payload);
  };

  const filterColumn = () => {
    return (
      <div className="w-full flex flex-col gap-2 max-h-[70vh] overflow-y-auto">
        <Modal
          title="Edit your Search name"
          open={openEditSavedSearch}
          footer={false}
          onCancel={() => setOpenEditSavedSearch(false)}
        >
          <Input
            value={searchNameToSave}
            onChange={(e) => setSearchNameToSave(e.target.value)}
            placeholder="Input your search name ..."
          />
          <Button
            loading={loadingEditSearchName}
            onClick={() => handleEditSavedSearch()}
            style={{ width: '100%', marginTop: '10px' }}
            type="primary"
          >
            Save
          </Button>
        </Modal>

        <Modal
          title="Save your Search"
          open={openSavedSearch}
          footer={false}
          onCancel={() => setOpenSavedSearch(false)}
        >
          <Input
            value={searchNameToSave}
            onChange={(e) => setSearchNameToSave(e.target.value)}
            placeholder="Input your search name ..."
          />
          <Button
            loading={loadingSavedSearch}
            onClick={() => functionSaveSearch(searchNameToSave)}
            style={{ width: '100%', marginTop: '10px' }}
            type="primary"
          >
            Save
          </Button>
        </Modal>

        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['keywords']}
          style={{
            width: '290px',
          }}
          items={[
            {
              key: 'keywords',
              label: (
                <div className="flex items-center gap-2">
                  <FileSearchOutlined className="text-lg" />
                  <span>Saved Searches</span>(
                  {loadingGetSavedSearchData ? (
                    <Spin size="small"></Spin>
                  ) : (
                    listSavedData.length
                  )}
                  )
                </div>
              ),
              children: (
                <div>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Type here to search ..."
                      onChange={(e) => {
                        const searchText = e.target.value;
                        const newOptions =
                          listSavedDataDefault?.filter((v) =>
                            v?.searchName
                              ?.toString()
                              .toLocaleLowerCase()
                              .includes(searchText.toLocaleLowerCase())
                          ) ?? [];
                        setListSavedData(newOptions);
                      }}
                    />
                  </div>
                  <div
                    style={{
                      maxHeight: '200px',
                      overflowY: 'scroll',
                    }}
                  >
                    {listSavedData?.map((item) => (
                      <div
                        style={{
                          cursor: 'pointer',
                          width: '100%',
                          height: '30px',
                          display: 'flex',
                          alignItems: 'center',
                          marginTop: '10px',
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <Dropdown
                            rootClassName="font-Montserrat"
                            menu={{
                              items: [
                                {
                                  label: 'Edit Name',
                                  key: 'edit_name',
                                  icon: <CopyOutlined />,
                                },
                                {
                                  label: 'Delete',
                                  key: 'delete',
                                  icon: <DeleteOutlined />,
                                },
                              ],
                              onClick: (e) => {
                                // e?.stopPropagation();
                                if (e.key === 'edit_name') {
                                  setSearchNameToSave(item?.searchName);
                                  setOpenEditSavedSearch(true);
                                  setIdSearchNameToSave(item?.id);
                                }

                                if (e.key === 'delete') {
                                  setIdSearchNameToDelete(item?.id);
                                  handleDeleteSavedSearch(item?.id);
                                }
                              },
                            }}
                            disabled={loadingSaveAsASequence}
                          >
                            <MoreOutlined
                              style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                              }}
                            />
                          </Dropdown>
                          <UserOutlined style={{ marginLeft: '10px' }} />
                          <span
                            onClick={() => {
                              handleLoadSavedSearch(item?.filterFields);
                            }}
                            style={{ marginLeft: '10px' }}
                          >
                            {item?.searchName}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="flex items-center gap-2">
                  <UserOutlined className="text-lg" />
                  <span>Company name</span>
                </div>
              ),
              children: (
                <div>
                  <div className="flex items-center gap-2">
                    <Form.Item label="" name="companyFinder">
                      <Controller
                        render={({ field }) => (
                          <AutoComplete
                            style={{ width: '250px' }}
                            {...field}
                            options={companyListPeople.map((option) => ({
                              value: option.id,
                              label: (
                                <div className="grid p-2">
                                  <div className="flex justify-between">
                                    <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                      {option.name}
                                      <br />
                                      <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                        {option.domain || '-'}
                                      </span>
                                    </span>
                                    <img
                                      className="absolute right-3"
                                      src={
                                        option?.logo_url
                                          ? `${option?.logo_url}`
                                          : ''
                                      }
                                      width={50}
                                      height={50}
                                      alt="Logo"
                                      onError={(e) => {
                                        e.target.onerror = null;
                                        e.target.src = logoZielo;
                                        e.target.alt = 'Logo';
                                      }}
                                    />
                                  </div>
                                </div>
                              ),
                            }))}
                            onSearch={(value) => {
                              setValue('companyFinder', value);
                              setValue('companyFinderSelect', null);
                              setValue('companyFinderId', null);
                            }}
                            onSelect={async (selectedCompanyId) => {
                              const selectedCompany = companyListPeople.find(
                                (ao) => ao.id == selectedCompanyId
                              );
                              setValue('companyFinder', selectedCompany.name);
                              setValue(
                                'companyFinderSelect',
                                selectedCompanyId
                              );
                              setValue('companyFinderId', selectedCompanyId);
                            }}
                          >
                            <Input />
                          </AutoComplete>
                        )}
                        name="companyFinder"
                        control={control}
                      />
                    </Form.Item>
                  </div>{' '}
                  <div>
                    <Checkbox
                      onChange={(e) => {
                        setShowNotAnyOfCompany(!showNotAnyOfCompany);
                      }}
                    >
                      Is not any of
                    </Checkbox>
                    {showNotAnyOfCompany && (
                      <Form.Item label="" name="companyFinderNotAny">
                        <Controller
                          render={({ field }) => (
                            <AutoComplete
                              style={{ width: '250px' }}
                              {...field}
                              options={companyListPeople.map((option) => ({
                                value: option.id,
                                label: (
                                  <div className="grid p-2">
                                    <div className="flex justify-between">
                                      <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                        {option.name}
                                        <br />
                                        <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                          {option.domain || '-'}
                                        </span>
                                      </span>
                                      <img
                                        className="absolute right-3"
                                        src={
                                          option?.logo_url
                                            ? `${option?.logo_url}`
                                            : ''
                                        }
                                        width={50}
                                        height={50}
                                        alt="Logo"
                                        onError={(e) => {
                                          e.target.onerror = null;
                                          e.target.src = logoZielo;
                                          e.target.alt = 'Logo';
                                        }}
                                      />
                                    </div>
                                  </div>
                                ),
                              }))}
                              onSearch={(value) => {
                                setValue('companyFinderNotAny', value);
                                setValue('companyFinderNotAnySelect', null);
                                setValue('companyFinderNotAnyId', null);
                              }}
                              onSelect={async (selectedCompanyId) => {
                                const selectedCompany = companyListPeople.find(
                                  (ao) => ao.id == selectedCompanyId
                                );
                                setValue(
                                  'companyFinderNotAny',
                                  selectedCompany.name
                                );
                                setValue('companyFinderNotAnySelect', [
                                  selectedCompanyId,
                                ]);
                                setValue('companyFinderNotAnyId', [
                                  selectedCompanyId,
                                ]);
                              }}
                            >
                              <Input />
                            </AutoComplete>
                          )}
                          name="companyFinderNotAny"
                          control={control}
                        />
                      </Form.Item>
                    )}
                  </div>
                  <div>
                    <Checkbox
                      onChange={(e) => {
                        setShowIncludeCompany(!showIncludeCompany);
                      }}
                    >
                      Include past company
                    </Checkbox>
                    {showIncludeCompany && (
                      <Form.Item label="" name="companyFinderInclude">
                        <Controller
                          render={({ field }) => (
                            <AutoComplete
                              style={{ width: '250px' }}
                              {...field}
                              options={companyListPeople.map((option) => ({
                                value: option.id,
                                label: (
                                  <div className="grid p-2">
                                    <div className="flex justify-between">
                                      <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                        {option.name}
                                        <br />
                                        <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                          {option.domain || '-'}
                                        </span>
                                      </span>
                                      <img
                                        className="absolute right-3"
                                        src={
                                          option?.logo_url
                                            ? `${option?.logo_url}`
                                            : ''
                                        }
                                        width={50}
                                        height={50}
                                        alt="Logo"
                                        onError={(e) => {
                                          e.target.onerror = null;
                                          e.target.src = logoZielo;
                                          e.target.alt = 'Logo';
                                        }}
                                      />
                                    </div>
                                  </div>
                                ),
                              }))}
                              onSearch={(value) => {
                                setValue('companyFinderInclude', value);
                                setValue('companyFinderIncludeSelect', null);
                                setValue('companyFinderIncludeId', null);
                              }}
                              onSelect={async (selectedCompanyId) => {
                                const selectedCompany = companyListPeople.find(
                                  (ao) => ao.id == selectedCompanyId
                                );
                                setValue(
                                  'companyFinderInclude',
                                  selectedCompany.name
                                );
                                setValue('companyFinderIncludeSelect', [
                                  selectedCompanyId,
                                ]);
                                setValue('companyFinderIncludeId', [
                                  selectedCompanyId,
                                ]);
                              }}
                            >
                              <Input />
                            </AutoComplete>
                          )}
                          name="companyFinderInclude"
                          control={control}
                        />
                      </Form.Item>
                    )}
                  </div>
                </div>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faLocationDot}
                      style={{ marginRight: '10px' }}
                    />{' '}
                    Location
                  </span>
                  {watch('locationFinder') &&
                    watch('locationFinder')?.length > 0 && (
                      <Row>
                        {watch('locationFinder').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Location Name" name="locationFinder">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          setValue('locationFinderText', searchText);
                        }}
                        {...field}
                        notFoundContent={null}
                        options={
                          watch('locationFinderText') === '' ||
                            !watch('locationFinderText')
                            ? locationListPeople.map((so) => ({
                              ...so,
                              label: so.cleaned_name,
                              value: so.id,
                            }))
                            : [
                              {
                                label: watch('locationFinderText'),
                                value: watch('locationFinderText'),
                              },
                              ...locationListPeople.map((so) => ({
                                ...so,
                                label: so.cleaned_name,
                                value: so.id,
                              })),
                            ]
                        }
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="locationFinder"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faMedal}
                      style={{ marginRight: '10px' }}
                    />
                    Title
                  </span>
                  {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
                    <Row>
                      {watch('titleFinder').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="Title" name="titleFinder">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          setValue('titleFinderText', searchText);
                        }}
                        {...field}
                        notFoundContent={null}
                        options={
                          watch('titleFinderText') === '' ||
                            !watch('titleFinderText')
                            ? titleList.map((so) => ({
                              ...so,
                              label: so.cleaned_name,
                              value: so.id,
                            }))
                            : [
                              {
                                label: watch('titleFinderText'),
                                value: watch('titleFinderText'),
                              },
                              ...titleList.map((so) => ({
                                ...so,
                                label: so.cleaned_name,
                                value: so.id,
                              })),
                            ]
                        }
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="titleFinder"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faUserGroup}
                      style={{ marginRight: '10px' }}
                    />
                    Employees
                  </span>
                  {watch('employeeFinder') &&
                    watch('employeeFinder')?.length > 0 && (
                      <Row>
                        {watch('employeeFinder').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Predefined Range" name="employeeFinder">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        onSearch={(searchText) => {
                          if (employeeList.length == 0) {
                            setValue('employeeFinderText', searchText);
                          }
                        }}
                        {...field}
                        notFoundContent={null}
                        options={employeeList.map((so) => ({
                          ...so,
                          label: so.display_name,
                          value: so.value,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="employeeFinder"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faIndustry}
                      style={{ marginRight: '10px' }}
                    />
                    Industry & Keywords
                  </span>
                  {watch('industryFinder') &&
                    watch('industryFinder')?.length > 0 && (
                      <Row>
                        <span className="text-xs my-auto mr-2">Industry: </span>
                        {watch('industryFinder').map((item, index) => (
                          <Col
                            key={index}
                            className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                          >
                            {item.label}
                          </Col>
                        ))}
                      </Row>
                    )}
                </div>
              ),
              children: (
                <>
                  <Form.Item label="Industry" name="industryFinder">
                    <Controller
                      render={({ field }) => (
                        <Select
                          labelInValue
                          mode="multiple"
                          onSearch={(searchText) => {
                            setValue('industryFinderText', searchText);
                          }}
                          {...field}
                          notFoundContent={null}
                          options={
                            watch('industryFinderText') === '' ||
                              !watch('industryFinderText')
                              ? industryList.map((so) => ({
                                ...so,
                                label: so.cleaned_name,
                                value: so.id,
                              }))
                              : [
                                {
                                  label: watch('industryFinderText'),
                                  value: watch('industryFinderText'),
                                },
                                ...industryList.map((so) => ({
                                  ...so,
                                  label: so.cleaned_name,
                                  value: so.id,
                                })),
                              ]
                          }
                          filterOption={(inputValue, option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) !== -1
                          }
                        />
                      )}
                      name="industryFinder"
                      control={control}
                    />
                  </Form.Item>
                  <Checkbox
                    className="w-full"
                    checked={showIncludeKeywordPeople}
                    onChange={(e) => {
                      setShowIncludeKeywordPeople(e.target.checked);
                      setValue('includeKeywordPeople', []);
                      setValue('includeKeywordPeopleText', '');
                    }}
                  >
                    Include Keywords
                  </Checkbox>
                  {showIncludeKeywordPeople && (
                    <Form.Item name="includeKeywordPeople">
                      <Controller
                        name="includeKeywordPeople"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('includeKeywordPeopleText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeKeywordPeopleText'),
                                label: watch('includeKeywordPeopleText'),
                                value: watch('includeKeywordPeopleText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showIncludeAllKeywordPeople}
                    onChange={(e) => {
                      setShowIncludeAllKeywordPeople(e.target.checked);
                      setValue('includeAllKeywordPeople', []);
                      setValue('includeAllKeywordPeopleText', '');
                    }}
                  >
                    Include ALL
                  </Checkbox>
                  {showIncludeAllKeywordPeople && (
                    <Form.Item name="includeAllKeywordPeople">
                      <Controller
                        name="includeAllKeywordPeople"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue(
                                'includeAllKeywordPeopleText',
                                inputValue
                              );
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('includeAllKeywordPeopleText'),
                                label: watch('includeAllKeywordPeopleText'),
                                value: watch('includeAllKeywordPeopleText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                  <Checkbox
                    className="w-full"
                    checked={showExcludeKeywordsPeople}
                    onChange={(e) => {
                      setShowExcludeKeywordsPeople(e.target.checked);
                      setValue('excludeKeywordsPeople', []);
                      setValue('excludeKeywordsPeopleText', '');
                    }}
                  >
                    Exclude Keywords
                  </Checkbox>
                  {showExcludeKeywordsPeople && (
                    <Form.Item name="excludeKeywordsPeople">
                      <Controller
                        name="excludeKeywordsPeople"
                        control={control}
                        render={({ field }) => (
                          <Select
                            labelInValue
                            filterOption={false}
                            mode="multiple"
                            onSearch={(inputValue) => {
                              setValue('excludeKeywordsPeopleText', inputValue);
                            }}
                            {...field}
                            options={[
                              {
                                key: watch('excludeKeywordsPeopleText'),
                                label: watch('excludeKeywordsPeopleText'),
                                value: watch('excludeKeywordsPeopleText'),
                              },
                            ]}
                          />
                        )}
                      />
                    </Form.Item>
                  )}
                </>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faChartLine}
                      style={{ marginRight: '10px' }}
                    />
                    Buying Intent
                  </span>
                </div>
              ),
              children: (
                <>
                  <div style={{ fontWeight: '700' }}>Intent Score</div>
                  <div style={{ marginTop: '10px' }}>
                    <Form.Item name="contactBuyingIntentScore">
                      <Controller
                        name="contactBuyingIntentScore"
                        control={control}
                        render={({ field }) => (
                          <Checkbox.Group
                            style={{
                              width: '100%',
                            }}
                            options={OptionBuyIntent}
                            onChange={(e) => {
                              setValue('contactBuyingIntentScore', e);
                            }}
                          ></Checkbox.Group>
                        )}
                      />
                    </Form.Item>
                  </div>
                  <div
                    style={{ width: '100%', height: '2px', background: '#ccc' }}
                  ></div>
                  <div style={{ fontWeight: '700', marginTop: '10px' }}>
                    Intent Topics
                  </div>
                  <div style={{ marginTop: '10px' }}>
                    <Form.Item name="contactBuyingIntentIds">
                      <Controller
                        name="contactBuyingIntentIds"
                        control={control}
                        render={({ field }) => (
                          <Checkbox.Group
                            onChange={(e) => {
                              setValue('contactBuyingIntentIds', e);
                            }}
                          >
                            {dataParentIntent?.map((item, index) => (
                              <div style={{ width: '100%', marginTop: '5px' }}>
                                <Checkbox value={item?.id}>
                                  {item?.name}
                                </Checkbox>
                              </div>
                            ))}
                          </Checkbox.Group>
                        )}
                      />
                    </Form.Item>
                    <div
                      onClick={() => setShowModalAddTopic(true)}
                      style={{ cursor: 'pointer' }}
                    >
                      Add more topic
                    </div>
                    <Modal
                      onCancel={() => setShowModalAddTopic(false)}
                      onOk={handleChangeSettingIntent}
                      width={'1000px'}
                      title="Intent Topic Settings"
                      open={showModalAddTopic}
                    >
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div style={{ width: '65%', border: '1px solid #ccc' }}>
                          <div
                            style={{
                              width: '100%',
                              borderBottom: '1px solid #ccc',
                              padding: '10px',
                            }}
                          >
                            Topics of Interest
                          </div>
                          <div
                            style={{
                              padding: '10px',
                              height: '500px',
                              overflowY: 'scroll',
                            }}
                          >
                            {dataListTopicTitle?.map((item, index) => (
                              <>
                                <IntentCollap
                                  setListDataIntentSetting={
                                    setListDataIntentSetting
                                  }
                                  listDataIntentSetting={listDataIntentSetting}
                                  item={item}
                                />
                              </>
                            ))}
                          </div>
                        </div>
                        <div style={{ width: '35%', border: '1px solid #ccc' }}>
                          <div
                            style={{
                              width: '100%',
                              borderBottom: '1px solid #ccc',
                              padding: '10px',
                            }}
                          >
                            Selected
                          </div>
                          <div style={{ padding: '10px' }}>
                            {listDataIntentSetting?.map((item, index) => (
                              <>
                                <Tag
                                  style={{ marginBottom: '10px' }}
                                  onClose={(e) => {
                                    e.preventDefault();
                                    handleDeleteIntent(item.id);
                                  }}
                                  closeIcon
                                >
                                  {item?.name}
                                </Tag>
                              </>
                            ))}
                          </div>
                        </div>
                      </div>
                    </Modal>
                  </div>
                </>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <>
                  <Flex>
                    <span>
                      <FontAwesomeIcon
                        icon={faTowerBroadcast}
                        style={{ marginRight: '10px' }}
                      />
                      Signals
                    </span>
                    {watch('searchSignalIds') &&
                      watch('searchSignalIds')?.length > 0 && (
                        <Tag className="ml-auto" color="volcano">
                          {watch('searchSignalIds')?.length}
                        </Tag>
                      )}
                  </Flex>
                </>
              ),
              children: (
                <>
                  <Form.Item name="searchSignalIds">
                    <Controller
                      name="searchSignalIds"
                      control={control}
                      render={({ field }) => (
                        <Flex vertical gap={8}>
                          {listSignals?.map((option, index, arr) => (
                            <Flex
                              className={
                                index !== arr.length - 1
                                  ? 'border-b pb-2'
                                  : undefined
                              }
                              justify="space-between"
                            >
                              <Checkbox
                                key={`signal-${option.id}`}
                                checked={field.value.includes(option.id)}
                                onChange={(e) =>
                                  updateArrayByKey(
                                    'searchSignalIds',
                                    e.target.checked,
                                    option.id
                                  )
                                }
                              >
                                {option.name}
                              </Checkbox>
                              {option.modality === 'people' ? (
                                <Tag
                                  className="flex items-center justify-center"
                                  color="processing"
                                  icon={<FaRegUser />}
                                />
                              ) : (
                                <Tag
                                  className="flex items-center justify-center"
                                  color="success"
                                  icon={<FaRegBuilding />}
                                />
                              )}
                            </Flex>
                          ))}
                        </Flex>
                      )}
                    />
                  </Form.Item>
                </>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faArrowsToDot}
                      style={{ marginRight: '10px' }}
                    />
                    Score
                  </span>
                  {watch('contactMinimumScore') && (
                    <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                      score:{' '}
                      <span style={{ fontWeight: '700' }}>
                        {watch('contactMinimumScore')}
                      </span>
                    </Col>
                  )}
                </div>
              ),
              children: (
                <>
                  <div style={{ fontWeight: '700', fontSize: '13px' }}>
                    Minimum score:
                  </div>
                  <Radio.Group
                    style={{ marginTop: '20px' }}
                    onChange={(e) =>
                      setValue('contactMinimumScore', e.target.value)
                    }
                  >
                    <Space direction="vertical">
                      <Radio value={'excellent'}>
                        <div
                          style={{
                            padding: '5px',
                            borderRadius: '5px',
                            background: 'rgba(61, 204, 133, .18)',
                          }}
                        >
                          ⭐️ Excellent
                        </div>
                      </Radio>
                      <Radio value={'good'}>
                        <div
                          style={{
                            padding: '5px',
                            borderRadius: '5px',
                            background: '#e9f2ff',
                          }}
                        >
                          😄 Good
                        </div>
                      </Radio>
                      <Radio value={'fair'}>
                        <div
                          style={{
                            padding: '5px',
                            borderRadius: '5px',
                            background: 'rgba(255,151,82,.18)',
                          }}
                        >
                          🙂 Fair
                        </div>
                      </Radio>
                      <Radio value={'poor'}>
                        <div
                          style={{
                            padding: '5px',
                            borderRadius: '5px',
                            background: '#efefef',
                          }}
                        >
                          ✖️ Not a fit
                        </div>
                      </Radio>
                    </Space>
                  </Radio.Group>
                </>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faMicrochip}
                      style={{ marginRight: '10px' }}
                    />
                    Technologies
                  </span>
                  {watch('listTechnologies') && (
                    <>
                      {watch('listTechnologies')?.map((item) => (
                        <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                          <span style={{ fontWeight: '700' }}>{item}</span>
                        </Col>
                      ))}
                    </>
                  )}
                </div>
              ),
              children: (
                <>
                  <Form.Item label="Technologies" name="listTechnologies">
                    <Controller
                      render={({ field }) => (
                        <Select
                          mode="multiple"
                          style={{ width: '250px' }}
                          filterOption={false}
                          {...field}
                          options={dataTechnologies.map((option) => ({
                            value: option.uid,
                            label: (
                              <div>
                                <div style={{ fontSize: '16px' }}>
                                  {option?.cleaned_name}
                                </div>
                                <div style={{ fontSize: '12px' }}>
                                  {option?.tag_category_downcase}
                                </div>
                              </div>
                            ),
                          }))}
                          onSearch={async (e) => {
                            const { data } = await employeeFinderSearchTag({
                              searchText: e,
                              type: 'technology',
                            });
                            setDataTechnologies(data?.tags);
                          }}
                          onChange={(value) =>
                            setValue('listTechnologies', value)
                          }
                        >
                          <Input />
                        </Select>
                      )}
                      name="companyFindCompany"
                      control={control}
                    />
                  </Form.Item>
                </>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faDollarSign}
                      style={{ marginRight: '10px' }}
                    />
                    Revenue
                  </span>
                  {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
                </div>
              ),
              children: (
                <>
                  <Form.Item name="revenueStatus">
                    <Controller
                      render={({ field }) => (
                        <Segmented
                          defaultValue="is_between"
                          onChange={async (value) => {
                            if (value === 'is_know') {
                              await handleGetRevenue({
                                openFactorNames: [
                                  'organization_trading_status',
                                ],
                                existFields: [
                                  'organization_revenue_in_thousands_int',
                                ],
                              });
                            } else if (value === 'is_un_known') {
                              await handleGetRevenue({
                                openFactorNames: [
                                  'organization_trading_status',
                                ],
                                notExistFields: [
                                  'organization_revenue_in_thousands_int',
                                ],
                              });
                            } else if (value === 'is_between') {
                              await handleGetRevenue({
                                openFactorNames: [
                                  'organization_trading_status',
                                ],
                              });
                            }
                            setValue('revenueStatus', value);
                          }}
                          options={[
                            { label: 'Is Between', value: 'is_between' },
                            { label: 'Is Know', value: 'is_know' },
                            { label: 'Is UnKnown', value: 'is_un_known' },
                          ]}
                        />
                      )}
                      name="revenueStatus"
                      control={control}
                    />
                  </Form.Item>
                  <div
                    style={{
                      width: '100%',
                      height: '1px',
                      backgroundColor: '#ccc',
                      marginTop: '-10px',
                    }}
                  ></div>
                  <div style={{ marginTop: '10px' }}>
                    <div>
                      <Checkbox.Group
                        onChange={(checkedValues) =>
                          setValue('revenueStatusItem', checkedValues)
                        }
                        style={{ width: '100%' }}
                      >
                        {listDataRevenue?.map((item) => (
                          <>
                            <Checkbox value={item?.value}>
                              <div
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                }}
                              >
                                <div>{item?.display_name}</div>
                                <div style={{ marginLeft: '20px' }}>
                                  {formatNumber(item?.count)}
                                </div>
                              </div>
                            </Checkbox>
                          </>
                        ))}
                      </Checkbox.Group>
                    </div>
                  </div>
                </>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faMoneyBills}
                      style={{ marginRight: '10px' }}
                    />
                    Funding
                  </span>
                  {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
                </div>
              ),
              children: (
                <>
                  <div>
                    <div>
                      <Checkbox.Group
                        onChange={(checkedValues) =>
                          setValue('fundingStatusItem', checkedValues)
                        }
                        style={{ width: '100%' }}
                      >
                        {listDataFunding?.map((item) => (
                          <>
                            <Checkbox
                              value={item?.value}
                              style={{ width: '100%' }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                }}
                              >
                                <div>{item?.display_name}</div>
                                <div style={{ marginLeft: '20px' }}>
                                  {formatNumber(item?.count)}
                                </div>
                              </div>
                            </Checkbox>
                          </>
                        ))}
                      </Checkbox.Group>
                    </div>
                  </div>
                  <div
                    style={{
                      width: '100%',
                      height: '1px',
                      backgroundColor: '#ccc',
                      marginTop: '20px',
                    }}
                  ></div>
                  <Form.Item name="fundingStatus">
                    <Controller
                      render={({ field }) => (
                        <Segmented
                          defaultValue="is_between"
                          onChange={async (value) => {
                            if (value === 'is_know') {
                              setFundingSize(false);
                              await handleGetFunding({
                                openFactorNames: [
                                  'organization_latest_funding_stage_cd',
                                ],
                                existFields: [
                                  'organization_total_funding_long',
                                ],
                              });
                            } else if (value === 'is_un_known') {
                              setFundingSize(false);
                              await handleGetFunding({
                                openFactorNames: [
                                  'organization_latest_funding_stage_cd',
                                ],
                                notExistFields: [
                                  'organization_total_funding_long',
                                ],
                              });
                            } else if (value === 'is_between') {
                              setFundingSize(true);
                              await handleGetFunding({
                                openFactorNames: [
                                  'organization_latest_funding_stage_cd',
                                ],
                              });
                            }
                            setValue('revenueStatus', value);
                          }}
                          options={[
                            { label: 'Is Between', value: 'is_between' },
                            { label: 'Is Know', value: 'is_know' },
                            { label: 'Is UnKnown', value: 'is_un_known' },
                          ]}
                        />
                      )}
                      name="fundingStatus"
                      control={control}
                    />
                  </Form.Item>
                  {fundingSize && (
                    <Row gutter={[24, 24]}>
                      <Col span={12}>
                        <Form.Item name="fundingMin" label="Funding min">
                          <Controller
                            name="fundingMin"
                            control={control}
                            render={({ field }) => (
                              <InputNumber
                                min={1}
                                onChange={(value) => {
                                  setValue('fundingMin', value);
                                }}
                              />
                            )}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="fundingMin" label="Funding Max">
                          <Controller
                            name="fundingMin"
                            control={control}
                            render={({ field }) => (
                              <InputNumber
                                min={1}
                                onChange={(value) => {
                                  setValue('fundingMax', value);
                                }}
                              />
                            )}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  )}
                </>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          defaultActiveKey={['contact-name']}
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faImagePortrait}
                      style={{ marginRight: '10px' }}
                    />
                    Name
                  </span>
                  {watch('nameFinderText') !== '' &&
                    watch('nameFinderText') && (
                      <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                        {watch('nameFinderText')}
                      </span>
                    )}
                </div>
              ),
              children: (
                <Form.Item label="Name" name="nameFinder">
                  <Controller
                    render={({ field }) => (
                      <Input
                        placeholder="name"
                        onChange={(e) =>
                          setValue('nameFinderText', e.target.value)
                        }
                      />
                    )}
                    name="nameFinder"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          expandIconPosition="end"
          style={{
            marginTop: '10px',
            width: '290px',
          }}
          defaultActiveKey={['contact-name']}
          items={[
            {
              key: 'contact-name',
              label: (
                <div className="inline-grid">
                  <span>
                    <FontAwesomeIcon
                      icon={faBriefcase}
                      style={{ marginRight: '10px' }}
                    />
                    Job Postings
                  </span>
                  <Row>
                    {watch('emailOpenedStatus') && (
                      <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                        status:{' '}
                        <span style={{ fontWeight: '700' }}>
                          {watch('emailOpenedStatus')}
                        </span>
                      </Col>
                    )}
                    {watch('contactEmailOpenedTime') && (
                      <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                        Time least:{' '}
                        <span style={{ fontWeight: '700' }}>
                          {watch('contactEmailOpenedTime')}
                        </span>
                      </Col>
                    )}
                  </Row>
                </div>
              ),
              children: (
                <>
                  <Form.Item
                    name="currentlyHiring"
                    label="Currently Hiring for"
                  >
                    <Controller
                      name="currentlyHiring"
                      control={control}
                      render={({ field }) => (
                        <Select
                          mode="multiple"
                          style={{ width: '250px' }}
                          filterOption={false}
                          {...field}
                          options={dataPersonTitle.map((option) => ({
                            value: option.cleaned_name,
                            label: <> {option?.cleaned_name}</>,
                          }))}
                          onSearch={async (e) => {
                            handleGetPersonalTitles(e);
                          }}
                          onChange={(value) =>
                            setValue('listCurrentlyHiring', value)
                          }
                        >
                          <Input />
                        </Select>
                      )}
                    />
                  </Form.Item>

                  <Form.Item name="contactJobLocated" label="Job located at">
                    <Controller
                      name="contactJobLocated"
                      control={control}
                      render={({ field }) => (
                        <Select
                          mode="multiple"
                          style={{ width: '250px' }}
                          filterOption={false}
                          {...field}
                          options={dataLocation.map((option) => ({
                            value: option.cleaned_name,
                            label: <> {option?.cleaned_name}</>,
                          }))}
                          onSearch={async (e) => {
                            handleGetLocation(e);
                          }}
                          onChange={(value) =>
                            setValue('contactJobLocated', value)
                          }
                        >
                          <Input />
                        </Select>
                      )}
                    />
                  </Form.Item>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Form.Item
                        name="organizationNumJobsRangeMin"
                        label="Active job"
                      >
                        <Controller
                          name="organizationNumJobsRangeMin"
                          control={control}
                          render={({ field }) => (
                            <InputNumber
                              min={1}
                              placeholder="Min"
                              onChange={(value) =>
                                setValue('organizationNumJobsRangeMin', value)
                              }
                            />
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="organizationNumJobsRangeMax" label=" ">
                        <Controller
                          name="organizationNumJobsRangeMax"
                          control={control}
                          render={({ field }) => (
                            <InputNumber
                              min={1}
                              placeholder="Max"
                              onChange={(value) =>
                                setValue('organizationNumJobsRangeMax', value)
                              }
                            />
                          )}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Form.Item
                        name="organizationJobPostedAtRangeMin"
                        label="Job Posted At"
                      >
                        <Controller
                          name="organizationJobPostedAtRangeMin"
                          control={control}
                          render={({ field }) => (
                            <DatePicker
                              placeholder="Start Date"
                              onChange={(date, dateString) => {
                                setValue(
                                  'organizationJobPostedAtRangeMin',
                                  dateString
                                );
                              }}
                            />
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="organizationJobPostedAtRangeMax"
                        label=" "
                      >
                        <Controller
                          name="organizationJobPostedAtRangeMax"
                          control={control}
                          render={({ field }) => (
                            <DatePicker
                              placeholder="End Date"
                              onChange={(date, dateString) => {
                                setValue(
                                  'organizationJobPostedAtRangeMax',
                                  dateString
                                );
                              }}
                            />
                          )}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              ),
            },
          ]}
        />
      </div>
    );
  };

  return (
    <div className="rounded-md">
      {/* Modal show contact detail */}
      <div style={{ float: 'right' }}>
        {selectedRowKeys?.length > 0 && (
          <div>
            <Dropdown
              className="mb-4 animated fadeInDownBig"
              placement="bottom"
              arrow
              menu={{
                items: [
                  {
                    key: 'delete-searchs',
                    label: (
                      <a
                        className="Montserrat flex gap-2 items-center py-2"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsAddContactFormBulk(true);
                        }}
                      >
                        <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
                      </a>
                    ),
                  },
                  {
                    key: 'bulk-add-contact-lists',
                    label: (
                      <a
                        className="Montserrat flex gap-2 items-center py-2"
                        onClick={async (e) => {
                          e.preventDefault();
                          const { accessedContacts, lockedAccessContacts } =
                            employeesChecked.reduce(
                              (acc, item) => {
                                if (!selectedRowKeys.includes(item.id)) {
                                  return acc;
                                }
                                if (
                                  item.email &&
                                  item.email !== '<EMAIL>'
                                ) {
                                  acc.accessedContacts.push(item);
                                } else {
                                  acc.lockedAccessContacts.push(item);
                                }

                                return acc;
                              },
                              {
                                accessedContacts: [],
                                lockedAccessContacts: [],
                              }
                            );
                          if (lockedAccessContacts.length > 0) {
                            setIsLoadingEmployee(true);
                            const requestAccessContacts = [];
                            for (let lockedAccessContact of lockedAccessContacts) {
                              try {
                                const { data } = await getLDetailEmployee({
                                  employeeId: lockedAccessContact.id,
                                });
                                accessedContacts.push(data);
                                requestAccessContacts.push(data);
                              } catch (error) {
                                // Do nothing
                              }
                            }
                            if (requestAccessContacts.length) {
                              const requestAccessContactIds =
                                requestAccessContacts.map((item) => item.id);
                              setListDetailEmployee([
                                ...listDetailEmployee.filter(
                                  (item) =>
                                    !requestAccessContactIds.includes(item.id)
                                ),
                                ...requestAccessContacts,
                              ]);
                              setListEmployee([
                                ...listEmployee.filter(
                                  (item) =>
                                    !requestAccessContactIds.includes(item.id)
                                ),
                                ...requestAccessContacts,
                              ]);
                            }
                            setIsLoadingEmployee(false);
                          }

                          if (accessedContacts.length === 0) {
                            notification.error({
                              message:
                                'Please choose at least one contact who has accessed email!',
                            });

                            return;
                          }

                          setOpenExistingUserGroup(true);
                          setCurrentContact();
                          setBulkContacts(accessedContacts);
                        }}
                      >
                        <span>{COMMON_STRINGS.BULK_ADD_TO_CONTACT_LIST}</span>
                      </a>
                    ),
                  },
                  {
                    key: 'bulk-enrich',
                    label: (
                      <a
                        className="Montserrat flex gap-2 items-center py-2"
                        onClick={(e) => {
                          handleBulkEnrich();
                        }}
                      >
                        <span>{COMMON_STRINGS.BULK_ENRICH}</span>
                      </a>
                    ),
                  },
                ],
              }}
            >
              <Space>
                <Button
                  type="primary"
                  className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                >
                  <p className="Montserrat">
                    {`${selectedRowKeys?.length} Selected`}
                  </p>
                  <DownOutlined />
                </Button>
              </Space>
            </Dropdown>
          </div>
        )}
      </div>
      <div style={{ clear: 'both' }}></div>
      <Modal
        width={1600}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title="Detail Contact"
        open={showDetailContact}
        onCancel={() => {
          setShowDetailContact(false);
        }}
        footer={false}
      >
        <>
          <Card
            title={
              <p>
                {detailContactStaff?.name}{' '}
                <span className="p-2 bg-gray-200 rounded-md">N/A</span>
              </p>
            }
            extra={
              <a href={detailContactStaff?.linkedin_url} target="_blank">
                <Button
                  type="primary"
                  shape="circle"
                  icon={<LinkedinOutlined className="text-[#0288d1]" />}
                />
              </a>
            }
          >
            <Row>
              <Col span={16}>
                <span className="text-base text-bluegray-900">
                  {detailContactStaff?.title} at{' '}
                  <span className=" text-blue-500">
                    {detailContactStaff?.organization?.name}
                  </span>
                </span>
              </Col>
              <Col>
                <Button icon={<CloudDownloadOutlined />}>
                  Access Email & Phone Number
                </Button>
              </Col>
            </Row>
          </Card>

          <Card className="mt-3" title={<p>Work History</p>}>
            <Row>
              <Col span={16}>
                {detailContactStaff.employment_history
                  ? detailContactStaff?.employment_history.map((item) => {
                    const employData = currentHistoryWork.find(
                      (obj) => obj.id === item.organization_id
                    );
                    return (
                      <Row className="mt-2">
                        <Col span={2}>
                          {employData ? (
                            <img
                              src={employData?.logo_url}
                              alt={item?.id}
                              style={{ width: '50px', height: '50px' }}
                            />
                          ) : (
                            <BankOutlined className=" text-xl border p-3 rounded" />
                          )}
                        </Col>
                        <Col>
                          <span className="text-base text-bluegray-900 font-semibold">
                            {item?.organization_name}
                          </span>
                          <div>
                            <div>
                              {item?.title} / {item?.start_date} -{' '}
                              {item?.end_date ? item?.end_date : 'Current'}
                            </div>
                          </div>
                        </Col>
                      </Row>
                    );
                  })
                  : ''}
              </Col>
              <Col>
                <p>
                  <span className="font-semibold text-base">Location:</span>{' '}
                  {`${detailContactStaff?.city
                      ? detailContactStaff?.city + ','
                      : ''
                    } ${detailContactStaff?.state
                      ? detailContactStaff?.state + ','
                      : ''
                    } ${detailContactStaff?.country
                      ? detailContactStaff?.country + '.'
                      : ''
                    }`}
                </p>
              </Col>
            </Row>
          </Card>
          <Card className="mt-3">
            <Row>
              <Col span={12}>
                <Row>
                  <Col span={2}>
                    <img
                      className="mr-2 rounded w-14"
                      src={detailContactStaff?.organization?.logo_url}
                      alt={detailContactStaff?.organization?.name}
                    />
                  </Col>
                  <Col>
                    <Meta
                      description={
                        <span className="text-lg text-blue-500">
                          {detailContactStaff?.organization?.name}
                        </span>
                      }
                    />
                    <Row className="flex gap-2 mt-1">
                      <Col>
                        <LinkOutlined
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGotoWebsite(
                              detailContactStaff?.organization?.website_url
                            );
                          }}
                          className="border p-1 rounded cursor-pointer text-gray-700 text-lg"
                        />
                      </Col>
                      <Col>
                        <LinkedinOutlined
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGotoWebsite(
                              detailContactStaff?.organization?.linkedin_url
                            );
                          }}
                          className="border p-1 rounded cursor-pointer text-[#0288d1] text-lg"
                        />
                      </Col>
                      <Col>
                        <FacebookOutlined
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGotoWebsite(
                              detailContactStaff?.organization?.facebook_url
                            );
                          }}
                          className="border p-1 rounded cursor-pointer text-[#3f51b5] text-lg"
                        />
                      </Col>
                      <Col>
                        <TwitterOutlined
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGotoWebsite(
                              detailContactStaff?.organization?.twitter_url
                            );
                          }}
                          className="border p-1 rounded cursor-pointer text-[#03a9f4] text-lg"
                        />
                      </Col>
                    </Row>
                  </Col>
                </Row>
                <Row className="mt-5">
                  <Col span={8}>
                    <p className="font-semibold text-base">Industry</p>
                    <p className="font-normal text-base">
                      {detailOrganization?.industry}
                    </p>
                  </Col>
                  <Col span={8}>
                    <p className="font-semibold text-base">Employees</p>
                    <p className="font-normal text-base">
                      {detailOrganization?.estimated_num_employees
                        ? detailOrganization?.estimated_num_employees
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, '.')
                        : ''}
                    </p>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <p className="font-semibold text-base">
                  Description <br />{' '}
                  <span className="font-normal">
                    {detailOrganization?.short_description}
                  </span>
                </p>
                <p className="mt-6 font-semibold text-base">
                  Keyword <br />
                  <Row>
                    {detailOrganization?.keywords
                      ? detailOrganization?.keywords.map((item, i) => (
                        <Col className="mt-1 my-2">
                          <span
                            className="px-3 py-1 bg-gray-200 rounded mr-2 font-normal"
                            key={i}
                          >
                            {item}
                          </span>
                        </Col>
                      ))
                      : ''}
                  </Row>
                </p>
              </Col>
            </Row>
          </Card>
        </>
      </Modal>

      {/* Modal show company detail */}
      {/* <Modal
        width={1600}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title="Detail Company"
        open={showDetailCompany}
        onCancel={() => {
          setShowDetailCompany(false);
        }}
        footer={false}
      >
        <div className="grid gap-4">
          <Card>
            <Row className="flex justify-center">
              <Col>
                <img
                  className="mr-2 rounded w-8"
                  src={detailCompany?.logo_url}
                  alt={detailCompany?.name}
                />
              </Col>
              <span className="text-lg my-auto">
                {detailCompany.name}{' '}
                <span className="px-2 py-1 bg-gray-200 rounded-md text-sm ml-1">
                  N/A
                </span>
              </span>
            </Row>
            <Row className="flex gap-2 mt-1 justify-center">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.website_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-gray-700 text-lg"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.linkedin_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#0288d1] text-lg"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.facebook_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#3f51b5] text-lg"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.twitter_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#03a9f4] text-lg"
                />
              </Col>
              <div class="border-r border-solid"></div>
              <Col>
                <CopyToClipboard
                  text={detailCompany?.phone}
                  onCopy={() => {
                    notification.success({ message: 'Success Copy Number' });
                  }}
                >
                  <Button icon={<PhoneOutlined />}>
                    {detailCompany?.phone}
                  </Button>
                </CopyToClipboard>
              </Col>
            </Row>
          </Card>
          <Card>
            <span className="font-semibold">
              {showFullText
                ? detailOrganization?.short_description
                : detailOrganization?.short_description?.slice(0, 200)}
            </span>
            {detailOrganization?.short_description ? (
              detailOrganization?.short_description.length > 200 ? (
                <button
                  className="text-blue-500 text-sm font-normal ml-1"
                  onClick={toggleText}
                >
                  {showFullText ? 'Show Less' : '... Show More'}
                </button>
              ) : (
                ''
              )
            ) : (
              ''
            )}
            <p className="mt-6 font-semibold text-base">
              Company Keyword <br />
              <Row className="mt-2">
                {detailOrganization?.keywords
                  ? detailOrganization?.keywords.map((item, i) => (
                      <Col className="mt-1 my-2">
                        <span
                          className="px-3 py-1 bg-gray-200 rounded mr-2 font-normal"
                          key={i}
                        >
                          {item}
                        </span>
                      </Col>
                    ))
                  : ''}
              </Row>
            </p>
          </Card>
          <Card>
            <Row>
              <Col className="grid gap-3" span={12}>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Industry</span>
                  </Col>
                  <Col span={18}>
                    <span className="flex flex-wrap">
                      {detailOrganization?.industries
                        ? detailOrganization?.industries.map((item, i) => (
                            <span
                              className="rounded mr-2 text-base font-semibold text-blue-500"
                              key={i}
                            >
                              {item}
                              {i === detailOrganization?.industries?.length - 1
                                ? '.'
                                : ','}
                            </span>
                          ))
                        : ''}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">
                      Founding Year
                    </span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.founded_year}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Employees</span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.estimated_num_employees}
                    </span>
                  </Col>
                </Row>
                {detailOrganization?.publicly_traded_exchange && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">Trading</span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.publicly_traded_exchange?.toUpperCase()}{' '}
                        :{' '}
                        {detailOrganization?.publicly_traded_symbol?.toUpperCase()}
                        .
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.market_cap && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Market Cap
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.market_cap}
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.annual_revenue_printed && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Annual Revenue
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        ${detailOrganization?.annual_revenue_printed}
                      </span>
                    </Col>
                  </Row>
                )}
              </Col>
              <Col span={12}>
                <Row className="items-center py-1">
                  <Col span={4} className="flex items-center mb-auto">
                    <EnvironmentOutlined className="pr-3 text-blue-400" />
                    <span className="pr-3 text-graySecondary text-base font-semibold">
                      Location
                    </span>
                  </Col>
                  <Col span={18} className="pr-3 text-base">{`${
                    detailOrganization?.raw_address
                      ? detailOrganization?.raw_address + ','
                      : ''
                  } ${
                    detailOrganization?.city
                      ? detailOrganization?.city + ','
                      : ''
                  } ${
                    detailOrganization?.state
                      ? detailOrganization?.state + ','
                      : ''
                  } ${
                    detailOrganization?.zip ? detailOrganization?.zip + ',' : ''
                  } ${
                    detailOrganization?.country
                      ? detailOrganization?.country + '.'
                      : ''
                  }`}</Col>
                </Row>
              </Col>
            </Row>
          </Card>
          <Card style={{ width: 600 }} className="my-auto mx-auto">
            <p className="text-base font-semibold mb-3">
              Technologiy Insights (
              {detailOrganization.current_technologies
                ? detailOrganization?.current_technologies.length || 0
                : 0}
              )
            </p>
            <List
              style={{ overflowY: 'scroll', maxHeight: '500px' }}
              header={false}
              footer={false}
              bordered
              dataSource={detailOrganization?.current_technologies || []}
              renderItem={(item) => (
                <List.Item>
                  <Row>
                    <Col className=" text-base font-semibold" span={24}>
                      {item?.name}
                    </Col>
                    {item?.category}
                  </Row>
                </List.Item>
              )}
            />
          </Card>
        </div>
      </Modal> */}

      <CompanyDetailModal
        showDetailCompany={showDetailCompany}
        setShowDetailCompany={setShowDetailCompany}
        detailCompany={detailCompany}
        detailOrganization={detailOrganization}
      />

      {/* Modal find email by link = list email */}
      <Modal
        width={1600}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title="List Staff"
        open={isModalStaffOpen}
        onCancel={() => {
          setShowFindEmail(false);
          setIsModalStaffOpen(false);
          setIsLoadingEmployee(false);
          setValue('searchCompany', '');
          setValue('searchPeople', '');
          resetAllPeople();
          resetAllCompany();
        }}
        footer={false}
      >
        <Tabs defaultActiveKey="1" items={itemsTableEmailFinder} />
        <Button
          className="flex ml-auto"
          htmlType="button"
          onClick={() => {
            setShowFindEmail(false);
            setIsModalStaffOpen(false);
            setIsLoadingEmployee(false);
            setValue('searchCompany', '');
            setValue('searchPeople', '');
            resetAllPeople();
            resetAllCompany();
          }}
          type="primary"
        >
          Done
        </Button>
      </Modal>

      {/* MODAL ADD CONTACT */}
      {isAddContactForm && (
        <Modal
          className="customize-contact-form"
          width={1000}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          // title="Add Contact Form"
          open={isAddContactForm}
          onCancel={() => {
            setIsAddContactForm(false);
            handleResetFormAddContact();
          }}
          footer={false}
        >
          <Form layout="vertical">
            <div className="text-xl mx-6 pb-3 pt-5 font-semibold text-base border-b-2 border-b-[#17c1e8]">
              <span>Add Contact Form</span>
            </div>
            <BullhornSubmissionContact
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              handleCloseClient={handleCloseClient}
              setHandleCloseClient={setHandleCloseClient}
              handleCloseContact={handleCloseContact}
              setHandleCloseContact={setHandleCloseContact}
              detailDataContact={detailDataContact}
              flagDetailContact={flagDetailContact}
            />
            <div className="pl-6 left-0 bottom-0 w-full pb-4">
              <div className="flex gap-4 mr-8">
                <Button
                  onClick={() => {
                    setIsAddContactForm(false);
                    handleResetFormAddContact();
                  }}
                  className={`bg-[#BEDAFD33] `}
                >
                  Cancel
                </Button>
                <Button
                  htmlType="button"
                  onClick={handleSubmitAddContact}
                  type="primary"
                  className={` `}
                >
                  Save
                </Button>
              </div>
            </div>
          </Form>
        </Modal>
      )}

      {isAddContactFormBulk && (
        <Modal
          className="customize-contact-form"
          width={1000}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          // title="Add Contact Form"
          open={isAddContactFormBulk}
          onCancel={() => {
            setIsAddContactFormBulk(false);
            // handleResetFormAddContact();
          }}
          footer={false}
        >
          <Form layout="vertical">
            <div className="text-xl mx-6 pb-3 pt-5 font-semibold text-base border-b-2 border-b-[#17c1e8]">
              <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
            </div>
            <BullhornBulkAddContactModal
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              handleCloseClient={handleCloseClient}
              setHandleCloseClient={setHandleCloseClient}
              handleCloseContact={handleCloseContact}
              setHandleCloseContact={setHandleCloseContact}
              detailDataContact={detailDataContact}
              flagDetailContact={flagDetailContact}
              listEmployee={listEmployee}
              selectedRowKeys={selectedRowKeys}
              isAddContactFormBulk={isAddContactFormBulk}
              loadingGetBulkData={loadingGetBulkData}
              setLoadingBulkData={setLoadingBulkData}
              setDataContacts={setDataContacts}
              dataContacts={dataContacts}
            />
            <div className="pl-6 left-0 bottom-0 w-full pb-4">
              <div
                className="flex gap-4 mr-8"
                style={{ float: 'right', paddingBottom: '10px' }}
              >
                <Button
                  onClick={() => {
                    setIsAddContactFormBulk(false);
                    handleResetFormAddContact();
                  }}
                  className={`bg-[#BEDAFD33] `}
                >
                  Cancel
                </Button>
                <Button
                  htmlType="button"
                  onClick={handleBulkAddContact}
                  type="primary"
                  loading={isLoadingAddContactFormBulk}
                  className={` `}
                  disabled={loadingGetBulkData}
                >
                  Save
                </Button>
              </div>
            </div>
            <div style={{ clear: 'both' }}></div>
          </Form>
        </Modal>
      )}
      <Modal
        width={800}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>New Email</div>
            <div style={{ marginRight: '50px' }}>
              {/* <label style={{fontSize: "14px", marginRight: "8px"}} for="configEmail">Don't send Sequence Email</label>
                <Checkbox id='configSequenceEmail' style={{ marginRight: "20px"}}/>
                <label style={{fontSize: "14px", marginRight: "10px"}} for="configEmail">Don't send Email</label>
                <Checkbox checked={checkBoxStatus} onClick={() => {setCheckBoxStatus(!checkBoxStatus)}} id='configEmail'/> */}
            </div>
          </div>
        }
        open={openModalSendEmail}
        onCancel={() => {
          setOpenSendEmail(false);
        }}
        footer={false}
      >
        <div>
          <Form layout="vertical">
            <BullhornSendEmail
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              sendToEmail={getValues()?.email}
              mailTitle={getValues()?.jobtitle}
              openModalSendEmail={openModalSendEmail}
              setOpenSendEmail={setOpenSendEmail}
              listAddContactSelected={[getValues()?.optionContactSelect]}
              setNumberStep={setNumberStep}
              numberStep={numberStep}
              inputNumberStep={inputNumberStep}
              setInputNumberStep={setInputNumberStep}
              // functionContactClient={functionContactClient}
              // job={lead}
              fromSequenseEmail={true}
              setEmailConfigData={setEmailConfigData}
              emailConfigData={emailConfigData}
            // setHaveSendJob={setHaveSendJob}
            // setIsLockedSendJob={setIsLockedSendJob}
            // isLockedSendJob={isLockedSendJob}
            // checkBoxStatus={checkBoxStatus}
            />
          </Form>
        </div>
      </Modal>
      <ModalShowListExitSequence
        currentContact={currentContact}
        setOpen={setOpenExistingSequence}
        open={openExistingSequence}
      />
      <ModalListUserGroup
        currentContact={currentContact}
        currentContacts={bulkContacts}
        setOpen={setOpenExistingUserGroup}
        open={openExistingUserGroup}
      />
      <Row>
        <Col flex="300px">
          <Row gutter={16}>
            <Form
              className="w-full pr-4"
              layout="vertical"
              onFinish={handleSubmit(handleSubmitPeople)}
            >
              <Col className="w-full mr-4">
                <Form.Item
                  label="Search"
                  name="searchPeople"
                  className="mb-2 mt-2"
                >
                  <Controller
                    render={({ field }) => (
                      <Input
                        prefix={<SearchOutlined />}
                        {...field}
                        placeholder="Search People ..."
                        style={{
                          width: '290px',
                        }}
                      />
                    )}
                    name="searchPeople"
                    control={control}
                  />
                </Form.Item>
              </Col>
            </Form>
            <div className="col-span-2 flex items-start h-full pr-2">
              <div className="w-full flex flex-col gap-4 contact-finder-v2-filter-container">
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindEmails)}
                >
                  <Col className="w-full">
                    <div style={{ width: '310px' }}>{filterColumn()}</div>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className="bg-cyan-600 font-semibold text-white"
                      style={{
                        width: '290px',
                        height: '40px',
                        padding: '0 20px',
                        marginTop: '20px',
                      }}
                    >
                      Search
                    </Button>

                    <Button
                      type="primary"
                      className="font-semibold text-white"
                      style={{
                        width: '290px',
                        height: '40px',
                        padding: '0 20px',
                        marginTop: '10px',
                        // backgroundColor: "#415893",
                      }}
                      onClick={() => {
                        setOpenSavedSearch(true);
                      }}
                    >
                      Save this search
                    </Button>
                  </Col>
                </Form>
              </div>
            </div>
          </Row>
        </Col>

        <Col flex="auto" className="w-2/3 search-table-new-design-container">
          <div className="customTable">
            <Tabs
              defaultActiveKey="1"
              tabBarExtraContent={
                <div className="col-span-10" id="expand-container-bulk-enrich">
                  <div className="w-full" id="expand-contract-bulk-enrich">
                    <div className="w-full justify-center items-center flex gap-4 items-center">
                      {enrichLoading && (
                        <EventSourceRender
                          updateRecentlyData={updateRecentlyData}
                          sseName="ENRICH_FLOQER_DATA_LINKEDIN_URL"
                        />
                      )}
                      <Loading />
                      <div className="font-semibold text-cyan-700 italic tracking-wide">
                        Enriching...
                      </div>
                    </div>
                  </div>
                </div>
              }
              items={[
                {
                  key: '1',
                  label: `Total (${formatNumber(listEmployeePagination?.total_entries)})`,
                },
              ]}
            />
            <Table
              scroll={{
                x: 3000,
                y: '65vh',
              }}
              bordered
              loading={isLoadingEmployee}
              pagination={false}
              rowKey={(record) => record.id}
              columns={columnsPeople}
              dataSource={listEmployee}
              onRow={(record) => {
                return {
                  onClick: () => {
                    handleDetailContact(record);
                  },
                  style: { cursor: 'pointer' },
                };
              }}
              rowClassName="custom-row"
              className="custom-table"
            // rowSelection={rowSelection}
            />
          </div>
          <Pagination
            className="mt-3"
            defaultCurrent={listEmployeePagination?.page}
            total={
              listEmployeePagination?.total_entries < 50000
                ? listEmployeePagination?.total_entries
                : 50000
            }
            showSizeChanger={false}
            defaultPageSize={25}
            onChange={handlePaginationListEmployee}
            pageSize={25}
          />
        </Col>
      </Row>
    </div>
  );
}

export default ListEmailFinder;
