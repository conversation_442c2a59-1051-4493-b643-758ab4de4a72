import { useForm } from 'react-hook-form';
import BullhornSendEmailModal, { ADD_STEP_TYPE } from './EmailtriggerStep';
import { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import {
  getContactLists,
  getHotLists,
  getShortLists,
} from '../../containers/Sequence/SequenceDetail';

const ChildSequence = ({ parentSeqId, defaultChildSequence, companyId }) => {
  const [openModal, setOpenModal] = useState(false);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [numberStep, setNumberStep] = useState(0);
  const [emailConfigData, setEmailConfigData] = useState();
  const [notDiscard, setNotDiscard] = useState(true);
  const [emailSeqId, setEmailSeqId] = useState('');
  const [notConfirmStep, setConfirmStep] = useState(false);
  const [contactListSelected, setContactListSelected] = useState([]);

  const { handleSubmit, control, getValues, setValue, watch } = useForm({
    defaultValues : {
      companyId: companyId
    }
  });

  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  const handleParsingSequenceData = async (sequence) => {
    const stepsTemp = sequence?.steps;
    // Mapping participants in Linkedin Step
    const linkedinStepParticipantsTemp =
      getValues('linkedinStepParticipants') || [];
    const linkedinStepParticipants = [];

    const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
      if (
        step?.type === ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
        (step?.recipients?.length > 0 ||
          step?.contactListIds?.length > 0 ||
          step?.hotlistIds?.length > 0)
      ) {
        const linkedinStepParticipantsObject = {
          id: step?.id || uuid(),
          key: step?.key || uuid(),
          contactListIds: step?.contactListIds || [],
          hotlistIds: step?.hotlistIds || [],
          recipients: step?.recipients || [],
        };
        linkedinStepParticipants.push(linkedinStepParticipantsObject);
      }
      return [
        ...step?.delays.map((delayItem) => ({
          ...delayItem,
          unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
          key: uuid(),
        })),
        { ...step, key: step?.id || uuid() },
      ];
    });

    setEmailSeqId(defaultChildSequence?.id);

    setValue('linkedinStepParticipants', [
      ...linkedinStepParticipants,
      ...linkedinStepParticipantsTemp,
    ]);

    const participantsTemp =
      sequence?.sequence?.participants || sequence?.recipients?.[0] || null;
    const recipients = participantsTemp?.recipients || [];
    const mergedRecipients = [...recipients];

    if (participantsTemp) {
      const {
        contactListIds,
        hotlistIds,
        recipients: sendToList,
        shortListIds = [],
      } = participantsTemp;

      const list = await Promise.all([
        await getContactLists(contactListIds),
        await getHotLists(hotlistIds),
        await getShortLists(shortListIds),
      ]);

      setValue('participants', {
        contactList: list[0],
        hotList: list[1],
        shortList: list[2],
        sendTo: sendToList,
      });
    }
    // const inputNumberStepTemp = JSON.parse(sequence?.sequence?.rawSequence);
    setValue('sequenceData', sequence?.sequence);
    setValue('sequenceObject', inputNumberStepTemp);

    setInputNumberStep([...inputNumberStepTemp]);

    setValue(`sendMail.mailStepParentMailTo`, mergedRecipients ?? []);
    setValue(`sendMail.mailStepParent`, sequence?.mails?.[0]?.delay);
    setValue(`sendMail.mailStepParentContent`, sequence?.mails?.[0]?.content);
    setValue(`sendMail.mailStepParentSubject`, sequence?.mails?.[0]?.subject);
    setValue(
      'sendMail.listEmail',
      mergedRecipients?.map((item) => item.email)
    );
    setValue(
      'sendMail.listEmailSend',
      mergedRecipients?.map((item) => item.email)
    );
    setNumberStep(inputNumberStepTemp?.inputNumberStep);
  };

  useEffect(() => {
    handleParsingSequenceData(defaultChildSequence);
  }, [defaultChildSequence]);

  return (
    <div className="max-w-[25rem]">
      <BullhornSendEmailModal
        watch={watch}
        control={control}
        setValue={setValue}
        getValues={getValues}
        sendToEmail={getValues()?.email}
        mailTitle={getValues()?.jobtitle}
        openModalSendEmail={openModal}
        setOpenSendEmail={setOpenModal}
        listAddContactSelected={[]}
        setNumberStep={setNumberStep}
        numberStep={numberStep}
        inputNumberStep={inputNumberStep}
        setInputNumberStep={setInputNumberStep}
        setEmailConfigData={setEmailConfigData}
        emailConfigData={emailConfigData}
        newUpdatedSequence={true}
        setNotDiscard={setNotDiscard}
        notDiscard={notDiscard}
        onHandleSeqId={handleEmailSeqId}
        setContactListSelected={setContactListSelected}
        contactListSelected={contactListSelected}
        fromChildSequence={true}
        parentSeqId={parentSeqId}
        setConfirmStep={setConfirmStep}
        selectedSequenceValue={{
          ...defaultChildSequence?.sequence,
          currentNumberStepCount: defaultChildSequence?.currentNumberStepCount,
          ignoreParticipantsIds: defaultChildSequence?.ignoreParticipantsIds,
        }}
        emailSeqId={emailSeqId}
      />
    </div>
  );
};

export default ChildSequence;
