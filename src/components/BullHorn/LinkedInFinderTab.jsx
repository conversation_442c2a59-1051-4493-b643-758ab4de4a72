import {
  Button,
  Col,
  Collapse,
  Form,
  Input,
  Row,
  Select,
  Table,
  notification,
  Image,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  getDetailCompanyById,
} from '../../services/companyFinder';
import {
  LeftOutlined,
  LinkedinOutlined,
  RightOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import {
  searchLinkedInContact,
  searchLinkedInQueryParams,
} from '../../services/linkedInFinder';
import { getDataUniplieAccount } from '../../services/auth';

const OptionType = [
  // { label: 'LOCATION', value: 'LOCATION' },
  // { label: 'PEOPLE', value: 'PEOPLE' },
  // { label: 'COMPANY', value: 'COMPANY' },
  // { label: 'SCHOOL', value: 'SCHOOL' },
  // { label: 'INDUSTRY', value: 'INDUSTRY' },
  // { label: 'SERVICE', value: 'SERVICE' },
];

export const HENLEY = 'Henley';

const LinkedInFinderTab = () => {
  const [isLoading] = useState(false);
  const [listContactData, setListContactData] = useState([]);
  const [isLoadingContact, setIsLoadingContact] = useState(false);
  const [optionLocation, setOptionLocation] = useState([]);
  const [optionPeople, setOptionPeople] = useState([]);
  const [optionCompany, setOptionCompany] = useState([]);
  const [optionSchool, setOptionSchool] = useState([]);
  const [optionIndustry, setOptionIndustry] = useState([]);
  const [optionService, setOptionService] = useState([]);
  const [nextPageTokenArr, setNextPageTokenArr] = useState([null]);
  const [currentPage, setCurrentPage] = useState(0);
  const [nextPageToken, setNextPageToken] = useState();
  const [statusLinkedIn, setStatusLinkedIn] = useState(false);
  const [isLoadingCheckLinkedIn, setIsLoadingCheckLinkedIn] = useState(true);
  const [filterOption, setFilterOption] = useState({
    location: [],
    people: [],
    company: [],
    industry: [],
    service: [],
    school: [],
  });

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const [messageApi, contextHolder] = message.useMessage();

  const {
    handleSubmit,
    control,
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      searchSignalIds: [],
      accountStageIds: [],
      notAccountStageIds: [],
    },
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      // Handle debounce if needed
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      // Handle debounce if needed  
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      // Handle debounce if needed
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);

    switch (keys[0]) {
      case '6':
        refetchListSignals();
        break;
      case '7':
        refetchListFacets();
        break;
      default:
        break;
    }
  };

  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});
  const [detailOrganization, setDetailOrganization] = useState({});
  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };

  const handleGetParam = async (type, keywords, limit) => {
    try {
      const { data } = await searchLinkedInQueryParams({
        type,
        keywords,
        limit: 100,
      });
      return data?.result?.data;
    } catch (e) {
      console.log(e);
    }
  };

  const handleGetLocations = async (key) => {
    const data = await handleGetParam('LOCATION', key);
    setOptionLocation(data?.items);
  };

  const handleGetCompany = async (key) => {
    const data = await handleGetParam('COMPANY', key);
    setOptionCompany(data?.items);
  };

  const handleGetSchool = async (key) => {
    const data = await handleGetParam('SCHOOL', key);
    setOptionSchool(data?.items);
  };

  const handleGetIndustry = async (key) => {
    const data = await handleGetParam('INDUSTRY', key);
    setOptionIndustry(data?.items);
  };

  const handleGetService = async (key) => {
    const data = await handleGetParam('SERVICE', key);
    setOptionService(data?.items);
  };

  const handleStartData = async () => {
    const [LocationData, CompanyData, SchoolData, IndustryData, ServiceData] =
      await Promise.all([
        handleGetParam('LOCATION', 'a'),
        // handleGetParam("PEOPLE", "a"),
        handleGetParam('COMPANY', 'a'),
        handleGetParam('SCHOOL', 'a'),
        handleGetParam('INDUSTRY', 'a'),
        handleGetParam('DEPARTMENT', 'a'),
      ]);

    console.log(LocationData);

    setOptionLocation(LocationData?.items),
      setOptionCompany(CompanyData?.items),
      setOptionSchool(SchoolData?.items),
      setOptionIndustry(IndustryData?.items),
      setOptionService(ServiceData?.items);
    setOptionPeople([]);
  };

  useEffect(() => {
    handleStartData();
  }, []);

  const handlePaginationListCompany = (page) => {
    handleFindCompany({ page: page });
  };

  const handleGetDataEmail = async () => {
    try {
      messageApi.open({
        type: 'loading',
        content: 'Waiting some minutes to check your account',
        duration: 0,
      });
      const { data } = await getDataUniplieAccount();
      if (data.result.data.sources[0]?.status === 'OK') {
        setStatusLinkedIn(true)
        setIsLoadingCheckLinkedIn(false)
        messageApi.destroy();
      } else {
        messageApi.destroy();
        setStatusLinkedIn(false)
        notification.error({
          message: "Please link your LinkedIn Account in Settings to use this feature."
        })
      }
    } catch (e) {
      messageApi.destroy();
      setStatusLinkedIn(false)
      notification.error({
        message: "Please link your LinkedIn Account in Settings to use this feature."
      })
    }
  };

  useEffect(() => {
    handleGetDataEmail()
  }, [])

  console.log(333, statusLinkedIn)

  const handleFindCompany = async (page) => {
    try {
      const payload = {
        api: 'classic',
        category: 'people',
        ...(filterOption.company?.length > 0 && { company: filterOption.company }),
        ...(filterOption.industry?.length > 0 && { industry: filterOption.industry }),
        ...(filterOption.location?.length > 0 && { location: filterOption.location }),
        ...(filterOption.school?.length > 0 && { school: filterOption.school }),
        ...(filterOption.service?.length > 0 && { service: filterOption.service }),
      };
      setIsLoadingContact(true);
      const { data } = await searchLinkedInContact(payload, page);
      setListContactData(data?.result?.data?.items);
      setNextPageToken(data?.result?.data?.cursor);
      setNextPageTokenArr([...nextPageTokenArr, data?.result?.data?.cursor]);
      setIsLoadingContact(false);
    } catch (e) {
      notification.error({ message: 'Something went wrong' });
      setIsLoadingContact(false);
    }
  };

  const handleGetNextPage = async () => {
    setCurrentPage(+currentPage + 1);
    await handleFindCompany(nextPageToken);
  };

  const handleGetPrevPage = async () => {
    setCurrentPage(+currentPage - 1);
    const newArr = nextPageTokenArr.slice(0, nextPageTokenArr.length - 1);
    setNextPageTokenArr(newArr);
    // await handleGetDataEmail(nextPageTokenArr[+currentPage - 1], 10);
    await handleFindCompany(nextPageTokenArr[+currentPage - 1]);
  };

  const filterColumnCompany = () => {
    return (
      <div className='w-full flex flex-col gap-2 max-h-[70vh] overflow-y-auto'>
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['location']}
          items={[
            {
              key: 'location',
              label: (
                <div className="inline-grid">
                  <span>Location</span>
                  {watch('location') && watch('location')?.length > 0 && (
                    <Row>
                      {watch('location').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="Location" name="location">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        filterOption={false}
                        {...field}
                        onSearch={(e) => {
                          if (e) {
                            handleGetLocations(e);
                          }
                        }}
                        notFoundContent={null}
                        options={optionLocation.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            location: e?.map((item) => item.value),
                          });
                        }}
                      >
                        <Input />
                      </Select>
                    )}
                    name="location"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['people']}
          items={[
            {
              key: 'people',
              label: (
                <div className="inline-grid">
                  <span>People</span>
                  {watch('people') && watch('people')?.length > 0 && (
                    <Row>
                      {watch('people').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="People" name="people">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        {...field}
                        notFoundContent={null}
                        options={optionPeople.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        filterOption={false}
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            people: e?.map((item) => item.value),
                          });
                        }}
                      />
                    )}
                    name="people"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['company']}
          items={[
            {
              key: 'company',
              label: (
                <div className="inline-grid">
                  <span>Company</span>
                  {watch('company') && watch('company')?.length > 0 && (
                    <Row>
                      {watch('company').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="Company" name="company">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        {...field}
                        notFoundContent={null}
                        options={optionCompany.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        filterOption={false}
                        onSearch={(e) => {
                          if (e) {
                            handleGetCompany(e);
                          }
                        }}
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            company: e?.map((item) => item.value),
                          });
                        }}
                      >
                        <Input />
                      </Select>
                    )}
                    name="company"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['school']}
          items={[
            {
              key: 'school',
              label: (
                <div className="inline-grid">
                  <span>School</span>
                  {watch('school') && watch('school')?.length > 0 && (
                    <Row>
                      {watch('school').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="School" name="school">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        {...field}
                        notFoundContent={null}
                        options={optionSchool.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            school: e?.map((item) => item.value),
                          });
                        }}
                      />
                    )}
                    name="school"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['industry']}
          items={[
            {
              key: 'industry',
              label: (
                <div className="inline-grid">
                  <span>Industry</span>
                  {watch('industry') && watch('industry')?.length > 0 && (
                    <Row>
                      {watch('industry').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="Industry" name="industry">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        {...field}
                        notFoundContent={null}
                        options={optionIndustry.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            industry: e?.map((item) => item.value),
                          });
                        }}
                      />
                    )}
                    name="industry"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
        <Collapse
          style={{
            marginTop: "10px"
          }}
          expandIconPosition="end"
          defaultActiveKey={['service']}
          items={[
            {
              key: 'service',
              label: (
                <div className="inline-grid">
                  <span>Service</span>
                  {watch('service') && watch('service')?.length > 0 && (
                    <Row>
                      {watch('service').map((item, index) => (
                        <Col
                          key={index}
                          className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                        >
                          {item.label}
                        </Col>
                      ))}
                    </Row>
                  )}
                </div>
              ),
              children: (
                <Form.Item label="Service" name="service">
                  <Controller
                    render={({ field }) => (
                      <Select
                        labelInValue
                        mode="multiple"
                        {...field}
                        notFoundContent={null}
                        options={optionService.map((so) => ({
                          ...so,
                          label: so.title,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                        onChange={(e) => {
                          setFilterOption({
                            ...filterOption,
                            service: e?.map((item) => item.value),
                          });
                        }}
                      />
                    )}
                    name="service"
                    control={control}
                  />
                </Form.Item>
              ),
            },
          ]}
        />
      </div>
    );
  };

  const columnsPeople = [
    {
      title: 'avatar'.toUpperCase(),
      dataIndex: 'avatar',
      key: 'avatar',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => (
        <Image
          style={{
            width: 40,
            height: 40,
            objectFit: 'cover',
          }}
          src={record?.profile_picture_url}
        />
      ),
    },
    {
      title: 'name'.toUpperCase(),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => <div>{record?.name}</div>,
    },
    {
      title: 'profile Url'.toUpperCase(),
      dataIndex: 'profile_url',
      key: 'profile_url',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => (
        <a href={record.profile_url} target="_blank">
          {
            <LinkedinOutlined
              alt="Linkedin Icon"
              className="text-3xl text-[#0288d1]"
            />
          }
        </a>
      ),
    },
    {
      title: 'location'.toUpperCase(),
      dataIndex: 'location',
      key: 'location',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => <div>{record?.location}</div>,
    },
    {
      title: 'head line'.toUpperCase(),
      dataIndex: 'headline',
      key: 'headline',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => <div>{record?.headline}</div>,
    },
    {
      title: 'Action'.toUpperCase(),
      dataIndex: 'job_offers_count',
      key: 'job_offers_count',
      align: 'center',
      // width: '25%',
      render: (allowRead, record) => <div>{record?.job_offers_count}</div>,
    },
  ];

  const handleSubmitCompanyFind = async ({ page = 1 }) => {};

  const handleSubmitCompany = async (e) => {};

  return (
    <div>
      <Row>
        {contextHolder}
        <Col flex={'300px'}>
          <Row gutter={16}>
            <Form
              className="w-full pr-4"
              layout="vertical"
              onFinish={handleSubmit(handleSubmitCompanyFind)}
            >
              <Col className="w-full mr-4">
                <Form.Item
                  label="Search"
                  name="searchCompany"
                  className="mb-2 mt-2"
                >
                  <Controller
                    render={({ field }) => (
                      <Input
                        prefix={<SearchOutlined />}
                        {...field}
                        placeholder="Search Company ..."
                      />
                    )}
                    name="searchCompany"
                    control={control}
                  />
                </Form.Item>
              </Col>
            </Form>
            <Form
              className="w-full pr-4"
              layout="vertical"
              onFinish={handleSubmit(() => handleFindCompany())}
            >
              <Col className="w-full">
                {filterColumnCompany()}
              </Col>
              <Col className="w-full mr-4">
                <Button
                  disabled={isLoadingCheckLinkedIn || isLoading}
                  loading={isLoading}
                  htmlType="submit"
                  type="primary"
                  className={`flex ml-auto mt-0`}
                >
                  Search
                </Button>
              </Col>
            </Form>
          </Row>
        </Col>
        <Col flex="auto" className="w-2/3 search-table-new-design-container ">
          <Table
            scroll={{y: '65vh' }}
            loading={isLoadingContact}
            pagination={false}
            columns={columnsPeople}
            dataSource={listContactData}
            // onRow={(record, rowIndex) => {
            //   return {
            //     onClick: (e) => {
            //       console.log(23123);
            //       handleDetailCompany(record);
            //     },
            //     style: { cursor: 'pointer' },
            //   };
            // }}
          />
          <div style={{ marginTop: '20px' }}>
            <Button
              disabled={currentPage == 0 ? true : false}
              onClick={() => handleGetPrevPage()}
            >
              <LeftOutlined />
            </Button>
            <Button
              style={{ marginLeft: '10px' }}
              onClick={() => handleGetNextPage()}
            >
              <RightOutlined />
            </Button>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default LinkedInFinderTab;
