import { Button, Input, Modal, message, notification } from 'antd';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { DeleteOutlined, FlagOutlined } from '@ant-design/icons';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { updateSignatures } from '../../services/signatures';
import { replaceImageSrcsWithBase64 } from './SignaturesCreateForm';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from './BullhornSendEmailModal';

const SignaturesEditForm = (props) => {
  const {
    modalDetailSigData,
    modalDetailSig,
    setModalDetailSig,
    handleDeleteSig,
    handleUpdateDefault,
    setModalDetailSigData,
    replaceImagePlaceholders,
    removeSignaturesDefault,
  } = props;

  const [imageFiles, setImageFiles] = useState([]);
  const [dataSet, setData] = useState();
  const [contentData, setContentData] = useState();
  const { getValues, setValue } = useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [currentName, setCurrentName] = useState();
  const editorRef = useRef(null);
  const editor = useRef(null);
  const [dataContent, setDataContent] = useState();
  const replaceImgSrc = (htmlString, links) => {
    links?.forEach(function (item) {
      let regex = new RegExp('src="([^"]*)' + item.name + '([^"]*)"', 'g');
      htmlString = htmlString.replace(regex, 'src="' + item.link + '"');
    });

    return htmlString;
  };

  useEffect(() => {
    const result = modalDetailSigData?.sigContent;
    setContentData(result);
    setValue('name', modalDetailSigData?.name);
    editorRef?.current?.setContent(contentData ?? '');
    setCurrentName(modalDetailSigData?.name);
  }, [modalDetailSigData]);

  const b64toBlob = (
    b64Data,
    contentType = '',
    fileName = '',
    sliceSize = 512
  ) => {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters?.length; offset += sliceSize) {
      const slice = byteCharacters?.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice?.length);
      for (let i = 0; i < slice?.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  };

  const handelUpdated = async () => {
    let content = dataContent ?? modalDetailSigData?.sigContent;

    const reStylePTagsEl = document.createElement('div');
    reStylePTagsEl.innerHTML = content;

    // if (reStylePTagsEl.children.length > 0) {
    //   reStylePTagsEl?.children?.[0]?.classList?.add('signature-container');
    //   reStylePTagsEl?.children?.[0].setAttribute('style', '')
    // }

    const allPTagsEl = reStylePTagsEl.getElementsByTagName('p');
    for (let i = 0; i < allPTagsEl.length; i++) {
      allPTagsEl[i].style.margin = '0';
      // allPTagsEl[i].style.fontFamily = 'Arial';
      allPTagsEl[i].style.textWrapMode = 'nowrap';
      allPTagsEl[i].style.minWidth = 'min-content';
    }

    // const allDivTagsEl = reStylePTagsEl.getElementsByTagName('div');
    // for (let i = 0; i < allDivTagsEl.length; i++) {
    //   allDivTagsEl[i].style.fontFamily = 'Arial';
    // }

    // const allLiTagsEl = reStylePTagsEl.getElementsByTagName('li');
    // for (let i = 0; i < allLiTagsEl.length; i++) {
    //   allLiTagsEl[i].style.fontFamily = 'Arial';
    // }

    const allSpanTagsEl = reStylePTagsEl.getElementsByTagName('span');
    for (let i = 0; i < allSpanTagsEl.length; i++) {
      allSpanTagsEl[i].style.textWrapMode = 'nowrap';
    }

    const allImgTagsEl = reStylePTagsEl.getElementsByTagName('img');
    for (let i = 0; i < allImgTagsEl.length; i++) {
      allImgTagsEl[i].style.objectFit = 'contain';
    }

    content =
      reStylePTagsEl.getElementsByClassName('signature-container').length > 0
        ? reStylePTagsEl.innerHTML
        : `<div class="signature-container">${reStylePTagsEl.innerHTML} </div>`;
    const regexBase64 =
      /<img[^>]+src="data:image\/(?:png|jpeg);base64,([^">]+)"[^>]*>/g;
    const regexIdSrc =
      /<img[^>]+id="create-by-code-(\d+)"[^>]+src="([^">]+)"[^>]*>/g;

    const imageFilesArray = [];
    let match;

    try {
      content = await replaceImageSrcsWithBase64(content);
    } catch (e) {
      console.log(e);
      notification.error({
        message: 'Error',
        description:
          'Uploading image error. Please check your image again, we cannot get data from your image',
      });
      return;
    }

    while ((match = regexBase64.exec(content)) !== null) {
      const base64String = match[1];
      const base64Type = match[0].includes('png') ? 'png' : 'jpeg';
      const blob = b64toBlob(base64String, `image/${base64Type}`, 'customImg');
      imageFilesArray.push(blob);
    }

    while ((match = regexIdSrc.exec(content)) !== null) {
      const imageUrlRegex = /src="([^"]+)"/;
      const matchImageUrl = imageUrlRegex.exec(match[0]);
      if (matchImageUrl) {
        await fetch(matchImageUrl[1].replace(/&amp;/g, '&'))
          .then((response) => response.blob())
          .then((blob) => {
            const pngBlob = new Blob([blob], { type: 'image/png' });
            imageFilesArray.push(pngBlob);
          })
          .catch((error) => {
            console.error('Error fetching image:', error);
          });
      }
    }

    // Replace base64 images with placeholders
    let index = 1;
    let replacedText = content;
    // .replace(regexBase64, (match, p1) => {
    //   const replacedSrc = `{{IMAGE_${index}}}`;
    //   index++;
    //   return match
    //     .replace(`data:image/jpeg;base64,${p1}`, replacedSrc)
    //     .replace(`data:image/png;base64,${p1}`, replacedSrc);
    // });

    index = 1;
    replacedText = replacedText;
    // .replace(regexIdSrc, (match, p1, p2) => {
    //   const replacedSrc = `{{IMAGE_${index}}}`;
    //   index++;
    //   return match.replace(p2, replacedSrc);
    // });

    const formData = new FormData();
    formData.append('name', getValues('name') ?? modalDetailSigData?.name);
    formData.append('content', replacedText);
    imageFilesArray.forEach((img) => {
      formData.append('signature', img);
    });

    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading',
      });
      const { data } = await updateSignatures(modalDetailSigData?.id, formData);
      if (data) {
        notification.success({
          message: 'Success',
          description: 'Signature update successfully',
        });
        messageApi.destroy();
        setValue('name', '');
      }
    } catch (error) {
      messageApi.destroy();
      console.error('Error updating signature:', error);
    }
  };


  //----------------------------------------------------------------

  const handleCKEditorChange = async (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  //----------------------------------------------------------------

  return (
    <>
      {' '}
      {contextHolder}
      <Modal
        title={modalDetailSigData?.name}
        open={modalDetailSig}
        onOk={() => {}}
        onCancel={() => {
          setModalDetailSig(false);
        }}
        width={800}
        footer={false}
        style={{ height: '500px' }}
      >
        <div style={{ marginTop: '20px' }}>
          <label>Signature Name</label>
          <Input
            onChange={(e) => {
              setValue('name', e.target.value), setCurrentName(e.target.value);
            }}
            value={currentName}
          />
        </div>
        <div style={{ marginTop: '20px' }}></div>
        <label>Content</label>
        <div>
          <CKEditor
            editor={ClassicEditor}
            config={{
              ...editorConfig,
              toolbar: {
                ...editorConfig.toolbar,
                items: [...editorConfig.toolbar.items].filter(
                  (item) => item !== 'insertMergeField'
                ),
              },
              mergeFields: null
            }}
            data={modalDetailSigData?.sigContent || ''}
            onChange={handleCKEditorChange}
          />
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div></div>
          <div style={{ marginTop: '20px' }}>
            <>
              <Button
                onClick={handleDeleteSig}
                icon={<DeleteOutlined />}
                style={{
                  marginRight: '10px',
                  background: 'red',
                  color: '#fff',
                }}
              >
                Delete
              </Button>
              {!modalDetailSigData?.isDefault ? (
                <>
                  {' '}
                  <Button
                    onClick={handleUpdateDefault}
                    icon={<FlagOutlined />}
                    style={{ marginRight: '10px' }}
                  >
                    Set as Default
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    onClick={removeSignaturesDefault}
                    icon={<FlagOutlined />}
                    style={{ marginRight: '10px' }}
                  >
                    Remove default
                  </Button>
                </>
              )}
            </>
            <Button
              onClick={() => {
                setModalDetailSig(false);
                setModalDetailSigData(null);
              }}
              style={{ marginRight: '10px' }}
            >
              Cancel
            </Button>
            <Button onClick={handelUpdated} type="primary">
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SignaturesEditForm;
