import { useCallback, useEffect, useState } from 'react';
import DebounceSelect from '../../../common/DebounceSelect';
import { searchBullhornData } from '../../../services/bullhorn';
import deleteIcon from '../../../assets/img/icons/delete-icon.png';
import { Button, Image, Select, Spin } from 'antd';
import {
  CheckOutlined,
  EditOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { tagRender } from '../../SearchDetailV2/NewSearchFilterComponent';
import { debounce } from 'lodash';
import { arrayUniqueByKey } from '../../../utils/common';

const BHCompanySelect = ({
  setIsEditCompany,
  setShowCompany,
  setJobDetailCompanyId,
  functionContactClient,
  setValue,
  setHandleCloseClient,
  setIsAddCompany,
  setIsSearchCompany,
  getValues,
  handleChangeGetSimilar,
  functionCompany,
  isAddCompany,
  isSearchCompany,
  ...field
}) => {
  const [companyOptionsList, setCompanyOptionsList] = useState([]);
  const [companyLoading, setCompanyLoading] = useState(false);
  const [searchCompanyText, setSearchCompanyText] = useState('');
  const [pagination, setPagination] = useState({
    start: 0,
    limit: 500,
  });

  const getBHCompanyOptions = async (text) => {
    setCompanyOptionsList([]);
    setSearchCompanyText(text);
    functionCompany?.setCompanyValueNotFound(text);
    functionCompany?.companySetOptions([]);
    setCompanyLoading(true);
    try {
      const { data } = await searchBullhornData('ClientCorporation')(
        pagination.start,
        pagination.limit,
        text
      );
      setCompanyLoading(false);

      if (data?.result?.length > 0) {
        const options = data?.result?.map((item) => ({
          ...item,
          label: item?.name,
          value: item?.id,
          record: item?.address,
          email: item?.email,
        }));
        setCompanyOptionsList(options);
        return options;
      } else {
        setCompanyOptionsList([]);
      }
      //   return [];
    } catch (error) {
      setCompanyLoading(false);

      setCompanyOptionsList([]);
      console.log('error: ', error);
      //   return [];
    }
  };

  const notFoundContent = () => {
    <Button
      onClick={(e) => {
        setIsSearchCompany(false);
        setValue('companySelect', functionCompany.valueNotFoundCompany);
        setValue('companyId', null);
        setValue('company', functionCompany.valueNotFoundCompany);
        setValue(
          'clientCorporation.name',
          functionCompany.valueNotFoundCompany
        );
        setHandleCloseClient(false);
        setIsEditCompany(false);
        setShowCompany(true);
        setJobDetailCompanyId(true);
        setIsAddCompany(true);
      }}
      onMouseLeave={(e) => {
        setHandleCloseClient(false);
      }}
      className="flex gap-1 w-full h-fit bg-white customized-dropdown-background"
      value={functionCompany.valueNotFoundCompanyhCompanyText}
    >
      <PlusOutlined className="my-auto" />{' '}
      <span className="my-auto">Add new </span>{' '}
      {functionCompany.valueNotFoundCompany}
    </Button>;
  };

  useEffect(() => {
    if (getValues('company')) {
      getBHCompanyOptions(getValues('company'));
    }
  }, []);

  // Memoize the debounced function
  const debouncedGetBHCompanyOptions = useCallback(
    debounce((text) => getBHCompanyOptions(text), 500), // Adjust debounce duration as needed
    []
  );

  const defautlCompanyOptionsList = functionCompany?.companyOptions?.map(
    (item) => ({
      ...item,
      label: item?.name,
      value: item?.id,
    })
  );

  return (
    <Select
      placeholder="Search company..."
      optionFilterProp="label"
      className="w-full"
      mode="multiple"
      showSearch
      labelInValue
      filterOption={false}
      onSearch={debouncedGetBHCompanyOptions}
      notFoundContent={
        companyLoading ? (
          <div className="w-full p-4 flex items-center justify-center">
            <Spin size="small" />
          </div>
        ) : (
          notFoundContent()
        )
      }
      loading={companyLoading}
      options={
        companyLoading
          ? []
          : arrayUniqueByKey(
              [...companyOptionsList, ...(defautlCompanyOptionsList || [])],
              'value'
            )
      }
      onKeyDown={(e) => {
        setIsAddCompany(false);
        setIsSearchCompany(true);
        setValue('companySelect', null);
        setHandleCloseClient(true);
        setValue('companyId', null);
        setValue('company', '');
        functionContactClient.contactSetOptions([]);
        setValue('contactId', null);
        setValue('contact', null);
        setValue('contactSelect', null);
        setValue('email', '');
      }}
      optionRender={({ value }) => {
        const id = value;
        const option = [
          ...companyOptionsList,
          ...(defautlCompanyOptionsList || []),
        ].find((option) => option?.id == id);

        return (
          option && (
            <div className="grid">
              <div className="flex justify-between">
                <span className="text-base font-base">
                  {option.id} | {option.name}
                </span>
                <Button
                  className="absolute right-3 top-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditCompany(true);
                    setShowCompany(false);
                    setJobDetailCompanyId(false);
                    functionContactClient.contactSetStart(0);
                    setValue('companySelect', option.id);
                    setValue('companyId', option.id);
                    const companyName = option.name;
                    setValue('company', companyName);
                    functionContactClient.handleContactSearch('', option.id);
                    setHandleCloseClient(false);
                    setTimeout(() => {
                      setShowCompany(true);
                      setJobDetailCompanyId(false);
                    }, 100);
                  }}
                >
                  <EditOutlined />
                </Button>
              </div>
              <div className="contact-details">
                <div className="flex">
                  <span className="text-gray-500 text-xs min-w-[200px]">
                    <PhoneOutlined /> {option.phone ? option.phone : '-'}
                  </span>
                  <span className="text-gray-500 text-xs min-w-[200px]">
                    <InfoCircleOutlined /> {option.status ? option.status : '-'}
                  </span>
                  <span className="text-gray-500 text-xs min-w-[200px]">
                    <EnvironmentOutlined />
                    {option.address &&
                    option.address.city &&
                    option.address.state
                      ? `${option.address.city}, ${option.address.state}`
                      : option.address && option.address.city
                        ? option.address.city
                        : option.address && option.address.state
                          ? option.address.state
                          : '-'}
                  </span>
                </div>
              </div>
            </div>
          )
        );
      }}
      onSelect={async ({ value }, option) => {
        const selectedCompany = companyOptionsList?.find(
          (co) => co?.id == value
        );
        setValue(
          'city',
          selectedCompany?.address?.city || option?.record?.city
        );
        setValue(
          'county',
          selectedCompany?.address?.state || option?.record?.state
        );
        setValue(
          'countySelect',
          selectedCompany?.address?.state || option?.record?.state
        );
        setValue('zip', selectedCompany?.address?.zip || option?.record?.zip);
        setValue(
          'address1',
          selectedCompany?.address?.address1 || option?.record?.address1
        );
        setValue(
          'state',
          selectedCompany?.address?.countryName || option?.record?.countryName
        );
        setValue(
          'stateId',
          selectedCompany?.address?.countryID || option?.record?.countryID
        );
        setIsAddCompany(false);
        setJobDetailCompanyId(false);
        setIsSearchCompany(false);
        setIsEditCompany(true);
        setShowCompany(false);
        setJobDetailCompanyId(false);
        functionContactClient.contactSetStart(0);
        setValue('companyDetail', selectedCompany);
        setValue('companySelect', value);
        setValue('companyId', value);
        setValue('companySequenceContactId', value);
        setValue('company', selectedCompany?.name);
        functionContactClient?.handleContactSearch('', value);
        setHandleCloseClient(false);
        handleChangeGetSimilar &&
          handleChangeGetSimilar(
            value,
            getValues('jobtitle'),
            getValues('description')
          );
      }}
      tagRender={(props) =>
        tagRender(
          {
            ...props,
            tagColor:
              getValues('company') === getValues('companyDetail.name') ||
              getValues('companyId')
                ? 'tag-company-selected'
                : 'tag-company',
          },
          false
        )
      }
      dropdownRender={(menus) => {
        if (companyOptionsList?.[0]?.id === 0) {
          return (
            <Button
              onClick={(e) => {
                setIsSearchCompany(false);
                setValue('companySelect', functionCompany.valueNotFoundCompany);
                setValue('companyId', null);
                setValue('company', functionCompany.valueNotFoundCompany);
                setValue(
                  'clientCorporation.name',
                  functionCompany.valueNotFoundCompany
                );
                setHandleCloseClient(false);
                setIsEditCompany(false);
                setShowCompany(true);
                setJobDetailCompanyId(true);
                setIsAddCompany(true);
              }}
              onMouseLeave={(e) => {
                setHandleCloseClient(false);
              }}
              className="flex gap-1 w-full h-fit bg-white customized-dropdown-background"
              value={functionCompany.valueNotFoundCompany}
            >
              <PlusOutlined className="my-auto" />{' '}
              <span className="my-auto">Add new </span>{' '}
              {functionCompany.valueNotFoundCompany}
            </Button>
          );
        } else {
          return (
            <div
              onMouseLeave={(e) => {
                setHandleCloseClient(false);
              }}
            >
              <Button
                onClick={(e) => {
                  setIsSearchCompany(false);
                  setValue(
                    'companySelect',
                    functionCompany.valueNotFoundCompany
                  );
                  setValue('companyId', null);
                  setValue('company', functionCompany.valueNotFoundCompany);
                  setValue(
                    'clientCorporation.name',
                    functionCompany.valueNotFoundCompany
                  );
                  setHandleCloseClient(false);
                  setIsEditCompany(false);
                  setShowCompany(true);
                  setJobDetailCompanyId(true);
                  setIsAddCompany(true);
                }}
                className="flex gap-1 w-full h-fit bg-white customized-dropdown-background"
              >
                <PlusOutlined className="my-auto" />{' '}
                <span className="my-auto">Add new</span>{' '}
                {functionCompany.valueNotFoundCompany}
              </Button>
              {menus}
            </div>
          );
        }
      }}
      suffixIcon={
        companyLoading ? (
          <Spin />
        ) : getValues().companySelect !== '' && getValues().companySelect ? (
          <>
            <CheckOutlined className="text-green-600" />
            {(!isAddCompany && isSearchCompany) || getValues().companyId ? (
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditCompany(true);
                  setShowCompany(false);
                  setJobDetailCompanyId(false);
                  functionContactClient.contactSetStart(0);
                  functionContactClient.handleContactSearch(
                    '',
                    getValues().companyId
                  );
                  setHandleCloseClient(false);
                  setTimeout(() => {
                    setShowCompany(true);
                    setJobDetailCompanyId(true);
                    setIsAddCompany(false);
                  }, 100);
                }}
              >
                <EditOutlined />
              </Button>
            ) : (
              ''
            )}
          </>
        ) : (
          <Image
            src={deleteIcon}
            alt="Delete Icon"
            width="16px"
            height="16px"
            preview={false}
          />
        )
      }
      {...field}
    />
  );
};

export default BHCompanySelect;
