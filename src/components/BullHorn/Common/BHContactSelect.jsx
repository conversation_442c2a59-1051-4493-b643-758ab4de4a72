import { Button, Image, message, Select, Spin } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import { searchBullhornData } from '../../../services/bullhorn';
import { arrayUniqueByKey } from '../../../utils/common';
import {
  CheckOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { tagRender } from '../../SearchDetailV2/NewSearchFilterComponent';
import deleteIcon from '../../../assets/img/icons/delete-icon.png';

const BHContactSelect = ({
  functionContactClient,
  setValue,
  getValues,
  setListAddContactSelected,
  setHandleCloseContact,
  setIsAddContact,
  listAddContactSelected,
  handleEmailFinder,
  setStartCreateContact,
  functionCompany,
  watch,
  ...field
}) => {
  const [contactOptionsList, setContactOptionsList] = useState([]);
  const [contactLoading, setContactLoading] = useState(false);
  const [isLoadedAll, setLoadedAll] = useState(false);
  const [pagination, setPagination] = useState({
    start: 0,
    limit: 7,
  });

  const notFoundContact = () => (
    <>
      {contactLoading ? (
        <div className="flex justify-center items-center w-full h-20 customized-dropdown-background">
          <Spin />
        </div>
      ) : (
        <>
          <div className="py-2 w-full items-center justify-center customized-dropdown-background">
            <span className="italic">There are no contacts found.</span>
          </div>
        </>
      )}
    </>
  );

  const handleClear = () => {
    setListAddContactSelected([]);
    setValue('contactSelect', null);
    setHandleCloseContact(true);
    setValue('contactId', null);
    setValue('contact', null);
  };

  const getBHContactOptions = async (text, submitPagination = null) => {
    setLoadedAll(false);
    // setContactOptionsList([]);
    functionContactClient?.setContactValueNotFound(text);
    functionContactClient?.contactSetOptions([]);
    setContactLoading(true);
    try {
      const { data } = await searchBullhornData('ClientContact')(
        submitPagination?.start | pagination.start,
        submitPagination?.limit | pagination.limit,
        text,
        watch('companyId')
      );
      setContactLoading(false);

      if (data?.result?.length > 0) {
        const options = data?.result?.map((item) => ({
          ...item,
          label: item?.name,
          value: item?.id,
          record: item?.address,
          email: item?.email,
        }));
        setContactOptionsList([...contactOptionsList, ...options]);
      } else {
        setContactOptionsList([]);
      }
    } catch (error) {
      setContactLoading(false);
      setContactOptionsList([]);
      console.log('error: ', error);
    }
  };

  const handleScrollOptions = async (submitPagination = null) => {
    setContactLoading(true);
    try {
      const { data } = await searchBullhornData('ClientContact')(
        submitPagination?.start | pagination.start,
        submitPagination?.limit | pagination.limit,
        '',
        watch('companyId')
      );
      setContactLoading(false);

      if (data?.result?.length > 0) {
        const options = data?.result?.map((item) => ({
          ...item,
          label: item?.name,
          value: item?.id,
          record: item?.address,
          email: item?.email,
        }));
        setContactOptionsList([...contactOptionsList, ...options]);
        // return options;
      } else {
        setLoadedAll(true);
        message.info('No more contacts found');
        setContactOptionsList([...contactOptionsList]);
      }
      //   return [];
    } catch (error) {
      setContactLoading(false);
      setContactOptionsList([]);
      console.log('error: ', error);
      //   return [];
    }
  };

  useEffect(() => {
    if (watch('companyId')) {
      handleClear();
      getBHContactOptions();
    }
  }, [watch('companyId')]); // eslint-disable-line react-hooks/exhaustive-deps

  // Memoize the debounced function
  const debouncedGetBHCompanyOptions = useCallback(
    debounce((text) => getBHContactOptions(text), 500), // Adjust debounce duration as needed
    []
  );

  const defautlContactOptionsList = functionContactClient?.contactOptions?.map(
    (item) => ({
      ...item,
      label: item?.name,
      value: item?.id,
    })
  );

  return (
    <>
      <Select
        placeholder="Search Contact..."
        optionFilterProp="label"
        className="w-full"
        mode="multiple"
        showSearch
        labelInValue
        filterOption={false}
        onSearch={debouncedGetBHCompanyOptions}
        onPopupScroll={() => {
          if (isLoadedAll || contactLoading) return;
          setPagination((prev) => ({
            ...prev,
            start: prev.start + 7,
          }));
          handleScrollOptions({ start: pagination.start + 7 });
        }}
        notFoundContent={
          contactLoading ? (
            <div className="w-full p-4 flex items-center justify-center">
              {console.log('contactOptionsList', contactOptionsList)}
              <Spin size="small" />
            </div>
          ) : (
            notFoundContact()
          )
        }
        loading={contactLoading}
        options={arrayUniqueByKey(
          [...contactOptionsList, ...(defautlContactOptionsList || [])],
          'value'
        )}
        onKeyDown={(e) => {
          handleClear();
        }}
        optionRender={({ value }) => {
          const id = value;
          const option = [
            ...contactOptionsList,
            ...(defautlContactOptionsList || []),
          ].find((option) => option?.id == id);

          return (
            option && (
              <div className="grid">
                <span className="text-base">{option?.name}</span>
                <div className="contact-details">
                  <div className="flex">
                    <span className="text-gray-500 text-xs min-w-[200px]">
                      <MailOutlined />
                      {option.email ? option.email : '-'}
                    </span>
                    <span className="text-gray-500 text-xs min-w-[200px]">
                      <PhoneOutlined />
                      {option.phone ? option.phone : '-'}
                    </span>
                    <span className="text-gray-500 text-xs">
                      <ContactsOutlined />{' '}
                      {option.occupation ? option.occupation : '-'}
                    </span>
                  </div>
                  <div className="flex">
                    <span className="text-gray-500 text-xs min-w-[200px]">
                      <EnvironmentOutlined />
                      {option.address &&
                      option.address.city &&
                      option.address.state
                        ? `${option.address.city}, ${option.address.state}`
                        : option.address && option.address.city
                          ? option.address.city
                          : option.address && option.address.state
                            ? option.address.state
                            : '-'}
                    </span>
                    <span className="text-gray-500 text-xs min-w-[200px]">
                      <InfoCircleOutlined />{' '}
                      {option.status ? option.status : '-'}
                    </span>
                  </div>
                </div>
              </div>
            )
          );
        }}
        onSelect={async ({ value }, option) => {
          setIsAddContact(false);
          setValue('contactSelect', value);
          setValue('contactId', value);
          const contact =
            listAddContactSelected.length > 0 && listAddContactSelected?.[0]?.id
              ? listAddContactSelected.find((co) => co.id === value)
              : contactOptionsList.find((co) => co?.id === value);
          setValue('contact', contact?.name);
          setValue('email', contact.email);
          setHandleCloseContact(false);
          setValue('sendMail.listEmail', contact.email);
          setValue('sendMail.listEmailSend', [
            {
              ...contact,
              name: contact?.name,
              email: contact?.email,
            },
          ]);
        }}
        tagRender={(props) =>
          tagRender(
            {
              ...props,
              tagColor: getValues('contactId')
                ? 'tag-company-selected'
                : 'tag-company',
            },
            false
          )
        }
        dropdownRender={(menus) => {
          return (
            <div
              onMouseLeave={(e) => {
                setHandleCloseContact(false);
              }}
            >
              <Button
                onClick={(e) => {
                  setValue('contactId', null);
                  setValue(
                    'contact',
                    functionContactClient?.valueNotFoundContacts
                  );
                  setValue(
                    'contactSelect',
                    functionContactClient?.valueNotFoundContacts
                  );
                  setHandleCloseContact(false);
                  handleEmailFinder();
                  setValue('email', '');
                  setStartCreateContact(true);
                  setIsAddContact(true);
                }}
                className="flex gap-1 w-full h-fit bg-white customized-dropdown-background"
                value={functionContactClient?.valueNotFoundContacts}
              >
                <PlusOutlined className="my-auto" />{' '}
                <span className="my-auto">Add new</span>{' '}
                {functionContactClient?.valueNotFoundContacts}
              </Button>
              {menus}
              {contactLoading && contactOptionsList?.length > 0 && (
                <div className="w-full p-4 flex items-center justify-center">
                  <Spin />
                </div>
              )}
            </div>
          );
        }}
        suffixIcon={
          functionCompany.isLoadingContacts || contactLoading ? (
            <Spin />
          ) : getValues('contactId') ? (
            <CheckOutlined className="text-green-600" />
          ) : (
            <Image
              src={deleteIcon}
              alt="Delete Icon"
              width="16px"
              height="16px"
              preview={false}
            />
          )
        }
        {...field}
      />
    </>
  );
};

export default BHContactSelect;
