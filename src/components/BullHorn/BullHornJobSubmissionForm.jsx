/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable react/no-unknown-property */
/* eslint-disable react/jsx-key */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */

import _get from 'lodash/get';
import _find from 'lodash/find';

import { useForm, Controller } from 'react-hook-form';
import {
  Input,
  Switch,
  DatePicker,
  Select,
  Button,
  InputNumber,
  Form,
  Divider,
  AutoComplete,
  Modal,
  Spin,
  Table,
  Row,
  Col,
  Space,
  notification,
  ConfigProvider,
  Radio,
  Card,
  Pagination,
  Collapse,
  Tabs,
  Checkbox,
  List,
  Empty,
  Dropdown,
  Image,
  Popover,
  Flex,
  Typography,
  Tag,
  Segmented,
  Popconfirm,
  Tooltip,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import {
  searchBullhornData,
  getCounties,
  getCountries,
  searchBullhorn,
  insertB<PERSON><PERSON>,
  upladteB<PERSON>horn,
  getSimilarJob,
  deleteBullhornContact,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchAddressWithOpenStreet } from '../../services/googleMap';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  FileTextOutlined,
  BankFilled,
  StarOutlined,
  WalletOutlined,
  SettingOutlined,
  CheckOutlined,
  PlusOutlined,
  MailOutlined,
  CloseCircleOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  ContactsOutlined,
  EditOutlined,
  LinkedinOutlined,
  FacebookOutlined,
  TwitterOutlined,
  LinkOutlined,
  SearchOutlined,
  BankOutlined,
  PoundOutlined,
  CopyOutlined,
  CaretDownOutlined,
  MenuUnfoldOutlined,
  SendOutlined,
  SmallDashOutlined,
  DownOutlined,
  DeleteOutlined,
  FieldTimeOutlined,
  SnippetsOutlined,
  BookOutlined,
  HourglassOutlined,
  FireOutlined,
  UserOutlined,
  RocketOutlined,
  InsertRowLeftOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  employeeFinderSearchTag,
  getLDetailEmployee,
  getListCompany,
  getListEmployee,
  getListCompanies,
  employeeFinderIntentCategory,
  employeeFinderSearchSignals,
  getRevenue,
  getOrganizationsSnippet,
  getRecommendedContactList,
} from '../../services/employee';

import BullHornJobSubmissionCompany from './BullhornJobSubmissionCompany';
import BullhornSubmissionContact from './BullhornSubmissionContact';
import { getDetailCompanyById, getFacets } from '../../services/companyFinder';
import { Paper, TableContainer } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectConfigForm } from '../../store/common';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import BullhornSendEmail from './BullhornSendEmailModal';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import logo from '/logo_bull.webp';
import ModalShowListExitSequence from '../../containers/Sequence/ModalShowListExitSequence';
import ModalListUserGroup from '../../containers/Sequence/ModalListUserGroup';
import ExistingContactItems from './ExistingContactItems';
import { validListEmail } from '../../services/emailFinder';
import deleteIcon from '../../assets/img/icons/delete-icon.png';
import IntentCollap from './IntentCollap';
import { FaRegBuilding, FaRegUser } from 'react-icons/fa';
import { UserGroupIcon } from '../Sidebar/consts';
import BullhornBulkAddContactModal from './BullhornBulkAddContactModal';
import { tagRender } from '../SearchDetailV2/NewSearchFilterComponent';
import { numberWithCommas, removeTrackingParams } from '../../utils/common';
import {
  bulkUpdateLeadStatus,
  getStatusLeads,
  LEAD_STATUS_TYPE,
} from '../../services/jobLeadStatuses';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/free-regular-svg-icons';
import {
  faArrowsToDot,
  faBriefcase,
  faChartLine,
  faCodeBranch,
  faDollarSign,
  faEnvelopeCircleCheck,
  faEnvelopeOpen,
  faImagePortrait,
  faIndustry,
  faLocationDot,
  faMedal,
  faMicrochip,
  faMoneyBills,
  faTowerBroadcast,
  faUserGroup,
} from '@fortawesome/free-solid-svg-icons';
import CompanyDetailModal from './CompanyDetail';
import {
  createNewLeadSheet,
  getLeadSheetsByType,
  LEAD_SHEET_TYPE,
} from '../../services/leadSheet';
import { handleGenerateNotificationBulkAddContact } from '../../helpers/util';
import LinkedInFinderTab from './LinkedInFinderTab';
import { COMMON_STRINGS } from '../../constants/common.constant';
import {
  CONTACTS_LIST_PAGE_SIZE,
  initialListEmployeePagination,
} from './EmailFinderList';
import clsx from 'clsx';

// Icon source
import companyIcon from '../../assets/img/icons/company-icon.png';
import jobTypeIcon from '../../assets/img/icons/jobtype-icon.png';
import locationIcon from '../../assets/img/icons/location-icon.png';
import salaryIcon from '../../assets/img/icons/salary-icon.png';
import sourceIcon from '../../assets/img/icons/source-icon.png';
import BHCompanySelect from './Common/BHCompanySelect';
import BHContactSelect from './Common/BHContactSelect';
import { retriveFindCounty } from '../../constants/cities';

const debounceTimeout = 500;

export const formatNumber = (number) => {
  const suffixes = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
  let suffixIndex = 0;
  while (number > 1000) {
    number /= 1000;
    suffixIndex++;
  }
  return number >= 1000 ? number?.toFixed(1) + suffixes[suffixIndex] : number;
};

// const { Option } = Select;
const { Meta } = Card;
export const HENLEY = 'Henley';
function BullHornJobSubmissionForm({
  control,
  setValue,
  getValues,
  job,
  handleCloseClient,
  setHandleCloseClient,
  functionCompany,
  handleCloseContact,
  setHandleCloseContact,
  functionContactClient,
  handleOk,
  handleCancel,
  setPlaceId,
  isLoadingAddressDetails,
  handleGetDefaultAddress,
  isSearchCompany,
  setIsSearchCompany,
  jobDetailCompanyId,
  setJobDetailCompanyId,
  setShowCompany,
  showCompany,
  watch,
  isModalVisible,
  setHaveSendJob,
  haveSendJob,
  setIsLockedSendJob,
  isLockedSendJob,
  setCheckBoxStatus,
  checkBoxStatus,
  sendingStatus,
  // setSendingStatus,
  fromManualCreate = false,
  setLoadingSimilar,
  setSimilarList,
  similarList,
  searchData,
  defaultDataCompany = false,
  onClose = false,
}) {
  const configForm = useSelector(selectConfigForm);
  // Custom hooks for search with debounce
  const { handleSubmit } = useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalStaffOpen, setIsModalStaffOpen] = useState(false);
  const [isContactActionLoading, setContactActionLoading] = useState(false);
  const [startCreateContact, setStartCreateContact] = useState(false);
  const [currentContact, setCurrentContact] = useState();

  const [listEmployee, setListEmployee] = useState([]);
  const [listEmployeePagination, setListEmployeePagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [sequenceStatus, setSequenceStatus] = useState(false);
  const [listEmail, setListEmail] = useState([]);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [dataParentIntent, setDataParentIntent] = useState([]);
  const [listDataIntentSetting, setListDataIntentSetting] = useState([]);
  const [dataListTopicTitle, setDataListTopicTitle] = useState([]);
  const [dataTechnologies, setDataTechnologies] = useState([]);
  const [dataIndustryList, setDataIndustryList] = useState([]);
  const [dataEmployeeList, setDataEmployeeList] = useState([]);
  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [listDataFunding, setListDataFunding] = useState([]);
  const [fundingSize, setFundingSize] = useState(true);
  const [listCurrentOrg, setListCurrentOrg] = useState([]);
  const [showNotAnyOfCompany, setShowNotAnyOfCompany] = useState(false);
  const [showIncludeCompany, setShowIncludeCompany] = useState(false);
  const [showNotAnyOfCompanyTab, setShowNotAnyOfCompanyTab] = useState(false);

  const [listEmployeeCompany, setListEmployeeCompany] = useState([]);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
    useState({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });

  const [listCompanies, setListCompanies] = useState([]);
  const [listCompaniesPagination, setListCompaniesPagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isDetailEmployeCompany, setIsDetailEmployeeCompany] = useState(false);

  const [isEditCompany, setIsEditCompany] = useState(false);
  const [isAddCompany, setIsAddCompany] = useState(false);
  const [isAddContact, setIsAddContact] = useState(false);
  const [isAddContactForm, setIsAddContactForm] = useState(false);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [flagDetailContact, setFlagDetailContact] = useState(false);
  const [flagEditContact, setFlagEditContact] = useState(false);
  const [isSubmitForm, setIsSubmitForm] = useState(false);
  const [openExistingSequence, setOpenExistingSequence] = useState(false);
  const [openExistingUserGroup, setOpenExistingUserGroup] = useState(false);

  const [
    debouncedSearchTextCompanyPeople,
    setDebouncedSearchTextCompanyPeople,
  ] = useState('');
  const [debouncedSearchTextCompany, setDebouncedSearchTextCompany] =
    useState('');
  const [debouncedLocationFinderText, setDebouncedLocationFinderText] =
    useState('');
  const [
    debouncedLocationFindCompanyText,
    setDebouncedLocationFindCompanyText,
  ] = useState('');
  const [debouncedSearchTextIndustry, setDebouncedSearchTextIndustry] =
    useState('');
  const [
    debouncedSearchTextIndustryCompany,
    setDebouncedSearchTextIndustryCompany,
  ] = useState('');
  const [debouncedSearchTextTitle, setDebouncedSearchTextTitle] = useState('');
  const [listDetailEmployee, setListDetailEmployee] = useState([]);
  const [showIncludeKeywordPeople, setShowIncludeKeywordPeople] =
    useState(false);
  const [showIncludeAllKeywordPeople, setShowIncludeAllKeywordPeople] =
    useState(false);
  const [showExcludeKeywordsPeople, setShowExcludeKeywordsPeople] =
    useState(false);
  const [showIncludeKeywordCompany, setShowIncludeKeywordCompany] =
    useState(false);
  const [showIncludeAllKeywordCompany, setShowIncludeAllKeywordCompany] =
    useState(false);
  const [showExcludeKeywordsCompany, setShowExcludeKeywordsCompany] =
    useState(false);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [openModalSendEmailContact, setOpenSendEmailContact] = useState(false);
  const [sendEmailStatus, setSendEmailStatus] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [showModalAddTopic, setShowModalAddTopic] = useState(false);
  const [dataPersonTitle, setDataPersonTitle] = useState([]);
  const [dataLocation, setDataLocation] = useState([]);
  let searchTimer;

  // Lead Sheet Area
  const [leadSheets, setLeadSheets] = useState([]);
  const [newLeadSheetName, setNewLeadSheetName] = useState('');
  const [leadSheetLoading, setLeadSheetLoading] = useState(true);
  const inputLeadSheetRef = useRef(null);
  const [bulkContacts, setBulkContacts] = useState([]);

  const onNewLeadSheetNameChange = (event) => {
    setNewLeadSheetName(event.target.value);
  };

  const addNewLeadSheet = async (e) => {
    if (!newLeadSheetName?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }
    e.preventDefault();
    try {
      const payload = {
        name: `${COMMON_STRINGS.VACANCY}: ${newLeadSheetName}`,
        leadStatusType: LEAD_SHEET_TYPE.VACANCY,
      };
      setLeadSheetLoading(true);
      const { data } = await createNewLeadSheet(payload);

      if (data?.result) {
        setLeadSheets([{ ...data?.result }, ...leadSheets]);
        setNewLeadSheetName('');
      }

      setTimeout(() => {
        inputLeadSheetRef.current?.focus();
      }, 0);
      setLeadSheetLoading(false);
    } catch (error) {
      notification.error({
        description:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadSheet: ', error);
      setLeadSheetLoading(false);
    }
  };

  const onChangeLeadSheet = (value) => {
    setValue('leadSheetId', value);
  };

  const getLeadSheetOptions = async () => {
    try {
      const { data } = await getLeadSheetsByType(LEAD_SHEET_TYPE.VACANCY);
      if (data?.result?.length > 0) {
        setLeadSheets([...data?.result]);
      }
      setLeadSheetLoading(false);
    } catch (error) {
      console.log('Error in getLeadSheetOptions: ', error);
      setLeadSheetLoading(false);
    }
  };

  useEffect(() => {
    getLeadSheetOptions();
  }, []);

  // End Lead Sheet Area

  const getDataEmail = async () => {
    if (!job?.job_id) return;
    const { data } = await getEmailConfigInJobBoard(job.job_id);
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          key: index + 1,
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, newData?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails?.[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails?.[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails?.[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
    }
  };

  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    isLoading: isLoadingConsultants,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  useEffect(() => {
    setValue(
      'consultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'consultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
    setValue(
      'consultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
  }, [userToSet]);

  const {
    options: skillsOptions,
    handleScrollPopup: handleSkillScroll,
    handleSearch: setSkillSearchText,
    isLoading: isLoadingSkills,
  } = useInfiniteScrollWithSearch(searchBullhornData('Skill'));

  const {
    options: categoriesOptions,
    handlePopupScroll: handleCategoryScroll,
    handleSearch: setCategorySearchText,
    isLoading: isLoadingCategories,
  } = useInfiniteScrollWithSearch(searchBullhornData('Category'));

  const {
    options: industriesOptions,
    handleScrollPopup: handleIndustriesScroll,
    handleSearch: setIndustrySearchText,
    isLoading: isLoadingIndustries,
  } = useInfiniteScrollWithSearch(searchBullhornData('BusinessSector'));

  const {
    searchOptions: addressOptions,
    setSearchText: setAddressSearchText,
    isLoading: isLoadingAddresses,
  } = useSearchWithDebounce(searchAddressWithOpenStreet);

  const getRecommendedContacts = async (bodyToSearch) => {
    const filter = {
      company: getValues('companyFinder'),
      page: bodyToSearch?.page || 1,
      pageSize: bodyToSearch?.pageSize || 25,
      ...bodyToSearch,
    };
    const result = { items: [], count: 0 };
    try {
      const { data } = await getRecommendedContactList(filter);
      result.count = data?.result?.count || 0;

      if (data?.result?.items?.length > 0) {
        const recommendedContactList =
          data?.result?.items?.map((item) => ({
            ...item,
            isRecommended: true,
          })) || [];
        result.items = [...recommendedContactList];
        return result;
      } else {
        return result;
      }
    } catch (error) {
      return result;
    }
  };

  const handleGetMetaMode = async (body) => {
    const { data } = await getListEmployee({
      ...body,
      // metaMode: 'metadata_mode',
    });

    return data;
  };

  const handleFindEmails = async ({ page = 1 }) => {
    try {
      setListEmployee([]);
      const currentType = watch('keyAcordionPeople') || 0;
      setIsLoadingEmployee(true);
      let resultData = [];
      let bodyToSearch = null;
      let recommendedContactsTemp = [];
      let rcmContactsCount = 0;
      if (currentType !== '6') {
        const locations = getValues().locationFinder
          ? getValues().locationFinder.map((item) => item.label)
          : [];
        const personTitles = getValues().titleFinder
          ? getValues().titleFinder.map((item) => item.label)
          : [];
        const employeeRanges = getValues().employeeFinder
          ? getValues().employeeFinder.map((item) => item.value)
          : [];
        const industryTagIds = getValues().industryFinder
          ? getValues().industryFinder.map((item) => item.value)
          : [];
        const contactEmailStatus = getValues().contactEmailStatus
          ? getValues().contactEmailStatus
          : [];
        const contactEmailOpened = getValues().emailOpenedStatus ?? null;
        const contactEmailOpenedAtLeast =
          getValues().contactEmailOpenedTime ?? null;
        const contactEmailOpenedAtDateRangeMin =
          getValues().contactEmailOpenedTimeMin &&
          getValues().contactEmailOpenedTimeMin !== ''
            ? getValues().contactEmailOpenedTimeMin
            : null;
        const contactEmailOpenedAtDateRangeMax =
          getValues().contactEmailOpenedTimeMax &&
          getValues().contactEmailOpenedTimeMax !== ''
            ? getValues().contactEmailOpenedTimeMax
            : null;
        const intentStrengths = getValues().contactBuyingIntentScore
          ? getValues().contactBuyingIntentScore
          : [];
        const intentIds = getValues().contactBuyingIntentIds
          ? getValues().contactBuyingIntentIds
          : [];
        const searchSignalIds = getValues().searchSignalIds
          ? getValues().searchSignalIds
          : [];
        const recommendationScoresMinTranche = getValues().contactMinimumScore
          ? getValues().contactMinimumScore
          : null;
        const containOneKeywords = watch('includeKeywordPeople')
          ? watch('includeKeywordPeople').map((item) => item.label)
          : [];
        const containAllKeyWords = watch('includeAllKeywordPeople')
          ? watch('includeAllKeywordPeople').map((item) => item.label)
          : [];
        const excludeKeyWords = watch('excludeKeywordsPeople')
          ? watch('excludeKeywordsPeople').map((item) => item.label)
          : [];
        const organizationLatestFundingStageCd =
          getValues('fundingStatusItem') ?? [];
        const currentlyUsingAnyOfTechnologyUids =
          getValues('listTechnologies') ?? [];
        const notExistFields =
          getValues('revenueStatus') === 'is_un_known'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const existFields =
          getValues('revenueStatus') === 'is_know'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const organizationJobLocations = getValues('contactJobLocated') ?? [];
        const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
        const personName = getValues('nameFinderText') ?? null;
        const organizationNumJobsRangeMin =
          getValues('organizationNumJobsRangeMin') ?? null;
        const organizationNumJobsRangeMax =
          getValues('organizationNumJobsRangeMax') ?? null;
        const organizationJobPostedAtRangeMin =
          getValues('organizationJobPostedAtRangeMin') ?? null;
        const organizationJobPostedAtRangeMax =
          getValues('organizationJobPostedAtRangeMax') ?? null;
        const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
        const totalFundingRangeMin = getValues('fundingMin') ?? null;
        const totalFundingRangeMax = getValues('fundingMax') ?? null;
        const personPastOrganizationIds =
          getValues('companyFinderInclude') ?? [];
        const notOrganizationIds = getValues('companyFinderNotAnySelect') ?? [];
        bodyToSearch = {
          organizationId: watch('companyFinderId'),
          locations,
          personTitles,
          employeeRanges,
          industryTagIds,
          page,
          searchText: watch('searchPeople'),
          containOneKeywords,
          containAllKeyWords,
          excludeKeyWords,
          contactEmailStatus,
          contactEmailOpened,
          contactEmailOpenedAtLeast,
          contactEmailOpenedAtDateRangeMin,
          contactEmailOpenedAtDateRangeMax,
          intentStrengths,
          intentIds,
          searchSignalIds,
          recommendationScoresMinTranche,
          currentlyUsingAnyOfTechnologyUids,
          existFields,
          notExistFields,
          organizationTradingStatus,
          organizationLatestFundingStageCd,
          totalFundingRangeMax,
          totalFundingRangeMin,
          personName,
          organizationJobLocations,
          qOrganizationJobTitles,
          organizationNumJobsRangeMin,
          organizationNumJobsRangeMax,
          organizationJobPostedAtRangeMin,
          organizationJobPostedAtRangeMax,
          notOrganizationIds,
          personPastOrganizationIds,
        };
        if (
          bodyToSearch?.organizationId?.trim() ||
          bodyToSearch?.locations?.length > 0 ||
          bodyToSearch?.personTitles?.length > 0 ||
          bodyToSearch?.personName?.trim()
        ) {
          const { items, count } = await getRecommendedContacts(bodyToSearch);
          recommendedContactsTemp = [...items];
          rcmContactsCount += count;
        }

        if (recommendedContactsTemp.length < CONTACTS_LIST_PAGE_SIZE) {
          const rcmPageCost = Math.ceil(
            rcmContactsCount / CONTACTS_LIST_PAGE_SIZE
          );
          const { data } = await getListEmployee({
            ...bodyToSearch,
            page: page - rcmPageCost > 0 ? page - rcmPageCost : 1,
          });
          resultData = data;
        }
      } else if (currentType === '6') {
        if (bodyToSearch?.organizationId?.trim()) {
          const { items, count } = await getRecommendedContacts(bodyToSearch);
          recommendedContactsTemp = [...items];
          rcmContactsCount += count;
        }

        if (recommendedContactsTemp.length < CONTACTS_LIST_PAGE_SIZE) {
          const rcmPageCost = Math.ceil(
            rcmContactsCount / CONTACTS_LIST_PAGE_SIZE
          );
          const { data } = await getListEmployee({
            organizationId: watch('companyFinderId'),
            page: page - rcmPageCost > 0 ? page - rcmPageCost : 1,
          });
          resultData = data;
        }

        const people = resultData?.result?.people || [];
        const contacts = resultData?.result?.contacts || [];
        setListEmployeeCompany([...people, ...contacts]);
        setListEmployeePaginationCompany(resultData?.result?.pagination);
        setIsLoadingEmployee(false);
        return;
      }
      if (resultData.length === 0 && recommendedContactsTemp.length === 0)
        return dataEmployeeNotFound();
      const people = resultData?.result?.people || [];
      const contacts = resultData?.result?.contacts || [];
      const recommendedCOntactsList = [...recommendedContactsTemp];
      const listData =
        recommendedCOntactsList.length >= CONTACTS_LIST_PAGE_SIZE
          ? [...recommendedCOntactsList]
          : [...recommendedCOntactsList, ...people, ...contacts].splice(
              0,
              CONTACTS_LIST_PAGE_SIZE
            );
      setListEmployee(listData);
      setIsLoadingEmployee(false);
      const dataPagination = await handleGetMetaMode(bodyToSearch);
      const pagination =
        dataPagination?.result?.pagination ||
        resultData?.result?.pagination ||
        initialListEmployeePagination;
      const combinedPagination = {
        ...pagination,
        total_entries: +pagination?.total_entries + rcmContactsCount,
      };
      setListEmployeePagination({ ...combinedPagination });
      const listOrgIds = listData
        ?.map((item) => item?.organization_id)
        .filter((id) => id != null);

      // const uniqueOrgIds = [...new Set(listOrgIds)];
      // const dataOrg = await getOrganizationsSnippet({
      //   ids: uniqueOrgIds,
      // });

      // setListCurrentOrg(dataOrg.data.organizations);
    } catch (err) {
      setListEmployee([]);
      setListEmployeePagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingEmployee(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const handleFindCompany = async ({ page = 1 }) => {
    try {
      setIsDetailEmployeeCompany(false);
      setIsLoadingCompanies(true);
      const locations = getValues().locationFindCompany
        ? getValues().locationFindCompany.map((item) => item.label)
        : [];
      const employeeRanges = getValues().employeesFindCompany
        ? getValues().employeesFindCompany.map((item) => item.value)
        : [];
      const personTitles = getValues().titleFinder
        ? getValues().titleFinder.map((item) => item.label)
        : [];
      const industryTagIds = getValues().industryKeywordCompany
        ? getValues().industryKeywordCompany.map((item) => item.value)
        : [];
      const searchSignalIds = getValues().searchSignalIds
        ? getValues().searchSignalIds
        : [];
      const accountStageIds = getValues().accountStageIds
        ? getValues().accountStageIds
        : [];
      const notAccountStageIds = getValues().notAccountStageIds
        ? getValues().notAccountStageIds
        : [];
      const containOneKeywords = watch('includeKeywordCompany')
        ? watch('includeKeywordCompany').map((item) => item.label)
        : [];
      const containAllKeyWords = watch('includeAllKeywordCompany')
        ? watch('includeAllKeywordCompany').map((item) => item.label)
        : [];
      const excludeKeyWords = watch('excludeKeywordsCompany')
        ? watch('excludeKeywordsCompany').map((item) => item.label)
        : [];
      const currentlyUsingAnyOfTechnologyUids =
        getValues('listTechnologies') ?? [];
      const recommendationScoresMinTranche =
        getValues().contactMinimumScore ?? null;

      const intentStrengths = getValues().contactBuyingIntentScore
        ? getValues().contactBuyingIntentScore
        : [];
      const intentIds = getValues().contactBuyingIntentIds
        ? getValues().contactBuyingIntentIds
        : [];
      const notExistFields =
        getValues('revenueStatus') === 'is_un_known'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const existFields =
        getValues('revenueStatus') === 'is_know'
          ? ['organization_revenue_in_thousands_int']
          : null;
      const organizationJobLocations = getValues('contactJobLocated') ?? [];
      const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
      const organizationNumJobsRangeMin =
        getValues('organizationNumJobsRangeMin') ?? null;
      const organizationNumJobsRangeMax =
        getValues('organizationNumJobsRangeMax') ?? null;
      const organizationLatestFundingStageCd =
        getValues('fundingStatusItem') ?? [];
      const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
      const totalFundingRangeMin = getValues('fundingMin') ?? null;
      const totalFundingRangeMax = getValues('fundingMax') ?? null;
      const organizationJobPostedAtRangeMin =
        getValues('organizationJobPostedAtRangeMin') ?? null;
      const organizationJobPostedAtRangeMax =
        getValues('organizationJobPostedAtRangeMax') ?? null;
      const { data } = await getListCompanies({
        organizationId: watch('companyFindCompanyId'),
        locations,
        personTitles,
        employeeRanges,
        industryTagIds,
        page,
        searchText: watch('searchCompany'),
        containOneKeywords,
        containAllKeyWords,
        excludeKeyWords,
        searchSignalIds,
        accountStageIds,
        notAccountStageIds,
        recommendationScoresMinTranche,
        currentlyUsingAnyOfTechnologyUids,
        intentStrengths,
        intentIds,
        existFields,
        notExistFields,
        organizationTradingStatus,
        organizationLatestFundingStageCd,
        totalFundingRangeMax,
        totalFundingRangeMin,
        organizationJobLocations,
        qOrganizationJobTitles,
        organizationNumJobsRangeMin,
        organizationNumJobsRangeMax,
        organizationJobPostedAtRangeMin,
        organizationJobPostedAtRangeMax,
      });
      if (data?.length === 0) return dataCompanyNotFound();
      if (data?.organizations?.length === 0) return dataCompanyNotFound();
      setListCompanies(data?.organizations);
      setListCompaniesPagination(data?.pagination);
      setIsLoadingCompanies(false);
    } catch (err) {
      setListCompanies([]);
      setListCompaniesPagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingCompanies(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const dataEmployeeNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingEmployee(false);
  };

  const dataCompanyNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingCompanies(false);
  };

  const handleSubmitPeople = async ({ page = 1 }) => {
    if (watch('searchPeople') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindEmails({ page });
  };

  const handleSearchPeople = async () => {
    functionContactClient.setIsLoadingContacts(true);
    if (watch('searchExistsPeople') === '') {
      functionContactClient.handleContactSearch(' ');
    } else {
      functionContactClient.handleContactSearch(watch('searchExistsPeople'));
    }
    functionContactClient.setIsLoadingContacts(false);
  };

  const handleSubmitCompanyFind = async ({ page = 1 }) => {
    if (watch('searchCompany') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindCompany({ page });
  };

  const resetFormFindEmails = () => {
    setIsLoading(false);
  };

  const handlePaginationListEmployee = (page) => {
    handleFindEmails({ page: page });
  };

  const handlePaginationListCompany = (page) => {
    handleFindCompany({ page: page });
  };

  const handleAddContact = async (fullName, dataContact) => {
    setIsLoading(true);
    await handleSetDataContact(dataContact);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      dataContact?.email
    );
    if (data?.result.length === 0) {
      setIsLoading(false);
      setIsAddContactForm(true);
    } else {
      const isDataExist = listAddContactSelected?.some(
        (item) => item?.id === data?.result[0].id
      );
      if (!isDataExist) {
        setListAddContactSelected([
          ...listAddContactSelected,
          { ...data?.result[0] },
        ]);
      }
      functionContactClient?.handleContactSearch(data?.result[0].name, '');
      setIsLoading(false);
      setFlagEditContact(false);
      setIsAddContactForm(false);
      notification.success({
        message: `This contact already existed on Bullhorn`,
      });
    }
  };

  const handleEditContact = async (fullName, email) => {
    setIsLoading(true);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      email
    );
    if (data?.result.length === 0) {
      notification.success({
        message: `This contact not existed on Bullhorn`,
      });
    } else {
      handleSetDataEditContact(data.result[0]);
      // setIsLoading(false);
      // setIsAddContactForm(true);
    }
  };

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleSetDataEditContact = async (data) => {
    setDetailDataContact(data);
    setFlagEditContact(true);
  };

  const handleGetRevenue = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataRevenue(data?.faceting?.organization_trading_status_facets);
  };

  const handleGetFunding = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataFunding(data?.faceting?.latest_funding_stage_facets);
  };

  const handleSubmitAddContact = async () => {
    setIsSubmitForm(true);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      categories,
      company,
    } = getValues()?.clientContact;
    if (!companyId) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      setIsSubmitForm(false);
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Address is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (checkHenley ? skills.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (categories.length === 0) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    }

    // Based on format of BH
    const rawInformation = {
      ...(getValues()?.rawInformation?.[0] || {}),
      organization_name: company,
      first_name: getValues()?.clientContact?.firstName,
      last_name: getValues()?.clientContact?.surename,
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      owner_id: getValues()?.clientContact.consultantId,
    };

    if (
      address?.length > 100 ||
      getValues()?.clientContact?.address2?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      // lastName: getValues()?.clientContact?.lastname,
      lastName: getValues()?.clientContact?.surename,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        address2: getValues()?.clientContact?.address2,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      categories: {
        replaceAll: categories?.map((obj) => obj.value),
      },
      skills: {
        replaceAll: skills?.map((obj) => obj.value),
      },
      customText1: getValues()?.clientContact.linkedProfileUrl,
      skills: {
        replaceAll: getValues()?.clientContact?.skills?.map(
          (item) => item?.value
        ),
      },
      rawInformation,
    };

    if (
      getValues()?.clientContact?.address > 100 ||
      getValues()?.clientContact?.address2 > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newContactPayloadCleaned = cleanPayload(payload);
    let data;
    try {
      data = flagEditContact
        ? await upladteBullhorn(
            getValues()?.clientContact?.id,
            newContactPayloadCleaned
          )
        : await insertBullhorn(newContactPayloadCleaned);
    } catch (err) {
      notification.error(err);
      setIsSubmitForm(false);
    }
    functionContactClient.handleContactSearch('', getValues().companyId);
    const isDataExist = listAddContactSelected.some(
      (item) => item?.id === data?.result?.changedEntityId
    );
    if (!isDataExist) {
      setListAddContactSelected([
        ...listAddContactSelected,
        { ...data?.result?.data, id: data?.result?.changedEntityId },
      ]);
    }
    setIsSubmitForm(false);
    setIsLoading(false);
    setIsAddContactForm(false);
    notification.success({
      message: `Success ${flagEditContact ? 'edit' : 'add'} contact ${data?.result?.data?.name}`,
    });
    setFlagEditContact(false);
  };

  const handleResetFormAddContact = async () => {
    setValue('clientContact.namePrefixSelect', null);
    setValue('clientContact.namePrefix', null);
    setValue('clientContact.firstName', null);
    setValue('clientContact.middleName', null);
    setValue('clientContact.surename', null);
    setValue('clientContact.consultantId', null);
    setValue('clientContact.consultant', null);
    setValue('clientContact.consultantSelect', null);
    setValue('clientContact.statusSelect', null);
    setValue('clientContact.type', null);
    setValue('clientContact.secondaryOwnerSelect', null);
    setValue('clientContact.companyId', null);
    setValue('clientContact.companySelect', null);
    setValue('clientContact.company', null);
    setValue('clientContact.department', null);
    setValue('clientContact.jobTitle', null);
    setValue('clientContact.workEmail', null);
    setValue('clientContact.personalEmail', null);
    setValue('clientContact.workPhone', null);
    setValue('clientContact.mobilePhone', null);
    setValue('clientContact.otherPhone', null);
    setValue('clientContact.fax', null);
    setValue('clientContact.stateId', null);
    setValue('clientContact.state', null);
    setValue('clientContact.county', null);
    setValue('clientContact.countySelect', null);
    setValue('clientContact.address', null);
    setValue('clientContact.city', null);
    setValue('clientContact.zip', null);
    setValue('clientContact.industries', []);
    setValue('clientContact.generalCommets', null);
    setValue('clientContact.referredById', null);
  };

  const updateArrayByKey = (key, checked, optionKey) => {
    const newValues = checked
      ? [...(getValues(key) ?? []), optionKey]
      : (getValues(key) ?? [])?.filter((value) => value !== optionKey);

    setValue(key, newValues);
  };

  const handleDeleteIntent = async (value) => {
    const index = listDataIntentSetting.find((obj) => obj.id === value);
    if (index) {
      const newArr = listDataIntentSetting.filter((obj) => obj.id !== value);
      setListDataIntentSetting(newArr);
    }
  };

  const handleGetLocation = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'location',
    });
    setDataLocation(data?.tags);
  };

  const handleGetDetailEmployee = async (id) => {
    try {
      const { data } = await getLDetailEmployee({ employeeId: id });
      if (data) {
        setListDetailEmployee([...listDetailEmployee, { ...data }]);
        setIsLoading(true);
        setListEmployee([...listEmployee]);
        setListEmployeePagination({ ...listEmployeePagination });
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error in getting employee detail', error);
      if (error?.response?.status == 402) {
        notification.error({
          message:
            'Your account is running out of credits. Please contact to your admin for support!',
        });
      } else {
        notification.error({
          message: 'Something went wrong. Please try again in 5 minutes',
        });
      }
    }
  };

  const handleAccordionChangePeople = (keys) => {
    setValue('keyAcordionPeople', keys[0]);
    if (keys[0] === '10') {
      refetchListSignals();
    }
  };

  const checkboxGroups = {
    'Safe to send': [
      { key: 'likely_to_engage', label: 'Likely to engage' },
      { key: 'verified', label: 'Verified' },
    ],
    'Send with caution': [{ key: 'unverified', label: 'Unverified' }],
    'Do not send': [
      { key: 'update_required', label: 'Update required' },
      { key: 'unavailable', label: 'Unavailable' },
    ],
  };

  const handleGetIntentCategory = async () => {
    const data = await employeeFinderIntentCategory();
    setDataListTopicTitle(data.data.categories);
  };
  useEffect(() => {
    if (!onClose) {
      handleGetIntentCategory();
    }
  }, [showModalAddTopic]);

  const handleGetTechno = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'technology',
    });
    setDataTechnologies(data?.tags);
  };

  const resetAllPeople = () => {
    setValue('companyFinder', '');
    setValue('companyFinderId', '');
    setValue('locationFinderText', ' ');
    setValue('locationFinder', []);
    setValue('titleFinderText', ' ');
    setValue('titleFinder', []);
    setValue('employeeFinderText', '');
    setValue('employeeFinder', []);
    setValue('industryFinderText', ' ');
    setValue('industryFinder', []);
    setShowIncludeKeywordPeople(false);
    setShowIncludeAllKeywordPeople(false);
    setShowExcludeKeywordsPeople(false);
    setValue('includeKeywordPeople', []);
    setValue('includeKeywordPeopleText', '');
    setValue('includeAllKeywordPeople', []);
    setValue('includeAllKeywordPeopleText', '');
    setValue('excludeKeywordsPeople', []);
    setValue('excludeKeywordsPeopleText', '');
  };

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);

    switch (keys[0]) {
      case '6':
        refetchListSignals();
        break;
      case '7':
        refetchListFacets();
        break;
      default:
        break;
    }
  };

  const resetAllCompany = () => {
    setValue('companyFindCompany', '');
    setValue('companyFindCompanyId', '');
    setValue('locationFindCompanyText', ' ');
    setValue('locationFindCompany', []);
    setValue('employeesFindCompanyText', ' ');
    setValue('employeesFindCompany', []);
    setValue('industryKeywordCompanyText', ' ');
    setValue('industryKeywordCompany', []);
    setShowIncludeKeywordCompany(false);
    setShowIncludeAllKeywordCompany(false);
    setShowExcludeKeywordsCompany(false);
    setValue('includeKeywordCompany', []);
    setValue('includeKeywordCompanyText', '');
    setValue('includeAllKeywordCompany', []);
    setValue('includeAllKeywordCompanyText', '');
    setValue('excludeKeywordsCompany', []);
    setValue('excludeKeywordsCompanyText', '');
  };

  const handleSendEmail = () => {
    setSendEmailStatus(true);
  };

  useEffect(() => {
    if (!onClose) {
      setSendEmailStatus(false);
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      getDataEmail();
    }
  }, [openModalSendEmail]);

  const changeListEmail = (str) => {
    const index = listEmail.indexOf(str);
    if (index !== -1) {
      const newStrings = [...listEmail];
      newStrings.splice(index, 1);
      setListEmail(newStrings);
    } else {
      setListEmail([...listEmail, str]);
    }
  };

  const { data: listSignals, refetch: refetchListSignals } = useQuery(
    ['GET_LIST_SIGNALS'],
    async () => {
      const { data } = await employeeFinderSearchSignals();
      return data?.templates || [];
    },
    { enabled: false }
  );

  const EmailOpenedStatus = [
    { key: 0, value: 'yes', label: 'Yes' },
    { key: 0, value: 'no', label: 'No' },
  ];

  const OptionBuyIntent = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'mid' },
    { label: 'Low', value: 'low' },
    { label: 'None', value: 'none' },
  ];

  const handleChangeSettingIntent = async () => {
    setDataParentIntent(listDataIntentSetting);
    setShowModalAddTopic(false);
    localStorage.setItem(
      'listIntentChoose',
      JSON.stringify(listDataIntentSetting)
    );
  };

  useEffect(() => {
    const storedData = localStorage?.getItem('listIntentChoose');
    setDataParentIntent(storedData ? JSON.parse(storedData) : []);
    setListDataIntentSetting(storedData ? JSON.parse(storedData) : []);
    setValue('isOpen', true);
  }, []);

  const handleGetPersonalTitles = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'person_title',
    });
    setDataPersonTitle(data?.tags);
  };

  const { data: listFacets, refetch: refetchListFacets } = useQuery(
    ['GET_LIST_FACETS'],
    async () => {
      const { data } = await getFacets();
      return data?.faceting?.account_stage_facets || [];
    },
    { enabled: false }
  );

  const filterAddContact = [
    {
      key: '1',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBuilding}
              style={{ marginRight: '10px' }}
            />
            Company
          </span>
          {watch('companyFinderId') !== '' && watch('companyFinderId') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('companyFinder')}
            </span>
          )}
        </div>
      ),
      children: (
        // <Form.Item label="Company Name" name="companyFinder">
        //   <Controller
        //     render={({ field }) => (
        //       <AutoComplete
        //         style={{ width: '250px' }}
        //         {...field}
        //         options={companyListPeople.map((option) => ({
        //           value: option.id,
        //           label: (
        //             <div className="grid p-2">
        //               <div className="flex justify-between">
        //                 <span className="text-base font-base my-auto w-4/5 whitespace-normal">
        //                   {option.name}
        //                   <br />
        //                   <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
        //                     {option.domain || '-'}
        //                   </span>
        //                 </span>
        //                 <img
        //                   className="absolute right-3"
        //                   src={option?.logo_url ? `${option?.logo_url}` : ''}
        //                   width={50}
        //                   height={50}
        //                   alt="Logo"
        //                 />
        //               </div>
        //             </div>
        //           ),
        //         }))}
        //         onSearch={(value) => {
        //           setValue('companyFinder', value);
        //           setValue('companyFinderSelect', null);
        //           setValue('companyFinderId', null);
        //         }}
        //         onSelect={async (selectedCompanyId) => {
        //           const selectedCompany = companyListPeople.find(
        //             (ao) => ao.id == selectedCompanyId
        //           );
        //           setValue('companyFinder', selectedCompany.name);
        //           setValue('companyFinderSelect', selectedCompanyId);
        //           setValue('companyFinderId', selectedCompanyId);
        //         }}
        //       >
        //         <Input />
        //       </AutoComplete>
        //     )}
        //     name="companyFinder"
        //     control={control}
        //   />
        // </Form.Item>
        <div>
          <div
            style={{
              color: '#737373',
              fontSize: '14px',
              fontWeight: '600',
            }}
          >
            Company name
          </div>
          <Form.Item label="" name="companyFinder">
            <Controller
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={companyListPeople.map((option) => ({
                    value: option.id,
                    label: (
                      <div className="grid p-2">
                        <div className="flex justify-between">
                          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                            {option.name}
                            <br />
                            <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                              {option.domain || '-'}
                            </span>
                          </span>
                          <img
                            className="absolute right-3"
                            src={option?.logo_url ? `${option?.logo_url}` : ''}
                            width={50}
                            height={50}
                            alt="Logo"
                          />
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={(value) => {
                    setValue('companyFinder', value);
                    setValue('companyFinderSelect', null);
                    setValue('companyFinderId', null);
                  }}
                  onSelect={async (selectedCompanyId) => {
                    const selectedCompany = companyListPeople.find(
                      (ao) => ao.id == selectedCompanyId
                    );
                    setValue('companyFinder', selectedCompany.name);
                    setValue('companyFinderSelect', selectedCompanyId);
                    setValue('companyFinderId', selectedCompanyId);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
              name="companyFinder"
              control={control}
            />
          </Form.Item>

          <div>
            <Checkbox
              onChange={(e) => {
                setShowNotAnyOfCompany(!showNotAnyOfCompany);
              }}
            >
              Is not any of
            </Checkbox>
            {showNotAnyOfCompany && (
              <Form.Item label="" name="companyFinderNotAny">
                <Controller
                  render={({ field }) => (
                    <AutoComplete
                      style={{ width: '250px' }}
                      {...field}
                      options={companyListPeople.map((option) => ({
                        value: option.id,
                        label: (
                          <div className="grid p-2">
                            <div className="flex justify-between">
                              <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                {option.name}
                                <br />
                                <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                  {option.domain || '-'}
                                </span>
                              </span>
                              <img
                                className="absolute right-3"
                                src={
                                  option?.logo_url ? `${option?.logo_url}` : ''
                                }
                                width={50}
                                height={50}
                                alt="Logo"
                              />
                            </div>
                          </div>
                        ),
                      }))}
                      onSearch={(value) => {
                        setValue('companyFinderNotAny', value);
                        setValue('companyFinderNotAnySelect', null);
                        setValue('companyFinderNotAnyId', null);
                      }}
                      onSelect={async (selectedCompanyId) => {
                        const selectedCompany = companyListPeople.find(
                          (ao) => ao.id == selectedCompanyId
                        );
                        setValue('companyFinderNotAny', selectedCompany.name);
                        setValue('companyFinderNotAnySelect', [
                          selectedCompanyId,
                        ]);
                        setValue('companyFinderNotAnyId', [selectedCompanyId]);
                      }}
                    >
                      <Input />
                    </AutoComplete>
                  )}
                  name="companyFinderNotAny"
                  control={control}
                />
              </Form.Item>
            )}
          </div>
          <div>
            <Checkbox
              onChange={(e) => {
                setShowIncludeCompany(!showIncludeCompany);
              }}
            >
              Include past company
            </Checkbox>
            {showIncludeCompany && (
              <Form.Item label="" name="companyFinderInclude">
                <Controller
                  render={({ field }) => (
                    <AutoComplete
                      style={{ width: '250px' }}
                      {...field}
                      options={companyListPeople.map((option) => ({
                        value: option.id,
                        label: (
                          <div className="grid p-2">
                            <div className="flex justify-between">
                              <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                {option.name}
                                <br />
                                <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                  {option.domain || '-'}
                                </span>
                              </span>
                              <img
                                className="absolute right-3"
                                src={
                                  option?.logo_url ? `${option?.logo_url}` : ''
                                }
                                width={50}
                                height={50}
                                alt="Logo"
                              />
                            </div>
                          </div>
                        ),
                      }))}
                      onSearch={(value) => {
                        setValue('companyFinderInclude', value);
                        setValue('companyFinderIncludeSelect', null);
                        setValue('companyFinderIncludeId', null);
                      }}
                      onSelect={async (selectedCompanyId) => {
                        const selectedCompany = companyListPeople.find(
                          (ao) => ao.id == selectedCompanyId
                        );
                        setValue('companyFinderInclude', selectedCompany.name);
                        setValue('companyFinderIncludeSelect', [
                          selectedCompanyId,
                        ]);
                        setValue('companyFinderIncludeId', [selectedCompanyId]);
                      }}
                    >
                      <Input />
                    </AutoComplete>
                  )}
                  name="companyFinderInclude"
                  control={control}
                />
              </Form.Item>
            )}
          </div>
        </div>
      ),
    },
    {
      key: '2',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faLocationDot}
              style={{ marginRight: '10px' }}
            />{' '}
            Location
          </span>
          {watch('locationFinder') && watch('locationFinder')?.length > 0 && (
            <Row>
              {watch('locationFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Location Name" name="locationFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('locationFinderText') === '' ||
                  !watch('locationFinderText')
                    ? locationListPeople?.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('locationFinderText'),
                          value: watch('locationFinderText'),
                        },
                        ...locationListPeople?.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon icon={faMedal} style={{ marginRight: '10px' }} />
            Title
          </span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('titleFinderText'),
                          value: watch('titleFinderText'),
                        },
                        ...titleList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faUserGroup}
              style={{ marginRight: '10px' }}
            />
            Employees
          </span>
          {watch('employeeFinder') && watch('employeeFinder')?.length > 0 && (
            <Row>
              {watch('employeeFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Predefined Range" name="employeeFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (dataEmployeeList.length == 0) {
                    setValue('employeeFinderText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={dataEmployeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeeFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faIndustry}
              style={{ marginRight: '10px' }}
            />
            Industry & Keywords
          </span>
          {watch('industryFinder') && watch('industryFinder')?.length > 0 && (
            <Row>
              <span className="text-xs my-auto mr-2">Industry: </span>
              {watch('industryFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryFinder">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryFinderText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={dataIndustryList.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryFinder"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordPeople}
            onChange={(e) => {
              setShowIncludeKeywordPeople(e.target.checked);
              setValue('includeKeywordPeople', []);
              setValue('includeKeywordPeopleText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordPeople && (
            <Form.Item name="includeKeywordPeople">
              <Controller
                name="includeKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordPeopleText'),
                        label: watch('includeKeywordPeopleText'),
                        value: watch('includeKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordPeople}
            onChange={(e) => {
              setShowIncludeAllKeywordPeople(e.target.checked);
              setValue('includeAllKeywordPeople', []);
              setValue('includeAllKeywordPeopleText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordPeople && (
            <Form.Item name="includeAllKeywordPeople">
              <Controller
                name="includeAllKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordPeopleText'),
                        label: watch('includeAllKeywordPeopleText'),
                        value: watch('includeAllKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsPeople}
            onChange={(e) => {
              setShowExcludeKeywordsPeople(e.target.checked);
              setValue('excludeKeywordsPeople', []);
              setValue('excludeKeywordsPeopleText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsPeople && (
            <Form.Item name="excludeKeywordsPeople">
              <Controller
                name="excludeKeywordsPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsPeopleText'),
                        label: watch('excludeKeywordsPeopleText'),
                        value: watch('excludeKeywordsPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '9',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faChartLine}
              style={{ marginRight: '10px' }}
            />
            Buying Intent
          </span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '10',
      showArrow: false,
      label: (
        <>
          <Flex>
            <span>
              <FontAwesomeIcon
                icon={faTowerBroadcast}
                style={{ marginRight: '10px' }}
              />
              Signals
            </span>
            {watch('searchSignalIds') &&
              watch('searchSignalIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {watch('searchSignalIds')?.length}
                </Tag>
              )}
          </Flex>
        </>
      ),
      children: (
        <>
          <Form.Item name="searchSignalIds">
            <Controller
              name="searchSignalIds"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {listSignals?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value?.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      {option.modality === 'people' ? (
                        <Tag
                          className="flex items-center justify-center"
                          color="processing"
                          icon={<FaRegUser />}
                        />
                      ) : (
                        <Tag
                          className="flex items-center justify-center"
                          color="success"
                          icon={<FaRegBuilding />}
                        />
                      )}
                    </Flex>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '11',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faArrowsToDot}
              style={{ marginRight: '10px' }}
            />
            Score
          </span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '12',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMicrochip}
              style={{ marginRight: '10px' }}
            />
            Technologies
          </span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '13',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faDollarSign}
              style={{ marginRight: '10px' }}
            />
            Revenue
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '14',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMoneyBills}
              style={{ marginRight: '10px' }}
            />
            Funding
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '15',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faImagePortrait}
              style={{ marginRight: '10px' }}
            />
            Name
          </span>
          {watch('nameFinderText') !== '' && watch('nameFinderText') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('nameFinderText')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Name" name="nameFinder">
          <Controller
            render={({ field }) => (
              <Input
                placeholder="name"
                onChange={(e) => setValue('nameFinderText', e.target.value)}
              />
            )}
            name="nameFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '16',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBriefcase}
              style={{ marginRight: '10px' }}
            />
            Job Postings
          </span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const itemsDropdown = [
    {
      key: '1',
      label: <div>Edit</div>,
    },
    {
      key: '2',
      label: <div>Delete</div>,
    },
  ];

  const handleGenderSupportBar = (
    record,
    name = null,
    rawInformation = null
  ) => {
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              if (rawInformation) {
                setValue('rawInformation', [rawInformation]);
              }
              await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                      }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                        setValue('email', record?.email);
                        setValue('sendMail.mailStepParentMailTo', [
                          { email: record?.email, name: record?.name },
                        ]);
                        setValue('optionContactSelect', record);
                        setOpenSendEmailContact(true);
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <div>
                      {record?.email}{' '}
                      <CopyOutlined
                        style={{ marginLeft: '10px' }}
                        onClick={() => {
                          navigator.clipboard.writeText(record?.email),
                            notification.success({
                              message: 'Copy To Clipboard success',
                            });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => {
                        setOpenExistingUserGroup(true);
                        setCurrentContact(record);
                      }}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const filterCompany = [
    {
      key: '1',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBuilding}
              style={{ marginRight: '10px' }}
            />
            Company
          </span>
          {watch('companyFindCompanyId') !== '' &&
            watch('companyFindCompanyId') && (
              <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
                {watch('companyFindCompany')}
              </span>
            )}
        </div>
      ),
      children: (
        // <Form.Item label="Company Name" name="companyFindCompany">
        //   <Controller
        //     render={({ field }) => (
        //       <AutoComplete
        //         style={{ width: '250px' }}
        //         {...field}
        //         options={companyList.map((option) => ({
        //           value: option.id,
        //           label: (
        //             <div className="grid p-2">
        //               <div className="flex justify-between">
        //                 <span className="text-base font-base my-auto w-4/5 whitespace-normal">
        //                   {option.name}
        //                   <br />
        //                   <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
        //                     {option.domain || '-'}
        //                   </span>
        //                 </span>
        //                 <img
        //                   className="absolute right-3"
        //                   src={option?.logo_url ? `${option?.logo_url}` : ''}
        //                   width={50}
        //                   height={50}
        //                   alt="Logo"
        //                 />
        //               </div>
        //             </div>
        //           ),
        //         }))}
        //         onSearch={(value) => {
        //           setValue('companyFindCompany', value);
        //           setValue('companyFindCompanySelect', null);
        //           setValue('companyFindCompanyId', null);
        //         }}
        //         onSelect={async (selectedCompanyId) => {
        //           const selectedCompany = companyList.find(
        //             (ao) => ao.id == selectedCompanyId
        //           );
        //           setValue('companyFindCompany', selectedCompany.name);
        //           setValue('companyFindCompanySelect', selectedCompanyId);
        //           setValue('companyFindCompanyId', selectedCompanyId);
        //         }}
        //       >
        //         <Input />
        //       </AutoComplete>
        //     )}
        //     name="companyFindCompany"
        //     control={control}
        //   />
        // </Form.Item>
        <div>
          <div
            style={{
              color: '#737373',
              fontSize: '14px',
              fontWeight: '600',
            }}
          >
            Company name
          </div>
          <Form.Item name="companyFindCompany">
            <Controller
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={companyList.map((option) => ({
                    value: option.id,
                    label: (
                      <div className="grid p-2">
                        <div className="flex justify-between">
                          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                            {option.name}
                            <br />
                            <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                              {option.domain || '-'}
                            </span>
                          </span>
                          <img
                            className="absolute right-3"
                            src={option?.logo_url ? `${option?.logo_url}` : ''}
                            width={50}
                            height={50}
                            alt="Logo"
                          />
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={(value) => {
                    setValue('companyFindCompany', value);
                    setValue('companyFindCompanySelect', null);
                    setValue('companyFindCompanyId', null);
                  }}
                  onSelect={async (selectedCompanyId) => {
                    const selectedCompany = companyList.find(
                      (ao) => ao.id == selectedCompanyId
                    );
                    setValue('companyFindCompany', selectedCompany.name);
                    setValue('companyFindCompanySelect', selectedCompanyId);
                    setValue('companyFindCompanyId', selectedCompanyId);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
          <div>
            <Checkbox
              onChange={() => {
                setShowNotAnyOfCompanyTab(!showNotAnyOfCompanyTab);
              }}
            >
              Is not any of
            </Checkbox>
            {showNotAnyOfCompanyTab && (
              <Form.Item label="" name="companyFinderNotAny">
                <Controller
                  render={({ field }) => (
                    <AutoComplete
                      style={{ width: '250px' }}
                      {...field}
                      options={companyList.map((option) => ({
                        value: option.id,
                        label: (
                          <div className="grid p-2">
                            <div className="flex justify-between">
                              <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                                {option.name}
                                <br />
                                <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                                  {option.domain || '-'}
                                </span>
                              </span>
                              <img
                                className="absolute right-3"
                                src={
                                  option?.logo_url ? `${option?.logo_url}` : ''
                                }
                                width={50}
                                height={50}
                                alt="Logo"
                              />
                            </div>
                          </div>
                        ),
                      }))}
                      onSearch={(value) => {
                        setValue('companyFinderNotAny', value);
                        setValue('companyFinderNotAnySelect', null);
                        setValue('companyFinderNotAnyId', null);
                      }}
                      onSelect={async (selectedCompanyId) => {
                        const selectedCompany = companyList.find(
                          (ao) => ao.id == selectedCompanyId
                        );
                        setValue('companyFinderNotAny', selectedCompany.name);
                        setValue('companyFinderNotAnySelect', [
                          selectedCompanyId,
                        ]);
                        setValue('companyFinderNotAnyId', [selectedCompanyId]);
                      }}
                    >
                      <Input />
                    </AutoComplete>
                  )}
                  name="companyFinderNotAny"
                  control={control}
                />
              </Form.Item>
            )}
          </div>
        </div>
      ),
    },
    {
      key: '2',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faLocationDot}
              style={{ marginRight: '10px' }}
            />
            Location
          </span>
          {watch('locationFindCompany') &&
            watch('locationFindCompany')?.length > 0 && (
              <Row>
                {watch('locationFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Account Location" name="locationFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFindCompanyText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={locationListCompany.map((so) => ({
                  ...so,
                  label: so.cleaned_name,
                  value: so.id,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon icon={faMedal} style={{ marginRight: '10px' }} />
            Title
          </span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('titleFinderText'),
                          value: watch('titleFinderText'),
                        },
                        ...titleList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faUserGroup}
              style={{ marginRight: '10px' }}
            />
            Employees
          </span>
          {watch('employeesFindCompany') &&
            watch('employeesFindCompany')?.length > 0 && (
              <Row>
                {watch('employeesFindCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <Form.Item label="Employees" name="employeesFindCompany">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (dataEmployeeList.length == 0) {
                    setValue('employeesFindCompanyText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={dataEmployeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeesFindCompany"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faIndustry}
              style={{ marginRight: '10px' }}
            />
            Industry & Keywords
          </span>
          {watch('industryKeywordCompany') &&
            watch('industryKeywordCompany')?.length > 0 && (
              <Row>
                <span className="text-xs my-auto mr-2">Industry: </span>
                {watch('industryKeywordCompany').map((item, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {item.label}
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryKeywordCompany">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryKeywordCompanyText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={industryListCompany.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryKeywordCompany"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordCompany}
            onChange={(e) => {
              setShowIncludeKeywordCompany(e.target.checked);
              setValue('includeKeywordCompany', []);
              setValue('includeKeywordCompanyText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordCompany && (
            <Form.Item name="includeKeywordCompany">
              <Controller
                name="includeKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordCompanyText'),
                        label: watch('includeKeywordCompanyText'),
                        value: watch('includeKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordCompany}
            onChange={(e) => {
              setShowIncludeAllKeywordCompany(e.target.checked);
              setValue('includeAllKeywordCompany', []);
              setValue('includeAllKeywordCompanyText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordCompany && (
            <Form.Item name="includeAllKeywordCompany">
              <Controller
                name="includeAllKeywordCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordCompanyText'),
                        label: watch('includeAllKeywordCompanyText'),
                        value: watch('includeAllKeywordCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsCompany}
            onChange={(e) => {
              setShowExcludeKeywordsCompany(e.target.checked);
              setValue('excludeKeywordsCompany', []);
              setValue('excludeKeywordsCompanyText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsCompany && (
            <Form.Item name="excludeKeywordsCompany">
              <Controller
                name="excludeKeywordsCompany"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsCompanyText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsCompanyText'),
                        label: watch('excludeKeywordsCompanyText'),
                        value: watch('excludeKeywordsCompanyText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '6',
      showArrow: false,
      label: (
        <Flex>
          <span>
            <FontAwesomeIcon
              icon={faTowerBroadcast}
              style={{ marginRight: '10px' }}
            />
            Signals
          </span>
          {watch('searchSignalIds') && watch('searchSignalIds')?.length > 0 && (
            <Tag className="ml-auto" color="volcano">
              {watch('searchSignalIds')?.length}
            </Tag>
          )}
        </Flex>
      ),
      children: (
        <Form.Item name="searchSignalIds">
          <Controller
            name="searchSignalIds"
            control={control}
            render={({ field }) => (
              <Flex vertical gap={8}>
                {listSignals
                  ?.filter((item) => item.modality === 'companies')
                  ?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value?.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      <Tag
                        className="flex items-center justify-center"
                        color="success"
                        icon={<FaRegBuilding />}
                      />
                    </Flex>
                  ))}
              </Flex>
            )}
          />
        </Form.Item>
      ),
    },
    {
      key: '7',
      showArrow: false,
      label: (
        <Flex>
          <span>
            <FontAwesomeIcon
              icon={faCodeBranch}
              style={{ marginRight: '10px' }}
            />
            Stage
          </span>
          {(watch('accountStageIds') && watch('accountStageIds')?.length > 0) ||
            (watch('notAccountStageIds') &&
              watch('notAccountStageIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {(watch('accountStageIds')?.length || 0) +
                    (watch('notAccountStageIds')?.length || 0)}
                </Tag>
              ))}
        </Flex>
      ),
      children: (
        <Form.Item name="accountStageIds">
          <Controller
            name="accountStageIds"
            control={control}
            render={({ field }) => (
              <Flex vertical>
                <Typography.Title level={5}>
                  Include account stages
                </Typography.Title>
                {listFacets?.map((option, index, arr) => (
                  <Checkbox
                    key={`signal-${option.value}`}
                    checked={field?.value.includes(option.value)}
                    className={`pt-2 ${index !== arr.length - 1 ? 'border-b pb-2' : undefined}`}
                    onChange={(e) =>
                      updateArrayByKey(
                        'accountStageIds',
                        e.target.checked,
                        option.value
                      )
                    }
                  >
                    {option.display_name}
                  </Checkbox>
                ))}
                <Typography.Title className="mt-4" level={5}>
                  Exclude account stages
                </Typography.Title>
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%' }}
                  placeholder="Please select"
                  options={listFacets?.map((item) => ({
                    label: item.display_name,
                    value: item.value,
                  }))}
                  onChange={(values) => {
                    setValue('notAccountStageIds', values);
                  }}
                />
              </Flex>
            )}
          />
        </Form.Item>
      ),
    },
    {
      key: '8',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faArrowsToDot}
              style={{ marginRight: '10px' }}
            />
            Score
          </span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '9',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMicrochip}
              style={{ marginRight: '10px' }}
            />
            Technologies
          </span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '10',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faChartLine}
              style={{ marginRight: '10px' }}
            />
            Buying Intent
          </span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '11',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faDollarSign}
              style={{ marginRight: '10px' }}
            />
            Revenue
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '12',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faMoneyBills}
              style={{ marginRight: '10px' }}
            />
            Funding
          </span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '13',
      showArrow: false,
      label: (
        <div className="inline-grid">
          <span>
            <FontAwesomeIcon
              icon={faBriefcase}
              style={{ marginRight: '10px' }}
            />
            Job Postings
          </span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  const columnsCompany = [
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '200px',
      // width: '25%',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              className=""
              src={record?.logo_url ? `${record?.logo_url}` : ''}
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  setIsDetailEmployeeCompany(true);
                  const { data } = await getListEmployee({
                    organizationId: record.id,
                    page: 1,
                  });
                  if (data?.result.people.length === 0)
                    return notification.error({ message: 'Data Not Found' });
                  setValue('keyAcordionPeople', '6');
                  setValue('companyFinderId', record.id);
                  setListEmployeeCompany(data?.result?.people);
                  setListEmployeePaginationCompany(data?.result?.pagination);
                  setIsLoadingEmployee(false);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'estimated_num_employees',
      key: 'estimated_num_employees',
      align: 'left',
      // width: '25%',
      render: (allowRead, record) => (
        <Button
          type="text"
          className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
          onClick={async (e) => {
            e.stopPropagation();
            handleDetailCompany(record?.organization);
          }}
        >
          <UserGroupIcon className="w-4 h-4" />
          {record?.estimated_num_employees}
          <span>Employees</span>
        </Button>
        // <Row gutter={16} className="w-[5rem]">
        //   <p>{record?.estimated_num_employees}</p>
        // </Row>
      ),
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industries',
      key: 'industries',
      width: '25%',
      render: (allowRead, record) => {
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={record?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {record?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < record.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      width: '25%',
      render: (allowRead, record) => {
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={record?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < record.keywords.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {record?.keywords?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < record.keywords.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.LOCATION,
      dataIndex: 'location',
      key: 'location',
      width: '250px',
      render: (allowRead, record) => {
        return (
          <div
            style={{
              fontWeight: 600,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {record?.country} {record?.country ? `, ${record?.country}` : ''}
          </div>
        );
      },
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listSelectSection, setListSelectSection] = useState([]);

  const handleClickSignal = (id) => {
    if (!selectedRowKeys.includes(id)) {
      setSelectedRowKeys([...selectedRowKeys, id]);
    } else {
      setSelectedRowKeys(selectedRowKeys.filter((item) => item !== id));
    }
  };

  const handleCheckAll = () => {
    if (!listSelectSection.includes(listEmployeePagination.page)) {
      setListSelectSection([...listSelectSection, listEmployeePagination.page]);
      const data = listEmployee.map((item) => item.id);
      setSelectedRowKeys([...new Set([...selectedRowKeys, ...data])]);
    } else {
      const data = listEmployee.map((item) => item.id);
      const updatedArray = selectedRowKeys.filter((id) => !data.includes(id));
      setSelectedRowKeys(updatedArray);
      setListSelectSection(
        listSelectSection.filter((item) => item !== listEmployeePagination.page)
      );
    }
  };

  const columnsPeople = [
    {
      title: (
        <>
          <Checkbox
            disabled={listEmployee.length == 0}
            checked={listSelectSection.includes(listEmployeePagination?.page)}
            onChange={() => handleCheckAll()}
            indeterminate={
              selectedRowKeys.length > 0 &&
              selectedRowKeys.length < listEmployee.length &&
              listEmployee
                .map((item) => item.id)
                .filter((element) => selectedRowKeys.includes(element)).length >
                0
            }
          ></Checkbox>
        </>
      ),
      fixed: 'left',
      width: '50px',
      dataIndex: 'checkbox',
      key: 'checkbox',
      render: (allowWrite, record) => (
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <>
            <Checkbox
              checked={selectedRowKeys.includes(record.id)}
              onChange={(e) => {
                e.stopPropagation();
                handleClickSignal(record?.id);
              }}
            ></Checkbox>
          </>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.FULL_NAME,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '150px',
      render: (allowWrite, record) => (
        <>
          <Row gutter={16} className="full-name-cell-container">
            {record?.isRecommended && (
              <button
                title="Contact was audited by Zileo staff."
                class="button ant-btn-primary text-xs font-medium"
              >
                Recommended <StarOutlined />
              </button>
            )}
            <p className="font-semibold mr-2">{record?.name}</p>
            {record?.linkedin_url && (
              <Row gutter={16}>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.linkedin_url);
                    }}
                    title={`Access to linkedin account`}
                    className="cursor-pointer text-[#0288d1]"
                  />
                </Col>
              </Row>
            )}
          </Row>
        </>
      ),
    },
    {
      title: COMMON_STRINGS.JOB_TITLE,
      dataIndex: 'title',
      key: 'title',
      width: '400px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p>{record?.title}</p>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'organization.name',
      key: 'organization.name',
      width: '300px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              onClick={async (e) => {
                e.stopPropagation();
                handleDetailCompany(record?.organization);
              }}
              src={
                record.organization?.logo_url
                  ? `${record.organization?.logo_url}`
                  : ''
              }
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  handleDetailCompany(record?.organization);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.organization?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: '350px',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          {record?.email &&
          record?.email !== '<EMAIL>' ? (
            <>{handleGenderSupportBar(record)}</>
          ) : listDetailEmployee.some(
              (item) =>
                item.person_id === record.id ||
                item.person_id === record.person_id
            ) ? (
            listDetailEmployee
              .filter(
                (item) =>
                  item.person_id === record.id ||
                  item.person_id === record.person_id
              )
              .map((item, index) => (
                <>{handleGenderSupportBar(item, record?.name)}</>
              ))
          ) : (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleGetDetailEmployee(record.id || record.person_id);
              }}
              type="primary"
              icon={<MailOutlined />}
            >
              Access Email
            </Button>
          )}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone_numbers',
      key: 'phone_numbers',
      align: 'center',
      width: '150px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{ color: 'blue', textAlign: 'center' }}
        >
          <a href={`tel:${record?.phone_numbers?.[0]?.sanitized_number}`}>
            {record?.phone_numbers?.[0]?.sanitized_number}
          </a>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.CONTACT_LOCATION,
      dataIndex: 'contact-location',
      key: 'contact-location',
      align: 'left',
      width: '300px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{
            fontWeight: '600',
          }}
        >
          {record?.present_raw_address ||
            (record?.city ?? '') +
              (record?.city ? ', ' : '') +
              (record?.country ?? '')}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'employees',
      key: 'employees',
      width: '320px',
      align: 'center',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <Button
            type="text"
            className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
            onClick={async (e) => {
              e.stopPropagation();
              handleDetailCompany(record?.organization);
            }}
          >
            <UserGroupIcon className="w-4 h-4" />
            {items?.estimated_num_employees}
            <span>Employees</span>
          </Button>
        );
      },
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industry',
      key: 'industry',
      width: '350px',
      align: 'center',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <Tooltip
                placement="topLeft"
                title={items?.industries?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.industries.length - 1 && ', '}
                  </span>
                ))}
              >
                <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {items?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < items.industries.length - 1 && ', '}
                    </span>
                  ))}
                </span>
              </Tooltip>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      align: 'center',
      width: '300px',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <Tooltip
              placement="topLeft"
              title={items?.keywords?.map((item, index) => (
                <span key={index}>
                  {item}
                  {index < items.keywords.length - 1 && ', '}
                </span>
              ))}
            >
              <span
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                }}
              >
                {items?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items.keywords.length - 1 && ', '}
                  </span>
                ))}
              </span>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleAddAllEmail = () => {
    const listEmailToSave = functionContactClient.contactOptions.map((item) => {
      return item?.email;
    });
    setListEmail(listEmailToSave);
  };

  const handleValidEmail = async () => {
    setContactActionLoading(true);
    const listEmailToSave = functionContactClient.contactOptions
      .filter((item) => {
        return listEmail?.includes(item?.id);
      })
      .map((item) => item?.email);

    const { data } = await validListEmail({
      emails: listEmailToSave,
    });

    setContactActionLoading(false);
    setListEmailChecked(data?.result);
  };

  const columnsExistingContacs = [
    // {
    //   title: '',
    //   dataIndex: 'index',
    //   key: 'index',
    //   render: (text, record) => (
    //     <>
    //       <Checkbox
    //         checked={listEmail.includes(record?.email)}
    //         onChange={() => changeListEmail(record?.email)}
    //       ></Checkbox>
    //     </>
    //   ),
    // },
    {
      title: COMMON_STRINGS.NAME,
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="font-semibold">{text}</span>,
    },
    {
      title: COMMON_STRINGS.EMAIL,
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (text, record) => (
        <ExistingContactItems
          record={record}
          listEmailChecked={listEmailChecked}
        />
      ),
    },
    {
      title: COMMON_STRINGS.OCCUPATION,
      dataIndex: 'occupation',
      key: 'occupation',
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone',
      width: '15%',
      key: 'phone',
    },
    {
      title: COMMON_STRINGS.STATUS,
      width: '10%',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: COMMON_STRINGS.ACTION,
      key: 'action',
      align: 'center',
      render: (text, record) => (
        <Space size="small">
          <Popconfirm
            title="Delete the contact"
            description="Are you sure to delete this contact?"
            onConfirm={async () => {
              const { id } = record;
              try {
                const res = await deleteBullhornContact({
                  id,
                  entity: 'ClientContact',
                });
                const newContactOptions = [
                  ...functionContactClient.contactOptions.filter(
                    (contact) => contact?.id !== id
                  ),
                ];
                functionContactClient.contactSetOptions([...newContactOptions]);
                notification.success({
                  description: 'Contact deleted!',
                });
              } catch (error) {
                console.log('error delete contact: ', error);
                notification.error({
                  description: 'Network error! Try again later!',
                });
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button className="text-red-400 border-red-300">
              <DeleteOutlined />
            </Button>
          </Popconfirm>
          <Button
            disabled={isLoading}
            onClick={async () => {
              handleEditContact(record.name, record.email).finally(() => {
                setIsAddContactForm(true);
                setFlagEditContact(true);
                setIsLoading(false);
              });
            }}
          >
            <EditOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setListEmail(newSelectedRowKeys);
  };

  const rowTableExistingMailsSelection = {
    selectedRowKeys: listEmail,
    onChange: onSelectChange,
  };

  const [isAddContactFormBulk, setIsAddContactFormBulk] = useState(false);
  const [loadingGetBulkData, setLoadingBulkData] = useState(false);
  const [dataContacts, setDataContacts] = useState([]);
  const [isLoadingAddContactFormBulk, setIsLoadingAddContactFormBulk] =
    useState(false);

  // const onSelectChangeTable = (e) => {
  //   setSelectedRowKeys(e);
  // };

  // const rowSelection = {
  //   selectedRowKeys: selectedRowKeys,
  //   onChange: onSelectChangeTable,
  // };

  const itemsTableEmailFinder = [
    {
      key: '1',
      label: 'Existing Contacts',
      children: (
        <>
          <Row>
            <Col flex="100%">
              <Row gutter={16}>
                <div className="pb-4">
                  {/* <Button onClick={handleAddAllEmail}>Select all</Button> */}
                  {/* <Button
                    style={{ marginLeft: '10px' }}
                    onClick={handleValidEmail}
                  >
                    Validate Email
                  </Button> */}
                  <Button
                    loading={isContactActionLoading}
                    disabled={listEmail?.length === 0}
                    onClick={handleValidEmail}
                    type="primary"
                    className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                  >
                    <p className="Montserrat">
                      {`Validate ${listEmail?.length} Email(s)`}
                    </p>
                    <CheckOutlined />
                  </Button>
                </div>
                <TableContainer
                  className="search-table-new-design-container"
                  component={Paper}
                  style={{ maxHeight: '600px', overflowY: 'auto' }}
                  onScroll={(e) => {
                    functionContactClient.handleContactScroll(
                      e,
                      'ClientContact',
                      getValues().companyId
                    );
                  }}
                >
                  <Table
                    rowKey={(record) => record.id}
                    rowSelection={rowTableExistingMailsSelection}
                    columns={columnsExistingContacs}
                    dataSource={
                      functionContactClient?.valueNotFoundContacts
                        ? [
                            {
                              name: functionContactClient?.valueNotFoundContacts,
                            },
                            ...(functionContactClient.contactOptions || []),
                          ]
                        : functionContactClient.contactOptions || []
                    }
                    locale={{
                      emptyText: (
                        <Empty
                          description="No contacts found"
                          className="w-full"
                        />
                      ),
                    }}
                    pagination={false}
                    footer={() =>
                      functionContactClient.isLoadingContacts ? (
                        <Spin className="w-full mx-auto" />
                      ) : (
                        ''
                      )
                    }
                  />
                </TableContainer>
              </Row>
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: '2',
      label: 'People',
      children: (
        <div>
          <div style={{ float: 'right' }}>
            {selectedRowKeys?.length > 0 && (
              <div>
                <Dropdown
                  className="mb-4 animated fadeInDownBig"
                  placement="bottom"
                  arrow
                  menu={{
                    items: [
                      {
                        key: 'delete-searchs',
                        label: (
                          <a
                            className="Montserrat flex gap-2 items-center py-2"
                            onClick={(e) => {
                              e.preventDefault();
                              setIsAddContactFormBulk(true);
                            }}
                          >
                            <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
                          </a>
                        ),
                      },
                      {
                        key: 'bulk-add-contact-lists',
                        label: (
                          <a
                            className="Montserrat flex gap-2 items-center py-2"
                            onClick={async (e) => {
                              e.preventDefault();
                              const { accessedContacts, lockedAccessContacts } =
                                listEmployee.reduce(
                                  (acc, item) => {
                                    if (!selectedRowKeys.includes(item.id)) {
                                      return acc;
                                    }
                                    if (
                                      item.email &&
                                      item.email !==
                                        '<EMAIL>'
                                    ) {
                                      acc.accessedContacts.push(item);
                                    } else {
                                      acc.lockedAccessContacts.push(item);
                                    }

                                    return acc;
                                  },
                                  {
                                    accessedContacts: [],
                                    lockedAccessContacts: [],
                                  }
                                );
                              if (lockedAccessContacts.length > 0) {
                                setIsLoadingEmployee(true);
                                const requestAccessContacts = [];
                                for (let lockedAccessContact of lockedAccessContacts) {
                                  try {
                                    const { data } = await getLDetailEmployee({
                                      employeeId: lockedAccessContact.id,
                                    });
                                    accessedContacts.push(data);
                                    requestAccessContacts.push(data);
                                  } catch (error) {
                                    // Do nothing
                                  }
                                }
                                if (requestAccessContacts.length) {
                                  const requestAccessContactIds =
                                    requestAccessContacts.map(
                                      (item) => item.id
                                    );
                                  setListDetailEmployee([
                                    ...listDetailEmployee.filter(
                                      (item) =>
                                        !requestAccessContactIds.includes(
                                          item.id
                                        )
                                    ),
                                    ...requestAccessContacts,
                                  ]);
                                  setListEmployee([
                                    ...listEmployee.filter(
                                      (item) =>
                                        !requestAccessContactIds.includes(
                                          item.id
                                        )
                                    ),
                                    ...requestAccessContacts,
                                  ]);
                                }
                                setIsLoadingEmployee(false);
                              }

                              if (accessedContacts.length === 0) {
                                notification.error({
                                  message:
                                    'Please choose at least one contact who has accessed email!',
                                });

                                return;
                              }

                              setOpenExistingUserGroup(true);
                              setCurrentContact();
                              setBulkContacts(accessedContacts);
                            }}
                          >
                            <span>
                              {COMMON_STRINGS.BULK_ADD_TO_CONTACT_LIST}
                            </span>
                          </a>
                        ),
                      },
                    ],
                  }}
                >
                  <Space>
                    <Button
                      type="primary"
                      className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                    >
                      <p className="Montserrat">
                        {`${selectedRowKeys?.length} Selected`}
                      </p>
                      <DownOutlined />
                    </Button>
                  </Space>
                </Dropdown>
              </div>
            )}
          </div>
          <div style={{ clear: 'both' }}></div>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleSubmitPeople)}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchPeople"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            addonBefore={<SearchOutlined />}
                            {...field}
                            placeholder="Search People ..."
                          />
                        )}
                        name="searchPeople"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindEmails)}
                >
                  <Col className="w-full customize-collapse">
                    <Form.Item label="Filter" className={'custom-collapse'}>
                      <Collapse
                        accordion
                        items={filterAddContact}
                        onChange={handleAccordionChangePeople}
                      />
                    </Form.Item>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      <span className={isLoading ? 'text-black' : ''}>
                        Search
                      </span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            <Col
              flex="auto"
              className="w-2/3 search-table-new-design-container"
            >
              <Tabs
                defaultActiveKey="1"
                items={[
                  {
                    key: '1',
                    label: `Total (${formatNumber(listEmployeePagination.total_entries)})`,
                  },
                ]}
              />
              <div className="customTable">
                <Table
                  // scroll={{ x: true, y: 360 }}
                  scroll={{
                    x: 3000,
                    y: '67vh',
                  }}
                  rowKey={(record) => record.id}
                  loading={isLoadingEmployee}
                  pagination={false}
                  columns={columnsPeople}
                  dataSource={listEmployee}
                  onRow={(record, rowIndex) => {
                    return {
                      onClick: () => {
                        handleDetailContact(record);
                      },
                      style: { cursor: 'pointer' },
                    };
                  }}
                  rowClassName="custom-row"
                  className="custom-table"
                  // rowSelection={rowSelection}
                />
              </div>
              <Pagination
                className="mt-3"
                defaultCurrent={listEmployeePagination.page}
                total={listEmployeePagination.total_entries}
                showSizeChanger={false}
                onChange={handlePaginationListEmployee}
              />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: '3',
      label: 'Companies',
      children: (
        <>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleSubmitCompanyFind)}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchCompany"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            addonBefore={<SearchOutlined />}
                            {...field}
                            placeholder="Search Company ..."
                          />
                        )}
                        name="searchCompany"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit(handleFindCompany)}
                >
                  <Col className="w-full">
                    <Form.Item label="Filter" className={'custom-collapse'}>
                      <Collapse
                        accordion
                        items={filterCompany}
                        onChange={handleAccordionChangeCompany}
                      />
                    </Form.Item>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      disabled={isLoading}
                      loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      <span className={isLoading ? 'text-black' : ''}>
                        Search
                      </span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            {isDetailEmployeCompany ? (
              <Col
                flex="auto"
                className="w-2/3 search-table-new-design-container customTable"
              >
                <Tabs
                  defaultActiveKey="1"
                  items={[
                    {
                      key: '1',
                      label: `Total (${formatNumber(listCompaniesPagination.total_entries)})`,
                    },
                  ]}
                />
                <Table
                  // scroll={{ x: true, y: 360 }}
                  scroll={{
                    x: 3000,
                  }}
                  loading={isLoadingEmployee}
                  pagination={false}
                  columns={columnsPeople}
                  dataSource={listEmployeeCompany}
                />
                <Pagination
                  className="mt-3"
                  defaultCurrent={listEmployeePaginationCompany.page}
                  total={listEmployeePaginationCompany.total_entries}
                  showSizeChanger={false}
                  onChange={handlePaginationListEmployee}
                />
              </Col>
            ) : (
              <Col
                flex="auto"
                className="w-2/3 search-table-new-design-container"
              >
                <div className="customTable">
                  <Tabs
                    defaultActiveKey="1"
                    items={[
                      {
                        key: '1',
                        label: `Total (${formatNumber(listCompaniesPagination.total_entries)})`,
                      },
                    ]}
                  />
                  <Table
                    // scroll={{ x: true, y: 360 }}
                    scroll={{
                      x: 1500,
                      y: '65vh',
                    }}
                    loading={isLoadingCompanies}
                    pagination={false}
                    columns={columnsCompany}
                    dataSource={listCompanies}
                    onRow={(record, rowIndex) => {
                      return {
                        onClick: () => {
                          handleDetailCompany(record);
                        },
                        style: { cursor: 'pointer' },
                      };
                    }}
                  />
                </div>
                <Pagination
                  className="mt-3"
                  defaultCurrent={listCompaniesPagination.page}
                  total={listCompaniesPagination.total_entries}
                  showSizeChanger={false}
                  onChange={handlePaginationListCompany}
                />
              </Col>
            )}
          </Row>
        </>
      ),
    },
    {
      key: 4,
      label: 'Linkedin People',
      children: <LinkedInFinderTab />,
    },
  ];

  // START DETAIL CONTACT
  const [showDetailContact, setShowDetailContact] = useState(false);
  const [detailContactStaff, setDetailContactStaff] = useState({});
  const [detailOrganization, setDetailOrganization] = useState({});
  const [showFullText, setShowFullText] = useState(false);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const handleDetailContact = async (record) => {
    setShowDetailContact(true);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.organization?.id,
      });
      setDetailOrganization(data?.organization);
      setDetailContactStaff(record);
    }
  };
  // END DETAIL CONTACT

  // START DETAIL COMPANY
  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});
  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };
  // END DETAIL COMPANY

  // useMemo(() => {
  //   if (getValues()?.joblocationcity)
  //     handleGetDefaultAddress(getValues()?.joblocationcity);
  // }, [job, getValues()?.joblocationcity]);

  useEffect(() => {
    resetFormFindEmails();
    setValue('companyFinder', getValues().company);
    setValue('fullName', '');
    setIsSubmitForm(false);
  }, [isModalOpen]);

  const { data: counties = [], refetch: refetchCounty } = useQuery(
    ['GET_COUNTIES'],
    async () => {
      // if (watch('stateId') == 2378) {
      //   return counties
      // }
      const { data: dataCounties } = await getCounties({
        filter: '',
        countryId: watch('stateId'),
      });
      return dataCounties?.result;
    },
    // {
    //   enabled: !!watch('stateId') && watch('stateId') !== 2378 && !onClose,
    // }
  );

  // Update refetch county

  const [listCountySelect, setListCountySelect] = useState()
  const [loadingListCountySelect, setLoadingListCountySelect] = useState(false)

  const handleRefetchCountry = async (stateId) => {
    setLoadingListCountySelect(true)
     const { data: dataCounties } = await getCounties({
        filter: '',
        countryId: stateId,
      });
      setLoadingListCountySelect(false)
      setListCountySelect(dataCounties?.result)
      return dataCounties?.result;
  }

  const { data: countries = [] } = useQuery(
    ['GET_COUNTRIES'],
    async () => {
      try {
        if (onClose) return;
        const { data } = await getCountries();
        return data.result;
      } catch (err) {
        notification.error({ message: err?.response?.data?.message });
      }
    },
    { enabled: true }
  );

  const handleSubmitBullhorn = (e) => {
    handleOk();
  };

  const handleSubmitCompany = async (e) => {
    const companyPayload = getValues().clientCorporation;
    const companyId = getValues().companyId;

    const {
      name,
      status,
      mainPhone,
      companyAddress,
      stateId,
      countySelect,
      zip,
      standardFeeArrangement,
      industries,
      categories,
      skills,
    } = companyPayload;

    if (!name) {
      return notification.error({ message: 'Company name is required.' });
    } else if (!status) {
      return notification.error({ message: 'Status is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      return notification.error({ message: 'Industries is required.' });
    } else if (!mainPhone) {
      return notification.error({ message: 'Main phone number is required.' });
    } else if (!companyAddress) {
      return notification.error({ message: 'Company address is required.' });
    } else if (!stateId) {
      return notification.error({ message: 'Country is required.' });
    } else if (!countySelect) {
      return notification.error({ message: 'County is required.' });
    } else if (!zip) {
      return notification.error({ message: 'Postcode is required.' });
    } else if (categories.length === 0) {
      return notification.error({ message: 'Categories is required.' });
    } else if (skills.length === 0) {
      return notification.error({ message: 'Skills is required.' });
    }

    if (companyPayload?.companyAddress?.length > 100) {
      notification.error({
        message: 'Address should not have more than 100 characters',
      });
      return;
    }

    if (companyPayload?.companySecondAddress?.length > 100) {
      notification.error({
        message: 'Address should not have more than 100 characters',
      });
      return;
    }

    const newCompanyPayload = {
      entityName: 'ClientCorporation',
      name: companyPayload?.name,
      status: companyPayload?.status,
      parentClientCorporation: {
        id:
          companyPayload?.parentCompanyId === ''
            ? undefined
            : companyPayload?.parentCompanyId,
      },
      customText2: companyPayload?.companyOwnerId,
      businessSectorList: !companyPayload.industries
        ? []
        : companyPayload?.industries.length === 0
          ? []
          : companyPayload.industries
              .filter((industry) => industry && industry.label)
              .map((industry) => industry.label),
      notes: companyPayload?.companyComments,
      companyDescription: companyPayload?.companyDescription,
      companyURL: companyPayload?.companyWebsite,
      phone: `${companyPayload?.mainPhone}`,
      address: {
        countryName: companyPayload?.state,
        countryID: Number(companyPayload?.stateId),
        address1: companyPayload?.companyAddress,
        address2: companyPayload?.companySecondAddress,
        city: companyPayload?.city,
        zip: companyPayload?.zip,
        state: companyPayload?.county,
        timezone: null,
      },
      billingContact: companyPayload?.billingContact,
      billingPhone: `${companyPayload?.billingPhone}`,
      invoiceFormat: companyPayload?.invoiceFormatInformation,
      feeArrangement: companyPayload?.standardFeeArrangement,
      billingAddress: {
        state: companyPayload?.billingZip,
        address1: companyPayload?.billingAddress,
        city: companyPayload?.billingCity,
        zip: companyPayload?.billingZip,
        county: companyPayload?.billingCounty,
      },
      options: {
        categories: {
          replaceAll: categories?.map((obj) => obj.value),
        },
        skills: {
          replaceAll: skills?.map((obj) => obj.value),
        },
      },
      customText1: companyPayload?.linkedProfileUrl,
    };

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newCompanyPayloadCleaned = cleanPayload(newCompanyPayload);
    // newCompanyPayloadCleaned.address.address2 = null;
    newCompanyPayloadCleaned.address.countryCode = '';
    newCompanyPayloadCleaned.address.timezone = null;
    try {
      const { data } = isEditCompany
        ? await upladteBullhorn(companyId, newCompanyPayloadCleaned)
        : await insertBullhorn(newCompanyPayloadCleaned);
      // newCompanyPayload
      setAddressSearchText(companyPayload?.companyAddress);
      setValue('address1', companyPayload?.companyAddress);
      setValue('address2', companyPayload?.companySecondAddress);
      setValue('city', companyPayload?.city);
      setValue('county', companyPayload?.county);
      setValue('countySelect', companyPayload?.county);
      setValue('zip', companyPayload?.zip);
      setValue('state', companyPayload?.state);
      setValue('stateId', Number(companyPayload?.stateId));
      setValue('stateSelected', Number(companyPayload?.stateId));
      if (isEditCompany) {
        setValue('company', data?.result?.data?.name);
      } else {
        setValue('companySelect', data?.result?.changedEntityId);
        setValue('companyId', data?.result?.changedEntityId);
        setValue('company', data?.result?.data?.name);
        functionContactClient.contactSetStart(0);
        functionContactClient?.handleContactSearch(
          '',
          data?.result?.changedEntityId
        );
      }
      setListAddContactSelected([]);
      setJobDetailCompanyId(false);
      setIsAddCompany(false);
      setShowCompany(false);
      setHandleCloseClient(false);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
    }
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinder'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinder')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinderInclude'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderInclude')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinderNotAny'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderNotAny')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFindCompany'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFindCompany')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFinderNotAny'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinderNotAny')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFinderText(watch('locationFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFindCompanyText(watch('locationFindCompanyText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustry(watch('industryFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustryCompany(
        watch('industryKeywordCompanyText')
      );
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextTitle(watch('titleFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  const { data: companyListPeople = [] } = useQuery(
    ['GET_LIST_COMPANY_PEOPLE', debouncedSearchTextCompanyPeople],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompanyPeople,
      });
      return data?.organizations;
    },
    { enabled: !!debouncedSearchTextCompanyPeople }
  );

  const { data: companyList = [] } = useQuery(
    ['GET_LIST_COMPANY', debouncedSearchTextCompany],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompany,
      });
      return data.organizations;
    },
    { enabled: !!debouncedSearchTextCompany }
  );

  const { data: locationListPeople = [] } = useQuery(
    ['GET_LIST_LOCATION_PEOPLE', debouncedLocationFinderText],
    async () => {
      if (onClose) return;
      const { data } = await employeeFinderSearchTag({
        searchText:
          debouncedLocationFinderText.trim() === ''
            ? ''
            : debouncedLocationFinderText,
        type: 'location',
      });
      return data?.tags;
    },
    { enabled: !!debouncedLocationFinderText }
  );

  const { data: locationListCompany = [] } = useQuery(
    ['GET_LIST_LOCATION', debouncedLocationFindCompanyText],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedLocationFindCompanyText,
        type: 'location',
      });
      return data?.tags;
    },
    { enabled: !!debouncedLocationFindCompanyText }
  );

  // const { data: employeeList = [] } = useQuery(
  //   ['GET_LIST_EMPLOYEE'],
  //   async () => {
  //     const { data } = await employeeFinderSearchTag({
  //       searchText: '',
  //       type: 'employee',
  //     });
  //     return data?.faceting.num_employees_facets;
  //   },
  //   { enabled: true }
  // );

  // const { data: industryList = [] } = useQuery(
  //   ['GET_LIST_INDUSTRY', debouncedSearchTextIndustry],
  //   async () => {
  //     const { data } = await employeeFinderSearchTag({
  //       searchText: debouncedSearchTextIndustry,
  //       type: 'linkedin_industry',
  //     });
  //     return data?.tags;
  //   },
  //   { enabled: !!debouncedSearchTextIndustry }
  // );

  const handleGetIndustry = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: debouncedSearchTextIndustry,
      type: 'technology',
    });
    setDataIndustryList(data?.tags);
  };

  const handleGetEmployeeList = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'employee',
    });
    setDataEmployeeList(data?.faceting.num_employees_facets);
  };

  const { data: industryListCompany = [] } = useQuery(
    ['GET_LIST_INDUSTRYCompany', debouncedSearchTextIndustryCompany],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextIndustryCompany,
        type: 'linkedin_industry',
      });
      return data?.tags;
    },
    { enabled: !!debouncedSearchTextIndustryCompany }
  );

  const { data: titleList = [] } = useQuery(
    ['GET_LIST_TITLE', debouncedSearchTextTitle],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextTitle,
        type: 'person_title',
      });
      return data?.tags;
    },
    { enabled: !!debouncedSearchTextTitle }
  );

  const timeoutRef = useRef(null);

  const handleEmailFinder = () => {
    setIsModalStaffOpen(true);
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
  };

  useEffect(() => {
    if (!onClose) {
      setIndustrySearchText(' ');
      setCategorySearchText(' ');
      setSkillSearchText(' ');
      functionContactClient.handleContactSearch(
        '',
        defaultDataCompany ? defaultDataCompany?.companyId : ''
      );
      setHandleCloseContact(false);
      setIsSearchCompany(false);
    }
  }, []);

  useEffect(() => {
    if (startCreateContact) {
      handleGetTechno();
      handleGetRevenue({
        openFactorNames: ['organization_trading_status'],
      });
      handleGetFunding({
        openFactorNames: ['organization_latest_funding_stage_cd'],
      });
      handleGetPersonalTitles();
      handleGetLocation();
      handleGetIndustry();
      handleGetEmployeeList();
    }
  }, [startCreateContact]);

  const calculateClientBillRate = (payRate, markUpPercentage) => {
    if (payRate && !markUpPercentage) {
      return payRate;
    } else if (payRate && markUpPercentage) {
      const clientBillRate = payRate + payRate * (markUpPercentage / 100);
      return parseFloat(clientBillRate?.toFixed(2));
    }
    return null;
  };

  const calculateMarkUpPercentage = (payRate, clientBillRate) => {
    if (payRate && !clientBillRate) {
      return 0;
    } else if (payRate && clientBillRate) {
      const markUpPercentage = ((clientBillRate - payRate) / payRate) * 100;
      return parseFloat(markUpPercentage?.toFixed(2));
    }
    return null;
  };

  const handlePayRateChange = (value) => {
    const newClientBillRate = calculateClientBillRate(
      value,
      getValues('markUpPercentage')
    );
    setValue('clientBillRate', newClientBillRate);
    const newMarkUpPercentage = calculateMarkUpPercentage(
      value,
      newClientBillRate
    );
    setValue('markUpPercentage', newMarkUpPercentage);
  };

  const handleClientBillRateChange = (value) => {
    const newMarkUpPercentage = calculateMarkUpPercentage(
      getValues('payRate'),
      value
    );
    setValue('markUpPercentage', newMarkUpPercentage);
  };

  const handleMarkupChange = (value) => {
    const newClientBillRate =
      (value / 100) * watch('payRate') + watch('payRate');
    setValue('clientBillRate', newClientBillRate);
  };

  const handleChangeTab = (e) => {
    if (e == 1) {
      functionContactClient.handleContactSearch('', getValues().companyId);
    }
  };

  const handleBulkAddContact = async () => {
    await handleSubmitBulkAddContact();
  };

  const handleSubmitBulkAddContact = async () => {
    if (!getValues()?.clientContactBulk?.companyId) {
      notification.error({
        message: 'Company is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.status) {
      notification.error({
        message: 'Status is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.industries) {
      notification.error({
        message: 'Industries is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.categories) {
      notification.error({
        message: 'Categories is required.',
      });
      return;
    }
    setLoadingBulkData(true);
    setIsLoadingAddContactFormBulk(true);
    const checkListContact = [];
    const dataToInsert = [];
    const unretrievableContactIds = [];
    const checkListContactIds = [];
    for (let id of selectedRowKeys) {
      try {
        const { data } = await getLDetailEmployee({ employeeId: id });
        checkListContact.push({ ...data, rawId: id });
        // setDataContacts(prevDataContacts => [data, ...prevDataContacts]);
      } catch (error) {
        unretrievableContactIds.push(id);
      }
    }

    for (let itemCheck of checkListContact) {
      try {
        const { data } = await searchBullhorn(
          'ClientContact',
          0,
          5,
          '',
          '',
          '',
          itemCheck?.email,
          null,
          true
        );
        if (data.result.length === 0) {
          dataToInsert.push(itemCheck);
          checkListContactIds.push(itemCheck?.rawId);
        }
      } catch (error) {}
    }

    setLoadingBulkData(false);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      status,
      categories,
    } = getValues()?.clientContactBulk;

    const emailList = checkListContact
      .filter((contact) => contact.email !== null)
      .map((contact) => contact.email);
    if (emailList?.length == 0) {
      notification.warning({
        message: 'No email available',
      });
      setIsAddContactFormBulk(false);
      setIsLoadingAddContactFormBulk(false);
      setSelectedRowKeys([]);
      return;
    }

    const arrContact = dataToInsert?.map((item) => {
      const addressParts = [
        item?.city,
        item?.country,
        item?.county,
        item?.state,
      ].filter(Boolean);

      const combinedAddress = addressParts.join(', ');
      return {
        entityName: 'ClientContact',
        firstName: item?.first_name,
        // lastName: getValues()?.clientContact?.lastName,
        status: status.label,
        lastName: item?.last_name,
        owner: {
          id: userToSet?.user?.consultantId || userToSet?.consultantId,
        },
        // type: getValues()?.clientContact?.type,
        // secondaryOwners: {
        //   replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
        // },
        clientCorporation: {
          id: companyId,
        },
        // division: getValues()?.clientContact?.department,
        occupation: item?.title,
        email: item?.email,
        phone: item?.sanitized_phone,
        mobile: getValues()?.clientContactBulk?.mobilePhone,
        phone2: getValues()?.clientContactBulk?.otherPhone,
        fax: item?.clientContact?.fax,
        address: {
          countryID: getValues()?.clientContactBulk?.stateId,
          countryName: getValues()?.clientContactBulk?.state,
          state: getValues()?.clientContactBulk?.county,
          address1: getValues()?.clientContactBulk?.address,
          city: getValues()?.clientContactBulk?.city,
          zip: getValues()?.clientContactBulk?.zip,
        },
        businessSectors: {
          replaceAll: industries.map((obj) => obj.value),
        },
        // comments: getValues()?.clientContact?.generalCommets,
        // referredByPerson: {
        //   id: getValues().clientContact.referredById || null,
        // },
        skills: {
          replaceAll: skills?.map((obj) => obj.value),
        },
        categories: {
          replaceAll: categories?.map((obj) => obj.value),
        },
        options: {
          skills: {
            replaceAll: skills?.map((obj) => obj.value),
          },
          categories: {
            replaceAll: categories?.map((obj) => obj.value),
          },
        },
        name: `${item?.first_name} ${item?.last_name}`,
        customText1: item?.linkedin_url,
        rawInformation: {
          ...item,
          organization:
            item?.organization ||
            listEmployee.find((emp) => checkListContactIds.includes(emp?.id))
              ?.organization,
        },
      };
    });

    try {
      for (let itemI of arrContact) {
        try {
          const { data } = await insertBullhorn(itemI);
        } catch (error) {
          console.log('Insert contact fails');
        }
      }
    } catch (e) {
      // notification.error({
      //   message: "Some things went wrong"
      // })
    }

    handleGenerateNotificationBulkAddContact(
      arrContact,
      selectedRowKeys,
      unretrievableContactIds
    );

    setIsAddContactFormBulk(false);
    setIsLoadingAddContactFormBulk(false);
    setSelectedRowKeys([]);
  };

  const handleRenderModal = () => {
    return (
      <>
        {/* Modal show company detail */}
        <Modal
          width={1600}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title="Detail Company"
          open={showDetailCompany}
          onCancel={() => {
            setShowDetailCompany(false);
          }}
          footer={false}
        >
          <div className="grid gap-4">
            <Card>
              <Row className="flex justify-center">
                <Col>
                  <img
                    className="mr-2 rounded w-8"
                    src={detailCompany?.logo_url}
                    alt={detailCompany?.name}
                  />
                </Col>
                <span className="text-lg my-auto">
                  {detailCompany?.name}{' '}
                  <span className="px-2 py-1 bg-gray-200 rounded-md text-sm ml-1">
                    N/A
                  </span>
                </span>
              </Row>
              <Row className="flex gap-2 mt-1 justify-center">
                <Col>
                  <LinkOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(detailCompany?.website_url);
                    }}
                    className="border p-1 rounded cursor-pointer text-gray-700 text-lg"
                  />
                </Col>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(detailCompany?.linkedin_url);
                    }}
                    className="border p-1 rounded cursor-pointer text-[#0288d1] text-lg"
                  />
                </Col>
                <Col>
                  <FacebookOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(detailCompany?.facebook_url);
                    }}
                    className="border p-1 rounded cursor-pointer text-[#3f51b5] text-lg"
                  />
                </Col>
                <Col>
                  <TwitterOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(detailCompany?.twitter_url);
                    }}
                    className="border p-1 rounded cursor-pointer text-[#03a9f4] text-lg"
                  />
                </Col>
                <div class="border-r border-solid"></div>
                <Col>
                  {/* text={this.state.value} */}
                  <CopyToClipboard
                    text={detailCompany?.phone}
                    onCopy={() => {
                      notification.success({ message: 'Success Copy Number' });
                    }}
                  >
                    <Button icon={<PhoneOutlined />}>
                      {detailCompany?.phone}
                    </Button>
                  </CopyToClipboard>
                </Col>
              </Row>
            </Card>
            <Card>
              <span className="font-semibold">
                {showFullText
                  ? detailOrganization?.short_description
                  : detailOrganization?.short_description?.slice(0, 200)}
              </span>
              {detailOrganization?.short_description ? (
                detailOrganization?.short_description.length > 200 ? (
                  <button
                    className="text-blue-500 text-sm font-normal ml-1"
                    onClick={toggleText}
                  >
                    {showFullText ? 'Show Less' : '... Show More'}
                  </button>
                ) : (
                  ''
                )
              ) : (
                ''
              )}
              <p className="mt-6 font-semibold text-base">
                Company Keyword <br />
                <Row className="mt-2">
                  {detailOrganization?.keywords
                    ? detailOrganization?.keywords.map((item, i) => (
                        <Col className="mt-1 my-2">
                          <span
                            className="px-3 py-1 bg-gray-200 rounded mr-2 font-normal"
                            key={i}
                          >
                            {item}
                          </span>
                        </Col>
                      ))
                    : ''}
                </Row>
              </p>
            </Card>
            <Card>
              <Row>
                <Col className="grid gap-3" span={12}>
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">Industry</span>
                    </Col>
                    <Col span={18}>
                      <span className="flex flex-wrap">
                        {detailOrganization?.industries
                          ? detailOrganization?.industries.map((item, i) => (
                              <span
                                className="rounded mr-2 text-base font-semibold text-blue-500"
                                key={i}
                              >
                                {item}
                                {i ===
                                detailOrganization?.industries?.length - 1
                                  ? '.'
                                  : ','}
                              </span>
                            ))
                          : ''}
                      </span>
                    </Col>
                  </Row>
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Founding Year
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.founded_year}
                      </span>
                    </Col>
                  </Row>
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">Employees</span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.estimated_num_employees}
                      </span>
                    </Col>
                  </Row>
                  {detailOrganization?.publicly_traded_exchange && (
                    <Row className="flex items-start">
                      <Col span={4}>
                        <span className="font-semibold text-base">Trading</span>
                      </Col>
                      <Col span={18}>
                        <span className="rounded text-base">
                          {detailOrganization?.publicly_traded_exchange?.toUpperCase()}{' '}
                          :{' '}
                          {detailOrganization?.publicly_traded_symbol?.toUpperCase()}
                          .
                        </span>
                      </Col>
                    </Row>
                  )}
                  {detailOrganization?.market_cap && (
                    <Row className="flex items-start">
                      <Col span={4}>
                        <span className="font-semibold text-base">
                          Market Cap
                        </span>
                      </Col>
                      <Col span={18}>
                        <span className="rounded text-base">
                          {detailOrganization?.market_cap}
                        </span>
                      </Col>
                    </Row>
                  )}
                  {detailOrganization?.annual_revenue_printed && (
                    <Row className="flex items-start">
                      <Col span={4}>
                        <span className="font-semibold text-base">
                          Annual Revenue
                        </span>
                      </Col>
                      <Col span={18}>
                        <span className="rounded text-base">
                          ${detailOrganization?.annual_revenue_printed}
                        </span>
                      </Col>
                    </Row>
                  )}
                </Col>
                <Col span={12}>
                  <Row className="items-center py-1">
                    <Col span={4} className="flex items-center mb-auto">
                      <EnvironmentOutlined className="pr-3 text-blue-400" />
                      <span className="pr-3 text-graySecondary  text-base font-semibold">
                        Location
                      </span>
                    </Col>
                    <Col span={18} className="pr-3 text-base">{`${
                      detailOrganization?.raw_address
                        ? detailOrganization?.raw_address + ','
                        : ''
                    } ${
                      detailOrganization?.city
                        ? detailOrganization?.city + ','
                        : ''
                    } ${
                      detailOrganization?.state
                        ? detailOrganization?.state + ','
                        : ''
                    } ${
                      detailOrganization?.zip
                        ? detailOrganization?.zip + ','
                        : ''
                    } ${
                      detailOrganization?.country
                        ? detailOrganization?.country + '.'
                        : ''
                    }`}</Col>
                  </Row>
                </Col>
              </Row>
            </Card>
            <Card style={{ width: 600 }} className="my-auto mx-auto">
              <p className="text-base font-semibold mb-3">
                Technology Insights (
                {detailOrganization.current_technologies
                  ? detailOrganization?.current_technologies.length || 0
                  : 0}
                )
              </p>
              <List
                style={{ overflowY: 'scroll', maxHeight: '500px' }}
                header={false}
                footer={false}
                bordered
                dataSource={detailOrganization?.current_technologies || []}
                renderItem={(item) => (
                  <List.Item>
                    <Row>
                      <Col className=" text-base font-semibold" span={24}>
                        {item?.name}
                      </Col>
                      {item?.category}
                    </Row>
                  </List.Item>
                )}
              />
            </Card>
          </div>
        </Modal>

        {/* Modal show contact detail */}
        <CompanyDetailModal
          showDetailCompany={showDetailCompany}
          setShowDetailCompany={setShowDetailCompany}
          detailCompany={detailCompany}
          detailOrganization={detailOrganization}
        />

        {isAddContactFormBulk && (
          <Modal
            className="customize-contact-form"
            width={1000}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            // title="Add Contact Form"
            open={isAddContactFormBulk}
            onCancel={() => {
              setIsAddContactFormBulk(false);
              // handleResetFormAddContact();
            }}
            footer={false}
          >
            <Form layout="vertical">
              <div className="w-full  text-xl pl-6 pb-3 pt-5 font-semibold text-base">
                <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
              </div>
              <BullhornBulkAddContactModal
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                handleCloseClient={handleCloseClient}
                setHandleCloseClient={setHandleCloseClient}
                handleCloseContact={handleCloseContact}
                setHandleCloseContact={setHandleCloseContact}
                detailDataContact={detailDataContact}
                flagDetailContact={flagDetailContact}
                listEmployee={listEmployee}
                selectedRowKeys={selectedRowKeys}
                isAddContactFormBulk={isAddContactFormBulk}
                loadingGetBulkData={loadingGetBulkData}
                setLoadingBulkData={setLoadingBulkData}
                setDataContacts={setDataContacts}
                dataContacts={dataContacts}
              />
              <div className="pl-6 left-0 bottom-0 w-full pb-4">
                <div
                  className="flex gap-4 mr-8"
                  style={{ float: 'right', paddingBottom: '10px' }}
                >
                  <Button
                    onClick={() => {
                      setIsAddContactForm(false);
                      handleResetFormAddContact();
                    }}
                    className={`bg-[#BEDAFD33]  `}
                  >
                    Cancel
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={handleBulkAddContact}
                    type="primary"
                    loading={isLoadingAddContactFormBulk}
                    className={``}
                    disabled={loadingGetBulkData}
                  >
                    Save
                  </Button>
                </div>
              </div>
              <div style={{ clear: 'both' }}></div>
            </Form>
          </Modal>
        )}

        {/* Modal find email by link = list email */}
        <Modal
          width={1600}
          // style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          // title="List Staff"
          className="top-10"
          rootClassName="contact-modal-container"
          open={isModalStaffOpen}
          onCancel={() => {
            setIsModalStaffOpen(false);
            setIsLoadingEmployee(false);
            setValue('searchCompany', '');
            setValue('searchPeople', '');
            resetAllPeople();
            resetAllCompany();
          }}
          footer={false}
        >
          <Tabs
            defaultActiveKey="1"
            onChange={(e) => handleChangeTab(e)}
            items={itemsTableEmailFinder}
          />
          <Button
            className="flex ml-auto mt-3"
            htmlType="button"
            onClick={() => {
              setIsModalStaffOpen(false);
              setIsLoadingEmployee(false);
              setValue('searchCompany', '');
              setValue('searchPeople', '');
              resetAllPeople();
              resetAllCompany();
            }}
            type="primary"
          >
            Done
          </Button>
        </Modal>

        {/* Modal send Mail */}
        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
              <div style={{ marginRight: '50px' }}>
                <label
                  style={{ fontSize: '14px', marginRight: '8px' }}
                  for="configSequenceEmail"
                >
                  Don&apos;t send Sequence Email
                </label>
                <Checkbox
                  id="configSequenceEmail"
                  style={{ marginRight: '20px' }}
                  onClick={() => {
                    setSequenceStatus(!sequenceStatus);
                    setValue('sequenceStatus', !sequenceStatus);
                  }}
                />
                <label
                  style={{ fontSize: '14px', marginRight: '10px' }}
                  for="configEmail"
                >
                  Don&apos;t send Email
                </label>
                <Checkbox
                  checked={checkBoxStatus}
                  onClick={() => {
                    setCheckBoxStatus(!checkBoxStatus);
                  }}
                  id="configEmail"
                />
              </div>
            </div>
          }
          open={openModalSendEmail}
          onCancel={() => {
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmail}
                setOpenSendEmail={setOpenSendEmail}
                listAddContactSelected={listAddContactSelected}
                functionContactClient={functionContactClient}
                job={{ ...job, state: getValues('state') }}
                setHaveSendJob={setHaveSendJob}
                setIsLockedSendJob={setIsLockedSendJob}
                isLockedSendJob={isLockedSendJob}
                checkBoxStatus={checkBoxStatus}
                sequenceStatus={sequenceStatus}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromManualCreate={fromManualCreate}
                isFromVacancy={true}
                newUpdatedSequence={true}
              />
            </Form>
          </div>
        </Modal>

        {/* CREATE CONTACT */}
        {isAddContactForm && (
          <Modal
            className="customize-contact-form"
            width={1000}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            // title={`${flagEditContact ? 'Edit Contact Form' : 'Add Contact Form'}`}
            open={isAddContactForm}
            onCancel={() => {
              setIsAddContactForm(false);
              setFlagEditContact(false);
              handleResetFormAddContact();
            }}
            footer={false}
          >
            <Form layout="vertical">
              <div className="w-full  text-xl pl-6 pb-3 pt-5 font-semibold text-base">
                <span>
                  {flagEditContact ? 'Edit Contact Form' : 'Add Contact Form'}
                </span>
              </div>
              <BullhornSubmissionContact
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                handleCloseClient={handleCloseClient}
                setHandleCloseClient={setHandleCloseClient}
                handleCloseContact={handleCloseContact}
                setHandleCloseContact={setHandleCloseContact}
                detailDataContact={detailDataContact}
                flagDetailContact={flagDetailContact}
                flagEditContact={flagEditContact}
                fromLead={true}
              />
              <div className="pl-6 left-0 bottom-0 w-full pb-4">
                <div className="flex gap-4 mr-8">
                  <Button
                    onClick={() => {
                      setIsAddContactForm(false);
                      setFlagEditContact(false);
                      handleResetFormAddContact();
                    }}
                    className={`bg-[#BEDAFD33]  `}
                  >
                    Cancel
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={handleSubmitAddContact}
                    type="primary"
                    disabled={isSubmitForm}
                    loading={isSubmitForm}
                    className={` `}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </Form>
          </Modal>
        )}

        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
              <div style={{ marginRight: '50px' }}>
                {/* <label style={{fontSize: "14px", marginRight: "8px"}} for="configEmail">Don't send Sequence Email</label>
                <Checkbox id='configSequenceEmail' style={{ marginRight: "20px"}}/>
                <label style={{fontSize: "14px", marginRight: "10px"}} for="configEmail">Don't send Email</label>
                <Checkbox checked={checkBoxStatus} onClick={() => {setCheckBoxStatus(!checkBoxStatus)}} id='configEmail'/> */}
              </div>
            </div>
          }
          open={openModalSendEmailContact}
          onCancel={() => {
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmailContact}
                setOpenSendEmail={setOpenSendEmailContact}
                listAddContactSelected={[getValues()?.optionContactSelect]}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                // functionContactClient={functionContactClient}
                // job={lead}
                fromSequenseEmail={true}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                newUpdatedSequence={true}
                // setHaveSendJob={setHaveSendJob}
                // setIsLockedSendJob={setIsLockedSendJob}
                // isLockedSendJob={isLockedSendJob}
                // checkBoxStatus={checkBoxStatus}
              />
            </Form>
          </div>
        </Modal>
        <ModalShowListExitSequence
          setOpen={setOpenExistingSequence}
          open={openExistingSequence}
          onClose={onClose}
        />

        <ModalListUserGroup
          setOpen={setOpenExistingUserGroup}
          open={openExistingUserGroup}
          currentContact={currentContact}
          // currentContact={currentContact}
          currentContacts={bulkContacts}
        />
        <Row></Row>
      </>
    );
  };

  const handleCheckLogicRender = (data, fieldName) => {
    const findData = _find(data, { name: fieldName });

    return _get(findData, 'on');
  };

  const handleChangeGetSimilar = async (
    companyId,
    jobTitle,
    jobDescription
  ) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(async () => {
      if (
        companyId &&
        jobTitle &&
        jobTitle != '' &&
        jobDescription &&
        jobDescription != ''
      ) {
        setLoadingSimilar(true);
        const { data } = await getSimilarJob({
          companyId: companyId,
          jobTitle: jobTitle,
          jobDescription: jobDescription,
        });
        setLoadingSimilar(false);
        setSimilarList(data?.result?.items);
      }
    }, 500);
  };

  const handleRenderJobInformation = () => {
    return (
      <>
        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Employment Type'
        ) && (
          <>
            {/* EmploymentType */}
            <Form.Item label="Employment Type" name="employmentType">
              <Controller
                control={control}
                name="employmentType"
                defaultValue={'temporary'}
                render={({ field }) => (
                  <div className="grid grid-cols-5 gap-4 mt-4">
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center min-w-[10rem] p-4 ${getValues('employmentType') === 'temporary' ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      onClick={() => setValue('employmentType', 'temporary')}
                    >
                      <HourglassOutlined className="text-3xl text-cyan-600 font-thin" />
                      <span className="font-semibold">Temporary</span>
                    </Button>
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center min-w-[10rem] p-4 ${getValues('employmentType') === 'contract' ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      onClick={() => setValue('employmentType', 'contract')}
                    >
                      <SnippetsOutlined className="text-3xl text-cyan-600" />
                      <span className="font-semibold">Contract</span>
                    </Button>
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center min-w-[10rem] p-4 ${getValues('employmentType') === 'permanent' ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      onClick={() => setValue('employmentType', 'permanent')}
                    >
                      <BookOutlined className="text-3xl text-cyan-600" />
                      <span className="font-semibold">Permanent</span>
                    </Button>
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center min-w-[10rem] p-4 ${getValues('employmentType') === 'fixed term' ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      onClick={() => setValue('employmentType', 'fixed term')}
                    >
                      <FieldTimeOutlined className="text-3xl text-cyan-600" />
                      <span className="font-semibold">Fixed Term</span>
                    </Button>
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center min-w-[10rem] p-4 ${getValues('employmentType') === 'opportunity' ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      onClick={() => setValue('employmentType', 'opportunity')}
                    >
                      <FireOutlined className="text-3xl text-cyan-600" />
                      <span className="font-semibold">Opportunity</span>
                    </Button>
                  </div>
                  // <Radio.Group {...field}>
                  //   <Radio value={'temporary'}>Temporary</Radio>
                  //   <Radio value={'contract'}>Contract</Radio>
                  //   <Radio value={'permanent'}>Permanent</Radio>
                  //   <Radio value={'fixed term'}>Fixed Term</Radio>
                  //   <Radio value={'opportunity'}>Opportunity</Radio>
                  // </Radio.Group>
                )}
              />
            </Form.Item>
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Job Type'
        ) &&
          fromManualCreate && (
            <>
              {/* EmploymentType */}
              <Form.Item label="Job Type" name="jobtype">
                <Controller
                  control={control}
                  name="jobtype"
                  defaultValue={'Remote'}
                  render={({ field }) => (
                    <Radio.Group {...field}>
                      <Radio value={'Remote'}>Remote</Radio>
                      <Radio value={'On Site'}>On Site</Radio>
                    </Radio.Group>
                  )}
                />
              </Form.Item>
            </>
          )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Job Title'
        ) && (
          <>
            {/* Title */}
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Job Title
                </p>
              }
              name="jobtitle"
            >
              <Controller
                render={({ field }) => (
                  <>
                    <Input
                      showCount
                      className="!rounded-xl"
                      disabled={showCompany}
                      required
                      maxLength={100}
                      value={getValues('jobtitle')}
                      {...field}
                      defaultValue={job?.jobtitle}
                      onChange={async (e) => {
                        setValue('jobtitle', e.target.value);
                        handleChangeGetSimilar(
                          getValues('companyId'),
                          e.target.value,
                          getValues('description')
                        );
                      }}
                      suffix={
                        getValues().jobtitle !== '' &&
                        getValues().jobtitle &&
                        getValues().jobtitle?.length <= 100 ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      }
                    />
                    {/* <div
                      style={{
                        marginTop: '10px',
                        marginLeft: '10px',
                        fontSize: '12px',
                        // color: '#fff',
                      }}
                    >
                      <i>
                        note: Maximum length of title is 100. Please have an
                        adjustment
                      </i>
                    </div> */}
                  </>
                )}
                name="jobtitle"
                control={control}
              />
            </Form.Item>
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Description'
        ) && (
          <>
            {/* Text Detail */}
            <div className="grid grid-cols-8 gap-x-4 mb-5 border-2 border-[#d9d9d9] p-3 rounded-md">
              <div className="col-span-8 sm:col-span-4">
                <div className="items-center py-1 grid grid-cols-10 just">
                  <div className="col-span-3 flex items-center">
                    <Image
                      className="mr-3 text-blue-400"
                      src={companyIcon}
                      alt="Company Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                    <span className="pl-3 text-graySecondary ">Company</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6  ">
                    {watch('company')}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <Image
                      className="mr-3 text-blue-400"
                      src={jobTypeIcon}
                      alt="Job Type Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                    <span className="pl-3 text-graySecondary ">Job Type</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    {watch('jobtype') || '-'}
                  </span>
                </div>
                {/* <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <WalletOutlined className="pr-3" />
                    <span className="pr-3 text-graySecondary ">
                      Range
                    </span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    {(watch('min_salary') &&
                      `${watch('min_salary')} - ${watch('max_salary') || ''}`) ||
                      '-'}
                  </span>
                </div> */}
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <Image
                      className="mr-3 text-blue-400"
                      src={locationIcon}
                      alt="Location Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                    <span className="pl-3 text-graySecondary ">Location</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    {/* {`${watch('joblocationcity')}`
                      .toLowerCase()
                      .includes('anywhere')
                      ? watch('joblocationcity')
                      : `${watch('address1') ? watch('address1') + ',' : ''} ${
                          watch('city') ? watch('city') + ',' : ''
                        } ${watch('county') ? watch('county') + ',' : ''} ${
                          watch('zip') ? watch('zip') + ',' : ''
                        } ${watch('state') ? watch('state') + '.' : ''}`} */}
                    {job?.joblocationcity}
                  </span>
                </div>
              </div>
              <div className="col-span-8 sm:col-span-4">
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <Image
                      className="mr-3 text-blue-400"
                      src={salaryIcon}
                      alt="Salary Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                    <span className="pl-3 text-graySecondary ">Salary</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    {job?.salary || watch('salary') || '-'}
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <Image
                      className="mr-3 text-blue-400"
                      src={sourceIcon}
                      alt="Source Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                    <span className="pl-3 text-graySecondary ">Source</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    <span className="font-medium col-span-6 text-secondary">
                      <Button
                        onClick={() => {
                          window.open(
                            removeTrackingParams(job?.link),
                            '_blank'
                          );
                        }}
                        type={'primary'}
                      >
                        Click to View Job
                      </Button>
                    </span>
                  </span>
                </div>
                <div className="items-center py-1 grid grid-cols-10">
                  <div className="col-span-3 flex items-center">
                    <StarOutlined className="pr-3 text-cyan-600 font-extrabold" />
                    <span className="text-graySecondary ">Experience</span>
                  </div>
                  <span className="pr-3 font-medium col-span-6 ">
                    Not Supplied
                  </span>
                </div>
              </div>
            </div>

            {/* Description */}
            <Form.Item label="Description" name="description">
              <Controller
                render={({ field }) => (
                  <Input.Group className="flex gap-2 ">
                    <Input.TextArea
                      // style={{ cursor: 'pointer' }}
                      // className='border-[#6492fa]'
                      disabled={showCompany}
                      rows={10}
                      {...field}
                      resize
                      onChange={async (e) => {
                        setValue('description', e.target.value);
                        handleChangeGetSimilar(
                          getValues('companyId'),
                          getValues('jobtitle'),
                          e.target.value
                        );
                      }}
                    />
                  </Input.Group>
                )}
                name="description"
                control={control}
              />
            </Form.Item>
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Status'
        ) && (
          <>
            {/* Status (Drop down) */}
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Status
                </p>
              }
              name="status"
              className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <AutoComplete
                    disabled={showCompany}
                    {...field}
                    onSelect={(value) => {
                      setValue('statusSelect', value);
                    }}
                    onSearch={() => {
                      setValue('statusSelect', null);
                    }}
                    options={[
                      'Lead',
                      'Accepting Candidates',
                      'HOT Lead',
                      'Offer Out',
                      'Placed',
                      'Filled by Client',
                      'Lost to Competitor',
                      'Archived',
                      'On Hold',
                      'Cancelled',
                    ].map((val) => ({ label: val, value: val }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  >
                    <Input
                      addonBefore={
                        <RocketOutlined
                          style={{
                            color: 'rgba(0,0,0,.5)',
                          }}
                        />
                      }
                      required
                      suffix={
                        getValues().statusSelect !== '' &&
                        getValues().statusSelect ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      }
                    />
                  </AutoComplete>
                )}
                name="status"
                control={control}
              />
            </Form.Item>

            <Form.Item
              label={
                <p>
                  <span className="text-red-600 mr-1">*</span>
                  Lead Sheet
                </p>
              }
              name="leadSheetId"
              className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <Select
                    suffixIcon={
                      <div className="mt-[9px]">
                        {getValues('leadSheetId') ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )}
                      </div>
                    }
                    defaultValue={{
                      label: leadSheets?.find(
                        (item) => item?.id === getValues('leadSheetId')
                      )?.name,
                      value: getValues('leadSheetId'),
                    }}
                    loading={leadSheetLoading}
                    {...field}
                    disabled={showCompany}
                    placeholder="Select a Lead Sheet"
                    dropdownRender={(menu) => (
                      <div className="w-full">
                        <div className="grid grid-cols-10 gap-4 py-2 px-3">
                          <Input
                            addonBefore={COMMON_STRINGS.VACANCY}
                            className="col-span-7"
                            placeholder="Please enter sheet name"
                            ref={inputLeadSheetRef}
                            value={newLeadSheetName}
                            onChange={onNewLeadSheetNameChange}
                            onKeyDown={(e) => e.stopPropagation()}
                          />
                          <Button
                            loading={leadSheetLoading}
                            className="col-span-3 font-medium"
                            type="default"
                            icon={<PlusOutlined />}
                            onClick={addNewLeadSheet}
                          >
                            Add New Sheet
                          </Button>
                        </div>
                        <Divider
                          style={{
                            margin: '8px 0',
                          }}
                        />
                        {menu}
                      </div>
                    )}
                    options={leadSheets.map((item) => ({
                      label: item?.name,
                      value: item?.id,
                    }))}
                    onChange={onChangeLeadSheet}
                  />
                )}
                name="leadSheetId"
                control={control}
              />
            </Form.Item>

            {/* Open/Closed (Switch toggle) */}
            <div className="flex gap-5 justify-start">
              <span className="font-semibold mt-1 text-base">Open</span>
              <Form.Item name="isOpen" valuePropName="checked">
                <Controller
                  render={({ field }) => (
                    <div className="flex gap-2">
                      <Button
                        className={clsx(
                          getValues('isOpen') &&
                            '!bg-cyan-600 !text-white shadow-md'
                        )}
                        onClick={() => setValue('isOpen', true)}
                        type="primary"
                        icon={
                          <CheckOutlined
                            className={clsx(
                              'font-semibold',
                              getValues('isOpen') && '!text-white'
                            )}
                          />
                        }
                      >
                        <span
                          className={clsx(getValues('isOpen') && '!text-white')}
                        >
                          Yes
                        </span>
                      </Button>
                      <Button
                        className={clsx(
                          !getValues('isOpen') &&
                            '!bg-cyan-600 !text-white shadow-md'
                        )}
                        onClick={() => setValue('isOpen', false)}
                        type="primary"
                        icon={
                          <CloseOutlined
                            className={clsx(
                              'font-semibold',
                              !getValues('isOpen') && '!text-white'
                            )}
                          />
                        }
                      >
                        <span
                          className={clsx(
                            !getValues('isOpen') && '!text-white'
                          )}
                        >
                          No
                        </span>
                      </Button>
                    </div>
                  )}
                  name="isOpen"
                  control={control}
                />
              </Form.Item>
            </div>
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Consultant'
        ) && (
          <>
            {/* Consultant (Drop down with search API) */}
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Consultant
                </p>
              }
              name="consultant"
              className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <AutoComplete
                    disabled={showCompany}
                    {...field}
                    options={consultantOptions.map((option) => ({
                      value: option.id,
                      label: option.name,
                    }))}
                    onPopupScroll={handleConsultantScroll}
                    onSearch={(searchText) => {
                      setValue('consultantSelect', null);
                      setValue('consultantId', null);
                      setValue('consultant', searchText);
                      handleConsultantSearch(searchText);
                    }}
                    filterOption={(input, option) =>
                      option?.label
                        ?.toLowerCase()
                        .indexOf(input?.toLowerCase()) >= 0
                    }
                    onSelect={(value) => {
                      setValue('consultantSelect', value);
                      setValue('consultantId', value);
                      setValue('contactConsultantSelect', value);
                      setValue('contactConsultantId', value);
                      const consultantName = consultantOptions.find(
                        (co) => co.id == value
                      )?.name;
                      setValue('contactConsultant', consultantName);
                      setValue('consultant', consultantName);
                    }}
                  >
                    <Input
                      addonBefore={
                        <UserOutlined
                          style={{
                            color: 'rgba(0,0,0,.5)',
                          }}
                        />
                      }
                      required
                      suffix={
                        isLoadingConsultants ? (
                          <Spin />
                        ) : getValues().consultantSelect !== '' &&
                          getValues().consultantSelect ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      }
                      placeholder="Select Consultant"
                    />
                  </AutoComplete>
                )}
                name="consultant"
                control={control}
              />
            </Form.Item>
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Company'
        ) && (
          <>
            {/* Client (Drop down with search API) */}
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Company
                </p>
              }
              name="company"
              className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <>
                    <BHCompanySelect
                      {...field}
                      watch={watch}
                      disabled={showCompany}
                      onMouseDown={(e) => {
                        setHandleCloseClient(true);
                      }}
                      setIsEditCompany={setIsEditCompany}
                      setShowCompany={setShowCompany}
                      setJobDetailCompanyId={setJobDetailCompanyId}
                      functionContactClient={functionContactClient}
                      setValue={setValue}
                      setHandleCloseClient={setHandleCloseClient}
                      setIsAddCompany={setIsAddCompany}
                      setIsSearchCompany={setIsSearchCompany}
                      getValues={getValues}
                      handleChangeGetSimilar={handleChangeGetSimilar}
                      functionCompany={functionCompany}
                      isAddCompany={isAddCompany}
                      isSearchCompany={isSearchCompany}
                      open={handleCloseClient}
                    />
                  </>
                )}
                name="company"
                control={control}
              />
            </Form.Item>

            {/* section company add & edit */}
            {showCompany && (
              <Form.Item>
                <Card className=" max-w-full mx-auto bg-[#BEDAFD33] shadow-lg rounded-2xl overflow-hidden hover:shadow-xl">
                  <div className="w-full bg-cyan-600 pl-6 py-3 font-semibold text-base">
                    <span className="text-white">
                      {isEditCompany ? 'Edit' : 'Add New'} Company
                    </span>
                  </div>
                  <div className="p-6">
                    <div className="mb-6 border-b-2 border-b-cyan-600 pb-2">
                      <span className="font-medium text-base  ">
                        {isEditCompany ? 'Edit' : 'Add'} Company
                      </span>
                    </div>
                    <BullHornJobSubmissionCompany
                      isEditCompany={isEditCompany}
                      control={control}
                      setValue={setValue}
                      getValues={getValues}
                      handleCloseClient={handleCloseClient}
                      setHandleCloseClient={setHandleCloseClient}
                      watch={watch}
                      dataSearch={searchData?.search}
                    />
                    <div className="left-0 bottom-0 w-full">
                      <div className="flex gap-4 mr-8">
                        <Button
                          onClick={() => {
                            setShowCompany(false);
                            functionCompany.companySetStart(0);
                            setValue('companySelect', null);
                            setHandleCloseClient(true);
                            setValue('companyId', null);
                            setValue('company', '');
                            functionContactClient.contactSetOptions([]);
                            setValue('contactId', null);
                            setValue('contact', null);
                            setValue('contactSelect', null);
                            setValue('email', '');
                            functionCompany.handleCompanySearch(' ');
                            setIsAddCompany(false);
                          }}
                          className={`bg-white`}
                        >
                          Cancel
                        </Button>
                        <Button
                          // htmlType="button"
                          onClick={handleSubmitCompany}
                          type="primary"
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </Form.Item>
            )}
          </>
        )}

        {handleCheckLogicRender(
          _get(configForm, 'data.0.children'),
          'Contact'
        ) && (
          <>
            {/* Contact INPUT*/}
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Contact
                </p>
              }
              name="contact"
              className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <>
                    <BHContactSelect
                      watch={watch}
                      disabled={showCompany || jobDetailCompanyId}
                      setListAddContactSelected={setListAddContactSelected}
                      setHandleCloseContact={setHandleCloseContact}
                      setIsAddContact={setIsAddContact}
                      listAddContactSelected={listAddContactSelected}
                      handleEmailFinder={handleEmailFinder}
                      setStartCreateContact={setStartCreateContact}
                      functionCompany={functionCompany}
                      functionContactClient={functionContactClient}
                      getValues={getValues}
                      setValue={setValue}
                      {...field}
                      open={handleCloseContact}
                      onMouseDown={(e) => {
                        setHandleCloseContact(true);
                      }}
                    />
                  </>
                )}
                name="contact"
                control={control}
              />
            </Form.Item>
          </>
        )}

        <div className="w-full flex gap-5">
          {handleCheckLogicRender(
            _get(configForm, 'data.0.children'),
            '# of Openings'
          ) ||
            (handleCheckLogicRender(
              _get(configForm, 'data.0.children'),
              'Start Date'
            ) && (
              <div
                className={` ${
                  !handleCheckLogicRender(
                    _get(configForm, 'data.0.children'),
                    '# of Openings'
                  ) ||
                  !handleCheckLogicRender(
                    _get(configForm, 'data.0.children'),
                    'Start Date'
                  )
                    ? 'w-full'
                    : 'w-1/2'
                }`}
              >
                {handleCheckLogicRender(
                  _get(configForm, 'data.0.children'),
                  '# of Openings'
                ) && (
                  <>
                    {/* # of Openings */}
                    <Form.Item label="No. of Openings" name="numOpenings">
                      <Controller
                        render={({ field }) => (
                          <InputNumber
                            className="w-full"
                            disabled={showCompany || jobDetailCompanyId}
                            {...field}
                          />
                        )}
                        name="numOpenings"
                        control={control}
                      />
                    </Form.Item>
                  </>
                )}
                {handleCheckLogicRender(
                  _get(configForm, 'data.0.children'),
                  'Start Date'
                ) && (
                  <>
                    {/* Start Date (Date) */}
                    <Form.Item label="Start Date" name="startDate">
                      <Controller
                        render={({ field }) => (
                          <DatePicker
                            disabled={showCompany || jobDetailCompanyId}
                            {...field}
                          />
                        )}
                        name="startDate"
                        control={control}
                      />
                    </Form.Item>
                  </>
                )}
              </div>
            ))}

          {handleCheckLogicRender(
            _get(configForm, 'data.0.children'),
            'Scheduled End Date'
          ) ||
            (handleCheckLogicRender(
              _get(configForm, 'data.0.children'),
              'Source'
            ) && (
              <div
                className={` ${
                  !handleCheckLogicRender(
                    _get(configForm, 'data.0.children'),
                    'Scheduled End Date'
                  ) ||
                  !handleCheckLogicRender(
                    _get(configForm, 'data.0.children'),
                    'Source'
                  )
                    ? 'w-full'
                    : 'w-1/2'
                }`}
              >
                {handleCheckLogicRender(
                  _get(configForm, 'data.0.children'),
                  'Source'
                ) && (
                  <>
                    <Form.Item label="Source" name="source">
                      <Controller
                        render={({ field }) => (
                          <AutoComplete
                            className="mb-2"
                            disabled={showCompany || jobDetailCompanyId}
                            {...field}
                            onSelect={(value) => {
                              setValue('sourceSelect', value);
                            }}
                            onSearch={() => {
                              setValue('sourceSelect', null);
                            }}
                            options={[
                              'Zileo',
                              'Client Website',
                              'Internal Database',
                              'Inbound',
                              'Referral',
                              'Social Media',
                            ].map((val) => ({ label: val, value: val }))}
                            filterOption={(inputValue, option) =>
                              option.label
                                .toLowerCase()
                                .indexOf(inputValue.toLowerCase()) !== -1
                            }
                          ></AutoComplete>
                        )}
                        name="source"
                        control={control}
                      />
                    </Form.Item>
                  </>
                )}
                {handleCheckLogicRender(
                  _get(configForm, 'data.0.children'),
                  'Scheduled End Date'
                ) && (
                  <>
                    {/* Scheduled End Date */}
                    <Form.Item
                      label="Scheduled End Date"
                      name="scheduledEndDate"
                    >
                      <Controller
                        render={({ field }) => (
                          <DatePicker
                            disabled={showCompany || jobDetailCompanyId}
                            {...field}
                          />
                        )}
                        name="scheduledEndDate"
                        control={control}
                      />
                    </Form.Item>
                  </>
                )}
              </div>
            ))}
        </div>
      </>
    );
  };

  const handleRenderCompensationFees = () => {
    return (
      <>
        <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
          <span className="font-medium text-lg  ">Compensation & Fees</span>
        </div>
        {/* <Divider orientation="left"></Divider> */}

        <div className="flex w-full gap-5 compensation-fees-container mb-2">
          <div className="w-1/5">
            {handleCheckLogicRender(
              _get(configForm, 'data.1.children'),
              'Pay Unit'
            ) && (
              <>
                {/* Status (Drop down) */}
                <Form.Item label="Pay Unit" name="salaryUnit">
                  <Controller
                    render={({ field }) => (
                      <AutoComplete
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        onSelect={(value) => {
                          setValue('salaryUnitSelect', value);
                        }}
                        onSearch={() => {
                          setValue('salaryUnitSelect', null);
                        }}
                        options={['Per Hour', 'Per Day', 'Per Year'].map(
                          (val) => ({
                            label: val,
                            value: val,
                          })
                        )}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      ></AutoComplete>
                    )}
                    name="salaryUnit"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/5">
            {handleCheckLogicRender(
              _get(configForm, 'data.1.children'),
              'Salary'
            ) && (
              <>
                <Form.Item label="Salary" name="salary">
                  <Controller
                    render={({ field }) => (
                      <InputNumber
                        formatter={numberWithCommas}
                        addonAfter={<PoundOutlined />}
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        onChange={(value) => {
                          field.onChange(value);
                        }}
                      />
                    )}
                    name="salary"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/5">
            {handleCheckLogicRender(
              _get(configForm, 'data.1.children'),
              'Pay Rate'
            ) && (
              <>
                <Form.Item label="Pay Rate" name="payRate">
                  <Controller
                    render={({ field }) => (
                      <InputNumber
                        addonAfter={<div>%</div>}
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        onChange={(value) => {
                          field.onChange(value);
                          handlePayRateChange(value);
                        }}
                      />
                    )}
                    name="payRate"
                    control={control}
                  />
                </Form.Item>

                {/* <Form.Item label="Pay Rate" name="clientBillRate">
                  <Controller
                    render={({ field }) => (
                      <InputNumber
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        onChange={(value) => {
                          field.onChange(value);
                          handleClientBillRateChange(value);
                        }}
                      />
                    )}
                    name="clientBillRate"
                    control={control}
                  />
                </Form.Item> */}
              </>
            )}
          </div>
          <div className="w-1/5">
            {handleCheckLogicRender(
              _get(configForm, 'data.1.children'),
              'Perm fee (%)'
            ) && (
              <>
                <Form.Item label="Perm fee" name="permFee">
                  <Controller
                    render={({ field }) => (
                      <InputNumber
                        addonAfter={<div>%</div>}
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                      />
                    )}
                    name="permFee"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/5">
            {handleCheckLogicRender(
              _get(configForm, 'data.1.children'),
              'Mark-up %'
            ) && (
              <>
                <Form.Item label="Mark-up" name="markUpPercentage">
                  <Controller
                    render={({ field }) => (
                      <InputNumber
                        addonAfter={<div>%</div>}
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        onChange={(value) => {
                          field.onChange(value);
                          handleMarkupChange(value);
                        }}
                      />
                    )}
                    name="markUpPercentage"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </div>
      </>
    );
  };

  const handleRenderDesiredExperience = () => {
    return (
      <>
        <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
          <span className="font-medium text-lg  ">Desired Experience</span>
        </div>
        {/* <Divider orientation="left">Desired Experience</Divider> */}
        <div className="w-full flex gap-5">
          <div className="w-1/2">
            {handleCheckLogicRender(
              _get(configForm, 'data.2.children'),
              'Industries'
            ) && (
              <>
                <Form.Item
                  label={
                    <p>
                      {/* <span className="text-red-600">*</span>  */}
                      Industries
                    </p>
                  }
                  name="businessSectors"
                  className="mb-3"
                >
                  <Controller
                    rules={{ required: 'This field is required' }}
                    name="businessSectors"
                    control={control}
                    defaultValue={[]}
                    render={({ field }) => (
                      <>
                        <ConfigProvider
                          theme={{
                            token: {
                              colorBgElevated: '#E0EBF9',
                            },
                          }}
                        >
                          <Select
                            tagRender={(props) =>
                              tagRender({ ...props, tagColor: 'tag-orange' })
                            }
                            className="overflow-auto"
                            placeholder="Select Industries"
                            disabled={showCompany || jobDetailCompanyId}
                            required
                            labelInValue
                            mode="multiple"
                            onSearch={(searchText) => {
                              setIndustrySearchText(searchText);
                            }}
                            suffixIcon={
                              getValues().businessSectors?.length !== 0 ? (
                                <CheckOutlined className="text-green-600" />
                              ) : (
                                <Image
                                  src={deleteIcon}
                                  alt="Delete Icon"
                                  width="16px"
                                  height="16px"
                                  preview={false}
                                />
                              )
                            }
                            onPopupScroll={(e) =>
                              handleIndustriesScroll(e, 'BusinessSector')
                            }
                            {...field}
                            loading={isLoadingIndustries}
                            notFoundContent={
                              isLoadingIndustries ? <Spin size="small" /> : null
                            }
                            options={Object.values(
                              industriesOptions.reduce((acc, so) => {
                                acc[so.id] = {
                                  ...so,
                                  label: so.name,
                                  value: so.id,
                                };
                                return acc;
                              }, {})
                            )}
                            filterOption={(inputValue, option) =>
                              option.label
                                .toLowerCase()
                                .indexOf(inputValue.toLowerCase()) !== -1
                            }
                          />
                        </ConfigProvider>
                      </>
                    )}
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/2 ">
            {handleCheckLogicRender(
              _get(configForm, 'data.2.children'),
              'Categories'
            ) && (
              <>
                <Form.Item
                  label={
                    <p>
                      {/* <span className="text-red-600">*</span>  */}
                      Categories
                    </p>
                  }
                  name="categories"
                >
                  <Controller
                    rules={{ required: 'This field is required' }}
                    render={({ field }) => (
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-blue' })
                        }
                        placeholder="Select Categories"
                        disabled={showCompany || jobDetailCompanyId}
                        required
                        labelInValue
                        mode="multiple"
                        onSearch={() => {
                          setValue('categories', null);
                        }}
                        suffixIcon={
                          getValues().categories ? (
                            getValues().categories.length !== 0 ? (
                              <CheckOutlined className="text-green-600" />
                            ) : (
                              <Image
                                src={deleteIcon}
                                alt="Delete Icon"
                                width="16px"
                                height="16px"
                                preview={false}
                              />
                            )
                          ) : (
                            <Image
                              src={deleteIcon}
                              alt="Delete Icon"
                              width="16px"
                              height="16px"
                              preview={false}
                            />
                          )
                        }
                        {...field}
                        notFoundContent={null}
                        options={categoriesOptions.map((so) => ({
                          ...so,
                          label: so.name,
                          value: so.id,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      />
                    )}
                    name="categories"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </div>

        {handleCheckLogicRender(
          _get(configForm, 'data.2.children'),
          'Required Skills'
        ) && (
          <>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Required Skills
                </p>
              }
              name="skills"
              className=" mb-10"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                name="skills"
                control={control}
                defaultValue={[]}
                render={({ field }) => (
                  <Select
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-purple' })
                    }
                    placeholder="Enter Skills to search"
                    disabled={showCompany || jobDetailCompanyId}
                    required
                    labelInValue
                    suffixIcon={
                      getValues().skills.length !== 0 ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    mode="multiple"
                    {...field}
                    notFoundContent={
                      isLoadingSkills ? <Spin size="small" /> : null
                    }
                    options={skillsOptions.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    showSearch={true}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? '')
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? '').toLowerCase())
                    }
                    optionFilterProp="label"
                  />
                )}
              />
            </Form.Item>
          </>
        )}
      </>
    );
  };

  const handleRenderJobLocation = () => {
    return (
      <>
        <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
          <span className="font-medium text-lg  ">Job Location</span>
        </div>
        {/* <Divider orientation="left">Job Location</Divider> */}

        <div className="flex w-full gap-5">
          <div className="w-1/2">
            {handleCheckLogicRender(
              _get(configForm, 'data.3.children'),
              'Address Line 1'
            ) && (
              <>
                {/* Start address */}
                <Form.Item
                  label="Address Line 1"
                  name="address1"
                  className="mb-7"
                >
                  <Controller
                    render={({ field }) => (
                      <AutoComplete
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        options={addressOptions?.data?.map((ao) => ({
                          label: (
                            <div>
                              <div>{ao.display_name || ao.address1}</div>
                              <div
                                style={{ fontSize: '13px', color: '#b3b3b3' }}
                              >
                                {ao?.full_address || ao?.place_formatted}
                              </div>
                            </div>
                          ),
                          value: ao.place_id,
                        }))}
                        onSearch={(value) => {
                          setAddressSearchText(value);
                          setValue('address1', value);
                          setValue('address_line_1', value);
                          setValue('address1Select', value);
                        }}
                        value={getValues('address1')}
                        onSelect={async (selectedAddressId) => {
                          // Not use LAMBDA for now
                          // if (addressOptions?.sourceKey === 'LAMBDA') {
                          //   const selectedAddress = addressOptions?.data?.find(
                          //     (ao) => ao.place_id == selectedAddressId
                          //   );
                          //   // setValue('stateId', selectedAddress.place_id);
                          //   setValue('address1', selectedAddress.display_name);
                          //   setValue(
                          //     'address_line_1',
                          //     selectedAddress.display_name
                          //   );
                          //   setValue('address2', selectedAddress.display_name);
                          //   setValue('zip', selectedAddress.address?.postcode);
                          //   setValue('county', selectedAddress.address?.county);
                          //   setValue(
                          //     'countySelect',
                          //     selectedAddress.address?.county
                          //   );
                          //   setValue('city', selectedAddress.address?.state);
                          //   setValue('state', selectedAddress.address?.country);
                          // } else {
                          const selectedAddress = addressOptions?.data?.find(
                            (ao) =>
                              ao.id == selectedAddressId ||
                              ao.place_id == selectedAddressId
                          );
                          setPlaceId(selectedAddressId);
                          setValue('address1', selectedAddress.address1);
                          setValue('address_line_1', selectedAddress.address1);
                          setValue('address2', selectedAddress.address2);
                          setValue('address1Select', selectedAddressId);
                          const state = countries?.find((item) => item.label === selectedAddress?.context?.country?.name)
                          setValue('stateId', state?.value);
                          setValue('state', state?.label)
                          const countryCode = selectedAddress?.context?.country?.country_code

                          const listCounTries = await handleRefetchCountry(state?.value)
                          let countySelect = listCounTries?.find((item) => ( item.label === selectedAddress?.context?.region?.name))
                          if (!countySelect) {
                                let countyFinder = retriveFindCounty(selectedAddress?.context?.place?.name, countryCode)
                                if (countyFinder) {
                                  countySelect = listCounTries?.find((item) => ( item.label.trim().toLowerCase() === countyFinder.trim().toLowerCase()))
                                }
                          }
                          setValue('county', countySelect?.value);
                          setValue('countySelect', countySelect)
                          // }
                        }}
                      ></AutoComplete>
                    )}
                    name="address1"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
          <div className="w-1/2">
            {handleCheckLogicRender(
              _get(configForm, 'data.3.children'),
              'Address Line 2'
            ) && (
              <>
                {/* Start address */}
                <Form.Item
                  label="Address Line 2"
                  name="address2"
                  className="mb-7"
                >
                  <Controller
                    render={({ field }) => (
                      <AutoComplete
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        options={addressOptions?.data?.map((ao) => ({
                          label: ao.display_name || ao.address1,
                          value: ao.place_id,
                        }))}
                        onSearch={(value) => {
                          setAddressSearchText(value);
                          setValue('address2', value);
                          setValue('address_line_2', value);
                          setValue('address2Select', value);
                        }}
                        onSelect={async (selectedAddressId) => {
                          // Not use LAMBDA for now
                          // if (addressOptions?.sourceKey === 'LAMBDA') {
                          //   const selectedAddress = addressOptions?.data?.find(
                          //     (ao) => ao.place_id == selectedAddressId
                          //   );
                          //   // setValue('stateId', selectedAddress.place_id);
                          //   setValue('address1', selectedAddress.display_name);
                          //   setValue(
                          //     'address_line_1',
                          //     selectedAddress.display_name
                          //   );
                          //   setValue('address2', selectedAddress.display_name);
                          //   setValue('zip', selectedAddress.address?.postcode);
                          //   setValue('county', selectedAddress.address?.county);
                          //   setValue(
                          //     'countySelect',
                          //     selectedAddress.address?.county
                          //   );
                          //   setValue('city', selectedAddress.address?.state);
                          //   setValue('state', selectedAddress.address?.country);
                          // } else {
                          const selectedAddress = addressOptions?.data?.find(
                            (ao) => ao.id == selectedAddressId
                          );
                          // setValue('stateId', null);
                          setPlaceId(selectedAddressId);
                          const state = countries?.find((item) => item.label === selectedAddress?.context?.country?.name)
                          setValue('stateId', state?.value);
                          setValue('address1', selectedAddress.address1);
                          setValue('address_line_1', selectedAddress.address1);
                          setValue('address2', selectedAddress.address1);
                          setValue('address1Select', selectedAddressId);
                          setValue('county', state?.value);
                          setValue('countySelect', state)
                          // }
                        }}
                      ></AutoComplete>
                    )}
                    name="address2"
                    control={control}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </div>

        <Row gutter={[8, 8]}>
          {handleCheckLogicRender(
            _get(configForm, 'data.3.children'),
            'City'
          ) && (
            <>
              <Col span={8}>
                <Form.Item label="City" name="city">
                  <Controller
                    render={({ field }) => (
                      <Input
                        required
                        className="mt-2 rounded-xl"
                        disabled={showCompany || jobDetailCompanyId}
                        suffix={
                          getValues().city !== '' && getValues().city ? (
                            <CheckOutlined className="text-green-600" />
                          ) : (
                            <Image
                              src={deleteIcon}
                              alt="Delete Icon"
                              width="16px"
                              height="16px"
                              preview={false}
                            />
                          )
                        }
                        {...field}
                      />
                    )}
                    name="city"
                    control={control}
                  />
                </Form.Item>
              </Col>
            </>
          )}

          {handleCheckLogicRender(
            _get(configForm, 'data.3.children'),
            'County'
          ) && (
            <>
              <Col span={8}>
                <Form.Item label="County/State" name="county">
                  <Controller
                    name="county"
                    control={control}
                    render={({ field }) => (
                      <AutoComplete
                        disabled={showCompany || jobDetailCompanyId}
                        {...field}
                        className="search-input"
                        options={listCountySelect}
                        onSelect={(value, record) => {
                          setValue('countySelect', value);
                        }}
                        value={watch("county")}
                        onSearch={(searchText) => {
                          setValue('county', searchText);
                          setValue('countySelect', null);
                        }}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                      >
                        <Input
                          suffix={
                            loadingListCountySelect ? (
                              <Spin />
                            ) : watch().countySelect !== '' &&
                              watch().countySelect ? (
                              <CheckOutlined className="text-green-600" />
                            ) : (
                              <Image
                                src={deleteIcon}
                                alt="Delete Icon"
                                width="16px"
                                height="16px"
                                preview={false}
                              />
                            )
                          }
                        />
                      </AutoComplete>
                    )}
                  />
                </Form.Item>
              </Col>
            </>
          )}

          {handleCheckLogicRender(
            _get(configForm, 'data.3.children'),
            'Postcode'
          ) && (
            <>
              <Col span={8}>
                <Form.Item label="Postcode" name="zip">
                  <Controller
                    render={({ field }) => (
                      <Input
                        className="mt-2 rounded-xl"
                        disabled={showCompany || jobDetailCompanyId}
                        suffix={
                          isLoadingAddressDetails ? (
                            <Spin />
                          ) : getValues().zip !== '' && getValues().zip ? (
                            <CheckOutlined className="text-green-600" />
                          ) : (
                            <Image
                              src={deleteIcon}
                              alt="Delete Icon"
                              width="16px"
                              height="16px"
                              preview={false}
                            />
                          )
                        }
                        {...field}
                      />
                    )}
                    name="zip"
                    control={control}
                  />
                </Form.Item>
              </Col>
            </>
          )}
        </Row>

        {handleCheckLogicRender(
          _get(configForm, 'data.3.children'),
          'Country'
        ) && (
          <>
            <Form.Item label="Country" name="state" className="mb-12">
              <Controller
                render={({ field }) => (
                  <AutoComplete
                    disabled={showCompany || jobDetailCompanyId}
                    {...field}
                    options={countries}
                    onSearch={(searchText) => {
                      setValue('state', searchText);
                      setValue('stateId', null);
                      setValue('stateSelected', null);
                    }}
                    value={watch("state")}
                    filterOption={(input, option) =>
                      option?.label
                        ?.toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                    onSelect={(value) => {
                      setValue('stateSelected', value);
                      setValue('stateId', value);
                      const countryName = countries.find(
                        (co) => co.value == value
                      )?.label;
                      setValue('state', countryName);
                    }}
                  >
                    <Input
                      required
                      value={watch("state")}
                      suffix={
                        isLoadingAddressDetails ? (
                          <Spin />
                        ) : (watch().stateId !== '' && watch().stateId) ||
                          (watch().state !== '' && watch().state) ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      }
                    />
                  </AutoComplete>
                )}
                name="state"
                control={control}
              />
            </Form.Item>
          </>
        )}
      </>
    );
  };

  const handleRenderJobBoardPublishing = () => {
    return (
      <>
        <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
          <span className="font-medium text-lg  ">
            Job Board Publishing (Optional)
          </span>
        </div>
        {/* <Divider orientation="left">Job Board Publishing (Optional)</Divider> */}

        {handleCheckLogicRender(
          _get(configForm, 'data.4.children'),
          'Published Description'
        ) && (
          <>
            <Form.Item
              label="Published Description"
              name="publishedDescription"
            >
              <Controller
                render={({ field }) => (
                  <Input.Group className="flex gap-2">
                    <Input.TextArea
                      placeholder="Enter Description"
                      disabled={showCompany || jobDetailCompanyId}
                      rows={4}
                      {...field}
                    />
                  </Input.Group>
                )}
                name="publishedDescription"
                control={control}
              />
            </Form.Item>
          </>
        )}
      </>
    );
  };

  const handleRenderEmail = () => {
    return (
      <div
      // style={{
      //   paddingBottom: '24px',
      // }}
      >
        <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
          <span className="font-medium text-lg  ">Email Notification</span>
        </div>
        {/* <Divider orientation="left">Email Notification</Divider> */}

        {handleCheckLogicRender(
          _get(configForm, 'data.5.children'),
          'Internal User'
        ) && (
          <>
            <Form.Item
              label="Internal User"
              name="internalUser"
              // className="mb-12"
            >
              <Controller
                render={({ field }) => (
                  <Input
                    disabled={showCompany || jobDetailCompanyId}
                    {...field}
                  />
                )}
                name="internalUser"
                control={control}
              />
            </Form.Item>
          </>
        )}
      </div>
    );
  };

  const handleRenderAction = () => {
    return (
      <div className="absolute left-0 bottom-4 w-full ">
        <div className="flex gap-4 mx-7 justify-start">
          <Button
            disabled={showCompany || jobDetailCompanyId || sendingStatus}
            onClick={handleCancel}
            className={`bg-cyan-600  `}
          >
            Cancel
          </Button>
          <Button
            disabled={showCompany || jobDetailCompanyId || sendingStatus}
            onClick={() => {
              setHaveSendJob(false);
              setOpenSendEmail(!openModalSendEmail);
            }}
            className={`bg-cyan-600  h-fit py-2 px-9 rounded-xl`}
            // style={{ color: '#D7D7D7' }}
          >
            Next
          </Button>
          <Button
            disabled={
              showCompany ||
              jobDetailCompanyId ||
              isLockedSendJob ||
              sendingStatus
            }
            htmlType="submit"
            className={`bg-white text-cyan-600  ${
              !showCompany || (jobDetailCompanyId && 'hover:bg-blue-700')
            }`}
            loading={sendingStatus}
            // style={{ color: '#D7D7D7' }}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Form onFinish={handleSubmitBullhorn} layout="vertical">
      {!_get(configForm, 'data') && (
        <>
          <LoadingAdvanced isSkeleton />
          <LoadingAdvanced isSkeleton />
          <LoadingAdvanced isSkeleton />
        </>
      )}

      {_get(configForm, 'data.0.on') && handleRenderJobInformation()}

      {_get(configForm, 'data.1.on') && handleRenderCompensationFees()}

      {_get(configForm, 'data.2.on') && handleRenderDesiredExperience()}

      {_get(configForm, 'data.3.on') && handleRenderJobLocation()}

      {_get(configForm, 'data.4.on') && handleRenderJobBoardPublishing()}

      {_get(configForm, 'data.5.on') && handleRenderEmail()}

      {handleRenderModal()}

      {/* {handleRenderAction()} */}
    </Form>
  );
}

export default BullHornJobSubmissionForm;
