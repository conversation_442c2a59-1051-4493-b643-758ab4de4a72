import {
  AutoComplete,
  Button,
  ConfigProvider,
  Form,
  Image,
  Input,
  Select,
  Spin,
} from 'antd';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhorn, searchBullhornData } from '../../services/bullhorn';
import { useEffect, useMemo, useRef, useState } from 'react';
import _get from 'lodash/get';
import _map from 'lodash/map';
import { useSelector } from 'react-redux';
import { selectCompanyResponse } from '../../store/common';
import deleteIcon from '../../assets/img/icons/delete-icon.png';
import {
  CheckOutlined,
  EditOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  PhoneOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Controller } from 'react-hook-form';
import { getLDetailEmployee } from '../../services/employee';
import { tagRender } from '../SearchDetailV2/NewSearchFilterComponent';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';

const BullhornBulkAddContactModal = (props) => {
  const { control, setValue, getValues, dataContacts } = props;

  const [handleCloseClient, setHandleCloseClient] = useState(false);
  const [isSearchCompany, setIsSearchCompany] = useState(false);
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();
  const userToSet = profileUser || profileUserAuth;
  const checkHenley = userToSet?.organization?.name === "HENLEY"

  const timeoutRef = useRef(null);

  const {
    options: callCompanyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const {
    options: skillsOptions,
    handleScrollPopup: handleSkillScroll,
    handleSearch: setSkillSearchText,
    isLoading: isLoadingSkills,
  } = useInfiniteScrollWithSearch(searchBullhornData('Skill'));

  const {
    options: categoriesOptions,
    handlePopupScroll: handleCategoryScroll,
    handleSearch: setCategorySearchText,
    isLoading: isLoadingCategories,
  } = useInfiniteScrollWithSearch(searchBullhornData('Category'));

  const {
    options: contactIndustriesOptions,
    handleScrollPopup: handleContactIndustriesScroll,
    handleSearch: setContactIndustrySearchText,
    isLoading: isLoadingContactIndustries,
  } = useInfiniteScrollWithSearch(searchBullhornData('BusinessSector'), 1000);

  const handleLoadData = async () => {
    handleCompanySearch(' ');
    setSkillSearchText(' ');
    setCategorySearchText(' ');
    setContactIndustrySearchText(' ');
  };

  useEffect(() => {
    handleLoadData();
  }, []);

  const checkIdInArray = (id) => {
    return dataContacts?.some((item) => item.person_id === id);
  };

  return (
    <>
      <div className="px-5 pt-5">
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: '20px',
          }}
        >
          {/* <div
            style={{
              width: '25%',
            }}
          >
            {listEmployee
              ?.filter((obj) => selectedRowKeys.includes(obj.id))
              ?.map((item) => {
                return <div style={{
                  padding: "10px",
                  background: "#fff",
                  cursor: "pointer",
                  // background: checkIdInArray(item?.id) ? "red" : "white"
                  display: "flex",
                  justifyContent: "space-between"
                }}><div>{item?.name}
                  </div><div> {checkIdInArray(item?.id) ? (<CheckOutlined className="text-green-600" />) : ""}</div></div>;
              })}
          </div> */}
          <div
            style={{
              width: '100%',
              // border: "2px solid #fff",
              padding: '10px',
            }}
          >
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Company Name
                </p>
              }
              name="clientContactBulk.company"
              className="pb-4"
            >
              <Controller
                render={({ field }) => (
                  <ConfigProvider
                    theme={{
                      token: {
                        colorBgElevated:
                          callCompanyOptions?.[0]?.id === 0
                            ? '#FFFFFF'
                            : '#E0EBF9',
                      },
                    }}
                  >
                    <AutoComplete
                      {...field}
                      open={handleCloseClient}
                      onMouseDown={(e) => setHandleCloseClient(true)}
                      onPopupScroll={(e) =>
                        handleCompanyScroll(e, 'ClientCorporation')
                      }
                      options={_map(callCompanyOptions, (option) => ({
                        value: option?.id,
                        label: (
                          <div className="grid">
                            <div className="flex justify-between">
                              <span className="text-base font-base">
                                {option?.id} | {option?.name}
                              </span>
                              <Button
                                className="absolute right-3 top-2"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // setShowEditCompany(true);
                                  // setIsEditCompany(true);
                                  // setShowCompany(false);
                                  setValue(
                                    'clientContactBulk.companySelect',
                                    option?.id
                                  );
                                  setValue(
                                    'clientContactBulk.companyId',
                                    option?.id
                                  );
                                  const companyName = callCompanyOptions.find(
                                    (co) => co?.id == option?.id
                                  )?.name;
                                  setValue(
                                    'clientContactBulk.company',
                                    companyName
                                  );
                                  setHandleCloseClient(false);
                                  // setTimeout(() => {
                                  //   setShowCompany(true);
                                  // }, 100);
                                }}
                              >
                                <EditOutlined />
                              </Button>
                            </div>
                            <div className="contact-details">
                              <div className="flex">
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <PhoneOutlined />
                                  {option?.phone ? option?.phone : '-'}
                                </span>
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <InfoCircleOutlined />
                                  {option?.status ? option?.status : '-'}
                                </span>
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <EnvironmentOutlined />
                                  {option?.address &&
                                  option?.address.city &&
                                  option?.address.state
                                    ? `${option?.address.city}, ${option?.address.state}`
                                    : option?.address && option?.address.city
                                      ? option?.address.city
                                      : option?.address && option?.address.state
                                        ? option?.address.state
                                        : '-'}
                                </span>
                              </div>
                            </div>
                          </div>
                        ),
                        record: option?.address,
                        email: option?.email,
                      }))}
                      onSearch={(searchText) => {
                        if (timeoutRef.current) {
                          clearTimeout(timeoutRef.current);
                        }
                        timeoutRef.current = setTimeout(() => {
                          if (searchText) companySetStart(0);
                          setValue('clientContactBulk.companySelect', null);
                          setHandleCloseClient(true);
                          setValue('clientContactBulk.companyId', null);
                          setValue('clientContactBulk.company', searchText);
                          handleCompanySearch(searchText);
                        }, 500);
                      }}
                      onSelect={(value, option) => {
                        const currentCompany = callCompanyOptions.find(
                          (co) => co?.id == value
                        );
                        setValue(
                          'clientContactBulk.workPhone',
                          currentCompany?.phone
                        );
                        setValue(
                          'clientContactBulk.mobilePhone',
                          currentCompany?.phone
                        );
                        setValue(
                          'clientContactBulk.fax',
                          currentCompany?.phone
                        );
                        setValue(
                          'clientContactBulk.address',
                          currentCompany?.address?.address1 ||
                            currentCompany?.address?.address2
                        );
                        setValue(
                          'clientContactBulk.county',
                          currentCompany?.address?.state
                        );
                        setValue(
                          'clientContactBulk.state',
                          currentCompany?.address?.countryName
                        );
                        setValue(
                          'clientContactBulk.stateId',
                          currentCompany?.address?.countryID
                        );
                        setValue(
                          'clientContactBulk.countySelect',
                          currentCompany?.address?.countryID
                        );
                        setValue(
                          'clientContactBulk.city',
                          currentCompany?.address?.city
                        );
                        setValue(
                          'clientContactBulk.zip',
                          currentCompany?.address?.zip
                        );
                        //   setShowEditCompany(false);
                        //   setIsAddCompany(false);
                        setIsSearchCompany(false);
                        //   setIsEditCompany(true);
                        //   setShowCompany(false);
                        setValue('clientContactBulk.companySelect', value);
                        setValue('clientContactBulk.companyId', value);
                        const companyName = callCompanyOptions?.find(
                          (co) => co.id == value
                        )?.name;
                        setValue('clientContactBulk.company', companyName);
                        setHandleCloseClient(false);
                        // setTimeout(() => setShowCompany(true), 100);
                        // Update industries
                        const businessSectorList =
                          currentCompany?.businessSectorList || [];
                        if (businessSectorList.length > 0) {
                          const filteredValues = contactIndustriesOptions
                            .filter((obj) =>
                              currentCompany?.businessSectorList.includes(
                                obj.name
                              )
                            )
                            .map((obj) => ({ value: obj.id, label: obj.name }));

                          setValue(
                            'clientContactBulk.industries',
                            filteredValues
                          );
                        }
                      }}
                      dropdownRender={(menus) => (
                        <>
                          {callCompanyOptions[0]?.id === 0 ? (
                            <Button
                              onClick={() => {
                                //   setIsSearchCompany(false);
                                //   setValue(
                                //     'clientContactBulk.companySelect',
                                //     valueNotFoundCompany
                                //   );
                                //   setValue('clientContactBulk.companyId', null);
                                //   setValue(
                                //     'clientContactBulk.company',
                                //     valueNotFoundCompany
                                //   );
                                //   setValue(
                                //     'clientCorporation.name',
                                //     valueNotFoundCompany
                                //   );
                                //   setHandleCloseClient(false);
                                //   setIsEditCompany(false);
                                //   setShowCompany(true);
                                //   setIsAddCompany(true);
                              }}
                              onMouseLeave={() => setHandleCloseClient(false)}
                              className="flex gap-1 w-full h-fit bg-white"
                              value={valueNotFoundCompany}
                            >
                              <PlusOutlined className="my-auto" />{' '}
                              <span className="my-auto">Add new</span>{' '}
                              {valueNotFoundCompany}
                            </Button>
                          ) : (
                            <div
                              onMouseLeave={() => setHandleCloseClient(false)}
                            >
                              <Button
                                onClick={() => {
                                  // setIsSearchCompany(false);
                                  // setValue(
                                  //   'clientContactBulk.companySelect',
                                  //   valueNotFoundCompany
                                  // );
                                  // setValue('clientContactBulk.companyId', null);
                                  // setValue(
                                  //   'clientContactBulk.company',
                                  //   valueNotFoundCompany
                                  // );
                                  // setValue(
                                  //   'clientCorporation.name',
                                  //   valueNotFoundCompany
                                  // );
                                  // setHandleCloseClient(false);
                                  // setIsEditCompany(false);
                                  // setShowCompany(true);
                                  // setIsAddCompany(true);
                                  // setShowEditCompany(true);
                                }}
                                className="flex gap-1 w-full h-fit bg-white"
                              >
                                <PlusOutlined className="my-auto" />{' '}
                                <span className="my-auto">Add new</span>{' '}
                                {valueNotFoundCompany}
                              </Button>
                              {menus}
                            </div>
                          )}
                        </>
                      )}
                    >
                      <div className="custom-input">
                        <span
                          className={`${!getValues().clientContactBulk.companyId && !isSearchCompany ? 'input-text-select-add' : getValues().clientContactBulk.companyId ? 'input-text-select-company-contact' : 'input-text'}`}
                        >
                          {getValues().clientContactBulk.company}
                        </span>
                        <Input
                          value={getValues().clientContactBulk.company}
                          {...field}
                          required
                          // suffix={
                          //   isLoadingCompany ? (
                          //     <Spin />
                          //   ) : getValues().clientContactBulk.companyId !== '' &&
                          //     getValues().clientContactBulk.companyId ? (
                          //     <>
                          //       <CheckOutlined className="text-green-600" />
                          //       {(!isAddCompany && isSearchCompany) ||
                          //       getValues().clientContactBulk.companyId ? (
                          //         <Button
                          //           onClick={(e) => {
                          //             e.stopPropagation();
                          //             setShowEditCompany(true);
                          //             setIsEditCompany(true);
                          //             setShowCompany(false);
                          //             setHandleCloseClient(false);
                          //             setTimeout(() => {
                          //               setShowCompany(true);
                          //               setIsAddCompany(false);
                          //             }, 100);
                          //           }}
                          //         >
                          //           <EditOutlined />
                          //         </Button>
                          //       ) : (
                          //         ''
                          //       )}
                          //     </>
                          //   ) : (
                          //     <Image
                          //       src={deleteIcon}
                          //       alt="Delete Icon"
                          //       width="16px"
                          //       height="16px"
                          //       preview={false}
                          //     />
                          //   )
                          // }
                        />
                      </div>
                    </AutoComplete>
                  </ConfigProvider>
                )}
                name="clientContactBulk.company"
                control={control}
              />
            </Form.Item>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Status
                </p>
              }
              name="clientContactBulk.status"
              className="pb-4 center-select-row-customize"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                name="clientContactBulk.status"
                control={control}
                defaultValue={[]}
                render={({ field }) => (
                  <Select
                    placeholder="Select Status"
                    required
                    labelInValue
                    // mode="multiple"
                    suffixIcon={
                      getValues()?.clientContactBulk?.status.length !== 0 ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    {...field}
                    options={[
                      'Prospect',
                      'Active',
                      'Passive Account',
                      'DNC',
                      'Archive',
                      'Live Lead',
                    ].map((val) => ({ label: val, value: val }))}
                    filterOption={(inputValue, option) =>
                      option?.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  />
                )}
              />
            </Form.Item>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Categories
                </p>
              }
              name="clientContactBulk.categories"
            >
              <Controller
                rules={checkHenley ? { required: 'This field is required' } : {}}
                render={({ field }) => (
                  <Select
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-orange' })
                    }
                    placeholder="Select Categories"
                    // disabled={showCompany || jobDetailCompanyId}
                    required
                    labelInValue
                    mode="multiple"
                    onSearch={() => {
                      setValue('categories', null);
                    }}
                    suffixIcon={
                      checkHenley ? (getValues('clientContactBulk.categories') ? (
                        getValues('clientContactBulk.categories')?.length !== 0 ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )) : false
                    }
                    {...field}
                    notFoundContent={null}
                    options={categoriesOptions.map((so) => ({
                      ...so,
                      label: so.name,
                      value: so.id,
                    }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  />
                )}
                name="clientContactBulk.categories"
                control={control}
              />
            </Form.Item>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Required Skills
                </p>
              }
              name="clientContactBulk.skills"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                name="clientContactBulk.skills"
                control={control}
                defaultValue={[]}
                render={({ field }) => (
                  <Select
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-purple' })
                    }
                    placeholder="Enter Skills to search"
                    required
                    labelInValue
                    mode="multiple"
                    {...field}
                    notFoundContent={
                      isLoadingSkills ? <Spin size="small" /> : null
                    }
                    options={skillsOptions.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    showSearch={true}
                    filterSort={(optionA, optionB) =>
                      (optionA?.label ?? '')
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? '').toLowerCase())
                    }
                    optionFilterProp="label"
                  />
                )}
              />
            </Form.Item>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Industry
                </p>
              }
              name="clientContactBulk.industries"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                name="clientContactBulk.industries"
                control={control}
                defaultValue={[]}
                render={({ field }) => (
                  <Select
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-blue' })
                    }
                    placeholder="Select Industry"
                    required
                    labelInValue
                    mode="multiple"
                    suffixIcon={
                      getValues()?.clientContactBulk?.industries.length !==
                      0 ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    onSearch={(searchText) => {
                      setContactIndustrySearchText(searchText);
                    }}
                    onPopupScroll={(e) =>
                      handleContactIndustriesScroll(e, 'BusinessSector')
                    }
                    {...field}
                    loading={isLoadingContactIndustries}
                    notFoundContent={
                      isLoadingContactIndustries ? <Spin size="small" /> : null
                    }
                    options={contactIndustriesOptions.map((so) => ({
                      ...so,
                      label: so.name,
                      value: so.id,
                    }))}
                    filterOption={(inputValue, option) =>
                      option?.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  />
                )}
              />
            </Form.Item>
          </div>
        </div>
      </div>
    </>
  );
};

export default BullhornBulkAddContactModal;
