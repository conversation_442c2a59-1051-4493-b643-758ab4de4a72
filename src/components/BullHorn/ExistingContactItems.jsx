import { Button, Space, Tag, notification } from 'antd';
import { useEffect, useState } from 'react';
import { validEmail } from '../../services/emailFinder';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

const ExistingContactItems = (props) => {
  const [validStatus, setValidStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const { record, listEmailChecked } = props;
  const handleValidEmail = async () => {
    try {
      if(record?.email) {
        setLoading(true);
        const { data } = await validEmail({ email: record.email });
        setValidStatus(data?.result?.result?.verdict)
        setLoading(false);
      } else {
        setValidStatus('Invalid');
      }
    } catch (err) {
      notification.error({
        description: 'some things went wrong',
      });
    }
  };

  useEffect(() => {
    if(listEmailChecked?.length > 0) {
      const foundItem = false;
      for (let i = 0; i < listEmailChecked.length; i++) {
          if (listEmailChecked[i].email === record.email) {
            setValidStatus(listEmailChecked[i].result);
              break;
          }
      }
    }
  }, [listEmailChecked])

  return (
    <Space size="middle">
      <div style={{ width: '250px' }} className='line-clamp-1' title={record?.email}>{record?.email}</div>
      {validStatus && <div><Tag color={validStatus == "Valid" ? "#87d068" : "#f50"}>{validStatus}</Tag></div>}
      {!validStatus && (
        <Button
          disabled={loading}
          onClick={async () => {
            handleValidEmail();
          }}
          // icon={<CheckOutlined />}
        >
          Validate
        </Button>
      )}
    </Space>
  );
};

export default ExistingContactItems;
