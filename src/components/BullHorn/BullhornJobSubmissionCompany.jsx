/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import { countries } from 'country-list-json';
import { Controller } from 'react-hook-form';
import {
  Input,
  Select,
  Button,
  InputNumber,
  Form,
  AutoComplete,
  Spin,
  Row,
  Col,
  ConfigProvider,
  Image,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import useGoogleMapAddressDetails from '../../hooks/useGoogleMapAddressDetails';
import {
  searchBullhornData,
  getCounties,
  searchBullhorn,
  getCountries,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchAddressWithOpenStreet } from '../../services/googleMap';
import React, { useEffect, useMemo, useState } from 'react';
import {
  CheckOutlined,
  PlusOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import deleteIcon from '../../assets/img/icons/delete-icon.png';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { getCountyByCity, retriveFindCounty, US_CITIES } from '../../constants/cities';

{
  /* <Image
  src={deleteIcon}
  alt="Delete Icon"
  width="16px"
  height="16px"
  preview={false}
/>; */
}
let searchTimer;
export const HENLEY = 'Henley';

const { Option } = Select;
const googleKey = import.meta.env.VITE_KEY_GOOGLE;

const countryCodeNumberList = countries.map((country) => ({
  label: country.dial_code,
  value: country.dial_code,
}));

function BullHornJobSubmissionCompany({
  isEditCompany,
  control,
  setValue,
  getValues,
  handleCloseClient,
  setHandleCloseClient,
  watch,
  isFromAddContact,
  dataSearch = false,
}) {
  const {
    options: parentCompanyOptions,
    handlePopupScroll: handleParentCompanyScroll,
    handleSearch: handleParentCompanySearch,
    isLoading: isLoadingParentCompany,
    valueNotFound: valueNotFoundParentCompany,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const {
    options: categoriesOptions,
    handlePopupScroll: handleCategoryScroll,
    handleSearch: setCategorySearchText,
    isLoading: isLoadingCategories,
  } = useInfiniteScrollWithSearch(searchBullhornData('Category'));

  const {
    options: skillsOptions,
    handleScrollPopup: handleSkillScroll,
    handleSearch: setSkillSearchText,
    isLoading: isLoadingSkills,
  } = useInfiniteScrollWithSearch(searchBullhornData('Skill'));

  const {
    options: companyOwnerOption,
    handlePopupScroll: handleCompanyOwnerScroll,
    handleSearch: handleCompanyOwnerSearch,
    isLoading: isLoadingCompanyOwner,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const {
    options: companyIndustriesOptions,
    handleScrollPopup: handleCompanyIndustriesScroll,
    handleSearch: setCompanyIndustrySearchText,
    isLoading: isLoadingCompanyIndustries,
  } = useInfiniteScrollWithSearch(searchBullhornData('BusinessSector'));

  const {
    searchOptions: companyAddressOptions,
    setSearchText: setCompanyAddressSearchText,
    isLoading: isLoadingCompanyAddresses,
  } = useSearchWithDebounce(searchAddressWithOpenStreet);

  // const {
  //   setPlaceId: setCompanyBillingPlaceId,
  //   isLoading: isLoadingCompanyBillingAddress,
  // } = useGoogleMapAddressDetails(setValue, false, 'companyBilling');

  const {
    searchOptions: companyBillingAddressOptions,
    setSearchText: setCompanyBillingAddressSearchText,
    isLoading: isLoadingCompanyBillingAddresses,
  } = useSearchWithDebounce(searchAddressWithOpenStreet);

  const { data: ukCounties = [] } = useQuery(
    ['GET_COUNTIES'],
    async () => {
      const { data } = await getCounties({ filter: '', countryId: 2359 });
      return data.result;
    },
    { enabled: true }
  );

   // Update refetch county

  const [listCountySelect, setListCountySelect] = useState()
  const [loadingListCountySelect, setLoadingListCountySelect] = useState(false)

  const handleRefetchCountry = async (stateId) => {
      setLoadingListCountySelect(true)
       const { data: dataCounties } = await getCounties({
          filter: '',
          countryId: stateId,
        });
        setLoadingListCountySelect(false)
        setListCountySelect(dataCounties?.result)
        return dataCounties?.result;
  }

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;
  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  const handleGetCompanyDetail = async () => {
    await resetCompany();
    const { data } = isFromAddContact
      ? await searchBullhorn(
          'ClientCorporation',
          0,
          10,
          getValues().clientContact.company,
          '',
          getValues().clientContact.companyId
        )
      : await searchBullhorn(
          'ClientCorporation',
          0,
          10,
          getValues().company,
          '',
          getValues().companyId
        );
    if (data.result.length === 0) return;
    const detailCompany = data.result[0];
    setValue(
      'clientCorporation.companyAddress',
      `${detailCompany?.address?.address1}`
    );
    setValue(
      'clientCorporation.companyAddressSelect',
      `${detailCompany?.address?.address1}`
    );
    setValue('clientCorporation.city', `${detailCompany?.address?.city}`);
    setValue('clientCorporation.zip', `${detailCompany?.address?.zip}`);
    setValue('clientCorporation.county', `${detailCompany?.address?.state}`);
    setValue(
      'clientCorporation.countySelect',
      `${detailCompany?.address?.state}`
    );
    setValue(
      'clientCorporation.state',
      `${detailCompany?.address?.countryName}`
    );
    setValue(
      'clientCorporation.stateId',
      `${detailCompany?.address?.countryID}`
    );
    setValue(
      'clientCorporation.stateSelected',
      `${detailCompany?.address?.countryID}`
    );
    setCompanyAddressSearchText(detailCompany?.address?.address1);
    setValue('clientCorporation.name', detailCompany?.name);
    setValue('clientCorporation.status', detailCompany?.status);
    setValue(
      'clientCorporation.parentCompanySelect',
      detailCompany?.parentClientCorporation?.name
    );
    setValue(
      'clientCorporation.parentCompany',
      detailCompany?.parentClientCorporation?.name
    );
    setValue(
      'clientCorporation.parentCompanyId',
      detailCompany?.parentClientCorporation?.id
    );
    if (detailCompany?.owners && detailCompany?.owners?.total >= 1) {
      setValue(
        'clientCorporation.companyOwner',
        `${detailCompany?.owners?.data[0].firstName} ${detailCompany?.owners?.data[0].lastName}`
      );
      setValue(
        'clientCorporation.companyOwnerSelect',
        `${detailCompany?.owners?.data[0].firstName} ${detailCompany?.owners?.data[0].lastName}`
      );
      setValue(
        'clientCorporation.companyOwnerId',
        detailCompany?.owners?.data[0].id
      );
    }
    const newIndustries = [];
    if (detailCompany?.businessSectorList) {
      if (detailCompany?.businessSectorList.length > 0) {
        detailCompany?.businessSectorList.forEach((item) => {
          newIndustries.push({ label: item, value: item });
        });
      }
    }
    setValue('clientCorporation.industries', newIndustries);
    setValue('clientCorporation.companyComments', detailCompany?.notes);
    setValue(
      'clientCorporation.companyDescription',
      detailCompany?.companyDescription || ''
    );
    const companyURL = detailCompany?.companyURL || '';
    let truncatedURL = '';

    if (companyURL && companyURL.length > 100) {
      truncatedURL = companyURL.substring(0, 100);
    } else {
      truncatedURL = companyURL;
    }
    const mainPhoneSplit = detailCompany?.phone?.split(')') || '';
    const billingPhoneSplit = detailCompany?.billingPhone?.split(')') || '';

    const mainPhone =
      (detailCompany?.phone && mainPhoneSplit[1]) || detailCompany?.phone;
    const billingPhone =
      (detailCompany?.billingPhone && billingPhoneSplit[1]) ||
      detailCompany?.billingPhone;
    setValue(
      'clientCorporation.countryCodeSelect',
      mainPhoneSplit[0]?.replace('(', '') || ''
    );
    setValue(
      'clientCorporation.countryCodeBillingPhoneSelect',
      billingPhoneSplit[0]?.replace('(', '') || ''
    );

    setValue('clientCorporation.companyWebsite', companyURL);
    setValue('clientCorporation.mainPhone', mainPhone);
    setValue('clientCorporation.billingContact', detailCompany?.billingContact);
    setValue('clientCorporation.billingPhone', billingPhone);
    setValue(
      'clientCorporation.billingAddress',
      detailCompany?.billingAddress?.address1
    );
    setValue(
      'clientCorporation.billingAddressSelect',
      detailCompany?.billingAddress?.address1
    );
    setCompanyBillingAddressSearchText(
      detailCompany?.billingAddress?.address1 || ''
    );
    setValue(
      'clientCorporation.billingCity',
      detailCompany?.billingAddress?.city
    );
    setValue(
      'clientCorporation.billingCounty',
      detailCompany?.billingAddress?.county
    );
    setValue(
      'clientCorporation.billingCountySelect',
      detailCompany?.billingAddress?.county
    );
    setValue(
      'clientCorporation.billingZip',
      detailCompany?.billingAddress?.zip
    );
    setValue(
      'clientCorporation.billingState',
      detailCompany?.billingAddress?.countryName
    );
    setValue(
      'clientCorporation.invoiceFormatInformation',
      detailCompany?.invoiceFormat
    );
    setValue(
      'clientCorporation.standardFeeArrangement',
      detailCompany?.feeArrangement
    );
  };

  const resetCompany = () => {
    setValue('clientCorporation.status', '');
    setValue('clientCorporation.parentCompanySelect', '');
    setValue('clientCorporation.parentCompany', '');
    setValue('clientCorporation.parentCompanyId', '');
    setValue('clientCorporation.companyOwner', '');
    setValue('clientCorporation.companyOwnerSelect', '');
    setValue('clientCorporation.companyOwnerId', '');
    setValue('clientCorporation.industries', []);
    setValue('clientCorporation.categories', []);
    setValue('clientCorporation.companyComments', '');
    setValue('clientCorporation.companyDescription', '');
    setValue('clientCorporation.companyWebsite', '');
    setValue('clientCorporation.mainPhone', '');
    setValue('clientCorporation.companyAddress', '');
    setValue('clientCorporation.companyAddressSelect', '');
    setCompanyAddressSearchText('');
    setValue('clientCorporation.city', '');
    setValue('clientCorporation.county', '');
    setValue('clientCorporation.countySelect', '');
    setValue('clientCorporation.zip', '');
    setValue('clientCorporation.state', 'United Kingdom');
    setValue('clientCorporation.stateId', 2359);
    setValue('clientCorporation.stateSelected', 2359);
    setValue('clientCorporation.billingContact', '');
    setValue('clientCorporation.billingPhone', '');
    setValue('clientCorporation.billingAddress', '');
    setValue('clientCorporation.billingAddressSelect', '');
    setCompanyBillingAddressSearchText('');
    setValue('clientCorporation.billingCity', '');
    setValue('clientCorporation.billingCounty', '');
    setValue('clientCorporation.billingCountySelect', '');
    setValue('clientCorporation.billingZip', '');
    setValue('clientCorporation.billingState', '');
    setValue('clientCorporation.invoiceFormatInformation', '');
    setValue('clientCorporation.standardFeeArrangement', '');
  };

  const stateId = watch('clientCorporation.stateId');
  const { data: counties = [] } = useQuery(
    ['GET_COUNTIES', watch('clientCorporation.stateId')],
    async () => {
      if (!watch('clientCorporation.stateId')) return;
      const { data } = await getCounties({
        filter: '',
        countryId: stateId,
      });
      return data.result;
    },
    { enabled: !!stateId }
  );

  const { setPlaceId: setCompanyPlaceId, isLoading: isLoadingCompanyAddress } =
    useGoogleMapAddressDetails(setValue, false, 'companyAddress');

  const { data: countries = [] } = useQuery(
    ['GET_COUNTRIES'],
    async () => {
      const { data } = await getCountries();
      return data.result;
    },
    { enabled: true }
  );

  useMemo(async () => {
    setCompanyIndustrySearchText(' ', 0, 100);
    if (isEditCompany) {
      handleGetCompanyDetail();
    } else {
      resetCompany();
      setCompanyAddressSearchText(
        `${getValues().clientCorporation.name} ${getValues()?.address1 ? getValues()?.address1 : ''}`
      );
      setValue(
        'clientCorporation.companyAddress',
        `${getValues().clientCorporation.name} ${getValues()?.address1 ? getValues()?.address1 : ''}`
      );
      setValue(
        'clientCorporation.companyAddressSelect',
        `${getValues().clientCorporation.name} ${getValues()?.address1 ? getValues()?.address1 : ''}`
      );
    }
  }, []);

  useEffect(() => {
    setCategorySearchText(' ');
    setSkillSearchText(' ');
  }, []);

  useEffect(() => {
    if (countries && dataSearch) {
      const foundCountry = countries.find(
        (item) => item.label === dataSearch.country
      );
      if (foundCountry) {
        setValue('clientCorporation.state', foundCountry.label);
        setValue('clientCorporation.stateId', foundCountry.value);
        setValue('clientCorporation.stateSelected', foundCountry.value);
      }
    }
  }, [countries, dataSearch]);

  return (
    <>
      <div className="flex gap-5">
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Company Name
              </p>
            }
            name="clientCorporation.name"
          >
            <Controller
              render={({ field }) => (
                <Input
                  className="mt-2"
                  required
                  {...field}
                  suffix={
                    getValues().clientCorporation.name !== '' &&
                    getValues().clientCorporation.name ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                  onChange={async (e) => {
                    field.onChange(e);
                    const value = e.target.value;
                    setCompanyAddressSearchText(value);
                    const { data } = await searchAddressWithOpenStreet(value);
                    // Not use LAMBDA for now
                    // if (data?.sourceKey === 'LAMBDA') {
                    //   setValue(
                    //     'clientCorporation.zip',
                    //     data?.data?.[0]?.address?.postcode
                    //   );
                    //   setValue(
                    //     'clientCorporation.county',
                    //     data?.data?.[0]?.address?.county ||
                    //       data?.data?.[0]?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.countySelect',
                    //     data?.data?.[0]?.address?.county ||
                    //       data?.data?.[0]?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.city',
                    //     data?.data?.[0]?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.state',
                    //     data?.data?.[0]?.address?.country
                    //   );
                    //   const country = await getCountries(
                    //     data?.data?.[0]?.address?.country
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateId',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateSelected',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientCorporation.companyAddress',
                    //     `${value} ${getValues()?.clientCorporation?.address1 ? getValues()?.clientCorporation?.address1 : ''}`
                    //   );
                    //   setValue(
                    //     'clientCorporation.companyAddressSelect',
                    //     `${value} ${getValues()?.clientCorporation?.address1 ? getValues()?.clientCorporation?.address1 : ''}`
                    //   );
                    // } else {
                    const filteredLocations = data?.data?.filter((item) =>
                      item?.address1?.startsWith(value)
                    );
                    setCompanyPlaceId(
                      !filteredLocations[0]?.id
                        ? data?.data?.[0]?.id
                        : filteredLocations[0]?.id
                    );
                    setValue(
                      'clientCorporation.companyAddress',
                      `${value} ${getValues()?.clientCorporation?.address1 ? getValues()?.clientCorporation?.address1 : ''}`
                    );
                    setValue(
                      'clientCorporation.companyAddressSelect',
                      `${value} ${getValues()?.clientCorporation?.address1 ? getValues()?.clientCorporation?.address1 : ''}`
                    );
                    // }
                  }}
                />
              )}
              name="clientCorporation.name"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Status
              </p>
            }
            name="clientCorporation.status"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  onSelect={(value) => {
                    setValue('clientCorporation.status', value);
                  }}
                  onSearch={() => {
                    setValue('clientCorporation.status', null);
                  }}
                  options={[
                    'Prospect',
                    'Active',
                    'Passive Account',
                    'DNC',
                    'Archive',
                    'Live Lead',
                  ].map((val) => ({ label: val, value: val }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                >
                  <Input
                    placeholder="Select Status"
                    required
                    suffix={
                      getValues().clientCorporation.status !== '' &&
                      getValues().clientCorporation.status ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
              name="clientCorporation.status"
              control={control}
            />
          </Form.Item>
        </div>
      </div>

      <div className="mb-6 border-b-2 border-b-cyan-600 pb-2">
        <span className="font-medium text-base  ">General Information</span>
      </div>
      {/* <Divider orientation="left">General Information</Divider> */}

      <Form.Item label="Parent Company" name="clientCorporation.parentCompany">
        <Controller
          render={({ field }) => (
            <>
              <ConfigProvider
                theme={{
                  token: {
                    colorBgElevated:
                      parentCompanyOptions[0]?.id === 0 ? '#FFFFFF' : '#E0EBF9',
                  },
                }}
              >
                <AutoComplete
                  {...field}
                  open={handleCloseClient}
                  options={parentCompanyOptions.map((option) => ({
                    value: option.id,
                    label: option.name,
                  }))}
                  onMouseDown={(e) => {
                    setHandleCloseClient(true);
                  }}
                  onPopupScroll={handleParentCompanyScroll}
                  onSearch={(searchText) => {
                    setValue('clientCorporation.parentCompanySelect', null);
                    setHandleCloseClient(true);
                    setValue('clientCorporation.parentCompanyId', null);
                    setValue('clientCorporation.parentCompany', searchText);
                    handleParentCompanySearch(searchText);
                  }}
                  onSelect={(value) => {
                    setValue('clientCorporation.parentCompanySelect', value);
                    setValue('clientCorporation.parentCompanyId', value);
                    const companyName = parentCompanyOptions.find(
                      (co) => co.id == value
                    )?.name;
                    setValue('clientCorporation.parentCompany', companyName);
                    setHandleCloseClient(false);
                  }}
                  dropdownRender={(menus) => {
                    if (parentCompanyOptions[0]?.id === 0) {
                      return (
                        <Button
                          onClick={(e) => {
                            setValue(
                              'clientCorporation.parentCompanySelect',
                              valueNotFoundParentCompany
                            );
                            setValue(
                              'clientCorporation.parentCompanyId',
                              valueNotFoundParentCompany
                            );
                            setValue(
                              'clientCorporation.parentCompany',
                              valueNotFoundParentCompany
                            );
                            setHandleCloseClient(false);
                          }}
                          onMouseLeave={(e) => {
                            setHandleCloseClient(false);
                          }}
                          className="flex gap-1 w-full h-fit bg-white"
                          value={valueNotFoundParentCompany}
                        >
                          <PlusOutlined className="my-auto" />{' '}
                          <span className="my-auto">Add new</span>{' '}
                          {valueNotFoundParentCompany}
                        </Button>
                      );
                    } else {
                      return (
                        <div
                          onMouseLeave={(e) => {
                            setHandleCloseClient(false);
                          }}
                        >
                          <Button
                            onClick={(e) => {
                              setValue(
                                'clientCorporation.parentCompanySelect',
                                valueNotFoundParentCompany
                              );
                              setValue(
                                'clientCorporation.parentCompanyId',
                                valueNotFoundParentCompany
                              );
                              setValue(
                                'clientCorporation.parentCompany',
                                valueNotFoundParentCompany
                              );
                              setHandleCloseClient(false);
                            }}
                            className="flex gap-1 w-full h-fit bg-white"
                          >
                            <PlusOutlined className="my-auto" />{' '}
                            <span className="my-auto">Add new</span>{' '}
                            {valueNotFoundParentCompany}
                          </Button>
                          {menus}
                        </div>
                      );
                    }
                  }}
                >
                  <Input
                    placeholder="Enter Parent Company Name"
                    suffix={isLoadingParentCompany ? <Spin /> : ''}
                  />
                </AutoComplete>
              </ConfigProvider>
            </>
          )}
          name="clientCorporation.parentCompany"
          control={control}
        />
      </Form.Item>

      <div className="flex w-full gap-5">
        <div className="w-1/2">
          <Form.Item
            label="Company Owner"
            name="clientCorporation.companyOwner"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  options={companyOwnerOption.map((option) => ({
                    value: option.id,
                    label: option.name,
                  }))}
                  onPopupScroll={handleCompanyOwnerScroll}
                  onSearch={(searchText) => {
                    setValue('clientCorporation.companyOwnerSelect', null);
                    setValue('clientCorporation.companyOwnerId', null);
                    setValue('clientCorporation.companyOwner', searchText);
                    handleCompanyOwnerSearch(searchText);
                  }}
                  filterOption={(input, option) =>
                    option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onSelect={(value) => {
                    setValue('clientCorporation.companyOwnerSelect', value);
                    setValue('clientCorporation.companyOwnerId', value);
                    const companyOwnerName = companyOwnerOption.find(
                      (co) => co.id == value
                    )?.name;
                    setValue(
                      'clientCorporation.companyOwner',
                      companyOwnerName
                    );
                  }}
                >
                  <Input
                    placeholder="Enter Company Owner"
                    suffix={isLoadingCompanyOwner ? <Spin /> : ''}
                  />
                </AutoComplete>
              )}
              name="clientCorporation.companyOwner"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label={'Industries'}
            name="clientCorporation.industries"
            style={{
              height: '40px',
            }}
          >
            <Controller
              name="clientCorporation.industries"
              control={control}
              rules={{ required: 'This field is required' }}
              render={({ field }) => (
                <ConfigProvider
                  theme={{
                    token: {
                      colorBgElevated: '#E0EBF9',
                    },
                  }}
                >
                  <Select
                    placeholder="Select Industry"
                    className="mt-2"
                    required
                    labelInValue
                    mode="multiple"
                    // onSearch={(searchText) => {
                    //   setCompanyIndustrySearchText(searchText);
                    // }}
                    // onPopupScroll={(e) =>
                    //   handleCompanyIndustriesScroll(e, 'BusinessSector')
                    // }
                    {...field}
                    loading={isLoadingCompanyIndustries}
                    notFoundContent={
                      isLoadingCompanyIndustries ? <Spin size="small" /> : null
                    }
                    options={companyIndustriesOptions.map((so) => ({
                      ...so,
                      label: so.name,
                      value: so.id,
                    }))}
                    suffixIcon={
                      checkHenley ? (
                        <></>
                      ) : getValues('clientCorporation.industries') ? (
                        getValues('clientCorporation.industries').length !==
                        0 ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                    style={{
                      height: '40px !important',
                    }}
                  />
                </ConfigProvider>
              )}
            />
          </Form.Item>
        </div>
      </div>

      <Form.Item
        label="Company Comments"
        name="clientCorporation.companyComments"
      >
        <Controller
          render={({ field }) => (
            <Input.Group className="flex gap-2">
              <Input.TextArea
                placeholder="Enter the Company Comments or Remarks (Limitation with 6 letters)"
                maxLength={6}
                rows={4}
                {...field}
              />
            </Input.Group>
          )}
          name="clientCorporation.companyComments"
          control={control}
        />
      </Form.Item>
      <Form.Item
        label="Company Description"
        name="clientCorporation.companyDescription"
      >
        <Controller
          render={({ field }) => (
            <Input.Group className="flex gap-2">
              <Input.TextArea
                placeholder="Enter the Company Description (Limitation with 6 letters)"
                maxLength={6}
                rows={4}
                {...field}
              />
            </Input.Group>
          )}
          name="clientCorporation.companyDescription"
          control={control}
        />
      </Form.Item>

      <div className="mb-6 border-b-2 border-b-cyan-600 pb-3">
        <span className="font-medium text-lg  ">Desired Experience</span>
      </div>
      <div className="w-full flex gap-5">
        <div className="w-1/2">
          <>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Categories
                </p>
              }
              name="clientCorporation.categories"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                render={({ field }) => (
                  <Select
                    placeholder="Select Categories"
                    // disabled={showCompany || jobDetailCompanyId}
                    required
                    labelInValue
                    mode="multiple"
                    onSearch={() => {
                      setValue('categories', null);
                    }}
                    suffixIcon={
                      getValues('clientCorporation.categories') ? (
                        getValues('clientCorporation.categories').length !==
                        0 ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    {...field}
                    notFoundContent={null}
                    options={categoriesOptions.map((so) => ({
                      ...so,
                      label: so.name,
                      value: so.id,
                    }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                    // onSelect={(value, options) => {
                    //   // console.log(value)
                    //   setValue('clientCorporation.categories', value);
                    // }}
                  />
                )}
                name="clientCorporation.categories"
                control={control}
              />
            </Form.Item>
          </>
        </div>
        <div className="w-1/2 ">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Required Skills
              </p>
            }
            name="clientCorporation.skills"
            className=" mb-10"
          >
            <Controller
              rules={{ required: 'This field is required' }}
              name="clientCorporation.skills"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <Select
                  placeholder="Enter Skills to search"
                  required
                  labelInValue
                  suffixIcon={
                    getValues('clientCorporation.skills').length !== 0 ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                  mode="multiple"
                  {...field}
                  notFoundContent={
                    isLoadingSkills ? <Spin size="small" /> : null
                  }
                  options={skillsOptions.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  showSearch={true}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  optionFilterProp="label"
                />
              )}
            />
          </Form.Item>
        </div>
      </div>

      <div className="mb-6 border-b-2 border-b-cyan-600 pb-2">
        <span className="font-medium text-base  ">Company Address</span>
      </div>
      {/* <Divider orientation="left">Main Location / Info</Divider> */}

      <div className="flex w-full gap-5">
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Address Line 1
              </p>
            }
            name="clientCorporation.companyAddress"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  className="mb-5"
                  {...field}
                  value={getValues('clientCorporation.companyAddress')}
                  options={companyAddressOptions?.data?.map((option) => ({
                    value: option.place_id || option.id,
                    label: (
                      <div>
                        <div>{option.display_name || option.address1}</div>
                        <div style={{ fontSize: '13px', color: '#b3b3b3' }}>
                          {option?.full_address || option?.place_formatted}
                        </div>
                      </div>
                    ),
                  }))}
                  // options={companyAddressOptions.map(ao => ({
                  //   label: ao.address1,
                  //   value: ao.id
                  // }))}
                  onSearch={(value) => {
                    setCompanyAddressSearchText(value);
                    setValue('clientCorporation.companyAddress', value);
                    setValue('clientCorporation.companyAddressSelect', value);
                  }}
                  onSelect={async (selectedAddressId) => {
                    // Not use LAMBDA for now
                    // if (companyAddressOptions?.sourceKey === 'LAMBDA') {
                    //   const selectedAddress = companyAddressOptions?.data?.find(
                    //     (ao) => ao.place_id == selectedAddressId
                    //   );
                    //   console.log(
                    //     'selectedAddress',
                    //     selectedAddress,
                    //     selectedAddress?.address.county
                    //   );
                    //   // setCompanyPlaceId(selectedAddressId);
                    //   setValue(
                    //     'clientCorporation.companyAddress',
                    //     selectedAddress.display_name
                    //   );
                    //   setValue(
                    //     'clientCorporation.companyAddressSelect',
                    //     selectedAddressId
                    //   );

                    //   const findCounty = counties?.find(
                    //     (item) =>
                    //       item.label?.toLowerCase() ===
                    //       selectedAddress?.address?.county?.toLowerCase()
                    //   );
                    //   const findState = counties?.find(
                    //     (item) =>
                    //       item.label?.toLowerCase() ===
                    //       selectedAddress?.address?.state?.toLowerCase()
                    //   );
                    //   const selectedCountyOrState = findCounty || findState;

                    //   setValue(
                    //     'clientCorporation.zip',
                    //     selectedAddress?.address.postcode
                    //   );
                    //   setValue(
                    //     'clientCorporation.county',
                    //     selectedCountyOrState || selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.countySelect',
                    //     selectedCountyOrState || selectedAddress?.address.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.city',
                    //     selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.state',
                    //     selectedAddress?.address?.country
                    //   );
                    //   const country = await getCountries(
                    //     selectedAddress?.address?.country
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateId',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateSelected',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    // } else {
                    const selectedAddress = companyAddressOptions?.data?.find(
                            (ao) =>
                              ao.id == selectedAddressId ||
                              ao.place_id == selectedAddressId
                          );

                    setCompanyPlaceId(selectedAddressId);
                    setValue(
                      'clientCorporation.companyAddress',
                      selectedAddress?.full_address || selectedAddress?.place_formatted
                    );
                    setValue(
                      'clientCorporation.companyAddressSelect',
                      selectedAddressId
                    );
                    const state = countries?.find((item) => item.label === selectedAddress?.context?.country?.name)
                    setValue('clientCorporation.state', state?.label);
                    setValue('clientCorporation.stateId', state?.value);
                    setValue('clientCorporation.stateSelected', state?.value);
                    const countryCode = selectedAddress?.context?.country?.country_code
                    const listCounTries = await handleRefetchCountry(state?.value)
                    let countySelect = listCounTries?.find((item) => ( item.label === selectedAddress?.context?.region?.name))
                    if (!countySelect) {
                      let countyFinder = retriveFindCounty(selectedAddress?.context?.place?.name, countryCode)
                      if (countyFinder) {
                        countySelect = listCounTries?.find((item) => ( item.label.trim().toLowerCase() === countyFinder.trim().toLowerCase()))
                      }
                    }
                    setValue('clientCorporation.county', countySelect?.value);
                    setValue('clientCorporation.countySelect', countySelect)

                    // }
                  }}
                >
                  <Input
                    required
                    value={getValues('clientCorporation.companyAddress')}
                    suffix={
                      isLoadingCompanyAddresses ? (
                        <Spin />
                      ) : getValues().clientCorporation.companyAddressSelect !==
                          '' &&
                        getValues().clientCorporation.companyAddressSelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
              name="clientCorporation.companyAddress"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Address Line 2
              </p>
            }
            name="clientCorporation.companySecondAddress"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  className="mb-5"
                  {...field}
                  options={companyAddressOptions?.data?.map((option) => ({
                    value: option.place_id || option.id,
                    label: (
                      <div className="grid p-2">
                        <div className="flex justify-between">
                          <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                            {option.display_name || option?.address1}
                          </span>
                        </div>
                      </div>
                    ),
                  }))}
                  // options={companyAddressOptions.map(ao => ({
                  //   label: ao.address1,
                  //   value: ao.id
                  // }))}
                  onSearch={(value) => {
                    setCompanyAddressSearchText(value);
                    setValue('clientCorporation.companySecondAddress', value);
                    setValue(
                      'clientCorporation.companySecondAddressSelect',
                      value
                    );
                  }}
                  onSelect={async (selectedAddressId) => {
                    // Not use LAMBDA for now
                    // if (companyAddressOptions?.sourceKey === 'LAMBDA') {
                    //   const selectedAddress = companyAddressOptions?.data?.find(
                    //     (ao) => ao.place_id == selectedAddressId
                    //   );

                    //   setValue(
                    //     'clientCorporation.companyAddress',
                    //     selectedAddress.display_name
                    //   );
                    //   setValue(
                    //     'clientCorporation.companyAddressSelect',
                    //     selectedAddressId
                    //   );
                    //   setValue(
                    //     'clientCorporation.zip',
                    //     selectedAddress?.address.postcode
                    //   );
                    //   setValue(
                    //     'clientCorporation.county',
                    //     selectedAddress?.address.county ||
                    //       selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.countySelect',
                    //     selectedAddress?.address?.county ||
                    //       selectedAddress?.address.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.city',
                    //     selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientCorporation.state',
                    //     selectedAddress?.address?.country
                    //   );
                    //   const country = await getCountries(
                    //     selectedAddress?.address?.country
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateId',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientCorporation.stateSelected',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    // } else {
                    const selectedAddress = companyAddressOptions?.data?.find(
                      (ao) => ao.id == selectedAddressId
                    );
                    setCompanyPlaceId(selectedAddressId);
                    setValue(
                      'clientCorporation.companySecondAddress',
                      selectedAddress?.address1
                    );
                    setValue(
                      'clientCorporation.companyAddressSelect',
                      selectedAddressId
                    );
                    // }
                  }}
                >
                  <Input
                    // required
                    suffix={
                      isLoadingCompanyAddresses ? (
                        <Spin />
                      ) : getValues().clientCorporation
                          .companySecondAddressSelect !== '' &&
                        getValues().clientCorporation
                          .companySecondAddressSelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : null
                    }
                  />
                </AutoComplete>
              )}
              name="clientCorporation.companySecondAddress"
              control={control}
            />
          </Form.Item>
        </div>
      </div>
      {/* <Form.Item
        label={
          <p>
            Company Address
          </p>
        }
        name="clientCorporation.companyAddress"
      >
        <Controller
          render={({ field }) => (
            <AutoComplete
              className="mb-5"
              {...field}
              options={companyAddressOptions.map((option) => ({
                value: option.id,
                label: (
                  <div className="grid p-2">
                    <div className="flex justify-between">
                      <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                        {option.address1}
                      </span>
                      <Image
                        className="absolute right-3"
                        src={
                          option.details
                            ? `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${option.details.width}&photoreference=${option.details.photo_reference}&key=${googleKey}`
                            : 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=1000&photoreference=error'
                        }
                        width={50}
                        height={50}
                      />
                    </div>
                  </div>
                ),
              }))}
              // options={companyAddressOptions.map(ao => ({
              //   label: ao.address1,
              //   value: ao.id
              // }))}
              onSearch={(value) => {
                setCompanyAddressSearchText(value);
                setValue('clientCorporation.companyAddress', value);
                setValue('clientCorporation.companyAddressSelect', value);
              }}
              onSelect={async (selectedAddressId) => {
                const selectedAddress = companyAddressOptions.find(
                  (ao) => ao.id == selectedAddressId
                );
                setCompanyPlaceId(selectedAddressId);
                setValue(
                  'clientCorporation.companyAddress',
                  selectedAddress.address1
                );
                setValue(
                  'clientCorporation.companyAddressSelect',
                  selectedAddressId
                );
              }}
            >
              <Input
                required
                suffix={
                  isLoadingCompanyAddresses ? (
                    <Spin />
                  ) : getValues().clientCorporation.companyAddressSelect !==
                      '' &&
                    getValues().clientCorporation.companyAddressSelect ? (
                    <CheckOutlined className="text-green-600" />
                  ) : (
                    <Image
  src={deleteIcon}
  alt="Delete Icon"
  width="16px"
  height="16px"
  preview={false}
/>
                  )
                }
              />
            </AutoComplete>
          )}
          name="clientCorporation.companyAddress"
          control={control}
        />
      </Form.Item> */}

      <Row gutter={[24, 24]}>
        <Col span={8}>
          <Form.Item label="City" name="clientCorporation.city">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter City"
                  className="mt-2"
                  suffix={isLoadingCompanyAddress ? <Spin /> : ''}
                  {...field}
                />
              )}
              name="clientCorporation.city"
              control={control}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label={'County/State'} name="clientCorporation.county">
            <Controller
              name="clientCorporation.county"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  className="search-input"
                  options={listCountySelect}
                  onSelect={(value, record) => {
                    setValue('clientCorporation.countySelect', value);
                  }}
                  value={getValues("clientCorporation.county")}
                  onSearch={(searchText) => {
                    setValue('clientCorporation.county', searchText);
                    setValue('clientCorporation.countySelect', null);
                  }}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                >
                  <Input
                    placeholder="Enter County"
                    value={getValues("clientCorporation.county")}
                    suffix={
                      isLoadingCompanyAddress ? (
                        <Spin />
                      ) : getValues().clientCorporation.county !== '' &&
                        getValues().clientCorporation.county ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label={'Postcode'} name="clientCorporation.zip">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Postcode"
                  className="mt-2"
                  required
                  suffix={
                    isLoadingCompanyAddress ? (
                      <Spin />
                    ) : getValues().clientCorporation.zip !== '' &&
                      getValues().clientCorporation.zip ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                  {...field}
                />
              )}
              name="clientCorporation.zip"
              control={control}
            />
          </Form.Item>
        </Col>
      </Row>

      <div className="w-full flex gap-5">
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Country
              </p>
            }
            name="clientCorporation.state"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  options={countries}
                  onSearch={(searchText) => {
                    setValue('clientCorporation.state', searchText);
                    setValue('clientCorporation.stateId', null);
                    setValue('clientCorporation.stateSelected', null);
                  }}
                  filterOption={(input, option) =>
                    option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  value={watch('clientCorporation.state')}
                  onSelect={(value) => {
                    setValue('clientCorporation.stateSelected', value);
                    setValue('clientCorporation.stateId', value);
                    const countryName = countries.find(
                      (co) => co.value == value
                    )?.label;
                    setValue('clientCorporation.state', countryName);
                    setValue('clientCorporation.county', '');
                    setValue('clientCorporation.countySelect', '');
                  }}
                >
                  <Input
                    placeholder="Select Country"
                    value={watch('clientCorporation.state')}
                    required
                    suffix={
                      isLoadingCompanyAddress ? (
                        <Spin />
                      ) : getValues().clientCorporation.stateSelected !== '' &&
                        getValues().clientCorporation.stateSelected ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
              name="clientCorporation.state"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label={<p>Company Website</p>}
            name="clientCorporation.companyWebsite"
          >
            <Controller
              render={({ field }) => <Input className="mt-2 py-1" {...field} />}
              name="clientCorporation.companyWebsite"
              control={control}
            />
          </Form.Item>
        </div>
      </div>

      <div className="w-full flex">
        {/* <div className="w-1/6">
          <Form.Item
            label={"Country Code"}
            name="clientCorporation.countrycode"
          >
            <Controller
              name="clientCorporation.countrycode"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  className="search-input"
                  options={countryCodeNumberList}
                  onSelect={(value, record) => {
                    setValue('clientCorporation.countryCodeSelect', value);
                  }}
                  onSearch={(searchText) => {
                    setValue('clientCorporation.countrycode', searchText);
                    setValue('clientCorporation.countryCode', null);
                  }}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                >
                  <Input
                    suffix={
                      isLoadingCompanyAddress ? (
                        <Spin />
                      ) : getValues().clientCorporation.countryCodeSelect !==
                          '' &&
                        getValues().clientCorporation.countryCodeSelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
            />
          </Form.Item>
        </div> */}
        <div style={{ width: '100%' }}>
          <Form.Item label={'Main Phone'} name="clientCorporation.mainPhone">
            <Controller
              render={({ field }) => (
                <Input
                  className="mt-2"
                  required
                  {...field}
                  suffix={
                    isLoadingCompanyAddress ? (
                      <Spin />
                    ) : getValues().clientCorporation.mainPhone !== '' &&
                      getValues().clientCorporation.mainPhone ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
              )}
              name="clientCorporation.mainPhone"
              control={control}
            />
          </Form.Item>
        </div>
      </div>

      <div className="mb-6 border-b-2 border-b-cyan-600 pb-2">
        <span className="font-medium text-base  ">Billing Information</span>
      </div>
      {/* <Divider orientation="left">Billing Information</Divider> */}

      <div className="w-full flex gap-5">
        <div className="w-1/2">
          <Form.Item
            label="Billing Contact"
            name="clientCorporation.billingContact"
          >
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Contact"
                  className="mt-2"
                  {...field}
                />
              )}
              name="clientCorporation.billingContact"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2 flex gap-5">
          <div className="w-2/6">
            <Form.Item
              label={'Country Code'}
              name="clientCorporation.countryCodeBillingPhone"
            >
              <Controller
                name="clientCorporation.countryCodeBillingPhone"
                control={control}
                render={({ field }) => (
                  <AutoComplete
                    {...field}
                    className="search-input"
                    options={countryCodeNumberList}
                    onSelect={(value, record) => {
                      setValue(
                        'clientCorporation.countryCodeBillingPhoneSelect',
                        value
                      );
                    }}
                    onSearch={(searchText) => {
                      setValue(
                        'clientCorporation.countryCodeBillingPhone',
                        searchText
                      );
                      setValue(
                        'clientCorporation.countryCodeBillingPhone',
                        null
                      );
                    }}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  >
                    <Input
                      suffix={
                        isLoadingCompanyAddress ? (
                          <Spin />
                        ) : getValues().clientCorporation
                            .countryCodeBillingPhone !== '' &&
                          getValues().clientCorporation
                            .countryCodeBillingPhone ? (
                          <CheckOutlined className="text-green-600" />
                        ) : null
                      }
                    />
                  </AutoComplete>
                )}
              />
            </Form.Item>
          </div>
          <div className="w-4/6">
            <Form.Item
              label="Billing Phone"
              name="clientCorporation.billingPhone"
            >
              <Controller
                render={({ field }) => (
                  <Input
                    placeholder="Billing Phone"
                    className="mt-2"
                    {...field}
                  />
                )}
                name="clientCorporation.billingPhone"
                control={control}
              />
            </Form.Item>
          </div>
        </div>
      </div>

      <Form.Item
        label="Invoice Format Information"
        name="clientCorporation.invoiceFormatInformation"
      >
        <Controller
          render={({ field }) => (
            <Input.Group className="flex gap-2">
              <Input.TextArea
                placeholder="Invoice Information"
                rows={2}
                {...field}
              />
            </Input.Group>
          )}
          name="clientCorporation.invoiceFormatInformation"
          control={control}
        />
      </Form.Item>

      <Form.Item
        label={<p>Standard Fee Arrangement (%)</p>}
        name="clientCorporation.standardFeeArrangement"
      >
        <Controller
          render={({ field }) => (
            <InputNumber className="w-full mt-2" {...field} />
          )}
          name="clientCorporation.standardFeeArrangement"
          control={control}
        />
      </Form.Item>

      {/* <Form.Item
        label="Billing Address"
        name="clientCorporation.billingAddress"
      >
        <Controller
          render={({ field }) => (
            <AutoComplete
            className='mb-4'
              {...field}
              options={companyBillingAddressOptions.map((ao) => ({
                label: ao.address1,
                value: ao.id,
              }))}
              onSearch={(value) => {
                setCompanyBillingAddressSearchText(value);
                setValue('clientCorporation.billingAddress', value);
                setValue('clientCorporation.billingAddressSelect', value);
              }}
              onSelect={async (selectedAddressId) => {
                const selectedAddress = companyBillingAddressOptions.find(
                  (ao) => ao.id == selectedAddressId
                );
                setCompanyBillingPlaceId(selectedAddressId);
                setValue(
                  'clientCorporation.billingAddress',
                  selectedAddress.address1
                );
                setValue(
                  'clientCorporation.billingAddressSelect',
                  selectedAddressId
                );
              }}
            >
              <Input
                suffix={isLoadingCompanyBillingAddresses ? <Spin /> : ''}
              />
            </AutoComplete>
          )}
          name="clientCorporation.billingAddress"
          control={control}
        />
      </Form.Item> */}

      <Row gutter={[8, 8]}>
        <Col span={8}>
          <Form.Item label="City" name="clientCorporation.billingCity">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter County"
                  className="mt-2"
                  // suffix={isLoadingCompanyBillingAddress ? <Spin /> : ''}
                  {...field}
                />
              )}
              name="clientCorporation.billingCity"
              control={control}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="County/State"
            name="clientCorporation.billingCounty"
          >
            <Controller
              name="clientCorporation.billingCounty"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  className="search-input"
                  options={ukCounties}
                  onSelect={(value) => {
                    setValue('clientCorporation.billingCountySelect', value);
                  }}
                  onSearch={() => {
                    setValue('clientCorporation.billingCountySelect', null);
                  }}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                >
                  <Input
                    placeholder="Enter County"
                    // suffix={isLoadingCompanyBillingAddress ? <Spin /> : ''}
                  />
                </AutoComplete>
              )}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Postcode" name="clientCorporation.billingZip">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Postal Code"
                  className="mt-2"
                  // suffix={isLoadingCompanyBillingAddress ? <Spin /> : ''}
                  {...field}
                />
              )}
              name="clientCorporation.billingZip"
              control={control}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label={<p>Country</p>} name="clientCorporation.billingState">
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              options={countries}
              onSearch={(searchText) => {
                setValue('clientCorporation.billingState', searchText);
                setValue('clientCorporation.billingStateId', null);
                setValue('clientCorporation.billingStateSelected', null);
              }}
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onSelect={(value) => {
                setValue('clientCorporation.billingStateSelected', value);
                setValue('clientCorporation.billingStateId', value);
                const countryName = countries.find(
                  (co) => co.value == value
                )?.label;
                setValue('clientCorporation.billingState', countryName);
              }}
            >
              <Input
                placeholder="Select Country"
                // suffix={isLoadingCompanyBillingAddress ? <Spin /> : ''}
              />
            </AutoComplete>
          )}
          name="clientCorporation.billingState"
          control={control}
        />
      </Form.Item>
    </>
  );
}

export default BullHornJobSubmissionCompany;
