/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState } from 'react';

import _find from 'lodash/find';

import {
  Collapse,
  Divider,
  Modal,
  Progress,
  Spin,
  Tooltip,
  notification,
} from 'antd';
import BullHornJobSubmissionForm from './BullHornJobSubmissionForm';
import { useForm } from 'react-hook-form';
import {
  sendJobToBullhorn,
  searchBullhornData,
  getCountries,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import useGoogleMapAddressDetails from '../../hooks/useGoogleMapAddressDetails';
import {
  getAddressDetailsByPlaceId,
  searchAddressWithOpenStreet,
} from '../../services/googleMap';
import { message, Steps, Button, Form, Checkbox } from 'antd';
import { useParams } from 'react-router-dom';
import { deleteJobLogs, updateJobLogs } from '../../services/jobs';
import { useDispatch } from 'react-redux';
import { saveCompanyResponse } from '../../store/common';
import ConfigFormAdvanced from '../../common/ConfigFormAdvanced';
import { defaultValueBullhornSubmissionForm } from '../../common/ConfigFormAdvanced/defaultValues';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import BullhornSendEmail from './BullhornSendEmailModal';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import {
  CloseOutlined,
  FireOutlined,
  SwapRightOutlined,
} from '@ant-design/icons';

import './bullhornSendEmailModal.scss';
import BullhornSendEmailModal from './EmailtriggerStep';
import JobDetail from '../JobDetail';
import dayjs from 'dayjs';
import BullHornLeadSubmissionForm from './BullHornLeadSubmissionForm';
import BullHornOpportunitySubmissionForm from './BullHornOpportunitySubmissionForm';
import { isGrantedSequence } from '../../helpers/getUserViewAs';

const BullHornJobSubmissionModalPage = ({
  job,
  isModalVisible = false,
  setIsModalVisible = () => {},
  fromManualCreate,
  handleDeleteData = false,
  sentJobId = false,
  searchData,
  actionKey = null,
  defaultDataCompany = false,
  searchIdProp = '',
  jobSkills = [],
}) => {
  const {
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    formState,
    getValues,
  } = useForm({
    defaultValues: { ...job, skills: [] },
  });
  const dispatch = useDispatch();

  const [handleCloseClient, setHandleCloseClient] = useState(false);
  const [handleCloseContact, setHandleCloseContact] = useState(false);
  const [isLockedSendJob, setIsLockedSendJob] = useState(true);
  const [checkBoxStatus, setCheckBoxStatus] = useState(false);
  const [showCompany, setShowCompany] = useState(false);
  const [similarList, setSimilarList] = useState([]);
  const [haveSendJob, setHaveSendJob] = useState(true);
  const [onClose, setOnclose] = useState(false);
  const [deleteLogStatus, setDeleteLogStatus] = useState(false);
  let { searchId } = useParams();
  const {
    options: contactOptions,
    setOptions: contactSetOptions,
    handleScrollPopup: handleContactScroll,
    handleSearch: handleContactSearch,
    isLoading: isLoadingContacts,
    setLoading: setIsLoadingContacts,
    valueNotFound: valueNotFoundContacts,
    setValueNotFound: setContactValueNotFound,
    setCompanyId,
    setStart: contactSetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setValueNotFound: setCompanyValueNotFound,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const {
    setPlaceId,
    placeId,
    isLoading: isLoadingAddressDetails,
  } = useGoogleMapAddressDetails(setValue, false, 'Address');

  const [fromStep, setFromStep] = useState('Vacancy');
  const [isSearchCompany, setIsSearchCompany] = useState(false);
  const [jobDetailCompanyId, setJobDetailCompanyId] = useState(false);
  const [sendingStatus, setSendingStatus] = useState(false);

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  // Change style UI vacancy form to step style concept
  const [numberStep, setNumberStep] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [isOpenSendMailStep, setOpenSendMailStep] = useState(false);

  const [notConfirmStep, setConfirmStep] = useState(false);
  const [notDiscard, setNotDiscard] = useState(true);
  const [listEmailSend, setListEmailSend] = useState([]);
  const [emailSeqId, setEmailSeqId] = useState('');
  const [pinnedSequence, setPinnedSequence] = useState(true);
  const [disableActionSequence, setDisableActionSequence] = useState(true);
  const [loadingSimilar, setLoadingSimilar] = useState(false);
  const [listContactSelected, setListContactSelected] = useState([]);

  const nextStep = () => {
    window.scrollTo(0, 0);
    if (getValues()?.jobtitle?.length > 100) {
      notification.error({
        message: 'Job Title is too long',
        description: 'The job title cannot be longer than 100 characters.',
      });
      return;
    }
    if (getValues()?.address1?.length > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'Address cannot be longer than 100 characters.',
      });
      return;
    }
    setDisableActionSequence(false);
    setCurrentStep(currentStep + 1);
  };
  const prevStep = () => {
    window.scrollTo(0, 0);
    setCurrentStep(currentStep - 1);
  };

  const getDataEmail = async () => {
    setOpenSendMailStep(true);
    if (!job?.job_id) return;
    const { data } = await getEmailConfigInJobBoard(job.job_id);
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          type: item?.type || '',
          key: index + 1,
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, newData?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails?.[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails?.[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails?.[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
      setValue('actionSaveEmail', true);
    }
  };

  const userToSet = profileUser || profileUserAuth;

  const isAllowToSequence = userToSet && isGrantedSequence();

  useEffect(() => {
    setValue(
      'consultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'consultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
    setValue(
      'consultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
  }, [userToSet, isModalVisible]);

  const handleGetDefaultAddress = async (inputJob, job) => {
    if (inputJob) {
      if (!onClose) {
        const { data } = await searchAddressWithOpenStreet(inputJob);
        // Not use LAMBDA for now
        // if (data?.sourceKey === 'LAMBDA') {
        //   setValue('zip', data?.data[0]?.address?.postcode);
        //   setValue(
        //     'county',
        //     data?.data[0]?.address?.county || data?.data[0]?.address?.city
        //   );
        //   setValue(
        //     'countySelect',
        //     data?.data[0]?.address?.county || data?.data[0]?.address?.city
        //   );
        //   setValue('city', data?.data[0]?.address?.city);
        //   setValue('state', data?.data[0]?.address?.country);
        //   setValue('address1', inputJob);
        //   const companyName = `Company: ${job?.company ? job?.company : ''}\n`;
        //   const jobType = `JobType: ${
        //     !job?.jobtype
        //       ? '-'
        //       : job?.jobtype.split(',')?.length > 0
        //         ? job?.jobtype.split(',')
        //         : '-' || '-'
        //   }\n`;
        //   const minMaxSalary = `Range: ${
        //     (job?.min_salary &&
        //       `${job?.min_salary ? job?.min_salary : ''} - ${
        //         job?.max_salary ? job?.max_salary : ''
        //       }`) ||
        //     '-'
        //   }\n`;
        //   const salary = `Salary: ${job?.salary || '-'}\n`;
        //   const source = `Source: ${watch('source') || '-'}\n`;
        //   let address = '';
        //   address = `Location: ${job?.joblocationcity ? job?.joblocationcity + ',' : ''} ${job?.joblocationinput}\n`;
        //   setValue(
        //     'description',
        //     ` ${companyName} ${jobType} ${minMaxSalary} ${address} ${salary} ${source} ${watch(
        //       'description'
        //     )}`
        //   );
        // } else {
        const filteredLocations = data?.data.filter((item) =>
          item.address1.startsWith(inputJob)
        );
        // }
      }
    }
  };

  const handleGetDataJob = async () => {
    if (job && !onClose) {
      if (!job?.companyId) {
        setValue('companyId', job?.companyId ? job?.companyId : null);
        setJobDetailCompanyId(true);
      }
      setValue(
        'employmentType',
        job?.jobtype ? job?.jobtype.toLowerCase() : ''
      );
      setValue('company', job?.company ? job?.company : '');
      companySetStart(0);
      handleCompanySearch(job?.company ? job?.company : ' ');
      setValue('description', job?.description ? job?.description : '');
      setValue('cleanDescription', job?.description ? job?.description : '');
      setValue('jobtitle', job?.jobtitle ? job?.jobtitle : '');
      setValue('salary', job?.max_salary ? job?.max_salary : '');
      setValue('min_salary', job?.min_salary ? job?.min_salary : '');
      setValue('max_salary', job?.max_salary ? job?.max_salary : '');
      if (job?.salary) {
        const salaryTemp =
          job?.salary && job?.salary?.toString()?.split(' Per ');
        setValue(
          'salaryUnit',
          salaryTemp?.length > 1 ? `Per ${salaryTemp?.[1] || ''}` : ''
        );
      }
      setValue(
        'jobtype',
        !job?.jobtype
          ? []
          : job?.jobtype.split(',')?.length > 0
            ? job?.jobtype.split(',')
            : []
      );
      setValue('email', job?.email ? job?.email : '');
      setValue('source', 'Zileo');
      setValue(
        'joblocationcity',
        job?.joblocationcity ? job?.joblocationcity : ''
      );
      await handleGetDefaultAddress(
        job?.joblocationcity ? job?.joblocationcity : '',
        job
      );
    } else {
      reset();
      setValue('source', 'Zileo');
      setValue('employmentType', 'temporary');
      setValue('state', 'United Kingdom');
      setValue('stateId', 2359);
      setValue('stateSelected', 2359);
      setValue('clientContact.stateId', null);
    }
  };

  useEffect(() => {
    handleGetDataJob();
  }, [job, isModalVisible]);

  const handleOk = () => {
    handleSubmit(onSubmit)();
  };

  const handleResetForm = () => {
    setValue('companyId', null);
    setValue('companySelect', null);
    setValue('company', null);
    setValue('contactId', null);
    setValue('contact', null);
    setValue('contactSelect', null);
    setValue('consultant', null);
    setValue('email', null);
    setValue('description', null);
    setValue('cleanDescription', null);
    setValue('joblocationcity', null);
    setValue('zip', null);
    setValue('county', null);
    setValue('countySelect', null);
    setValue('city', null);
    setValue('state', null);
    setValue('stateId', null);
    setValue('stateSelected', null);
    setValue('address1', null);
    if (fromManualCreate) {
      reset();
      setIsSearchCompany(false);
      setValue('source', 'Zileo');
      setValue('employmentType', 'temporary');
      setValue('state', 'United Kingdom');
      setValue('stateId', 2359);
      setValue('stateSelected', 2359);
    }
  };

  const handleCloseAllDropdown = async () => {
    setHandleCloseClient(false);
    setHandleCloseContact(false);
  };

  const handleCancel = async () => {
    setDeleteLogStatus(true);
    if (job) {
      const payload = {
        jobId: job?.job_id,
      };
      !deleteLogStatus ? deleteJobLogs(payload) : null;
    }
    setHaveSendJob(true);
    setCheckBoxStatus(false);
    setIsLockedSendJob(true);
    setShowCompany(false);
    setJobDetailCompanyId(false);
    setIsSearchCompany(false);
    setFromStep('Vacancy');
    await handleCloseAllDropdown();
    handleContactSearch('', '');
    setIsModalVisible(false);
    handleResetForm();
    setCurrentStep(0);
  };

  const onSubmit = async (payload) => {
    // Send data to server API using axios or your preferred method
    try {
      setSendingStatus(true);
      const emailPayload = {
        emailSeqId,
        jobBoardId: job?.job_id,
        recipients: getValues('sendMail.mailStepParentMailTo') ?? [],
        isNotSendSequence: getValues('sequenceStatus') ?? false,
        primaryMail: {
          subject: getValues('firstMail.subject'),
          content: getValues('firstMail.content'),
          delay: getValues('sequenceStatus')
            ? getValues('sendMail.mailStepParent')
            : 1,
        },
        externalJobId: null,
      };

      let tempData = {
        ...payload,
        description: payload.description
          ? payload.description.replace(/\n/g, '<br />')
          : payload.description,
        cleanDescription: payload?.cleanDescription,
        permFee: payload.permFee ? payload.permFee / 100 : 0,
        salary: `${payload.salary ? payload.salary : 0}`,
        payRate: `${payload.payRate ? payload.payRate : 0}`,
        job,
        searchId: searchIdProp,
        sentJobId: sentJobId ?? null,
        source: 'Zileo',
        countryAddress: payload.state,
      };

      if (getValues('firstMail') && getValues('actionSaveEmail')) {
        tempData.sendEmail = emailPayload;
      }

      const { data } = await sendJobToBullhorn(tempData);

      if (data?.success) {
        setOnclose(true);
        setCurrentStep(0);
        setSendingStatus(false);
        setHaveSendJob(true);
        setIsModalVisible(false);
        reset();
        handleResetForm();
        message.info('Job sent to Bullhorn Successfully');
        handleEditLogs();
        if (handleDeleteData && job?.job_id) {
          handleDeleteData(job?.job_id);
        }
      }
    } catch (err) {
      setCurrentStep(0);
      setSendingStatus(false);
      message.error(err?.response?.data?.message ?? 'Something went wrong');
    }
  };

  const handleEditLogs = async () => {
    try {
      const payload = {
        type: 'SENT_TO_BH_VACANCY_SUBMISSION_SUCCESSFULLY',
        jobId: job?.job_id,
      };
      await updateJobLogs(payload);
    } catch (error) {
      console.log('ERROR WHEN LOGGING: ', error);
    }
  };

  useEffect(() => {
    if (defaultDataCompany) {
      setValue('city', defaultDataCompany?.company?.address?.city);
      setValue('county', defaultDataCompany?.company?.address?.state);
      setValue('countySelect', defaultDataCompany?.company?.address?.state);
      setValue('zip', defaultDataCompany?.company?.address?.zip);
      setValue('address1', defaultDataCompany?.company?.address?.address1);
      setValue('state', defaultDataCompany?.company?.address?.countryName);
      // setIsAddCompany(false);
      setValue('companySelect', defaultDataCompany?.companyId);
      setValue('companyId', defaultDataCompany?.companyId);
      setValue('companySequenceContactId', defaultDataCompany?.companyId);
      setValue('company', defaultDataCompany?.company?.name);
    }
  }, [defaultDataCompany]);

  useEffect(() => {
    const companyResponse = _find(
      companyOptions,
      (item) => item?.name === watch('company')
    );
    if (companyResponse) {
      dispatch(saveCompanyResponse(companyResponse));
    }
  }, [companyOptions, watch('company')]);

  useEffect(() => {
    if (jobSkills?.length > 0) {
      setValue('jobskills', jobSkills);
    }
  }, [jobSkills]);

  const vacancyForm = (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div
        className="bullhorn-job-submission-form-container p-5"
        style={{
          width:
            getValues('companyId') && getValues('listSimilarData')?.length > 0
              ? '65%'
              : '100%',
        }}
      >
        <ConfigFormAdvanced
          formName="bullhorn_vacancy_form"
          // title={`Bullhorn Job Submission (${fromStep})`}
          title={job?.jobtitle || ''}
          // subtitle={'Update the Job details here.'}
          onClose={(e) => {
            !deleteLogStatus ? handleCancel() : {};
          }}
          formDefault={defaultValueBullhornSubmissionForm}
          onCloseStep={onClose}
        />
        {(!actionKey || actionKey == 1) && (
          <BullHornJobSubmissionForm
            fromStep={fromStep}
            setFromStep={setFromStep}
            watch={watch}
            control={control}
            setValue={setValue}
            getValues={getValues}
            job={job}
            onClose={onClose}
            isModalVisible={isModalVisible}
            handleCloseClient={handleCloseClient}
            setHandleCloseClient={setHandleCloseClient}
            handleCloseContact={handleCloseContact}
            setHandleCloseContact={setHandleCloseContact}
            setHaveSendJob={setHaveSendJob}
            setIsLockedSendJob={setIsLockedSendJob}
            isLockedSendJob={isLockedSendJob}
            setCheckBoxStatus={setCheckBoxStatus}
            checkBoxStatus={checkBoxStatus}
            haveSendJob={haveSendJob}
            functionContactClient={{
              contactOptions,
              contactSetOptions,
              handleContactScroll,
              handleContactSearch,
              isLoadingContacts,
              setIsLoadingContacts,
              valueNotFoundContacts,
              setCompanyId,
              contactSetStart,
              setContactValueNotFound,
            }}
            functionCompany={{
              companyOptions,
              companySetOptions,
              handleCompanyScroll,
              handleCompanySearch,
              isLoadingCompany,
              setIsLoadingCompany,
              valueNotFoundCompany,
              companySetStart,
              setCompanyValueNotFound,
            }}
            defaultDataCompany={defaultDataCompany}
            handleOk={handleOk}
            handleCancel={(e) => {
              !deleteLogStatus ? handleCancel() : {};
            }}
            setPlaceId={setPlaceId}
            placeId={placeId}
            isLoadingAddressDetails={isLoadingAddressDetails}
            handleGetDefaultAddress={handleGetDefaultAddress}
            isSearchCompany={isSearchCompany}
            setIsSearchCompany={setIsSearchCompany}
            jobDetailCompanyId={jobDetailCompanyId}
            setJobDetailCompanyId={setJobDetailCompanyId}
            showCompany={showCompany}
            setShowCompany={setShowCompany}
            setSendingStatus={setSendingStatus}
            sendingStatus={sendingStatus}
            fromManualCreate={fromManualCreate}
            setLoadingSimilar={setLoadingSimilar}
            setSimilarList={setSimilarList}
            similarList={setSimilarList}
            searchData={searchData}
          />
        )}

        {actionKey == 2 && (
          <BullHornLeadSubmissionForm
            fromStep={fromStep}
            setFromStep={setFromStep}
            watch={watch}
            control={control}
            setValue={setValue}
            getValues={getValues}
            job={job}
            onClose={onClose}
            isModalVisible={isModalVisible}
            handleCloseClient={handleCloseClient}
            setHandleCloseClient={setHandleCloseClient}
            handleCloseContact={handleCloseContact}
            setHandleCloseContact={setHandleCloseContact}
            setHaveSendJob={setHaveSendJob}
            setIsLockedSendJob={setIsLockedSendJob}
            isLockedSendJob={isLockedSendJob}
            setCheckBoxStatus={setCheckBoxStatus}
            checkBoxStatus={checkBoxStatus}
            haveSendJob={haveSendJob}
            defaultDataCompany={defaultDataCompany}
            functionContactClient={{
              contactOptions,
              contactSetOptions,
              handleContactScroll,
              handleContactSearch,
              isLoadingContacts,
              setIsLoadingContacts,
              valueNotFoundContacts,
              setCompanyId,
              contactSetStart,
              setContactValueNotFound,
            }}
            functionCompany={{
              companyOptions,
              companySetOptions,
              handleCompanyScroll,
              handleCompanySearch,
              isLoadingCompany,
              setIsLoadingCompany,
              valueNotFoundCompany,
              companySetStart,
              setCompanyValueNotFound,
            }}
            handleOk={handleOk}
            handleCancel={(e) => {
              !deleteLogStatus ? handleCancel() : {};
            }}
            // //  setPlaceId={setPlaceId}
            //  placeId={placeId}
            //  isLoadingAddressDetails={isLoadingAddressDetails}
            handleGetDefaultAddress={handleGetDefaultAddress}
            isSearchCompany={isSearchCompany}
            setIsSearchCompany={setIsSearchCompany}
            jobDetailCompanyId={jobDetailCompanyId}
            setJobDetailCompanyId={setJobDetailCompanyId}
            showCompany={showCompany}
            setShowCompany={setShowCompany}
            setSendingStatus={setSendingStatus}
            sendingStatus={sendingStatus}
            fromManualCreate={fromManualCreate}
            setLoadingSimilar={setLoadingSimilar}
            setSimilarList={setSimilarList}
            similarList={setSimilarList}
            searchData={searchData}
          />
        )}

        {actionKey == 3 && (
          <BullHornOpportunitySubmissionForm
            fromStep={fromStep}
            setFromStep={setFromStep}
            watch={watch}
            control={control}
            setValue={setValue}
            getValues={getValues}
            job={job}
            onClose={onClose}
            isModalVisible={isModalVisible}
            handleCloseClient={handleCloseClient}
            setHandleCloseClient={setHandleCloseClient}
            handleCloseContact={handleCloseContact}
            setHandleCloseContact={setHandleCloseContact}
            setHaveSendJob={setHaveSendJob}
            setIsLockedSendJob={setIsLockedSendJob}
            isLockedSendJob={isLockedSendJob}
            setCheckBoxStatus={setCheckBoxStatus}
            checkBoxStatus={checkBoxStatus}
            haveSendJob={haveSendJob}
            defaultDataCompany={defaultDataCompany}
            functionContactClient={{
              contactOptions,
              contactSetOptions,
              handleContactScroll,
              handleContactSearch,
              isLoadingContacts,
              setIsLoadingContacts,
              valueNotFoundContacts,
              setCompanyId,
              contactSetStart,
              setContactValueNotFound,
            }}
            functionCompany={{
              companyOptions,
              companySetOptions,
              handleCompanyScroll,
              handleCompanySearch,
              isLoadingCompany,
              setIsLoadingCompany,
              valueNotFoundCompany,
              companySetStart,
              setCompanyValueNotFound,
            }}
            handleOk={handleOk}
            handleCancel={(e) => {
              !deleteLogStatus ? handleCancel() : {};
            }}
            //  setPlaceId={setPlaceId}
            //  placeId={placeId}
            //  isLoadingAddressDetails={isLoadingAddressDetails}
            handleGetDefaultAddress={handleGetDefaultAddress}
            isSearchCompany={isSearchCompany}
            setIsSearchCompany={setIsSearchCompany}
            jobDetailCompanyId={jobDetailCompanyId}
            setJobDetailCompanyId={setJobDetailCompanyId}
            showCompany={showCompany}
            setShowCompany={setShowCompany}
            setSendingStatus={setSendingStatus}
            sendingStatus={sendingStatus}
            fromManualCreate={fromManualCreate}
            setLoadingSimilar={setLoadingSimilar}
            setSimilarList={setSimilarList}
            similarList={setSimilarList}
            searchData={searchData}
          />
        )}
      </div>
      {!fromManualCreate &&
        getValues('companyId') &&
        similarList?.length > 0 && (
          <div
            style={{
              width: '33%',
              // border: "1px solid #ccc",
              borderRadius: '1rem',
              height: '57vh',
              position: 'sticky',
              top: 0,
              overflowY: 'scroll',
            }}
          >
            <div>
              <div
                style={{
                  fontSize: '20px',
                  fontWeight: '700',
                  marginLeft: '10px',
                }}
              >
                Simliar vacancy
              </div>
              <div>
                {loadingSimilar ? (
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <Spin />
                  </div>
                ) : (
                  <div>
                    {similarList?.map((item) => {
                      return (
                        <div
                          style={{
                            width: '95%',
                            // padding: "10px",
                            // border: "1px solid #ccc",
                            // borderRadius: "1rem",
                            margin: '0 auto',
                            marginTop: '10px',
                            cursor: 'pointer',
                          }}
                        >
                          <Collapse
                            style={{ border: '1px solid #eff6ff' }}
                            items={[
                              {
                                key: '1',
                                label: (
                                  <>
                                    <div>
                                      <div
                                        style={{
                                          fontSize: '15px',
                                          fontWeight: '600',
                                          paddingBottom: '10px',
                                        }}
                                      >
                                        {item?.title}
                                      </div>
                                      {/* <div
                              style={{
                                marginTop: '10px',
                              }}
                            >
                              {item?.description}
                            </div> */}
                                    </div>
                                  </>
                                ),
                                children: (
                                  <div>
                                    <div>
                                      {' '}
                                      <span
                                        style={{
                                          fontWeight: '500',
                                          marginRight: '5px',
                                        }}
                                      >
                                        Date posted:{' '}
                                      </span>{' '}
                                      {dayjs(item?.dateAdded).format(
                                        'DD/MM/YYYY'
                                      )}
                                    </div>
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: item?.description,
                                      }}
                                    ></div>
                                  </div>
                                ),
                              },
                            ]}
                          />
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
    </div>
  );

  const onPinSequence = (e) => {
    setPinnedSequence(e.target.checked);
  };

  const sendMailForm = (
    <>
      <div
        id="job-detail-container"
        className="h-full overflow-scroll pt-3 px-3"
      >
        <JobDetail
          job={
            job || {
              jobtitle: getValues('jobtitle'),
              description: getValues('description'),
              company: getValues('company'),
              source: getValues('source'),
              salary: getValues('salary'),
              joblocationcity: getValues('joblocationcity'),
              jobtype: getValues('jobtype'),
            }
          }
        />
      </div>

      {/* <div className="w-full flex justify-end">
        <Checkbox value={pinnedSequence} onChange={onPinSequence}>
          Pin Sequence on right side.
        </Checkbox>
      </div> */}
      {/* <Divider orientation="left" orientationMargin="0">
        Send an mail
      </Divider> */}
    </>
  );

  const confirmationStep = () => {
    const isValidatedSyncForm =
      (!(showCompany || jobDetailCompanyId) &&
        formState.isValid &&
        getValues('leadSheetId')) ||
      fromManualCreate;

    const completePercentage = isValidatedSyncForm ? 100 : 66;

    return (
      <div className="flex justify-center w-full px-3">
        <div className="p-4 rounded-md shadow-md border w-full flex flex-col gap-3">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              {/* <Sum className="text-3xl text-cyan-600 font-semibold" /> */}
              <div className="font-semibold text-xl">Summary</div>
            </div>
            {/* <div className="flex items-center gap-1">
              <div className="font-semibold text-xl text-cyan-700">
                {completePercentage}%
              </div>
              <div className="font-semibold opacity-50">Completed</div>
            </div> */}
          </div>
          <div>
            <Progress percent={completePercentage} showInfo={false} />
          </div>
          <div>
            <Steps
              direction="vertical"
              current={2}
              items={[
                {
                  title: <div className="font-medium">Job Review</div>,
                  description: (
                    <div>
                      {isValidatedSyncForm ? 'Completed!' : 'Missing Fields'}
                    </div>
                  ),
                  status: isValidatedSyncForm ? 'finish' : 'error',
                },
                {
                  title: (
                    <div className="font-medium flex items-center gap-1">
                      Sequence <span className="font-semibold">(optional)</span>
                    </div>
                  ),
                  description: (
                    <div>
                      {!emailSeqId?.trim()
                        ? 'No sequence created!'
                        : 'Your sequence has been created!'}
                    </div>
                  ),
                  status: !emailSeqId?.trim() ? 'error' : 'finish',
                },
                {
                  title: 'Confirmation',
                  description: (
                    <div className="flex items-center gap-2 font-medium opacity-80">
                      <div>
                        If you checked all the information and it is correct,
                        please
                      </div>
                      <Tooltip
                        title={
                          isValidatedSyncForm
                            ? 'Syncing job to Bullhorn'
                            : 'Please check required fields again.'
                        }
                      >
                        <Button
                          disabled={!isValidatedSyncForm}
                          htmlType="submit"
                          className={`bg-white text-cyan-600 flex items-center justify-between ${
                            !showCompany ||
                            (jobDetailCompanyId && 'hover:bg-blue-700')
                          }`}
                          loading={sendingStatus}
                          onClick={handleOk}
                          type="primary"
                        >
                          Save
                          <SwapRightOutlined />
                        </Button>
                      </Tooltip>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </div>
      </div>
    );
  };

  const steps = [
    {
      key: 'update-job-detail',
      title: <span className="font-semibold">Job Review</span>,
      content: vacancyForm,
      description: 'Update job detail.',
    },
    {
      key: 'send-mail',
      title: (
        <span className="font-semibold">
          <Tooltip
            placement="topLeft"
            title={
              disableActionSequence
                ? 'Need to review Job first then click Next'
                : ''
            }
          >
            Sequence <span className="text-sm italic">(optional)</span>
          </Tooltip>
        </span>
      ),
      content: sendMailForm,
      description: (
        <Tooltip
          placement="topLeft"
          title={
            disableActionSequence
              ? 'Need to review Job first then click Next'
              : ''
          }
        >
          Set up a sequence automation
        </Tooltip>
      ),
      disabled: disableActionSequence,
    },
    {
      key: 'finish',
      title: <span className="font-semibold">Confirmation</span>,
      content: <>{confirmationStep()}</>,
      description: 'Confirm your actions.',
      disabled: getValues('actionSaveEmail') ? false : true,
    },
  ];

  const onChangeSteps = (step) => {
    if (step === 2 && !getValues('actionSaveEmail')) {
      notification.error({
        message: (
          <em>
            You haven't set up the <strong>Sequence</strong> yet, so please do
            set it up and remember to <strong>save</strong> them for the next
            step. Otherwise, press <strong>do not sequence</strong>
          </em>
        ),
      });
      return;
    }
    if (getValues()?.jobtitle?.length > 100) {
      notification.error({
        message: 'Job Title is too long',
        description: 'The job title cannot be longer than 100 characters.',
      });
      return;
    }
    if (getValues()?.address1?.length > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }
    const SEND_MAIL_STEP = 1;
    const CONFIRM_STEP = 2;
    // if (step === SEND_MAIL_STEP) {
    //   console.log("showCompany: ", showCompany)
    //   if (!showCompany) {
    //     message.warning('Please complete to update job first!');
    //     return;
    //   }
    // }
    setCurrentStep(step);
    // console.log('isOpenSendMailStep: ', isOpenSendMailStep);
    if (step === SEND_MAIL_STEP && !isOpenSendMailStep) {
      getDataEmail();
    }
  };

  const handleEmailSeqId = (emailSeqId) => {
    setEmailSeqId(emailSeqId);
  };

  return (
    <div className="sequence-background-container w-full h-full p-4">
      <div className="grid grid-cols-7 gap-4">
        <div
          className={
            pinnedSequence && currentStep === 1 ? `col-span-5` : 'col-span-7'
          }
        >
          <Steps
            // direction="vertical"
            type="navigation"
            current={currentStep}
            items={steps}
            onChange={onChangeSteps}
          />
          <div className="mt-3">{steps[currentStep].content}</div>
          <div className="flex justify-start gap-2 mt-5">
            <div>
              {/* <Button
                style={{
                  margin: '0 8px',
                }}
                onClick={(e) => {
                  !deleteLogStatus ? handleCancel() : {};
                }}
              >
                Cancel
              </Button> */}
              {currentStep === 1 && (
                <Button type="primary" onClick={nextStep}>
                  Do not sequence
                </Button>
              )}

              {currentStep > 0 && (
                <Button
                  style={{
                    margin: '0 8px',
                  }}
                  onClick={prevStep}
                >
                  Previous
                </Button>
              )}
              {currentStep < steps.length - 1 && (
                <Button
                  disabled={
                    currentStep != 1
                      ? false
                      : getValues('actionSaveEmail')
                        ? false
                        : true
                  }
                  type="primary"
                  onClick={nextStep}
                >
                  Next
                </Button>
              )}
            </div>
          </div>
          {!getValues('actionSaveEmail') && currentStep == 1 && (
            <div style={{ marginLeft: '20px', marginTop: '10px' }}>
              <em>
                You haven't set up the <strong>Sequence</strong> yet, so please
                do set it up and remember to <strong>save</strong> them for the
                next step. Otherwise, press <strong>do not sequence</strong>
              </em>
            </div>
          )}
        </div>

        {pinnedSequence && currentStep === 1 && (
          <div className="col-span-2">
            {!isAllowToSequence ? (
              <>
                <div
                  style={{
                    textAlign: 'center',
                    marginTop: '20%',
                  }}
                >
                  <div>
                    <div>
                      To use the sequence please visit your profile and link to
                      sequence
                    </div>
                    <div>
                      <p
                        style={{
                          color: 'blue',
                          cursor: 'pointer',
                          textDecoration: 'underline',
                          fontSize: '14px',
                          marginTop: '10px',
                        }}
                        onClick={(e) => {
                          window.location.href = `${window.location.origin}/settings?sequence=true`;
                        }}
                      >
                        Go to Sequence Settings
                      </p>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <BullhornSendEmailModal
                control={control}
                setValue={setValue}
                getValues={getValues}
                // sequenceStatus={sequenceStatus}
                setConfirmStep={setConfirmStep}
                setNotDiscard={setNotDiscard}
                notDiscard={notDiscard}
                job={{ ...job, state: getValues('state') }}
                listEmailSend={listEmailSend}
                // openModalSendEmail={openModalSendEmail}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                // fromSequenseEmail={true}
                // dataSubject={dataSubject}
                // defaultUserSelected={listContactSelected?.map((obj) => ({
                //   label: obj.name,
                //   value: obj.name,
                //   disabled: obj.massMailOptOut || obj.email === '' || obj.email === undefined,
                //   ...obj,
                // }))}
                onHandleSeqId={handleEmailSeqId}
                emailSeqId={emailSeqId}
                newUpdatedSequence={true}
                setListContactSelected={setListContactSelected}
                actionKey={actionKey}
                fromSync={true}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BullHornJobSubmissionModalPage;
