import {
  EnvironmentOutlined,
  FacebookOutlined,
  <PERSON>Outlined,
  LinkedinOutlined,
  PhoneOutlined,
  TwitterOutlined,
} from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  List,
  Modal,
  Row,
  notification,
} from 'antd';
import { useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import InSights from './InSights';

const CompanyDetailModal = (props) => {
  const {
    showDetailCompany,
    setShowDetailCompany,
    detailCompany,
    detailOrganization,
  } = props;
  const [showFullText, setShowFullText] = useState(false);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  return (
    <div>
      <Modal
        width={1600}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        title={
          <div>
            <Breadcrumb
              separator=">"
              items={[
                {
                  title: 'Search',
                },
                {
                  title: 'Companies',
                },
                {
                  title: detailCompany?.name,
                },
              ]}
            />
          </div>
        }
        open={showDetailCompany}
        onCancel={() => {
          setShowDetailCompany(false);
        }}
        footer={false}
      >
        <div className="grid gap-4">
          <Card>
            <Row className="flex justify-center">
              <Col>
                <img
                  className="mr-2 rounded w-8"
                  src={detailCompany?.logo_url}
                  alt={detailCompany?.name}
                />
              </Col>
              <span className="text-lg my-auto">
                {detailCompany.name}{' '}
                <span className="px-2 py-1 bg-gray-200 rounded-md text-sm ml-1">
                  N/A
                </span>
              </span>
            </Row>
            <Row className="flex gap-2 mt-1 justify-center">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.website_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-gray-700 text-lg"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.linkedin_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#0288d1] text-lg"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.facebook_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#3f51b5] text-lg"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(detailCompany?.twitter_url);
                  }}
                  className="border p-1 rounded cursor-pointer text-[#03a9f4] text-lg"
                />
              </Col>
              <div class="border-r border-solid"></div>
              <Col>
                <CopyToClipboard
                  text={detailCompany?.phone}
                  onCopy={() => {
                    notification.success({ message: 'Success Copy Number' });
                  }}
                >
                  <Button icon={<PhoneOutlined />}>
                    {detailCompany?.phone}
                  </Button>
                </CopyToClipboard>
              </Col>
            </Row>
          </Card>
          <Card>
            <span className="font-semibold">
              {showFullText
                ? detailOrganization?.short_description
                : detailOrganization?.short_description?.slice(0, 200)}
            </span>
            {detailOrganization?.short_description ? (
              detailOrganization?.short_description.length > 200 ? (
                <button
                  className="text-blue-500 text-sm font-normal ml-1"
                  onClick={toggleText}
                >
                  {showFullText ? 'Show Less' : '... Show More'}
                </button>
              ) : (
                ''
              )
            ) : (
              ''
            )}
            <p className="mt-6 font-semibold text-base">
              Company Keyword <br />
              <Row className="mt-2">
                {detailOrganization?.keywords
                  ? detailOrganization?.keywords.map((item, i) => (
                      <Col className="mt-1 my-2">
                        <span
                          className="px-3 py-1 bg-gray-200 rounded mr-2 font-normal"
                          key={i}
                        >
                          {item}
                        </span>
                      </Col>
                    ))
                  : ''}
              </Row>
            </p>
          </Card>
          <Card>
            <Row>
              <Col className="grid gap-3" span={12}>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Industry</span>
                  </Col>
                  <Col span={18}>
                    <span className="flex flex-wrap">
                      {detailOrganization?.industries
                        ? detailOrganization?.industries.map((item, i) => (
                            <span
                              className="rounded mr-2 text-base font-semibold text-blue-500"
                              key={i}
                            >
                              {item}
                              {i === detailOrganization?.industries?.length - 1
                                ? '.'
                                : ','}
                            </span>
                          ))
                        : ''}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">
                      Founding Year
                    </span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.founded_year}
                    </span>
                  </Col>
                </Row>
                <Row className="flex items-start">
                  <Col span={4}>
                    <span className="font-semibold text-base">Employees</span>
                  </Col>
                  <Col span={18}>
                    <span className="rounded text-base">
                      {detailOrganization?.estimated_num_employees}
                    </span>
                  </Col>
                </Row>
                {detailOrganization?.publicly_traded_exchange && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">Trading</span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.publicly_traded_exchange?.toUpperCase()}{' '}
                        :{' '}
                        {detailOrganization?.publicly_traded_symbol?.toUpperCase()}
                        .
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.market_cap && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Market Cap
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        {detailOrganization?.market_cap}
                      </span>
                    </Col>
                  </Row>
                )}
                {detailOrganization?.annual_revenue_printed && (
                  <Row className="flex items-start">
                    <Col span={4}>
                      <span className="font-semibold text-base">
                        Annual Revenue
                      </span>
                    </Col>
                    <Col span={18}>
                      <span className="rounded text-base">
                        ${detailOrganization?.annual_revenue_printed}
                      </span>
                    </Col>
                  </Row>
                )}
              </Col>
              <Col span={12}>
                <Row className="items-center py-1">
                  <Col span={4} className="flex items-center mb-auto">
                    <EnvironmentOutlined className="pr-3 text-blue-400" />
                    <span className="pr-3 text-graySecondary text-base font-semibold">
                      Location
                    </span>
                  </Col>
                  <Col span={18} className="pr-3 text-base">{`${
                    detailOrganization?.raw_address
                      ? detailOrganization?.raw_address + ','
                      : ''
                  } ${
                    detailOrganization?.city
                      ? detailOrganization?.city + ','
                      : ''
                  } ${
                    detailOrganization?.state
                      ? detailOrganization?.state + ','
                      : ''
                  } ${
                    detailOrganization?.zip ? detailOrganization?.zip + ',' : ''
                  } ${
                    detailOrganization?.country
                      ? detailOrganization?.country + '.'
                      : ''
                  }`}</Col>
                </Row>
              </Col>
            </Row>
          </Card>
          <div style={{
            display: 'flex',
            justifyContent: "space-between"
          }}>
            <Card style={{ width: "30%" }} className="">
              <p className="text-base font-semibold mb-3">
                Technology Insights (
                {detailOrganization.current_technologies
                  ? detailOrganization?.current_technologies.length || 0
                  : 0}
                )
              </p>
              <List
                style={{ overflowY: 'scroll', maxHeight: '500px' }}
                header={false}
                footer={false}
                bordered
                dataSource={detailOrganization?.current_technologies || []}
                renderItem={(item) => (
                  <List.Item>
                    <Row>
                      <Col className=" text-base font-semibold" span={24}>
                        {item?.name}
                      </Col>
                      {item?.category}
                    </Row>
                  </List.Item>
                )}
              />
            </Card>
            <Card style={{ width: "69%" }} className="">
              <InSights detailOrganization={detailOrganization} detailCompany={detailCompany}/>
            </Card>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CompanyDetailModal;
