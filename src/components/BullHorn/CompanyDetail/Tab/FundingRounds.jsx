import { useEffect, useState } from 'react';
import { getRevenue } from '../../../../services/employee';
import { Col, List, Row, Tag } from 'antd';
import dayjs from 'dayjs';

const FundingRounds = (props) => {
  const { detailOrganization } = props;

  return (
    <div>
      <List
        style={{ overflowY: 'scroll', maxHeight: '500px' }}
        header={false}
        footer={false}
        bordered
        dataSource={detailOrganization?.funding_events || []}
        renderItem={(item) => (
          <List.Item>
            <Row>
              <Col className=" text-base font-semibold" span={24}>
                {item?.currency} {item?.id}
              </Col>
              <div>
                {dayjs(item?.date).format('YYYY-MM-DD HH:mm')}
              </div>
              <div style={{marginLeft: "10px"}}>
                <Tag className="rounded-md bg-[#f0f0f2] border-none px-2.5 py-0.5">
                    {item?.type}
                </Tag>
              </div>
            </Row>
          </List.Item>
        )}
      />
    </div>
  );
};

export default FundingRounds;
