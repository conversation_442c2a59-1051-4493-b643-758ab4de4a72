import { useEffect, useLayoutEffect, useState } from 'react';
import { getRevenue } from '../../../../services/employee';
import { Col, List, Row, Tabs, Tag } from 'antd';
import dayjs from 'dayjs';
import { getDetailCompanyById } from '../../../../services/companyFinder';
import {
  CartesianGrid,
  LineChart,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  ReferenceLine,
  AreaChart,
  Area,
  Line,
  BarChart,
  Bar,
} from 'recharts';
import ChartTab1 from './Chart/CharTab1';
import ChartTab2 from './Chart/CharTab2';
import ChartTab3 from './Chart/CharTab3';
import CharTab4 from './Chart/CharTab4';

const EmployeeTrends = (props) => {
  const { detailOrganization } = props;
  const [triggerData, setTriggerData] = useState();
  const [newAndChurnedData, setNewAndChurnedData] = useState();
  const [tableName, setTableName] = useState([]);

  function aggregateDataByMonth(data) {
    return data?.map((item) => {
      const { start_date, departments } = item;
      const monthName = dayjs(start_date).format('YYYY/MM');

      const totals = departments.reduce((acc, dep) => {
        if (dep.functions) {
          const func = dep.functions;
          if (!acc[func]) acc[func] = 0;
          acc[func] += dep.retained;
        }
        return acc;
      }, {});

      const sortedFunctions = Object.keys(totals).sort(
        (a, b) => totals[b] - totals[a]
      );

      const otherRetained = sortedFunctions
        .slice(6)
        .reduce((sum, func) => sum + totals[func], 0);

      const result = {
        name: monthName,
        [`${sortedFunctions[0]}`]: totals[sortedFunctions[0]],
        [`${sortedFunctions[1]}`]: totals[sortedFunctions[1]],
        [`${sortedFunctions[2]}`]: totals[sortedFunctions[2]],
        [`${sortedFunctions[3]}`]: totals[sortedFunctions[3]],
        [`${sortedFunctions[4]}`]: totals[sortedFunctions[4]],
        [`${sortedFunctions[5]}`]: totals[sortedFunctions[5]],
        other: otherRetained,
      };

      return result;
    });
  }

  function aggregateNewAndChurnedByMonth(data) {
    return data.map(item => {
        const { start_date, departments } = item;
        const monthName = dayjs(start_date).format('YYYY/MM');

        // Tính tổng new và churned cho mỗi tháng
        const totals = departments.reduce((acc, dep) => {
            acc.new += dep.new || 0;
            acc.churned += dep.churned || 0;
            return acc;
        }, { new: 0, churned: 0 });

        // Tạo đối tượng mới với tên tháng và tổng new và churned
        return {
            name: monthName,
            new: totals.new,
            churned: totals.churned == 0 ? 0 : Number(-totals.churned)
        };
    });
}

  function getRandomKeys(obj) {
    const keys = Object.keys(obj).filter(
      (key) => key !== 'other' && key !== 'name'
    );
    keys.sort(() => Math.random() - 0.5);
    keys.unshift('other');
    const colors = [
      '#e3e3e5',
      '#146ef6',
      '#3dcc85',
      '#eb7a2f',
      '#fecf40',
      '#f75252',
      'pink',
      'brown',
      'gray',
      'black',
    ];
    const mappedColors = keys.map((key, index) => ({
      name: key,
      color: colors[index],
    }));
    return mappedColors;
  }

  const handleSearchData = async () => {
    const { data } = await getDetailCompanyById({
      organizationId: detailOrganization?.id,
      withOutContext: true,
    });
    const dataMapped = aggregateDataByMonth(
      data.organization?.employee_metrics
    );
    setNewAndChurnedData(aggregateNewAndChurnedByMonth(data.organization?.employee_metrics))
    setTableName(getRandomKeys(dataMapped[0]));
    setTriggerData(dataMapped);
  };

  useEffect(() => {
    handleSearchData();
  }, [detailOrganization]);


  const items = [
    {
      key: '1',
      label: 'Total Count',
      children: <ChartTab1 detailOrganization={detailOrganization} triggerData={triggerData} tableName={tableName} />,
    },
    {
      key: '2',
      label: 'Retention',
      children: <ChartTab2 detailOrganization={detailOrganization} triggerData={newAndChurnedData} tableName={[{name: "new"}, {name: "churned"}]}/>,
    },
    {
      key: '3',
      label: 'Department',
      children: <ChartTab3 detailOrganization={detailOrganization} triggerData={triggerData} tableName={tableName}/>,
    },
    {
      key: '4',
      label: 'Location',
      children: <CharTab4 detailOrganization={detailOrganization} triggerData={triggerData} tableName={tableName}/>,
    },
  ];

  return (
    <div>
      <Tabs defaultActiveKey="1" items={items}/>
    </div>
  );
};

export default EmployeeTrends;
