import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';


const ChartTab1 = (props) => {
  const { detailOrganization, tableName, triggerData } = props;
    const dataMap = tableName?.reverse()?.map((name) => ({
        name: name.name,
        data: triggerData?.map((item) => item[name?.name])
    }));

    console.log('dataMap', dataMap)
    const options = {
        chart: {
          type: 'column'
        },
        height: 700,
        xAxis: {
          categories: triggerData?.map((item) => item.name)
        },
        yAxis: {
          min: 0,
          title: {
            text: 'Count'
          },
          stackLabels: {
            enabled: true
          }
        },
        legend: {
          align: 'left',
          x: 70,
          verticalAlign: 'top',
          y: 70,
          floating: true,
          backgroundColor: Highcharts.defaultOptions.legend.backgroundColor || 'white',
          borderColor: '#CCC',
          borderWidth: 1,
          shadow: false
        },
        tooltip: {
          headerFormat: '<b>{point.x}</b><br/>',
          pointFormat: '{series.name}: {point.y}<br/>Total: {point.stackTotal}'
        },
        plotOptions: {
          column: {
            stacking: 'normal',
            dataLabels: {
              enabled: true
            }
          }
        },
        series: dataMap
      };

  return (
    <div>
      <HighchartsReact highcharts={Highcharts} options={options} containerProps={{ style: { height: "500px" } }}/>
    </div>
  )
};

export default ChartTab1;
