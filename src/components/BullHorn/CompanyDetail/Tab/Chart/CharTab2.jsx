import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import { useEffect } from 'react';

const ChartTab2 = (props) => {
  const { detailOrganization, tableName, triggerData } = props;
  const dataMap = tableName?.map((name) => ({
    name: name.name,
    data: triggerData?.map((item) => item[name?.name]),
  }));

  useEffect(() => {
    Highcharts.Templating = Highcharts.Templating || { helpers: {} };
    Highcharts.Templating.helpers.abs = (value) => Math.abs(value);
  }, []);

  const options = {
    chart: {
      type: 'bar',
    },
    title: {
      text: 'Employee Retention in the past 2 years.',
      align: 'left',
    },

    accessibility: {
      point: {
        valueDescriptionFormat: '{index}. Age {xDescription}, {value}%.',
      },
    },
    xAxis: [
      {
        categories: triggerData?.map((item) => item.name),
        reversed: false,
        labels: {
          step: 1,
        },
      },
      {
        opposite: true,
        reversed: false,
        categories: triggerData?.map((item) => item.name),
        linkedTo: 0,
        labels: {
          step: 1,
        },
      },
    ],
    yAxis: {
      title: {
        text: null,
      },
      labels: {
        format: '{value}%',
      },
      accessibility: {
        description: 'Percentage population',
        rangeDescription: 'Range: 0 to 5%',
      },
    },
    plotOptions: {
      series: {
        stacking: 'normal',
        borderRadius: '50%',
      },
    },
    tooltip: {
      pointFormat:
        '<b>{series.name}, age {point.category}</b><br/>Population: {point.y:.2f}%',
    },
    series: dataMap,
  };

  return (
    <div>
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
        containerProps={{ style: { height: '500px' } }}
      />
    </div>
  );
};

export default ChartTab2;
