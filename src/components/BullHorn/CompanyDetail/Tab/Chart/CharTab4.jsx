import React, { useState, useEffect, useRef } from 'react';
import Highcharts from 'highcharts';
import HighchartsMap from 'highcharts/modules/map';
import HighchartsMore from 'highcharts/highcharts-more';

// Import necessary modules
HighchartsMore(Highcharts);
HighchartsMap(Highcharts);

const CharTab4 = () => {
    const [allMaps, setAllMaps] = useState({});
    const [mapName, setMapName] = useState('World, medium resolution');
    const [mapKey, setMapKey] = useState('custom/world');
    const [showDataLabels, setShowDataLabels] = useState(false);

    const inputRef = useRef(null);
    const chartRef = useRef(null);

    const baseMapPath = 'https://code.highcharts.com/mapdata/';
    const mapsToSkip = [
        'World, Eckert III projection, high resolution',
        'World, Eckert III projection, low resolution',
        'World, Eckert III projection, medium resolution',
        'World, Robinson projection, high resolution',
        'World, Robinson projection, low resolution',
        'World, Robinson projection, medium resolution'
    ];

    useEffect(() => {
        const fetchMapData = async () => {
            const allMapsData = { ...Highcharts.mapDataIndex };
            Object.entries(allMapsData).forEach(([mapGroup, maps]) => {
                if (mapGroup !== 'version') {
                    Highcharts.merge(true, allMaps, maps);
                }
            });

            Object.keys(allMaps).forEach(key => {
                if (key.includes(', Miller projection')) {
                    allMaps[key.replace(', Miller projection', '')] = allMaps[key];
                    delete allMaps[key];
                }
            });

            setAllMaps(
                Object.entries(allMaps)
                    .filter(([desc]) => !mapsToSkip.includes(desc))
                    .reduce((acc, [desc, path]) => {
                        acc[desc] = path;
                        return acc;
                    }, {})
            );
        };

        fetchMapData();
    }, []);

    useEffect(() => {
        const fetchInitialMapData = async () => {
            const response = await fetch(`${baseMapPath}${mapKey}.topo.json`);
            const mapData = await response.json();

            Highcharts.mapChart(chartRef.current, {
                accessibility: {
                    series: {
                        descriptionFormat: '{series.name}, map with {series.points.length} areas.',
                        pointDescriptionEnabledThreshold: 50
                    }
                },
                chart: {
                    events: {
                        drilldown: onDrilldown,
                        afterDrillUp: onAfterDrillUp,
                        afterDrilldown: function () {
                            this.credits.update();
                        }
                    }
                },
                colorAxis: { min: 0 },
                drilldown: {
                    activeDataLabelStyle: {
                        color: '#fff',
                        fontWeight: 'normal',
                        textDecoration: 'none'
                    }
                },
                mapNavigation: {
                    enabled: true,
                    buttonOptions: { alignTo: 'spacingBox', x: 10 }
                },
                legend: { layout: 'vertical', align: 'left', verticalAlign: 'bottom' },
                plotOptions: { map: { dataLabels: { enabled: showDataLabels } } },
                series: [{
                    data: processMapData(mapData),
                    mapData: mapData,
                    joinBy: ['hc-key', 'key'],
                    name: mapName,
                    dataLabels: { formatter: dataLabelsFormatter },
                    custom: { mapName, mapKey }
                }],
                title: { text: null },
                responsive: {
                    rules: [{
                        condition: {
                            callback() { return document.body.offsetWidth < 753; }
                        },
                        chartOptions: {
                            colorAxis: { layout: 'horizontal' },
                            legend: { align: 'center' },
                            mapNavigation: { buttonOptions: { verticalAlign: 'bottom' } }
                        }
                    }]
                }
            });
        };

        fetchInitialMapData();
    }, [mapName, showDataLabels]);

    const processMapData = (mapData) => {
        return mapData.objects.default.geometries.map((g, value) => ({
            key: g.properties['hc-key'],
            drilldown: g.properties['hc-key'],
            value
        }));
    };

    const dataLabelsFormatter = function () {
        return (
            mapKey === 'custom/world' ||
            mapKey === 'countries/us/us-all'
        ) ?
            (this.point.properties && this.point.properties['hc-a2']) :
            this.point.name;
    };

    const onDrilldown = async (e) => {
        const map = Object.entries(allMaps).find(([name]) =>
            name === e.point.name
        ) || Object.entries(allMaps).find(([name]) =>
            name.indexOf(e.point.name) === 0
        );

        if (map) {
            const [mapName, path] = map;
            const mapKey = path.slice(0, -3);

            chartRef.current.showLoading('<i class="icon-spinner icon-spin icon-3x"></i>');

            const response = await fetch(`${baseMapPath}${mapKey}.topo.json`);
            const topology = await response.json();

            const data = processMapData(topology);

            chartRef.current.hideLoading();
            chartRef.current.addSeriesAsDrilldown(e.point, {
                mapData: topology,
                name: e.point.name,
                data,
                joinBy: ['hc-key', 'key'],
                dataLabels: { formatter: dataLabelsFormatter },
                custom: { mapName, mapKey }
            });

            setMapName(mapName);
            setMapKey(mapKey);
            inputRef.current.value = mapName;
        }
    };

    const onAfterDrillUp = (e) => {
        const { mapName, mapKey } = e.seriesOptions.custom;
        if (mapName && mapKey) {
            setMapName(mapName);
            setMapKey(mapKey);
            inputRef.current.value = mapName;
        }
        chartRef.current.credits.update();
    };

    const updateChart = async (mapName) => {
        const mapKey = allMaps[mapName].slice(0, -3);

        chartRef.current.showLoading('<i class="fa fa-spinner fa-spin fa-2x"></i>');

        const response = await fetch(`${baseMapPath}${mapKey}.topo.json`);
        const mapData = await response.json();

        if (!mapData) {
            chartRef.current.showLoading('<i class="fa fa-frown"></i> Map not found');
            return;
        }

        const data = processMapData(mapData);

        chartRef.current.series[0].update({
            mapData,
            data,
            name: mapName,
            dataLabels: { formatter: dataLabelsFormatter },
            custom: { mapName, mapKey }
        });

        chartRef.current.hideLoading();
        chartRef.current.credits.update();
    };

    const handleInputChange = (e) => {
        if (allMaps[e.target.value]) {
            const pointOnCurrentMap = chartRef.current.series[0].points.find(point => point.name === e.target.value);

            if (pointOnCurrentMap) {
                pointOnCurrentMap.doDrilldown();
            } else {
                resetDrilldown();
                updateChart(e.target.value);
            }
        }
    };

    const resetDrilldown = () => {
        if (chartRef.current.breadcrumbs && chartRef.current.breadcrumbs.elementList[0]) {
            chartRef.current.breadcrumbs.destroy();
            delete chartRef.current.breadcrumbs;
            delete chartRef.current.drilldown;
            delete chartRef.current.drilldownLevels;
        }
    };

    const handlePrevButtonClick = () => {
        const desiredIndex = Object.keys(allMaps).indexOf(mapName) - 1;
        const [nextMapName] = Object.entries(allMaps)[
            desiredIndex < 0 ? Object.keys(allMaps).length - 1 : desiredIndex
        ];
        resetDrilldown();
        updateChart(nextMapName);
        setMapName(nextMapName);
    };

    const handleNextButtonClick = () => {
        const desiredIndex = Object.keys(allMaps).indexOf(mapName) + 1;
        const [nextMapName] = Object.entries(allMaps)[
            desiredIndex > Object.keys(allMaps).length - 1 ? 0 : desiredIndex
        ];
        resetDrilldown();
        updateChart(nextMapName);
        setMapName(nextMapName);
    };

    const handleDataLabelsCheckboxChange = (e) => {
        setShowDataLabels(e.target.checked);
    };

    return (
        <div>
            <div id="container" ref={chartRef}></div>
        </div>
    );
};

export default CharTab4;
