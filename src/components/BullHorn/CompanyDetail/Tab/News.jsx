import { useEffect, useState } from 'react';
import { getNewsOfCompany } from '../../../../services/employee';
import { Button, Dropdown, List, Space, Spin, Tag } from 'antd';
import dayjs from 'dayjs';
import { CaretDownOutlined, DownOutlined } from '@ant-design/icons';

const NewsTab = (props) => {
  const { detailOrganization } = props;
  const [newData, setNewData] = useState([]);
  const [loadingNews, setLoadingNews] = useState(false);

  const handleGetData = async (orderBy) => {
    const { data } = await getNewsOfCompany({
      organizationIds: [detailOrganization?.id],
      sortAscending: orderBy || false,
    });
    setNewData(data.news_articles);
  };

  useEffect(() => {
    handleGetData();
  }, []);

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  const items = [
    {
      label: <div onClick={() => handleGetData(true)}>Recent first</div>,
      key: '0',
    },
    {
      label: <div onClick={() => handleGetData(false)}>Oldest first</div>,
      key: '1',
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div></div>
        <div style={{marginBottom: "20px"}}>
          <Dropdown
            menu={{
              items,
            }}
            trigger={['click']}
            onOpenChange={(e) => console.log(e)}
          >
            <Button>
              Sort By <CaretDownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>

      <List
        style={{ overflowY: 'scroll', maxHeight: '500px' }}
        header={false}
        footer={false}
        bordered
        loading={loadingNews}
        dataSource={newData || []}
        renderItem={(item) => (
          <List.Item>
            <div
              onClick={() => handleGotoWebsite(item?.url)}
              style={{width: "100%"}}
              className="hover:bg-[#f0f0f2] cursor-pointer p-2.5 rounded-md"
            >
              <div className="font-semibold text-sm">{item?.title}</div>
              <div className="text-gray-600">
                {dayjs(item?.published_at).format('YYYY-MM-DD HH:mm')}
              </div>
              <div className="underline text-xs">{item?.domain}</div>
              <div className="mt-0.5">
                {item?.event_categories?.map((ca) => {
                  return (
                    <Tag className="rounded-md bg-[#f0f0f2] border-none px-2.5 py-0.5">
                      {ca}
                    </Tag>
                  );
                })}
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};

export default NewsTab;
