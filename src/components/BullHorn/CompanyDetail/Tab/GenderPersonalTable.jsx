import {
  CaretDownOutlined,
  CopyOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON>inOutlined,
  <PERSON>Outlined,
  MenuUnfoldOutlined,
  PhoneOutlined,
  SendOutlined,
  SmallDashOutlined,
  TwitterOutlined,
} from '@ant-design/icons';
import { Button, Checkbox, Col, Dropdown, Image, Pagination, Popover, Row, Table, notification } from 'antd';
import { useState } from 'react';
import { UserGroupIcon } from '../../../Sidebar/consts';
import { Tooltip } from 'chart.js';
import logo from '/logo_bull.webp';
import { getLDetailEmployee } from '../../../../services/employee';
import { COMMON_STRINGS } from '../../../../constants/common.constant';

const GenderPersonalTable = (props) => {
  const {
    listCurrentOrg,
    setListCurrentOrg,
    isLoadingEmployee,
    listEmployeeCompany,
    listEmployeePaginationCompany,
    handleGetData,
    currentPersonal
  } = props;

  console.log(listEmployeeCompany, listCurrentOrg)
  const [listDetailEmployee, setListDetailEmployee] = useState([]);

  const handleGetDetailEmployee = async (id) => {
    try {
        const { data } = await getLDetailEmployee({ employeeId: id });
        if (data) {
          setListDetailEmployee([...listDetailEmployee, { ...data }]);
          // setIsLoading(true);
          // // setListEmployee([...listEmployee]);
          // // setListEmployeePagination({ ...listEmployeePagination });
          // setIsLoading(false);
        }
    } catch (error) {
      console.error('Error in getting employee detail', error);
      if (error?.response?.status == 402) {
        notification.error({
          message: 'Your account is running out of credits. Please contact to your admin for support!',
        });
      } else {
        notification.error({
          message: 'Something went wrong. Please try again in 5 minutes',
        });
      }
    }
  };

  const handlePaginationListEmployee = (page) => {
    handleGetData(currentPersonal, page);
  };

  const itemsDropdown = [
    {
      key: '1',
      label: <div>Edit</div>,
    },
    {
      key: '2',
      label: <div>Delete</div>,
    },
  ];

  const handleGenderSupportBar = (record, name = null) => {
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              // await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      // onClick={() => {
                      //   setOpenExistingSequence(true);
                      // }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                       
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <div>
                      {record?.email}{' '}
                      <CopyOutlined
                        style={{ marginLeft: '10px' }}
                        onClick={() => {
                          navigator.clipboard.writeText(record?.email),
                            notification.success({
                              message: 'Copy To Clipboard success',
                            });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      // onClick={() => setOpenExistingUserGroup(true)}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const columnsPeople = [
    // {
    //   title: (
    //     <>
    //       <Checkbox
    //       // disabled={listEmployee.length == 0}
    //       // checked={listSelectSection.includes(listEmployeePagination.page)}
    //       // onChange={() => handleCheckAll()}
    //       // indeterminate={
    //       //   selectedRowKeys.length > 0 &&
    //       //   selectedRowKeys.length < listEmployee.length &&
    //       //   listEmployee
    //       //     .map((item) => item.id)
    //       //     .filter((element) => selectedRowKeys.includes(element)).length >
    //       //     0
    //       // }
    //       ></Checkbox>
    //     </>
    //   ),
    //   fixed: 'left',
    //   width: '50px',
    //   dataIndex: 'checkbox',
    //   key: 'checkbox',
    //   render: (allowWrite, record) => (
    //     <div
    //       onClick={(e) => {
    //         e.stopPropagation();
    //       }}
    //     >
    //       <>
    //         <Checkbox
    //         //   checked={selectedRowKeys.includes(record.id)}
    //           onChange={(e) => {
    //             e.stopPropagation();
    //             // handleClickSignal(record?.id);
    //           }}
    //         ></Checkbox>
    //       </>
    //     </div>
    //   ),
    // },
    {
      title: COMMON_STRINGS.FULL_NAME,
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: '100px',
      render: (allowWrite, record) => (
        <>
          <Row gutter={16}>
            <p className="font-semibold mr-2">{record?.name}</p>
            {record?.linkedin_url && (
              <Row gutter={16}>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      //   handleGotoWebsite(record?.linkedin_url);
                    }}
                    title={`Access to linkedin account`}
                    className="cursor-pointer text-[#0288d1]"
                  />
                </Col>
              </Row>
            )}
          </Row>
        </>
      ),
    },
    {
      title: COMMON_STRINGS.JOB_TITLE,
      dataIndex: 'title',
      key: 'title',
      width: '220px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p>{record?.title}</p>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'organization.name',
      key: 'organization.name',
      width: '100px',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              src={
                record.organization?.logo_url
                  ? `${record.organization?.logo_url}`
                  : ''
              }
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  //   handleDetailCompany(record?.organization);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.organization?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    // handleGotoWebsite(record?.organization?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    // handleGotoWebsite(record?.organization?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    // handleGotoWebsite(record?.organization?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'left',
      width: '100px',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          {record?.email &&
          record?.email !== '<EMAIL>' ? (
            <>{handleGenderSupportBar(record)}</>
          ) : listDetailEmployee.some(
              (item) =>
                item.person_id === record.id ||
                item.person_id === record.person_id
            ) ? (
            listDetailEmployee
              .filter(
                (item) =>
                  item.person_id === record.id ||
                  item.person_id === record.person_id
              )
              .map((item, index) => (
                <>{handleGenderSupportBar(item, record?.name)}</>
              ))
          ) : (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleGetDetailEmployee(record.id || record.person_id);
              }}
              type="primary"
              icon={<MailOutlined />}
            >
              Access Email
            </Button>
          )}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone_numbers',
      key: 'phone_numbers',
      align: 'left',
      width: '100px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{ color: 'blue', textAlign: 'center' }}
        >
          <a href={`tel:${record?.phone_numbers?.[0]?.sanitized_number}`}>
            {record?.phone_numbers?.[0]?.sanitized_number}
          </a>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.CONTACT_LOCATION,
      dataIndex: 'contact-location',
      key: 'contact-location',
      align: 'left',
      width: '150px',
      render: (allowRead, record) => (
        <div
          className="flex gap-2"
          style={{
            fontWeight: '600',
          }}
        >
          {record?.state ? `${record?.state}, ` : ""} {record?.city ? `${record?.city}, ` : ""} {record?.country ? `${record?.country}` : ""}
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.EMPLOYEES,
      dataIndex: 'employees',
      key: 'employees',
      width: '100px',
      align: 'left',
      render: (allowRead, record) => {
        const items = listCurrentOrg.find(
          (element) => element?.id === record?.organization_id
        );
        return (
          <Button
            type="text"
            className=" !border-[#b2b8be] flex gap-2 items-center font-Montserrat"
          >
            <UserGroupIcon className="w-4 h-4" />
            {items?.estimated_num_employees}
            <span>Employees</span>
          </Button>
        );
      },
    },
    {
      title: COMMON_STRINGS.INDUSTRY,
      dataIndex: 'industry',
      key: 'industry',
      width: '150px',
      align: 'left',
      render: (allowRead, record) => {
        const items = listCurrentOrg?.find(
          (element) => element?.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
            <div>
              <span
                  style={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    WebkitLineClamp: 2,
                  }}
                >
                  {items?.industries?.map((item, index) => (
                    <span key={index}>
                      {item}
                      {index < items?.industries?.length - 1 && ', '}
                    </span>
                  ))}
                </span>
            </div>
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      align: 'left',
      width: '150px',
      render: (allowRead, record) => {
        const items = listCurrentOrg?.find(
          (element) => element?.id === record?.organization_id
        );
        return (
          <div
            style={{
              fontWeight: '600',
            }}
          >
              <span
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                }}
              >
                {items?.keywords?.map((item, index) => (
                  <span key={index}>
                    {item}
                    {index < items?.keywords.length - 1 && ', '}
                  </span>
                ))}
              </span>
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ width: '60vw' }}>
      <Col flex="auto" className="search-table-new-design-container">
        <div className="customTable">
          <Table
            // scroll={{ x: true, y: 360 }}
            scroll={{
              x: 3000,
              y: 400
            }}
            loading={isLoadingEmployee}
            pagination={false}
            columns={columnsPeople}
            dataSource={listEmployeeCompany}
            rowClassName="custom-row"
            className="custom-table"
          />
            <Pagination
                  className="mt-3"
                  defaultCurrent={listEmployeePaginationCompany.page}
                  total={listEmployeePaginationCompany.total_entries}
                  showSizeChanger={false}
                  onChange={handlePaginationListEmployee}
                  pageSize={25}
                />
        </div>
      </Col>
    </div>
  );
};

export default GenderPersonalTable;
