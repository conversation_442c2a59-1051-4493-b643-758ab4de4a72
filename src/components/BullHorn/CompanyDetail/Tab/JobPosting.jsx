import { useEffect, useState } from 'react';
import { getNewFeedEvent, getRevenue } from '../../../../services/employee';
import { Col, List, Row, Tag } from 'antd';
import dayjs from 'dayjs';

const JobPosting = (props) => {
  const { detailOrganization } = props;
  const [dataJob, setDataJob] = useState([]);

  const handleGetData = async () => {
    const { data } = await getNewFeedEvent({
      organizationIds: [detailOrganization?.id],
    });

    setDataJob(data?.newsfeed_events);
  };

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  useEffect(() => {
    handleGetData();
  }, []);
  return (
    <div>
      <List
        style={{ overflowY: 'scroll', maxHeight: '500px' }}
        header={false}
        footer={false}
        bordered
        dataSource={dataJob || []}
        renderItem={(item) => (
          <List.Item>
            <div
              onClick={() => handleGotoWebsite(item?.job?.url)}
              style={{width: "100%"}}
              className="hover:bg-[#f0f0f2] cursor-pointer p-2.5 rounded-md"
            >
              <div className="font-semibold text-sm">{item?.job?.title}</div>
              <div className="text-gray-600">
                {dayjs(item?.published_at).format('YYYY-MM-DD HH:mm')}
              </div>
              <div style={{
                            fontWeight: 400,
                            fontSize: "14px"
                        }}>{item?.job?.city}, {item?.job?.country}</div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};

export default JobPosting;
