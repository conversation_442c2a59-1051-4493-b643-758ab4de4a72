import { useEffect, useState } from 'react';
import {
  getListEmployee,
  getOrganizationsSnippet,
  getRevenue,
} from '../../../../services/employee';
import { Checkbox, Col, Row, Tag } from 'antd';
import {
  FacebookOutlined,
  LinkOutlined,
  LinkedinOutlined,
  TwitterOutlined,
} from '@ant-design/icons';
import JobPosting from './JobPosting';
import GenderPersonalTable from './GenderPersonalTable';

const Overview = (props) => {
  const { detailOrganization, detailCompany } = props;
  const [personalData, setPersonalData] = useState([]);
  const [dataPerson, setDataPerson] = useState([]);
  const [activeTable, setActiveTable] = useState(false);
  const [listCurrentOrg, setListCurrentOrg] = useState([]);
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [currentPersonal, setCurrentPersonal] = useState(1);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
  useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });

  const handleGetRevenue = async () => {
    const { data } = await getRevenue({
      openFactorNames: ['person_personas'],
    });

    setPersonalData(data?.faceting?.person_persona_facets);
  };

  useEffect(() => {
    handleGetRevenue();
  }, []);

  useEffect(() => {
    setDataPerson([])
    handleGetRevenue()
    setListEmployeePaginationCompany({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    })
    setActiveTable(false)
  }, [detailCompany])

  const handleGetData = async (personal, page) => {
    try {
      setActiveTable(true);
      setIsLoadingEmployee(true)
      setCurrentPersonal(personal)
      const { data } = await getListEmployee({
        qPersonPersonaIds: [personal],
        mixedAccountIds: [detailOrganization?.account_id],
        context: "account-show-people-page",
        page: page || 1
      });
      setDataPerson(data?.result?.people);

      const listOrgIds = data?.result?.people?.map(
        (item) => item?.organization_id
      );

    //   const uniqueOrgIds = [...new Set(listOrgIds)];
      const dataOrg = await getOrganizationsSnippet({
        ids: listOrgIds,
      });

      setListCurrentOrg(dataOrg.data.organizations);
      setListEmployeePaginationCompany(data?.result?.pagination);
    } catch (err) {
      console.log('Error get data', err);
    }
    setIsLoadingEmployee(false)
  };

  return (
    <div>
      <div>
        {detailOrganization?.context?.relevant_personas?.map((item) => {
          const personal = personalData?.find(
            (pr) => pr.value === item.persona_id
          );
          return (
            <Tag
              onClick={() => handleGetData(item.persona_id)}
              style={{ cursor: 'pointer', padding: '5px' }}
            >
              <span style={{ fontSize: '13px' }}>{personal?.display_name}</span>
              <span style={{ marginLeft: '5px' }}>({item?.count})</span>
            </Tag>
          );
        })}
      </div>
      {activeTable && (
         <div style={{ position: 'relative', width: '100%', marginTop: '20px' }}>
         <GenderPersonalTable
           listCurrentOrg={listCurrentOrg}
           listEmployeeCompany={dataPerson}
           isLoadingEmployee={isLoadingEmployee}
           listEmployeePaginationCompany={listEmployeePaginationCompany}
           handleGetData={handleGetData}
           currentPersonal={currentPersonal}
         />
       </div>
      )}
    </div>
  );
};

export default Overview;
