import { useEffect, useState } from "react";
import { getRevenue } from "../../../../services/employee";
import { Col, List, Row, Tag } from "antd";

const TechnologiesTab = (props) => {
    const {detailOrganization} = props

    return (
        <div>
            <List
                style={{ overflowY: 'scroll', maxHeight: '500px' }}
                header={false}
                footer={false}
                bordered
                dataSource={detailOrganization?.current_technologies || []}
                renderItem={(item) => (
                  <List.Item>
                    <Row>
                      <Col className=" text-base font-semibold" span={24}>
                        {item?.name}
                      </Col>
                      {item?.category}
                    </Row>
                  </List.Item>
                )}
              />
        </div>
    )
}

export default TechnologiesTab;