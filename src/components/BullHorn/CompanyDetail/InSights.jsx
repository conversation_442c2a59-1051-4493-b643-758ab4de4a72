import { Tabs } from 'antd';
import Overview from './Tab/Overview';
import NewsTab from './Tab/News';
import TechnologiesTab from './Tab/TechnologiesTab';
import JobPosting from './Tab/JobPosting';
import FundingRounds from './Tab/FundingRounds';
import EmployeeTrends from './Tab/EmployeeTrends';

const InSights = (props) => {
  const { detailOrganization, detailCompany } = props;
  const items = [
    {
      key: '1',
      label: 'Overview',
      children: <Overview detailCompany={detailCompany} detailOrganization={detailOrganization} />,
    },
    {
      key: '2',
      label: 'News',
      children: <NewsTab detailOrganization={detailOrganization} />,
    },
    {
      key: '3',
      label: `Technologies (
        ${
          detailOrganization.current_technologies
            ? detailOrganization?.current_technologies.length || 0
            : 0
        }
        )`,
      children: <TechnologiesTab detailOrganization={detailOrganization}/>,
    },
    {
      key: '4',
      label: `Funding rounds ( ${ detailOrganization.funding_events
        ? detailOrganization?.funding_events.length || 0
        : 0} )`,
      children: <FundingRounds detailOrganization={detailOrganization}/>,
    },
    {
      key: '5',
      label: 'Job Postings',
      children: <JobPosting detailOrganization={detailOrganization}/>,
    },
    {
      key: '6',
      label: 'Employee trends',
      children: <EmployeeTrends detailOrganization={detailOrganization}/>,
    },
  ];
  const onChange = (key) => {
    console.log(key);
  };
  return (
    <div>
      <div style={{ fontSize: '16px', fontWeight: 600 }}>Insights </div>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange}/>
    </div>
  );
};

export default InSights;
