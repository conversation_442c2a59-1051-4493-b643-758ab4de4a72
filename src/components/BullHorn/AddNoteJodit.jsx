import { DeleteOutlined } from '@ant-design/icons';
import { Button, Select, notification } from 'antd';
import { useRef, useState } from 'react';
import { NOTE_ACTION } from './EmailtriggerStep';
import TextArea from 'antd/es/input/TextArea';
import clsx from 'clsx';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from './BullhornSendEmailModal';

const AddNoteJodit = (props) => {
  const {
    inputNumber,
    inputNumberStep,
    setInputNumberStep,
    index,
    setLockedStep,
    comments,
    actions,
    isEdit,
    setEditingNotes,
    isLinkedinInvitationStep = false,
    mergeFieldsBasedOnPlace,
    addSavedSteps,
    endEdittingStep = () => {},
  } = props;

  const [noteComments, setNoteComments] = useState(comments);
  const [noteAction, setNoteAction] = useState(actions);
  const handelAddNoteData = () => {
    if (!noteComments && !isLinkedinInvitationStep) {
      notification.error({
        message: 'Please enter note comments',
      });
      return;
    }

    if (!noteAction && !isLinkedinInvitationStep) {
      notification.error({
        message: 'Please select note action',
      });
      return;
    }
    const newItem = [...inputNumberStep];
    const index = newItem.findIndex((item) => item.key === inputNumber?.key);
    if (index !== -1) {
      newItem[index].content = noteComments;
      if (!isLinkedinInvitationStep) {
        newItem[index].subject = noteAction;
        newItem[index].type = 'NOTE';
        newItem[index].saved = true;
      }
    }
    isEdit && setEditingNotes(null);
    setInputNumberStep(newItem);
    setLockedStep(false);
    addSavedSteps(newItem[index]?.key || newItem[index]?.id)
    endEdittingStep()
  };

  const handelChangeSelect = (value) => {
    setNoteAction(value);
  };

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setNoteComments(data);
  };
  const editor = useRef(null);
  return (
    <div
      className={clsx(
        'bg-white py-3 px-2 rounded-bl-lg rounded-br-lg shadow-xl mail-content-container w-full',
        isEdit
          ? 'border-2 border-t-0 border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
          : 'border border-[#ccc]'
      )}
    >
      {/* Next Email will be sent in{' '} */}
      <div
        style={{
          textAlign: 'left',
        }}
        className="w-full"
      >
        {isLinkedinInvitationStep ? 'InMail Message' : 'Comments'}
        <div className="w-full">
          {!isLinkedinInvitationStep && (
            <CKEditor
              ref={editor}
              editor={ClassicEditor}
              config={{
                ...editorConfig,
                toolbar: {
                  ...editorConfig.toolbar,
                  shouldNotGroupWhenFull: false,
                },
                mergeFields: {...mergeFieldsBasedOnPlace}
              }}
              data={noteComments || ''}
              onChange={handleCKEditorChange}
              onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
            />
          )}
          {isLinkedinInvitationStep && (
            <TextArea
              value={noteComments}
              onChange={(ev) => setNoteComments(ev.target.value)}
            />
          )}
        </div>
        {!isLinkedinInvitationStep && (
          <div style={{ marginTop: '10px' }}>
            <span>Action</span>
            <div>
              <Select
                size="middle"
                style={{
                  width: 200,
                }}
                defaultValue={{
                  label: actions,
                  value: actions,
                }}
                options={NOTE_ACTION}
                onChange={handelChangeSelect}
              />
            </div>
          </div>
        )}

        <div
          style={{
            marginTop: '20px',
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Button
            onClick={(e) => {
              e.stopPropagation();
              // setNumberStep(numberStep - 1);
              const newArr = inputNumberStep.filter(
                (item, itemIndex) => itemIndex !== index
              );
              setInputNumberStep([...newArr]);
              setLockedStep(false);
              setEditingNotes(null)
            }}
          >
            <DeleteOutlined />
          </Button>
          <Button onClick={() => handelAddNoteData()}>Save</Button>
        </div>
      </div>
    </div>
  );
};

export default AddNoteJodit;
