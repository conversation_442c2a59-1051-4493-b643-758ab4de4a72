import {
  CheckOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import {
  Button,
  DatePicker,
  notification,
  Select,
  Spin,
  TimePicker,
  Tooltip,
} from 'antd';
import { Calendar } from 'react-date-range';
import dayjs from 'dayjs';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { capFirst, getOffSet } from '../../utils/common';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { getSequenceTimeSettings } from '../../services/users';
import { isEmpty } from 'lodash';
import { getTimezoneList } from '../../services/nylas';

const SequenceScheduleDate = ({
  scheduleInformation,
  setScheduleInformation,
  closeScheduleDate,
  setSequenceScheduled,
  emailSeqId = null,
}) => {
  const [loading, setLoading] = useState(true);
  const [tzOptions, setTZOptions] = useState([]);

  const getDefaultSettings = async (timezoneList = null) => {
    try {
      const { data } = await getSequenceTimeSettings();
      const userSettings = data?.result || {};
      if (
        !isEmpty(userSettings?.timezone) &&
        !isEmpty(userSettings?.workingHours)
      ) {
        const selectedTZ = [...(timezoneList || tzOptions)].find(
          (item) => item?.timezone === userSettings?.timezone
        );
        if (emailSeqId) {
          if (!scheduleInformation?.name) {
            setScheduleInformation({
              ...scheduleInformation,
              name: selectedTZ?.timezone,
              utc: selectedTZ?.utc,
            });
          }

          return;
        };
        setScheduleInformation({
          ...scheduleInformation,
          utc: selectedTZ?.utc,
          name: selectedTZ?.timezone,
        });
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const getTimezonesList = async () => {
    try {
      const { data } = await getTimezoneList();

      if (data?.result?.length > 0) {
        const tzList = data?.result?.map((item) => ({
          label: item.text,
          value: item.timezone,
          order: item.order,
          ...item,
        }));

        setTZOptions([...tzList]);
      }
      return (await data?.result) || [];
    } catch (error) {
      console.log('error: ', error);
      return [];
    }
  };

  const getData = async () => {
    try {
      setLoading(true);
      const timezoneList = await getTimezonesList();
      await getDefaultSettings(timezoneList);

      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <div className="min-w-[45rem]">
      {loading && (
        <div className="min-h-[10rem] w-full flex justify-center items-center">
          <Spin />
        </div>
      )}
      {!loading && (
        <div>
          <div className="border-b w-full grid grid-cols-10">
            <div className="px-5 py-8 border-r col-span-4 flex flex-col justify-between min-h-[25rem]">
              <div className="flex flex-col gap-2">
                <div className="text-sm text-[#9eadc0] font-medium">
                  Walkthrough
                </div>
                <div className="text-xl font-bold">Schedule a Sequence</div>
                <div className="flex gap-1 items-center bg-[#e8f2ff] text-[#2683f2] w-fit px-2 py-1 rounded-md font-medium">
                  <ClockCircleOutlined className="font-semibold" />
                  <Tooltip title="Sequence duration">
                    <span>10 mins</span>
                  </Tooltip>
                </div>
              </div>
              <div className="flex flex-col gap-3 bg-[#f7f9fb] pl-3 py-2 rounded-md">
                <div>
                  <div className="text-xs font-medium text-gray-700">
                    TIME ZONE
                  </div>
                  <div className="text-base font-bold pt-1">
                    {tzOptions.find(
                      (item) => item?.timezone === scheduleInformation?.name
                    )?.label || '-'}
                  </div>
                </div>
                {dayjs(scheduleInformation?.triggerAt).format(
                  'DD. MMM YYYY'
                ) !== 'Invalid Date' && (
                  <div>
                    <div className="text-xs font-medium text-gray-700">
                      DATE
                    </div>
                    <div className="text-base font-bold pt-1">
                      {dayjs(scheduleInformation?.triggerAt).format(
                        'DD. MMM YYYY'
                      ) || '-'}
                    </div>
                  </div>
                )}
                {scheduleInformation?.triggerAt && (
                  <div>
                    <div className="text-xs font-medium text-gray-700">
                      TIME
                    </div>
                    <div className="text-base font-bold pt-1">
                      <TimePicker
                        variant="borderless"
                        bordered={false}
                        size="large"
                        allowClear={false}
                        defaultValue={dayjs(
                          scheduleInformation?.triggerAt?.split('T')[1],
                          'HH:mm'
                        )}
                        format="HH:mm"
                        onChange={(value) => {
                          if (!scheduleInformation?.triggerAt) return;
                          const newTime = dayjs(value).format('HH:mm');
                          let currTriggerAt =
                            scheduleInformation?.triggerAt?.split('T') || [];
                          currTriggerAt[1] = newTime;

                          setScheduleInformation({
                            ...scheduleInformation,
                            triggerAt: currTriggerAt.join('T'),
                          });
                          setSequenceScheduled(true);
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-7 col-span-6 relative">
              <Tooltip title="Close">
                <Button
                  className="absolute top-3 right-3"
                  type="text"
                  onClick={closeScheduleDate}
                  icon={<CloseOutlined />}
                />
              </Tooltip>
              <div className="text-sm text-[#9eadc0] font-medium flex flex-col items-center justify-between gap-2">
                <div>Select a Date & Time</div>
                <Select
                  loading={loading}
                  optionFilterProp="label"
                  style={{ width: '100%' }}
                  showSearch
                  defaultValue={scheduleInformation.name}
                  placeholder="Time Zone"
                  onChange={(value) => {
                    const selectedTZ = tzOptions.find(
                      (item) => item.timezone === value
                    );
                    setScheduleInformation({
                      ...scheduleInformation,
                      utc: selectedTZ.utc,
                      name: selectedTZ.timezone,
                    });

                    setSequenceScheduled(true);
                  }}
                  options={tzOptions.sort((a, b) =>
                    a.order < b.order ? -1 : 1
                  )}
                />
              </div>
              <div className="flex justify-center items-center">
                <Calendar
                  minDate={
                    new Date(
                      moment()
                        .utcOffset(getOffSet(scheduleInformation?.utc))
                        .format('YYYY-MM-DDTHH:mm:ss')
                    )
                  }
                  date={
                    scheduleInformation?.triggerAt?.split('T')[0] || new Date()
                  }
                  onChange={(value) => {
                    const newDate = dayjs(value).format('YYYY-MM-DD');
                    let currTriggerAt =
                      scheduleInformation?.triggerAt?.split('T') || [];
                    currTriggerAt[0] = newDate;
                    setScheduleInformation({
                      ...scheduleInformation,
                      triggerAt: currTriggerAt.join('T'),
                    });

                    setSequenceScheduled(true);
                  }}
                />
              </div>
            </div>
          </div>
          <div className="w-full flex items-center justify-between px-5 py-3">
            <Button
              onClick={() => {
                setSequenceScheduled(true);
                setScheduleInformation({
                  ...scheduleInformation,
                  isTriggeredNow: true,
                });
                closeScheduleDate();
              }}
              icon={<LeftOutlined />}
            >
              Start Immediately
            </Button>
            <Button
              onClick={() => {
                setSequenceScheduled(true);
                setScheduleInformation({
                  ...scheduleInformation,
                  isTriggeredNow: false,
                });
                notification.success({
                  description: `Sequence scheduled at ${dayjs(scheduleInformation?.triggerAt).format('HH:mm on DD. MMM YYYY')}`,
                });
                closeScheduleDate();
              }}
              type="primary"
              icon={<CheckOutlined />}
            >
              Schedule
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
export default SequenceScheduleDate;
