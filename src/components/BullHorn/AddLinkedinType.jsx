'use client';
import {
  CommentOutlined,
  EyeOutlined,
  LeftOutlined,
  LikeOutlined,
  LinkedinOutlined,
  LinkOutlined,
  MailOutlined,
  MessageOutlined,
  NotificationFilled,
  NotificationOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { But<PERSON>, Tooltip } from 'antd';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { ADD_STEP_TYPE, LINKINEDIN_REQUEST_TYPE } from './EmailtriggerStep';
import { AnimatedBeam } from '../MagicUI/AnimatedBeam';

const AddLinkedinTypeButton = ({ handleAddNewStep, index }) => {
  // Separate linkedin step
  const [toggleActive, setToggleActive] = useState(false);
  const [triggerRenderSvg, setTriggerRenderSvg] = useState(false);
  const containerRef = useRef(null);
  const linkedinBtnRef = useRef(null);
  const invitationRef = useRef(null);
  const messageRef = useRef(null);
  const inMailRef = useRef(null);

  const toggle = () => {
    setToggleActive(!toggleActive);
  };

  useEffect(() => {
    if (!toggleActive) return;
    setTimeout(() => setTriggerRenderSvg(true), 500);

    return () => setTriggerRenderSvg(false);
  }, [toggleActive]);

  return (
    <div ref={containerRef}>
      <div
        ref={linkedinBtnRef}
        className={clsx('menu-linkedin', toggleActive && 'active')}
      >
        {!toggleActive ? (
          <Button
            className={clsx(
              'w-40 flex items-center justify-center bg-white z-50',
              toggleActive && 'text-base font-semibold !w-52'
            )}
            type={toggleActive ? 'text' : 'default'}
            onClick={toggle}
          >
            <span
              className={clsx(
                '!flex items-center gap-2',
                toggleActive &&
                  'text-base font-semibold !px-28 py-1 rounded-md border '
              )}
            >
              <LinkedinOutlined
                className={clsx(
                  'text-[#0288d1] transform-btn',
                  toggleActive && 'text-lg'
                )}
              />
              <span style={toggleActive ? { letterSpacing: '2px' } : null}>
                LinkedIn
              </span>
            </span>
          </Button>
        ) : (
          <Button
            className={
              'text-base font-semibold absolute -top-[3.5rem] -left-1 z-20 animated animate-fadeintopleft'
            }
            type={'text'}
            icon={<LeftOutlined />}
            onClick={toggle}
          ></Button>
        )}
        {toggleActive && (
          <div className="absolute -top-4 left-0 w-full h-full grid grid-cols-2 gap-3 p-3 z-0">
            <Tooltip title="Add a LinkedIn Invitation">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.INVITATION
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <MailOutlined className="text-base mr-2" />
                  {toggleActive && <span>Invitation</span>}
                </div>
              </a>
            </Tooltip>

            <Tooltip title="Add a LinkedIn InMail">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.INMAIL
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <CommentOutlined className="text-base mr-2" />
                  {toggleActive && <span>InMail</span>}
                </div>
              </a>
            </Tooltip>

            <Tooltip title="Like random any post.">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.REACT_POST
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <LikeOutlined className="text-base mr-2" />
                  {toggleActive && <span>Like</span>}
                </div>
              </a>
            </Tooltip>

            <Tooltip title="Add a LinkedIn Message">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.NORMAL_MESSAGE
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <MessageOutlined className="text-base mr-2" />
                  {toggleActive && <span>Message</span>}
                </div>
              </a>
            </Tooltip>

            <Tooltip title="Follow linkedin contact">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.FOLLOW
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <NotificationOutlined className="text-base mr-2" />
                  {toggleActive && <span>Follow</span>}
                </div>
              </a>
            </Tooltip>

            <Tooltip title="View user's profile">
              <a
                className="animated animate-spinnergrow"
                onClick={() =>
                  handleAddNewStep(
                    index === 'first' ? 0 : index + 1,
                    ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST,
                    null,
                    LINKINEDIN_REQUEST_TYPE.VIEW_PROFILE
                  )
                }
              >
                <div className="flex items-center px-2 py-1 border rounded-md justify-center font-medium">
                  <EyeOutlined className="text-base mr-2" />
                  {toggleActive && <span>View Profile</span>}
                </div>
              </a>
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddLinkedinTypeButton;
