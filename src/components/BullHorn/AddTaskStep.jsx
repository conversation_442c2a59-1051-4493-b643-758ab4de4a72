import {
  ContactsOutlined,
  DeleteOutlined,
  DownOutlined,
  HeartOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PlusOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Checkbox,
  Form,
  Popconfirm,
  Select,
  Spin,
  Tag,
  Tooltip,
  notification,
} from 'antd';
import { useEffect, useRef, useState } from 'react';
import {
  ADD_STEP_TYPE,
  TASK_ACTION,
  TASK_ACTION_ICONS,
} from './EmailtriggerStep';
import clsx from 'clsx';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from './BullhornSendEmailModal';
import { Controller, useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import {
  VALIDATE_STATUS_COLOR,
  VALIDATE_STATUS_ICON,
} from '../../containers/HotList/HotListTable';
import { arrayUniqueByKey } from '../../utils/common';

const AddTaskStep = ({
  inputNumber,
  inputNumberStep,
  setInputNumberStep,
  index,
  comments,
  actions,
  isEdit,
  setEditingNotes,
  mergeFieldsBasedOnPlace,
  addSavedSteps,
  getGlobalValues,
  endEdittingStep = () => {},
}) => {
  const [taskDescription, setTaskDescription] = useState(comments);
  const [taskAction, setTaskAction] = useState(actions || TASK_ACTION[0].value);

  const [openParticipantModal, setOpenParticipantModal] = useState(false);
  const [sendToList, setSendToList] = useState([]);
  const { control, setValue } = useForm();
  const editor = useRef(null);

  const handelAddTaskData = () => {
    if (sendToList?.length === 0) {
      notification.error({
        message: 'Please add at least one recipient',
      });
      return;
    }
    if (!taskAction) {
      notification.error({
        message: 'Please select task action',
      });
      return;
    }
    const newItem = [...inputNumberStep];
    const index = newItem.findIndex((item) => item.key === inputNumber?.key);
    if (index !== -1) {
      newItem[index].content = taskDescription;
      newItem[index].subject = taskAction;
      newItem[index].type = ADD_STEP_TYPE.TASK;
      newItem[index].recipients = [...sendToList];
    }
    isEdit && setEditingNotes(null);
    setInputNumberStep(newItem);
    addSavedSteps(newItem[index]?.key || newItem[index]?.id);
    endEdittingStep()
  };

  const handelChangeSelect = (value) => {
    setTaskAction(value);
  };

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setTaskDescription(data);
  };

  const { options: contactOptions, isLoading: isLoadingContacts } =
    useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const handleChange = (value, options, isSelectAll = false) => {
    const dataSet = options
      ?.map((obj) => ({
        ...obj,
        name: obj?.name || obj?.firstName,
        email: obj.email,
        id: obj.contactId,
      }))
      .filter((item) => item?.name && item?.email);

    setValue(
      'linkedin.participants',
      dataSet?.map((data) => data.email)
    );
    setSendToList([...(dataSet || [])]);
  };

  useEffect(() => {
    if (inputNumber?.recipients?.length > 0) {
      const newRecipients = [...inputNumber?.recipients];
      setSendToList([...newRecipients]);
    }
  }, [inputNumber?.recipients]);

  return (
    <div
      className={clsx(
        'bg-white py-3 px-2 rounded-bl-lg rounded-br-lg shadow-xl mail-content-container w-full',
        isEdit
          ? 'border-2 border-t-0 border-b-[#7CF5FF] border-l-[#7CF5FF] border-r-[#7CF5FF] bg-[#F5F5F5]'
          : 'border border-[#ccc]'
      )}
    >
      <div
        className={clsx(
          'flex items-center flex-col border rounded-md py-3 px-2 w-full gap-2 mb-2',
          'border-[#7CF5FF] border-2'
        )}
      >
        <div className="flex items-center justify-between w-full">
          <Badge showZero count={sendToList?.length || 0}>
            <Button
              type="text"
              className="flex items-center font-medium w-full justify-center"
              onClick={(e) => {
                e.stopPropagation();
                setOpenParticipantModal(!openParticipantModal);
              }}
            >
              <UsergroupAddOutlined className="text-cyan-600 text-xl font-semibold" />
            </Button>
          </Badge>
          <Select
            placeholder="Type of Task"
            className="w-1/3"
            rootClassName="company-status-container"
            defaultValue={
              taskAction
                ? {
                    label: taskAction,
                    value: taskAction,
                  }
                : TASK_ACTION[0]
            }
            options={TASK_ACTION}
            onChange={handelChangeSelect}
            optionRender={(opt) => {
              const { data } = opt;
              return (
                <div className="flex items-center gap-2">
                  {TASK_ACTION_ICONS[data?.value]}
                  <span>{data?.label}</span>
                </div>
              );
            }}
          />
          <Tooltip title="Delete Step">
            <Popconfirm
              title="Delete the Step"
              description="Are you sure to delete this step?"
              onConfirm={(e) => {
                e.stopPropagation();
                // setNumberStep(numberStep - 1);
                const newArr = inputNumberStep.filter(
                  (item, itemIndex) => itemIndex !== index
                );
                setInputNumberStep([...newArr]);
                setEditingNotes(null)
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="text"
                className="flex items-center font-medium justify-center"
              >
                <DeleteOutlined className="text-red-500 font-semibold text-base" />
              </Button>
            </Popconfirm>
          </Tooltip>
        </div>
        {openParticipantModal && (
          <div className="w-full linkedin-step-participant-container">
            <div className="w-full pb-2">
              <div>
                <div className="flex items-center w-full mt-2">
                  <p>
                    <span className="text-red-600">*</span> Send To ( email )
                  </p>
                </div>
                <div>
                  <Form.Item
                    className="add-contact-container !my-2"
                    name="participants"
                  >
                    <Controller
                      render={({ field }) => (
                        <Select
                          onChange={handleChange}
                          className="w-full"
                          disabled={isLoadingContacts}
                          mode="multiple"
                          placeholder="Please select"
                          notFoundContent={
                            isLoadingContacts ? (
                              <div className="w-full flex justify-center py-4">
                                <Spin size="default" />
                              </div>
                            ) : null
                          }
                          loading={isLoadingContacts}
                          options={[
                            ...(getGlobalValues('participants.sendTo') || []),
                          ]?.map((option) => ({
                            ...option,
                            value:
                              (option?.id || option.email) + '_' + option.name,
                            label: option?.name || option?.email,
                            email: option?.email,
                            phone: option?.phone,
                            address: option?.address,
                            status: option?.status,
                            occupation: option?.occupation,
                            name: option?.name,
                            contactId: option?.id,
                            disabled:
                              (!option?.name && !option?.firstName) ||
                              !option?.email,
                          }))}
                          value={arrayUniqueByKey([...sendToList], 'email')}
                          optionLabelProp="name"
                          tagRender={(props) => {
                            const { label, closable, onClose, value } = props;
                            const options = contactOptions?.concat(
                              getGlobalValues('sendMail.listEmailSend') || []
                            );
                            const optionSelect = options.find(
                              (item) =>
                                (item?.id || item.email) + '_' + item.name ===
                                value
                            );
                            return (
                              <Tag
                                className="mb-1 max-w-[20rem]"
                                closable={closable}
                                onClose={onClose}
                                style={{ marginRight: 3 }}
                                color={
                                  typeof value === 'number'
                                    ? '#f50'
                                    : value?.includes('CONTACTLIST')
                                      ? 'cyan'
                                      : value?.includes('HOTLIST')
                                        ? 'orange'
                                        : optionSelect?.emailStatus
                                          ? VALIDATE_STATUS_COLOR[
                                              optionSelect?.emailStatus
                                            ]
                                          : undefined
                                }
                                title={label || value}
                              >
                                {label || value}
                              </Tag>
                            );
                          }}
                          optionRender={(opt) => {
                            const { data: option } = opt;
                            const lastOption =
                              [...contactOptions][contactOptions?.length - 1] ||
                              {};
                            const isLastOption =
                              lastOption && lastOption?.id === option?.id;
                            return (
                              option?.name && (
                                <div>
                                  <div className="grid">
                                    <div className="flex items-center gap-1">
                                      <span className="text-base">
                                        {option.name || option?.label}
                                      </span>
                                      {option?.emailStatus ? (
                                        <Tag
                                          icon={
                                            VALIDATE_STATUS_ICON[
                                              option?.emailStatus
                                            ]
                                          }
                                          color={
                                            VALIDATE_STATUS_COLOR[
                                              option?.emailStatus
                                            ]
                                          }
                                        >
                                          {option?.emailStatus}
                                        </Tag>
                                      ) : (
                                        <Tag icon={<InfoCircleOutlined />}>
                                          Undefined
                                        </Tag>
                                      )}
                                    </div>
                                    <div className="contact-details">
                                      {option?.email && (
                                        <div className="flex">
                                          <span className="text-gray-700 text-xs min-w-[200px]">
                                            <MailOutlined />
                                            {option.email ? option.email : '-'}
                                          </span>
                                          <span className="text-gray-700 text-xs min-w-[200px]">
                                            <HomeOutlined />
                                            {option.clientCorporation?.name}
                                          </span>
                                        </div>
                                      )}
                                      {option?.address && (
                                        <div className="flex text-gray-700 text-xs font-medium">
                                          <ContactsOutlined />
                                          {option.occupation
                                            ? option.occupation
                                            : '-'}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  {isLastOption && isLoadingContacts && (
                                    <div className="w-full flex justify-center py-4">
                                      <Spin size="default" />
                                    </div>
                                  )}
                                </div>
                              )
                            );
                          }}
                        />
                      )}
                      name="participants"
                      control={control}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div
        style={{
          textAlign: 'left',
        }}
        className="w-full"
      >
        <span className="font-medium">Description</span>
        <div className="w-full mt-2">
          <CKEditor
            ref={editor}
            editor={ClassicEditor}
            config={{
              ...editorConfig,
              toolbar: {
                ...editorConfig.toolbar,
                shouldNotGroupWhenFull: false,
              },
              mergeFields: { ...mergeFieldsBasedOnPlace },
            }}
            data={taskDescription || ''}
            onChange={handleCKEditorChange}
            onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
          />
        </div>
        <div className="mt-5 flex w-full justify-end">
          {/* <Button
            onClick={(e) => {
              e.stopPropagation();
              // setNumberStep(numberStep - 1);
              const newArr = inputNumberStep.filter(
                (item, itemIndex) => itemIndex !== index
              );
              setInputNumberStep([...newArr]);
            }}
          >
            <DeleteOutlined />
          </Button> */}
          <Button onClick={() => handelAddTaskData()}>Save</Button>
        </div>
      </div>
    </div>
  );
};

export default AddTaskStep;
