import _get from 'lodash/get';
import _find from 'lodash/find';

import {  Controller } from 'react-hook-form';
import {
  Input,
  Button,
  Form,
  AutoComplete,
  Modal,
  Spin,
  Table,
  Row,
  Col,
  Space,
  notification,
  Pagination,
  Collapse,
  Tabs,
  Checkbox,
  Empty,
  Dropdown,
  Image,
  Popover,
  Popconfirm,
} from 'antd';
import {
  searchBullhorn,
  deleteBullhornContact,
} from '../../services/bullhorn';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  CheckOutlined,
  MailOutlined,
  PhoneOutlined,
  EditOutlined,
  LinkedinOutlined,
  FacebookOutlined,
  TwitterOutlined,
  LinkOutlined,
  SearchOutlined,
  CopyOutlined,
  CaretDownOutlined,
  MenuUnfoldOutlined,
  SendOutlined,
  SmallDashOutlined,
  DownOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import {
  getListEmployee,
} from '../../services/employee';

import { getDetailCompanyById,  } from '../../services/companyFinder';
import { Paper, TableContainer } from '@mui/material';
import logo from '/logo_bull.webp';
import ExistingContactItems from './ExistingContactItems';
import { validListEmail } from '../../services/emailFinder';
import { COMMON_STRINGS } from '../../constants/common.constant';

const BullHornAddContact = ({
  functionContactClient,
  handleSubmit,
  control,
  setValue,
  getValues,
  job,
  isModalStaffOpen,
  listEmail,
  setListEmail,
  watch
}) => {
  const [isContactActionLoading, setContactActionLoading] = useState(false);
  const [isAddContactFormBulk, setIsAddContactFormBulk] = useState(false);
  const [listEmployee, setListEmployee] = useState([]);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [listEmployeePagination, setListEmployeePagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });

  const handleValidEmail = async () => {
    setContactActionLoading(true);
    const listEmailToSave = functionContactClient.contactOptions
      .filter((item) => {
        return listEmail?.includes(item?.id);
      })
      .map((item) => item?.email);
    const { data } = await validListEmail({
      emails: listEmailToSave,
    });
    setContactActionLoading(false);
    setListEmailChecked(data?.result);
  };

  const handleGotoWebsite = (url) => {
    window.open(url, '_blank');
  };

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setListEmail(newSelectedRowKeys);
  };

  const rowTableExistingMailsSelection = {
    selectedRowKeys: listEmail,
    onChange: onSelectChange,
  };

  const handleDetailCompany = async (record) => {
    setShowDetailCompany(true);
    setDetailCompany(record);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.id,
      });
      setDetailOrganization(data?.organization);
    }
  };

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleAddContact = async (fullName, dataContact) => {
    setIsLoading(true);
    await handleSetDataContact(dataContact);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      dataContact?.email
    );
    if (data?.result.length === 0) {
      setIsLoading(false);
      setIsAddContactForm(true);
    } else {
      const isDataExist = listAddContactSelected?.some(
        (item) => item?.id === data?.result[0].id
      );
      if (!isDataExist) {
        setListAddContactSelected([
          ...listAddContactSelected,
          { ...data?.result[0] },
        ]);
      }
      functionContactClient?.handleContactSearch(data?.result[0].name, '');
      setIsLoading(false);
      setFlagEditContact(false);
      setIsAddContactForm(false);
      notification.success({
        message: `This contact already existed on Bullhorn`,
      });
    }
  };

  // ADD DATA 
  

  const handleGenderSupportBar = (record, name = null) => {
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                      }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                        setValue('email', record?.email);
                        setValue('sendMail.mailStepParentMailTo', [
                          { email: record?.email, name: record?.name },
                        ]);
                        setValue('optionContactSelect', record);
                        setOpenSendEmailContact(true);
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <div>
                      {record?.email}{' '}
                      <CopyOutlined
                        style={{ marginLeft: '10px' }}
                        onClick={() => {
                          navigator.clipboard.writeText(record?.email),
                            notification.success({
                              message: 'Copy To Clipboard success',
                            });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => setOpenExistingUserGroup(true)}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const filterAddContact = [
    {
      key: '1',
      label: (
        <div className="inline-grid">
          <span>Company</span>
          {watch('companyFinderId') !== '' && watch('companyFinderId') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('companyFinder')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Company Name" name="companyFinder">
          <Controller
            render={({ field }) => (
              <AutoComplete
                style={{ width: '250px' }}
                {...field}
                options={companyListPeople.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid p-2">
                      <div className="flex justify-between">
                        <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                          {option.name}
                          <br />
                          <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                            {option.domain || '-'}
                          </span>
                        </span>
                        <img
                          className="absolute right-3"
                          src={option?.logo_url ? `${option?.logo_url}` : ''}
                          width={50}
                          height={50}
                          alt="Logo"
                        />
                      </div>
                    </div>
                  ),
                }))}
                onSearch={(value) => {
                  setValue('companyFinder', value);
                  setValue('companyFinderSelect', null);
                  setValue('companyFinderId', null);
                }}
                onSelect={async (selectedCompanyId) => {
                  const selectedCompany = companyListPeople.find(
                    (ao) => ao.id == selectedCompanyId
                  );
                  setValue('companyFinder', selectedCompany.name);
                  setValue('companyFinderSelect', selectedCompanyId);
                  setValue('companyFinderId', selectedCompanyId);
                }}
              >
                <Input />
              </AutoComplete>
            )}
            name="companyFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
  ];

  const handleDetailContact = async (record) => {
    setShowDetailContact(true);
    if (record) {
      const { data } = await getDetailCompanyById({
        organizationId: record?.organization?.id,
      });
      setDetailOrganization(data?.organization);
      setDetailContactStaff(record);
    }
  };

  const columnsExistingContacs = [
    // {
    //   title: '',
    //   dataIndex: 'index',
    //   key: 'index',
    //   render: (text, record) => (
    //     <>
    //       <Checkbox
    //         checked={listEmail.includes(record?.email)}
    //         onChange={() => changeListEmail(record?.email)}
    //       ></Checkbox>
    //     </>
    //   ),
    // },
    {
      title: 'Name'.toUpperCase(),
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="font-semibold">{text}</span>,
    },
    {
      title: 'Email'.toUpperCase(),
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (text, record) => (
        <ExistingContactItems
          record={record}
          listEmailChecked={listEmailChecked}
        />
      ),
    },
    {
      title: 'Occupation'.toUpperCase(),
      dataIndex: 'occupation',
      key: 'occupation',
    },
    {
      title: 'Phone'.toUpperCase(),
      dataIndex: 'phone',
      width: '15%',
      key: 'phone',
    },
    {
      title: 'Status'.toUpperCase(),
      width: '10%',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: 'Action'.toUpperCase(),
      key: 'action',
      align: 'center',
      render: (text, record) => (
        <Space size="small">
          <Popconfirm
            title="Delete the contact"
            description="Are you sure to delete this contact?"
            onConfirm={async () => {
              const { id } = record;
              try {
                const res = await deleteBullhornContact({
                  id,
                  entity: 'ClientContact',
                });
                const newContactOptions = [
                  ...functionContactClient.contactOptions.filter(
                    (contact) => contact?.id !== id
                  ),
                ];
                functionContactClient.contactSetOptions([...newContactOptions]);
                notification.success({
                  description: 'Contact deleted!',
                });
              } catch (error) {
                console.log('error delete contact: ', error);
                notification.error({
                  description: 'Network error! Try again later!',
                });
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button className="text-red-400 border-red-300">
              <DeleteOutlined />
            </Button>
          </Popconfirm>
          <Button
            disabled={isLoading}
            onClick={async () => {
              handleEditContact(record.name, record.email).finally(() => {
                setIsAddContactForm(true);
                setFlagEditContact(true);
                setIsLoading(false);
              });
            }}
          >
            <EditOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listSelectSection, setListSelectSection] = useState([]);

  const handleClickSignal = (id) => {
    if (!selectedRowKeys.includes(id)) {
      setSelectedRowKeys([...selectedRowKeys, id]);
    } else {
      setSelectedRowKeys(selectedRowKeys.filter((item) => item !== id));
    }
  };

  const handleCheckAll = () => {
    if (!listSelectSection.includes(listEmployeePagination.page)) {
      setListSelectSection([...listSelectSection, listEmployeePagination.page]);
      const data = listEmployee.map((item) => item.id);
      setSelectedRowKeys([...new Set([...selectedRowKeys, ...data])]);
    } else {
      const data = listEmployee.map((item) => item.id);
      const updatedArray = selectedRowKeys.filter((id) => !data.includes(id));
      setSelectedRowKeys(updatedArray);
      setListSelectSection(
        listSelectSection.filter((item) => item !== listEmployeePagination.page)
      );
    }
  };

  const handleAccordionChangePeople = (keys) => {
    setValue('keyAcordionPeople', keys[0]);
    if (keys[0] === '10') {
      refetchListSignals();
    }
  };

  const handleGetMetaMode = async(body) => {
    const {data} = await getListEmployee({...body, metaMode: "metadata_mode"})

    return data;
  }

  const handleFindEmails = async ({ page = 1 }) => {
    try {
      const currentType = watch('keyAcordionPeople') || 0;
      setIsLoadingEmployee(true);
      let resultData = [];
      let bodyToSearch = null
      if (currentType !== '6') {
        const locations = getValues().locationFinder
          ? getValues().locationFinder.map((item) => item.label)
          : [];
        const personTitles = getValues().titleFinder
          ? getValues().titleFinder.map((item) => item.label)
          : [];
        const employeeRanges = getValues().employeeFinder
          ? getValues().employeeFinder.map((item) => item.label)
          : [];
        const industryTagIds = getValues().industryFinder
          ? getValues().industryFinder.map((item) => item.value)
          : [];
        const contactEmailStatus = getValues().contactEmailStatus
          ? getValues().contactEmailStatus
          : [];
        const contactEmailOpened = getValues().emailOpenedStatus ?? null;
        const contactEmailOpenedAtLeast =
          getValues().contactEmailOpenedTime ?? null;
        const contactEmailOpenedAtDateRangeMin =
          getValues().contactEmailOpenedTimeMin &&
          getValues().contactEmailOpenedTimeMin !== ''
            ? getValues().contactEmailOpenedTimeMin
            : null;
        const contactEmailOpenedAtDateRangeMax =
          getValues().contactEmailOpenedTimeMax &&
          getValues().contactEmailOpenedTimeMax !== ''
            ? getValues().contactEmailOpenedTimeMax
            : null;
        const intentStrengths = getValues().contactBuyingIntentScore
          ? getValues().contactBuyingIntentScore
          : [];
        const intentIds = getValues().contactBuyingIntentIds
          ? getValues().contactBuyingIntentIds
          : [];
        const searchSignalIds = getValues().searchSignalIds
          ? getValues().searchSignalIds
          : [];
        const recommendationScoresMinTranche = getValues().contactMinimumScore
          ? getValues().contactMinimumScore
          : null;
        const containOneKeywords = watch('includeKeywordPeople')
          ? watch('includeKeywordPeople').map((item) => item.label)
          : [];
        const containAllKeyWords = watch('includeAllKeywordPeople')
          ? watch('includeAllKeywordPeople').map((item) => item.label)
          : [];
        const excludeKeyWords = watch('excludeKeywordsPeople')
          ? watch('excludeKeywordsPeople').map((item) => item.label)
          : [];
        const organizationLatestFundingStageCd =
          getValues('fundingStatusItem') ?? [];
        const currentlyUsingAnyOfTechnologyUids =
          getValues('listTechnologies') ?? [];
        const notExistFields =
          getValues('revenueStatus') === 'is_un_known'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const existFields =
          getValues('revenueStatus') === 'is_know'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const organizationJobLocations = getValues('contactJobLocated') ?? [];
        const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
        const personName = getValues('nameFinderText') ?? null;
        const organizationNumJobsRangeMin =
          getValues('organizationNumJobsRangeMin') ?? null;
        const organizationNumJobsRangeMax =
          getValues('organizationNumJobsRangeMax') ?? null;
        const organizationJobPostedAtRangeMin =
          getValues('organizationJobPostedAtRangeMin') ?? null;
        const organizationJobPostedAtRangeMax =
          getValues('organizationJobPostedAtRangeMax') ?? null;
        const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
        const totalFundingRangeMin = getValues('fundingMin') ?? null;
        const totalFundingRangeMax = getValues('fundingMax') ?? null;
        bodyToSearch = {
          organizationId: watch('companyFinderId'),
          locations,
          personTitles,
          employeeRanges,
          industryTagIds,
          page,
          searchText: watch('searchPeople'),
          containOneKeywords,
          containAllKeyWords,
          excludeKeyWords,
          contactEmailStatus,
          contactEmailOpened,
          contactEmailOpenedAtLeast,
          contactEmailOpenedAtDateRangeMin,
          contactEmailOpenedAtDateRangeMax,
          intentStrengths,
          intentIds,
          searchSignalIds,
          recommendationScoresMinTranche,
          currentlyUsingAnyOfTechnologyUids,
          existFields,
          notExistFields,
          organizationTradingStatus,
          organizationLatestFundingStageCd,
          totalFundingRangeMax,
          totalFundingRangeMin,
          personName,
          organizationJobLocations,
          qOrganizationJobTitles,
          organizationNumJobsRangeMin,
          organizationNumJobsRangeMax,
          organizationJobPostedAtRangeMin,
          organizationJobPostedAtRangeMax,
        }
        const { data } = await getListEmployee(bodyToSearch);
        resultData = data;
      } else if (currentType === '6') {
        const { data } = await getListEmployee({
          organizationId: watch('companyFinderId'),
          page,
        });
        resultData = data ?? null;
        const people = resultData?.result?.people || [];
        const contacts = resultData?.result?.contacts || [];
        setListEmployeeCompany([...people, ...contacts]);
        setListEmployeePaginationCompany(resultData?.result?.pagination);
        setIsLoadingEmployee(false);
        return;
      }
      if (resultData.length === 0) return dataEmployeeNotFound();
      const people = resultData?.result?.people || [];
      const contacts = resultData?.result?.contacts || [];
      const listData = [...people, ...contacts];
      setListEmployee(listData);
      setIsLoadingEmployee(false);
      const dataPagination = await handleGetMetaMode(bodyToSearch)
      setListEmployeePagination(dataPagination?.result?.pagination || resultData?.result?.pagination);
      const listOrgIds = listData
        ?.map((item) => item?.organization_id)
        .filter((id) => id != null);

      // const uniqueOrgIds = [...new Set(listOrgIds)];
      // const dataOrg = await getOrganizationsSnippet({
      //   ids: uniqueOrgIds,
      // });

      // setListCurrentOrg(dataOrg.data.organizations);
    } catch (err) {
      console.log(err)
      setListEmployee([]);
      setListEmployeePagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingEmployee(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const handlePaginationListEmployee = (page) => {
    handleFindEmails({ page: page });
  };


  const columnsPeople = [
    {
      title: (
        <>
          <Checkbox
            disabled={listEmployee.length == 0}
            checked={listSelectSection.includes(listEmployeePagination.page)}
            onChange={() => handleCheckAll()}
            indeterminate={
              selectedRowKeys.length > 0 &&
              selectedRowKeys.length < listEmployee.length &&
              listEmployee
                .map((item) => item.id)
                .filter((element) => selectedRowKeys.includes(element)).length >
                0
            }
          ></Checkbox>
        </>
      ),
      dataIndex: 'checkbox',
      key: 'checkbox',
      render: (allowWrite, record) => (
        <div
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <>
            <Checkbox
              checked={selectedRowKeys.includes(record.id)}
              onChange={(e) => {
                e.stopPropagation();
                handleClickSignal(record?.id);
              }}
            ></Checkbox>
          </>
        </div>
      ),
    },
    {
      title: COMMON_STRINGS.FULL_NAME,
      dataIndex: 'name',
      key: 'name',
      render: (allowWrite, record) => (
        <>
          <Row gutter={16}>
            <p className="font-semibold mr-2">{record?.name}</p>
            {record?.linkedin_url && (
              <Row gutter={16}>
                <Col>
                  <LinkedinOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGotoWebsite(record?.linkedin_url);
                    }}
                    title={`Access to linkedin account`}
                    className="cursor-pointer text-[#0288d1]"
                  />
                </Col>
              </Row>
            )}
          </Row>
        </>
      ),
    },
    {
      title: COMMON_STRINGS.JOB_TITLE,
      dataIndex: 'title',
      key: 'title',
      width: '25%',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <p>{record?.title}</p>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.COMPANY,
      dataIndex: 'organization.name',
      key: 'organization.name',
      render: (allowRead, record) => (
        <Row gutter={16}>
          <Col>
            <img
              src={
                record.organization?.logo_url
                  ? `${record.organization?.logo_url}`
                  : ''
              }
              width={50}
              height={50}
              alt="Logo"
            />
          </Col>
          <Col>
            <Row>
              <p
                onClick={async (e) => {
                  e.stopPropagation();
                  handleDetailCompany(record?.organization);
                }}
                className="font-semibold cursor-pointer hover:text-blue-700"
              >
                {record?.organization?.name}
              </p>
            </Row>
            <Row className="flex gap-2">
              <Col>
                <LinkOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.website_url);
                  }}
                  className="cursor-pointer text-gray-600 hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <LinkedinOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.linkedin_url);
                  }}
                  className="cursor-pointer text-[#0288d1] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <FacebookOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.facebook_url);
                  }}
                  className="cursor-pointer text-[#3f51b5] hover:text-[#0a66c2]"
                />
              </Col>
              <Col>
                <TwitterOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGotoWebsite(record?.organization?.twitter_url);
                  }}
                  className="cursor-pointer text-[#03a9f4] hover:text-[#0a66c2]"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      ),
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (allowRead, record) => (
        <div className="flex gap-2">
          {record?.email &&
          record?.email !== '<EMAIL>' ? (
            <>{handleGenderSupportBar(record)}</>
          ) : listDetailEmployee.some(
              (item) =>
                item.person_id === record.id ||
                item.person_id === record.person_id
            ) ? (
            listDetailEmployee
              .filter(
                (item) =>
                  item.person_id === record.id ||
                  item.person_id === record.person_id
              )
              .map((item, index) => (
                <>{handleGenderSupportBar(item, record?.name)}</>
              ))
          ) : (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleGetDetailEmployee(record.id || record.person_id);
              }}
              type="primary"
              icon={<MailOutlined />}
            >
              Access Email
            </Button>
          )}
        </div>
      ),
    },
  ];

  const itemsTableEmailFinder = [
    {
      key: '1',
      label: 'Existing Contacts',
      children: (
        <>
          <Row>
            <Col flex="100%">
              <Row gutter={16}>
                <div className="pb-4">
                  <Button
                    loading={isContactActionLoading}
                    disabled={listEmail?.length === 0}
                    onClick={handleValidEmail}
                    type="primary"
                    className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                  >
                    <p className="Montserrat">
                      {`Validate ${listEmail?.length} Email(s)`}
                    </p>
                    <CheckOutlined />
                  </Button>
                </div>
                <TableContainer
                  className="search-table-new-design-container"
                  component={Paper}
                  style={{ maxHeight: '600px', overflowY: 'auto' }}
                  onScroll={(e) => {
                    functionContactClient.handleContactScroll(
                      e,
                      'ClientContact',
                      getValues().companyId
                    );
                  }}
                >
                  <Table
                    rowKey={(record) => record.id}
                    rowSelection={rowTableExistingMailsSelection}
                    columns={columnsExistingContacs}
                    dataSource={functionContactClient.contactOptions || []}
                    locale={{
                      emptyText: (
                        <Empty
                          description="No contacts found"
                          className="w-full"
                        />
                      ),
                    }}
                    pagination={false}
                    footer={() =>
                      functionContactClient.isLoadingContacts ? (
                        <Spin className="w-full mx-auto" />
                      ) : (
                        ''
                      )
                    }
                  />
                </TableContainer>
              </Row>
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: '2',
      label: 'People',
      children: (
        <div>
          <div style={{ float: 'right' }}>
            {selectedRowKeys?.length > 0 && (
              <div>
                <Dropdown
                  className="mb-4 animated fadeInDownBig"
                  placement="bottom"
                  arrow
                  menu={{
                    items: [
                      {
                        key: 'delete-searchs',
                        label: (
                          <a
                            className="Montserrat flex gap-2 items-center py-2"
                            onClick={(e) => {
                              e.preventDefault();
                              setIsAddContactFormBulk(true);
                            }}
                          >
                            <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
                          </a>
                        ),
                      },
                    ],
                  }}
                >
                  <Space>
                    <Button
                      type="primary"
                      className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
                    >
                      <p className="Montserrat">
                        {`${selectedRowKeys?.length} Selected`}
                      </p>
                      <DownOutlined />
                    </Button>
                  </Space>
                </Dropdown>
              </div>
            )}
          </div>
          <div style={{ clear: 'both' }}></div>
          <Row>
            <Col flex="300px">
              <Row gutter={16}>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit()}
                >
                  <Col className="w-full mr-4">
                    <Form.Item
                      label="Search"
                      name="searchPeople"
                      className="mb-2 mt-2"
                    >
                      <Controller
                        render={({ field }) => (
                          <Input
                            prefix={<SearchOutlined />}
                            {...field}
                            placeholder="Search People ..."
                          />
                        )}
                        name="searchPeople"
                        control={control}
                      />
                    </Form.Item>
                  </Col>
                </Form>
                <Form
                  className="w-full pr-4"
                  layout="vertical"
                  onFinish={handleSubmit()}
                >
                  <Col className="w-full customize-collapse">
                    <Form.Item label="Filter">
                      <Collapse
                        accordion
                        items={filterAddContact}
                        onChange={handleAccordionChangePeople}
                      />
                    </Form.Item>
                  </Col>
                  <Col className="w-full mr-4">
                    <Button
                      type="primary"
                      //   disabled={isLoading}
                      //   loading={isLoading}
                      htmlType="submit"
                      className={`flex ml-auto mt-0  `}
                    >
                      {/* <span className={isLoading ? 'text-black' : 'text-white'}>
                        Search
                      </span> */}
                      <span className={'text-white'}>Search</span>
                    </Button>
                  </Col>
                </Form>
              </Row>
            </Col>
            <Col
              flex="auto"
              className="w-2/3 search-table-new-design-container"
            >
              <Table
                // scroll={{ x: true, y: 360 }}
                rowKey={(record) => record.id}
                // loading={isLoadingEmployee}
                pagination={false}
                columns={columnsPeople}
                dataSource={listEmployee}
                onRow={(record, rowIndex) => {
                  return {
                    onClick: () => {
                      handleDetailContact(record);
                    },
                    style: { cursor: 'pointer' },
                  };
                }}
                rowClassName="custom-row"
                className="custom-table"
                // rowSelection={rowSelection}
              />
              <Pagination
                className="mt-3"
                defaultCurrent={listEmployeePagination.page}
                total={listEmployeePagination.total_entries}
                showSizeChanger={false}
                onChange={handlePaginationListEmployee}
              />
            </Col>
          </Row>
        </div>
      ),
    },
    // {
    //   key: '3',
    //   label: 'Companies',
    //   children: (
    //     <>
    //       <Row>
    //         <Col flex="300px">
    //           <Row gutter={16}>
    //             <Form
    //               className="w-full pr-4"
    //               layout="vertical"
    //               onFinish={handleSubmit(handleSubmitCompanyFind)}
    //             >
    //               <Col className="w-full mr-4">
    //                 <Form.Item
    //                   label="Search"
    //                   name="searchCompany"
    //                   className="mb-2 mt-2"
    //                 >
    //                   <Controller
    //                     render={({ field }) => (
    //                       <Input
    //                         prefix={<SearchOutlined />}
    //                         {...field}
    //                         placeholder="Search Company ..."
    //                       />
    //                     )}
    //                     name="searchCompany"
    //                     control={control}
    //                   />
    //                 </Form.Item>
    //               </Col>
    //             </Form>
    //             <Form
    //               className="w-full pr-4"
    //               layout="vertical"
    //               onFinish={handleSubmit(handleFindCompany)}
    //             >
    //               <Col className="w-full">
    //                 <Form.Item label="Filter">
    //                   <Collapse
    //                     accordion
    //                     items={filterCompany}
    //                     onChange={handleAccordionChangeCompany}
    //                   />
    //                 </Form.Item>
    //               </Col>
    //               <Col className="w-full mr-4">
    //                 <Button
    //                   type="primary"
    //                   disabled={isLoading}
    //                   loading={isLoading}
    //                   htmlType="submit"
    //                   className={`flex ml-auto mt-0  `}
    //                 >
    //                   <span className={isLoading ? 'text-black' : 'text-white'}>
    //                     Search
    //                   </span>
    //                 </Button>
    //               </Col>
    //             </Form>
    //           </Row>
    //         </Col>
    //         {isDetailEmployeCompany ? (
    //           <Col
    //             flex="auto"
    //             className="w-2/3 search-table-new-design-container"
    //           >
    //             <Table
    //               // scroll={{ x: true, y: 360 }}
    //               loading={isLoadingEmployee}
    //               pagination={false}
    //               columns={columnsPeople}
    //               dataSource={listEmployeeCompany}
    //             />
    //             <Pagination
    //               className="mt-3"
    //               defaultCurrent={listEmployeePaginationCompany.page}
    //               total={listEmployeePaginationCompany.total_entries}
    //               showSizeChanger={false}
    //               onChange={handlePaginationListEmployee}
    //             />
    //           </Col>
    //         ) : (
    //           <Col
    //             flex="auto"
    //             className="w-2/3 search-table-new-design-container"
    //           >
    //             <Table
    //               // scroll={{ x: true, y: 360 }}
    //               loading={isLoadingCompanies}
    //               pagination={false}
    //               columns={columnsCompany}
    //               dataSource={listCompanies}
    //               onRow={(record, rowIndex) => {
    //                 return {
    //                   onClick: () => {
    //                     handleDetailCompany(record);
    //                   },
    //                   style: { cursor: 'pointer' },
    //                 };
    //               }}
    //             />
    //             <Pagination
    //               className="mt-3"
    //               defaultCurrent={listCompaniesPagination.page}
    //               total={listCompaniesPagination.total_entries}
    //               showSizeChanger={false}
    //               onChange={handlePaginationListCompany}
    //             />
    //           </Col>
    //         )}
    //       </Row>
    //     </>
    //   ),
    // },
  ];

  return (
    <>
      <Modal
        width={1600}
        // style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
        // title="List Staff"
        className="top-10"
        rootClassName="contact-modal-container"
        open={isModalStaffOpen}
        onCancel={() => {
          setIsModalStaffOpen(false);
          setIsLoadingEmployee(false);
          setValue('searchCompany', '');
          setValue('searchPeople', '');
          resetAllPeople();
          resetAllCompany();
        }}
        footer={false}
      >
        <Tabs
          defaultActiveKey="1"
          onChange={(e) => handleChangeTab(e)}
          items={itemsTableEmailFinder}
        />
        <Button
          className="flex ml-auto mt-3"
          htmlType="button"
          onClick={() => {
            setIsModalStaffOpen(false);
            setIsLoadingEmployee(false);
            setValue('searchCompany', '');
            setValue('searchPeople', '');
            resetAllPeople();
            resetAllCompany();
          }}
          type="primary"
        >
          Done
        </Button>
      </Modal>
    </>
  );
};

export default BullHornAddContact;
