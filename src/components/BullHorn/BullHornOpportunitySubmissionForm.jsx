/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable react/no-unknown-property */
/* eslint-disable react/jsx-key */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */

import _get from 'lodash/get';
import _find from 'lodash/find';

import { useForm, Controller } from 'react-hook-form';
import {
  Input,
  Switch,
  DatePicker,
  Select,
  Button,
  InputNumber,
  Form,
  Divider,
  AutoComplete,
  Modal,
  Spin,
  Table,
  Row,
  Col,
  Space,
  notification,
  ConfigProvider,
  Radio,
  Card,
  Pagination,
  Collapse,
  Tabs,
  Checkbox,
  List,
  Empty,
  Dropdown,
  Image,
  Popover,
  Flex,
  Typography,
  Tag,
  Segmented,
  Popconfirm,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import {
  searchBullhornData,
  getCounties,
  getCountries,
  searchBullhorn,
  insertB<PERSON><PERSON>,
  upladteBullhorn,
  getSimilarJob,
  deleteBullhornContact,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchAddressWithOpenStreet } from '../../services/googleMap';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  FileTextOutlined,
  BankFilled,
  StarOutlined,
  WalletOutlined,
  SettingOutlined,
  CheckOutlined,
  PlusOutlined,
  MailOutlined,
  CloseCircleOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  ContactsOutlined,
  EditOutlined,
  LinkedinOutlined,
  FacebookOutlined,
  TwitterOutlined,
  LinkOutlined,
  SearchOutlined,
  BankOutlined,
  PoundOutlined,
  CopyOutlined,
  CaretDownOutlined,
  MenuUnfoldOutlined,
  SendOutlined,
  SmallDashOutlined,
  DownOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import {
  employeeFinderSearchTag,
  getLDetailEmployee,
  getListCompany,
  getListEmployee,
  getListCompanies,
  employeeFinderIntentCategory,
  employeeFinderSearchSignals,
  getRevenue,
} from '../../services/employee';

import BullHornJobSubmissionCompany from './BullhornJobSubmissionCompany';
import BullhornSubmissionContact from './BullhornSubmissionContact';
import { getDetailCompanyById, getFacets } from '../../services/companyFinder';
import { Paper, TableContainer } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectConfigForm } from '../../store/common';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import BullhornSendEmail from './BullhornSendEmailModal';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { getEmailConfigInJobBoard } from '../../services/jobs';
import logo from '/logo_bull.webp';
import ModalShowListExitSequence from '../../containers/Sequence/ModalShowListExitSequence';
import ModalListUserGroup from '../../containers/Sequence/ModalListUserGroup';
import ExistingContactItems from './ExistingContactItems';
import { validListEmail } from '../../services/emailFinder';
import deleteIcon from '../../assets/img/icons/delete-icon.png';
import IntentCollap from './IntentCollap';
import { FaRegBuilding, FaRegUser } from 'react-icons/fa';
import { UserGroupIcon } from '../Sidebar/consts';
import BullhornBulkAddContactModal from './BullhornBulkAddContactModal';
import ContactFinderForm from './ContactFinderForm';
import {
  bulkUpdateLeadStatus,
  getStatusLeads,
  LEAD_STATUS_TYPE,
} from '../../services/jobLeadStatuses';
import {
  createNewLeadSheet,
  getLeadSheetsByType,
  LEAD_SHEET_TYPE,
} from '../../services/leadSheet';
import { handleGenerateNotificationBulkAddContact } from '../../helpers/util';
import { COMMON_STRINGS } from '../../constants/common.constant';
import clsx from 'clsx';
import BHCompanySelect from './Common/BHCompanySelect';
import BHContactSelect from './Common/BHContactSelect';

// const { Option } = Select;
const { Meta } = Card;
export const HENLEY = 'Henley';
function BullHornOpportunitySubmissionForm({
  control,
  setValue,
  getValues,
  job,
  handleCloseClient,
  setHandleCloseClient,
  functionCompany,
  handleCloseContact,
  setHandleCloseContact,
  functionContactClient,
  handleOk,
  handleCancel,
  setPlaceId,
  isLoadingAddressDetails,
  handleGetDefaultAddress,
  isSearchCompany,
  setIsSearchCompany,
  jobDetailCompanyId,
  setJobDetailCompanyId,
  setShowCompany,
  showCompany,
  watch,
  isModalVisible,
  setHaveSendJob,
  haveSendJob,
  setIsLockedSendJob,
  isLockedSendJob,
  setCheckBoxStatus,
  checkBoxStatus,
  sendingStatus,
  // setSendingStatus,
  fromManualCreate = false,
  setLoadingSimilar,
  setSimilarList,
  similarList,
  searchData,
  onClose = false,
  defaultDataCompany = false,
}) {
  const configForm = useSelector(selectConfigForm);
  // Custom hooks for search with debounce
  const { handleSubmit } = useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalStaffOpen, setIsModalStaffOpen] = useState(false);
  const [isContactActionLoading, setContactActionLoading] = useState(false);
  const [startCreateContact, setStartCreateContact] = useState(false);

  const [listEmployee, setListEmployee] = useState([]);
  const [listEmployeePagination, setListEmployeePagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingEmployee, setIsLoadingEmployee] = useState(false);
  const [sequenceStatus, setSequenceStatus] = useState(false);
  const [listEmail, setListEmail] = useState([]);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [dataParentIntent, setDataParentIntent] = useState([]);
  const [listDataIntentSetting, setListDataIntentSetting] = useState([]);
  const [dataListTopicTitle, setDataListTopicTitle] = useState([]);
  const [dataTechnologies, setDataTechnologies] = useState([]);
  const [dataIndustryList, setDataIndustryList] = useState([]);
  const [dataEmployeeList, setDataEmployeeList] = useState([]);
  const [listDataRevenue, setListDataRevenue] = useState([]);
  const [listDataFunding, setListDataFunding] = useState([]);
  const [fundingSize, setFundingSize] = useState(true);

  const [listEmployeeCompany, setListEmployeeCompany] = useState([]);
  const [listEmployeePaginationCompany, setListEmployeePaginationCompany] =
    useState({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });

  const [listCompanies, setListCompanies] = useState([]);
  const [listCompaniesPagination, setListCompaniesPagination] = useState({
    page: 0,
    per_page: 0,
    total_entries: 0,
    total_pages: 0,
  });
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isDetailEmployeCompany, setIsDetailEmployeeCompany] = useState(false);

  const [isEditCompany, setIsEditCompany] = useState(false);
  const [isAddCompany, setIsAddCompany] = useState(false);
  const [isAddContact, setIsAddContact] = useState(false);
  const [isAddContactForm, setIsAddContactForm] = useState(false);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [flagDetailContact, setFlagDetailContact] = useState(false);
  const [flagEditContact, setFlagEditContact] = useState(false);
  const [isSubmitForm, setIsSubmitForm] = useState(false);
  const [openExistingSequence, setOpenExistingSequence] = useState(false);
  const [openExistingUserGroup, setOpenExistingUserGroup] = useState(false);

  const [
    debouncedSearchTextCompanyPeople,
    setDebouncedSearchTextCompanyPeople,
  ] = useState('');
  const [debouncedSearchTextCompany, setDebouncedSearchTextCompany] =
    useState('');
  const [debouncedLocationFinderText, setDebouncedLocationFinderText] =
    useState('');
  const [
    debouncedLocationFindCompanyText,
    setDebouncedLocationFindCompanyText,
  ] = useState('');
  const [debouncedSearchTextIndustry, setDebouncedSearchTextIndustry] =
    useState('');
  const [
    debouncedSearchTextIndustryCompany,
    setDebouncedSearchTextIndustryCompany,
  ] = useState('');
  const [debouncedSearchTextTitle, setDebouncedSearchTextTitle] = useState('');
  const [listDetailEmployee, setListDetailEmployee] = useState([]);
  const [showIncludeKeywordPeople, setShowIncludeKeywordPeople] =
    useState(false);
  const [showIncludeAllKeywordPeople, setShowIncludeAllKeywordPeople] =
    useState(false);
  const [showExcludeKeywordsPeople, setShowExcludeKeywordsPeople] =
    useState(false);
  const [showIncludeKeywordCompany, setShowIncludeKeywordCompany] =
    useState(false);
  const [showIncludeAllKeywordCompany, setShowIncludeAllKeywordCompany] =
    useState(false);
  const [showExcludeKeywordsCompany, setShowExcludeKeywordsCompany] =
    useState(false);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  const [openModalSendEmail, setOpenSendEmail] = useState(false);
  const [openModalSendEmailContact, setOpenSendEmailContact] = useState(false);
  const [sendEmailStatus, setSendEmailStatus] = useState(false);
  const [numberStep, setNumberStep] = useState(0);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [emailConfigData, setEmailConfigData] = useState();
  const [showModalAddTopic, setShowModalAddTopic] = useState(false);
  const [dataPersonTitle, setDataPersonTitle] = useState([]);
  const [dataLocation, setDataLocation] = useState([]);
  let searchTimer;

  // Lead Sheet Area
  const [leadSheets, setLeadSheets] = useState([]);
  const [newLeadSheetName, setNewLeadSheetName] = useState('');
  const [leadSheetLoading, setLeadSheetLoading] = useState(true);
  const inputLeadSheetRef = useRef(null);

  const onNewLeadSheetNameChange = (event) => {
    setNewLeadSheetName(event.target.value);
  };

  const addNewLeadSheet = async (e) => {
    if (!newLeadSheetName?.trim()) {
      notification.warning({
        description: 'Sheet Name is required!',
      });
      return;
    }
    e.preventDefault();
    try {
      const payload = {
        name: `OPPORTUNITY: ${newLeadSheetName}`,
        leadStatusType: LEAD_SHEET_TYPE.OPPORTUNITY,
      };
      setLeadSheetLoading(true);
      const { data } = await createNewLeadSheet(payload);

      if (data?.result) {
        setLeadSheets([{ ...data?.result }, ...leadSheets]);
        setNewLeadSheetName('');
      }

      setTimeout(() => {
        inputLeadSheetRef.current?.focus();
      }, 0);
      setLeadSheetLoading(false);
    } catch (error) {
      notification.error({
        description:
          error?.response?.data?.message ||
          'Something went wrong! Try again later',
      });
      console.log('Error in addNewLeadSheet: ', error);
      setLeadSheetLoading(false);
    }
  };

  const onChangeLeadSheet = (value) => {
    setValue('leadSheetId', value);
  };

  const getLeadSheetOptions = async () => {
    try {
      const { data } = await getLeadSheetsByType(LEAD_SHEET_TYPE.OPPORTUNITY);
      if (data?.result?.length > 0) {
        setLeadSheets([...data?.result]);
      }
      setLeadSheetLoading(false);
    } catch (error) {
      console.log('Error in getLeadSheetOptions: ', error);
      setLeadSheetLoading(false);
    }
  };

  useEffect(() => {
    getLeadSheetOptions();
  }, []);

  // End Lead Sheet Area

  const getDataEmail = async () => {
    if (!job?.job_id) return;
    const { data } = await getEmailConfigInJobBoard(job.job_id);
    if (data) {
      const newValueArr = data?.result?.mails?.map((item, index) =>
        index === data?.result?.mails.length - 1
          ? { ...item, delay: index + 1 }
          : item
      );
      const newData = newValueArr?.slice(1).map((item, index) => {
        return {
          delay: item.delay,
          subject: item.subject,
          content: item.content,
          key: index + 1,
        };
      });
      setInputNumberStep(newData);
      setValue(`sendMail.mailStepParent`, newData?.[0]?.delay);
      setValue(
        `sendMail.mailStepParentContent`,
        data?.result?.mails?.[0]?.content
      );
      setValue(
        `sendMail.mailStepParentSubject`,
        data?.result?.mails?.[0]?.subject
      );
      setValue(
        `sendMail.mailStepParentMailTo`,
        data?.result?.mails?.[0]?.recipients ?? []
      );
      setEmailConfigData(data?.result?.mails);
      setNumberStep(newData?.length);
    }
  };

  const {
    options: consultantOptions,
    handlePopupScroll: handleConsultantScroll,
    handleSearch: handleConsultantSearch,
    isLoading: isLoadingConsultants,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;

  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  useEffect(() => {
    setValue(
      'consultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'consultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'contactConsultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
    setValue(
      'consultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );
  }, [userToSet]);

  const {
    searchOptions: addressOptions,
    setSearchText: setAddressSearchText,
    isLoading: isLoadingAddresses,
  } = useSearchWithDebounce(searchAddressWithOpenStreet);

  const handleGetMetaMode = async (body) => {
    const { data } = await getListEmployee({
      ...body,
      metaMode: 'metadata_mode',
    });

    return data;
  };

  const handleFindEmails = async ({ page = 1 }) => {
    try {
      const currentType = watch('keyAcordionPeople') || 0;
      setIsLoadingEmployee(true);
      let resultData = [];
      let bodyToSearch = null;
      if (currentType !== '6') {
        const locations = getValues().locationFinder
          ? getValues().locationFinder.map((item) => item.label)
          : [];
        const personTitles = getValues().titleFinder
          ? getValues().titleFinder.map((item) => item.label)
          : [];
        const employeeRanges = getValues().employeeFinder
          ? getValues().employeeFinder.map((item) => item.label)
          : [];
        const industryTagIds = getValues().industryFinder
          ? getValues().industryFinder.map((item) => item.value)
          : [];
        const contactEmailStatus = getValues().contactEmailStatus
          ? getValues().contactEmailStatus
          : [];
        const contactEmailOpened = getValues().emailOpenedStatus ?? null;
        const contactEmailOpenedAtLeast =
          getValues().contactEmailOpenedTime ?? null;
        const contactEmailOpenedAtDateRangeMin =
          getValues().contactEmailOpenedTimeMin &&
          getValues().contactEmailOpenedTimeMin !== ''
            ? getValues().contactEmailOpenedTimeMin
            : null;
        const contactEmailOpenedAtDateRangeMax =
          getValues().contactEmailOpenedTimeMax &&
          getValues().contactEmailOpenedTimeMax !== ''
            ? getValues().contactEmailOpenedTimeMax
            : null;
        const intentStrengths = getValues().contactBuyingIntentScore
          ? getValues().contactBuyingIntentScore
          : [];
        const intentIds = getValues().contactBuyingIntentIds
          ? getValues().contactBuyingIntentIds
          : [];
        const searchSignalIds = getValues().searchSignalIds
          ? getValues().searchSignalIds
          : [];
        const recommendationScoresMinTranche = getValues().contactMinimumScore
          ? getValues().contactMinimumScore
          : null;
        const containOneKeywords = watch('includeKeywordPeople')
          ? watch('includeKeywordPeople').map((item) => item.label)
          : [];
        const containAllKeyWords = watch('includeAllKeywordPeople')
          ? watch('includeAllKeywordPeople').map((item) => item.label)
          : [];
        const excludeKeyWords = watch('excludeKeywordsPeople')
          ? watch('excludeKeywordsPeople').map((item) => item.label)
          : [];
        const organizationLatestFundingStageCd =
          getValues('fundingStatusItem') ?? [];
        const currentlyUsingAnyOfTechnologyUids =
          getValues('listTechnologies') ?? [];
        const notExistFields =
          getValues('revenueStatus') === 'is_un_known'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const existFields =
          getValues('revenueStatus') === 'is_know'
            ? ['organization_revenue_in_thousands_int']
            : null;
        const organizationJobLocations = getValues('contactJobLocated') ?? [];
        const qOrganizationJobTitles = getValues('listCurrentlyHiring') ?? [];
        const personName = getValues('nameFinderText') ?? null;
        const organizationNumJobsRangeMin =
          getValues('organizationNumJobsRangeMin') ?? null;
        const organizationNumJobsRangeMax =
          getValues('organizationNumJobsRangeMax') ?? null;
        const organizationJobPostedAtRangeMin =
          getValues('organizationJobPostedAtRangeMin') ?? null;
        const organizationJobPostedAtRangeMax =
          getValues('organizationJobPostedAtRangeMax') ?? null;
        const organizationTradingStatus = getValues('revenueStatusItem') ?? [];
        const totalFundingRangeMin = getValues('fundingMin') ?? null;
        const totalFundingRangeMax = getValues('fundingMax') ?? null;
        bodyToSearch = {
          organizationId: watch('companyFinderId'),
          locations,
          personTitles,
          employeeRanges,
          industryTagIds,
          page,
          searchText: watch('searchPeople'),
          containOneKeywords,
          containAllKeyWords,
          excludeKeyWords,
          contactEmailStatus,
          contactEmailOpened,
          contactEmailOpenedAtLeast,
          contactEmailOpenedAtDateRangeMin,
          contactEmailOpenedAtDateRangeMax,
          intentStrengths,
          intentIds,
          searchSignalIds,
          recommendationScoresMinTranche,
          currentlyUsingAnyOfTechnologyUids,
          existFields,
          notExistFields,
          organizationTradingStatus,
          organizationLatestFundingStageCd,
          totalFundingRangeMax,
          totalFundingRangeMin,
          personName,
          organizationJobLocations,
          qOrganizationJobTitles,
          organizationNumJobsRangeMin,
          organizationNumJobsRangeMax,
          organizationJobPostedAtRangeMin,
          organizationJobPostedAtRangeMax,
        };
        const { data } = await getListEmployee(bodyToSearch);
        resultData = data;
      } else if (currentType === '6') {
        const { data } = await getListEmployee({
          organizationId: watch('companyFinderId'),
          page,
        });
        resultData = data;
        const people = resultData?.result?.people || [];
        const contacts = resultData?.result?.contacts || [];
        setListEmployeeCompany([...people, ...contacts]);
        setListEmployeePaginationCompany(resultData?.result?.pagination);
        setIsLoadingEmployee(false);
        return;
      }
      if (resultData.length === 0) return dataEmployeeNotFound();
      const people = resultData?.result?.people || [];
      const contacts = resultData?.result?.contacts || [];
      const listData = [...people, ...contacts];
      setListEmployee(listData);
      setIsLoadingEmployee(false);
      const dataPagination = await handleGetMetaMode(bodyToSearch);
      // console.log("dataPagination", dataPagination?.result?.pagination)
      setListEmployeePagination(
        dataPagination?.result?.pagination || resultData?.result?.pagination
      );
      const listOrgIds = listData
        ?.map((item) => item?.organization_id)
        .filter((id) => id != null);
    } catch (err) {
      console.log(err);
      setListEmployee([]);
      setListEmployeePagination({
        page: 0,
        per_page: 0,
        total_entries: 0,
        total_pages: 0,
      });
      setIsLoadingEmployee(false);
      notification.error({ message: err?.response?.data?.message });
    }
  };

  const dataEmployeeNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingEmployee(false);
  };

  const dataCompanyNotFound = async () => {
    notification.error({ message: 'Data Not Found' });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setIsLoadingCompanies(false);
  };

  const handleSubmitPeople = async ({ page = 1 }) => {
    if (watch('searchPeople') === '')
      return notification.error({ message: 'Please input search' });
    await handleFindEmails({ page });
  };

  const resetFormFindEmails = () => {
    setIsLoading(false);
  };
  const handleAddContact = async (fullName, dataContact) => {
    setIsLoading(true);
    await handleSetDataContact(dataContact);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      dataContact?.email
    );
    if (data?.result.length === 0) {
      setIsLoading(false);
      setIsAddContactForm(true);
    } else {
      const isDataExist = listAddContactSelected?.some(
        (item) => item?.id === data?.result[0].id
      );
      if (!isDataExist) {
        setListAddContactSelected([
          ...listAddContactSelected,
          { ...data?.result[0] },
        ]);
      }
      functionContactClient?.handleContactSearch(data?.result[0].name, '');
      setIsLoading(false);
      setFlagEditContact(false);
      setIsAddContactForm(false);
      notification.success({
        message: `This contact already existed on Bullhorn`,
      });
    }
  };

  const handleEditContact = async (fullName, email) => {
    setIsLoading(true);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      email
    );
    if (data?.result.length === 0) {
      notification.success({
        message: `This contact not existed on Bullhorn`,
      });
    } else {
      handleSetDataEditContact(data.result[0]);
      // setIsLoading(false);
      // setIsAddContactForm(true);
    }
  };

  const handleSetDataContact = async (data) => {
    setDetailDataContact(data);
    setFlagDetailContact(!flagDetailContact);
  };

  const handleSetDataEditContact = async (data) => {
    setDetailDataContact(data);
    setFlagEditContact(true);
  };

  const handleGetRevenue = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataRevenue(data?.faceting?.organization_trading_status_facets);
  };

  const handleGetFunding = async (payload) => {
    const { data } = await getRevenue(payload);
    setListDataFunding(data?.faceting?.latest_funding_stage_facets);
  };

  const handleSubmitAddContact = async () => {
    setIsSubmitForm(true);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      categories,
    } = getValues()?.clientContact;
    console.log('handlle add contact', surename);
    if (!companyId) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      setIsSubmitForm(false);
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Address is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (checkHenley ? skills.length === 0 : false) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    } else if (categories?.length === 0) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    }

    if (
      address?.length > 100 ||
      getValues()?.clientContact?.address2?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      // lastName: getValues()?.clientContact?.lastname,
      lastName: getValues()?.clientContact?.surename,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        address2: getValues()?.clientContact?.address2,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.surename
      }`,
      categories: {
        replaceAll: categories?.map((obj) => obj.value),
      },
      skills: {
        replaceAll: skills?.map((obj) => obj.value),
      },
      customText1: getValues()?.clientContact.linkedProfileUrl,
    };

    if (
      getValues()?.clientContact?.address > 100 ||
      getValues()?.clientContact?.address2 > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newContactPayloadCleaned = cleanPayload(payload);
    let data;
    try {
      data = flagEditContact
        ? await upladteBullhorn(
            getValues()?.clientContact?.id,
            newContactPayloadCleaned
          )
        : await insertBullhorn(newContactPayloadCleaned);
    } catch (err) {
      notification.error(err);
      setIsSubmitForm(false);
    }
    functionContactClient.handleContactSearch('', getValues().companyId);
    const isDataExist = listAddContactSelected.some(
      (item) => item?.id === data?.result?.changedEntityId
    );
    if (!isDataExist) {
      setListAddContactSelected([
        ...listAddContactSelected,
        { ...data?.result?.data, id: data?.result?.changedEntityId },
      ]);
    }
    setIsSubmitForm(false);
    setIsLoading(false);
    setIsAddContactForm(false);
    notification.success({
      message: `Success ${flagEditContact ? 'edit' : 'add'} contact ${data?.result?.data?.name}`,
    });
    setFlagEditContact(false);
  };

  const handleResetFormAddContact = async () => {
    setValue('clientContact.namePrefixSelect', null);
    setValue('clientContact.namePrefix', null);
    setValue('clientContact.firstName', null);
    setValue('clientContact.middleName', null);
    setValue('clientContact.surename', null);
    setValue('clientContact.consultantId', null);
    setValue('clientContact.consultant', null);
    setValue('clientContact.consultantSelect', null);
    setValue('clientContact.statusSelect', null);
    setValue('clientContact.type', null);
    setValue('clientContact.secondaryOwnerSelect', null);
    setValue('clientContact.companyId', null);
    setValue('clientContact.companySelect', null);
    setValue('clientContact.company', null);
    setValue('clientContact.department', null);
    setValue('clientContact.jobTitle', null);
    setValue('clientContact.workEmail', null);
    setValue('clientContact.personalEmail', null);
    setValue('clientContact.workPhone', null);
    setValue('clientContact.mobilePhone', null);
    setValue('clientContact.otherPhone', null);
    setValue('clientContact.fax', null);
    setValue('clientContact.stateId', null);
    setValue('clientContact.state', null);
    setValue('clientContact.county', null);
    setValue('clientContact.countySelect', null);
    setValue('clientContact.address', null);
    setValue('clientContact.city', null);
    setValue('clientContact.zip', null);
    setValue('clientContact.industries', []);
    setValue('clientContact.generalCommets', null);
    setValue('clientContact.referredById', null);
  };

  const updateArrayByKey = (key, checked, optionKey) => {
    const newValues = checked
      ? [...(getValues(key) ?? []), optionKey]
      : (getValues(key) ?? [])?.filter((value) => value !== optionKey);

    setValue(key, newValues);
  };

  const handleDeleteIntent = async (value) => {
    const index = listDataIntentSetting.find((obj) => obj.id === value);
    if (index) {
      const newArr = listDataIntentSetting.filter((obj) => obj.id !== value);
      setListDataIntentSetting(newArr);
    }
  };

  const handleGetLocation = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'location',
    });
    setDataLocation(data?.tags);
  };

  const handleGetDetailEmployee = async (id) => {
    const { data } = await getLDetailEmployee({ employeeId: id });
    if (data) {
      setListDetailEmployee([...listDetailEmployee, { ...data }]);
      setIsLoading(true);
      setListEmployee([...listEmployee]);
      setListEmployeePagination({ ...listEmployeePagination });
      setIsLoading(false);
    }
  };

  const handleAccordionChangePeople = (keys) => {
    setValue('keyAcordionPeople', keys[0]);
    if (keys[0] === '10') {
      refetchListSignals();
    }
  };

  const checkboxGroups = {
    'Safe to send': [
      { key: 'likely_to_engage', label: 'Likely to engage' },
      { key: 'verified', label: 'Verified' },
    ],
    'Send with caution': [{ key: 'unverified', label: 'Unverified' }],
    'Do not send': [
      { key: 'update_required', label: 'Update required' },
      { key: 'unavailable', label: 'Unavailable' },
    ],
  };

  const handleGetIntentCategory = async () => {
    const data = await employeeFinderIntentCategory();
    setDataListTopicTitle(data.data.categories);
  };
  useEffect(() => {
    if (!onClose) {
      handleGetIntentCategory();
    }
  }, [showModalAddTopic]);

  const handleGetTechno = async () => {
    const { data } = await employeeFinderSearchTag({
      searchText: '',
      type: 'technology',
    });
    setDataTechnologies(data?.tags);
  };

  const resetAllPeople = () => {
    setValue('companyFinder', '');
    setValue('companyFinderId', '');
    setValue('locationFinderText', ' ');
    setValue('locationFinder', []);
    setValue('titleFinderText', ' ');
    setValue('titleFinder', []);
    setValue('employeeFinderText', '');
    setValue('employeeFinder', []);
    setValue('industryFinderText', ' ');
    setValue('industryFinder', []);
    setShowIncludeKeywordPeople(false);
    setShowIncludeAllKeywordPeople(false);
    setShowExcludeKeywordsPeople(false);
    setValue('includeKeywordPeople', []);
    setValue('includeKeywordPeopleText', '');
    setValue('includeAllKeywordPeople', []);
    setValue('includeAllKeywordPeopleText', '');
    setValue('excludeKeywordsPeople', []);
    setValue('excludeKeywordsPeopleText', '');
  };

  const handleAccordionChangeCompany = (keys) => {
    setValue('keyAcordionCompany', keys[0]);

    switch (keys[0]) {
      case '6':
        refetchListSignals();
        break;
      case '7':
        refetchListFacets();
        break;
      default:
        break;
    }
  };

  const resetAllCompany = () => {
    setValue('companyFindCompany', '');
    setValue('companyFindCompanyId', '');
    setValue('locationFindCompanyText', ' ');
    setValue('locationFindCompany', []);
    setValue('employeesFindCompanyText', ' ');
    setValue('employeesFindCompany', []);
    setValue('industryKeywordCompanyText', ' ');
    setValue('industryKeywordCompany', []);
    setShowIncludeKeywordCompany(false);
    setShowIncludeAllKeywordCompany(false);
    setShowExcludeKeywordsCompany(false);
    setValue('includeKeywordCompany', []);
    setValue('includeKeywordCompanyText', '');
    setValue('includeAllKeywordCompany', []);
    setValue('includeAllKeywordCompanyText', '');
    setValue('excludeKeywordsCompany', []);
    setValue('excludeKeywordsCompanyText', '');
  };

  const handleSendEmail = () => {
    setSendEmailStatus(true);
  };

  useEffect(() => {
    if (!onClose) {
      setSendEmailStatus(false);
      setNumberStep(0);
      setInputNumberStep([]);
      setValue('mailStep', null);
      getDataEmail();
    }
  }, [openModalSendEmail]);

  const changeListEmail = (str) => {
    const index = listEmail.indexOf(str);
    if (index !== -1) {
      const newStrings = [...listEmail];
      newStrings.splice(index, 1);
      setListEmail(newStrings);
    } else {
      setListEmail([...listEmail, str]);
    }
  };

  const { data: listSignals, refetch: refetchListSignals } = useQuery(
    ['GET_LIST_SIGNALS'],
    async () => {
      const { data } = await employeeFinderSearchSignals();
      return data?.templates || [];
    },
    { enabled: false }
  );

  const EmailOpenedStatus = [
    { key: 0, value: 'yes', label: 'Yes' },
    { key: 0, value: 'no', label: 'No' },
  ];

  const OptionBuyIntent = [
    { label: 'High', value: 'high' },
    { label: 'Medium', value: 'mid' },
    { label: 'Low', value: 'low' },
    { label: 'None', value: 'none' },
  ];

  const handleChangeSettingIntent = async () => {
    setDataParentIntent(listDataIntentSetting);
    setShowModalAddTopic(false);
    localStorage.setItem(
      'listIntentChoose',
      JSON.stringify(listDataIntentSetting)
    );
  };

  useEffect(() => {
    const storedData = localStorage?.getItem('listIntentChoose');
    setDataParentIntent(storedData ? JSON.parse(storedData) : []);
    setListDataIntentSetting(storedData ? JSON.parse(storedData) : []);
  }, []);

  const formatNumber = (number) => {
    const suffixes = ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];
    let suffixIndex = 0;
    while (number >= 1000) {
      number /= 1000;
      suffixIndex++;
    }
    return number.toFixed(1) + suffixes[suffixIndex];
  };

  const handleGetPersonalTitles = async (keyword = '') => {
    const { data } = await employeeFinderSearchTag({
      searchText: keyword,
      type: 'person_title',
    });
    setDataPersonTitle(data?.tags);
  };

  const { data: listFacets, refetch: refetchListFacets } = useQuery(
    ['GET_LIST_FACETS'],
    async () => {
      const { data } = await getFacets();
      return data?.faceting?.account_stage_facets || [];
    },
    { enabled: false }
  );

  const filterAddContact = [
    {
      key: '1',
      label: (
        <div className="inline-grid">
          <span>Company</span>
          {watch('companyFinderId') !== '' && watch('companyFinderId') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('companyFinder')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Company Name" name="companyFinder">
          <Controller
            render={({ field }) => (
              <AutoComplete
                style={{ width: '250px' }}
                {...field}
                options={companyListPeople.map((option) => ({
                  value: option.id,
                  label: (
                    <div className="grid p-2">
                      <div className="flex justify-between">
                        <span className="text-base font-base my-auto w-4/5 whitespace-normal">
                          {option.name}
                          <br />
                          <span className="text-xs font-base my-auto w-4/5 whitespace-normal">
                            {option.domain || '-'}
                          </span>
                        </span>
                        <img
                          className="absolute right-3"
                          src={option?.logo_url ? `${option?.logo_url}` : ''}
                          width={50}
                          height={50}
                          alt="Logo"
                        />
                      </div>
                    </div>
                  ),
                }))}
                onSearch={(value) => {
                  setValue('companyFinder', value);
                  setValue('companyFinderSelect', null);
                  setValue('companyFinderId', null);
                }}
                onSelect={async (selectedCompanyId) => {
                  const selectedCompany = companyListPeople.find(
                    (ao) => ao.id == selectedCompanyId
                  );
                  setValue('companyFinder', selectedCompany.name);
                  setValue('companyFinderSelect', selectedCompanyId);
                  setValue('companyFinderId', selectedCompanyId);
                }}
              >
                <Input />
              </AutoComplete>
            )}
            name="companyFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '2',
      label: (
        <div className="inline-grid">
          <span>Location</span>
          {watch('locationFinder') && watch('locationFinder')?.length > 0 && (
            <Row>
              {watch('locationFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Location Name" name="locationFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('locationFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('locationFinderText') === '' ||
                  !watch('locationFinderText')
                    ? locationListPeople?.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('locationFinderText'),
                          value: watch('locationFinderText'),
                        },
                        ...locationListPeople?.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="locationFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '3',
      label: (
        <div className="inline-grid">
          <span>Title</span>
          {watch('titleFinder') && watch('titleFinder')?.length > 0 && (
            <Row>
              {watch('titleFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Title" name="titleFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  setValue('titleFinderText', searchText);
                }}
                {...field}
                notFoundContent={null}
                options={
                  watch('titleFinderText') === '' || !watch('titleFinderText')
                    ? titleList.map((so) => ({
                        ...so,
                        label: so.cleaned_name,
                        value: so.id,
                      }))
                    : [
                        {
                          label: watch('titleFinderText'),
                          value: watch('titleFinderText'),
                        },
                        ...titleList.map((so) => ({
                          ...so,
                          label: so.cleaned_name,
                          value: so.id,
                        })),
                      ]
                }
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="titleFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '4',
      label: (
        <div className="inline-grid">
          <span>Employees</span>
          {watch('employeeFinder') && watch('employeeFinder')?.length > 0 && (
            <Row>
              {watch('employeeFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Predefined Range" name="employeeFinder">
          <Controller
            render={({ field }) => (
              <Select
                labelInValue
                mode="multiple"
                onSearch={(searchText) => {
                  if (dataEmployeeList.length == 0) {
                    setValue('employeeFinderText', searchText);
                  }
                }}
                {...field}
                notFoundContent={null}
                options={dataEmployeeList.map((so) => ({
                  ...so,
                  label: so.display_name,
                  value: so.value,
                }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              />
            )}
            name="employeeFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '5',
      label: (
        <div className="inline-grid">
          <span>Industry & Keywords</span>
          {watch('industryFinder') && watch('industryFinder')?.length > 0 && (
            <Row>
              <span className="text-xs my-auto mr-2">Industry: </span>
              {watch('industryFinder').map((item, index) => (
                <Col
                  key={index}
                  className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                >
                  {item.label}
                </Col>
              ))}
            </Row>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Industry" name="industryFinder">
            <Controller
              render={({ field }) => (
                <Select
                  labelInValue
                  mode="multiple"
                  onSearch={(searchText) => {
                    setValue('industryFinderText', searchText);
                  }}
                  {...field}
                  notFoundContent={null}
                  options={dataIndustryList.map((so) => ({
                    ...so,
                    label: so.cleaned_name,
                    value: so.id,
                  }))}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                />
              )}
              name="industryFinder"
              control={control}
            />
          </Form.Item>
          <Checkbox
            className="w-full"
            checked={showIncludeKeywordPeople}
            onChange={(e) => {
              setShowIncludeKeywordPeople(e.target.checked);
              setValue('includeKeywordPeople', []);
              setValue('includeKeywordPeopleText', '');
            }}
          >
            Include Keywords
          </Checkbox>
          {showIncludeKeywordPeople && (
            <Form.Item name="includeKeywordPeople">
              <Controller
                name="includeKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeKeywordPeopleText'),
                        label: watch('includeKeywordPeopleText'),
                        value: watch('includeKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showIncludeAllKeywordPeople}
            onChange={(e) => {
              setShowIncludeAllKeywordPeople(e.target.checked);
              setValue('includeAllKeywordPeople', []);
              setValue('includeAllKeywordPeopleText', '');
            }}
          >
            Include ALL
          </Checkbox>
          {showIncludeAllKeywordPeople && (
            <Form.Item name="includeAllKeywordPeople">
              <Controller
                name="includeAllKeywordPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('includeAllKeywordPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('includeAllKeywordPeopleText'),
                        label: watch('includeAllKeywordPeopleText'),
                        value: watch('includeAllKeywordPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
          <Checkbox
            className="w-full"
            checked={showExcludeKeywordsPeople}
            onChange={(e) => {
              setShowExcludeKeywordsPeople(e.target.checked);
              setValue('excludeKeywordsPeople', []);
              setValue('excludeKeywordsPeopleText', '');
            }}
          >
            Exclude Keywords
          </Checkbox>
          {showExcludeKeywordsPeople && (
            <Form.Item name="excludeKeywordsPeople">
              <Controller
                name="excludeKeywordsPeople"
                control={control}
                render={({ field }) => (
                  <Select
                    labelInValue
                    filterOption={false}
                    mode="multiple"
                    onSearch={(inputValue) => {
                      setValue('excludeKeywordsPeopleText', inputValue);
                    }}
                    {...field}
                    options={[
                      {
                        key: watch('excludeKeywordsPeopleText'),
                        label: watch('excludeKeywordsPeopleText'),
                        value: watch('excludeKeywordsPeopleText'),
                      },
                    ]}
                  />
                )}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      key: '7',
      label: (
        <div className="inline-grid">
          <span>Email Status</span>
          {watch('contactEmailStatus') &&
            watch('contactEmailStatus')?.length > 0 && (
              <Row>
                {watch('contactEmailStatus').map((emailStatusKey, index) => (
                  <Col
                    key={index}
                    className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1"
                  >
                    {
                      Object.entries(checkboxGroups)
                        .find(([_, options]) =>
                          options.find(
                            (option) => option.key === emailStatusKey
                          )
                        )
                        .at(1)
                        .find((option) => option.key === emailStatusKey).label
                    }
                  </Col>
                ))}
              </Row>
            )}
        </div>
      ),
      children: (
        <>
          <Form.Item name="contactEmailStatus">
            <Controller
              name="contactEmailStatus"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {Object.entries(checkboxGroups).map(([group, options]) => (
                    <div key={group}>
                      <Typography.Title level={5}>{group}</Typography.Title>
                      <Flex vertical gap={4}>
                        {options.map((option, index) => (
                          <Checkbox
                            key={`email-satus-${option.key}-${index}`}
                            checked={field?.value?.includes(option.key)}
                            onChange={(e) =>
                              updateArrayByKey(
                                'contactEmailStatus',
                                e.target.checked,
                                option.key
                              )
                            }
                          >
                            {option.label}
                          </Checkbox>
                        ))}
                      </Flex>
                    </div>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '8',
      label: (
        <div className="inline-grid">
          <span>Email Opened</span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTimeMin') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time min:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTimeMin')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTimeMax') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time min:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTimeMax')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="contactEmailOpened" label="Opened Status">
            <Controller
              name="contactEmailOpened"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  style={{ width: '250px' }}
                  {...field}
                  options={EmailOpenedStatus.map((option) => ({
                    value: option.value,
                    label: <>{option.label}</>,
                  }))}
                  onSelect={async (value) => {
                    setValue('contactEmailOpened', value);
                    setValue('emailOpenedStatus', value);
                  }}
                >
                  <Input />
                </AutoComplete>
              )}
            />
          </Form.Item>

          <Form.Item
            name="contactEmailOpenedTime"
            label="Opened at least __ times"
          >
            <Controller
              name="contactEmailOpenedTime"
              control={control}
              render={({ field }) => (
                <InputNumber
                  min={1}
                  max={100000}
                  style={{ width: '100%' }}
                  onChange={(e) => {
                    setValue('contactEmailOpenedTime', e);
                  }}
                />
              )}
            />
          </Form.Item>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="contactEmailOpenedTimeMin" label="Time min">
                <Controller
                  name="contactEmailOpenedTimeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      onChange={(date, dateString) => {
                        setValue('contactEmailOpenedTimeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="contactEmailOpenedTimeMax" label="Time Max">
                <Controller
                  name="contactEmailOpenedTimeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      onChange={(date, dateString) => {
                        setValue('contactEmailOpenedTimeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
    {
      key: '9',
      label: (
        <div className="inline-grid">
          <span>Buying Intent</span>
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700' }}>Intent Score</div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentScore">
              <Controller
                name="contactBuyingIntentScore"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    style={{
                      width: '100%',
                    }}
                    options={OptionBuyIntent}
                    onChange={(e) => {
                      setValue('contactBuyingIntentScore', e);
                    }}
                  ></Checkbox.Group>
                )}
              />
            </Form.Item>
          </div>
          <div
            style={{ width: '100%', height: '2px', background: '#ccc' }}
          ></div>
          <div style={{ fontWeight: '700', marginTop: '10px' }}>
            Intent Topics
          </div>
          <div style={{ marginTop: '10px' }}>
            <Form.Item name="contactBuyingIntentIds">
              <Controller
                name="contactBuyingIntentIds"
                control={control}
                render={({ field }) => (
                  <Checkbox.Group
                    onChange={(e) => {
                      setValue('contactBuyingIntentIds', e);
                    }}
                  >
                    {dataParentIntent?.map((item, index) => (
                      <div style={{ width: '100%', marginTop: '5px' }}>
                        <Checkbox value={item?.id}>{item?.name}</Checkbox>
                      </div>
                    ))}
                  </Checkbox.Group>
                )}
              />
            </Form.Item>
            <div
              onClick={() => setShowModalAddTopic(true)}
              style={{ cursor: 'pointer' }}
            >
              Add more topic
            </div>
            <Modal
              onCancel={() => setShowModalAddTopic(false)}
              onOk={handleChangeSettingIntent}
              width={'1000px'}
              title="Intent Topic Settings"
              open={showModalAddTopic}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '65%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Topics of Interest
                  </div>
                  <div
                    style={{
                      padding: '10px',
                      height: '500px',
                      overflowY: 'scroll',
                    }}
                  >
                    {dataListTopicTitle?.map((item, index) => (
                      <>
                        <IntentCollap
                          setListDataIntentSetting={setListDataIntentSetting}
                          listDataIntentSetting={listDataIntentSetting}
                          item={item}
                        />
                      </>
                    ))}
                  </div>
                </div>
                <div style={{ width: '35%', border: '1px solid #ccc' }}>
                  <div
                    style={{
                      width: '100%',
                      borderBottom: '1px solid #ccc',
                      padding: '10px',
                    }}
                  >
                    Selected
                  </div>
                  <div style={{ padding: '10px' }}>
                    {listDataIntentSetting?.map((item, index) => (
                      <>
                        <Tag
                          style={{ marginBottom: '10px' }}
                          onClose={(e) => {
                            e.preventDefault();
                            handleDeleteIntent(item.id);
                          }}
                          closeIcon
                        >
                          {item?.name}
                        </Tag>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            </Modal>
          </div>
        </>
      ),
    },
    {
      key: '10',
      label: (
        <>
          <Flex>
            <span>Signals</span>
            {watch('searchSignalIds') &&
              watch('searchSignalIds')?.length > 0 && (
                <Tag className="ml-auto" color="volcano">
                  {watch('searchSignalIds')?.length}
                </Tag>
              )}
          </Flex>
        </>
      ),
      children: (
        <>
          <Form.Item name="searchSignalIds">
            <Controller
              name="searchSignalIds"
              control={control}
              render={({ field }) => (
                <Flex vertical gap={8}>
                  {listSignals?.map((option, index, arr) => (
                    <Flex
                      className={
                        index !== arr.length - 1 ? 'border-b pb-2' : undefined
                      }
                      justify="space-between"
                    >
                      <Checkbox
                        key={`signal-${option.id}`}
                        checked={field.value?.includes(option.id)}
                        onChange={(e) =>
                          updateArrayByKey(
                            'searchSignalIds',
                            e.target.checked,
                            option.id
                          )
                        }
                      >
                        {option.name}
                      </Checkbox>
                      {option.modality === 'people' ? (
                        <Tag
                          className="flex items-center justify-center"
                          color="processing"
                          icon={<FaRegUser />}
                        />
                      ) : (
                        <Tag
                          className="flex items-center justify-center"
                          color="success"
                          icon={<FaRegBuilding />}
                        />
                      )}
                    </Flex>
                  ))}
                </Flex>
              )}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '11',
      label: (
        <div className="inline-grid">
          <span>Score</span>
          {watch('contactMinimumScore') && (
            <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
              score:{' '}
              <span style={{ fontWeight: '700' }}>
                {watch('contactMinimumScore')}
              </span>
            </Col>
          )}
        </div>
      ),
      children: (
        <>
          <div style={{ fontWeight: '700', fontSize: '13px' }}>
            Minimum score:
          </div>
          <Radio.Group
            style={{ marginTop: '20px' }}
            onChange={(e) => setValue('contactMinimumScore', e.target.value)}
          >
            <Space direction="vertical">
              <Radio value={'excellent'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(61, 204, 133, .18)',
                  }}
                >
                  ⭐️ Excellent
                </div>
              </Radio>
              <Radio value={'good'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#e9f2ff',
                  }}
                >
                  😄 Good
                </div>
              </Radio>
              <Radio value={'fair'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: 'rgba(255,151,82,.18)',
                  }}
                >
                  🙂 Fair
                </div>
              </Radio>
              <Radio value={'poor'}>
                <div
                  style={{
                    padding: '5px',
                    borderRadius: '5px',
                    background: '#efefef',
                  }}
                >
                  ✖️ Not a fit
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      ),
    },
    {
      key: '12',
      label: (
        <div className="inline-grid">
          <span>Technologies</span>
          {watch('listTechnologies') && (
            <>
              {watch('listTechnologies')?.map((item) => (
                <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                  <span style={{ fontWeight: '700' }}>{item}</span>
                </Col>
              ))}
            </>
          )}
        </div>
      ),
      children: (
        <>
          <Form.Item label="Technologies" name="listTechnologies">
            <Controller
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataTechnologies.map((option) => ({
                    value: option.uid,
                    label: (
                      <div>
                        <div style={{ fontSize: '16px' }}>
                          {option?.cleaned_name}
                        </div>
                        <div style={{ fontSize: '12px' }}>
                          {option?.tag_category_downcase}
                        </div>
                      </div>
                    ),
                  }))}
                  onSearch={async (e) => {
                    const { data } = await employeeFinderSearchTag({
                      searchText: e,
                      type: 'technology',
                    });
                    setDataTechnologies(data?.tags);
                  }}
                  onChange={(value) => setValue('listTechnologies', value)}
                >
                  <Input />
                </Select>
              )}
              name="companyFindCompany"
              control={control}
            />
          </Form.Item>
        </>
      ),
    },
    {
      key: '13',
      label: (
        <div className="inline-grid">
          <span>Revenue</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <Form.Item name="revenueStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        existFields: ['organization_revenue_in_thousands_int'],
                      });
                    } else if (value === 'is_un_known') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                        notExistFields: [
                          'organization_revenue_in_thousands_int',
                        ],
                      });
                    } else if (value === 'is_between') {
                      await handleGetRevenue({
                        openFactorNames: ['organization_trading_status'],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="revenueStatus"
              control={control}
            />
          </Form.Item>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '-10px',
            }}
          ></div>
          <div style={{ marginTop: '10px' }}>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('revenueStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataRevenue?.map((item) => (
                  <>
                    <Checkbox value={item?.value}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
        </>
      ),
    },
    {
      key: '14',
      label: (
        <div className="inline-grid">
          <span>Funding</span>
          {/* {watch('listTechnologies') && (
            <>
            {watch('listTechnologies')?.map((item) => (
               <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
               <span style={{ fontWeight: '700' }}>
                 {item}
               </span>
             </Col>
            ))}
            </>
            )} */}
        </div>
      ),
      children: (
        <>
          <div>
            <div>
              <Checkbox.Group
                onChange={(checkedValues) =>
                  setValue('fundingStatusItem', checkedValues)
                }
                style={{ width: '100%' }}
              >
                {listDataFunding?.map((item) => (
                  <>
                    <Checkbox value={item?.value} style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>{item?.display_name}</div>
                        <div style={{ marginLeft: '20px' }}>
                          {formatNumber(item?.count)}
                        </div>
                      </div>
                    </Checkbox>
                  </>
                ))}
              </Checkbox.Group>
            </div>
          </div>
          <div
            style={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '20px',
            }}
          ></div>
          <Form.Item name="fundingStatus">
            <Controller
              render={({ field }) => (
                <Segmented
                  defaultValue="is_between"
                  onChange={async (value) => {
                    if (value === 'is_know') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        existFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_un_known') {
                      setFundingSize(false);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                        notExistFields: ['organization_total_funding_long'],
                      });
                    } else if (value === 'is_between') {
                      setFundingSize(true);
                      await handleGetFunding({
                        openFactorNames: [
                          'organization_latest_funding_stage_cd',
                        ],
                      });
                    }
                    setValue('revenueStatus', value);
                  }}
                  options={[
                    { label: 'Is Between', value: 'is_between' },
                    { label: 'Is Know', value: 'is_know' },
                    { label: 'Is UnKnown', value: 'is_un_known' },
                  ]}
                />
              )}
              name="fundingStatus"
              control={control}
            />
          </Form.Item>
          {fundingSize && (
            <Row gutter={[24, 24]}>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding min">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMin', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="fundingMin" label="Funding Max">
                  <Controller
                    name="fundingMin"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        min={1}
                        onChange={(value) => {
                          setValue('fundingMax', value);
                        }}
                      />
                    )}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      key: '15',
      label: (
        <div className="inline-grid">
          <span>Name</span>
          {watch('nameFinderText') !== '' && watch('nameFinderText') && (
            <span className="bg-gray-200 p-1 mt-1 rounded text-xs">
              {watch('nameFinderText')}
            </span>
          )}
        </div>
      ),
      children: (
        <Form.Item label="Name" name="nameFinder">
          <Controller
            render={({ field }) => (
              <Input
                placeholder="name"
                onChange={(e) => setValue('nameFinderText', e.target.value)}
              />
            )}
            name="nameFinder"
            control={control}
          />
        </Form.Item>
      ),
    },
    {
      key: '16',
      label: (
        <div className="inline-grid">
          <span>Job Postings</span>
          <Row>
            {watch('emailOpenedStatus') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                status:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('emailOpenedStatus')}
                </span>
              </Col>
            )}
            {watch('contactEmailOpenedTime') && (
              <Col className="bg-gray-200 p-1 mt-1 rounded text-xs mr-1">
                Time least:{' '}
                <span style={{ fontWeight: '700' }}>
                  {watch('contactEmailOpenedTime')}
                </span>
              </Col>
            )}
          </Row>
        </div>
      ),
      children: (
        <>
          <Form.Item name="currentlyHiring" label="Currently Hiring for">
            <Controller
              name="currentlyHiring"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataPersonTitle.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetPersonalTitles(e);
                  }}
                  onChange={(value) => setValue('listCurrentlyHiring', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>

          <Form.Item name="contactJobLocated" label="Job located at">
            <Controller
              name="contactJobLocated"
              control={control}
              render={({ field }) => (
                <Select
                  mode="multiple"
                  style={{ width: '250px' }}
                  filterOption={false}
                  {...field}
                  options={dataLocation.map((option) => ({
                    value: option.cleaned_name,
                    label: <> {option?.cleaned_name}</>,
                  }))}
                  onSearch={async (e) => {
                    handleGetLocation(e);
                  }}
                  onChange={(value) => setValue('contactJobLocated', value)}
                >
                  <Input />
                </Select>
              )}
            />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMin" label="Active job">
                <Controller
                  name="organizationNumJobsRangeMin"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Min"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMin', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationNumJobsRangeMax" label=" ">
                <Controller
                  name="organizationNumJobsRangeMax"
                  control={control}
                  render={({ field }) => (
                    <InputNumber
                      min={1}
                      placeholder="Max"
                      onChange={(value) =>
                        setValue('organizationNumJobsRangeMax', value)
                      }
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                name="organizationJobPostedAtRangeMin"
                label="Job Posted At"
              >
                <Controller
                  name="organizationJobPostedAtRangeMin"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="Start Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMin', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="organizationJobPostedAtRangeMax" label=" ">
                <Controller
                  name="organizationJobPostedAtRangeMax"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      placeholder="End Date"
                      onChange={(date, dateString) => {
                        setValue('organizationJobPostedAtRangeMax', dateString);
                      }}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  const itemsDropdown = [
    {
      key: '1',
      label: <div>Edit</div>,
    },
    {
      key: '2',
      label: <div>Delete</div>,
    },
  ];

  const handleGenderSupportBar = (record, name = null) => {
    return (
      <div style={{ display: 'flex' }}>
        <div>
          <Button
            onClick={async (e) => {
              e.stopPropagation();
              await handleAddContact(record?.name, record);
            }}
            style={{ borderRadius: '0' }}
          >
            <Image
              preview={false}
              src={logo}
              style={{ width: '20px', height: '20px' }}
            />
          </Button>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <Button
                      type={'primary'}
                      onClick={() => {
                        setOpenExistingSequence(true);
                      }}
                    >
                      Add to sequence
                    </Button>
                    <Button
                      onClick={() => {
                        setValue('email', record?.email);
                        setValue('sendMail.mailStepParentMailTo', [
                          { email: record?.email, name: record?.name },
                        ]);
                        setValue('optionContactSelect', record);
                        setOpenSendEmailContact(true);
                      }}
                    >
                      Send Email
                    </Button>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <div>
                      {record?.email}{' '}
                      <CopyOutlined
                        style={{ marginLeft: '10px' }}
                        onClick={() => {
                          navigator.clipboard.writeText(record?.email),
                            notification.success({
                              message: 'Copy To Clipboard success',
                            });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MailOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Direct Dial
                  </div>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      fontWeight: '700',
                    }}
                  >
                    {record?.sanitized_phone}
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button type="primary">
                      <a href={`tel:${record?.sanitized_phone}`}>Call</a>
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <PhoneOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '300px' }}>
                  <div
                    style={{
                      marginTop: '5px',
                      fontSize: '15px',
                      padding: '5px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    {record.name} is in any Lists
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button
                      onClick={() => setOpenExistingUserGroup(true)}
                      type="link"
                    >
                      Add to Lists
                    </Button>
                  </div>
                </div>
              </div>
            }
            // okText={'Close'}
            // cancelText={<></>}
            // showCancel={false}
            // showArrow={false}
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <MenuUnfoldOutlined />
            </Button>
          </Popover>
        </div>
        <div>
          <Popover
            placement="top"
            title={false}
            content={
              <div onClick={(e) => e.stopPropagation()}>
                <div style={{ width: '200px' }}>
                  <div style={{ marginTop: '5px', fontSize: '15px' }}>
                    Add Contact to Sequence
                  </div>
                  <div style={{ marginTop: '5px', fontSize: '14px' }}>
                    You are one click away from an automated email workflow to
                    get more open rates and meetings
                  </div>
                  <div style={{ marginTop: '12px' }}>
                    <Button icon={<SendOutlined />} type="primary">
                      Create new Sequence
                    </Button>
                  </div>
                </div>
              </div>
            }
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '50px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SendOutlined />
              <CaretDownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Popover>
        </div>
        <div>
          <Dropdown menu={{ items: itemsDropdown }} placement="top">
            <Button
              onClick={(e) => e.stopPropagation()}
              style={{
                borderRadius: '0',
                width: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <SmallDashOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    );
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listSelectSection, setListSelectSection] = useState([]);

  const [isAddContactFormBulk, setIsAddContactFormBulk] = useState(false);
  const [loadingGetBulkData, setLoadingBulkData] = useState(false);
  const [dataContacts, setDataContacts] = useState([]);
  const [isLoadingAddContactFormBulk, setIsLoadingAddContactFormBulk] =
    useState(false);

  // const onSelectChangeTable = (e) => {
  //   setSelectedRowKeys(e);
  // };

  // const rowSelection = {
  //   selectedRowKeys: selectedRowKeys,
  //   onChange: onSelectChangeTable,
  // };

  // START DETAIL CONTACT
  const [showDetailContact, setShowDetailContact] = useState(false);
  const [detailContactStaff, setDetailContactStaff] = useState({});

  // END DETAIL CONTACT

  // START DETAIL COMPANY
  const [showDetailCompany, setShowDetailCompany] = useState(false);
  const [detailCompany, setDetailCompany] = useState({});

  // END DETAIL COMPANY

  // useMemo(() => {
  //   if (getValues()?.joblocationcity)
  //     handleGetDefaultAddress(getValues()?.joblocationcity);
  // }, [job, getValues()?.joblocationcity]);

  useEffect(() => {
    resetFormFindEmails();
    setValue('companyFinder', getValues().company);
    setValue('fullName', '');
    setIsSubmitForm(false);
  }, [isModalOpen]);

  const handleSubmitBullhorn = (e) => {
    handleOk();
  };

  const handleSubmitCompany = async (e) => {
    const companyPayload = getValues().clientCorporation;
    const companyId = getValues().companyId;

    const {
      name,
      status,
      mainPhone,
      companyAddress,
      stateId,
      countySelect,
      zip,
      standardFeeArrangement,
      industries,
      categories,
      skills,
    } = companyPayload;

    if (!name) {
      return notification.error({ message: 'Company name is required.' });
    } else if (!status) {
      return notification.error({ message: 'Status is required.' });
    } else if (!checkHenley ? industries.length === 0 : false) {
      return notification.error({ message: 'Industries is required.' });
    } else if (!mainPhone) {
      return notification.error({ message: 'Main phone number is required.' });
    } else if (!companyAddress) {
      return notification.error({ message: 'Company address is required.' });
    } else if (!stateId) {
      return notification.error({ message: 'Country is required.' });
    } else if (!countySelect) {
      return notification.error({ message: 'County is required.' });
    } else if (!zip) {
      return notification.error({ message: 'Postcode is required.' });
    } else if (categories.length === 0) {
      return notification.error({ message: 'Categories is required.' });
    } else if (skills.length === 0) {
      return notification.error({ message: 'Skills is required.' });
    }

    if (companyPayload?.companyAddress?.length > 100) {
      notification.error({
        message: 'Address should not have more than 100 characters',
      });
      return;
    }

    if (companyPayload?.companySecondAddress?.length > 100) {
      notification.error({
        message: 'Address should not have more than 100 characters',
      });
      return;
    }

    const newCompanyPayload = {
      entityName: 'ClientCorporation',
      name: companyPayload?.name,
      status: companyPayload?.status,
      parentClientCorporation: {
        id:
          companyPayload?.parentCompanyId === ''
            ? undefined
            : companyPayload?.parentCompanyId,
      },
      customText2: companyPayload?.companyOwnerId,
      businessSectorList: !companyPayload.industries
        ? []
        : companyPayload?.industries.length === 0
          ? []
          : companyPayload.industries
              .filter((industry) => industry && industry.label)
              .map((industry) => industry.label),
      notes: companyPayload?.companyComments,
      companyDescription: companyPayload?.companyDescription,
      companyURL: companyPayload?.companyWebsite,
      phone: `${companyPayload?.mainPhone}`,
      address: {
        countryName: companyPayload?.state,
        countryID: Number(companyPayload?.stateId),
        address1: companyPayload?.companyAddress,
        address2: companyPayload?.companySecondAddress,
        city: companyPayload?.city,
        zip: companyPayload?.zip,
        state: companyPayload?.county,
        timezone: null,
      },
      billingContact: companyPayload?.billingContact,
      billingPhone: `${companyPayload?.billingPhone}`,
      invoiceFormat: companyPayload?.invoiceFormatInformation,
      feeArrangement: companyPayload?.standardFeeArrangement,
      billingAddress: {
        state: companyPayload?.billingZip,
        address1: companyPayload?.billingAddress,
        city: companyPayload?.billingCity,
        zip: companyPayload?.billingZip,
        county: companyPayload?.billingCounty,
      },
      options: {
        categories: {
          replaceAll: categories?.map((obj) => obj.value),
        },
        skills: {
          replaceAll: skills?.map((obj) => obj.value),
        },
      },
      customText1: companyPayload?.linkedProfileUrl,
    };

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newCompanyPayloadCleaned = cleanPayload(newCompanyPayload);
    // newCompanyPayloadCleaned.address.address2 = null;
    newCompanyPayloadCleaned.address.countryCode = '';
    newCompanyPayloadCleaned.address.timezone = null;
    try {
      const { data } = isEditCompany
        ? await upladteBullhorn(companyId, newCompanyPayloadCleaned)
        : await insertBullhorn(newCompanyPayloadCleaned);
      // newCompanyPayload
      setAddressSearchText(companyPayload?.companyAddress);
      setValue('address1', companyPayload?.companyAddress);
      setValue('address2', companyPayload?.companySecondAddress);
      setValue('city', companyPayload?.city);
      setValue('county', companyPayload?.county);
      setValue('countySelect', companyPayload?.county);
      setValue('zip', companyPayload?.zip);
      setValue('state', companyPayload?.state);
      setValue('stateId', Number(companyPayload?.stateId));
      setValue('stateSelected', Number(companyPayload?.stateId));
      if (isEditCompany) {
        setValue('company', data?.result?.data?.name);
      } else {
        setValue('companySelect', data?.result?.changedEntityId);
        setValue('companyId', data?.result?.changedEntityId);
        setValue('company', data?.result?.data?.name);
        functionContactClient.contactSetStart(0);
        functionContactClient?.handleContactSearch(
          '',
          data?.result?.changedEntityId
        );
      }
      setListAddContactSelected([]);
      setJobDetailCompanyId(false);
      setIsAddCompany(false);
      setShowCompany(false);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
    }
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompanyPeople(watch('companyFinder'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFinder')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextCompany(watch('companyFindCompany'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('companyFindCompany')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFinderText(watch('locationFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedLocationFindCompanyText(watch('locationFindCompanyText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('locationFindCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustry(watch('industryFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryFinderText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextIndustryCompany(
        watch('industryKeywordCompanyText')
      );
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('industryKeywordCompanyText')]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setDebouncedSearchTextTitle(watch('titleFinderText'));
    }, 200);

    return () => clearTimeout(delayDebounceFn);
  }, [watch('titleFinderText')]);

  const { data: companyListPeople = [] } = useQuery(
    ['GET_LIST_COMPANY_PEOPLE', debouncedSearchTextCompanyPeople],
    async () => {
      const { data } = await getListCompany({
        searchText: debouncedSearchTextCompanyPeople,
      });
      return data?.organizations;
    },
    { enabled: !!debouncedSearchTextCompanyPeople }
  );

  const { data: locationListPeople = [] } = useQuery(
    ['GET_LIST_LOCATION_PEOPLE', debouncedLocationFinderText],
    async () => {
      if (onClose) return;
      const { data } = await employeeFinderSearchTag({
        searchText:
          debouncedLocationFinderText.trim() === ''
            ? ''
            : debouncedLocationFinderText,
        type: 'location',
      });
      return data?.tags;
    },
    { enabled: !!debouncedLocationFinderText }
  );

  const { data: titleList = [] } = useQuery(
    ['GET_LIST_TITLE', debouncedSearchTextTitle],
    async () => {
      const { data } = await employeeFinderSearchTag({
        searchText: debouncedSearchTextTitle,
        type: 'person_title',
      });
      return data?.tags;
    },
    { enabled: !!debouncedSearchTextTitle }
  );

  const timeoutRef = useRef(null);

  const handleEmailFinder = () => {
    setIsModalStaffOpen(true);
    setListEmployee([]);
    setListEmployeePagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
    setListCompanies([]);
    setListCompaniesPagination({
      page: 0,
      per_page: 0,
      total_entries: 0,
      total_pages: 0,
    });
  };

  useEffect(() => {
    if (!onClose) {
      // setIndustrySearchText(' ');
      // setCategorySearchText(' ');
      // setSkillSearchText(' ');
      functionContactClient.handleContactSearch(
        '',
        defaultDataCompany ? defaultDataCompany?.companyId : ''
      );
      setHandleCloseContact(false);
      setIsSearchCompany(false);
    }
  }, []);

  const handleBulkAddContact = async () => {
    await handleSubmitBulkAddContact();
  };

  const handleSubmitBulkAddContact = async () => {
    if (!getValues()?.clientContactBulk?.companyId) {
      notification.error({
        message: 'Company is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.status) {
      notification.error({
        message: 'Status is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.industries) {
      notification.error({
        message: 'Industries is required.',
      });
      return;
    }
    if (!getValues()?.clientContactBulk?.categories) {
      notification.error({
        message: 'Categories is required.',
      });
      return;
    }
    setLoadingBulkData(true);
    setIsLoadingAddContactFormBulk(true);
    const checkListContact = [];
    const dataToInsert = [];
    const unretrievableContactIds = [];
    for (let id of selectedRowKeys) {
      try {
        const { data } = await getLDetailEmployee({ employeeId: id });
        checkListContact.push(data);
        // setDataContacts(prevDataContacts => [data, ...prevDataContacts]);
      } catch (error) {
        unretrievableContactIds.push(id);
      }
    }

    for (let itemCheck of checkListContact) {
      try {
        const { data } = await searchBullhorn(
          'ClientContact',
          0,
          5,
          '',
          '',
          '',
          itemCheck?.email,
          null,
          true
        );
        if (data.result.length === 0) {
          dataToInsert.push(itemCheck);
        }
      } catch (error) {}
    }

    setLoadingBulkData(false);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
      skills,
      status,
      categories,
    } = getValues()?.clientContactBulk;

    const emailList = checkListContact
      .filter((contact) => contact.email !== null)
      .map((contact) => contact.email);
    if (emailList?.length == 0) {
      notification.warning({
        message: 'No email available',
      });
      setIsAddContactFormBulk(false);
      setIsLoadingAddContactFormBulk(false);
      setSelectedRowKeys([]);
      return;
    }

    const arrContact = dataToInsert?.map((item) => {
      const addressParts = [
        item?.city,
        item?.country,
        item?.county,
        item?.state,
      ].filter(Boolean);

      const combinedAddress = addressParts.join(', ');
      return {
        entityName: 'ClientContact',
        firstName: item?.first_name,
        // lastName: getValues()?.clientContact?.lastName,
        status: status.label,
        lastName: item?.last_name,
        owner: {
          id: userToSet?.user?.consultantId || userToSet?.consultantId,
        },
        // type: getValues()?.clientContact?.type,
        // secondaryOwners: {
        //   replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
        // },
        clientCorporation: {
          id: companyId,
        },
        // division: getValues()?.clientContact?.department,
        occupation: item?.title,
        email: item?.email,
        phone: item?.sanitized_phone,
        mobile: getValues()?.clientContactBulk?.mobilePhone,
        phone2: getValues()?.clientContactBulk?.otherPhone,
        fax: item?.clientContact?.fax,
        address: {
          countryID: getValues()?.clientContactBulk?.stateId,
          countryName: getValues()?.clientContactBulk?.state,
          state: getValues()?.clientContactBulk?.county,
          address1: getValues()?.clientContactBulk?.address,
          city: getValues()?.clientContactBulk?.city,
          zip: getValues()?.clientContactBulk?.zip,
        },
        businessSectors: {
          replaceAll: industries.map((obj) => obj.value),
        },
        // comments: getValues()?.clientContact?.generalCommets,
        // referredByPerson: {
        //   id: getValues().clientContact.referredById || null,
        // },
        skills: {
          replaceAll: skills?.map((obj) => obj.value),
        },
        categories: {
          replaceAll: categories?.map((obj) => obj.value),
        },
        options: {
          skills: {
            replaceAll: skills?.map((obj) => obj.value),
          },
          categories: {
            replaceAll: categories?.map((obj) => obj.value),
          },
        },
        name: `${item?.first_name} ${item?.last_name}`,
        customText1: item?.linkedin_url,
      };
    });

    try {
      for (let itemI of arrContact) {
        try {
          const { data } = await insertBullhorn(itemI);
        } catch (error) {}
      }
    } catch (e) {
      // notification.error({
      //   message: "Some things went wrong"
      // })
    }

    handleGenerateNotificationBulkAddContact(
      arrContact,
      selectedRowKeys,
      unretrievableContactIds
    );
    setIsAddContactFormBulk(false);
    setIsLoadingAddContactFormBulk(false);
    setSelectedRowKeys([]);
  };

  const handleRenderModal = () => {
    return (
      <>
        {/* Modal show company detail */}

        {/* Modal show contact detail */}

        {isAddContactFormBulk && (
          <Modal
            className="customize-contact-form"
            width={1000}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            // title="Add Contact Form"
            open={isAddContactFormBulk}
            onCancel={() => {
              setIsAddContactFormBulk(false);
              // handleResetFormAddContact();
            }}
            footer={false}
          >
            <Form layout="vertical">
              <div className="text-xl mx-6 pb-3 pt-5 font-semibold text-base border-b-2 border-b-[#17c1e8]">
                <span>{COMMON_STRINGS.BULK_ADD_TO_BULLHORN}</span>
              </div>
              <BullhornBulkAddContactModal
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                handleCloseClient={handleCloseClient}
                setHandleCloseClient={setHandleCloseClient}
                handleCloseContact={handleCloseContact}
                setHandleCloseContact={setHandleCloseContact}
                detailDataContact={detailDataContact}
                flagDetailContact={flagDetailContact}
                listEmployee={listEmployee}
                selectedRowKeys={selectedRowKeys}
                isAddContactFormBulk={isAddContactFormBulk}
                loadingGetBulkData={loadingGetBulkData}
                setLoadingBulkData={setLoadingBulkData}
                setDataContacts={setDataContacts}
                dataContacts={dataContacts}
              />
              <div className="pl-6 left-0 bottom-0 w-full pb-4">
                <div
                  className="flex gap-4 mr-8"
                  style={{ float: 'right', paddingBottom: '10px' }}
                >
                  <Button
                    onClick={() => {
                      console.log('colasd');
                      setIsAddContactFormBulk(false);
                      // handleResetFormAddContact();
                    }}
                    className={`bg-[#BEDAFD33] `}
                  >
                    Cancel
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={handleBulkAddContact}
                    type="primary"
                    loading={isLoadingAddContactFormBulk}
                    className={` `}
                    disabled={loadingGetBulkData}
                  >
                    Save
                  </Button>
                </div>
              </div>
              <div style={{ clear: 'both' }}></div>
            </Form>
          </Modal>
        )}

        {/* Modal find email by link = list email */}
        {/* <Modal
          width={1600}
          // style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          // title="List Staff"
          className="top-10"
          rootClassName="contact-modal-container"
          open={isModalStaffOpen}
          onCancel={() => {
            setIsModalStaffOpen(false);
            setIsLoadingEmployee(false);
            setValue('searchCompany', '');
            setValue('searchPeople', '');
            resetAllPeople();
            resetAllCompany();
          }}
          footer={false}
        >
          <Tabs
            defaultActiveKey="1"
            onChange={(e) => handleChangeTab(e)}
            items={itemsTableEmailFinder}
          />
          <Button
            className="flex ml-auto mt-3"
            htmlType="button"
            onClick={() => {
              setIsModalStaffOpen(false);
              setIsLoadingEmployee(false);
              setValue('searchCompany', '');
              setValue('searchPeople', '');
              resetAllPeople();
              resetAllCompany();
            }}
            type="primary"
          >
            Done
          </Button>
        </Modal> */}

        <ContactFinderForm
          functionContactClient={functionContactClient}
          handleSubmit={handleSubmit}
          control={control}
          setValue={setValue}
          getValues={getValues}
          job={job}
          watch={watch}
          isModalStaffOpen={isModalStaffOpen}
          setIsModalStaffOpen={setIsModalStaffOpen}
          listEmail={listEmail}
          setListEmail={setListEmail}
          setHandleCloseContact={setHandleCloseContact}
          setIsSearchCompany={setIsSearchCompany}
          isAddContactForm={isAddContactForm}
          setIsAddContactForm={setIsAddContactForm}
          isAddContactFormBulk={isAddContactFormBulk}
          setIsAddContactFormBulk={setIsAddContactFormBulk}
          showDetailContact={showDetailContact}
          setShowDetailContact={setShowDetailContact}
          setDetailContactStaff={setDetailContactStaff}
          detailContactStaff={detailContactStaff}
          setDetailDataContact={setDetailDataContact}
        />

        {/* Modal send Mail */}
        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
              <div style={{ marginRight: '50px' }}>
                <label
                  style={{ fontSize: '14px', marginRight: '8px' }}
                  for="configSequenceEmail"
                >
                  Don&apos;t send Sequence Email
                </label>
                <Checkbox
                  id="configSequenceEmail"
                  style={{ marginRight: '20px' }}
                  onClick={() => {
                    setSequenceStatus(!sequenceStatus);
                    setValue('sequenceStatus', !sequenceStatus);
                  }}
                />
                <label
                  style={{ fontSize: '14px', marginRight: '10px' }}
                  for="configEmail"
                >
                  Don&apos;t send Email
                </label>
                <Checkbox
                  checked={checkBoxStatus}
                  onClick={() => {
                    setCheckBoxStatus(!checkBoxStatus);
                  }}
                  id="configEmail"
                />
              </div>
            </div>
          }
          open={openModalSendEmail}
          onCancel={() => {
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmail}
                setOpenSendEmail={setOpenSendEmail}
                listAddContactSelected={listAddContactSelected}
                functionContactClient={functionContactClient}
                job={job}
                setHaveSendJob={setHaveSendJob}
                setIsLockedSendJob={setIsLockedSendJob}
                isLockedSendJob={isLockedSendJob}
                checkBoxStatus={checkBoxStatus}
                sequenceStatus={sequenceStatus}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                fromManualCreate={fromManualCreate}
                isFromVacancy={true}
                newUpdatedSequence={true}
              />
            </Form>
          </div>
        </Modal>

        {/* CREATE CONTACT */}
        {isAddContactForm && (
          <Modal
            className="customize-contact-form"
            width={1000}
            style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
            // title={`${flagEditContact ? 'Edit Contact Form' : 'Add Contact Form'}`}
            open={isAddContactForm}
            onCancel={() => {
              setIsAddContactForm(false);
              setFlagEditContact(false);
              handleResetFormAddContact();
            }}
            footer={false}
          >
            <Form layout="vertical">
              <div className="text-xl mx-6 pb-3 pt-5 font-semibold text-base border-b-2 border-b-[#17c1e8]">
                <span>
                  {flagEditContact ? 'Edit Contact Form' : 'Add Contact Form'}
                </span>
              </div>
              <BullhornSubmissionContact
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                handleCloseClient={handleCloseClient}
                setHandleCloseClient={setHandleCloseClient}
                handleCloseContact={handleCloseContact}
                setHandleCloseContact={setHandleCloseContact}
                detailDataContact={detailDataContact}
                flagDetailContact={flagDetailContact}
                flagEditContact={flagEditContact}
                fromLead={true}
              />
              <div className="pl-6 left-0 bottom-0 w-full pb-4">
                <div className="flex gap-4 mr-8">
                  <Button
                    onClick={() => {
                      setIsAddContactForm(false);
                      setFlagEditContact(false);
                      handleResetFormAddContact();
                    }}
                    className={`bg-[#BEDAFD33] `}
                  >
                    Cancel
                  </Button>
                  <Button
                    htmlType="button"
                    onClick={handleSubmitAddContact}
                    type="primary"
                    disabled={isSubmitForm}
                    loading={isSubmitForm}
                    className={` `}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </Form>
          </Modal>
        )}

        <Modal
          width={800}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>New Email</div>
              <div style={{ marginRight: '50px' }}>
                {/* <label style={{fontSize: "14px", marginRight: "8px"}} for="configEmail">Don't send Sequence Email</label>
                <Checkbox id='configSequenceEmail' style={{ marginRight: "20px"}}/>
                <label style={{fontSize: "14px", marginRight: "10px"}} for="configEmail">Don't send Email</label>
                <Checkbox checked={checkBoxStatus} onClick={() => {setCheckBoxStatus(!checkBoxStatus)}} id='configEmail'/> */}
              </div>
            </div>
          }
          open={openModalSendEmailContact}
          onCancel={() => {
            setOpenSendEmail(false);
          }}
          footer={false}
        >
          <div>
            <Form layout="vertical">
              <BullhornSendEmail
                watch={watch}
                control={control}
                setValue={setValue}
                getValues={getValues}
                sendToEmail={getValues()?.email}
                mailTitle={getValues()?.jobtitle}
                openModalSendEmail={openModalSendEmailContact}
                setOpenSendEmail={setOpenSendEmailContact}
                listAddContactSelected={[getValues()?.optionContactSelect]}
                setNumberStep={setNumberStep}
                numberStep={numberStep}
                inputNumberStep={inputNumberStep}
                setInputNumberStep={setInputNumberStep}
                // functionContactClient={functionContactClient}
                // job={lead}
                fromSequenseEmail={true}
                setEmailConfigData={setEmailConfigData}
                emailConfigData={emailConfigData}
                newUpdatedSequence={true}
                // setHaveSendJob={setHaveSendJob}
                // setIsLockedSendJob={setIsLockedSendJob}
                // isLockedSendJob={isLockedSendJob}
                // checkBoxStatus={checkBoxStatus}
              />
            </Form>
          </div>
        </Modal>
        <ModalShowListExitSequence
          setOpen={setOpenExistingSequence}
          open={openExistingSequence}
          onClose={onClose}
        />
        <ModalListUserGroup
          setOpen={setOpenExistingUserGroup}
          open={openExistingUserGroup}
        />
        <Row></Row>
      </>
    );
  };

  const handleRenderInfo = () => {
    return (
      <div>
        <Form.Item
          label={
            <p>
              {/* <span className="text-red-600">*</span>  */}
              Title
            </p>
          }
          name="jobtitle"
        >
          <Controller
            render={({ field }) => (
              <>
                <Input
                  showCount
                  maxLength={100}
                  className="!rounded-xl"
                  disabled={showCompany}
                  required
                  max={100}
                  value={getValues('jobtitle')}
                  {...field}
                  onChange={async (e) => {
                    setValue('jobtitle', e.target.value);
                  }}
                  suffix={
                    getValues().jobtitle !== '' &&
                    getValues().jobtitle &&
                    getValues().jobtitle?.length < 100 ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
                {/* <div
                  style={{
                    marginTop: '10px',
                    marginLeft: '10px',
                    fontSize: '12px',
                    color: '#fff',
                  }}
                >
                  {' '}
                  <i>
                    note: Maximum length of title is 100. Please have an
                    adjustment
                  </i>
                </div> */}
              </>
            )}
            name="jobtitle"
            control={control}
          />
        </Form.Item>
        <div>
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Company
              </p>
            }
            name="company"
            className="mb-12"
          >
            <Controller
              render={({ field }) => (
                <>
                  <BHCompanySelect
                    {...field}
                    watch={watch}
                    disabled={showCompany}
                    onMouseDown={(e) => {
                      setHandleCloseClient(true);
                    }}
                    setIsEditCompany={setIsEditCompany}
                    setShowCompany={setShowCompany}
                    setJobDetailCompanyId={setJobDetailCompanyId}
                    functionContactClient={functionContactClient}
                    setValue={setValue}
                    setHandleCloseClient={setHandleCloseClient}
                    setIsAddCompany={setIsAddCompany}
                    setIsSearchCompany={setIsSearchCompany}
                    getValues={getValues}
                    // handleChangeGetSimilar={handleChangeGetSimilar}
                    functionCompany={functionCompany}
                    isAddCompany={isAddCompany}
                    isSearchCompany={isSearchCompany}
                    open={handleCloseClient}
                  />
                </>
              )}
              name="company"
              control={control}
            />
          </Form.Item>

          {showCompany && (
            <Form.Item>
              <Card className=" max-w-full mx-auto bg-[#BEDAFD33] shadow-lg rounded-2xl overflow-hidden hover:shadow-xl">
                <div className="w-full bg-cyan-600 pl-6 py-3 font-semibold text-base">
                  <span className="text-white">
                    {isEditCompany ? 'Edit' : 'Add New'} Company
                  </span>
                </div>

                {/* <Divider
                    className=" font-semibold text-base sticky top-0 z-50 w-full"
                    orientation="left"
                  >
                    <span className="text-blue-600">
                      {isEditCompany ? 'Edit' : 'Add'} Companyhyhy
                    </span>
                  </Divider> */}
                <div className="p-6">
                  <div className="mb-6 border-b-2 border-b-white pb-2">
                    <span className="font-medium text-base text-white ">
                      {isEditCompany ? 'Edit' : 'Add'} Company
                    </span>
                  </div>
                  <BullHornJobSubmissionCompany
                    isEditCompany={isEditCompany}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    handleCloseClient={handleCloseClient}
                    setHandleCloseClient={setHandleCloseClient}
                    watch={watch}
                    dataSearch={searchData?.search}
                  />
                  <div className="left-0 bottom-0 w-full">
                    <div className="flex gap-4 mr-8">
                      <Button
                        onClick={() => {
                          setShowCompany(false);
                          functionCompany.companySetStart(0);
                          setValue('companySelect', null);
                          setHandleCloseClient(true);
                          setValue('companyId', null);
                          setValue('company', '');
                          functionContactClient.contactSetOptions([]);
                          setValue('contactId', null);
                          setValue('contact', null);
                          setValue('contactSelect', null);
                          setValue('email', '');
                          functionCompany.handleCompanySearch(' ');
                          setIsAddCompany(false);
                        }}
                        className={`bg-[#BEDAFD33] `}
                      >
                        Cancel
                      </Button>
                      <Button
                        htmlType="button"
                        onClick={handleSubmitCompany}
                        type="primary"
                        className={`bg-white text-cyan-600 `}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </Form.Item>
          )}
        </div>
        <div>
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Contact
              </p>
            }
            name="contact"
            className="mb-12"
          >
            <Controller
              render={({ field }) => (
                <>
                  <BHContactSelect
                    watch={watch}
                    disabled={showCompany || jobDetailCompanyId}
                    setListAddContactSelected={setListAddContactSelected}
                    setHandleCloseContact={setHandleCloseContact}
                    setIsAddContact={setIsAddContact}
                    listAddContactSelected={listAddContactSelected}
                    handleEmailFinder={handleEmailFinder}
                    setStartCreateContact={setStartCreateContact}
                    functionCompany={functionCompany}
                    functionContactClient={functionContactClient}
                    getValues={getValues}
                    setValue={setValue}
                    {...field}
                    open={handleCloseContact}
                    onMouseDown={(e) => {
                      setHandleCloseContact(true);
                    }}
                  />
                </>
              )}
              name="contact"
              control={control}
            />
          </Form.Item>
        </div>
        <Form.Item
          label={
            <p>
              {/* <span className="text-red-600">*</span>  */}
              Status
            </p>
          }
          name="status"
          className="mb-12"
        >
          <Controller
            render={({ field }) => (
              <AutoComplete
                disabled={showCompany || jobDetailCompanyId}
                {...field}
                onSelect={(value) => {
                  setValue('statusSelect', value);
                }}
                onSearch={() => {
                  setValue('statusSelect', null);
                }}
                options={[
                  'Lead',
                  'Accepting Candidates',
                  'HOT Lead',
                  'Offer Out',
                  'Placed',
                  'Filled by Client',
                  'Lost to Competitor',
                  'Archived',
                  'On Hold',
                  'Cancelled',
                ].map((val) => ({ label: val, value: val }))}
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) !== -1
                }
              >
                <Input
                  required
                  suffix={
                    getValues().statusSelect !== '' &&
                    getValues().statusSelect ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
              </AutoComplete>
            )}
            name="status"
            control={control}
          />
        </Form.Item>
        <Form.Item
          label={
            <p>
              <span className="text-red-600">*</span>
              Lead Sheet
            </p>
          }
          name="leadSheetId"
          className="mb-12"
        >
          <Controller
            render={({ field }) => (
              <Select
                suffixIcon={
                  <div className="mt-[9px]">
                    {getValues('leadSheetId') ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )}
                  </div>
                }
                defaultValue={{
                  label: leadSheets?.find(
                    (item) => item?.id === getValues('leadSheetId')
                  )?.name,
                  value: getValues('leadSheetId'),
                }}
                loading={leadSheetLoading}
                {...field}
                disabled={showCompany}
                placeholder="Select a Lead Sheet"
                dropdownRender={(menu) => (
                  <div className="w-full">
                    <div className="grid grid-cols-10 gap-4 py-2 px-3">
                      <Input
                        addonBefore={'OPPORTUNITY :'}
                        className="col-span-7"
                        placeholder="Please enter sheet name"
                        ref={inputLeadSheetRef}
                        value={newLeadSheetName}
                        onChange={onNewLeadSheetNameChange}
                        onKeyDown={(e) => e.stopPropagation()}
                      />
                      <Button
                        loading={leadSheetLoading}
                        className="col-span-3 font-medium"
                        type="default"
                        icon={<PlusOutlined />}
                        onClick={addNewLeadSheet}
                      >
                        Add New Sheet
                      </Button>
                    </div>
                    <Divider
                      style={{
                        margin: '8px 0',
                      }}
                    />
                    {menu}
                  </div>
                )}
                options={leadSheets.map((item) => ({
                  label: item?.name,
                  value: item?.id,
                }))}
                onChange={onChangeLeadSheet}
              />
            )}
            name="leadSheetId"
            control={control}
          />
        </Form.Item>
        <Form.Item label="Deal Value" name="dealValue">
          <Controller
            render={({ field }) => (
              <InputNumber
                {...field}
                onChange={(value) => {
                  field.onChange(value);
                }}
                disabled={showCompany || jobDetailCompanyId}
                style={{
                  width: '100%',
                }}
              />
            )}
            name="dealValue"
            control={control}
          />
        </Form.Item>
        <Form.Item
          label={
            <p>
              {/* <span className="text-red-600">*</span>  */}
              Associated Lead
            </p>
          }
          name="associatedLead"
        >
          <Controller
            render={({ field }) => (
              <Input
                {...field}
                disabled={showCompany || jobDetailCompanyId}
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            )}
            name="associatedLead"
            control={control}
          />
        </Form.Item>
        <Form.Item
          label={
            <p>
              {/* <span className="text-red-600">*</span>  */}
              Expected Close Date
            </p>
          }
          name="type"
        >
          <Controller
            render={({ field }) => (
              <DatePicker disabled={showCompany || jobDetailCompanyId} />
            )}
            name="associatedLead"
            control={control}
          />
        </Form.Item>
        <Form.Item label="Description" name="description">
          <Controller
            render={({ field }) => (
              <Input.Group className="flex gap-2">
                <Input.TextArea
                  style={{ cursor: 'pointer' }}
                  disabled={showCompany || jobDetailCompanyId}
                  rows={10}
                  {...field}
                  resize
                  onChange={async (e) => {
                    setValue('description', e.target.value);
                  }}
                />
              </Input.Group>
            )}
            name="description"
            control={control}
          />
        </Form.Item>
      </div>
    );
  };

  return (
    <Form onFinish={handleSubmitBullhorn} layout="vertical">
      {!_get(configForm, 'data') && (
        <>
          <LoadingAdvanced isSkeleton />
          <LoadingAdvanced isSkeleton />
          <LoadingAdvanced isSkeleton />
        </>
      )}
      {handleRenderInfo()}

      {handleRenderModal()}
    </Form>
  );
}

export default BullHornOpportunitySubmissionForm;
