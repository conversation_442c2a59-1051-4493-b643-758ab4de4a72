.ck {
  &.ck-content {
    ul {
      list-style-type: initial;
      padding-left: 40px;

      li {
        list-style-type: initial;
      }
    }

    div p {
      margin: 0; // Reset the margin for p tags inside div elements
    }
  }

  &-editor__editable_inline {
    padding: 20px 30px !important;
  }
}
// Customize new BH Submission form
.bullhorn-job-submission-form-container {
  .ant-form-item .ant-form-item-label label {
    font-size: medium !important;
  }
  border-radius: 1rem;
  .ant-modal-content {
    background: transparent;
  }
  .ant-modal-title {
    background-color: #0891b2;
  }
  label {
    font-size: medium;
    font-weight: 600;
    // color: white !important;
  }
  .ant-radio-wrapper {
    font-size: small !important;
    font-weight: 100;
    // color: white !important;
  }
  .ant-form-item-label {
    padding: 0;
  }
  textarea[name='description'] {
    border-radius: 1rem !important;
    resize: none;
    // color: #576f89;
  }
  input,
  .ant-select-selector,
  .ant-input,
  .ant-input-group {
    // color: #576f89;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .ant-picker-input input,
  .ant-input-affix-wrapper,
  .ant-input-group textarea {
    border-width: 2px !important;
  }
  .ant-input-affix-wrapper,
  .ant-input-number,
  .ant-picker,
  .ant-select-selector,
  .ant-input {
    border-radius: 0.4rem !important;
  }
  .ant-select-selector {
    min-height: 2.7rem !important;
  }
  .ant-divider-horizontal {
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    // color: white;
  }
  .ant-divider::before,
  .ant-divider::after {
    border-top: 1px solid white;
  }
  // .ant-input-affix-wrapper,
  // .ant-input-number,
  // .ant-picker,
  // .ant-select-selector {
  //   background-color: white !important;
  // }

  .compensation-fees-container {
    .ant-input-number {
      width: 100%;
      padding-top: 0.14rem !important;
      padding-bottom: 0.14rem !important;
    }
  }

  span.ant-input-affix-wrapper.ant-select-selection-search-input,
  .ant-input-affix-wrapper {
    padding: 1px 11px !important;
  }

  .ant-form-item-label {
    margin-bottom: 0.3rem;
  }
  .ant-switch-inner {
    // border: 2px solid white;
    // background-color: white;
  }
  .ant-switch-handle::before {
    background-color: #0891b2;
  }

  .ant-radio-checked .ant-radio-inner {
    // border-color: white !important ;
  }

  .ant-radio-inner {
    background-color: #0891b2;
  }

  .button.ant-btn {
    background-color: white;
  }

  .ant-select-selector-disabled::placeholder,
  .ant-input-disabled,
  .ant-input-group-disabled,
  .ant-select-disabled,
  .ant-input[disabled]::placeholder,
  .ant-input-number-disabled,
  .ant-input-number-disabled::placeholder,
  .ant-picker-input input[disabled],
  .ant-picker-input input[disabled]::placeholder,
  .ant-select-selection-search-input[disabled],
  .ant-select-selection-search-input[disabled]::placeholder,
  .ant-select-disabled .ant-select-selection-placeholder {
    // color: white !important;
  }

  // Styled for Add new company form
  .ant-card .ant-card-body {
    padding: 0 !important;
  }
}

// .customize-contact-form {
//   border-radius: 1rem;
//   .ant-form {
//     background-color: #0891b2;
//   }
//   .ant-modal-content {
//     padding: 0 !important;
//     // border: 1px solid #BEDAFD;
//   }
//   label {
//     font-size: medium;
//     font-weight: 500;
//     color: white !important;
//   }
//   textarea[name='description'] {
//     border-radius: 1rem !important;
//     resize: none;
//     color: #576f89;
//   }
//   .ant-input-affix-wrapper,
//   .ant-input-number,
//   .ant-picker,
//   .ant-select-selector,
//   .ant-input {
//     border-radius: 1rem !important;
//   }
//   input,
//   .ant-select-selector,
//   .ant-input,
//   .ant-input-group {
//     color: #576f89;
//     padding-top: 0.5rem !important;
//     padding-bottom: 0.5rem !important;
//   }
//   .ant-select-selector {
//     height: 2.9rem !important;
//   }

//   span.ant-input-affix-wrapper.ant-select-selection-search-input,
//   .ant-input-affix-wrapper {
//     padding: 1px 11px !important;
//   }

//   textarea[name='description'] {
//     border-radius: 1rem !important;
//     resize: none;
//     color: #576f89;
//   }

//   .ant-modal-content {
//     background-color: #0891b2 !important;
//   }
//   .ant-btn-primary {
//     background-color: white !important;
//   }

//   .ant-card .ant-card-body {
//     padding: 0 !important;
//   }
//   .ant-btn-primary {
//     background-color: white !important;
//   }
// }

.customize-contact-form {
  .ant-modal-content {
    padding: 0 !important;
  }
  label {
    font-size: medium;
    font-weight: 500;
  }
  textarea[name='description'] {
    border-radius: 0.5rem !important;
    resize: none;
    color: #576f89;
  }
  input,
  .ant-select-selector,
  .ant-input,
  .ant-input-group {
    color: #576f89;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .ant-select-selector {
    min-height: 2.9rem;
  }

  span.ant-input-affix-wrapper.ant-select-selection-search-input,
  .ant-input-affix-wrapper {
    padding: 1px 11px !important;
  }

  .ant-card .ant-card-body {
    padding: 0 !important;
  }
}

.center-select-row-customize .ant-select-arrow {
  margin-top: 0 !important;
}

label[for='sendMail.listEmail'] {
  width: 100% !important;
}

.add-contact-btn .ant-spin-dot-item {
  background-color: white;
}

// Redesign sequence mails

.ant-popconfirm-message-icon {
  display: none;
}

.preview-mail-content-container .ck .ck-sticky-panel__content {
  display: none;
}
.preview-mail-content-container {
  max-height: 30rem;
  overflow: auto;
}

// .ant-form-vertical {
//   text-align: -webkit-center;
// }

.customize-options .ant-popconfirm-buttons {
  display: none;
}

// .ant-drawer .ant-drawer-mask {
//   background: rgba(0, 0, 0, 0);
// }

textarea {
  resize: none;
  background: white;
}

.ant-drawer-mask {
  // display: none;
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(4px);
}

// customize UI in sequence detail

.mail-generator-container .ant-select-selector,
.mail-generator-container .ant-input {
  border-radius: 0px !important;
}

.mail-generator-container .ant-select-selector {
  // background-color: #969696 !important;
  background-color: #e5e7eb !important;
  border: none !important;
}
.mail-generator-container .ant-select-selection-item {
  font-weight: 600 !important;
}

.mail-generator-content-container .ant-spin .ant-spin-dot-spin {
  animation-duration: 0.5s !important;
}
.ant-drawer-content-wrapper {
  transform: translateX(0) !important;
}

.remove-antd-delete-btn .ant-modal-close {
  display: none !important;
}

.customize-linkedin-request {
  .ant-popconfirm-title {
    display: none !important;
  }
  .ant-btn-default {
    display: none !important;
  }
}

#inmail-content-input-area {
  height: 100% !important;
}

.custom-collapse .ant-collapse {
  height: 55vh;
  overflow-x: scroll;
}

.trigger-container .ant-popover-inner {
  padding: 0 !important;
}
.trigger-container .ant-picker-large {
  padding-left: 0 !important;
}

// step status css

@media (min-width: 500px) {
  .ribbons-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
}

.ribbon {
  position: relative;
  background-size: cover;
}
.ribbon-base {
  position: absolute;
  top: -6.1px;
  right: 61px;
  z-index: 2;
  color: white;
}

.ribbon-base span:before,
.ribbon-base span:after {
  position: absolute;
  content: '';
}
// PENDING ribbon
.ribbon-pending span {
  position: relative;
  display: block;
  text-align: center;
  background: #fcde70;
  font-size: 12px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 63px;
  font-weight: 700;
}

.ribbon-pending span:before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  background: #fcde70;
}
.ribbon-pending span:after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
  background: #d8a25e;
}

// SENT
.ribbon-sent span {
  position: relative;
  display: block;
  text-align: center;
  background: #c1fceb;
  font-size: 12px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 63px;
  font-weight: 700;
  color: #00996d;
}

.ribbon-sent span:before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  background: #e6fff8;
}
.ribbon-sent span:after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
  background: #00996d;
}

// ERROR

.ribbon-error span {
  position: relative;
  display: block;
  text-align: center;
  background: #f8463f;
  font-size: 12px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 63px;
  font-weight: 700;
}

.ribbon-error span:before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  background: #f8aeae;
}
.ribbon-error span:after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
  background: #c02031;
}

// notsent

.ribbon-notsent span {
  position: relative;
  display: block;
  text-align: center;
  background: #f8463f;
  font-size: 12px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 63px;
  font-weight: 700;
}

.ribbon-notsent span:before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  background: #f8aeae;
}
.ribbon-notsent span:after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
  background: #c02031;
}

// STOPPED

.ribbon-stopped span {
  position: relative;
  display: block;
  text-align: center;
  background: #f8aeae;
  font-size: 12px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 63px;
  font-weight: 700;
  color: #b63838;
}

.ribbon-stopped span:before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  background: #f8aeae;
}
.ribbon-stopped span:after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
  background: #b63838;
}

.sequence-component-container .ant-btn-text:hover {
  background-color: #e6f4ff !important;
  border: 1px solid #1677ff !important;
  transition-delay: 100ms;

  .anticon {
    @apply text-[#1677ff] delay-100;
  }
}

.linkedin-step-participant-container .ant-collapse-header {
  background-color: #c1d8c3 !important;
  border-radius: 0 !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
}

#expand-container {
  overflow: hidden;
}

#expand-contract {
  margin-top: -130%;
  transition: all 1s;
}

#expand-contract.expanded {
  margin-top: 0;
}

#expand-container-bulk-enrich {
  overflow: hidden;
}

#expand-contract-bulk-enrich {
  margin-top: -130%;
  transition: all 1s;
}

#expand-contract-bulk-enrich.expanded {
  margin-top: 0;
}

.stop-rule-container .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #f53939;
  border-color: #f53939;
}

// Linkedin step separate
.Animated-Radial-Menu {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
}
.Animated-Radial-Menu .menu-linkedin {
  position: relative;
  // width: 280px;
  // height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.Animated-Radial-Menu .menu-linkedin .toggle {
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.Animated-Radial-Menu .menu-linkedin li {
  position: absolute;
  left: 0;
  list-style: none;
  // transition: 0.3s;
  transition-delay: calc(0.04s * var(--i));
  transform: rotate(0deg) translateX(80px);
  transform-origin: 110px;
  margin-left: -3rem;
}
.Animated-Radial-Menu {
  transition: 0.3s;
  transition-delay: 0.3s;
}

.Animated-Radial-Menu .menu-linkedin li a {
  display: flex;
  // justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  color: var(--clr);
  // border: 1px solid var(--clr);
  // border-radius: 50%;
  font-weight: 500;
  font-size: 1em;
  transform: rotate(calc(-45deg * var(--i)));
  // transition: 0.3s;
}
// .Animated-Radial-Menu .menu-linkedin li a:hover {
//   background: var(--clr);
//   color: #333;
//   box-shadow:
//     0 0 10px var(--clr),
//     0 0 30px var(--clr),
//     0 0 50px var(--clr);
// }
.Animated-Radial-Menu,
.Animated-Radial-Menu .menu-linkedin .transform-btn {
  // transition: transform 0.5s;
}
.Animated-Radial-Menu .menu-linkedin.active .toggle .transform-btn {
  transform: rotate(360deg);
}
.Animated-Radial-Menu .menu-linkedin.active li {
  transform: rotate(calc(45deg * var(--i))) translateX(0px);
} /*# sourceMappingURL=Animated-Radial-Menu.css.map */

.add-new-step-container:has(.active) .add-new-step-button {
  visibility: hidden;
}

// .add-new-step-button {
//   transition: all 0.1s;
// }
.ck-body-wrapper {
  position: fixed;
  z-index: 1300;
}

.ck-dropdown-menu-list {
  position: relative;
}

.option-container .merge-tag-display {
  @apply outline outline-[1px] outline-blue-400 hover:outline-blue-600 hover:outline-[2px] px-1;
}

.ck-dropdown__panel_se .ck.ck-toolbar .ck-toolbar__items:has(.col-dropdown) {
  flex-direction: column;
  width: 100%;
}

div[aria-label='Dropdown toolbar'] .ck-toolbar__items {
  max-width: 20rem !important;
  flex-wrap: wrap !important;
}

.col-dropdown {
  width: 100% !important;
  justify-content: start;
}

// .job-detail-container:has(.ant-drawer-content-wrapper){
//   width: 45rem !important;
// }

.full-name-cell-container {
  .button {
    position: relative;
    padding: 5px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .button:active {
    transform: scale(0.96);
  }

  .button:before,
  .button:after {
    position: absolute;
    content: '';
    width: 150%;
    left: 50%;
    height: 100%;
    transform: translateX(-50%);
    z-index: -1000;
    background-repeat: no-repeat;
  }

  .button:hover:before {
    top: -70%;
    background-image: radial-gradient(circle, #a89215 20%, transparent 20%),
      radial-gradient(circle, transparent 20%, #13a5be 20%, transparent 30%),
      radial-gradient(circle, #a3b82d 20%, transparent 20%),
      radial-gradient(circle, #590cbe 20%, transparent 20%),
      radial-gradient(circle, transparent 10%, #bd1717 15%, transparent 20%),
      radial-gradient(circle, #2a7ce8 20%, transparent 20%),
      radial-gradient(circle, #30e82a 20%, transparent 20%),
      radial-gradient(circle, #e92c75 20%, transparent 20%),
      radial-gradient(circle, #914fe7 20%, transparent 20%);
    background-size:
      10% 10%,
      20% 20%,
      15% 15%,
      20% 20%,
      18% 18%,
      10% 10%,
      15% 15%,
      10% 10%,
      18% 18%;
    background-position: 50% 120%;
    animation: greentopBubbles 0.6s ease;
  }

  @keyframes greentopBubbles {
    0% {
      background-position:
        5% 90%,
        10% 90%,
        10% 90%,
        15% 90%,
        25% 90%,
        25% 90%,
        40% 90%,
        55% 90%,
        70% 90%;
    }

    50% {
      background-position:
        0% 80%,
        0% 20%,
        10% 40%,
        20% 0%,
        30% 30%,
        22% 50%,
        50% 50%,
        65% 20%,
        90% 30%;
    }

    100% {
      background-position:
        0% 70%,
        0% 10%,
        10% 30%,
        20% -10%,
        30% 20%,
        22% 40%,
        50% 40%,
        65% 10%,
        90% 20%;
      background-size:
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%;
    }
  }

  .button:hover::after {
    bottom: -70%;
    background-image: radial-gradient(circle, #ff93db 20%, transparent 20%),
      radial-gradient(circle, #2ae8df 20%, transparent 20%),
      radial-gradient(circle, transparent 10%, #71ffbd 15%, transparent 20%),
      radial-gradient(circle, #2a9ce8 20%, transparent 20%),
      radial-gradient(circle, #7814fc 20%, transparent 20%),
      radial-gradient(circle, #73e4f8 20%, transparent 20%),
      radial-gradient(circle, #f8d3a9 20%, transparent 20%);
    background-size:
      15% 15%,
      20% 20%,
      18% 18%,
      20% 20%,
      15% 15%,
      20% 20%,
      18% 18%;
    background-position: 50% 0%;
    animation: greenbottomBubbles 0.6s ease;
  }

  @keyframes greenbottomBubbles {
    0% {
      background-position:
        10% -10%,
        30% 10%,
        55% -10%,
        70% -10%,
        85% -10%,
        70% -10%,
        70% 0%;
    }

    50% {
      background-position:
        0% 80%,
        20% 80%,
        45% 60%,
        60% 100%,
        75% 70%,
        95% 60%,
        105% 0%;
    }

    100% {
      background-position:
        0% 90%,
        20% 90%,
        45% 70%,
        60% 110%,
        75% 80%,
        95% 70%,
        110% 10%;
      background-size:
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%,
        0% 0%;
    }
  }
}

// Flip card
.flip-card {
  background-color: transparent;
  width: 25rem;
  height: 4rem;
  perspective: 1000px;
}

.title {
  font-size: 1.5em;
  font-weight: 900;
  text-align: center;
  margin: 0;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.1s;
  transform-style: preserve-3d;
}

.flip-card-hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  box-shadow: 0 8px 14px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.flip-card-front {
  // background: linear-gradient(120deg, #7AB2D3 40%, #B9E5E8 88%,
  //    #DFF2EB 40%, #7AB2D3 48%);
  // color: #4A628A;
  background: #2596be;
  color: white;
}

.flip-card-back {
  background: #2596be;
  color: white;
  transform: rotateY(180deg);
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: auto;
}
