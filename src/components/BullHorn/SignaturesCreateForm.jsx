// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { Button, Input, Modal, Tabs, message, notification } from 'antd';
import { Controller, Form, useForm } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import {
  convertToBase64,
  createSignatures,
  deleteSignature,
  getSignatures,
  removeSignaturesDefault,
  updateSignaturesDefault,
} from '../../services/signatures';
import { CheckOutlined, DeleteOutlined, FlagOutlined } from '@ant-design/icons';
import SignaturesEditForm from './SignatureEditForm';
import useEditUser from '../../hooks/useEditUser';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { decodeHtmlEntities, removeEmptyNodes } from '../../utils/common';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from './BullhornSendEmailModal';

export async function replaceImageSrcsWithBase64(text) {
  const imgTags = text.match(/<img [^>]*src="https:[^"]*"[^>]*>/g);
  if (!imgTags) return text;

  for (const imgTag of imgTags) {
    const urlMatch = imgTag.match(/src="(https:[^"]*)"/);
    if (urlMatch) {
      const url = urlMatch[1];
      const { data, headers } = await convertToBase64(decodeHtmlEntities(url));
      // const imageData = arrayBufferToBase64(data?.result);
      const base64Url = `data:${headers['content-type']};base64,${data?.result}`;
      const newImgTag = imgTag.replace(url, base64Url);
      text = text.replace(imgTag, newImgTag);
    }
  }

  return text;
}

const SignaturesCreateForm = (props) => {
  const { user } = useEditUser();

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = user || profileUser || profileUserAuth;

  const { isModalOpen, setIsModalOpen } = props;
  const [modalDetailSig, setModalDetailSig] = useState(false);
  const [modalDetailSigData, setModalDetailSigData] = useState();
  const [dataContent, setDataContent] = useState();
  const [dataContentUpdate, setDataContentUpdate] = useState();
  const { getValues, setValue } = useForm();
  const [imgArr, setImgArr] = useState([]);
  const [newFile, setNewFile] = useState([]);
  const [listData, setListData] = useState();
  const [messageApi, contextHolder] = message.useMessage();
  const [tabDefautl, setTabDefault] = useState(1);
  const editorRef = useRef(null);
  const [imageArray, setImageArray] = useState([]);

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // useEffect(() => {
  //   const newArr = imgArr.concat(newFile);
  //   const imageHtml = `<img src="{{IMAGE_${newArr.newFile + 1}}}" alt="Uploaded Image" />`;
  //   setImgArr(newArr);
  // }, [newFile]);

  const b64toBlob = (
    b64Data,
    contentType = '',
    fileName = '',
    sliceSize = 512
  ) => {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  };

  function arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  const handleCreateSignature = async () => {
    try {
      if (!getValues('name')) {
        notification.error({
          message: 'Error',
          description: 'Name is required',
        });
        return;
      }
      let content = dataContent;

      const reStylePTagsEl = document.createElement('div');
      reStylePTagsEl.innerHTML = content;

      // if (reStylePTagsEl.children.length > 0) {
      //   reStylePTagsEl?.children?.[0]?.classList?.add('signature-container');
      //   reStylePTagsEl?.children?.[0].setAttribute('style', '')
      // }

      const allPTagsEl = reStylePTagsEl.getElementsByTagName('p');
      for (let i = 0; i < allPTagsEl.length; i++) {
        allPTagsEl[i].style.margin = '0';
        allPTagsEl[i].style.textWrapMode = 'nowrap';
        allPTagsEl[i].style.minWidth = 'min-content';
      }

      // const allDivTagsEl = reStylePTagsEl.getElementsByTagName('div');
      // for (let i = 0; i < allDivTagsEl.length; i++) {
      //   allDivTagsEl[i].style.fontFamily = 'Arial';
      // }

      // const allLiTagsEl = reStylePTagsEl.getElementsByTagName('li');
      // for (let i = 0; i < allLiTagsEl.length; i++) {
      //   allLiTagsEl[i].style.fontFamily = 'Arial';
      // }

      const allSpanTagsEl = reStylePTagsEl.getElementsByTagName('span');
      for (let i = 0; i < allSpanTagsEl.length; i++) {
        allSpanTagsEl[i].style.textWrapMode = 'nowrap';
      }

      const allImgTagsEl = reStylePTagsEl.getElementsByTagName('img');
      for (let i = 0; i < allImgTagsEl.length; i++) {
        allImgTagsEl[i].style.objectFit = 'contain';
      }

      content =
        reStylePTagsEl.getElementsByClassName('signature-container').length > 0
          ? reStylePTagsEl.innerHTML
          : `<div class="signature-container">${reStylePTagsEl.innerHTML} </div>`;

      let reText;
      try {
        reText = await replaceImageSrcsWithBase64(content);
      } catch (e) {
        console.log('test ci 123');
        console.log(e);
        notification.error({
          message: 'Error',
          description:
            'Uploading image error. Please check your image again, we cannot get data from your image',
        });
      }
      const regex = /<img[^>]+src="data:image\/(png|jpeg);base64,([^">]+)"/g;
      const imageFilesArray = [];
      let match;
      while ((match = regex.exec(reText)) !== null) {
        const base64Type = match[1];
        const base64String = match[2];
        const blob = b64toBlob(
          base64String,
          `image/${base64Type}`,
          'customImg'
        );
        imageFilesArray.push(blob);
      }

      let index = 1;
      const regex2 = /<img[^>]+src="data:image\/(png|jpeg);base64,([^">]+)"/g;
      let replacedText = reText;
      // .replace(regex2, (match, p1, p2) => {
      //   const replacedSrc = `"{{IMAGE_${index}}}"`;
      //   index++;
      //   return match.replace(`"data:image/${p1};base64,${p2}"`, replacedSrc);
      // });

      const formData = new FormData();
      formData.append('name', getValues('name'));
      formData.append('content', replacedText);
      imageFilesArray.forEach((img) => {
        formData.append(`signature`, img);
      });
      messageApi.open({
        type: 'loading',
        content: 'Loading',
      });
      try {
        const { data } = await createSignatures(
          formData,
          userToSet?.user?.id || userToSet?.id
        );
        if (data) {
          notification.success({
            message: 'Success',
            description: 'Signature created successfully',
          });
          setTabDefault(1);
          handleGetData();
          messageApi.destroy();
          // setDataContent('');
          setValue('name', '');
        }
      } catch (error) {
        messageApi.destroy();
      }
    } catch (e) {}
  };

  const handleGetData = async () => {
    messageApi.open({
      type: 'loading',
      content: 'Loading',
    });
    try {
      const { data } = await getSignatures(
        userToSet?.user?.id || userToSet?.id
      );
      if (data) {
        setListData(data?.result);
        messageApi.destroy();
      }
    } catch (error) {
      messageApi.destroy();
    }
  };

  //----------------------------------------------------------------

  const handleUpdateDefault = async () => {
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading',
        duration: 2.5,
      });
      const { data } = await updateSignaturesDefault(
        modalDetailSigData?.id,
        userToSet?.user?.id || userToSet?.id
      );
      if (data) {
        messageApi.destroy();
        setModalDetailSig(false);
        setModalDetailSigData(null);
        handleGetData();
        notification.success({
          message: 'Success',
          description: 'Update signature successfully',
        });
      }
    } catch (err) {
      messageApi.destroy();
      notification.error({
        message: 'Error',
        description: 'Something went wrong',
      });
    }
  };

  const handleDeleteSig = async () => {
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading',
        duration: 2.5,
      });
      const { data } = await deleteSignature(
        modalDetailSigData?.id,
        userToSet?.user?.id || userToSet?.id
      );
      if (data) {
        messageApi.destroy();
        setModalDetailSig(false);
        setModalDetailSigData(null);
        handleGetData();
        notification.success({
          message: 'Success',
          description: 'Delete signature successfully',
        });
      }
    } catch (err) {
      messageApi.destroy();
      notification.error({
        message: 'Error',
        description: 'Something went wrong',
      });
    }
  };

  const handleRemoveSig = async () => {
    try {
      messageApi.open({
        type: 'loading',
        content: 'Loading',
        duration: 2.5,
      });
      const { data } = await removeSignaturesDefault(
        modalDetailSigData?.id,
        userToSet?.user?.id || userToSet?.id
      );
      if (data) {
        messageApi.destroy();
        setModalDetailSig(false);
        setModalDetailSigData(null);
        handleGetData();
        notification.success({
          message: 'Success',
          description: 'Remove signature successfully',
        });
      }
    } catch (err) {
      messageApi.destroy();
      notification.error({
        message: 'Error',
        description: 'Something went wrong',
      });
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      handleGetData();
    }
  }, [isModalOpen]);

  const handleUploadFile = (file) => {
    const reader = new FileReader();
    reader.onload = function (event) {
      const blobUrl = URL.createObjectURL(file);
      editorRef?.current?.insertContent(`<img src="${blobUrl}"/>`);
    };
    reader.readAsDataURL(file);
  };

  const handleCKEditorChange = async (_event, editor) => {
    const data = editor?.getData() || '';
    setDataContent(data);
  };

  const items = [
    {
      key: '1',
      label: 'Existing Signatures',
      children: (
        <div>
          {listData?.map((item) => (
            <div
              key={item?.id}
              style={{
                width: '30%',
                border: '1px solid #ccc',
                boxShadow: item?.isDefault ? '5px 8px 20px 0px #ccc' : 'none',
                background: item?.isDefault ? '#0891b2' : '#FFFFFF',
                padding: '10px',
                marginTop: '10px',
                borderRadius: '3px',
                cursor: 'pointer',
                float: 'left',
                marginLeft: '10px',
                color: item?.isDefault ? '#FFFFFF' : '#000',
              }}
              onClick={() => {
                setModalDetailSigData(item);
                setModalDetailSig(true);
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>{item?.name}</div>
                {item?.isDefault && (
                  <div>
                    <CheckOutlined />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ),
    },
    {
      key: '2',
      label: 'Create new Signatures',
      children: (
        <>
          <div>
            <label>Signature Name</label>
            <Input onChange={(e) => setValue('name', e.target.value)} />
          </div>
          <div style={{ marginTop: '20px' }}></div>
          <label>Content</label>
          <div>
            <CKEditor
              editor={ClassicEditor}
              config={{
                ...editorConfig,
                toolbar: {
                  ...editorConfig.toolbar,
                  items: [...editorConfig.toolbar.items].filter(
                    (item) => item !== 'insertMergeField'
                  ),
                },
                mergeFields: null,
              }}
              // data={dataContent || ''}
              onChange={handleCKEditorChange}
            />
          </div>
          <div style={{ marginTop: '20px' }}>
            <Button onClick={() => handleCreateSignature()} type="primary">
              Save
            </Button>
          </div>
        </>
      ),
    },
  ];

  const replaceImagePlaceholders = (text, imgMetadata) => {
    let counter = 1;

    imgMetadata?.forEach(function (imgData) {
      text = text.replace(
        /<img[^>]*?src="(cid:[^"]*?|{{IMAGE_\d+}})"[^>]*?>/g,
        function (match) {
          const replacedMatch = match.replace(/<img/g, function (imgMatch) {
            return imgMatch + ' id="create-by-code-' + counter + '"';
          });
          counter++;
          return replacedMatch;
        }
      );
      const regex = new RegExp(
        'src="(cid:' + imgData.imgId + '|{{IMAGE_' + (counter - 1) + '}})"',
        'g'
      );
      text = text.replace(regex, 'src="' + imgData.link + '"');

      const crossoriginRegex = new RegExp('crossorigin="use-credentials"', 'g');
      text = text.replace(crossoriginRegex, '');
    });
    return text;
  };

  return (
    <>
      <Button style={{ marginTop: '20px' }} type="primary" onClick={showModal}>
        Signatures
      </Button>
      {contextHolder}
      <Modal
        title="Signatures"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={false}
        width={800}
        style={{ height: '500px' }}
      >
        <Tabs
          defaultActiveKey={tabDefautl}
          accessKey={tabDefautl}
          items={items}
        />
      </Modal>
      <SignaturesEditForm
        modalDetailSigData={modalDetailSigData}
        modalDetailSig={modalDetailSig}
        setModalDetailSig={setModalDetailSig}
        handleDeleteSig={handleDeleteSig}
        removeSignaturesDefault={handleRemoveSig}
        handleUpdateDefault={handleUpdateDefault}
        setModalDetailSigData={setModalDetailSigData}
        replaceImagePlaceholders={replaceImagePlaceholders}
      />
    </>
  );
};

export default SignaturesCreateForm;
