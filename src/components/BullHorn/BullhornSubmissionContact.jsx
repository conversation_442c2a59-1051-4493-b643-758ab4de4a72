/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React from 'react';

import _get from 'lodash/get';
import _map from 'lodash/map';

import { Controller } from 'react-hook-form';
import {
  Input,
  Select,
  Button,
  InputNumber,
  Form,
  Divider,
  AutoComplete,
  Modal,
  Spin,
  Row,
  Col,
  Card,
  ConfigProvider,
  notification,
  Image,
} from 'antd';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import useGoogleMapAddressDetails from '../../hooks/useGoogleMapAddressDetails';
import {
  searchBullhornData,
  getCounties,
  getCountries,
  searchBullhorn,
  insertBullhorn,
  upladteBullhorn,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchAddressWithOpenStreet } from '../../services/googleMap';
import { useState, useEffect, useMemo } from 'react';
import {
  EditOutlined,
  CheckOutlined,
  PlusOutlined,
  MailOutlined,
  CloseCircleOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  ContactsOutlined,
  UserOutlined,
  LinkedinOutlined,
  PrinterOutlined,
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import BullHornJobSubmissionCompany from './BullhornJobSubmissionCompany';
import { useSelector } from 'react-redux';
import { selectCompanyResponse } from '../../store/common';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';

import deleteIcon from '../../assets/img/icons/delete-icon.png';
import { tagRender } from '../SearchDetailV2/NewSearchFilterComponent';
import { retriveFindCounty } from '../../constants/cities';

{
  /* <Image
  src={deleteIcon}
  alt="Delete Icon"
  width="16px"
  height="16px"
  preview={false}
/>; */
}

const { Option } = Select;
export const HENLEY = 'Henley';
function BullhornSubmissionContact({
  watch,
  control,
  setValue,
  getValues,
  handleCloseContact,
  setHandleCloseContact,
  detailDataContact,
  flagDetailContact,
  flagEditContact,
  fromLead = false,
}) {
  if (
    detailDataContact?.public_identifier &&
    detailDataContact?.type === 'PEOPLE'
  ) {
    // LinkedIn
    detailDataContact.title = detailDataContact.current_title;
    detailDataContact.present_raw_address = detailDataContact.location;
    const [city, state, country] = detailDataContact.location?.split(',') || [];
    detailDataContact.country = country;
    detailDataContact.state = state;
    detailDataContact.city = city;
    detailDataContact.organization = {
      name: detailDataContact.current_employer,
    };
    detailDataContact.phone_numbers = detailDataContact.teaser?.phones;
    detailDataContact.phone = detailDataContact.teaser?.phones?.[0];
  }
  const defaultCompanyOptions = useSelector(selectCompanyResponse);

  const [handleCloseClient, setHandleCloseClient] = useState(false);
  const [ApprovalDataCompany, setApprovalDataCompany] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isEditCompany, setIsEditCompany] = useState(false);
  const [isAddCompany, setIsAddCompany] = useState(false);
  const [showCompany, setShowCompany] = useState(false);
  const [isSearchCompany, setIsSearchCompany] = useState(false);
  const [showEditCompany, setShowEditCompany] = useState(false);

  // Loading behavior

  const [companyFormLoading, setCompanyFormLoading] = useState(false);

  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const userToSet = profileUser || profileUserAuth;
  const checkHenley = userToSet?.organization?.name === HENLEY;
  // Remove red * (required) for Industry in the above forms for Henley users - Keep the red * for Industry as current for Pearson Carter

  const handleBindingConsultant = () => {
    setValue(
      'clientContact.consultantSelect',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'clientContact.consultantId',
      userToSet?.user?.consultantId || userToSet?.consultantId
    );
    setValue(
      'clientContact.consultant',
      userToSet?.user?.consultantName || userToSet?.consultantName
    );

    // setValue('clientContact.namePrefix', 'Mr.');
  };

  const handleOk = async () => {
    setConfirmLoading(true);
    const { data } = await searchBullhorn(
      'ClientCorporation',
      0,
      10,
      '',
      '',
      watch('clientContact.companyId')
    );
    if (data.result.length === 0) {
      setConfirmLoading(false);
      setApprovalDataCompany(false);
      return;
    }
    const detailCompany = data.result[0];

    const handleIndustry = async () => {
      const newIndustries = [];
      if (
        detailCompany.businessSectorList ||
        detailCompany?.businessSectors?.data
      ) {
        const dataToSet =
          detailCompany.businessSectorList ||
          detailCompany?.businessSectors?.data;
        for (const item of dataToSet) {
          const dataIndustry = await searchBullhorn(
            'BusinessSector',
            0,
            100,
            item,
            '',
            ''
          );
          dataIndustry.data.result.forEach((industryData) => {
            if (industryData.name.toLowerCase() === item.toLowerCase()) {
              newIndustries.push({
                value: industryData.id,
                label: industryData.name,
              });
            }
          });
        }
      }
      return newIndustries;
    };
    const tempNewIndustries = await handleIndustry();
    setValue('clientContact.industries', tempNewIndustries);
    setValue('clientContact.workPhone', detailCompany.phone);
    setValue('clientContact.address', `${detailCompany?.address?.address1}`);
    setValue('clientContact.address2', `${detailCompany?.address?.address2}`);
    setValue(
      'clientContact.addressSelect',
      `${detailCompany?.address?.address1}`
    );
    setValue('clientContact.city', `${detailCompany?.address?.city || ''}`);
    setValue('clientContact.zip', `${detailCompany?.address?.zip}`);
    setValue('clientContact.county', `${detailCompany?.address?.state}`);
    setValue('clientContact.countySelect', `${detailCompany?.address?.state}`);
    setValue('clientContact.state', `${detailCompany?.address?.countryName}`);
    setValue('clientContact.stateId', `${detailCompany?.address?.countryID}`);
    setValue(
      'clientContact.stateSelected',
      `${detailCompany?.address?.countryID}`
    );
    setContactAddressSearchText(detailCompany?.address?.address1);
    setConfirmLoading(false);
    setApprovalDataCompany(false);
  };

  const handleCancel = () => {
    setApprovalDataCompany(false);
  };

  const handleGetIndustry = async () => {
    const newIndustries = [];
    if (detailDataContact?.businessSectors?.data && fromLead) {
      const dataToSet = detailDataContact?.businessSectors?.data;
      for (const item of dataToSet) {
        newIndustries.push({
          value: item.id,
          label: item.name,
        });
      }
    }

    return newIndustries;
  };

  const {
    options: callCompanyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const companyOptions = useMemo(() => {
    if (_get(callCompanyOptions, '0.name')) return callCompanyOptions;
    // setValue('clientContact.company', _get(defaultCompanyOptions, 'name'));
    return [defaultCompanyOptions];
  }, [callCompanyOptions, defaultCompanyOptions]);


  const {
    options: contactConsultant,
    handlePopupScroll: handleContactConsultantScroll,
    handleSearch: handleContactConsultantSearch,
    isLoading: isLoadingContactConsultant,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const {
    options: contactSecondaryOwner,
    handlePopupScroll: handleContactSecondaryOwnerScroll,
    handleSearch: handleContactSecondaryOwnerSearch,
    isLoading: isLoadingContactSecondaryOwner,
  } = useInfiniteScrollWithSearch(searchBullhornData('CorporateUser'));

  const {
    options: contactIndustriesOptions,
    handleScrollPopup: handleContactIndustriesScroll,
    handleSearch: setContactIndustrySearchText,
    isLoading: isLoadingContactIndustries,
  } = useInfiniteScrollWithSearch(searchBullhornData('BusinessSector'));

  const {
    searchOptions: contactAddressOptions,
    setSearchText: setContactAddressSearchText,
    isLoading: isLoadingContactAddresses,
  } = useSearchWithDebounce(searchAddressWithOpenStreet);

  const {
    options: contactReferredByOptions,
    handlePopupScroll: handleReferredByScroll,
    handleSearch: handleReferredBySearch,
    isLoading: isLoadingReferredBy,
    valueNotFound: valueNotFoundReferredBy,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const {
    options: categoriesOptions,
    handlePopupScroll: handleCategoryScroll,
    handleSearch: setCategorySearchText,
    isLoading: isLoadingCategories,
  } = useInfiniteScrollWithSearch(searchBullhornData('Category'));

  const {
    options: skillsOptions,
    handleScrollPopup: handleSkillScroll,
    handleSearch: setSkillSearchText,
    isLoading: isLoadingSkills,
  } = useInfiniteScrollWithSearch(searchBullhornData('Skill'));

  const {
    options: contactOptions,
    handlePopupScroll: handleContactScroll,
    handleSearch: handleContactSearch,
    isLoading: isLoadingContacts,
    valueNotFound: valueNotFoundContacts,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));


  const [listCountySelect, setListCountySelect] = useState()
  const [loadingListCountySelect, setLoadingListCountySelect] = useState(false)

  const handleRefetchCountry = async (stateId) => {
      setLoadingListCountySelect(true)
       const { data: dataCounties } = await getCounties({
          filter: '',
          countryId: stateId,
        });
        setLoadingListCountySelect(false)
        setListCountySelect(dataCounties?.result)
        return dataCounties?.result;
  }

  const handleGetContactDetailAppolo = async () => {
    setContactIndustrySearchText(' ');
    if (watch().company && watch().companySelect) {
      setValue('clientContact.companySelect', watch().companySelect);
      setValue('clientContact.company', watch().company);
      setValue('clientContact.companyId', watch().companyId);
      handleCompanySearch(watch().company);
      handleOk();
    } else {
      setValue(
        'clientContact.companySelect',
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
      setValue(
        'clientContact.company',
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
      handleCompanySearch(
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
      const addressParts = [
        detailDataContact.city,
        detailDataContact.country,
        detailDataContact.county,
        detailDataContact.state,
      ].filter(Boolean);

      const combinedAddress = addressParts.join(', ');
      setContactAddressSearchText(
        detailDataContact?.present_raw_address
          ? detailDataContact?.present_raw_address
          : `${combinedAddress}`
      );
      setValue(
        'clientContact.address',
        detailDataContact?.present_raw_address
          ? detailDataContact?.present_raw_address
          : `${combinedAddress}`
      );
      setValue(
        'clientContact.addressSelect',
        detailDataContact?.present_raw_address
          ? detailDataContact?.present_raw_address
          : `${combinedAddress}`
      );
      setValue('clientContact.state', detailDataContact?.country);
      setValue('clientContact.city', detailDataContact?.city);
      setValue(
        'clientContact.county',
        detailDataContact?.county || detailDataContact?.state
      );
      setValue('clientContact.zip', '');
    }

    setValue('clientContact.firstName', detailDataContact?.first_name || '');
    setValue('clientContact.middleName', '');
    setValue('clientContact.surename', detailDataContact?.last_name || '');
    setValue('clientContact.lastName', detailDataContact?.last_name || '');
    setValue('clientContact.status', '');
    setValue('clientContact.statusSelect', '');
    setValue('clientContact.type', '');
    setValue('clientContact.secondaryOwner', '');
    setValue('clientContact.secondaryOwnerSelect', '');
    setValue('clientContact.secondaryOwnerId', '');
    setValue('clientContact.department', 'Other');
    setValue('clientContact.departmentSelect', 'Other');
    setValue(
      'clientContact.jobTitle',
      detailDataContact?.title || detailDataContact?.occupation || ''
    );
    setValue('clientContact.reportToSelect', '');
    setValue('clientContact.reportTo', '');

    setValue('clientContact.workEmail', detailDataContact?.email || '');
    setValue('clientContact.personalEmail', '');
    setValue(
      'clientContact.workPhone',
      !detailDataContact?.phone_numbers
        ? ''
        : detailDataContact?.phone_numbers.length > 0
          ? detailDataContact?.phone_numbers[0]?.sanitized_number
          : detailDataContact?.sanitized_phone
    );
    setValue('clientContact.mobilePhone', '');
    setValue('clientContact.otherPhone', '');
    setValue('clientContact.fax', '');
    const newInd = await handleGetIndustry();
    setValue('clientContact.industries', newInd);

    setValue('clientContact.generalCommets', '');
    setValue('clientContact.referredBy', '');
    setValue('clientContact.referredById', null);
    setValue('clientContact.referredBySelect', '');
    setValue('clientContact.referredBy', '');
    setValue('clientContact.referredBySelect', '');
    setValue('clientContact.referredById', null);
    setValue('clientContact.bullhornAutomationScore', '');
    setValue(
      'clientContact.linkedProfileUrl',
      detailDataContact?.linkedin_url || ''
    );
  };

  const handleGetContactEdit = async () => {
    setContactIndustrySearchText(' ');
    // handle binding company
    setValue('clientContact.id', detailDataContact?.id);
    if (detailDataContact?.clientCorporation) {
      setValue(
        'clientContact.companySelect',
        detailDataContact?.clientCorporation?.id
      );
      setValue(
        'clientContact.companyId',
        detailDataContact?.clientCorporation?.id
      );
      setValue(
        'clientContact.company',
        detailDataContact?.clientCorporation?.name
      );
      handleCompanySearch(detailDataContact?.clientCorporation?.name);
    } else {
      setValue(
        'clientContact.companySelect',
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
      setValue(
        'clientContact.company',
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
      // setValue(
      //   'clientContact.companyId',
      //   detailDataContact?.organization?.id
      //     ? detailDataContact?.organization?.id
      //     : detailDataContact?.organization_id
      // );
      handleCompanySearch(
        detailDataContact?.organization?.name
          ? detailDataContact?.organization?.name
          : detailDataContact?.organization_name
      );
    }

    const addressParts = [
      detailDataContact?.address?.city,
      detailDataContact?.address?.country,
      detailDataContact?.address?.county,
      detailDataContact?.address?.state,
    ].filter(Boolean);

    const combinedAddress = addressParts.join(', ');
    setContactAddressSearchText(
      detailDataContact?.address?.address1
        ? detailDataContact?.address?.address1
        : `${combinedAddress}`
    );
    setValue(
      'clientContact.address',
      detailDataContact?.address?.address1
        ? detailDataContact?.address?.address1
        : `${combinedAddress}`
    );
    setValue(
      'clientContact.addressSelect',
      detailDataContact?.address?.address1
        ? detailDataContact?.address?.address1
        : `${combinedAddress}`
    );
    setValue('clientContact.state', detailDataContact?.address?.countryName);
    setValue('clientContact.stateId', detailDataContact?.address?.countryID);
    setValue(
      'clientContact.stateSelected',
      detailDataContact?.address?.countryID
    );
    setValue('clientContact.city', detailDataContact?.address?.city);
    setValue('clientContact.county', detailDataContact?.address?.state);
    setValue('clientContact.zip', detailDataContact?.address?.zip);

    setValue(
      'clientContact.firstName',
      detailDataContact?.name.split(' ')[0] || ''
    );
    setValue(
      'clientContact.surename',
      detailDataContact?.name.split(' ')[
        detailDataContact?.name.split(' ').length - 1
      ] || ''
    );
    setValue(
      'clientContact.lastName',
      detailDataContact?.name.split(' ')[
        detailDataContact?.name.split(' ').length - 1
      ] || ''
    );
    setValue('clientContact.status', detailDataContact?.status);
    setValue('clientContact.statusSelect', detailDataContact?.status);
    setValue('clientContact.type', detailDataContact?.type);
    setValue('clientContact.secondaryOwner', '');
    setValue('clientContact.secondaryOwnerSelect', '');
    setValue('clientContact.secondaryOwnerId', '');
    setValue('clientContact.department', 'Other');
    setValue('clientContact.departmentSelect', 'Other');
    setValue(
      'clientContact.jobTitle',
      detailDataContact?.title || detailDataContact?.occupation || ''
    );
    setValue('clientContact.reportToSelect', '');
    setValue('clientContact.reportTo', '');

    setValue('clientContact.workEmail', detailDataContact?.email || '');
    setValue('clientContact.personalEmail', '');
    setValue('clientContact.workPhone', detailDataContact?.phone);
    setValue('clientContact.mobilePhone', '');
    setValue('clientContact.otherPhone', '');
    setValue('clientContact.fax', detailDataContact?.fax);
    const newInd = await handleGetIndustry();
    setValue('clientContact.industries', newInd);

    setValue('clientContact.generalCommets', '');
    setValue('clientContact.referredBy', '');
    setValue('clientContact.referredById', null);
    setValue('clientContact.referredBySelect', '');
    setValue('clientContact.referredBy', '');
    setValue('clientContact.referredBySelect', '');
    setValue('clientContact.referredById', null);
    setValue('clientContact.bullhornAutomationScore', '');
    setValue(
      'clientContact.linkedProfileUrl',
      detailDataContact?.linkedin_url || ''
    );
  };

  const { data: counties = [] } = useQuery(
    ['GET_COUNTIES', watch('clientContact.stateId')],
    async () => {
      if (!watch('clientContact.stateId')) return;
      const { data } = await getCounties({
        filter: '',
        countryId: watch('clientContact.stateId'),
      });
      return data.result;
    },
    { enabled: true }
  );

  const { data: countries = [] } = useQuery(
    ['GET_COUNTRIES'],
    async () => {
      try {
        const { data } = await getCountries();
        return data.result;
      } catch (err) {
        notification.error({ message: err?.response?.data?.message });
      }
    },
    { enabled: true }
  );

  useMemo(() => {
    handleGetContactDetailAppolo();
    handleBindingConsultant();
  }, [flagDetailContact]);

  useMemo(() => {
    if (flagEditContact) {
      handleGetContactEdit();
      handleBindingConsultant();
    }
  }, [flagEditContact, detailDataContact]);

  const handleSubmitCompany = async (e) => {
    setCompanyFormLoading(true);
    const companyPayload = getValues().clientCorporation;
    const companyId = getValues().clientContact.companyId;

    const {
      name,
      status,
      mainPhone,
      companyAddress,
      stateId,
      countySelect,
      zip,
      standardFeeArrangement,
      industries,
    } = companyPayload;

    if (!name) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!status) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Status is required.' });
    } else if (checkHenley ? false : industries.length === 0) {
      return notification.error({ message: 'Industries is required.' });
    } else if (!mainPhone) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Main phone number is required.' });
    } else if (!companyAddress) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Company address is required.' });
    } else if (!stateId) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Country is required.' });
    } else if (!countySelect) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'County is required.' });
    } else if (!zip) {
      setCompanyFormLoading(false);
      return notification.error({ message: 'Postcode is required.' });
    }

    if (
      companyPayload?.companyAddress?.length > 100 ||
      companyPayload?.companySecondAddress?.length > 100
    ) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      setCompanyFormLoading(false);
      return;
    }

    const newCompanyPayload = {
      entityName: 'ClientCorporation',
      name: companyPayload?.name,
      status: companyPayload?.status,
      parentClientCorporation: {
        id:
          companyPayload?.parentCompanyId === ''
            ? undefined
            : companyPayload?.parentCompanyId,
      },
      customText2: companyPayload?.companyOwnerId,
      businessSectorList: !companyPayload.industries
        ? []
        : companyPayload?.industries.length === 0
          ? []
          : companyPayload.industries
              .filter((industry) => industry && industry.label)
              .map((industry) => industry.label),
      notes: companyPayload?.companyComments,
      companyDescription: companyPayload?.companyDescription,
      companyURL: companyPayload?.companyWebsite,
      phone: companyPayload?.mainPhone,
      address: {
        countryName: companyPayload?.state,
        countryID: Number(companyPayload?.stateId),
        address1: companyPayload?.companyAddress,
        address2: companyPayload?.companySecondAddress,
        city: companyPayload?.city,
        zip: companyPayload?.zip,
        state: companyPayload?.county,
        timezone: null,
      },
      billingContact: companyPayload?.billingContact,
      billingPhone: companyPayload?.billingPhone,
      invoiceFormat: companyPayload?.invoiceFormatInformation,
      feeArrangement: companyPayload?.standardFeeArrangement,
      billingAddress: {
        state: companyPayload?.billingZip,
        address1: companyPayload?.billingAddress,
        city: companyPayload?.billingCity,
        zip: companyPayload?.billingZip,
        county: companyPayload?.billingCounty,
      },
      customText1: companyPayload?.linkedProfileUrl,
    };

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newCompanyPayloadCleaned = cleanPayload(newCompanyPayload);
    newCompanyPayloadCleaned.address.address2 = '';
    newCompanyPayloadCleaned.address.countryCode = '';
    newCompanyPayloadCleaned.address.timezone = null;
    const { data } = isEditCompany
      ? await upladteBullhorn(companyId, newCompanyPayloadCleaned)
      : await insertBullhorn(newCompanyPayloadCleaned);
    // newCompanyPayload
    // setAddressSearchText(companyPayload?.companyAddress);
    // setValue("address1", companyPayload?.companyAddress);
    // setValue("city", companyPayload?.city)
    // setValue("county", companyPayload?.county)
    // setValue("countySelect", companyPayload?.county)
    // setValue("zip", companyPayload?.zip)
    // setValue("state", companyPayload?.state)
    // setValue("stateId", Number(companyPayload?.stateId))
    // setValue("stateSelected", Number(companyPayload?.stateId))
    if (isEditCompany) {
      setValue('clientContact.company', data?.result?.data?.name);
    } else {
      setValue('clientContact.companySelect', data?.result?.changedEntityId);
      setValue('clientContact.companyId', data?.result?.changedEntityId);
      setValue('clientContact.company', data?.result?.data?.name);
    }

    setIsAddCompany(false);
    setShowCompany(false);
    if (watch()?.clientContact?.companyId !== watch()?.companyId)
      setApprovalDataCompany(true);
    setShowEditCompany(false);
    setCompanyFormLoading(false);
  };

  useEffect(() => {
    setCategorySearchText(' ');
    setSkillSearchText(' ');
  }, []);

  return (
    <div className="px-5 pt-5">
      {/* Client (Drop down with search API) */}
      <Form.Item
        label={
          <p>
            {/* <span className="text-red-600">*</span>  */}
            Company Name
          </p>
        }
        name="clientContact.company"
        className="mb-12"
      >
        <Controller
          render={({ field }) => (
            <ConfigProvider
              theme={{
                token: {
                  colorBgElevated:
                    companyOptions?.[0]?.id === 0 ? '#FFFFFF' : '#E0EBF9',
                },
              }}
            >
              <AutoComplete
                {...field}
                open={handleCloseClient}
                onMouseDown={(e) => setHandleCloseClient(true)}
                onPopupScroll={(e) =>
                  handleCompanyScroll(e, 'ClientCorporation')
                }
                options={_map(companyOptions, (option) => ({
                  value: option?.id,
                  label: (
                    <div className="grid">
                      <div className="flex justify-between">
                        <span className="text-base font-base">
                          {option?.id} | {option?.name}
                        </span>
                        <Button
                          className="absolute right-3 top-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowEditCompany(true);
                            setIsEditCompany(true);
                            setShowCompany(false);
                            setValue('clientContact.companySelect', option?.id);
                            setValue('clientContact.companyId', option?.id);
                            const companyName = companyOptions.find(
                              (co) => co?.id == option?.id
                            )?.name;
                            setValue('clientContact.company', companyName);
                            setHandleCloseClient(false);
                            setTimeout(() => {
                              setShowCompany(true);
                            }, 100);
                          }}
                        >
                          <EditOutlined />
                        </Button>
                      </div>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <PhoneOutlined />
                            {option?.phone ? option?.phone : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <InfoCircleOutlined />
                            {option?.status ? option?.status : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <EnvironmentOutlined />
                            {option?.address &&
                            option?.address.city &&
                            option?.address.state
                              ? `${option?.address.city}, ${option?.address.state}`
                              : option?.address && option?.address.city
                                ? option?.address.city
                                : option?.address && option?.address.state
                                  ? option?.address.state
                                  : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  record: option?.address,
                  email: option?.email,
                }))}
                onSearch={(searchText) => {
                  setIsAddCompany(false);
                  setIsSearchCompany(true);
                  if (searchText) companySetStart(0);
                  setValue('clientContact.companySelect', null);
                  setHandleCloseClient(true);
                  setValue('clientContact.companyId', null);
                  setValue('clientContact.company', searchText);
                  handleCompanySearch(searchText);
                }}
                onSelect={(value, option) => {
                  const currentCompany = companyOptions.find(
                    (co) => co?.id == value
                  );
                  setValue('clientContact.workPhone', currentCompany?.phone);
                  setValue('clientContact.mobilePhone', currentCompany?.phone);
                  setValue('clientContact.fax', currentCompany?.phone);
                  setValue(
                    'clientContact.address',
                    currentCompany?.address?.address1 ||
                      currentCompany?.address?.address2
                  );
                  setValue(
                    'clientContact.county',
                    currentCompany?.address?.state
                  );
                  setValue(
                    'clientContact.state',
                    currentCompany?.address?.countryName
                  );
                  setValue(
                    'clientContact.stateId',
                    currentCompany?.address?.countryID
                  );
                  setValue(
                    'clientContact.countySelect',
                    currentCompany?.address?.countryID
                  );
                  setValue('clientContact.city', currentCompany?.address?.city);
                  setValue('clientContact.zip', currentCompany?.address?.zip);
                  setShowEditCompany(false);
                  setIsAddCompany(false);
                  setIsSearchCompany(false);
                  setIsEditCompany(true);
                  setShowCompany(false);
                  setValue('clientContact.companySelect', value);
                  setValue('clientContact.companyId', value);
                  const companyName = companyOptions?.find(
                    (co) => co.id == value
                  )?.name;
                  setValue('clientContact.company', companyName);
                  setHandleCloseClient(false);
                  setTimeout(() => setShowCompany(true), 100);

                  // Update industries
                  // const businessSectorList =
                  //   currentCompany?.businessSectorList || [];
                  // if (businessSectorList.length > 0) {
                  //   const currentCompanyIndustries = businessSectorList.map(
                  //     (sector) => ({ value: sector, label: sector })
                  //   );
                  //   setValue(
                  //     'clientContact.industries',
                  //     currentCompanyIndustries
                  //   );
                  // }
                }}
                dropdownRender={(menus) => (
                  <>
                    {companyOptions[0]?.id === 0 ? (
                      <Button
                        onClick={() => {
                          setIsSearchCompany(false);
                          setValue(
                            'clientContact.companySelect',
                            valueNotFoundCompany
                          );
                          setValue('clientContact.companyId', null);
                          setValue(
                            'clientContact.company',
                            valueNotFoundCompany
                          );
                          setValue(
                            'clientCorporation.name',
                            valueNotFoundCompany
                          );
                          setHandleCloseClient(false);
                          setIsEditCompany(false);
                          setShowCompany(true);
                          setIsAddCompany(true);
                        }}
                        onMouseLeave={() => setHandleCloseClient(false)}
                        className="flex gap-1 w-full h-fit bg-white"
                        value={valueNotFoundCompany}
                      >
                        <PlusOutlined className="my-auto" />{' '}
                        <span className="my-auto">Add new</span>{' '}
                        {valueNotFoundCompany}
                      </Button>
                    ) : (
                      <div onMouseLeave={() => setHandleCloseClient(false)}>
                        <Button
                          onClick={() => {
                            setIsSearchCompany(false);
                            setValue(
                              'clientContact.companySelect',
                              valueNotFoundCompany
                            );
                            setValue('clientContact.companyId', null);
                            setValue(
                              'clientContact.company',
                              valueNotFoundCompany
                            );
                            setValue(
                              'clientCorporation.name',
                              valueNotFoundCompany
                            );
                            setHandleCloseClient(false);
                            setIsEditCompany(false);
                            setShowCompany(true);
                            setIsAddCompany(true);
                            setShowEditCompany(true);
                          }}
                          className="flex gap-1 w-full h-fit bg-white"
                        >
                          <PlusOutlined className="my-auto" />{' '}
                          <span className="my-auto">Add new</span>{' '}
                          {valueNotFoundCompany}
                        </Button>
                        {menus}
                      </div>
                    )}
                  </>
                )}
              >
                <div className="custom-input">
                  <span
                    className={`${(isAddCompany && !isSearchCompany) || (!getValues().clientContact.companyId && !isSearchCompany) ? 'input-text-select-add' : getValues().clientContact.companyId ? 'input-text-select-company-contact' : 'input-text'}`}
                  >
                    {getValues().clientContact.company}
                  </span>
                  <Input
                    value={getValues().clientContact.company}
                    {...field}
                    required
                    suffix={
                      isLoadingCompany ? (
                        <Spin />
                      ) : getValues().clientContact.companyId !== '' &&
                        getValues().clientContact.companyId ? (
                        <>
                          <CheckOutlined className="text-green-600" />
                          {(!isAddCompany && isSearchCompany) ||
                          getValues().clientContact.companyId ? (
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowEditCompany(true);
                                setIsEditCompany(true);
                                setShowCompany(false);
                                setHandleCloseClient(false);
                                setTimeout(() => {
                                  setShowCompany(true);
                                  setIsAddCompany(false);
                                }, 100);
                              }}
                            >
                              <EditOutlined />
                            </Button>
                          ) : (
                            ''
                          )}
                        </>
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </div>
              </AutoComplete>
            </ConfigProvider>
          )}
          name="clientContact.company"
          control={control}
        />
      </Form.Item>

      {/* section company add & edit */}
      {(showEditCompany || isAddCompany) && (
        <Form.Item>
          <Card className=" max-w-full mx-auto bg-[#BEDAFD33] shadow-lg rounded-2xl overflow-hidden hover:shadow-xl">
            <div className="w-full bg-[#BEDAFD] pl-6 py-3 font-semibold text-base">
              <span>{isEditCompany ? 'Edit' : 'Add New'} Company</span>
            </div>
            {/* <Divider
              className=" font-semibold text-base sticky top-0 z-50 w-full"
              orientation="left"
            >
              <span className="text-blue-600">
                {isEditCompany ? 'Edit' : 'Add New'} Company
              </span>
            </Divider> */}
            <div className="p-6">
              <div className="my-8 border-b-2 border-b-[#17c1e8] pt-4 pb-2">
                <span className="font-semibold text-lg ">
                  {isEditCompany ? 'Edit' : 'Add'} Company
                </span>
              </div>
              <BullHornJobSubmissionCompany
                isFromAddContact={true}
                isEditCompany={isEditCompany}
                control={control}
                setValue={setValue}
                getValues={getValues}
                handleCloseClient={handleCloseClient}
                setHandleCloseClient={setHandleCloseClient}
                watch={watch}
              />
              <div className="left-0 bottom-0 w-full">
                <div className="flex gap-4 mr-8">
                  <Button
                    onClick={() => {
                      setShowCompany(false);
                      companySetStart(0);
                      setValue('clientContact.companySelect', null);
                      setHandleCloseClient(true);
                      setValue('clientContact.companyId', null);
                      setValue('clientContact.company', '');
                      setShowEditCompany(false);
                      // functionContactClient.contactSetOptions([]);
                      // setValue("clientContact.contactId", null);
                      // setValue("clientContact.contact", null);
                      // setValue("clientContact.contactSelect", null);
                      // setValue("clientContact.email", "");
                      handleCompanySearch(' ');
                      setIsAddCompany(false);
                    }}
                    className={`bg-[#BEDAFD33]  `}
                  >
                    Cancel
                  </Button>
                  <Button
                    loading={companyFormLoading}
                    htmlType="button"
                    onClick={handleSubmitCompany}
                    type="primary"
                    className={``}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </Form.Item>
      )}
      <div className="flex w-full gap-5">
        <div className="w-1/5">
          <Form.Item label={<p>Title</p>} name="clientContact.namePrefix">
            <Controller
              render={({ field }) => (
                <AutoComplete
                  placeholder="Select Title"
                  {...field}
                  onSelect={(value) => {
                    setValue('clientContact.namePrefixSelect', value);
                  }}
                  onSearch={() => {
                    setValue('clientContact.namePrefixSelect', null);
                  }}
                  options={['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Miss'].map((val) => ({
                    label: val,
                    value: val,
                  }))}
                  filterOption={(inputValue, option) =>
                    option?.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                  style={{
                    height: '20px !important',
                  }}
                ></AutoComplete>
              )}
              name="clientContact.namePrefix"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-2/5">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                First Name
              </p>
            }
            name="clientContact.firstName"
          >
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter First Name"
                  required
                  {...field}
                  suffix={
                    getValues().clientContact.firstName !== '' &&
                    getValues().clientContact.firstName ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
              )}
              name="clientContact.firstName"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-2/5">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Surname
              </p>
            }
            name="clientContact.surename"
          >
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Surname"
                  required
                  {...field}
                  suffix={
                    getValues().clientContact.surename !== '' &&
                    getValues().clientContact.surename ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
              )}
              name="clientContact.surename"
              control={control}
            />
          </Form.Item>
        </div>
      </div>

      <Form.Item
        label={
          <p>
            {/* <span className="text-red-600">*</span>  */}
            Consultant
          </p>
        }
        name="clientContact.consultant"
        className="mb-12"
      >
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              options={contactConsultant.map((option) => ({
                value: option?.id,
                label: option?.name,
              }))}
              onPopupScroll={handleContactConsultantScroll}
              onSearch={(searchText) => {
                setValue('clientContact.consultantSelect', null);
                setValue('clientContact.consultantId', null);
                setValue('clientContact.consultant', searchText);
                handleContactConsultantSearch(searchText);
              }}
              filterOption={(input, option) =>
                option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onSelect={(value) => {
                setValue('clientContact.consultantSelect', value);
                setValue('clientContact.consultantId', value);
                const contactConsultantName = contactConsultant.find(
                  (co) => co.id == value
                )?.name;
                setValue('clientContact.consultant', contactConsultantName);
              }}
            >
              <Input
                addonBefore={
                  <UserOutlined
                    style={{
                      color: 'rgba(0,0,0,.5)',
                    }}
                  />
                }
                {...field}
                required
                suffix={
                  isLoadingContactConsultant ? (
                    <Spin />
                  ) : getValues().clientContact.consultantId !== '' &&
                    getValues().clientContact.consultantId ? (
                    <CheckOutlined className="text-green-600" />
                  ) : (
                    <Image
                      src={deleteIcon}
                      alt="Delete Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                  )
                }
              />
            </AutoComplete>
          )}
          name="clientContact.consultant"
          control={control}
        />
      </Form.Item>

      {/* <Form.Item label="Status" name="clientContact.status" className="mb-10">
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              onSelect={(value) => {
                setValue('clientContact.statusSelect', value);
              }}
              onSearch={() => {
                setValue('clientContact.statusSelect', null);
              }}
              options={[
                'Prospect',
                'Active',
                'Passive Account',
                'DNC',
                'Archive',
                'Live Lead',
              ].map((val) => ({ label: val, value: val }))}
              filterOption={(inputValue, option) =>
                option?.label
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) !== -1
              }
            ></AutoComplete>
          )}
          name="clientContact.status"
          control={control}
        />
      </Form.Item> */}

      {/* <Form.Item label="Type" name="clientContact.type" className="mb-10">
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              onSelect={(value) => {
                setValue('clientContact.typeSelect', value);
              }}
              onSearch={() => {
                setValue('clientContact.typeSelect', null);
              }}
              options={[
                'Hiring Manager',
                'Billing Contact',
                'Decision Marker',
              ].map((val) => ({ label: val, value: val }))}
              filterOption={(inputValue, option) =>
                option?.label
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) !== -1
              }
            ></AutoComplete>
          )}
          name="clientContact.type"
          control={control}
        />
      </Form.Item> */}

      {/* <Form.Item
        label="Secondary Owners"
        name="clientContact.secondaryOwner"
        className="mb-10"
      >
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              options={contactSecondaryOwner.map((option) => ({
                value: option?.id,
                label: option?.name,
              }))}
              onPopupScroll={handleContactSecondaryOwnerScroll}
              onSearch={(searchText) => {
                setValue('clientContact.secondaryOwnerSelect', null);
                setValue('clientContact.secondaryOwnerId', null);
                setValue('clientContact.secondaryOwner', searchText);
                handleContactSecondaryOwnerSearch(searchText);
              }}
              filterOption={(input, option) =>
                option?.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onSelect={(value) => {
                setValue('clientContact.secondaryOwnerSelect', value);
                setValue('clientContact.secondaryOwnerId', value);
                const contactSecondaryOwnerName = contactSecondaryOwner.find(
                  (co) => co.id == value
                )?.name;
                setValue(
                  'clientContact.secondaryOwner',
                  contactSecondaryOwnerName
                );
              }}
            >
              <Input suffix={isLoadingContactSecondaryOwner ? <Spin /> : ''} />
            </AutoComplete>
          )}
          name="clientContact.secondaryOwner"
          control={control}
        />
      </Form.Item> */}

      <Modal
        centered
        width={1000}
        style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Update Contact"
        open={ApprovalDataCompany}
        okText={'Confirm'}
        okButtonProps={{
          loading: confirmLoading,
        }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        Would you like to update this contact address, work phone, and fax with
        this company information?
      </Modal>

      <Form.Item
        label="Department"
        name="clientContact.department"
        className="mb-10"
      >
        <Controller
          render={({ field }) => (
            <AutoComplete
              placeholder="Select Department"
              {...field}
              onSelect={(value) => {
                setValue('clientContact.departmentSelect', value);
              }}
              onSearch={() => {
                setValue('clientContact.departmentSelect', null);
              }}
              options={[
                'Accounting',
                'Accounting Payable',
                'Accounting Reveivable',
                'Administration',
                'Customer Service',
                'Customer Support',
                'Finance',
                'Human Resource',
                'Information Technology',
                'Marketing',
                'Operations',
                'Payroll',
                'Sales',
                'Training',
                'Travel',
                'Warehouse',
                'Other',
              ].map((val) => ({ label: val, value: val }))}
              filterOption={(inputValue, option) =>
                option?.label
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) !== -1
              }
            ></AutoComplete>
          )}
          name="clientContact.department"
          control={control}
        />
      </Form.Item>

      <Form.Item
        label={
          <p>
            {/* <span className="text-red-600">*</span>  */}
            Job Title
          </p>
        }
        name="clientContact.jobTitle"
      >
        <Controller
          render={({ field }) => (
            <Input
              placeholder="Enter Job Title"
              required
              {...field}
              suffix={
                getValues().clientContact.jobTitle !== '' &&
                getValues().clientContact.jobTitle ? (
                  <CheckOutlined className="text-green-600" />
                ) : (
                  <Image
                    src={deleteIcon}
                    alt="Delete Icon"
                    width="16px"
                    height="16px"
                    preview={false}
                  />
                )
              }
            />
          )}
          name="clientContact.jobTitle"
          control={control}
        />
      </Form.Item>

      <Form.Item label="Reports to" name="clientContact.reportTo">
        <Controller
          render={({ field }) => (
            <ConfigProvider
              theme={{
                token: {
                  colorBgElevated:
                    contactOptions[0]?.id === 0 ? '#FFFFFF' : '#E0EBF9',
                },
              }}
            >
              <AutoComplete
                {...field}
                open={handleCloseContact}
                onMouseDown={(e) => {
                  setHandleCloseContact(true);
                }}
                onPopupScroll={(e) => {
                  handleContactScroll(e, getValues().clientContact.companyId);
                }}
                options={contactOptions.map((option) => ({
                  value: option?.id,
                  label: (
                    <div className="grid">
                      <span className="text-base">{option?.name}</span>
                      <div className="contact-details">
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <MailOutlined />
                            {option?.email ? option?.email : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <PhoneOutlined />
                            {option?.phone ? option?.phone : '-'}
                          </span>
                          <span className="text-gray-500 text-xs">
                            <ContactsOutlined />{' '}
                            {option?.occupation ? option?.occupation : '-'}
                          </span>
                        </div>
                        <div className="flex">
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <EnvironmentOutlined />
                            {option?.address &&
                            option?.address.city &&
                            option?.address.state
                              ? `${option?.address.city}, ${option?.address.state}`
                              : option?.address && option?.address.city
                                ? option?.address.city
                                : option?.address && option?.address.state
                                  ? option?.address.state
                                  : '-'}
                          </span>
                          <span className="text-gray-500 text-xs min-w-[200px]">
                            <InfoCircleOutlined />{' '}
                            {option?.status ? option?.status : '-'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ),
                  email: option?.email,
                }))}
                onSearch={(searchText) => {
                  setValue('clientContact.reportToSelect', null);
                  setHandleCloseContact(true);
                  setValue('clientContact.reportToSelect', null);
                  setValue('clientContact.reportTo', searchText);
                  handleContactSearch(
                    searchText,
                    getValues().clientContact.companyId
                  );
                }}
                filterOption={(input, option) => {
                  const labelString = option?.label.props.children
                    .map((child) =>
                      child.props ? child.props.children : child
                    )
                    .join(' ');
                  return (
                    labelString.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  );
                }}
                onSelect={(value, record) => {
                  setValue('clientContact.reportToSelect', value);
                  setValue('clientContact.reportToSelect', value);
                  const contact = contactOptions.find((co) => co.id === value);
                  setValue('clientContact.reportTo', contact?.name);
                  setHandleCloseContact(false);
                }}
                dropdownRender={(menus) => {
                  if (contactOptions[0]?.id === 0) {
                    return (
                      <Button
                        onClick={(e) => {
                          setValue(
                            'clientContact.reportTo',
                            valueNotFoundContacts
                          );
                          setValue(
                            'clientContact.reportToSelect',
                            valueNotFoundContacts
                          );
                          setHandleCloseContact(false);

                          setValue('clientContact.reportToSelect', null);
                        }}
                        onMouseLeave={(e) => {
                          setHandleCloseContact(false);
                        }}
                        className="flex gap-1 w-full h-fit bg-white"
                        value={valueNotFoundContacts}
                      >
                        <PlusOutlined className="my-auto" />{' '}
                        <span className="my-auto">Add new</span>{' '}
                        {valueNotFoundContacts}
                      </Button>
                    );
                  } else {
                    return (
                      <div
                        onMouseLeave={(e) => {
                          setHandleCloseContact(false);
                        }}
                      >
                        <Button
                          onClick={(e) => {
                            setValue(
                              'clientContact.reportTo',
                              valueNotFoundContacts
                            );
                            setValue(
                              'clientContact.reportToSelect',
                              valueNotFoundContacts
                            );
                            setHandleCloseContact(false);

                            setValue('clientContact.reportToSelect', null);
                          }}
                          className="flex gap-1 w-full h-fit bg-white"
                          value={valueNotFoundContacts}
                        >
                          <PlusOutlined className="my-auto" />{' '}
                          <span className="my-auto">Add new</span>{' '}
                          {valueNotFoundContacts}
                        </Button>
                        {menus}
                      </div>
                    );
                  }
                }}
              >
                <Input
                  addonBefore={
                    <UserOutlined
                      style={{
                        color: 'rgba(0,0,0,.5)',
                      }}
                    />
                  }
                  suffix={isLoadingContacts ? <Spin /> : ''}
                />
              </AutoComplete>
            </ConfigProvider>
          )}
          name="clientContact.reportTo"
          control={control}
        />
      </Form.Item>

      <div className="my-8 border-b-2 border-b-[#17c1e8] pt-4 pb-2">
        <span className="font-semibold text-lg ">Contact Information</span>
      </div>

      {/* <Divider orientation="left">Contact Information</Divider> */}
      <div className="w-full flex gap-5">
        <div className="w-1/2">
          <Form.Item label="Work Email" name="clientContact.workEmail">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Email Address"
                  {...field}
                  suffix={
                    getValues().clientContact.workEmail !== '' &&
                    getValues().clientContact.workEmail ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                />
              )}
              name="clientContact.workEmail"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item label="Personal Email" name="clientContact.personalEmail">
            <Controller
              render={({ field }) => (
                <Input placeholder="Enter Email Address" {...field} />
              )}
              name="clientContact.personalEmail"
              control={control}
            />
          </Form.Item>
        </div>
      </div>
      <Form.Item
        label="LinkedIn Profile URL"
        name="clientContact.linkedProfileUrl"
        className="mb-12"
      >
        <Controller
          render={({ field }) => (
            <Input
              addonBefore={
                <LinkedinOutlined
                  style={{
                    color: 'rgba(0,0,0,.5)',
                  }}
                />
              }
              placeholder="Enter Linked Profile URL"
              {...field}
              suffix={
                getValues().clientContact.workEmail !== '' &&
                getValues().clientContact.workEmail ? (
                  <CheckOutlined className="text-green-600" />
                ) : (
                  <Image
                    src={deleteIcon}
                    alt="Delete Icon"
                    width="16px"
                    height="16px"
                    preview={false}
                  />
                )
              }
            />
          )}
          name="clientContact.linkedProfileUrl"
          control={control}
        />
      </Form.Item>
      <div className="flex w-full gap-5">
        <div className="w-1/3">
          <Form.Item label="Mobile Phone" name="clientContact.mobilePhone">
            <Controller
              render={({ field }) => (
                <Input
                  addonBefore={
                    <PhoneOutlined
                      style={{
                        color: 'rgba(0,0,0,.5)',
                      }}
                    />
                  }
                  placeholder="Enter Mobile Number"
                  {...field}
                />
              )}
              name="clientContact.mobilePhone"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/3">
          <Form.Item label="Other Phone" name="clientContact.otherPhone">
            <Controller
              render={({ field }) => (
                <Input
                  addonBefore={
                    <PhoneOutlined
                      style={{
                        color: 'rgba(0,0,0,.5)',
                      }}
                    />
                  }
                  placeholder="Enter Mobile Number"
                  {...field}
                />
              )}
              name="clientContact.otherPhone"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/3">
          <Form.Item label="Fax" name="clientContact.fax">
            <Controller
              render={({ field }) => (
                <Input
                  addonBefore={
                    <PrinterOutlined
                      style={{
                        color: 'rgba(0,0,0,.5)',
                      }}
                    />
                  }
                  placeholder="Enter Fax Code"
                  {...field}
                />
              )}
              name="clientContact.fax"
              control={control}
            />
          </Form.Item>
        </div>
      </div>
      {/* <Form.Item label="Work Phone" name="clientContact.workPhone">
        <Controller
          render={({ field }) => <Input {...field} />}
          name="clientContact.workPhone"
          control={control}
        />
      </Form.Item> */}

      <div className="w-full flex gap-5 mb-7">
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Address Line 1
              </p>
            }
            name="clientContact.address"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  options={contactAddressOptions?.data?.map((ao) => ({
                    label: (
                      <div>
                        <div>{ao.display_name || ao.address1}</div>
                        <div style={{ fontSize: '13px', color: '#b3b3b3' }}>
                          {ao?.full_address || ao?.place_formatted}
                        </div>
                      </div>
                    ),
                    value: ao.place_id,
                  }))}
                  onSearch={(value) => {
                    setContactAddressSearchText(value);
                    setValue('clientContact.address', value);
                    setValue('clientContact.addressSelect', value);
                  }}
                  value={getValues('clientContact.address')}

                  onSelect={async (selectedAddressId) => {
                    // Not use LAMBDA for now
                    // if(contactAddressOptions?.sourceKey === "LAMBDA") {
                    //   const selectedAddress = contactAddressOptions?.data.find(
                    //     (ao) => ao.place_id == selectedAddressId
                    //   );

                    //   setValue('clientContact.address', selectedAddress.display_name);
                    //   setValue('clientContact.addressSelect', selectedAddressId);

                    //   setValue('clientContact.zip', selectedAddress?.address.postcode);
                    //   setValue('clientContact.county', selectedAddress?.address.county || selectedAddress?.address?.city);
                    //   setValue('clientContact.countySelect', selectedAddress?.address?.county || selectedAddress?.address.city);
                    //   setValue('clientContact.city', selectedAddress?.address?.city);
                    //   setValue('clientContact.state', selectedAddress?.address?.country);
                    //   const country = await getCountries( selectedAddress?.address?.country);
                    //   setValue(
                    //     'clientContact.stateId',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientContact.stateSelected',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    // } else {
                    const selectedAddress = contactAddressOptions?.data?.find(
                            (ao) =>
                              ao.id == selectedAddressId ||
                              ao.place_id == selectedAddressId
                          );
                    setValue('clientContact.county',
                      selectedAddress?.context?.place?.name)
                    setValue('clientContact.address', selectedAddress?.full_address || selectedAddress?.place_formatted);
                    setValue('clientContact.addressSelect', selectedAddressId);
                    const state = countries?.find((item) => item.label === selectedAddress?.context?.country?.name)
                    setValue('clientContact.state', state?.label);
                    setValue('clientContact.stateId', state?.value);
                    setValue('clientContact.stateSelected', state?.value);
                    const listCounTries = await handleRefetchCountry(state?.value)
                    const countryCode = selectedAddress?.context?.country?.country_code

                    let countySelect = listCounTries?.find((item) => ( item.label === selectedAddress?.context?.region?.name))
                    if (!countySelect) {
                      let countyFinder = retriveFindCounty(selectedAddress?.context?.place?.name, countryCode)
                      if (countyFinder) {
                          countySelect = listCounTries?.find((item) => ( item.label.trim().toLowerCase() === countyFinder.trim().toLowerCase()))
                      }
                    }
                    setValue('clientContact.county', countySelect?.value);
                    setValue('clientContact.countySelect', countySelect)
                    // }
                  }}
                >
                  <Input
                    {...field}
                    required
                    suffix={
                      isLoadingContactAddresses ? (
                        <Spin />
                      ) : getValues().clientContact.addressSelect !== '' &&
                        getValues().clientContact.addressSelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
              name="clientContact.address"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Address Line 2
              </p>
            }
            name="clientContact.address2"
          >
            <Controller
              render={({ field }) => (
                <AutoComplete
                  {...field}
                  options={contactAddressOptions?.data?.map((ao) => ({
                    label: ao.display_name || ao.address1,
                    value: ao.place_id,
                  }))}
                  onSearch={(value) => {
                    setContactAddressSearchText(value);
                    setValue('clientContact.address2', value);
                    setValue('clientContact.addressSelect2', value);
                  }}
                  onSelect={async (selectedAddressId) => {
                    // Not use LAMBDA for now
                    // if (contactAddressOptions?.sourceKey === 'LAMBDA') {
                    //   const selectedAddress = contactAddressOptions?.data.find(
                    //     (ao) => ao.place_id == selectedAddressId
                    //   );
                    //   setValue(
                    //     'clientContact.address',
                    //     selectedAddress.address1
                    //   );
                    //   setValue(
                    //     'clientContact.addressSelect',
                    //     selectedAddressId
                    //   );

                    //   setValue(
                    //     'clientContact.zip',
                    //     selectedAddress?.address.postcode
                    //   );
                    //   setValue(
                    //     'clientContact.county',
                    //     selectedAddress?.address.county ||
                    //       selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientContact.countySelect',
                    //     selectedAddress?.address?.county ||
                    //       selectedAddress?.address.city
                    //   );
                    //   setValue(
                    //     'clientContact.city',
                    //     selectedAddress?.address?.city
                    //   );
                    //   setValue(
                    //     'clientContact.state',
                    //     selectedAddress?.address?.country
                    //   );
                    //   const country = await getCountries(
                    //     selectedAddress?.address?.country
                    //   );
                    //   setValue(
                    //     'clientContact.stateId',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    //   setValue(
                    //     'clientContact.stateSelected',
                    //     `${country?.data?.result[0].value}`
                    //   );
                    // } else {
                    const selectedAddress = contactAddressOptions?.data?.find(
                            (ao) =>
                              ao.id == selectedAddressId ||
                              ao.place_id == selectedAddressId
                          );
                    // setContactPlaceId(selectedAddressId);
                    setValue('clientContact.address', selectedAddress.address1);
                    setValue('clientContact.addressSelect', selectedAddressId);
                    // }
                  }}
                >
                  <Input
                    suffix={
                      isLoadingContactAddresses ? (
                        <Spin />
                      ) : getValues().clientContact.addressSelect !== '' &&
                        getValues().clientContact.addressSelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : null
                    }
                  />
                </AutoComplete>
              )}
              name="clientContact.address2"
              control={control}
            />
          </Form.Item>
        </div>
      </div>

      <Row gutter={[8, 8]}>
        <Col span={8}>
          <Form.Item label="City" name="clientContact.city">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter City"
                  className="mt-2"
                  suffix={isLoadingContactAddresses ? <Spin /> : ''}
                  {...field}
                />
              )}
              name="clientContact.city"
              control={control}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="County/State" name="clientContact.county">
            <Controller
              name="clientContact.county"
              control={control}
              render={({ field }) => (
                <AutoComplete
                  placeholder="Enter County"
                  {...field}
                  className="search-input"
                  options={listCountySelect}
                  onSelect={(value, record) => {
                    setValue('clientContact.countySelect', value);
                  }}
                  value={getValues("clientContact.county")}
                  onSearch={(searchText) => {
                    setValue('clientContact.county', searchText);
                    setValue('clientContact.countySelect', null);
                  }}
                  filterOption={(inputValue, option) =>
                    option?.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) !== -1
                  }
                >
                  <Input
                    {...field}
                    suffix={
                      isLoadingContactAddresses ? (
                        <Spin />
                      ) : getValues().clientContact.countySelect !== '' &&
                        getValues().clientContact.countySelect ? (
                        <CheckOutlined className="text-green-600" />
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                  />
                </AutoComplete>
              )}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Postcode" name="clientContact.zip">
            <Controller
              render={({ field }) => (
                <Input
                  placeholder="Enter Postal Code"
                  className="mt-2"
                  suffix={isLoadingContactAddresses ? <Spin /> : ''}
                  {...field}
                />
              )}
              name="clientContact.zip"
              control={control}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Country" name="clientContact.state" className="mb-12">
        <Controller
          render={({ field }) => (
            <AutoComplete
              {...field}
              options={countries}
              onSearch={(searchText) => {
                setValue('clientContact.state', searchText);
                setValue('clientContact.stateId', null);
                setValue('clientContact.stateSelected', null);
              }}
              filterOption={(input, option) =>
                option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              value={watch('clientContact.state')}
              onSelect={(value) => {
                setValue('clientContact.stateSelected', value);
                setValue('clientContact.stateId', value);
                const countryName = countries.find(
                  (co) => co.value == value
                )?.label;
                setValue('clientContact.state', countryName);
              }}
            >
              <Input
                placeholder="Select Country"
                required
                suffix={
                  isLoadingContactAddresses ? (
                    <Spin />
                  ) : getValues().clientContact.stateId !== '' &&
                    getValues().clientContact.stateId ? (
                    <CheckOutlined className="text-green-600" />
                  ) : (
                    <Image
                      src={deleteIcon}
                      alt="Delete Icon"
                      width="16px"
                      height="16px"
                      preview={false}
                    />
                  )
                }
              />
            </AutoComplete>
          )}
          name="clientContact.state"
          control={control}
        />
      </Form.Item>

      <div className="my-8 border-b-2 border-b-[#17c1e8] pt-4 pb-2">
        <span className="font-semibold text-lg ">Categories & Skills</span>
      </div>
      <div className="w-full flex gap-5">
        <div className="w-1/2">
          <>
            <Form.Item
              label={
                <p>
                  {/* <span className="text-red-600">*</span>  */}
                  Categories
                </p>
              }
              name="clientContact.categories"
            >
              <Controller
                rules={{ required: 'This field is required' }}
                render={({ field }) => (
                  <Select
                    tagRender={(props) =>
                      tagRender({ ...props, tagColor: 'tag-orange' })
                    }
                    placeholder="Select Categories"
                    // disabled={showCompany || jobDetailCompanyId}
                    required
                    labelInValue
                    mode="multiple"
                    onSearch={() => {
                      setValue('categories', null);
                    }}
                    suffixIcon={
                      getValues('clientContact.categories') ? (
                        getValues('clientContact.categories')?.length !== 0 ? (
                          <CheckOutlined className="text-green-600" />
                        ) : (
                          <Image
                            src={deleteIcon}
                            alt="Delete Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                        )
                      ) : (
                        <Image
                          src={deleteIcon}
                          alt="Delete Icon"
                          width="16px"
                          height="16px"
                          preview={false}
                        />
                      )
                    }
                    {...field}
                    notFoundContent={null}
                    options={categoriesOptions.map((so) => ({
                      ...so,
                      label: so.name,
                      value: so.id,
                    }))}
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) !== -1
                    }
                  />
                )}
                name="clientContact.categories"
                control={control}
              />
            </Form.Item>
          </>
        </div>
        <div className="w-1/2 ">
          <Form.Item
            label={
              <p>
                {/* <span className="text-red-600">*</span>  */}
                Required Skills
              </p>
            }
            name="clientContact.skills"
            className=" mb-10"
          >
            <Controller
              rules={{ required: 'This field is required' }}
              name="clientContact.skills"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <Select
                  tagRender={(props) =>
                    tagRender({ ...props, tagColor: 'tag-purple' })
                  }
                  placeholder="Enter Skills to search"
                  required
                  labelInValue
                  suffixIcon={
                    !checkHenley ? (
                      <></>
                    ) : getValues('clientContact.skills')?.length !== 0 ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )
                  }
                  mode="multiple"
                  // onPopupScroll={(e) => {
                  //   handleSkillScroll(e, 'Skill');
                  // }}
                  {...field}
                  notFoundContent={
                    isLoadingSkills ? <Spin size="small" /> : null
                  }
                  options={skillsOptions.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  showSearch={true}
                  filterSort={(optionA, optionB) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  optionFilterProp="label"
                />
              )}
            />
          </Form.Item>
        </div>
      </div>
      {/* <Divider orientation="left">Categories & Skills</Divider> */}
      <Form.Item
        label={
          <p>
            {/* <span className="text-red-600">*</span>  */}
            Industry
          </p>
        }
        name="clientContact.industries"
      >
        <Controller
          rules={{ required: 'This field is required' }}
          name="clientContact.industries"
          control={control}
          defaultValue={[]}
          render={({ field }) => (
            <Select
              tagRender={(props) =>
                tagRender({ ...props, tagColor: 'tag-blue' })
              }
              placeholder="Select Industry"
              required
              labelInValue
              mode="multiple"
              suffixIcon={
                checkHenley ? (
                  <></>
                ) : (
                  <>
                    {getValues()?.clientContact?.industries.length !== 0 ? (
                      <CheckOutlined className="text-green-600" />
                    ) : (
                      <Image
                        src={deleteIcon}
                        alt="Delete Icon"
                        width="16px"
                        height="16px"
                        preview={false}
                      />
                    )}
                  </>
                )
              }
              onSearch={(searchText) => {
                setContactIndustrySearchText(searchText);
              }}
              onPopupScroll={(e) =>
                handleContactIndustriesScroll(e, 'BusinessSector')
              }
              {...field}
              loading={isLoadingContactIndustries}
              notFoundContent={
                isLoadingContactIndustries ? <Spin size="small" /> : null
              }
              options={contactIndustriesOptions.map((so) => ({
                ...so,
                label: so.name,
                value: so.id,
              }))}
              filterOption={(inputValue, option) =>
                option?.label
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) !== -1
              }
            />
          )}
        />
      </Form.Item>

      <div className="my-8 border-b-2 border-b-[#17c1e8] pt-4 pb-2">
        <span className="font-semibold text-lg ">Additional Information</span>
      </div>
      {/* <Divider orientation="left">Additional Information</Divider> */}

      <Form.Item label="General Comments" name="clientContact.generalCommets">
        <Controller
          render={({ field }) => (
            <Input.Group className="flex gap-2">
              <Input.TextArea
                placeholder="Enter the General Comments or Remarks"
                rows={4}
                {...field}
              />
            </Input.Group>
          )}
          name="clientContact.generalCommets"
          control={control}
        />
      </Form.Item>

      <div className="flex w-full gap-5">
        <div className="w-1/2">
          <Form.Item label="Referred By" name="clientContact.referredBy">
            <Controller
              render={({ field }) => (
                <ConfigProvider
                  theme={{
                    token: {
                      colorBgElevated:
                        contactReferredByOptions[0]?.id === 0
                          ? '#FFFFFF'
                          : '#E0EBF9',
                    },
                  }}
                >
                  <AutoComplete
                    {...field}
                    open={handleCloseContact}
                    onMouseDown={(e) => {
                      setHandleCloseContact(true);
                    }}
                    onPopupScroll={(e) => {
                      handleReferredByScroll(
                        e,
                        getValues().clientContact.companyId
                      );
                    }}
                    options={contactReferredByOptions.map((option) => ({
                      value: option?.id,
                      label: (
                        <div className="grid">
                          <span className="text-base">{option?.name}</span>
                          <div className="contact-details">
                            <div className="flex">
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <MailOutlined />
                                {option?.email ? option?.email : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <PhoneOutlined />
                                {option?.phone ? option?.phone : '-'}
                              </span>
                              <span className="text-gray-500 text-xs">
                                <ContactsOutlined />{' '}
                                {option?.occupation ? option?.occupation : '-'}
                              </span>
                            </div>
                            <div className="flex">
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <EnvironmentOutlined />
                                {option?.address &&
                                option?.address.city &&
                                option?.address.state
                                  ? `${option?.address.city}, ${option?.address.state}`
                                  : option?.address && option?.address.city
                                    ? option?.address.city
                                    : option?.address && option?.address.state
                                      ? option?.address.state
                                      : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <InfoCircleOutlined />{' '}
                                {option?.status ? option?.status : '-'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ),
                      email: option?.email,
                    }))}
                    onSearch={(searchText) => {
                      setValue('clientContact.referredById', null);
                      setHandleCloseContact(true);
                      setValue('clientContact.referredBySelect', null);
                      setValue('clientContact.referredBy', searchText);
                      handleReferredBySearch(
                        searchText,
                        getValues().clientContact.companyId
                      );
                    }}
                    filterOption={(input, option) => {
                      const labelString = option?.label.props.children
                        .map((child) =>
                          child.props ? child.props.children : child
                        )
                        .join(' ');
                      return (
                        labelString
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                    onSelect={(value, record) => {
                      setValue('clientContact.referredById', value);
                      setValue('clientContact.referredBySelect', value);
                      const contact = contactReferredByOptions.find(
                        (co) => co.id === value
                      );
                      setValue('clientContact.referredBy', contact?.name);
                      setHandleCloseContact(false);
                    }}
                    dropdownRender={(menus) => {
                      if (contactReferredByOptions[0]?.id === 0) {
                        return (
                          <Button
                            onClick={(e) => {
                              setValue(
                                'clientContact.referredBy',
                                valueNotFoundReferredBy
                              );
                              setValue(
                                'clientContact.referredBySelect',
                                valueNotFoundReferredBy
                              );
                              setHandleCloseContact(false);

                              setValue('clientContact.referredById', null);
                            }}
                            onMouseLeave={(e) => {
                              setHandleCloseContact(false);
                            }}
                            className="flex gap-1 w-full h-fit bg-white"
                            value={valueNotFoundReferredBy}
                          >
                            <PlusOutlined className="my-auto" />{' '}
                            <span className="my-auto">Add new</span>{' '}
                            {valueNotFoundReferredBy}
                          </Button>
                        );
                      } else {
                        return (
                          <div
                            onMouseLeave={(e) => {
                              setHandleCloseContact(false);
                            }}
                          >
                            <Button
                              onClick={(e) => {
                                setValue(
                                  'clientContact.referredBy',
                                  valueNotFoundReferredBy
                                );
                                setValue(
                                  'clientContact.referredBySelect',
                                  valueNotFoundReferredBy
                                );
                                setHandleCloseContact(false);

                                setValue('clientContact.referredById', null);
                              }}
                              className="flex gap-1 w-full h-fit bg-white"
                              value={valueNotFoundReferredBy}
                            >
                              <PlusOutlined className="my-auto" />{' '}
                              <span className="my-auto">Add new</span>{' '}
                              {valueNotFoundReferredBy}
                            </Button>
                            {menus}
                          </div>
                        );
                      }
                    }}
                  >
                    <Input
                      placeholder="Select Industry"
                      suffix={isLoadingReferredBy ? <Spin /> : ''}
                    />
                  </AutoComplete>
                </ConfigProvider>
              )}
              name="clientContact.referredBy"
              control={control}
            />
          </Form.Item>
        </div>
        <div className="w-1/2">
          <Form.Item
            label="Bullhorn Automation Score"
            name="clientContact.bullhornAutomationScore"
          >
            <Controller
              render={({ field }) => (
                <InputNumber
                  placeholder="Enter Score"
                  className="w-full mt-2"
                  {...field}
                />
              )}
              name="clientContact.bullhornAutomationScore"
              control={control}
            />
          </Form.Item>
        </div>
      </div>
    </div>
  );
}

export default BullhornSubmissionContact;
