import { Button, Collapse, Input, Modal, Progress, Spin, Tag } from 'antd';
import { generateMailAnalyze } from '../../services/search';
import { useEffect, useState } from 'react';
import { LoadingOutlined } from '@ant-design/icons';
import TextArea from 'antd/es/input/TextArea';

const ModalAnalyze = (props) => {
  const {
    openModal = false,
    setOpenModal,
    plainTextContent,
    handleApplyAnalyzeContent,
    dataSubject
  } = props;
  const [dataEmailAnalyze, setDataEmailAnalyze] = useState();
  const [dataEmailAnalyzeItems, setDataEmailAnalyzeItems] = useState();
  const [loading, setLoading] = useState(true);

  const handleGenerateData = async () => {
    setLoading(true);
    const payload = {
      emailContent: plainTextContent,
      emailSubject: dataSubject,
    };
    const { data } = await generateMailAnalyze(payload);
    setDataEmailAnalyze(data?.result);
    await progressData(data?.result);
  };

  console.log('dataEmailAnalyze', dataEmailAnalyze);

  const progressData = (data) => {
    let initPayload = {};
    if (
      data?.data?.filter(
        (item) =>
          item.key === 'subject_length' ||
          item.key === 'subject_mobile_friendly'
      )
    ) {
      initPayload = {
        subject: {
          totalErrors: data?.data?.filter(
            (item) =>
              (item.key === 'subject_length' ||
                item.key === 'subject_mobile_friendly') &&
              item.pass !== 'Pass'
          ).length,
          content: {
            subject_length: data?.data?.find(
              (item) => item.key === 'subject_length'
            ),
            subject_mobile_friendly: data?.data?.find(
              (item) => item.key === 'subject_mobile_friendly'
            ),
          },
        },
      };
    }
    if (
      data?.data?.filter(
        (item) =>
          item.key === 'message_length' ||
          item.key === 'concise' ||
          item.key === 'reading_level' ||
          item.key === 'long_sentences' ||
          item.key === 'adverb'
      )
    ) {
      initPayload = {
        ...initPayload,
        mailContent: {
          totalErrors: data?.data?.filter(
            (item) =>
              (item.key === 'message_length' ||
                item.key === 'concise' ||
                item.key === 'reading_level' ||
                item.key === 'long_sentences' ||
                item.key === 'adverb') &&
              item.pass !== 'Pass'
          ).length,
          content: {
            message_length: data?.data?.find(
              (item) => item.key === 'message_length'
            ),
            concise: data?.data?.find((item) => item.key === 'concise'),
            reading_level: data?.data?.find(
              (item) => item.key === 'reading_level'
            ),
            long_sentences: data?.data?.find(
              (item) => item.key === 'long_sentences'
            ),
            adverb: data?.data?.find((item) => item.key === 'adverb'),
          },
        },
      };
    }
    if (
      data?.data?.filter(
        (item) => item.key === 'pain_point' || item.key === 'value_proposition'
      )
    ) {
      initPayload = {
        ...initPayload,
        saleStructure: {
          totalErrors: data?.data?.filter(
            (item) =>
              (item.key === 'pain_point' || item.key === 'value_proposition') &&
              item.pass !== 'Pass'
          ).length,
          content: {
            pain_point: data?.data?.find((item) => item.key === 'pain_point'),
            value_proposition: data?.data?.find(
              (item) => item.key === 'value_proposition'
            ),
          },
        },
      };
    }

    if (
      data?.data?.filter(
        (item) => item.key === 'personalized' || item.key === 'social_proof'
      )
    ) {
      initPayload = {
        ...initPayload,
        engagement: {
          totalErrors: data?.data?.filter(
            (item) =>
              (item.key === 'pain_point' || item.key === 'social_proof') &&
              item.pass !== 'Pass'
          ).length,
          content: {
            personalized: data?.data?.find(
              (item) => item.key === 'personalized'
            ),
            social_proof: data?.data?.find(
              (item) => item.key === 'social_proof'
            ),
          },
        },
      };
    }

    setDataEmailAnalyzeItems(initPayload);
    setLoading(false);
  };

  console.log('dataEmailAnalyzeItems1', dataEmailAnalyzeItems);

  useEffect(() => {
    if (openModal) {
      handleGenerateData();
    }
  }, [openModal]);

  const items = [
    {
      key: '1',
      label: (
        <div className="flex justify-between items-center">
          <div>
            <div className="text-base font-semibold">Subject line</div>
            <div className="text-sm font-normal">No subject line present</div>
          </div>
          {dataEmailAnalyzeItems?.subject?.totalErrors > 0 && (
            <div className="w-6 h-6 bg-red-500 flex items-center justify-center text-white rounded-full">
              {dataEmailAnalyzeItems?.subject?.totalErrors}
            </div>
          )}
        </div>
      ),
      children: (
        <div>
          <span className="text-base font-medium">
            Update your email with these minor suggestions.
          </span>
          <div>
            <div className="flex items-center">
              <div>
                {dataEmailAnalyzeItems?.subject?.content?.subject_length
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm mt-2">Subject length</div>
                <div className="font-light text-xs">
                  {dataEmailAnalyzeItems?.subject?.content?.subject_length?.why}
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.subject?.content
                  ?.subject_mobile_friendly?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">Mobile-friendly</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.subject?.content
                      ?.subject_mobile_friendly?.why
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const itemsClarity = [
    {
      key: '1',
      label: (
        <div className="flex justify-between items-center">
          <div>
            <div className="text-base font-semibold">Clarity bit</div>
            <div className="text-sm font-normal">A bit unclear</div>
          </div>
          {dataEmailAnalyzeItems?.mailContent?.totalErrors > 0 && (
            <div className="w-6 h-6 bg-red-500 flex items-center justify-center text-white rounded-full">
              {dataEmailAnalyzeItems?.mailContent?.totalErrors}
            </div>
          )}
        </div>
      ),
      children: (
        <div>
          <span className="text-base font-medium">
            Update your email with these minor suggestions.
          </span>
          <div>
            <div className="flex items-center">
              <div>
                {dataEmailAnalyzeItems?.mailContent?.content?.message_length
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm mt-2">Message length</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.mailContent?.content?.message_length
                      ?.why
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.mailContent?.content?.reading_level
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">Reading level</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.mailContent?.content?.reading_level
                      ?.why
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.mailContent?.content?.adverb?.pass ===
                'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">Minimal adverbs</div>
                <div className="font-light text-xs">
                  {dataEmailAnalyzeItems?.mailContent?.content?.adverb?.why}
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.mailContent?.content?.long_sentences
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">No long sentence</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.mailContent?.content?.long_sentences
                      ?.why
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.mailContent?.content?.concise?.pass ===
                'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">Not concise</div>
                <div className="font-light text-xs">
                  {dataEmailAnalyzeItems?.mailContent?.content?.concise?.why}
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const itemsSale = [
    {
      key: '1',
      label: (
        <div className="flex justify-between items-center">
          <div>
            <div className="text-base font-semibold">Sales structure</div>
            <div className="text-sm font-normal">A bit unclear</div>
          </div>
          {dataEmailAnalyzeItems?.saleStructure?.totalErrors > 0 && (
            <div className="w-6 h-6 bg-red-500 flex items-center justify-center text-white rounded-full">
              {dataEmailAnalyzeItems?.saleStructure?.totalErrors}
            </div>
          )}
        </div>
      ),
      children: (
        <div>
          <span className="text-base font-medium">
            Update your email with these minor suggestions.
          </span>
          <div>
            <div className="flex items-center">
              <div>
                {dataEmailAnalyzeItems?.saleStructure?.content?.pain_point
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm mt-2">No pain point</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.saleStructure?.content?.pain_point
                      ?.why
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.saleStructure?.content
                  ?.value_proposition?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">No value proposition</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.saleStructure?.content
                      ?.value_proposition?.why
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const itemEngage = [
    {
      key: '1',
      label: (
        <div className="flex justify-between items-center">
          <div>
            <div className="text-base font-semibold">Engagement</div>
            <div className="text-sm font-normal">A bit unclear</div>
          </div>
          {dataEmailAnalyzeItems?.engagement?.totalErrors > 0 && (
            <div className="w-6 h-6 bg-red-500 flex items-center justify-center text-white rounded-full">
              {dataEmailAnalyzeItems?.engagement?.totalErrors}
            </div>
          )}
        </div>
      ),
      children: (
        <div>
          <span className="text-base font-medium">
            Update your email with these minor suggestions.
          </span>
          <div>
            <div className="flex items-center">
              <div>
                {dataEmailAnalyzeItems?.engagement?.content?.personalized
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm mt-2">Not personalized</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.engagement?.content?.personalized
                      ?.why
                  }
                </div>
              </div>
            </div>
            <div className="flex items-center mt-2">
              <div>
                {dataEmailAnalyzeItems?.engagement?.content?.social_proof
                  ?.pass === 'Pass'
                  ? '✅'
                  : '❌'}
              </div>
              <div className="ml-2">
                <div className="font-medium text-sm">Not interesting</div>
                <div className="font-light text-xs">
                  {
                    dataEmailAnalyzeItems?.engagement?.content?.social_proof
                      ?.why
                  }
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const handleApplyEmail = () => {
    const formattedContent = dataEmailAnalyze?.rewrite_content?.replace(/\n/g, '<br>');
    console.log(formattedContent)
    handleApplyAnalyzeContent(formattedContent, dataEmailAnalyze?.rewrite_subject)
  }

  return (
    <Modal
      footer={
        <div>
          <Button
            type="primary"
            disabled={loading}
            onClick={() =>
              handleApplyEmail()
            }
          >
            Apply rewrite to email
          </Button>
          <Button
            disabled={loading}
            onClick={() =>
              setOpenModal(false)
            }
          >
            Close
          </Button>
        </div>
      }
      title="Email analyzer"
      width={100000}
      open={openModal}
      style={{
        top: 0,
      }}
      onCancel={() => setOpenModal(false)}
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
        </div>
      ) : (
        <div className="analyze-modal-content">
          <div className="analyze-modal-content-left">
            <div className="analyze-score">
              <div className="font-semibold">Overall score</div>
              <div className="flex mt-5">
                <Progress
                  type="circle"
                  percent={dataEmailAnalyze?.pass_percentage}
                />
                <div className="ml-10">
                  <div>
                    <Tag color="error">Keep editing</Tag>
                  </div>
                  <div className="font-light mt-2 font-semibold">
                    Reading time:{' '}
                    <span className="font-semibold">
                      {dataEmailAnalyze?.reading_time}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <div className="font-semibold">Detected tone</div>
              <div className="mt-2">
                <Tag color="processing">Optimistic</Tag>
                <Tag color="processing" className="ml-2">
                  Encouraging
                </Tag>
                <Tag color="processing" className="ml-2">
                  Appreciative
                </Tag>
              </div>
            </div>
            <div className="mt-6">
              <div className="font-semibold">Suggestions</div>
              <div
                className="mt-3"
                style={{
                  height: '300px',
                  overflowY: 'scroll',
                }}
              >
                <Collapse
                  items={items}
                  className="custom-analyze-collapse"
                  expandIcon={({ isActive }) => (
                    <div>
                      {dataEmailAnalyzeItems?.subject?.totalErrors > 0
                        ? '❌'
                        : '✅'}
                    </div>
                  )}
                />
                <Collapse
                  items={itemsClarity}
                  className="custom-analyze-collapse"
                  expandIcon={({ isActive }) => (
                    <div>
                      {dataEmailAnalyzeItems?.mailContent?.totalErrors > 0
                        ? '❌'
                        : '✅'}
                    </div>
                  )}
                />
                <Collapse
                  items={itemsSale}
                  className="custom-analyze-collapse"
                  expandIcon={({ isActive }) => (
                    <div>
                      {dataEmailAnalyzeItems?.saleStructure?.totalErrors > 0
                        ? '❌'
                        : '✅'}
                    </div>
                  )}
                />
                <Collapse
                  items={itemEngage}
                  className="custom-analyze-collapse"
                  expandIcon={({ isActive }) => (
                    <div>
                      {dataEmailAnalyzeItems?.engagement?.totalErrors > 0
                        ? '❌'
                        : '✅'}
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="analyze-modal-content-right">
            <div className="font-semibold">Rewrite with AI</div>
            <div>
              <div style={{ marginTop: '10px' }}>
                <div className="font-semibold">Subject</div>
                <Input
                  style={{ marginTop: '10px' }}
                  value={dataEmailAnalyze?.rewrite_subject}
                ></Input>
              </div>
              <div style={{ marginTop: '10px' }}>
                <div className="font-semibold">Subject</div>
                <TextArea
                  style={{ marginTop: '10px', height: '450px' }}
                  value={dataEmailAnalyze?.rewrite_content}
                ></TextArea>
              </div>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ModalAnalyze;
