import React, { useEffect, useState } from 'react';
import {
  Button,
  Image,
  Modal,
  notification,
  Spin,
  Table,
  Space,
  Form,
} from 'antd';
import moment from 'moment';
import { useForm } from 'react-hook-form';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import Search from 'antd/es/input/Search';

const ModalHotList = ({
  selectedHotList = [],
  onChooseItem = null,
  handleClose = null,
}) => {
  const { getValues, setValue } = useForm();

  const [open, setOpen] = useState(true);
  const [loading, setLoading] = useState(true);
  const [chooseItem, setChooseItem] = useState(null);

  const handleOke = () => {
    if (!chooseItem || chooseItem.length === 0) {
      notification.error({ message: 'Please choose an item' });

      return;
    }

    onChooseItem(chooseItem[0]);
    setOpen(false);
    handleClose();
  };

  const {
    options: data,
    handleSearch: setHotListSearchText,
    handleScrollPopup: handleHotListScroll,
    handleSearch: setHotListText,
    isLoading: loadingHotList,
  } = useInfiniteScrollWithSearch(
    () => searchBullhornData('Tearsheet')(0, 1000)
  );

  useEffect(() => {
    setLoading(true);
    setHotListText('');
    setLoading(false);
  }, []);

  const columns = [
    {
      title: 'Item',
      dataIndex: 'Item',
      width: '90%',
      render: (_, record) => (
        <div className="flex flex-col gap-1">
          <div className="flex justify-between">
            <span className="text-base font-semibold">{record.name}</span>
            <span className="text-xs text-gray-800">
              Date Added: {moment(record.dateAdded).format('MM/DD/YYYY')}
            </span>
          </div>
          <div className="text-xs text-gray-800">
            Client Contacts: {record.clientContacts?.total || 0}
          </div>
        </div>
      ),
    },
  ];

  const rowSelection = {
    onChange: (_selectedRowKeys, selectedRows) => {
      setChooseItem(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: !record.clientContacts?.total,
      name: record.name,
    }),
  };

  useEffect(() => {
    if (!open) {
      setChooseItem(null);
    }
  }, [open]);

  return (
    <>
      {open && (
        <Modal
          className="min-h-[48rem]"
          title="Select Hot List"
          centered
          open={open}
          footer={
            <>
              <div>
                <Button
                  disabled={!chooseItem && true}
                  type="primary"
                  onClick={() => handleOke()}
                >
                  Choose
                </Button>
              </div>
            </>
          }
          onOk={() => handleOke()}
          onCancel={() => {
            handleClose();
            setOpen(false);
          }}
          width={700}
        >
          <div style={{ marginTop: '20px', marginBottom: '20px' }}>
            <Search
              onSearch={(searchText) => setHotListSearchText(searchText)}
              placeholder="input search text"
              style={{ width: 200 }}
              onChange={(e) => setValue('searchText', e.target.value)}
            />
          </div>
          {loading && (
            <div className="w-full flex justify-center h-96 items-center">
              <Spin size="large" />
            </div>
          )}
          {!loading && (
            <Table
              columns={columns}
              dataSource={data
                .filter(({ id }) => id)
                .map((hotList) => ({ ...hotList, key: hotList.id }))}
              showHeader={false}
              rowSelection={{
                type: 'radio',
                ...rowSelection,
              }}
              selectedRowKeys={selectedHotList.map(({ value }) => value)}
              // onScroll={(e) => handleHotListScroll(e, 'Tearsheet')}
              pagination={{ pageSize: 4 }}
              loading={loadingHotList}
            />
          )}
        </Modal>
      )}
    </>
  );
};

export default ModalHotList;
