import { Table, Space, Button, Empty, notification, Modal, Form } from 'antd';
import { CheckOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import React, { useState, useEffect, useMemo } from 'react';
import {
  insertBullhorn,
  searchBullhorn,
  upladteBullhorn,
} from '../../../services/bullhorn';
import BullhornSubmissionContact from '../BullhornSubmissionContact';
import { useForm } from 'react-hook-form';
import { COMMON_STRINGS } from '../../../constants/common.constant';
import { Paper, TableContainer } from '@mui/material';
import ExistingContactItems from '../ExistingContactItems';
import { validListEmail } from '../../../services/emailFinder';

function ExistingContactTable(props) {
  const { dataSource, updateExistingContacts } = props;
  const [isAddContactForm, setIsAddContactForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [flagEditContact, setFlagEditContact] = useState(false);
  const [detailDataContact, setDetailDataContact] = useState([]);
  const [listAddContactSelected, setListAddContactSelected] = useState([]);
  const [isSubmitForm, setIsSubmitForm] = useState(false);
  const [handleCloseClient, setHandleCloseClient] = useState(true);
  const [handleCloseContact, setHandleCloseContact] = useState(true);
  const [isContactActionLoading, setContactActionLoading] = useState(false);
  const [listEmailChecked, setListEmailChecked] = useState([]);
  const [listEmail, setListEmail] = useState([]);

  const handleEditContact = async (fullName, email) => {
    setIsAddContactForm(true);
    setIsLoading(true);
    setFlagEditContact(true);
    const { data } = await searchBullhorn(
      'ClientContact',
      0,
      5,
      '',
      '',
      '',
      email
    );
    if (data.result.length === 0) {
      notification.success({
        message: `This contact not existed on Bullhorn`,
      });
    } else {
      handleSetDataEditContact(data.result[0]);
      setIsLoading(false);
      setIsAddContactForm(true);
    }
  };

  const handleSetDataEditContact = async (data) => {
    setDetailDataContact(data);
    setFlagEditContact(true);
  };

  const { setValue, getValues, control, watch } = useForm();

  const handleResetFormAddContact = async () => {
    setValue('clientContact.namePrefixSelect', null);
    setValue('clientContact.namePrefix', null);
    setValue('clientContact.firstName', null);
    setValue('clientContact.middleName', null);
    setValue('clientContact.surename', null);
    setValue('clientContact.consultantId', null);
    setValue('clientContact.consultant', null);
    setValue('clientContact.consultantSelect', null);
    setValue('clientContact.statusSelect', null);
    setValue('clientContact.type', null);
    setValue('clientContact.secondaryOwnerSelect', null);
    setValue('clientContact.companyId', null);
    setValue('clientContact.companySelect', null);
    setValue('clientContact.company', null);
    setValue('clientContact.department', null);
    setValue('clientContact.jobTitle', null);
    setValue('clientContact.workEmail', null);
    setValue('clientContact.personalEmail', null);
    setValue('clientContact.workPhone', null);
    setValue('clientContact.mobilePhone', null);
    setValue('clientContact.otherPhone', null);
    setValue('clientContact.fax', null);
    setValue('clientContact.stateId', null);
    setValue('clientContact.state', null);
    setValue('clientContact.county', null);
    setValue('clientContact.countySelect', null);
    setValue('clientContact.address', null);
    setValue('clientContact.city', null);
    setValue('clientContact.zip', null);
    setValue('clientContact.industries', []);
    setValue('clientContact.generalCommets', null);
    setValue('clientContact.referredById', null);
  };

  // const handleValidEmail = async () => {
  //   setContactActionLoading(true);
  //   const listEmailToSave = dataSource
  //     .filter((item) => {
  //       return listEmail?.includes(item?.id);
  //     })
  //     .map((item) => item?.email);
  //   const { data } = await validListEmail({
  //     emails: listEmailToSave,
  //   });
  //   setContactActionLoading(false);
  //   setListEmailChecked(data?.result);
  // };

  const columnsExistingContacts = [
    // {
    //   title: '',
    //   dataIndex: 'index',
    //   key: 'index',
    //   render: (text, record) => (
    //     <>
    //       <Checkbox
    //         checked={listEmail.includes(record?.email)}
    //         onChange={() => changeListEmail(record?.email)}
    //       ></Checkbox>
    //     </>
    //   ),
    // },
    {
      title: COMMON_STRINGS.NAME,
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="font-semibold">{text}</span>,
    },
    {
      title: COMMON_STRINGS.EMAIL,
      dataIndex: 'email',
      key: 'email',
      width: '25%',
      render: (text, record) => (
        <ExistingContactItems
          record={record}
          listEmailChecked={listEmailChecked}
        />
        // <></>
      ),
    },
    {
      title: COMMON_STRINGS.OCCUPATION,
      dataIndex: 'occupation',
      key: 'occupation',
    },
    {
      title: COMMON_STRINGS.PHONE,
      dataIndex: 'phone',
      width: '15%',
      key: 'phone',
    },
    {
      title: COMMON_STRINGS.STATUS,
      width: '10%',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: COMMON_STRINGS.ACTION,
      key: 'action',
      align: 'center',
      render: (text, record) => (
        <Space size="small">
          {/* <Popconfirm
            title="Delete the contact"
            description="Are you sure to delete this contact?"
            onConfirm={async () => {
              const { id } = record;
              try {
                const res = await deleteBullhornContact({
                  id,
                  entity: 'ClientContact',
                });
                const newContactOptions = [
                  ...functionContactClient.contactOptions.filter(
                    (contact) => contact?.id !== id
                  ),
                ];
                functionContactClient.contactSetOptions([...newContactOptions]);
                notification.success({
                  description: 'Contact deleted!',
                });
              } catch (error) {
                console.log('error delete contact: ', error);
                notification.error({
                  description: 'Network error! Try again later!',
                });
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button className="text-red-400 border-red-300">
              <DeleteOutlined />
            </Button>
          </Popconfirm> */}
          <Button
            disabled={isLoading}
            onClick={async () => {
              handleEditContact(record.name, record.email).finally(() => {
                setIsAddContactForm(true);
                setFlagEditContact(true);
                setIsLoading(false);
              });
            }}
          >
            <EditOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const handleSubmitAddContact = async () => {
    setIsSubmitForm(true);
    const {
      companyId,
      firstName,
      surename,
      consultant,
      jobTitle,
      address,
      industries,
    } = getValues()?.clientContact;

    if (!companyId) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Company name is required.' });
    } else if (!firstName) {
      setIsSubmitForm(false);
      return notification.error({ message: 'First Name is required.' });
    } else if (!surename) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Sure Name number is required.' });
    } else if (!consultant) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Consultant address is required.' });
    } else if (!jobTitle) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Job Title is required.' });
    } else if (!address) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Address is required.' });
    } else if (industries.length === 0) {
      setIsSubmitForm(false);
      return notification.error({ message: 'Industries is required.' });
    }

    if(address?.length > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }


    const payload = {
      entityName: 'ClientContact',
      namePrefix: getValues()?.clientContact?.namePrefixSelect,
      firstName: getValues()?.clientContact?.firstName,
      middleName: getValues()?.clientContact?.middleName,
      lastName: getValues()?.clientContact?.lastName,
      owner: {
        id: getValues()?.clientContact.consultantId,
      },
      status: getValues()?.clientContact?.statusSelect,
      type: getValues()?.clientContact?.type,
      secondaryOwners: {
        replaceAll: getValues()?.clientContact?.secondaryOwnerSelect,
      },
      clientCorporation: {
        id: getValues()?.clientContact.companyId,
      },
      division: getValues()?.clientContact?.department,
      occupation: getValues()?.clientContact?.jobTitle,
      email: getValues()?.clientContact?.workEmail,
      email2: getValues()?.clientContact?.personalEmail,
      phone: getValues()?.clientContact?.workPhone,
      mobile: getValues()?.clientContact?.mobilePhone,
      phone2: getValues()?.clientContact?.otherPhone,
      fax: getValues()?.clientContact?.fax,
      address: {
        countryID: getValues()?.clientContact?.stateId,
        countryName: getValues()?.clientContact?.state,
        state: getValues()?.clientContact?.county,
        address1: getValues()?.clientContact?.address,
        city: getValues()?.clientContact?.city,
        zip: getValues()?.clientContact?.zip,
      },
      businessSectors: {
        replaceAll: getValues()?.clientContact?.industries.map(
          (obj) => obj.value
        ),
      },
      comments: getValues()?.clientContact?.generalCommets,
      referredByPerson: {
        id: getValues().clientContact.referredById || null,
      },
      name: `${getValues()?.clientContact?.firstName} ${
        getValues()?.clientContact?.lastName
      }`,
    };

    if (getValues()?.clientContact?.address > 100) {
      notification.error({
        message: 'Address is too long',
        description: 'The Address cannot be longer than 100 characters.',
      });
      return;
    }

    const cleanPayload = (payload) => {
      if (payload === null || payload === undefined) {
        return {};
      }

      const cleanObject = {};
      Object.keys(payload).forEach((key) => {
        const value = payload[key];

        if (value !== '' && value !== undefined) {
          if (value !== '' && value !== null) {
            if (value.length !== 0) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedSubObject = cleanPayload(value);
                if (Object.keys(cleanedSubObject).length !== 0) {
                  cleanObject[key] = cleanedSubObject;
                }
              } else if (Array.isArray(value) && value.length > 0) {
                const cleanedArray = value.reduce((acc, item) => {
                  if (item !== '' && item !== undefined) {
                    acc.push(item);
                  }
                  return acc;
                }, []);
                cleanObject[key] = cleanedArray;
              } else {
                cleanObject[key] = value;
              }
            }
          }
        }
      });

      return cleanObject;
    };

    const newContactPayloadCleaned = cleanPayload(payload);
    const { data } = flagEditContact
      ? await upladteBullhorn(
          getValues()?.clientContact?.id,
          newContactPayloadCleaned
        )
      : await insertBullhorn(newContactPayloadCleaned);
    // functionContactClient.handleContactSearch('', getValues().companyId);
    const isDataExist = listAddContactSelected.some(
      (item) => item.id === data?.result?.changedEntityId
    );
    if (!isDataExist) {
      setListAddContactSelected([
        ...listAddContactSelected,
        { ...data?.result?.data, id: data?.result?.changedEntityId },
      ]);
    }
    setIsLoading(false);
    setIsAddContactForm(false);
    notification.success({
      message: `Success ${flagEditContact ? 'edit' : 'add'} contact ${data.result.data.name}`,
    });
    setFlagEditContact(false);
    updateExistingContacts();
  };

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setListEmail(newSelectedRowKeys);
  };

  const rowTableExistingMailsSelection = {
    selectedRowKeys: listEmail,
    onChange: onSelectChange,
  };

  const handleValidEmail = async () => {
    setContactActionLoading(true);
    const listEmailToSave = dataSource
      .filter((item) => {
        return listEmail?.includes(item?.id);
      })
      .map((item) => item?.email);

    const { data } = await validListEmail({
      emails: listEmailToSave,
    });

    setContactActionLoading(false);
    setListEmailChecked(data?.result);
  };

  return (
    <>
      <Button
        loading={isContactActionLoading}
        disabled={listEmail?.length === 0}
        onClick={handleValidEmail}
        type="primary"
        className="!border-[#b2b8be] flex gap-2 items-center text-[#fff]"
      >
        <p className="Montserrat">{`Validate ${listEmail?.length} Email(s)`}</p>
        <CheckOutlined />
      </Button>
      <TableContainer
        className="search-table-new-design-container"
        component={Paper}
        style={{ maxHeight: '600px', overflowY: 'auto', marginTop: '10px' }}
        onScroll={(e) => {
          // functionContactClient.handleContactScroll(
          //   e,
          //   'ClientContact',
          //   getValues().companyId
          // );
        }}
      >
        {' '}
        <Table
          columns={columnsExistingContacts}
          dataSource={dataSource}
          rowKey={(record) => record.id}
          rowSelection={rowTableExistingMailsSelection}
          locale={{
            emptyText: (
              <Empty description="No contacts found" className="w-full" />
            ),
          }}
          pagination={false}
        />
      </TableContainer>
      {isAddContactForm && (
        <Modal
          width={1000}
          style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 100px)' }}
          title={`${flagEditContact ? 'Edit Contact Form' : 'Add Contact Form'}`}
          open={isAddContactForm}
          onCancel={() => {
            setIsAddContactForm(false);
            setFlagEditContact(false);
            handleResetFormAddContact();
            setHandleCloseClient(false);
            setHandleCloseContact(false);
          }}
          footer={false}
          // onOk={updateExistingContacts()}
        >
          <Form layout="vertical">
            <BullhornSubmissionContact
              watch={watch}
              control={control}
              setValue={setValue}
              getValues={getValues}
              handleCloseClient={handleCloseClient}
              setHandleCloseClient={setHandleCloseClient}
              handleCloseContact={handleCloseContact}
              setHandleCloseContact={setHandleCloseContact}
              detailDataContact={detailDataContact}
              // flagDetailContact={flagDetailContact}
              flagEditContact={flagEditContact}
              fromLead={true}
            />
            <div className="left-0 bottom-0 w-full">
              <div className="flex gap-4 ml-auto mr-8">
                <Button
                  onClick={() => {
                    setIsAddContactForm(false);
                    setFlagEditContact(false);
                    handleResetFormAddContact();
                  }}
                  className="ml-auto border-red-500 text-red-500"
                >
                  Cancel
                </Button>
                <Button
                  htmlType="button"
                  onClick={handleSubmitAddContact}
                  type="primary"
                  // disabled={isSubmitForm}
                  // loading={isSubmitForm}
                >
                  Save
                </Button>
              </div>
            </div>
          </Form>
        </Modal>
      )}
    </>
  );
}

export { ExistingContactTable };
