import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Button, Input, Modal, notification } from 'antd';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import { updateSequenceName } from '../../services/jobs';

const ModalEditNameSequence = ({
  getValues,
  setValue,
  openEditNameModal,
  setOpenEditNameModal,
  isDependency = false,
  sequenceId = '',
}) => {
  const [defaultName, setDefaultName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSave = async () => {
    if (isDependency) {
      setLoading(true);
      try {
        const { data } = await updateSequenceName(sequenceId, defaultName);
        setLoading(true);

        setValue('sequenceData.name', defaultName);
        setOpenEditNameModal(false);
      } catch (error) {
        notification.error({
          description: 'Something went wrong! Try again later!',
        });
        console.log('error: ', error);
        setLoading(false);
      }
    } else {
      setValue('sequenceData.name', defaultName);
      setOpenEditNameModal(false);
    }
  };

  useEffect(() => {
    setDefaultName(getValues('sequenceData.name') || '');
  }, [getValues('sequenceData.name')]);

  return (
    <div className="flex items-center w-full">
      {/* <Modal
        open={openEditNameModal}
        onCancel={() => setOpenEditNameModal(false)}
        footer={
          <>
            <Button onClick={() => {setOpenEditNameModal(false)}}>Cancel</Button>
            <Button type={"primary"} onClick={handleSave}>Save</Button>
          </>
        }
      > */}
      <Input
        className="!border-t-0 !border-r-0 !border-l-0 !rounded-none w-full"
        variant="borderless"
        placeholder="Sequence name..."
        value={defaultName}
        onChange={(e) => setDefaultName(e.target.value)}
      />
      <Button
        type="text"
        disabled={loading}
        className={clsx(
          'font-semibold hover:text-[#858f96] text-[#5a5f7d]',
          !isDependency && 'bg-[#fff]'
        )}
        onClick={() => {
          setOpenEditNameModal(false);
        }}
        danger
        icon={<CloseOutlined />}
      ></Button>
      <Button
        disabled={!defaultName?.trim()}
        loading={loading}
        type="text"
        className={clsx(
          'font-semibold hover:text-[#2684c7] text-[#5a5f7d]',
          !isDependency && 'bg-[#fff]'
        )}
        onClick={handleSave}
        icon={<CheckOutlined />}
      ></Button>
      {/* </Modal> */}
    </div>
  );
};

export default ModalEditNameSequence;
