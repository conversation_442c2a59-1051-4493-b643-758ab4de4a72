import { CKEditor } from '@ckeditor/ckeditor5-react';
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  Spin,
  Tag,
  notification,
} from 'antd';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import {
  ContactsOutlined,
  EditOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import {
  generateContent,
  generateSubject,
  generateContentChildSubject,
  generateContentChildContent,
} from '../../services/search';
import ModalCreateFromTemplate from '../../containers/Sequence/ModalCreateFromTemplate';
import { ClassicEditor } from 'ckeditor5';

const EmailEditContent = (props) => {
  const {
    step,
    control,
    setValue,
    getValues,
    job,
    disabled = false,
    content,
    subject,
    listData,
    setListData,
    isParentEmail = false,
    defaultUserSelected,
    handleChangeEmail,
    emailConfigData,
    setEmailConfigData,
  } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentContent, setCurrentContent] = useState();
  const [currentObject, setCurrentObject] = useState();
  const [isSubjectLoading, setIsSubjectLoading] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(false);

  // Sequence Template features
  const [openSequenceTemplateOptions, setOpenSequenceTemplateOptions] =
    useState(false);

  const handleCloseTemplateOptions = () =>
    setOpenSequenceTemplateOptions(false);

  const onChooseTemplate = (chooseItem) => {
    const sequenceItemData = JSON.parse(chooseItem?.content);

    const { rawSequence: rawSequenceAllSteps } = sequenceItemData?.triggerItem;
    if (rawSequenceAllSteps?.length === 0 || !rawSequenceAllSteps) {
      notification.warning({
        description: "This template doesn't have any steps!",
      });
      return;
    }
    const rawSequence = rawSequenceAllSteps.filter(
      (item) => item?.type === 'EMAIL'
    );

    const content = rawSequence[0].content || '';
    const subject = rawSequence[0].subject || '';
    setCurrentObject(subject);
    setCurrentContent(content);
  };

  const defaultUser = getValues('sendMail.listEmail')?.map((email) => {
    return { value: email, label: email };
  });

  useEffect(() => {
    if (isModalOpen) {
      if (!isParentEmail) {
        const foundObject = listData.find((item) => item.key === step);
        setCurrentContent(foundObject?.content ?? null);
        setCurrentObject(foundObject?.subject ?? null);
      } else {
        setCurrentContent(emailConfigData?.[0]?.content ?? content);
        setCurrentObject(emailConfigData?.[0]?.subject ?? subject);
      }
    }
  }, [isModalOpen, subject, content]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    if (!isParentEmail) {
      setIsModalOpen(false);
      const newArr = updateObjectInArray(
        listData,
        step,
        currentContent,
        currentObject
      );
      setListData([...newArr]);
    } else {
      setIsModalOpen(false);
      setValue('sendMail.newParentContent', currentContent);
      setValue('sendMail.newParentObject', currentObject);
    }
    notification.success({
      message: 'Mail Edit',
      description: 'Mail Step edit successfully',
    });
  };

  const updateObjectInArray = (arr, keyToUpdate, newContent, newSubject) => {
    const indexToUpdate = arr.findIndex((item) => item.key === keyToUpdate);
    if (indexToUpdate !== -1) {
      arr[indexToUpdate].content = newContent;
      arr[indexToUpdate].subject = newSubject;
    }
    return arr;
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleCKEditorChange = async (event, editor) => {
    const data = editor.getData();
    setCurrentContent(data);
  };

  const handleSubjectInput = async (event) => {
    if (isParentEmail) {
      const updatedEmails = [...emailConfigData];
      updatedEmails[0].subject = event.target.value;
      setEmailConfigData(updatedEmails);
    }
    const data = event.target.value;
    setCurrentObject(data);
  };

  const refreshSubject = async () => {
    try {
      setIsSubjectLoading(true);
      let result = null;
      if (job?.job_id || job?.job_board_id || job?.id) {
        const { data } = await generateSubject(
          job?.job_id || job?.job_board_id || job?.id
        );
        result = data?.result;
      } else {
        const { data } = await generateContentChildSubject({ title: 'random' }); // temp solution for sequence template features
        result = data?.result;
      }

      setCurrentObject(result?.subject ?? `The content is being processed`);
      setIsSubjectLoading(false);
    } catch (e) {
      setIsSubjectLoading(true);
      setValue('sendMail.subject', `The content is being processed`);
      notification.error({
        message: 'Error!',
        description: 'Something went wrong!',
      });
    }
  };

  const refreshContent = async () => {
    console.log('refreshContent: ', refreshContent);
    const dataToGen = {
      jobBoardId: job?.job_id || job?.job_board_id || job?.id,
      unfinishedContent: currentContent,
    };

    try {
      setIsContentLoading(true);
      setCurrentContent(`The content is being processed`);

      let result = null;
      if (job?.job_id || job?.job_board_id || job?.id) {
        const { data } = await generateContent(dataToGen);
        result = data?.result;
      } else {
        const { data } = await generateContentChildContent({ title: 'random' }); // temp solution for sequence template features
        result = data?.result;
      }

      // const { data } = await generateContent(dataToGen);
      setCurrentContent(result?.content ?? `The content is being processed`);
      setIsContentLoading(false);
    } catch (e) {
      setIsContentLoading(true);
      setCurrentContent(`The content is being processed`);
      notification.error({
        message: 'Error!',
        description: 'Something went wrong!',
      });
    }
  };

  return (
    <>
      {/* <Button
        style={{
          fontSize: '12px',
          width: '100%',
          borderRadius: '0',
          border: '2px solid #ccc',
        }}
        type="primary"
        onClick={showModal}
        disabled={disabled}
      >
        Custom Email
      </Button> */}
      <div
        onClick={showModal}
        // style={{ position: 'absolute', left: '16px', bottom: '14px' }}
      >
        <div style={{ cursor: 'pointer' }}>
          {/* <EditOutlined /> */} Edit This Email
        </div>
      </div>
      <Modal
        title={`Edit step  ${isParentEmail ? 'parent' : step}`}
        open={
          isModalOpen == false
            ? false
            : isParentEmail
              ? content && subject
                ? true
                : false
              : isModalOpen
        }
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
      >
        <Form>
          <label
            for={`mailStep.step_${step}.subject`}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <p>
              <span className="text-red-600">*</span> Send to ( Email)
            </p>
          </label>
          <Form.Item name="sendMail.listEmail">
            <Controller
              render={({ field }) => (
                <>
                  <Select
                    mode="multiple"
                    allowClear
                    style={{ width: '100%' }}
                    placeholder="Please select"
                    defaultValue={defaultUser}
                    onChange={(value, options) => {
                      handleChangeEmail(value, options);
                    }}
                    options={defaultUserSelected?.map((option) => ({
                      value: option?.name,
                      label: option?.email,
                      email: option?.email,
                      phone: option?.phone,
                      address: option?.address,
                      status: option?.status,
                      occupation: option?.occupation,
                      name: option?.name,
                    }))}
                    // disabled={isParentEmail ? false : true}
                    optionLabelProp="name"
                    optionRender={(opt) => {
                      const { data: option } = opt;
                      return (
                        <div className="grid">
                          <span className="text-base">{option.name}</span>
                          <div className="contact-details">
                            <div className="flex">
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <MailOutlined />
                                {option.email ? option.email : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <PhoneOutlined />
                                {option.phone ? option.phone : '-'}
                              </span>
                              <span className="text-gray-500 text-xs">
                                <ContactsOutlined />{' '}
                                {option.occupation ? option.occupation : '-'}
                              </span>
                            </div>
                            <div className="flex">
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <EnvironmentOutlined />
                                {option.address &&
                                option.address.city &&
                                option.address.state
                                  ? `${option.address.city}, ${option.address.state}`
                                  : option.address && option.address.city
                                    ? option.address.city
                                    : option.address && option.address.state
                                      ? option.address.state
                                      : '-'}
                              </span>
                              <span className="text-gray-500 text-xs min-w-[200px]">
                                <InfoCircleOutlined />{' '}
                                {option.status ? option.status : '-'}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    }}
                    tagRender={(props) => {
                      const { label, closable, onClose } = props;
                      return (
                        <Tag
                          closable={closable}
                          onClose={onClose}
                          style={{ marginRight: 3 }}
                        >
                          <span className="mr-2 bg-blue-500 rounded-full w-2 h-2 inline-block"></span>
                          {label}
                        </Tag>
                      );
                    }}
                  />
                </>
              )}
              name="sendMail.listEmail"
              control={control}
            />
          </Form.Item>
          <label
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <p>
              <span className="text-red-600">*</span> From
            </p>
          </label>
          <Form.Item name="sendMail.from">
            <Controller
              render={({ field }) => <Input required {...field} />}
              name="sendMail.from"
              control={control}
            />
          </Form.Item>
          <label
            for={`mailStep.step_${step}.subject`}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <p>
              <span className="text-red-600">*</span> Subject
            </p>
            <Button
              type="text"
              shape="circle"
              onClick={refreshSubject}
              style={{ background: '#ffffff' }}
              icon={
                <SyncOutlined
                  spin={isSubjectLoading}
                  style={{ color: '#4096ff' }}
                />
              }
            />
          </label>

          <Form.Item name={`mailStep.step`} style={{ marginTop: '10px' }}>
            <Input.Group compact>
              <Controller
                render={({ field }) => (
                  <Input
                    style={{ paddingLeft: isSubjectLoading ? '40px' : '10px' }}
                    required
                    {...field}
                    value={currentObject}
                    defaultValue={currentObject}
                    onChange={handleSubjectInput}
                  />
                )}
                name={`mailStep.step`}
                control={control}
              />
              {isSubjectLoading && (
                <div style={{ marginTop: '-30px', marginLeft: '10px' }}>
                  {<Spin />}
                </div>
              )}
            </Input.Group>
          </Form.Item>
          <label
            for={`mailStep.step_${step}.content`}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <div className="flex gap-2">
              <p className="self-center">
                <span className="text-red-600">*</span> Content
              </p>
              <Button
                disabled={isSubjectLoading || isContentLoading}
                onClick={() => {
                  setOpenSequenceTemplateOptions(true);
                }}
                style={{ background: '#ffffff' }}
              >
                Choose content by template
              </Button>
            </div>
            <div>
              <Button
                type="text"
                onClick={refreshContent}
                style={{ background: '#ffffff' }}
                icon={
                  <SyncOutlined
                    spin={isContentLoading}
                    style={{ color: '#4096ff' }}
                  />
                }
              />
            </div>
          </label>
          <Form.Item
            style={{ marginTop: '10px' }}
            name={`mailStep.step_${step}.content`}
          >
            <Controller
              render={({ field }) => (
                <>
                  {/* <CKEditor
                    editor={ClassicEditor}
                    data={currentContent ?? null}
                    {...field}
                    style={{ height: '500px', paddingLeft: '50px' }}
                    onChange={handleCKEditorChange}
                  /> */}
                </>
              )}
              name={`mailStep.step_${step}.content`}
              control={control}
            />
            {isContentLoading && (
              <div style={{ marginTop: '-40px', marginLeft: '10px' }}>
                {<Spin />}
              </div>
            )}
          </Form.Item>
        </Form>

        {/* Send Mail Options Modal */}
        {openSequenceTemplateOptions && (
          <Modal
            title="Send Mail Options"
            open={openSequenceTemplateOptions}
            // onOk={}
            onCancel={handleCloseTemplateOptions}
            footer=""
          >
            <div
              style={{
                // padding: '20px',
                // display: 'flex',
                // justifyContent: 'space-between',
                textAlign: '-webkit-center',
              }}
            >
              <ModalCreateFromTemplate
                fromBullhornSendEmailModal={true}
                onChooseTemplate={onChooseTemplate}
                handleCloseTemplateOptions={handleCloseTemplateOptions}
              />
            </div>
          </Modal>
        )}
      </Modal>
    </>
  );
};

export default EmailEditContent;
