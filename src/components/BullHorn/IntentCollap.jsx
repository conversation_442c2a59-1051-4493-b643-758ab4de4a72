import { DownOutlined, RightOutlined } from '@ant-design/icons';
import { Checkbox, Spin } from 'antd';
import { useState } from 'react';
import { employeeFinderIntentByTopic } from '../../services/employee';

const IntentCollap = (props) => {
  const { item, setListDataIntentSetting, listDataIntentSetting } = props;
  const [focusStatus, setFocusStatus] = useState(false);
  const [dataIntent, setDataIntent] = useState([]);
  const [loadingGetIntent, setLoadingGetIntent] = useState(false);
  const handleShowData = async () => {
    setFocusStatus(!focusStatus);
    if (dataIntent?.length == 0) {
      setLoadingGetIntent(true);
      const data = await employeeFinderIntentByTopic(item);
      setDataIntent(data ? data?.data?.categories[0]?.topics : []);
      setLoadingGetIntent(false);
    }
  };

  const handleChoseIntent = async (e) => {
    let result = dataIntent.filter(obj => obj.id === e.target.value);
    let index = listDataIntentSetting?.find(obj => obj.id === e.target.value);
    if(index) {
        const newArr = listDataIntentSetting?.filter(obj => obj.id !== e.target.value);
        setListDataIntentSetting(newArr);
    } else {
        const newArr = listDataIntentSetting?.concat(result);
        setListDataIntentSetting(newArr);
    }
  }

  const listDefaultValue = listDataIntentSetting?.map(item => item.id)

  return (
    <>
      <div
        style={{ fontSize: '15px', fontWeight: '600', marginTop: '10px' }}
        onClick={() => handleShowData()}
      >
        {focusStatus ? <DownOutlined /> : <RightOutlined />} {item}
      </div>
      {focusStatus && (
        <div style={{ marginLeft: '15px' }}>
          {loadingGetIntent ? (
            <>
              <Spin />
            </>
          ) : (
            <Checkbox.Group defaultValue={listDefaultValue} style={{ width: '100%' }}>
                {dataIntent?.map((item, index) => (
                    <div style={{ marginTop: '5px', width: '100%' }}>
                      <Checkbox defaultChecked onChange={handleChoseIntent} value={item?.id}>{item?.name}</Checkbox>
                    </div>
                ))}
            </Checkbox.Group>
          )}
        </div>
      )}
    </>
  );
};

export default IntentCollap;
