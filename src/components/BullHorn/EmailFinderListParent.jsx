/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';

import List from './EmailFinderList';
import { useForm } from 'react-hook-form';
import {
  sendJobToBullhorn,
  searchBullhornData,
  getCountries,
} from '../../services/bullhorn';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import useGoogleMapAddressDetails from '../../hooks/useGoogleMapAddressDetails';
import { searchAddressWithOpenStreet } from '../../services/googleMap';
import { Segmented, Tabs } from 'antd';
import EmailFinderCompanyTab from './EmailFinderTabCompany';
import LinkedInFinderTab from './LinkedInFinderTab';

export const UK_NAME = 'United Kingdom';
export const UK_CODE = '2359';

const JobSubmissionModal = ({ job = null }) => {
  const [isModalVisible, setIsModalVisible] = useState(true);
  const fromManualCreate = false;
  const { handleSubmit, control, setValue, watch, reset, getValues } = useForm({
    defaultValues: { skills: [], contactEmailStatus: [], searchSignalIds: [] },
  });
  const [handleCloseClient, setHandleCloseClient] = useState(true);
  const [handleCloseContact, setHandleCloseContact] = useState(true);
  const [showCompany, setShowCompany] = useState(false);

  const {
    options: contactOptions,
    setOptions: contactSetOptions,
    handleScrollPopup: handleContactScroll,
    handleSearch: handleContactSearch,
    isLoading: isLoadingContacts,
    setLoading: setIsLoadingContacts,
    valueNotFound: valueNotFoundContacts,
    setCompanyId,
    setStart: contactSetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  // const {
  //   setPlaceId,
  //   placeId,
  //   isLoading: isLoadingAddressDetails,
  // } = useGoogleMapAddressDetails(setValue, false, 'Address');

  const [fromStep, setFromStep] = useState('Vacancy');
  const [isSearchCompany, setIsSearchCompany] = useState(false);
  const [jobDetailCompanyId, setJobDetailCompanyId] = useState(false);

  const handleEffectData = async () => {
    if (job) {
      if (!job.companyId) {
        setValue('companyId', job.companyId ? job.companyId : null);
        setJobDetailCompanyId(true);
      }
      setValue('employmentType', job.jobtype ? job.jobtype.toLowerCase() : '');
      setValue('company', job.company ? job.company : '');
      companySetStart(0);
      handleCompanySearch(job.company ? job.company : ' ');
      setValue('description', job.description ? job.description : '');
      setValue('cleanDescription', job?.description ? job?.description : '');
      setValue('jobtitle', job.jobtitle ? job.jobtitle : '');
      setValue('salary', job.salary ? job.salary : '');
      setValue('min_salary', job.min_salary ? job.min_salary : '');
      setValue('max_salary', job.max_salary ? job.max_salary : '');
      if (job.salary) {
        const salaryTemp =
          job?.salary && job?.salary?.toString()?.split(' Per ');
        setValue(
          'salaryUnit',
          salaryTemp?.length > 1 ? `Per ${salaryTemp?.[1] || ''}` : ''
        );
      }
      setValue(
        'jobtype',
        !job.jobtype
          ? []
          : job.jobtype.split(',').length > 0
            ? job.jobtype.split(',')
            : []
      );
      setValue('email', job.email ? job.email : '');
      setValue('source', 'Zileo');
      setValue(
        'joblocationcity',
        job.joblocationcity ? job.joblocationcity : ''
      );
      await handleGetDefaultAddress(
        job.joblocationcity ? job.joblocationcity : ''
      );
      const companyName = `Company: ${job.company ? job.company : ''}\n`;
      const jobType = `JobType: ${
        !job.jobtype
          ? '-'
          : job.jobtype.split(',').length > 0
            ? job.jobtype.split(',')
            : '-' || '-'
      }\n`;
      const minMaxSalary = `Range: ${
        (job.min_salary &&
          `${job.min_salary ? job.min_salary : ''} - ${
            job.max_salary ? job.max_salary : ''
          }`) ||
        '-'
      }\n`;
      const salary = `Salary: ${job.salary || '-'}\n`;
      const source = `Source: ${watch('source') || '-'}\n`;
      const address = `Location: ${
        watch('address1') ? watch('address1') + ',' : ''
      } ${watch('city') ? watch('city') + ',' : ''} ${
        watch('county') ? watch('county') + ',' : ''
      } ${watch('zip') ? watch('zip') + ',' : ''} ${
        watch('state') ? watch('state') + '.' : ''
      }\n`;
      setValue(
        'description',
        ` ${companyName} ${jobType} ${minMaxSalary} ${address} ${salary} ${source} ${watch(
          'description'
        )}`
      );
    } else {
      reset();
      setValue('source', 'Zileo');
      setValue('employmentType', 'temporary');
      setValue('state', UK_NAME);
      setValue('stateId', UK_CODE);
      setValue('stateSelected', UK_CODE);
      setValue('clientContact.stateId', null);
    }
  };

  useEffect(() => {
    handleEffectData();
  }, [job, isModalVisible]);

  const handleGetDefaultAddress = async (inputJob) => {
    if (inputJob) {
      const { data } = await searchAddressWithOpenStreet(inputJob);
      // if(data?.sourceKey === "LAMBDA") {
      //   setValue('zip', data?.data[0]?.address?.postcode);
      //   setValue('county', data?.data[0]?.address?.county || data?.data[0]?.address?.city);
      //   setValue('countySelect', data?.data[0]?.address?.county || data?.data[0]?.address?.city);
      //   setValue('city', data?.data[0]?.address?.city);
      //   setValue('state', data?.data[0]?.address.state);
      //   setValue('address1', inputJob);
      //   const country = await getCountries(data?.data[0]?.address?.country);
      //   setValue('stateId', `${country?.data?.result[0].value}`);
      //   setValue('stateSelected', `${country?.data?.result[0].value}`);
      // } else {
      const filteredLocations = data?.data.filter((item) =>
        item.address1.startsWith(inputJob)
      );
      setValue('address1', inputJob);
      // }
    }
  };

  const handleOk = () => {
    handleSubmit(onSubmit)();
    handleCancel();
  };

  const handleResetForm = () => {
    setValue('companyId', null);
    setValue('companySelect', null);
    setValue('company', null);
    setValue('contactId', null);
    setValue('contact', null);
    setValue('contactSelect', null);
    setValue('consultant', null);
    setValue('email', null);
    setValue('description', null);
    setValue('cleanDescription', null);
    setValue('joblocationcity', null);
    if (fromManualCreate) {
      reset();
      setIsSearchCompany(false);
      setValue('source', 'Zileo');
      setValue('employmentType', 'temporary');
      setValue('state', UK_NAME);
      setValue('stateId', UK_CODE);
      setValue('stateSelected', UK_CODE);
    }
  };

  const handleCloseAllDropdown = async () => {
    setHandleCloseClient(false);
    setHandleCloseContact(false);
  };

  const handleCancel = async () => {
    setShowCompany(false);
    setJobDetailCompanyId(false);
    setIsSearchCompany(false);
    setFromStep('Vacancy');
    await handleCloseAllDropdown();
    handleContactSearch('', '');
    setIsModalVisible(false);
    handleResetForm();
  };

  const onSubmit = async (data) => {
    // Send data to server API using axios or your preferred method
    try {
      await sendJobToBullhorn(data);
    } finally {
      setIsModalVisible(false);
      reset();
      handleResetForm();
    }
  };

  const OPTION = {
    PEOPLE: 'People',
    COMPANY: 'Company',
    LINKEDIN_PEOPLE: 'Linkedin People',
  };

  const [selectedSearch, setSelectedSearch] = useState(OPTION.PEOPLE);

  return (
    <div>
      <div className="grid grid-cols-10 gap-4 w-full flex items-start contact-finder-v2-root-container">
        <div
          className="w-full flex items-center justify-center col-span-2"
          style={{ marginLeft: '-20px' }}
        >
          <Segmented
            disabled={false}
            className="customized-segmented-contact-finder-v2"
            options={[OPTION.PEOPLE, OPTION.COMPANY]}
            onChange={(value) => {
              setSelectedSearch(value);
              // refreshPageState();
            }}
            value={selectedSearch}
          />
        </div>
      </div>
      {selectedSearch == OPTION.PEOPLE && (
        <List
          fromStep={fromStep}
          setFromStep={setFromStep}
          watch={watch}
          control={control}
          setValue={setValue}
          getValues={getValues}
          job={job}
          isModalVisible={isModalVisible}
          handleCloseClient={handleCloseClient}
          setHandleCloseClient={setHandleCloseClient}
          handleCloseContact={handleCloseContact}
          setHandleCloseContact={setHandleCloseContact}
          functionContactClient={{
            contactOptions,
            contactSetOptions,
            handleContactScroll,
            handleContactSearch,
            isLoadingContacts,
            setIsLoadingContacts,
            valueNotFoundContacts,
            setCompanyId,
            contactSetStart,
          }}
          functionCompany={{
            companyOptions,
            companySetOptions,
            handleCompanyScroll,
            handleCompanySearch,
            isLoadingCompany,
            setIsLoadingCompany,
            valueNotFoundCompany,
            companySetStart,
          }}
          handleOk={handleOk}
          handleCancel={handleCancel}
          // setPlaceId={setPlaceId}
          // placeId={placeId}
          // isLoadingAddressDetails={isLoadingAddressDetails}
          handleGetDefaultAddress={handleGetDefaultAddress}
          isSearchCompany={isSearchCompany}
          setIsSearchCompany={setIsSearchCompany}
          jobDetailCompanyId={jobDetailCompanyId}
          setJobDetailCompanyId={setJobDetailCompanyId}
          showCompany={showCompany}
          setShowCompany={setShowCompany}
        />
      )}

      {selectedSearch == OPTION.COMPANY && <EmailFinderCompanyTab />}
    </div>
  );
};

export default JobSubmissionModal;
