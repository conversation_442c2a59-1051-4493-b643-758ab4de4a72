import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import { getKeywords, saveKeywords } from '../../services/jobs';
import { message } from 'antd';
import { Select } from 'antd';

const Keywords = () => {
  const [visible, setVisible] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const options = [];
  options.length = 3;
  const [keywords, setKeywords] = useState([]);

  const handleChange = (value) => {
    if (value.length < 16) {
      setKeywords(value);
    } else {
      messageApi.error('Keywords limit(15) exceeds');
    }
  };

  const handleSave = () => {
    try {
      const data = JSON.parse(JSON.stringify({ keywords }));
      saveKeywords(data)
        .then(() => {
          messageApi.success('Keywords saved successfully');
          setVisible(false);
        })
        .catch(() => {
          messageApi.error('Keywords could not be saved');
        });
    } catch (err) {
      messageApi.error('Invalid JSON');
    }
  };

  useEffect(() => {
    getKeywords().then((res) => {
      setKeywords(res.data.keywords);
    });
  }, [visible]);

  return (
    <>
      <a onClick={() => setVisible(true)}>Update Keywords</a>
      {contextHolder}
      <Modal
        title="Keywords Editor"
        open={visible}
        onOk={handleSave}
        onCancel={() => setVisible(false)}
      >
        <Select
          mode="tags"
          placeholder="Please select"
          dropdownStyle={{ display: 'none' }}
          onChange={handleChange}
          style={{
            width: '100%',
          }}
          value={keywords}
        />

        {/* <TextArea rows={10} defaultValue={editedData} onChange={(e)=>setEditedData(e.target.value)}/> */}
      </Modal>
    </>
  );
};

export default Keywords;
