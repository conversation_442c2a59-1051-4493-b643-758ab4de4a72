/* eslint-disable react/prop-types */
import React from 'react';

import <PERSON><PERSON><PERSON> from '../Charts/PieChart';
import Card from '../Card';

const PieChartCard = ({ title, pieLabels, pieData }) => {
  const colors = [
    '#4318FF',
    '#6AD2FF',
    '#AED2FF',
    '#FFC436',
    '#F94C10',
    '#C8E4B2',
    '#900C3F',
    '#F266AB',
    '#B6EAFA',
  ];
  const pieChartOptions = {
    labels: pieLabels,
    colors: colors,
    chart: {
      width: '50px',
    },
    states: {
      hover: {
        filter: {
          type: 'none',
        },
      },
    },
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    hover: { mode: null },
    plotOptions: {
      donut: {
        expandOnClick: false,
        donut: {
          labels: {
            show: false,
          },
        },
      },
    },
    fill: {
      colors: colors,
    },
    tooltip: {
      enabled: true,
      theme: 'dark',
      style: {
        fontSize: '12px',
      },
    },
  };
  return (
    <Card extra="rounded-[20px] p-3 h-full">
      <div className="flex flex-row justify-between px-3 pt-2">
        <div>
          <h4 className="text-lg font-bold text-navy-700 dark:text-white">
            {title}
          </h4>
        </div>
        {/* <div className="mb-6 flex items-center justify-center">
          <select className="mb-3 mr-2 flex items-center justify-center text-sm font-bold text-gray-600 hover:cursor-pointer dark:!bg-navy-800 dark:text-white">
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="weekly">Weekly</option>
          </select>
        </div> */}
      </div>

      <div className="mb-auto flex h-[220px] w-full items-center justify-center">
        {pieData.length ? (
          <PieChart options={pieChartOptions} series={pieData} />
        ) : (
          <div className="flex justify-center items-center text-gray-500">
            No data available
          </div>
        )}
      </div>
      <div className="flex flex-row !justify-between rounded-2xl px-6 py-3 dark:!bg-navy-700 dark:shadow-none">
        {pieLabels?.map((label, index) => (
          <div
            className="flex flex-col items-center justify-center capitalize"
            key={label}
          >
            <div className="flex items-center justify-center">
              <div
                className="h-2 w-2 rounded-full"
                style={{ background: colors[index] }}
              />
              <p className="ml-1 text-sm font-normal text-gray-600">{label}</p>
            </div>
            <p className="mt-px text-xl font-bold text-navy-700  dark:text-white">
              {pieData[index]}
            </p>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default PieChartCard;
