import {
  Button,
  Collapse,
  Image,
  notification,
  Spin,
  Tag,
  Tooltip,
} from 'antd';
import { v4 as uuid } from 'uuid';
import clsx from 'clsx';
import bhlogo from '/logo_bull.webp';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  CaretRightFilled,
  CopyOutlined,
  EditOutlined,
  ScanOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import Loading from '../../containers/HotList/Loading';
import zileoLogo from '../../assets/img/welcome/logo.png';
import { useEffect, useState } from 'react';
import { querySearchCommonBulhorn } from '../../services/bullhorn';
import { getLinkedinProfile } from '../../services/task';
import InfiniteScroll from 'react-infinite-scroll-component';

const ContactInformation = ({
  participant,
  setParticipant,
  setSelectedNote,
  setNoteAction,
  selectedTaskProp,
}) => {
  const [loading, setLoading] = useState(true);
  const [linkedProfile, setLinkedProfile] = useState(null);
  const [notesData, setNotesData] = useState([]);
  const [isAllNotes, setAllNotes] = useState(false);

  const handleLinkedinProfile = async (linkedinUrl, isReturnData = false) => {
    try {
      const { data } = await getLinkedinProfile(linkedinUrl);
      if (isReturnData) return data?.result?.profile_picture_url_large;

      if (data?.result) {
        setLinkedProfile(data?.result);
      }
    } catch (error) {
      if (isReturnData) return '';
      console.log('error: ', error);
    }
  };

  const handleGetNotes = async () => {
    const participantId =
      participant?.id || selectedTaskProp?.participant?.id | null;
    if (!participantId) return;
    setLoading(true);
    const fields = `id,dateAdded,comments,action,commentingPerson,clientContacts`;
    let query = `clientContactUserID:${participantId}`;

    let start = (notesData?.length !== 0 && notesData?.length - 1) || 0;
    let limit = 5;
    try {
      // First API call
      const { data } = await querySearchCommonBulhorn(
        'Note',
        query,
        fields,
        start,
        limit
      );
      if (data?.result?.data?.length > 0) {
        setNotesData([...notesData, ...data?.result?.data]);
      } else if (
        parseInt(data?.result?.total) === notesData.length ||
        data?.result?.data?.length < limit
      ) {
        setAllNotes(true);
        // notification.info({
        //   description: 'No notes found!',
        // });
      }
      setLoading(false);
    } catch (ex) {
      setLoading(false);
      notification.error({
        description: 'Something went wrong!',
      });
    }
  };

  const handleGetLatestBHProfile = async () => {
    const participantId =
      participant?.id || selectedTaskProp?.participant?.id | null;
    if (!participantId) return;
    const fields = `id,firstName,lastName,customText1,name,occupation,clientCorporation,address,phone,email`; // get all fields
    let query = `id:${participantId}`;

    let start = 0;
    let limit = 1;
    try {
      // First API call
      const { data } = await querySearchCommonBulhorn(
        'ClientContact',
        query,
        fields,
        start,
        limit
      );
      console.log('data clientContact: ', data);
      const selectedParticipant = data?.result?.data?.[0] || null;
      const linkedinUrl = selectedParticipant?.customText1?.trim() || '';
      if (linkedinUrl) {
        await handleLinkedinProfile(linkedinUrl);
      }
    } catch (ex) {
      console.log('ex clientContact: ', ex);
      notification.error({
        description: 'Something went wrong!',
      });
    }
  };

  useEffect(() => {
    // if (!selectedTask) return;
    handleGetLatestBHProfile();
    handleGetNotes();
  }, []);
  return (
    <>
      <div
        key={uuid()}
        className="w-full flex flex-col items-center justify-center text-[#5e768d] font-medium"
      >
        <div className="text-cyan-500 font-semibold text-lg absolute -top-7">
          Contacts
        </div>
        <Image
          className={clsx(
            'py-3',
            linkedProfile?.profile_picture_url_large && 'rounded-full'
          )}
          src={linkedProfile?.profile_picture_url_large || zileoLogo}
          width={'150px'}
          preview={false}
        />
        {/* <span className="text-base font-semibold line-clamp-1 max-w-[15rem]">
          {participant?.name || '-'}
        </span> */}
        {/* <span className="font-semibold text-cyan-400 pb-2">
          {participant?.occupation || '-'}
        </span>
        <span>{participant?.clientCorporation?.name || '-'}</span>
        <span>{`${participant?.address?.countryName || '-'}`}</span>
        {participant?.phone && (
          <div className="flex items-center gap-1">
            <a href={`tel:${participant?.phone}`} className="text-cyan-500">
              {participant?.phone || '-'}
            </a>
            <CopyToClipboard
              text={participant?.phone}
              onCopy={() => {
                notification.success({
                  message: 'Success Copy Phone Number',
                });
              }}
            >
              <Button
                type="text"
                icon={<CopyOutlined />}
                className="text-[#5e768d]"
              ></Button>
            </CopyToClipboard>
          </div>
        )}
        {participant?.email && (
          <div className="flex items-center gap-1">
            <a href={`mailTo:${participant?.email}`} className="text-cyan-500">
              {participant?.email || '-'}
            </a>
            <CopyToClipboard
              text={participant?.email}
              onCopy={() => {
                notification.success({
                  message: 'Success Copy Email address',
                });
              }}
            >
              <Button
                type="text"
                icon={<CopyOutlined />}
                className="text-[#5e768d]"
              ></Button>
            </CopyToClipboard>
          </div>
        )} */}
        <div className="custom-button-load-more flex items-center">
          <Image src={bhlogo} width={35} preview={false} />
          <div class="buttons">
            <button class="btn">
              <span></span>
              <p
                className="border rounded-md"
                data-start="Click to Load more!"
                data-title={'Bullhorn Notes'}
                data-text="Bullhorn Notes"
              ></p>
            </button>
          </div>
        </div>
        <InfiniteScroll
          dataLength={notesData?.length}
          next={handleGetNotes}
          hasMore={!isAllNotes}
          loader={
            <div className="flex items-center justify-center font-medium text-xs gap-2">
              <Spin />
              <p>Loading...</p>
            </div>
          }
          endMessage={
            <div className="flex items-center justify-center font-medium text-xs gap-2">
              <ScanOutlined />
              <p>No more data to load.</p>
            </div>
          }
          height={250}
        >
          <div className="flex flex-col gap-2 pb-5 custom-collapse-activity text-left pr-2 overflow-y-auto">
            {notesData?.map((note) => (
              <Collapse
                key={uuid()}
                className="w-[24rem]"
                expandIconPosition={'end'}
                expandIcon={({ isActive }) => (
                  <CaretRightFilled rotate={isActive ? 90 : 0} />
                )}
                items={[
                  {
                    key: uuid(),
                    label: (
                      <div className=" w-full flex items-center justify-between">
                        <div className="font-semibold flex items-center gap-1">
                          <Button
                            onClick={() => {
                              setSelectedNote({ ...note });
                              setNoteAction(note?.action);
                            }}
                            type="text"
                            size="small"
                            icon={<EditOutlined />}
                          ></Button>
                          <span>{note?.action}</span>
                        </div>
                        <Tooltip
                          title={`Created by ${note?.commentingPerson?.firstName || ''} ${note?.commentingPerson?.lastName || ''}`}
                        >
                          <Tag color="#224abe">{`${note?.commentingPerson?.firstName || ''} ${note?.commentingPerson?.lastName || ''}`}</Tag>
                        </Tooltip>
                        <div className="flex justify-end items-end text-xs flex-col text-[#5e768d]">
                          <span>{`${dayjs(note?.dateAdded).format('DD/MM/YYYY')},`}</span>
                          <span>
                            {dayjs(note?.dateAdded).format('hh:mm a')}
                          </span>
                        </div>
                      </div>
                    ),
                    children: (
                      <p
                        className="max-w-[24rem]"
                        dangerouslySetInnerHTML={{
                          __html: note?.comments,
                        }}
                      ></p>
                    ),
                  },
                ]}
              />
            ))}
          </div>
        </InfiniteScroll>
      </div>
    </>
  );
};

export default ContactInformation;
