import React from 'react';
import {
  BankOutlined,
  BookFilled,
  CalendarOutlined,
  CaretRightFilled,
  CheckOutlined,
  CloseOutlined,
  ContactsOutlined,
  CopyOutlined,
  EditOutlined,
  EnvironmentOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
  RocketOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Collapse,
  Drawer,
  Image,
  notification,
  Select,
  Tag,
  Tooltip,
} from 'antd';
import CopyToClipboard from 'react-copy-to-clipboard';
import {
  insertBullhorn,
  querySearchCommonBulhorn,
  upladteBullhorn,
} from '../../services/bullhorn';
import { useEffect, useRef, useState } from 'react';
import {
  ADD_STEP_TYPE,
  ADD_STEP_TYPE_NAME,
  TASK_ACTION_ICONS,
  TASK_ACTION_STATUS,
  TASK_ACTION_STATUS_COLOR,
  TASK_ACTION_STATUS_ICON,
} from '../BullHorn/EmailtriggerStep';
import dayjs from 'dayjs';
import Loading from '../../containers/HotList/Loading';
import bhlogo from '/logo_bull.webp';
import { getLinkedinProfile } from '../../services/task';
import zileoLogo from '../../assets/img/welcome/logo.png';
import clsx from 'clsx';
import { getListEmailFromSequence } from '../../services/search';
import { v4 as uuid } from 'uuid';
import {
  getContactLists,
  getHotLists,
  getShortLists,
} from '../../containers/Sequence/SequenceDetail';
import { ACTION_NOTE } from '../JobsLeads/ViewVacancyDetail/ShortListTab/ModalEditEmail';
import ExtendTask from './ExtendTask';
import TaskForm from './TaskForm';
import ContactInformation from './ContactInformation';
import { formatPhoneNumber } from '../../utils/common';

const TaskImage = React.memo((rest) => {
  return <Image {...rest} />;
});

const TaskDetail = ({
  selectedTaskProp,
  actionLoading,
  handleUpdateStatus,
  setSelectedParentTask,
  selectedTaskListProp = [],
  handleUpdateTask,
  // Add manual task
  taskInteractLoading,
  setTaskInteractLoading,
  handleAddManualTask,
}) => {
  console.log('render TaskDetail');
  const [selectedTask, setSelectedTask] = useState({ participant: {} });
  const [selectedNote, setSelectedNote] = useState(null);
  const [participant, setParticipant] = useState(null);
  const [selectedTaskList, setSelectedTaskList] = useState([]);

  const [notesData, setNotesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [noteAction, setNoteAction] = useState(ACTION_NOTE[0].value);
  const [noteLoading, setNoteLoading] = useState(false);
  const [linkedProfile, setLinkedProfile] = useState(null);
  const [isAllNotes, setAllNotes] = useState(false);
  const [sequenceLoading, setSequenceLoading] = useState(true);
  const [inputNumberStep, setInputNumberStep] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [activeContactKeys, setActiveContactKeys] = useState([]);

  // Extend Task
  const [isOpenExtendTask, setOpenExtendTask] = useState(false);
  const showExtendTask = () => setOpenExtendTask(true);
  const closeExtendTask = () => setOpenExtendTask(false);

  // Create new manual task

  const [isModalManualTaskOpen, setIsModalManualTaskOpen] = useState(false);

  const showModalManualTask = () => {
    setIsModalManualTaskOpen(true);
  };
  const handleCancelManualTask = () => {
    setIsModalManualTaskOpen(false);
  };

  const editor = useRef(null);

  const handleGetNotes = async () => {
    const participantId =
      participant?.id || selectedTaskProp?.participant?.id | null;
    if (!participantId) return;
    setLoading(true);
    const fields = `id,dateAdded,comments,action,commentingPerson,clientContacts`;
    let query = `clientContactUserID:${participantId}`;

    let start = (notesData?.length !== 0 && notesData?.length - 1) || 0;
    let limit = 5;
    try {
      // First API call
      const { data } = await querySearchCommonBulhorn(
        'Note',
        query,
        fields,
        start,
        limit
      );
      if (data?.result?.data?.length > 0) {
        setNotesData([...data?.result?.data, ...notesData]);
      } else if (
        parseInt(data?.result?.total) === notesData.length ||
        data?.result?.data?.length < limit
      ) {
        setAllNotes(true);
        // notification.info({
        //   description: 'No notes found!',
        // });
      }
      setLoading(false);
    } catch (ex) {
      setLoading(false);
      notification.error({
        description: 'Something went wrong!',
      });
    }
  };

  const handleGetListData = async (sequenceId = '') => {
    if (!sequenceId) {
      setSequenceLoading(false);
      return;
    }
    setSequenceLoading(true);
    try {
      const { data } = await getListEmailFromSequence(sequenceId);

      const stepsTemp = data?.result?.mails;
      const inputNumberStepTemp = stepsTemp?.flatMap((step) => {
        return [
          ...step?.delays.map((delayItem) => ({
            ...delayItem,
            unit: delayItem?.unit === 'HOUR' ? 'Hours' : 'Days',
            subject: 'Delay',
            type: ADD_STEP_TYPE.ADD_WAIT,
            key: uuid(),
          })),
          { ...step, key: step?.id || uuid() },
        ];
      });
      setInputNumberStep([...inputNumberStepTemp]);
      const participantsTemp =
        data?.result?.sequence?.participants ||
        data?.result?.recipients?.[0] ||
        null;

      if (participantsTemp) {
        const {
          contactListIds,
          hotlistIds,
          recipients: sendToList,
          shortListIds = [],
        } = participantsTemp;

        const list = await Promise.all([
          await getContactLists(contactListIds),
          await getHotLists(hotlistIds),
          await getShortLists(shortListIds),
        ]);

        setParticipants([
          ...(list[1] || []),
          ...(list[1] || []),
          ...(list[2] || []),
          ...(sendToList || []),
        ]);
      }
      setSequenceLoading(false);
    } catch (error) {
      setSequenceLoading(false);
      console.log('error: ', error);
    }
  };

  const insertNoteData = async (dataContent) => {
    if (dataContent === '' || !dataContent) return;
    setNoteLoading(true);
    try {
      const payload = {
        entityName: 'Note',
        action: noteAction,
        comments: dataContent,
        // jobOrders: [{ id: watch('vacancyId') }],
        personReference: {
          firstName: participant?.firstName,
          id: participant?.contactId || participant?.id,
          lastName: participant?.lastName,
          _subtype: 'ClientContact',
        },
      };

      const dataUpdate = await insertBullhorn(payload);
      notification.success({
        message: 'Insert Note Success',
      });
      setNoteLoading(false);
      return dataUpdate;
    } catch (err) {
      setNoteLoading(false);
      notification.error({
        message: 'Insert Note failed',
      });
    }
  };

  const handleCompleteTask = async () => {
    const noteComment = editor.current?.value?.trim() || '';
    if (noteComment) {
      const updatedItem = await insertNoteData(noteComment);
      if (!updatedItem) {
        notification.error({
          message: 'Failed to add note',
        });
        return;
      }
    }
    const newSelectedTaskList = [...selectedTaskList];
    const currentTaskIndex = newSelectedTaskList.findIndex(
      (item) => item.id === selectedTask?.id
    );
    const nextTask =
      currentTaskIndex > -1 ? newSelectedTaskList[currentTaskIndex + 1] : null;
    await handleUpdateStatus(
      selectedTask?.groupId,
      selectedTask?.id,
      TASK_ACTION_STATUS.COMPLETED,
      selectedTaskProp,
      nextTask
    );
    setActiveContactKeys([currentTaskIndex + 1]);

    setSelectedNote(null);
    setLinkedProfile(null);
    newSelectedTaskList[currentTaskIndex] = {
      ...newSelectedTaskList[currentTaskIndex],
      status: TASK_ACTION_STATUS.COMPLETED,
    };
    setSelectedTaskList(newSelectedTaskList);
  };

  const handleUpdateBHNote = async () => {
    const noteComment = editor.current?.value?.trim() || '';
    if (noteComment === '' || !noteComment) {
      notification.warning({
        description: 'Note comment is required!',
      });
      return;
    }
    setNoteLoading(true);
    try {
      const payload = {
        entityName: 'Note',
        action: noteAction,
        // clientContacts: { replaceAll: [watch('contactId')] },
        comments: noteComment,
        personReference: {
          firstName: participant?.firstName,
          id: participant?.contactId || participant?.id,
          lastName: participant?.lastName,
          _subtype: 'ClientContact',
        },
        id: selectedNote?.id,
      };

      const dataUpdate = await upladteBullhorn(selectedNote?.id, payload);

      notification.success({
        description: 'Note updated successfully!',
      });

      setNoteLoading(false);
      handleGetNotes();
      setSelectedNote(null);
    } catch (error) {
      setNoteLoading(false);
      console.log('error: ', error);
      notification.error({
        description: 'Something went wrong!',
      });
    }
  };

  // useEffect(() => {
  //   if (!selectedTask) return;
  //   handleGetListData();
  // }, [selectedTask]);

  useEffect(() => {
    if (!selectedTaskProp) return;
    setSelectedTask({ ...selectedTaskProp });
    setParticipant(selectedTaskProp?.participant);
  }, [selectedTaskProp]);

  useEffect(() => {
    if (!selectedTaskListProp) {
      setSelectedTaskList([]);
      return;
    }

    setSelectedTaskList([...selectedTaskListProp]);
    const groupId = selectedTaskListProp?.[0]?.groupId || '';
    handleGetListData(groupId);
  }, [selectedTaskListProp]);

  return (
    <>
      <div className="grid grid-cols-3 gap-4 py-2 task-detail-container">
        <div className="rounded-md shadow-md py-2 px-1 flex flex-col gap-5 justify-start items-center custom-collapse-activity">
          <div
            style={{
              position: 'absolute',
              top: 0,
              marginTop: '1rem',
              zIndex: '100',
            }}
            className="text-cyan-700 font-semibold text-lg"
          >
            Sequence Summary
          </div>

          <div className="max-h-[38rem] overflow-y-auto flex flex-col gap-5 overflow-x-hidden pr-2">
            {!sequenceLoading && inputNumberStep?.length > 0 && (
              <div className="flex flex-wrap items-center justify-center gap-1 text-[#5e768d] font-medium py-4 px-2 border rounded-md w-[24rem] min-h-[5rem] max-h-[10rem] overflow-y-auto">
                <span>Participants: </span>
                <Tag color="cyan">{participants?.[0]?.name}</Tag>
                {participants?.slice(1, participants?.length - 1)?.length >
                  0 && (
                  <div className="flex gap-1 items-center">
                    <span>and</span>
                    <Tooltip
                      title={`${participants
                        ?.slice(1, participants?.length)
                        ?.map((participant) => participant?.name)
                        .join(', ')}`}
                    >
                      <span className="text-[#08979c] font-medium cursor-pointer">
                        Others
                      </span>
                    </Tooltip>
                  </div>
                )}
              </div>
            )}
            {!sequenceLoading &&
              inputNumberStep?.map((step) => (
                <Collapse
                  key={uuid()}
                  className="w-[24rem]"
                  //   onChange={onChange}
                  expandIconPosition={'end'}
                  expandIcon={({ isActive }) => (
                    <CaretRightFilled rotate={isActive ? 90 : 0} />
                  )}
                  items={[
                    {
                      key: uuid(),
                      label: (
                        <div className=" w-full grid grid-cols-3 items-center gap-2 justify-between">
                          <Tag
                            color="cyan"
                            className="font-semibold w-fit"
                            title={step?.name}
                          >
                            <span className="line-clamp-1 ">{step?.name}</span>
                          </Tag>
                          <div className="flex items-center justify-center">
                            <Tag color="cyan" className="w-fit">
                              {ADD_STEP_TYPE_NAME[step?.type]}
                            </Tag>
                          </div>
                          {step?.executedAt && (
                            <div className="flex justify-end items-center text-xs text-[#5e768d] w-full gap-1">
                              <Tag
                                color="success"
                                className="font-semibold w-fit text-xs"
                              >
                                Sent
                              </Tag>
                              <div className="flex items-center flex-col text-xs text-[#5e768d]">
                                <span>{`${dayjs(step?.executedAt).format('DD/MM/YYYY')},`}</span>
                                <span>
                                  {dayjs(step?.executedAt).format('hh:mm a')}
                                </span>
                              </div>
                            </div>
                          )}
                          {!step?.executedAt && (
                            <div className="flex justify-end items-end text-xs flex-col text-[#5e768d] w-full">
                              <Tag className="font-semibold w-fit">
                                Not sent
                              </Tag>
                            </div>
                          )}
                        </div>
                      ),
                      children: (
                        <div>
                          {step?.recipients?.length > 0 && (
                            <div className="flex flex-wrap items-center justify-center gap-1 text-[#5e768d] font-medium py-4 px-2 border rounded-md w-[22rem] mb-2 max-h-[5rem] overflow-y-hidden">
                              <span>Participants: </span>
                              <Tag color="cyan">
                                {step?.recipients?.[0]?.name}
                              </Tag>
                              {step?.recipients?.slice(
                                1,
                                step?.recipients?.length - 1
                              )?.length > 0 && (
                                <div className="flex gap-1 items-center">
                                  <span>and</span>
                                  <Tooltip
                                    title={`${step?.recipients
                                      ?.slice(1, step?.recipients?.length)
                                      ?.map((participant) => participant?.name)
                                      .join(', ')}`}
                                  >
                                    <span className="text-[#08979c] font-medium cursor-pointer">
                                      Others
                                    </span>
                                  </Tooltip>
                                </div>
                              )}
                            </div>
                          )}
                          <div className="flex justify-between pb-3 border-b border-[#5e768d]">
                            {step?.type === ADD_STEP_TYPE.SEND_MAIL ? (
                              <div className="flex items-center gap-2 text-[#5e768d] font-medium">
                                <span>Subject: </span>
                                <span
                                  title={step?.subject}
                                  className="line-clamp-1"
                                >
                                  {step?.subject}
                                </span>
                              </div>
                            ) : (
                              <Tag color="cyan">{step?.subject}</Tag>
                            )}
                            {step?.type === ADD_STEP_TYPE.ADD_WAIT && (
                              <div className="flex items-center gap-2">
                                <span className="text-[#5e768d] font-semibold">
                                  {step?.delay}
                                </span>
                                <span className="text-[#5e768d] font-medium">
                                  {step?.unit}
                                </span>
                              </div>
                            )}
                          </div>
                          {step?.type ===
                            ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
                            JSON.parse(step?.content)?.[0]?.message?.trim() && (
                              <p
                                className="max-w-[24rem] p-3 mt-2 border rounded-md"
                                dangerouslySetInnerHTML={{
                                  __html: JSON.parse(step?.content)?.[0]
                                    ?.message,
                                }}
                              ></p>
                            )}
                          {step?.type !==
                            ADD_STEP_TYPE.LINKEDIN_CONNECTION_REQUEST &&
                            step?.content?.trim() && (
                              <p
                                className="max-w-[24rem] p-3 mt-2 border rounded-md"
                                dangerouslySetInnerHTML={{
                                  __html: step?.content,
                                }}
                              ></p>
                            )}
                        </div>
                      ),
                    },
                  ]}
                />
              ))}
            {!sequenceLoading && inputNumberStep?.length === 0 && (
              <div className="text-gray-500 font-medium">
                No Sequence found!
              </div>
            )}
            {sequenceLoading && <Loading />}
          </div>
        </div>
        <div
          style={{
            position: 'absolute',
            top: 0,
            marginTop: '1rem',
            zIndex: '100',
            left: 680,
          }}
          className="text-cyan-700 font-semibold text-lg"
        >
          Contact
        </div>
        <div className="rounded-md shadow-md py-2 px-1 flex flex-col gap-1 text-center relative max-h-[42rem] overflow-y-auto">
          {selectedTaskList?.map((task, index) => (
            <>
              <Collapse
                onChange={(keys) => {
                  const index = keys?.[0] || null;
                  setActiveContactKeys(keys);
                  if (index === null) return;
                  const task = selectedTaskList[index];
                  setSelectedNote(null);
                  setSelectedParentTask(task);
                  setLinkedProfile(null);
                }}
                rootClassName={clsx(
                  'customized-collapse',
                  task?.participant?.id === participant?.id &&
                    'active-customized-collapse'
                )}
                expandIconPosition="end"
                // expandIcon={({ isActive }) => <></>}
                className="w-full"
                accordion
                // defaultActiveKey={['0']}
                activeKey={activeContactKeys}
                items={[
                  {
                    key: index,
                    disabled: sequenceLoading,
                    label: (
                      <div
                        className={clsx(
                          'flex items-center justify-start gap-3 font-medium p-1 w-full text-sm',
                          task?.participant?.id === participant?.id &&
                            'text-cyan-700',
                          sequenceLoading
                            ? 'cursor-not-allowed'
                            : 'cursor-pointer',
                          task?.status === TASK_ACTION_STATUS.COMPLETED &&
                            '!text-green-600',
                          task?.status === TASK_ACTION_STATUS.DECLINED &&
                            '!text-red-600'
                        )}
                      >
                        <Checkbox
                          disabled={loading || sequenceLoading}
                          checked={task?.participant?.id === participant?.id}
                        />
                        <div
                          // style={{ textWrap: 'nowrap' }}
                          // className={clsx('overflow-x-hidden')}
                          // title={task?.participant?.name}
                          className="flex flex-col items-start gap-1 w-full"
                        >
                          <div className="flex flex-col items-start justify-center w-full">
                            <div
                              className={clsx(
                                'line-clamp-1 max-w-xs text-sm font-semibold flex items-center gap-1'
                              )}
                              title={task?.participant?.name}
                            >
                              <UserOutlined /> {task?.participant?.name}
                            </div>
                          </div>
                          <span className="text-xs font-semibold flex items-center gap-1">
                            <ContactsOutlined />
                            {task?.participant?.occupation || '-'}
                          </span>
                          <span className="font-semibold flex items-center gap-1 text-xs">
                            <BankOutlined />
                            {task?.participant?.clientCorporation?.name || '-'}
                          </span>

                          <div className="flex items-center gap-1 text-xs font-semibold ">
                            <EnvironmentOutlined />{' '}
                            {`${task?.participant?.address?.countryName || '-'}`}
                          </div>
                          {task?.participant?.phone && (
                            <a
                              onClick={(e) => e.stopPropagation()}
                              href={`tel:${task?.participant?.phone}`}
                              className="text-cyan-700 flex items-center gap-1 text-xs font-semibold "
                            >
                              <PhoneOutlined />
                              <span>
                                {formatPhoneNumber(task?.participant?.phone)}
                              </span>
                            </a>
                          )}
                          {task?.participant?.email && (
                            <a
                              onClick={(e) => e.stopPropagation()}
                              href={`mailTo:${task?.participant?.email}`}
                              className="text-cyan-700 flex items-center gap-1 text-xs font-semibold "
                            >
                              <MailOutlined />
                              <span>{task?.participant?.email}</span>
                            </a>
                          )}
                        </div>
                      </div>
                    ),
                    children: (
                      <ContactInformation
                        participant={task?.participant}
                        setParticipant={setParticipant}
                        setSelectedNote={setSelectedNote}
                        setNoteAction={setNoteAction}
                        selectedTaskProp={selectedTaskProp}
                      />
                    ),
                  },
                ]}
              />
            </>
          ))}
        </div>
        <div className="rounded-md shadow-md py-2 px-3 flex flex-col gap-5">
          <div className="w-full flex justify-between">
            {selectedTask?.type && (
              <Tag color="cyan">
                <div className="flex items-center gap-2 px-1 py-2">
                  {TASK_ACTION_ICONS[selectedTask?.type]}
                  <span className="font-semibold">{selectedTask?.type}</span>
                </div>
              </Tag>
            )}
            {selectedTask?.status && (
              <Tag
                color={
                  TASK_ACTION_STATUS_COLOR[selectedTask?.status?.toUpperCase()]
                }
              >
                <div className="flex items-center gap-2 px-1 py-2">
                  {TASK_ACTION_STATUS_ICON[selectedTask?.status?.toUpperCase()]}
                  <span className="font-semibold">{selectedTask?.status}</span>
                </div>
              </Tag>
            )}
          </div>
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-center gap-2 text-[#5e768d]">
              <BookFilled className="font-semibold text-[#5e768d]" />
              <span className="font-semibold ">Task Notes</span>
            </div>
            {selectedTask?.content && (
              <div
                className="rounded-md border border-2 flex flex-col items-center justify-center py-3 font-semibold text-[#5e768d] min-h-[10rem]"
                dangerouslySetInnerHTML={{
                  __html: selectedTask?.content,
                }}
              ></div>
            )}
            {!selectedTask?.content && (
              <div className="rounded-md border border-2 flex flex-col items-center justify-center py-3 font-medium italic text-[#5e768d] min-h-[10rem]">
                No notes found!
              </div>
            )}
          </div>
          <div className="grid grid-cols-12 gap-1">
            <div className="w-full col-span-12">
              <div className="flex items-start justify-between gap-2 text-[#5e768d]">
                <div className="w-full flex items-center justify-between gap-2 text-[#5e768d]">
                  <div className="flex items-center justify-center gap-2">
                    <TaskImage src={bhlogo} width={20} preview={false} />
                    <span className="font-semibold ">BULLHORN NOTE</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {/* <span>
                      Action <span style={{ color: 'red' }}>*</span>
                    </span> */}
                    <Select
                      disabled={noteLoading}
                      options={ACTION_NOTE}
                      onSelect={(value) => setNoteAction(value)}
                      value={noteAction}
                    />
                  </div>
                </div>
              </div>
              <div className="rounded-md flex flex-col items-center justify-center py-3 font-normal text-[#5e768d] min-h-[10rem] gap-3">
                <div className="w-full flex justify-between items-center gap-5">
                  {selectedTaskProp !== 'active-key' && (
                    <div className="flex items-center gap-1 p-2 rounded-md border font-medium text-cyan-700 border-cyan-700 w-full">
                      <Checkbox checked />
                      {participant?.name}
                    </div>
                  )}
                  {participant && (
                    <Button
                      loading={noteLoading}
                      className="h-full py-2"
                      onClick={handleUpdateBHNote}
                      type="primary"
                    >
                      Save
                    </Button>
                  )}
                </div>
                <div className="w-full">
                  <textarea
                    disabled={noteLoading || !participant}
                    placeholder="Add note here..."
                    className="w-full border rounded-md p-2"
                    contentEditable={true}
                    suppressContentEditableWarning={true}
                    ref={editor}
                    rows={8}
                    defaultValue={selectedNote?.comments || ''}
                    // onChange={(e) => setNoteComments(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full flex justify-end border-t pt-4 mt-4">
        <div className="w-full flex gap-2 items-center">
          {!isOpenExtendTask && (
            <>
              <Tag
                color="error"
                icon={<CalendarOutlined />}
                className="flex justify-center items-center gap-1"
              >
                {dayjs(selectedTaskListProp?.[0]?.dueDate).format('DD/MM/YYYY')}
              </Tag>
              <Button
                type="primary"
                icon={<RocketOutlined className="hover:animate-bounce" />}
                onClick={showExtendTask}
              >
                Extend
              </Button>
              <span className="text-gray-700 font-semibold px-1">OR</span>
              <Button
                type="primary"
                icon={<PlusOutlined className="hover:animate-bounce" />}
                onClick={showModalManualTask}
              >
                Add New
              </Button>
            </>
          )}
          {isOpenExtendTask && (
            <ExtendTask
              defaultDueDate={selectedTaskListProp?.[0]?.dueDate}
              groupId={selectedTaskListProp?.[0]?.groupId || ''}
              selectedTask={selectedTask}
              setSelectedTask={setSelectedTask}
              handleExtendDate={handleUpdateTask}
              closeExtendTask={closeExtendTask}
            />
          )}
        </div>
        {selectedTask?.status === TASK_ACTION_STATUS.PENDING && (
          // true && (
          <div className="w-full flex justify-end gap-4">
            <Button
              disabled={actionLoading}
              onClick={handleCompleteTask}
              icon={<CheckOutlined />}
              // type="primary"
              loading={noteLoading}
              className="text-green-600 font-medium border-green-600"
            >
              Complete
            </Button>
            <Button
              disabled={actionLoading}
              onClick={() => {
                const newSelectedTaskList = [...selectedTaskList];
                const currentTaskIndex = newSelectedTaskList.findIndex(
                  (item) => item.id === selectedTask?.id
                );
                const nextTask =
                  currentTaskIndex > -1
                    ? newSelectedTaskList[currentTaskIndex + 1]
                    : null;

                handleUpdateStatus(
                  selectedTask?.groupId,
                  selectedTask?.id,
                  TASK_ACTION_STATUS.DECLINED,
                  selectedTaskProp,
                  nextTask
                );
                setSelectedNote(null);
                setLinkedProfile(null);
                newSelectedTaskList[currentTaskIndex] = {
                  ...newSelectedTaskList[currentTaskIndex],
                  status: TASK_ACTION_STATUS.DECLINED,
                };
                setSelectedTaskList(newSelectedTaskList);
              }}
              icon={<CloseOutlined className="text-red-600 font-medium" />}
              danger
            >
              Decline
            </Button>
          </div>
        )}
        {/* Add manual task */}
        <Drawer
          title={
            <div className="flex items-center w-full justify-center">
              <span className="font-semibold">Create</span>
              <Tag color="cyan" className="ml-1 text-base">
                Manual Task{' '}
              </Tag>
            </div>
          }
          placement="left"
          onClose={handleCancelManualTask}
          open={isModalManualTaskOpen}
          width="35%"
          getContainer={false}
        >
          {/* <div className="py-4 px-2 max-h-[88vh] overflow-y-auto"> */}
          <TaskForm
            loading={taskInteractLoading}
            setLoading={setTaskInteractLoading}
            handleCancelManualTask={handleCancelManualTask}
            handleAddManualTask={async (payload, participants) => {
              await handleAddManualTask(payload, participants).finally(() =>
                handleCancelManualTask()
              );
            }}
          />
          {/* </div> */}
        </Drawer>
      </div>
    </>
  );
};

export default TaskDetail;
