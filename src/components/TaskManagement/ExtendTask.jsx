import {
  CheckOutlined,
  CloseOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { Button, DatePicker, Tooltip } from 'antd';
import dayjs from 'dayjs';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { TASK_CREATING_TYPE } from '../BullHorn/EmailtriggerStep';

const ExtendTask = ({
  selectedTask,
  handleExtendDate,
  closeExtendTask,
  setSelectedTask,
  groupId,
  defaultDueDate,
}) => {
  const [dueDate, setDueDate] = useState(dayjs());
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    setLoading(true);
    const payload = {
      dueDate: dayjs(dueDate).format('YYYY-MM-DDTHH:mm:ss'),
      creatingType: TASK_CREATING_TYPE.EXTENDED,
    };
    handleExtendDate(groupId, payload).finally(() => {
      setLoading(false);
      closeExtendTask();
      setSelectedTask({
        ...selectedTask,
        dueDate: dayjs(dueDate).format('YYYY-MM-DDTHH:mm:ss'),
        creatingType: TASK_CREATING_TYPE.EXTENDED,
      });
    });
  };

  useEffect(() => {
    setDueDate(dayjs(defaultDueDate) || dayjs());
  }, [defaultDueDate]);
  return (
    <div className="flex gap-1">
      <Tooltip title="Due Date">
        <DatePicker
          disabledDate={(current) => {
            return (
              moment().add(-1, 'days') >= current ||
              moment().add(1, 'month') <= current
            );
          }}
          onChange={(date) => {
            setDueDate(date);
          }}
          value={dueDate}
          format={'DD/MM/YYYY'}
          placeholder="Due Date"
          className="w-full "
        />
      </Tooltip>
      <Button
        disabled={!dueDate}
        type="primary"
        loading={loading}
        onClick={handleSubmit}
      >
        Save
      </Button>
      <Button
        disabled={loading}
        onClick={closeExtendTask}
        // icon={<CloseOutlined className="font-semibold text-red-600" />}
      >
        Cancel
      </Button>
    </div>
  );
};

export default ExtendTask;
