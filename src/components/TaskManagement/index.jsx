import Search from 'antd/es/input/Search';
import { useEffect, useRef, useState } from 'react';
import {
  TASK_ACTION,
  TASK_ACTION_ICONS,
  TASK_ACTION_STATUS,
  TASK_ACTION_STATUS_ICON,
  TASK_CREATING_TYPE,
  TASK_CREATING_TYPE_COLOR,
  TASK_CREATING_TYPE_ICON,
} from '../BullHorn/EmailtriggerStep';
import clsx from 'clsx';
import {
  CalendarOutlined,
  ContactsOutlined,
  EnvironmentOutlined,
  FlagOutlined,
  InfoCircleOutlined,
  LeftCircleTwoTone,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Divider,
  Input,
  Modal,
  notification,
  Pagination,
  Popover,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import {
  addManualTask,
  getAllTasks,
  // getOtherCreatingTypeTasks,
  getTotalTypes,
  updateTask,
  updateTasksByGroupId,
} from '../../services/task';
import { addDays } from 'date-fns';
import dayjs from 'dayjs';
import { isArray } from 'lodash';
import TaskDetail from './TaskDetail';
import { use } from 'react';
import TaskForm from './TaskForm';
import FinishingScreen from './FinishingScreen';
import { v4 as uuid } from 'uuid';

const initialFilter = {
  type: 'ALL',
  search: '',
  sequence: '',
  status: TASK_ACTION_STATUS.PENDING,
  due: 7,
};

const DUE_DATE_VALUES = [
  {
    value: -1,
    label: 'All',
  },
  {
    value: 1,
    label: '1 days',
  },
  {
    value: 3,
    label: '3 days',
  },
  {
    value: 7,
    label: '7 days',
  },
  {
    value: 10,
    label: '10 days',
  },
  {
    value: 30,
    label: '30 days',
  },
];

const TaskManagement = () => {
  const [taskInteractLoading, setTaskInteractLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [selectedTaskList, setSelectedTaskList] = useState([]);
  const [filter, setFilter] = useState(initialFilter);
  const [dataSource, setDataSource] = useState([]);
  const [totalTypes, setTotalTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    totalCount: 0,
    pageSize: 10,
    currentPage: 1,
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [items, setItems] = useState(DUE_DATE_VALUES);
  const [name, setName] = useState('');
  const inputRef = useRef(null);

  // Task detail modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  // Create new manual task

  const [isModalManualTaskOpen, setIsModalManualTaskOpen] = useState(false);
  const showModalManualTask = () => {
    setIsModalManualTaskOpen(true);
  };
  const handleOkManualTask = () => {
    setIsModalManualTaskOpen(false);
  };
  const handleCancelManualTask = () => {
    setIsModalManualTaskOpen(false);
  };

  const onNameChange = (event) => {
    setName(event.target.value);
  };
  const addItem = (e) => {
    if (!name.trim()) return;
    e.preventDefault();
    setItems([...items, { value: parseInt(name), label: `${name} days` }]);
    setName('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const isYesterday = (triggerAt) => {
    const formattedDate = dayjs(triggerAt).format('DD/MM/YYYY');
    const yesterday = dayjs().subtract(1, 'day').format('DD/MM/YYYY');
    return formattedDate === yesterday;
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (seqName, record) =>
        seqName && (
          <div className="flex items-center gap-1">
            <span className="font-medium">{seqName}</span>
          </div>
        ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type, record) => {
        return (
          type && (
            <>
              {isArray(type) && type?.length > 0 ? (
                <Avatar.Group
                  max={{
                    count: 5,
                    style: {
                      color: '#f56a00',
                      backgroundColor: '#fde3cf',
                    },
                  }}
                >
                  {type?.map((text) => (
                    <Avatar
                      className="bg-cyan-400"
                      icon={TASK_ACTION_ICONS[text]}
                    />
                  ))}
                </Avatar.Group>
              ) : (
                <Tag color="cyan" icon={TASK_ACTION_ICONS[type]}>
                  {type}
                </Tag>
              )}
            </>
          )
        );
      },
    },
    {
      title: 'Notes',
      dataIndex: 'content',
      key: 'content',
      width: '15%',
      render: (content, record) => (
        <>
          {content?.total ? (
            <Tag>{content?.total}</Tag>
          ) : (
            <span
              className="line-clamp-1 max-w-[15rem]"
              title={content}
              dangerouslySetInnerHTML={{
                __html: content,
              }}
            ></span>
          )}
        </>
      ),
    },
    {
      title: 'Contacts',
      dataIndex: 'participant',
      key: 'participant',
      width: '10%',
      render: (participant, record) => {
        return (
          <Popover
            placement="top"
            title={false}
            content={
              <div>
                {record?.contacts?.map((option) => (
                  <div className="grid">
                    <span className="text-base">{option.name}</span>
                    <div className="contact-details">
                      <div className="flex">
                        <span className="text-gray-500 text-xs min-w-[230px]">
                          <MailOutlined />
                          {option.email ? option.email : '-'}
                        </span>
                        <span className="text-gray-500 text-xs min-w-[230px]">
                          <PhoneOutlined />
                          {option.phone ? option.phone : '-'}
                        </span>
                        <span className="text-gray-500 text-xs">
                          <ContactsOutlined />{' '}
                          {option.occupation ? option.occupation : '-'}
                        </span>
                      </div>
                      <div className="flex">
                        <span className="text-gray-500 text-xs min-w-[230px]">
                          <EnvironmentOutlined />
                          {option.address &&
                          option.address.city &&
                          option.address.state
                            ? `${option.address.city}, ${option.address.state}`
                            : option.address && option.address.city
                              ? option.address.city
                              : option.address && option.address.state
                                ? option.address.state
                                : '-'}
                        </span>
                        <span className="text-gray-500 text-xs min-w-[230px]">
                          <InfoCircleOutlined />{' '}
                          {option.status ? option.status : '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            }
          >
            <Tag color="cyan">{participant}</Tag>
          </Popover>
        );
      },
    },
    {
      title: 'Task Type',
      dataIndex: 'creatingType',
      key: 'creatingType',
      width: '10%',
      render: (creatingType, record) => (
        <Tag
          color={
            TASK_CREATING_TYPE_COLOR[creatingType] ||
            TASK_CREATING_TYPE_COLOR.Sequence
          }
          icon={
            TASK_CREATING_TYPE_ICON[creatingType] ||
            TASK_CREATING_TYPE_ICON.Sequence
          }
        >
          {creatingType || 'Sequence'}
        </Tag>
      ),
    },
    {
      title: 'Due',
      dataIndex: 'triggerAt',
      key: 'triggerAt',
      width: '10%',
      render: (triggerAt, record) => {
        const isAfter = dayjs().isAfter(dayjs(triggerAt));
        return (
          triggerAt && (
            <Tag color="error" icon={<CalendarOutlined />}>
              {isAfter ? 'Overdue' : dayjs(triggerAt).format('DD/MM/YYYY')}
            </Tag>
          )
        );
      },
    },
  ];

  const getData = async () => {
    setLoading(true);
    try {
      const query = {
        page: pagination.currentPage,
        limit: pagination.pageSize,
        ...(filter.status && { status: filter.status }),
        ...(filter.type && filter.type !== 'ALL' && { type: filter.type }),
        ...(filter.search?.trim() && { sequenceName: filter.search }),
        ...(filter.due &&
          filter.due !== -1 && {
            dueDate: addDays(new Date(), filter.due),
          }),
      };
      const { data } = await getAllTasks(query);
      if (data?.result?.pagination) {
        setPagination({
          ...pagination,
          totalCount: data?.result?.pagination?.total,
        });
      }

      let newDataSource = [];

      if (data?.result?.items?.length > 0) {
        const seqDataSource = [...data?.result?.items].map((seq) => ({
          id: seq?.groupId,
          name: seq?.tasks[0]?.name || 'Sequence Name here',
          triggerAt: seq?.tasks?.[0]?.dueDate || seq?.triggerAt,
          isTask: false,
          contacts: seq?.tasks?.map((task) => task.participant),
          participant: `${seq?.tasks?.length || 0} contact(s)`,
          total: `${seq?.tasks?.length || 0} task(s)`,
          type: seq?.tasks?.map((task) => task?.type),
          content: { total: `${seq?.tasks?.length || 0} note(s)` },
          creatingType:
            seq?.tasks?.[0]?.creatingType || TASK_CREATING_TYPE.SEQUENCE,
          sequenceTasks: seq?.tasks?.map((task) => ({
            ...task,
            sequenceId: seq?.groupId,
            name: seq?.tasks[0]?.name || 'Sequence Name here',
            isTask: true,
          })),
        }));
        newDataSource = newDataSource.concat([...seqDataSource]);
      }

      // console.log('seqData: ', seqData);
      setDataSource(newDataSource);

      setLoading(false);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Something went wrong!!',
      });
      setLoading(false);
    }
  };

  const getTotalTaskTypes = async (status) => {
    try {
      const { data } = await getTotalTypes(status);
      if (data?.result?.length > 0) {
        const newTotalData = [...data?.result];
        const total = newTotalData.reduce(
          (acc, item) => acc + parseInt(item?.count),
          0
        );

        newTotalData.push({ type: 'ALL', count: total });
        setTotalTypes([...newTotalData]);
      } else {
        setTotalTypes([]);
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const handleUpdateStatus = async (
    groupId,
    taskId,
    status,
    record,
    nextTask = null
  ) => {
    setActionLoading(true);
    try {
      const payload = {
        status,
      };
      const { data } = await updateTask(taskId, payload);
      const newDataSource = [...dataSource];
      const updatedSequenceIndex = newDataSource.findIndex(
        (item) => item?.id === groupId
      );
      const newChildren = [
        ...newDataSource[updatedSequenceIndex]?.sequenceTasks,
      ];
      const updatedTaskIndex = newChildren?.findIndex(
        (task) => task?.id === taskId
      );
      newChildren[updatedTaskIndex] = {
        ...newChildren[updatedTaskIndex],
        status,
      };

      newDataSource[updatedSequenceIndex].sequenceTasks = newChildren;

      setDataSource([...newDataSource]);

      notification.success({
        description: 'Task updated!',
      });

      if (filter.status && filter.status !== status) {
        const dataUpdate = totalTypes.map((obj) => {
          if (obj.type === record.type || obj.type === 'ALL') {
            return { ...obj, count: (+obj.count - 1).toString() };
          }
          return obj;
        });

        setTotalTypes(dataUpdate);

        const newDataSource = [...dataSource];
        const updatedSequenceIndex = newDataSource.findIndex(
          (item) => item?.id === groupId
        );

        if (updatedSequenceIndex !== -1) {
          let newChildren = [
            ...newDataSource[updatedSequenceIndex]?.sequenceTasks,
          ];
          const updatedTaskIndex = newChildren?.findIndex(
            (task) => task?.id === taskId
          );

          if (updatedTaskIndex !== -1) {
            newChildren.splice(updatedTaskIndex, 1);
          }

          if (newChildren.length === 0) {
            newDataSource.splice(updatedSequenceIndex, 1);
          } else {
            newDataSource[updatedSequenceIndex].sequenceTasks = newChildren;
          }

          setDataSource([...newDataSource]);
        }
      }
      setActionLoading(false);
      if (nextTask) {
        setSelectedTask({ ...nextTask });
      } else {
        setSelectedTask(null);
      }
      // handleCancel();
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Updating failed!!',
      });
      setActionLoading(false);
    }
  };

  const handleUpdateTask = async (groupId, payload) => {
    setActionLoading(true);
    try {
      const { data } = await updateTasksByGroupId(groupId, payload);
      console.log('selectedTaskList: ', selectedTaskList);
      console.log('datasource: ', dataSource);
      const newDataSource = [...dataSource];
      const updatedSequenceIndex = newDataSource.findIndex(
        (item) => item?.sequenceTasks?.[0]?.groupId === groupId
      );
      const newChildren = [
        ...newDataSource[updatedSequenceIndex]?.sequenceTasks,
      ]?.map((item) => ({ ...item, dueDate: payload?.dueDate }));
      
      getData();
      setSelectedTaskList([...newChildren]);

      console.log('newChildren: ', newChildren);

      setActionLoading(false);
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Updating failed!!',
      });
      setActionLoading(false);
    }
  };

  useEffect(() => {
    if (filter.status === 'ALL' && !filter.type && pagination.totalCount === 0)
      return; // initial
    getData();
  }, [
    filter.status,
    filter.type,
    filter.due,
    filter.search,
    pagination.currentPage,
  ]);

  useEffect(() => {
    getTotalTaskTypes(filter.status);
  }, [filter.status]);

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const onPageChange = (page, pageSize) => {
    setPagination({ ...pagination, currentPage: page, pageSize });
  };

  const handleAddManualTask = async (payload, participants) => {
    setTaskInteractLoading(true);
    try {
      const { data } = await addManualTask({
        ...payload,
        participants,
        groupdId: uuid(),
      });
      console.log('result: ', data);
      getData();
      // if (result?.length > 0) {
      //   const addingData = result?.map(({ data }) => ({
      //     id: data?.result?.raw?.[0]?.id || uuid(),
      //     name: payload?.name || '',
      //     triggerAt: payload?.dueDate,
      //     isTask: false,
      //     participant: `1 contact(s)`,
      //     total: `1 task(s)`,
      //     type: [payload?.type],
      //     content: { total: `1 note(s)` },
      //     sequenceTasks: [
      //       {
      //         ...payload,
      //         id: data?.result?.raw?.[0]?.id || uuid(),
      //       },
      //     ],
      //     creatingType: payload?.creatingType,
      //   }));
      //   const newDataSource = [...addingData, ...dataSource];
      //   setDataSource([...newDataSource]);
      // }

      notification.success({
        description: 'Task created!',
      });
      setTaskInteractLoading(false);
      handleOkManualTask();
    } catch (error) {
      console.log('error: ', error);
      notification.error({
        description: 'Creating task failed!',
      });
      setTaskInteractLoading(false);
    }
  };

  return (
    <div>
      <div className="pb-2 flex justify-between">
        <Search
          disabled={isModalManualTaskOpen}
          loading={loading}
          placeholder="Search by name"
          className="w-2/3"
          allowClear
          enterButton="Search"
          size="large"
          onSearch={(value) => setFilter({ ...filter, search: value })}
        />
        <Button
          type="primary"
          className="flex items-center "
          disabled={isModalManualTaskOpen}
          onClick={showModalManualTask}
          icon={<PlusOutlined />}
        >
          Add New Task
        </Button>
      </div>

      <div className="grid grid-cols-10 gap-5">
        <div className="col-span-2 flex flex-col gap-1">
          <div className="py-1 flex items-center gap-2">
            <span className="!text-[#5e768d] font-medium col-span-2">View</span>
            <div className="w-full col-span-4">
              <Select
                rootClassName="hehe"
                defaultValue={filter.due}
                placeholder="Due date"
                className="max-w-xs shadow-sm"
                onChange={(value) => setFilter({ ...filter, due: value })}
                options={items}
              />
            </div>
          </div>

          <div>
            <span className="!text-[#5e768d] font-medium">STATUS</span>
          </div>
          {Object.entries(TASK_ACTION_STATUS).map(([key, value]) => (
            <div
              className={clsx(
                'w-full bg-white border rounded-md shadow-sm px-5 py-3 gap-2 flex items-center font-medium !text-[#5e768d] cursor-pointer',
                filter.status === value &&
                  'border-cyan-600 !text-cyan-600 !font-semibold'
              )}
              onClick={() => {
                if (filter.status === value) {
                  setFilter({ ...filter, status: '' });
                } else {
                  setFilter({ ...filter, status: value });
                }
              }}
            >
              {TASK_ACTION_STATUS_ICON[key]}
              <span>{value}</span>
            </div>
          ))}
          <div className="py-1">
            <span className="!text-[#5e768d] font-medium">TYPES</span>
          </div>
          <div
            className={clsx(
              'w-full bg-white border rounded-md shadow-sm px-5 py-3 gap-2 flex items-center font-medium !text-[#5e768d] cursor-pointer',
              filter.type === 'ALL' &&
                'border-cyan-600 !text-cyan-600 !font-semibold'
            )}
            onClick={() => setFilter({ ...filter, type: 'ALL' })}
          >
            <UnorderedListOutlined className="text-base font-medium" />
            <span>All</span>
            {totalTypes?.length > 0 &&
              totalTypes?.find((item) => item?.type === 'ALL')?.count && (
                <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                  {totalTypes?.find((item) => item?.type === 'ALL')?.count}
                </span>
              )}
          </div>
          {TASK_ACTION.map(({ value }) => (
            <div
              className={clsx(
                'w-full bg-white border rounded-md shadow-sm px-5 py-3 gap-2 flex items-center font-medium !text-[#5e768d] cursor-pointer',
                filter.type === value &&
                  'border-cyan-600 !text-cyan-600 !font-semibold'
              )}
              onClick={() => setFilter({ ...filter, type: value })}
            >
              {TASK_ACTION_ICONS[value]}
              <span>{value}</span>
              {totalTypes?.length > 0 &&
                totalTypes?.find((item) => item?.type === value)?.count && (
                  <span className="bg-[#f6c23d] text-white px-2 rounded-md font-semibold">
                    {totalTypes?.find((item) => item?.type === value)?.count}
                  </span>
                )}
            </div>
          ))}
        </div>
        <div className="col-span-8">
          {!isModalManualTaskOpen && (
            <div className="task-table-new-design-container">
              <Table
                className="mb-5"
                dataSource={dataSource}
                columns={columns}
                loading={loading}
                rowKey={(record) => record?.id}
                //   rowSelection={rowSelection}
                onRow={(record, rowIndex) => {
                  return {
                    onClick: () => {
                      setSelectedTask('active-key');
                      setSelectedTaskList([...record?.sequenceTasks]);
                      showModal();
                    },
                    style: { cursor: 'pointer' },
                  };
                }}
                pagination={false}
              />
              <Pagination
                disabled={loading}
                current={pagination.currentPage}
                onChange={onPageChange}
                total={pagination.totalCount}
                pageSize={Number(pagination.pageSize)}
                pageSizeOptions={[10]}
              />
            </div>
          )}
          {isModalManualTaskOpen && (
            <div className="pt-6 pb-4 px-4 max-h-[88vh] min-h-[44rem] overflow-y-auto bg-white rounded-md shadow-md">
              <div className="w-full flex items-center justify-center pb-5">
                {/* <Button
                  onClick={handleCancelManualTask}
                  icon={<LeftCircleTwoTone />}
                  type='primary'
                >
                  Back
                </Button> */}
                <div className="flex items-center w-full justify-center text-base">
                  <span className="font-semibold">Create</span>
                  <Tag color="cyan" className="ml-1 text-base font-semibold">
                    Manual Task{' '}
                  </Tag>
                </div>
              </div>
              <TaskForm
                loading={taskInteractLoading}
                setLoading={setTaskInteractLoading}
                handleCancelManualTask={handleCancelManualTask}
                handleAddManualTask={handleAddManualTask}
              />
            </div>
          )}
        </div>
      </div>

      {/* Selected task modal */}
      <Modal
        open={isModalOpen}
        onOk={handleOk}
        closable={!!selectedTask}
        onCancel={handleCancel}
        footer={null}
        destroyOnClose
        className={clsx('top-5', selectedTask && 'min-w-[90rem]')}
        rootClassName={clsx(!selectedTask && 'customized-modal-bg')}
      >
        {selectedTask && (
          <div className="pt-6 pb-4 px-2 max-h-[88vh] min-h-[44rem] overflow-y-auto">
            <TaskDetail
              actionLoading={actionLoading}
              handleUpdateStatus={handleUpdateStatus}
              selectedTaskProp={selectedTask}
              setSelectedParentTask={setSelectedTask}
              selectedTaskListProp={selectedTaskList}
              handleUpdateTask={handleUpdateTask}
              // Add manual task
              taskInteractLoading={taskInteractLoading}
              setTaskInteractLoading={setTaskInteractLoading}
              handleAddManualTask={handleAddManualTask}
            />
          </div>
        )}
        {!selectedTask && <FinishingScreen handleSubmitClose={handleCancel} />}
      </Modal>

      {/* Create new manual task */}
      {/* <Modal
        open={isModalManualTaskOpen}
        onOk={handleOkManualTask}
        title={
          <div className="flex items-center w-full justify-center">
            <span className="font-semibold">Create</span>
            <Tag color="cyan" className="ml-1 text-base">
              Manual Task{' '}
            </Tag>
          </div>
        }
        closable={false}
        footer={null}
        destroyOnClose
        className="top-5"
      >
        <div className="py-4 px-2 max-h-[88vh] overflow-y-auto"></div>
      </Modal> */}
    </div>
  );
};

export default TaskManagement;
