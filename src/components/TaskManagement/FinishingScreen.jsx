import { <PERSON><PERSON>, <PERSON> } from 'antd';
import Styled from 'styled-components';
import rewardIcon from '../../assets/img/reward.png'

const BannerWrapper = Styled.figure`
  .ant-card {
    @media only screen and (max-width: 1199px) {
      margin-bottom: 50px !important;
    }
    @media only screen and (max-width: 991px) {
      margin-bottom: 30px !important;
    }
  }
`;

const Figure3 = Styled.figure`
    position: relative;    
    min-height: 180px;
    margin-bottom: 0;
    padding: 18px 0 0;
    &.theme-wide{
        padding: 0;
        min-height: 100%;
        width: 100%;
        figcaption{
            h2{
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 12px;
            }
            p{
                margin-bottom: 18px;
            }
        }
    }
    &.theme-3{
        padding: 0px 0 30px;
        img{
            bottom: -55px;
        }
        figcaption{  
            h2{
                margin-bottom: 10px;
            }
            p{
                margin-bottom: 18px;
            }
        }
    }
    h2{
        font-size: 30px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 25px;
    }
    p{
        color: #fff;
        opacity: .7;
    }
    img {
        position: absolute;
        bottom: -65px;
        ${({ theme }) => (theme.rtl ? 'left' : 'right')}: -25px;
        @media only screen and (max-width: 1599px){
            max-width: 150px;
        }
    }
    button{
        color: ${({ theme }) => theme['primary-color']} !important;
        &:focus{
            background-color: #fff !important;
        }
        &.ant-btn-lg{
            height: 44px;
            font-size: 15px;
            font-weight: 500;
        }
    }
`;

const FinishingScreen = ({ handleSubmitClose }) => {
  return (
    <BannerWrapper>
      <Card
        bodyStyle={{
          background: '#5F63F2',
          borderRadius: '10px',
          minHeight: '265px',
          display: 'flex',
          alignItems: 'center',
        }}
        headless
      >
        <Figure3 className="theme-wide">
          <img src={rewardIcon} alt="" />
          <figcaption>
            <h2>Congratulations!</h2>
            <p>You finished all tasks</p>
            <Button onClick={handleSubmitClose} size="large" type="default" className='text-white'>
              Click here to Close
            </Button>
          </figcaption>
        </Figure3>
      </Card>
    </BannerWrapper>
  );
};

export default FinishingScreen;
