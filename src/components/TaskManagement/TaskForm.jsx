import {
  <PERSON>Comple<PERSON>,
  Badge,
  Button,
  DatePicker,
  Form,
  Input,
  notification,
  Select,
  Spin,
  Tag,
  Tooltip,
} from 'antd';
import { useRef, useState } from 'react';
import AddTaskStep from '../BullHorn/AddTaskStep';
import { MERTAGS_DEFINITION } from '../../constants/common.constant';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor } from 'ckeditor5';
import { editorConfig } from '../BullHorn/BullhornSendEmailModal';
import {
  TASK_ACTION,
  TASK_ACTION_ICONS,
  TASK_ACTION_STATUS,
  TASK_CREATING_TYPE,
} from '../BullHorn/EmailtriggerStep';
import { Controller, useForm } from 'react-hook-form';

import clsx from 'clsx';
import {
  ContactsOutlined,
  EnvironmentOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';
import useInfiniteScrollWithSearch from '../../hooks/useInfinitiveScroll';
import { searchBullhornData } from '../../services/bullhorn';
import { arrayUniqueByKey } from '../../utils/common';
import { debounce } from 'lodash';
import {
  VALIDATE_STATUS_COLOR,
  VALIDATE_STATUS_ICON,
} from '../../containers/HotList/HotListTable';
import dayjs from 'dayjs';

const mergeFieldsBasedOnPlace = {
  definitions: [...MERTAGS_DEFINITION].filter(
    (item) => item.id.includes('RECIPIENT') || item.id === 'COMPANY_NAME'
  ),
  initialPreviewMode: '$defaultValues',
};

const initialData = {
  name: '',
  content: '',
  action: TASK_ACTION[0].value,
  dueDate: new Date(),
};

const TaskForm = ({
  handleCancelManualTask,
  handleAddManualTask,
  loading,
  setLoading,
}) => {
  const [task, setTask] = useState(initialData);

  const [openParticipantModal, setOpenParticipantModal] = useState(true);
  const [sendToList, setSendToList] = useState([]);
  const { control, setValue } = useForm();
  const editor = useRef(null);
  const timeoutRef = useRef(null);

  const handleCKEditorChange = (_event, editor) => {
    const data = editor?.getData() || '';
    setTask({ ...task, content: data });
  };

  const handelChangeSelect = (value) => {
    setTask({ ...task, action: value });
  };

  const handleSubmit = async () => {
    if (!task?.name || !task?.content) {
      notification.error({ message: 'Please fill in all required fields' });
      return;
    }
    if (!sendToList || sendToList?.length === 0) {
      notification.error({ message: 'Please add at least one participant!' });
      return;
    }

    const payload = {
      content: task?.content,
      status: TASK_ACTION_STATUS.PENDING,
      name: task?.name,
      type: task?.action,
      dueDate: dayjs(task?.dueDate).format('YYYY-MM-DDTHH:mm:ss'),
      creatingType: TASK_CREATING_TYPE.MANUAL,
    };
    handleAddManualTask(payload, sendToList);
  };

  const {
    options: contactOptions,
    isLoading: isLoadingContacts,
    handleSearch: handleContactSearch,
    setCompanyId,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientContact'));

  const {
    options: companyOptions,
    setOptions: companySetOptions,
    handleScrollPopup: handleCompanyScroll,
    handleSearch: handleCompanySearch,
    isLoading: isLoadingCompany,
    setLoading: setIsLoadingCompany,
    valueNotFound: valueNotFoundCompany,
    setStart: companySetStart,
    isLoadingScroll: isLoadingScrollCompany,
    setLoadingScroll: setIsLoadingScrollCompany,
  } = useInfiniteScrollWithSearch(searchBullhornData('ClientCorporation'));

  const handleChange = (_value, options) => {
    const dataSet = options
      ?.map((obj) => ({
        ...obj,
        name: obj?.name || obj?.firstName,
        email: obj.email,
        id: obj.contactId,
      }))
      .filter((item) => item?.name && item?.email);
    setSendToList([...dataSet]);
  };

  return (
    <div>
      <div>
        <div
          className={clsx(
            'flex items-center flex-col border rounded-md py-3 px-2 w-full gap-2 mb-2',
            'border-[#7CF5FF] border-2'
          )}
        >
          <div className="grid grid-cols-3 gap-5 items-center justify-between w-full">
            <Badge showZero count={sendToList?.length || 0}>
              <Button
                type="text"
                className="flex items-center font-medium w-full justify-center"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpenParticipantModal(!openParticipantModal);
                }}
              >
                <UsergroupAddOutlined className="text-cyan-600 text-xl font-semibold" />
              </Button>
            </Badge>
            <Select
              placeholder="Type of Task"
              className="w-full"
              rootClassName="company-status-container"
              defaultValue={
                task?.action
                  ? {
                      label: task?.action,
                      value: task?.action,
                    }
                  : TASK_ACTION[0]
              }
              options={TASK_ACTION}
              onChange={handelChangeSelect}
              optionRender={(opt) => {
                const { data } = opt;
                return (
                  <div className="flex items-center gap-2">
                    {TASK_ACTION_ICONS[data?.value]}
                    <span>{data?.label}</span>
                  </div>
                );
              }}
            />
            <Tooltip title="Due Date">
              <DatePicker
                defaultValue={dayjs(task?.dueDate)}
                format={'DD/MM/YYYY'}
                placeholder="Due Date"
                className="w-full border-red-400 text-red-400"
              />
            </Tooltip>
          </div>
          {openParticipantModal && (
            <div className="w-full linkedin-step-participant-container">
              <div className="w-full pb-2">
                <div className="">
                  <div className="flex items-center w-full">Company</div>
                  <AutoComplete
                    loading={isLoadingCompany}
                    onPopupScroll={(e) =>
                      handleCompanyScroll(e, 'ClientCorporation')
                    }
                    options={companyOptions?.map((option) => ({
                      ids: option.id,
                      value: option.name,
                      label: (
                        <>
                          <div className="grid">
                            <div className="flex justify-between">
                              <span className="text-base font-base">
                                {option.id} - {option.name}
                              </span>
                            </div>
                            <div className="contact-details">
                              <div className="flex">
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <PhoneOutlined />{' '}
                                  {option.phone ? option.phone : '-'}
                                </span>
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <InfoCircleOutlined />{' '}
                                  {option.status ? option.status : '-'}
                                </span>
                                <span className="text-gray-500 text-xs min-w-[200px]">
                                  <EnvironmentOutlined />
                                  {option.address &&
                                  option.address.city &&
                                  option.address.state
                                    ? `${option.address.city}, ${option.address.state}`
                                    : option.address && option.address.city
                                      ? option.address.city
                                      : option.address && option.address.state
                                        ? option.address.state
                                        : '-'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </>
                      ),
                    }))}
                    className="w-full bg-white mt-2"
                    onSelect={(value, option) => {
                      console.log('option: ', option);
                      setCompanyId(option?.ids || '');
                      handleContactSearch('', option?.ids || '');
                    }}
                    mode="multiple"
                    onSearch={(value) => {
                      if (timeoutRef.current) {
                        clearTimeout(timeoutRef.current);
                      }
                      timeoutRef.current = setTimeout(() => {
                        if (value) companySetStart(0);
                        handleCompanySearch(value);
                      }, 500);
                    }}
                    placeholder="Search Company"
                    notFoundContent={
                      isLoadingCompany ? (
                        <div className="w-full flex justify-center py-4">
                          <Spin size="default" />
                        </div>
                      ) : null
                    }
                  />
                </div>
                <div>
                  <div className="flex items-center w-full mt-2">
                    <p>
                      <span className="text-red-600 font-medium">*</span>{' '}
                      Participants
                    </p>
                  </div>
                  <div>
                    <Form.Item
                      className="add-contact-container !my-2"
                      name="participants"
                    >
                      <Controller
                        render={({ field }) => (
                          <Select
                            onChange={handleChange}
                            className="w-full"
                            placeholder="Please select"
                            notFoundContent={
                              isLoadingContacts ? (
                                <div className="w-full flex justify-center py-4">
                                  <Spin size="default" />
                                </div>
                              ) : null
                            }
                            loading={isLoadingContacts}
                            options={[...(contactOptions || [])]?.map(
                              (option) => ({
                                ...option,
                                value:
                                  (option?.id || option.email) +
                                  '_' +
                                  option.name,
                                label: option?.name || option?.email,
                                email: option?.email,
                                phone: option?.phone,
                                address: option?.address,
                                status: option?.status,
                                occupation: option?.occupation,
                                name: option?.name,
                                contactId: option?.id,
                                disabled:
                                  (!option?.name && !option?.firstName) ||
                                  !option?.email,
                              })
                            )}
                            value={arrayUniqueByKey([...sendToList], 'email')}
                            optionLabelProp="name"
                            tagRender={(props) => {
                              const { label, closable, onClose, value } = props;
                              const options = contactOptions;
                              const optionSelect = options.find(
                                (item) =>
                                  (item?.id || item.email) + '_' + item.name ===
                                  value
                              );
                              return (
                                <Tag
                                  className="mb-1 max-w-[20rem]"
                                  closable={closable}
                                  onClose={onClose}
                                  style={{ marginRight: 3 }}
                                  color={
                                    typeof value === 'number'
                                      ? '#f50'
                                      : value?.includes('CONTACTLIST')
                                        ? 'cyan'
                                        : value?.includes('HOTLIST')
                                          ? 'orange'
                                          : optionSelect?.emailStatus
                                            ? VALIDATE_STATUS_COLOR[
                                                optionSelect?.emailStatus
                                              ]
                                            : undefined
                                  }
                                  title={label || value}
                                >
                                  {label || value}
                                </Tag>
                              );
                            }}
                            optionRender={(opt) => {
                              const { data: option } = opt;
                              const lastOption =
                                [...contactOptions][
                                  contactOptions?.length - 1
                                ] || {};
                              const isLastOption =
                                lastOption && lastOption?.id === option?.id;
                              return (
                                option?.name && (
                                  <div>
                                    <div className="grid">
                                      <div className="flex items-center gap-1">
                                        <span className="text-base">
                                          {option.name || option?.label}
                                        </span>
                                        {option?.emailStatus ? (
                                          <Tag
                                            icon={
                                              VALIDATE_STATUS_ICON[
                                                option?.emailStatus
                                              ]
                                            }
                                            color={
                                              VALIDATE_STATUS_COLOR[
                                                option?.emailStatus
                                              ]
                                            }
                                          >
                                            {option?.emailStatus}
                                          </Tag>
                                        ) : (
                                          <Tag icon={<InfoCircleOutlined />}>
                                            Undefined
                                          </Tag>
                                        )}
                                      </div>
                                      <div className="contact-details">
                                        {option?.email && (
                                          <div className="flex">
                                            <span className="text-gray-700 text-xs min-w-[200px]">
                                              <MailOutlined />
                                              {option.email
                                                ? option.email
                                                : '-'}
                                            </span>
                                            <span className="text-gray-700 text-xs min-w-[200px]">
                                              <HomeOutlined />
                                              {option.clientCorporation?.name}
                                            </span>
                                          </div>
                                        )}
                                        {option?.address && (
                                          <div className="flex text-gray-700 text-xs font-medium">
                                            <ContactsOutlined />
                                            {option.occupation
                                              ? option.occupation
                                              : '-'}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {isLastOption && isLoadingContacts && (
                                      <div className="w-full flex justify-center py-4">
                                        <Spin size="default" />
                                      </div>
                                    )}
                                  </div>
                                )
                              );
                            }}
                            onSearch={async (searchText) => {
                              if (timeoutRef.current) {
                                clearTimeout(timeoutRef.current);
                              }
                              timeoutRef.current = setTimeout(async () => {
                                await handleContactSearch(searchText);
                              }, 500);
                            }}
                            mode="multiple"
                          />
                        )}
                        name="participants"
                        control={control}
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <div
            style={{
              textAlign: 'left',
            }}
            className="w-full font-medium"
          >
            <span className="text-red-600">*</span> Task Name
          </div>
          <Input
            value={task?.name || ''}
            onChange={(e) => setTask({ ...task, name: e.target.value })}
          />
        </div>
        <div
          style={{
            textAlign: 'left',
          }}
          className="w-full mt-2"
        >
          <div className="font-medium">
            <span className="text-red-600">*</span> Task Notes
          </div>
          <div className="w-full mt-2">
            <CKEditor
              ref={editor}
              editor={ClassicEditor}
              config={{
                ...editorConfig,
                toolbar: {
                  ...editorConfig.toolbar,
                  shouldNotGroupWhenFull: false,
                },
                mergeFields: { ...mergeFieldsBasedOnPlace },
              }}
              data={task?.content || ''}
              onChange={handleCKEditorChange}
              onAfterDestroy={(editor) => console.log('destroyyy: ', editor)}
            />
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between mt-4">
        <Button disabled={loading} onClick={handleCancelManualTask}>
          Close
        </Button>
        <Button onClick={handleSubmit} type="primary" loading={loading}>
          Submit
        </Button>
      </div>
    </div>
  );
};

export default TaskForm;
