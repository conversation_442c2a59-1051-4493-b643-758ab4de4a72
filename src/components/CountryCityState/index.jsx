/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { CountrySelect, StateSelect } from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';

export default function CountryCityState({ onCountryChange, onCityChange }) {
  const [countryId, setCountryId] = useState(0);
  return (
    <div className="flex justify-around">
      <div>
        <h6 className="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
          Country
        </h6>
        <CountrySelect
          onChange={(e) => {
            setCountryId(e.id);
            onCountryChange(e.name);
          }}
          placeHolder="Select Country"
        />
      </div>
      <div className="ms-2">
        <h6 className="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
          City
        </h6>
        <StateSelect
          countryid={countryId}
          onChange={(e) => {
            onCityChange(e.name);
          }}
          placeHolder="Select State"
        />
      </div>
    </div>
  );
}
