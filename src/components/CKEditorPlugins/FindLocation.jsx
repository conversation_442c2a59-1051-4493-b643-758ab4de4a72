import { notification } from 'antd';
import {
  // Other imports
  Plugin,
  ButtonView,
  icons,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class FindLocation extends Plugin {
  init() {
    const editor = this.editor;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('findlocation', () => {
      const findLocationFunc =
        editor.config.get('myProps.findLocation') || null;
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Find Location',
        withText: true,
        tooltip: 'Find location by the selected text.',
        icon: false,
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        const selectedText = [];
        const now = new Date();
        const selection = editor.model.document.selection;
        const range = selection.getFirstRange();

        for (const item of range.getItems()) {
          selectedText.push(item.data);
        }
        const locationText = selectedText.filter((text) => text)?.[0] || '';
        if (!locationText)
          return notification.warning({
            message: 'Please select text.',
          });
        findLocationFunc && findLocationFunc(locationText);
        // Change the model using the model writer.
        // editor.model.change((writer) => {
        //   // Insert the text at the user's current position.
        //   editor.model.insertContent(writer.createText(now.toString()));
        // });
      });

      return button;
    });
  }
}
