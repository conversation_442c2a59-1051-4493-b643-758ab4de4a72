import { notification } from 'antd';
import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class BulletPointSkills extends Plugin {
  init() {
    const editor = this.editor;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('bulletpointskills', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Bullet Point Skills',
        withText: true,
        tooltip: 'Insert Bullet Point Skills',
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        const now = new Date();

        // Change the model using the model writer.
        editor.model.change((writer) => {
          // Insert the text at the user's current position.
          const skills = editor.config.get('myProps.skills') || [];
          if (skills?.length === 0)
            return notification.warning({
              message: 'Can not get skills!',
            });
          const skillText = `&nbsp;
          <ul id="skills-id" style="padding: 0 0 0 30px;list-style-type: disc;" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">
            ${skills
              ?.filter((skill) => skill)
              ?.map((skill) => `<li >${skill}</li>`)
              .join('')}
          </ul>&nbsp;&nbsp;`;
          const htmlDP = editor.data.processor;
          const viewFragment = htmlDP.toView(skillText);
          const modelFragment = editor.data.toModel(viewFragment);

          editor.model.insertContent(modelFragment);
        });
      });

      return button;
    });
  }
}
