import { Plugin } from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

const ALL_TEXT_TAGS = 'p,span,a,h1,h2,h3,h4,h5,h6,table,td,th,ul,ol,li';

export class CustomizedPaste extends Plugin {
  init() {
    const editor = this.editor;

    editor.editing.view.document.on('clipboardInput', (evt, data) => {
      const dataTransfer = data?.dataTransfer?.getData('text/html');

      var divContentContainer = document?.createElement('div');
      divContentContainer.innerHTML = dataTransfer;

      var elComponents = divContentContainer.querySelectorAll(ALL_TEXT_TAGS);
      for (let i = 0; i < elComponents.length; ++i) {
        elComponents[i].style.fontSize = '11pt';
        elComponents[i].style.fontFamily = 'Calibri';
      }

      const parsedContent = divContentContainer.innerHTML;
      const viewFragment = editor.data.processor.toView(parsedContent);
      const modelFragment = editor.data.toModel(viewFragment);
      if (parsedContent.trim()) {
        // Add content after changing format
        editor.model.insertContent(modelFragment);

        // Prevent pasting raw copied content
        evt.stop();
      }
    });
  }
}
