import { notification } from 'antd';
import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class SentencesSKills extends Plugin {
  init() {
    const editor = this.editor;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('sentencesskills', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Sentences SKills',
        withText: true,
        tooltip: 'Insert Sentences SKills',
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        const now = new Date();

        // Change the model using the model writer.
        editor.model.change((writer) => {
          const skills = editor.config.get('myProps.skills') || [];
          if (skills?.length === 0)
            return notification.warning('Can not get skills!');
          //   const skillListEl = document?.getElementById('skills-id');
          const sentencesSkillsText = skills?.join(', ');
          // Insert the text at the user's current position.
          const skillText = `&nbsp;<span id="skills-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1 inline-block">${sentencesSkillsText}</span>&nbsp;&nbsp;`;
          const htmlDP = editor.data.processor;
          const viewFragment = htmlDP.toView(skillText);
          const modelFragment = editor.data.toModel(viewFragment);

          editor.model.change((writer) => {
            // Insert the text at the user's current position.
            editor.model.insertContent(modelFragment);
          });
        });
      });

      return button;
    });
  }
}
