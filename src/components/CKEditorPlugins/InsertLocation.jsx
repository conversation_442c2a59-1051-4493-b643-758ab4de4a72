import { notification } from 'antd';
import {
    // Other imports
    Plugin,
    ButtonView,
  } from 'ckeditor5';
  
  import 'ckeditor5/ckeditor5.css';
  
  export class Location extends Plugin {
    init() {
      const editor = this.editor;
      // The button must be registered among the UI components of the editor
      // to be displayed in the toolbar.
      editor.ui.componentFactory.add('location', () => {
        // The button will be an instance of ButtonView.
        const button = new ButtonView();
  
        button.set({
          label: 'Location',
          withText: true,
          tooltip: 'Insert Location',
        class: 'col-dropdown'
        });
  
        // Execute a callback function when the button is clicked.
        button.on('execute', () => {
          const location = editor.config.get( 'myProps.location' ) || '';
          // Change the model using the model writer.
          if(!location) return notification.warning('Can not found any location');
          const locationText = `&nbsp;<span id="location-id" class="cursor-default outline outline-[1px] outline-red-500 hover:outline-red-600 hover:outline-[2px] px-1">${location}</span>&nbsp;&nbsp;`
          const htmlDP = editor.data.processor;
          const viewFragment = htmlDP.toView(locationText)
          const modelFragment = editor.data.toModel(viewFragment);

          editor.model.change((writer) => {
            // Insert the text at the user's current position.
            editor.model.insertContent(modelFragment);
          });
        });
  
        return button;
      });
    }
  }
  