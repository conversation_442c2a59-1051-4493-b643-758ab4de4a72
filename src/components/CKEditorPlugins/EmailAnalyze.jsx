import { notification } from 'antd';
import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';
import { icons } from 'ckeditor5-premium-features';

import 'ckeditor5/ckeditor5.css';

export class EmailAnalyze extends Plugin {
  init() {
    const editor = this.editor;
    const analyzeEmail =
      editor.config.get('myProps.analyzeEmail') || null;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('analyzeemail', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Analyze',
        withText: true,
        tooltip: 'Analyze email',
        icon: icons.robotPencil,
        class: 'customized-tool-bar-btn'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        analyzeEmail && analyzeEmail()
      });

      return button;
    });
  }
}
