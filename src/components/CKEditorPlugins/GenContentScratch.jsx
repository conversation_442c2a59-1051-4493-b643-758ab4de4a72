import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class GenContentScratch extends Plugin {
  init() {
    const editor = this.editor;
    const handlePilotAIClick =
      editor.config.get('myProps.handlePilotAIClick') || null;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('gencontentscratch', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Recruitment Pilot AI',
        withText: true,
        // tooltip: 'Recruitment Pilot AI',
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        handlePilotAIClick && handlePilotAIClick('genContentScratch')
      });

      return button;
    });
  }
}
