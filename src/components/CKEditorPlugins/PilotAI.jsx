import {
  addListToDropdown,
  Collection,
  createDropdown,
  Locale,
  ViewModel,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class PilotAI extends Plugin {
  init() {
    const editor = this.editor;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('pilotai', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();
      const locale = new Locale();

      const collection = new Collection();
      collection.add({
        type: 'button',
        model: new ViewModel({
          label: 'Button',
          withText: true,
        }),
      });

      button.set({
        label: 'Pilot AI',
        withText: true,
        
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        const now = new Date();

        // Change the model using the model writer.
        editor.model.change((writer) => {
          // Insert the text at the user's current position.
          editor.model.insertContent(writer.createText(now.toString()));
        });
      });

      return button;
    });
  }
}
