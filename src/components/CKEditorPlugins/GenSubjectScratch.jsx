import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class GenSubjectScratch extends Plugin {
  init() {
    const editor = this.editor;
    const handlePilotAIClick =
      editor.config.get('myProps.handlePilotAIClick') || null;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('gensubjectscratch', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Email subject line generator',
        withText: true,
        // tooltip: 'Email subject line generator',
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        handlePilotAIClick && handlePilotAIClick('genSubjectScratch')
      });

      return button;
    });
  }
}
