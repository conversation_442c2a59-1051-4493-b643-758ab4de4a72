import { notification } from 'antd';
import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';
import { icons } from 'ckeditor5-premium-features';

import 'ckeditor5/ckeditor5.css';

export class RewriteByZileoAI extends Plugin {
  init() {
    const editor = this.editor;
    const rewriteByZileoAIFunc =
      editor.config.get('myProps.rewriteByZileoAI') || null;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('rewritebyzileoai', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Rewrite By Zileo AI',
        withText: true,
        tooltip: 'Rewrite selected text By Zileo AI.',       
        icon: icons.robotPencil,
        class: 'customized-tool-bar-btn'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        const selectedText = [];
        const selection = editor.model.document.selection;
        const range = selection.getFirstRange();

        for (const item of range.getItems()) {
          selectedText.push(item.data);
        }
        const textList = selectedText.filter((text) => text);
        if (!textList || textList?.length === 0)
          return notification.warning({
            message: 'Please select text.',
          });
        rewriteByZileoAIFunc && rewriteByZileoAIFunc(textList);
      });

      return button;
    });
  }
}
