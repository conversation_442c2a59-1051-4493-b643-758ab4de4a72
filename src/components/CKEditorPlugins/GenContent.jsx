import {
  // Other imports
  Plugin,
  ButtonView,
} from 'ckeditor5';

import 'ckeditor5/ckeditor5.css';

export class GenContent extends Plugin {
  init() {
    const editor = this.editor;
    const handlePilotAIClick =
      editor.config.get('myProps.handlePilotAIClick') || null;
    // The button must be registered among the UI components of the editor
    // to be displayed in the toolbar.
    editor.ui.componentFactory.add('gencontent', () => {
      // The button will be an instance of ButtonView.
      const button = new ButtonView();

      button.set({
        label: 'Look at vacancy and write a pitch',
        withText: true,
        // tooltip: 'Look at vacancy and write a pitch',
        class: 'col-dropdown'
      });

      // Execute a callback function when the button is clicked.
      button.on('execute', () => {
        handlePilotAIClick && handlePilotAIClick('genContent');
      });

      return button;
    });
  }
}
