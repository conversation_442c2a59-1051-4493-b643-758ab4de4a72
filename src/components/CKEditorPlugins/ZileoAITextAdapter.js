import { AIRequestError, AITextAdapter } from 'ckeditor5-premium-features';
import { generateAIAssistantMessage } from '../../services/search';

export class CustomAITextAdapter extends AITextAdapter {
  async sendRequest(requestData) {
    try {
      let prompt = requestData.query;
      if (requestData?.context) {
        prompt += '\n\n' + requestData.context;
      }

      const messages = [
        {
          role: 'user',
          content: `${prompt}`,
        },
      ];

      const payload = {
        messages,
        numOfChoice: 1,
        // stream: true
      };

      const { data } = await generateAIAssistantMessage(payload);

      if (data?.choices?.length > 0) {
        const responseText = data?.choices[0]?.message?.content;
        requestData.onData(responseText);
      } else {
        throw AIRequestError('Can not find any answer.');
      }
    } catch (error) {
      console.log('error: ', error);
      throw AIRequestError('The request failed for unknown reason.');
    }
  }
}
