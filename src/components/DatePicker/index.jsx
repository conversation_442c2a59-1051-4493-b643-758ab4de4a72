// eslint-disable-next-line max-classes-per-file
import React, { useEffect, useState } from 'react';
import { addDays } from 'date-fns';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { DateRangePicker } from 'react-date-range';
import { Button, DatePicker } from 'antd';
import { ItemWraper, ButtonGroup } from './style';

const DateRangePickerOne = ({
  handleSelectDateRange,
  setOpenPopup = false,
  defaultStartDate = '',
  defaultEndDate = '',
}) => {
  const [state, setState] = useState({
    datePickerInternational: null,
    dateRangePicker: {
      selection: {
        startDate: '',
        endDate: '',
        key: 'selection',
      },
    },
  });

  useEffect(() => {
    setState({
      datePickerInternational: null,
      dateRangePicker: {
        selection: {
          startDate: defaultStartDate,
          endDate: defaultEndDate,
          key: 'selection',
        },
      },
    });
    return () => {
      setState({
        datePickerInternational: null,
        dateRangePicker: {
          selection: {
            startDate: '',
            endDate: '',
            key: 'selection',
          },
        },
      });
    };
  }, []);

  const handleRangeChange = (which) => {
    setState({
      ...state,
      dateRangePicker: {
        ...state.dateRangePicker,
        ...which,
      },
    });
  };

  const { dateRangePicker } = state;
  const start = dateRangePicker?.selection?.startDate?.toString()?.split(' ');
  const end = dateRangePicker?.selection?.endDate?.toString()?.split(' ');

  return (
    <ItemWraper>
      <DateRangePicker
        onChange={handleRangeChange}
        showSelectionPreview
        moveRangeOnFirstSelection={false}
        className="PreviewArea"
        months={2}
        ranges={[dateRangePicker.selection]}
        direction="horizontal"
      />

      <ButtonGroup>
        {dateRangePicker?.selection?.startDate &&
        dateRangePicker?.selection?.endDate ? (
          <p>{`${start[1]} ${start[2]} ${start[3]} - ${end[1]} ${end[2]} ${end[3]}`}</p>
        ) : (
          <p>All Time</p>
        )}
        {dateRangePicker?.selection?.startDate &&
          dateRangePicker?.selection?.endDate && (
            <Button
              size="small"
              type="primary"
              htmlType="submit"
              onClick={() => handleSelectDateRange('')}
            >
              All time
            </Button>
          )}
        <Button
          size="small"
          type="primary"
          htmlType="submit"
          onClick={() =>
            handleSelectDateRange(
              `${start[1]} ${start[2]} ${start[3]} - ${end[1]} ${end[2]} ${end[3]}`
            )
          }
        >
          Apply
        </Button>
        {setOpenPopup && (
          <Button
            size="small"
            type="white"
            outlined
            onClick={() => {
              handleSelectDateRange('');
              setOpenPopup && setOpenPopup(false);
            }}
          >
            Cancel
          </Button>
        )}
      </ButtonGroup>
    </ItemWraper>
  );
};

class CustomDateRange extends React.Component {
  // eslint-disable-next-line react/state-in-constructor
  state = {
    startValue: null,
    endValue: null,
    endOpen: false,
  };

  disabledStartDate = (startValue) => {
    const { endValue } = this.state;
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > endValue.valueOf();
  };

  disabledEndDate = (endValue) => {
    const { startValue } = this.state;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf();
  };

  onChange = (field, value) => {
    this.setState({
      [field]: value,
    });
  };

  onStartChange = (value) => {
    this.onChange('startValue', value);
  };

  onEndChange = (value) => {
    this.onChange('endValue', value);
  };

  handleStartOpenChange = (open) => {
    if (!open) {
      this.setState({ endOpen: true });
    }
  };

  handleEndOpenChange = (open) => {
    this.setState({ endOpen: open });
  };

  render() {
    const { startValue, endValue, endOpen } = this.state;

    return (
      <div>
        <DatePicker
          disabledDate={this.disabledStartDate}
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          value={startValue}
          placeholder="Start"
          onChange={this.onStartChange}
          onOpenChange={this.handleStartOpenChange}
          style={{ margin: '5px' }}
        />

        <DatePicker
          disabledDate={this.disabledEndDate}
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          value={endValue}
          placeholder="End"
          onChange={this.onEndChange}
          open={endOpen}
          onOpenChange={this.handleEndOpenChange}
          style={{ margin: '5px' }}
        />
      </div>
    );
  }
}

export { DateRangePickerOne, CustomDateRange };
