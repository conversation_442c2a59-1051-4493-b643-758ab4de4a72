/* eslint-disable react/jsx-key */
import React from 'react';
import { ContactsFilled, ShoppingFilled, HomeFilled } from '@ant-design/icons';
import { Tabs } from 'antd';
import Vacancy from './Vacancy';
import Contracts from './Contracts';
import Company from './Company';

const label = ['Vacancy', 'Contracts', 'Company'];
const content = [<Vacancy />, <Contracts />, <Company />];
const AddJob = () => (
  <div className="mt-10">
    <Tabs
      defaultActiveKey="1"
      items={[ShoppingFilled, ContactsFilled, HomeFilled].map((Icon, i) => {
        const id = String(i + 1);
        return {
          label: (
            <span>
              <div className="flex">
                <div className="w-3">
                  <Icon style={{ fontSize: '25px', color: '#e30075' }} />
                </div>
                <div className="mt-1 font-[PoppinsSemiBold] sm:ms-5 ms-4">
                  {label[id - 1]}
                </div>
              </div>
            </span>
          ),
          key: id,
          children: content[id - 1],
        };
      })}
    />
    <div className="fixed bottom-0 left-0 right-0 bg-[#000000] align-middle ">
      <div className="flex justify-end mb-2">
        <div className="mt-2 flex items-center justify-end gap-x-6">
          <button
            // onClick={onSaveSearchClick}
            className="rounded-md bg-indigo-600 px-2 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Cancel
          </button>
        </div>
        <div className="mt-2 flex items-center justify-end gap-x-6">
          <button
            // onClick={onSaveSearchClick}
            className="rounded-md ms-5 mr-5 bg-[#e30075] px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
);
export default AddJob;
