import React, { useState } from 'react';
import { Radio, DatePicker, Input, Select } from 'antd';
import { status, payUnit, clientData } from './const';
import {
  PoundOutlined,
  DesktopOutlined,
  FormOutlined,
  MailOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
const { TextArea } = Input;

function Vacancy() {
  const [data, setData] = useState({
    jobTitle: '',
    status: '',
    open_close: '',
    consultant: '',
    ofOpening: '',
    startDate: '',
    permFee: '',
    salary: '',
    payRate: '',
    billRate: '',
    markup: '',
    email: '',
    street: '',
    city: '',
    region: '',
    postal: '',
    payUnit: '',
    date: '',
    publishDescription: '',
    client: '',
    contract: '',
    industry: '',
    jobCategory: '',
    requireSkill: '',
    interUser: '',
    disList: '',
    country: '',
  });

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setData({ ...data, [name]: value });
  };

  const handleStatusSelChange = (value) => {
    setData({ ...data, status: value });
  };

  const handlePaySelChange = (value) => {
    setData({ ...data, payUnit: value });
  };

  const onInterDesTextChange = (value) => {
    setData({ ...data, interDescription: value });
  };

  const onPubDesTextChange = (value) => {
    setData({ ...data, publishDescription: value });
  };

  const onDateChange = (dateString) => {
    setData({ ...data, date: dateString });
  };

  const onClientASelChange = (value) => {
    setData({ ...data, client: value });
  };

  const onContractASelChange = (value) => {
    setData({ ...data, contract: value });
  };

  const onIndusASelChange = (value) => {
    setData({ ...data, industry: value });
  };

  const onJobCateASelChange = (value) => {
    setData({ ...data, jobCategory: value });
  };

  const onReqSkillASelChange = (value) => {
    setData({ ...data, requireSkill: value });
  };

  const onInterUserASelChange = (value) => {
    setData({ ...data, interUser: value });
  };

  const onDisListASelChange = (value) => {
    setData({ ...data, disList: value });
  };

  const onCountryASelChange = (value) => {
    setData({ ...data, country: value });
  };

  return (
    <>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="jobTitle"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Job Title
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="jobTitle"
            id="jobTitle"
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="status"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Status
        </label>
        <Select
          id="status"
          defaultValue={status[0].name}
          style={{ width: '100%' }}
          onChange={handleStatusSelChange}
          options={status}
        />
      </div>
      <div className="sm:ms-12 ms-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Open/Close
        </label>
        <div className="mt-2">
          <Radio.Group
            onChange={(e) => setData({ ...data, open_close: e.target.value })}
          >
            <Radio.Button value="open">Open</Radio.Button>
            <Radio.Button value="close">Close</Radio.Button>
          </Radio.Group>
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="consultant"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Consultant
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="consultant"
            id="consultant"
            autoComplete="given-name"
            value={data.consultant}
            onChange={handleInputChange}
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Client
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onClientASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Contract
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onContractASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="ofOpening"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          # of Openings
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="ofOpening"
            id="ofOpening"
            onChange={handleInputChange}
            value={data.ofOpening}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="startDate"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Start Date
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="startDate"
            id="startDate"
            value={data.startDate}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Scheduled End Date
        </label>
        <div className="mt-2">
          <DatePicker onChange={onDateChange} />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Source
        </label>
        <div>
          <Select
            id="status"
            defaultValue={status[0].name}
            style={{ width: '100%' }}
            onChange={handleStatusSelChange}
            options={status}
          />
        </div>
      </div>

      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <PoundOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Compensation & Fees
        </div>
      </div>

      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="permFee"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Perm Fee[%]
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="permFee"
            id="permFee"
            value={data.permFee}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="salary"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Salary
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="salary"
            id="salary"
            value={data.salary}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Pay Unit
        </label>
        <div>
          <Select
            id="status"
            defaultValue={payUnit[0].name}
            style={{ width: '100%' }}
            onChange={handlePaySelChange}
            options={payUnit}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="payRate"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Pay Rate
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="payRate"
            id="payRate"
            value={data.payRate}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="billRate"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Bill Rate
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="billRate"
            id="billRate"
            value={data.billRate}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="markup"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Mark-up%
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="markup"
            id="markup"
            value={data.markup}
            onChange={handleInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <DesktopOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Desired Experience
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Industries
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onIndusASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Job Category
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onJobCateASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Required Skills
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onReqSkillASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <FormOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Job Description
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor=""
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Internal Description
        </label>
        <div className="mt-2">
          <TextArea
            placeholder="textarea with clear icon"
            allowClear
            onChange={(e) => onInterDesTextChange(e.target.value)}
          />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <EnvironmentOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Job Location
        </div>
      </div>
      <div className="sm:ms-12 ms-3 w-[70%] min-w-[350px]">
        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-4">
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Email address
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={data.email}
                  onChange={handleInputChange}
                  autoComplete="email"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-6">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Country
              </label>
              <div className="mt-2">
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="Select a person"
                  optionFilterProp="children"
                  onChange={onCountryASelChange}
                  // onSearch={onSearch}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={clientData}
                />
              </div>
            </div>

            <div className="col-span-full">
              <label
                htmlFor="street"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Street address
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="street"
                  id="street"
                  value={data.street}
                  onChange={handleInputChange}
                  autoComplete="street-address"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2 sm:col-start-1">
              <label
                htmlFor="city"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                City
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="city"
                  id="city"
                  value={data.city}
                  onChange={handleInputChange}
                  autoComplete="address-level2"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="region"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                State / Province
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="region"
                  id="region"
                  value={data.region}
                  onChange={handleInputChange}
                  autoComplete="address-level1"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="postal"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                ZIP / Postal code
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="postal"
                  id="postal"
                  value={data.postal}
                  onChange={handleInputChange}
                  autoComplete="postal-code"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <FormOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Job Board Publishing(Optional)
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Published Description
        </label>
        <div className="mt-2">
          <TextArea
            placeholder="textarea with clear icon"
            allowClear
            onChange={(e) => onPubDesTextChange(e.target.value)}
          />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <MailOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Email Notificatoin
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Internal User
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onInterUserASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Distribution List
        </label>
        <div className="mt-2">
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Select a person"
            optionFilterProp="children"
            onChange={onDisListASelChange}
            // onSearch={onSearch}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={clientData}
          />
        </div>
      </div>
    </>
  );
}

export default Vacancy;
