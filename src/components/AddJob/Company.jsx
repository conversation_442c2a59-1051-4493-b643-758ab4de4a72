import React from 'react';
import { Input } from 'antd';
import AutoSelect from '../AutoSelect';
import {
  HomeOutlined,
  PoundOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';
const { TextArea } = Input;

function Company() {
  return (
    <>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company Name
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Status
        </label>
        <div className="w-[70%] min-w-[350px]">
          {/* <Select role={source} /> */}
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <HomeOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Categories & Skills
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Parent Company
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Industries
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company Comments
        </label>
        <div className="mt-2">
          <textarea
            id="about"
            name="about"
            rows={3}
            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            defaultValue={''}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company Description
        </label>
        <div className="mt-2">
          <textarea
            id="about"
            name="about"
            rows={3}
            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            defaultValue={''}
          />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <EnvironmentOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Main Location/Contact Info
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company Website
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Mobile Phone
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 w-[70%] min-w-[350px]">
        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Country
              </label>
              <div className="mt-2">
                <AutoSelect id="first-name" />
              </div>
            </div>

            <div className="col-span-full">
              <label
                htmlFor="street-address"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Street address
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="street-address"
                  id="street-address"
                  autoComplete="street-address"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2 sm:col-start-1">
              <label
                htmlFor="city"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                City
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="city"
                  id="city"
                  autoComplete="address-level2"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="region"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                State / Province
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="region"
                  id="region"
                  autoComplete="address-level1"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="postal-code"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                ZIP / Postal code
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="postal-code"
                  id="postal-code"
                  autoComplete="postal-code"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <PoundOutlined style={{ fontSize: '20px' }} className="text-white" />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Billing Information
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Billing Contact
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Billing Phone
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Invoice Format Information
        </label>
        <div className="mt-2">
          <TextArea
            placeholder="textarea with clear icon"
            allowClear
            // onChange={onChange}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Standard Fee Arrangement(%)
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3 w-[70%] min-w-[350px]">
        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Billing Country
              </label>
              <div className="mt-2">
                <AutoSelect id="first-name" />
              </div>
            </div>

            <div className="col-span-full">
              <label
                htmlFor="street-address"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Billing address
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="street-address"
                  id="street-address"
                  autoComplete="street-address"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2 sm:col-start-1">
              <label
                htmlFor="city"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Billing City
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="city"
                  id="city"
                  autoComplete="address-level2"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="region"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Billing Address2
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="region"
                  id="region"
                  autoComplete="address-level1"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="postal-code"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Billing Postal code
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="postal-code"
                  id="postal-code"
                  autoComplete="postal-code"
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Company;
