import React, { useState } from 'react';
import { Select, Input } from 'antd';
import AutoSelect from '../AutoSelect';
import { payUnit } from './const';
import {
  ContactsOutlined,
  DesktopOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
const { TextArea } = Input;

function Contracts() {
  const [data, setData] = useState({
    title: '',
    firstName: '',
    mobileName: '',
    surname: '',
    consultant: '',
    type: '',
    secondaryOwners: '',
    company: '',
    department: '',
    jobTitle: '',
    reportsTo: '',
    workEmail: '',
    personalEmail: '',
    workPhone: '',
    mobilePhone: '',
    otherPhone: '',
    fax: '',
    country: '',
    streetAddress: '',
    city: '',
    region: '',
    postalCode: '',
    industry: '',
    gerneralComments: '',
    refferedBy: '',
    bullhornAutoScore: '',
    status: '',
  });

  const onInputChange = () => {
    // const { name, value } = event.target;
    // setData({ ...data, [name]: value });
  };

  const onTitleSelChange = (value) => {
    setData({ ...data, title: value });
  };

  const onStatusSelChange = (value) => {
    setData({ ...data, status: value });
  };

  const onTypeSelChange = (value) => {
    setData({ ...data, type: value });
  };

  const onDepartSelChange = (value) => {
    setData({ ...data, department: value });
  };

  const onChange = () => {};

  return (
    <>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Title
        </label>
        <div className="w-[70%] min-w-[350px]">
          <Select
            id="status"
            defaultValue={payUnit[0].name}
            style={{ width: '100%' }}
            onChange={onTitleSelChange}
            options={payUnit}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="firstName"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          First Name
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="firstName"
            id="firstName"
            value={data.firstName}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="mobileName"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Mobile Name
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="mobileName"
            id="mobileName"
            value={data.mobileName}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="surname"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Surname
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="surname"
            id="surname"
            value={data.surname}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="consultant"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Consultant
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="consultant"
            id="consultant"
            value={data.consultant}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Status
        </label>
        <div className="w-[70%] min-w-[350px]">
          <Select
            id="status"
            defaultValue={payUnit[0].name}
            style={{ width: '100%' }}
            onChange={onStatusSelChange}
            options={payUnit}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Type
        </label>
        <div className="w-[70%] min-w-[350px]">
          <Select
            id="status"
            defaultValue={payUnit[0].name}
            style={{ width: '100%' }}
            onChange={onTypeSelChange}
            options={payUnit}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Secondary Owners
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Department
        </label>
        <div className="w-[70%] min-w-[350px]">
          <Select
            id="status"
            defaultValue={payUnit[0].name}
            style={{ width: '100%' }}
            onChange={onDepartSelChange}
            options={payUnit}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="jobTitle"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Job title
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="jobTitle"
            id="jobTitle"
            value={data.jobTitle}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Reports to
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <ContactsOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Contract Information
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="workEmail"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Work Email
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="workEmail"
            id="workEmail"
            value={data.workEmail}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="personalEmail"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Personal Email
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="personalEmail"
            id="personalEmail"
            value={data.personalEmail}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="workPhone"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Work Phone
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="workPhone"
            id="workPhone"
            value={data.workPhone}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="mobilePhone"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Mobile Phone
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="mobilePhone"
            id="mobilePhone"
            value={data.mobilePhone}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="otherPhone"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Other Phone
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="otherPhone"
            id="otherPhone"
            value={data.otherPhone}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="fax"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Fax
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="fax"
            id="fax"
            value={data.fax}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
      <div className="sm:ms-12 ms30  w-[70%] min-w-[350px]">
        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label
                htmlFor="country"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Country
              </label>
              <div className="mt-2">
                <AutoSelect id="first-name" />
              </div>
            </div>

            <div className="col-span-full">
              <label
                htmlFor="streetAddress"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                Street address
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="streetAddress"
                  id="streetAddress"
                  value={data.streetAddress}
                  onChange={onInputChange}
                  autoComplete="street-address"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2 sm:col-start-1">
              <label
                htmlFor="city"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                City
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="city"
                  id="city"
                  value={data.city}
                  onChange={onInputChange}
                  autoComplete="address-level2"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="region"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                State / Province
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="region"
                  id="region"
                  value={data.region}
                  onChange={onInputChange}
                  autoComplete="address-level1"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div className="sm:col-span-2">
              <label
                htmlFor="postalCode"
                className="block text-sm font-medium leading-6 text-gray-900"
              >
                ZIP / Postal code
              </label>
              <div className="mt-2">
                <input
                  type="text"
                  name="postalCode"
                  id="postalCode"
                  value={data.postalCode}
                  onChange={onInputChange}
                  autoComplete="postal-code"
                  className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <DesktopOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Categories & Skills
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Industry
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="flex w-full bg-[#000000] rounded-lg h-10 mt-10">
        <div className="mt-1 ms-5">
          <InfoCircleOutlined
            style={{ fontSize: '20px' }}
            className="text-white"
          />
        </div>
        <div className="font-[PoppinsRegular] text-lg mt-1 ms-3 text-white">
          Additional Information
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          General Comments
        </label>
        <div className="mt-2">
          <TextArea
            name="description"
            placeholder="textarea with clear icon"
            allowClear
            onChange={onChange}
          />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Reffered By
        </label>
        <div className="mt-2">
          <AutoSelect id="first-name" />
        </div>
      </div>
      <div className="sm:ms-12 ms-3  pt-8 w-[70%] min-w-[350px]">
        <label
          htmlFor="bullhornAutoScore"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Bullhorn Automation Score
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="bullhornAutoScore"
            id="bullhornAutoScore"
            value={data.bullhornAutoScore}
            onChange={onInputChange}
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>
    </>
  );
}

export default Contracts;
