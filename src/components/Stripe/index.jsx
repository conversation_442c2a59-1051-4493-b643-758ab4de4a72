import { CheckoutProvider, Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import CheckoutForm from './CheckoutForm';
import axiosInstance from '../../utils/axios';
import {
  getUserViewAs,
  getUserViewAsLicense,
  getUserViewAsSubscriptionId,
} from '../../helpers/getUserViewAs';
import { Button, Flex, Image, Radio, Spin, Table } from 'antd';
import { useEffect, useState } from 'react';
import zileoLogo from '../../assets/img/welcome/logo.png';
import {
  CalendarOutlined,
  HourglassOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import {
  planIdConstant,
  planIdPriceConstant,
  planPriceConstant,
  planTextConstant,
} from '../../containers/CompanyOnboarding/LicensePackageSelection';
import { TableWrapper } from '../../containers/CRM/styled';
import {
  getUserOrganizationDetail,
  getUserOrganizations,
} from '../../services/users';
import { getPaymentInfor } from '../../services/payment';

export const STRIPE_PUBLISHABLE_KEY = import.meta.env
  .VITE_STRIPE_PUBLISHABLE_KEY;

const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);

export const perPaymentOptions = {
  monthly: 'monthly',
  yearly: 'yearly',
  annually: 'annually',
};

const StripeApp = () => {
  const orgLicense = getUserViewAsLicense();
  const amount = planPriceConstant[orgLicense?.toUpperCase()];
  const subscriptionId = getUserViewAsSubscriptionId();

  const [isPaid, setPaid] = useState(false);
  const [payPer, setPayPer] = useState(perPaymentOptions.monthly);
  const [paymentInfor, setPaymentInfor] = useState(null);
  const [loading, setLoading] = useState(false);
  const [getPaymentLoading, setGetPaymentLoading] = useState(true);
  const [orgEmployees, setOrgEmployees] = useState([]);
  const [orgDetail, setOrgDetail] = useState(null);

  const fetchClientSecret = async () => {
    setGetPaymentLoading(true);
    try {
      const { data } = await getPaymentInfor(subscriptionId);
      if (data?.result?.message === 'Subscription is already active') {
        setPaid(true);
        setGetPaymentLoading(false);
        return '';
      }
      if (data?.result) {
        setPaymentInfor({
          clientSecret: data.result.clientSecret,
          paymentIntentId: data.result.paymentIntentId,
        });
        return data.result?.clientSecret || '';
      }
      setGetPaymentLoading(false);
      return '';
    } catch (error) {
      const message = error?.response?.data?.message;
      if (message === 'Subscription is already active') {
        setPaid(true);
        setGetPaymentLoading(false);
      }
      console.error('Error fetching client secret:', error);
      return '';
    }
  };

  const getEmployeesFromOrganization = async () => {
    setLoading(true);
    try {
      const { data } = await getUserOrganizationDetail();
      if (data?.result?.users?.length > 0) {
        const users = data.result.users.map((user) => ({
          email: user.email,
          unit: amount,
          key: user.id,
          id: user.id,
        }));
        const renewInterval = data?.result?.renewInterval;
        setPayPer(
          renewInterval === perPaymentOptions.annually
            ? perPaymentOptions.yearly
            : perPaymentOptions.monthly
        );
        setOrgDetail(data?.result);
        setOrgEmployees(users);
      }
      console.log('Employees from organization:', data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching employees:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getEmployeesFromOrganization();
    fetchClientSecret();
  }, []);

  const columns = [
    {
      title: 'User',
      dataIndex: 'email',
      key: 'email',
      width: '50%',
      render: (email) => {
        return <div>{email}</div>;
      },
    },
    {
      title: 'Price',
      dataIndex: 'unit',
      key: 'unit',
      width: '20%',
      align: 'center',
      render: (unit) => {
        return <div className="flex justify-center items-center">${unit}</div>;
      },
    },
    {
      title: 'Total',
      dataIndex: 'unit',
      key: 'unit',
      width: '30%',
      render: (unit) => {
        return (
          <div className="flex justify-end">
            ${payPer === perPaymentOptions.monthly ? unit : +unit * 12}
          </div>
        );
      },
    },
  ];

  const totalPrice =
    orgDetail?.subscription?.licenseCount *
    planIdPriceConstant[orgDetail?.subscription?.planId];

  return (
    <>
      {isPaid && (
        <div className="flex items-center justify-center flex-col gap-2 p-8 flex-col">
          <p className="font-medium text-green-700">
            <SafetyCertificateOutlined className="text-2xl mr-2" />
            Your subscription is now active.
          </p>
          <p className="text-sm text-gray-500">
            Please click the continue button below to move next step.
          </p>
        </div>
      )}
      {!isPaid &&
        (getPaymentLoading && !paymentInfor ? (
          <div>
            <Spin size="large" className="flex items-center justify-center" />
          </div>
        ) : (
          <Elements
            stripe={stripePromise}
            options={{ clientSecret: paymentInfor?.clientSecret }}
          >
            <div className="grid xl:grid-cols-2 gap-4 grid-cols-1">
              <div className="w-full flex flex-col gap-4 border rounded-md shadow-sm p-4">
                <div className="flex flex-col gap-4 mb-4">
                  <div className="font-medium text-gray-900">Payment per:</div>
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center py-2 ${payPer === perPaymentOptions.monthly ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      // onClick={() => {
                      //   setPayPer(perPaymentOptions.monthly);
                      //   setPaymentInfor({
                      //     ...paymentOptions,
                      //     amount, // Monthly amount
                      //   });
                      // }}
                    >
                      <HourglassOutlined className="text-2xl text-cyan-600" />
                      <span className="font-semibold">Monthly</span>
                    </Button>
                    <Button
                      className={`shadow-md border-[2px] relative flex flex-col gap-2 h-auto items-center justify-center py-2 ${payPer === perPaymentOptions.yearly ? 'border-cyan-700 bg-cyan-100' : ''}`}
                      // onClick={() => {
                      //   setPayPer(perPaymentOptions.yearly);
                      //   setPaymentInfor({
                      //     ...paymentOptions,
                      //     amount: amount * 12, // Monthly amount
                      //   });
                      // }}
                    >
                      <CalendarOutlined className="text-2xl text-cyan-600" />
                      <span className="font-semibold">Yearly</span>
                    </Button>
                  </div>
                  <div className="text-xs">
                    Exchange rate and fees of your bank may apply
                  </div>
                </div>
                <div className="flex items-center justify-between gap-4 pt-4 border-t">
                  {/* <Image src={zileoLogo} width={100} preview={false} /> */}
                  <div>
                    <h2 className="text-lg font-semibold text-cyan-600 flex items-center">
                      <SafetyCertificateOutlined className="text-2xl mr-2" />
                      {planTextConstant[orgDetail?.subscription?.planId]}
                    </h2>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">
                      {payPer === perPaymentOptions.monthly
                        ? 'Monthly subscription'
                        : 'Yearly subscription'}
                    </p>
                    <p className="text-lg font-bold w-full flex justify-end">
                      {loading ? <Spin size="small" /> : `$${totalPrice}`}
                    </p>
                  </div>
                </div>
                <div>
                  <Flex justifyContent="space-between" alignItems="center">
                    <div className="text-sm text-gray-700 font-medium">
                      <span className="font-semibold">
                        {orgDetail?.subscription?.licenseCount}{' '}
                        {+orgDetail?.subscription?.licenseCount > 1
                          ? 'users'
                          : 'user'}{' '}
                      </span>
                      in your organization
                    </div>
                  </Flex>
                </div>
                {/* <div className="contact-table max-h-64 overflow-y-auto">
            <TableWrapper className="table-responsive text-gray-800">
              <Table
                loading={loading}
                className="customized-style-pagination w-full"
                rowClassName="editable-row"
                dataSource={orgEmployees}
                columns={columns}
                pagination={false}
              />
            </TableWrapper>
          </div> */}
              </div>
              <div className="w-full">
                <CheckoutForm paymentInfor={paymentInfor} />
              </div>
            </div>
          </Elements>
        ))}
    </>
  );
};

export default StripeApp;
