import {
  PaymentElement,
  useCheckout,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { Button, notification } from 'antd';
import { useState } from 'react';
import './checkout.css'; // Assuming you have a CSS file for styles
import { confirmPayment } from '../../services/payment';

const CheckoutForm = ({ paymentInfor }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isPaid, setPaid] = useState(false);
  const [loading, setLoading] = useState(false);
  // const checkout = useCheckout();

  const handleSubmit = async (event) => {
    if (!stripe || !elements) {
      return;
    }
    setLoading(true);
    event.preventDefault();

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.href, // Add this line
        },
        redirect: 'if_required',
      });
      if (!error) {
        const { data } = await confirmPayment(paymentInfor?.paymentIntentId);
        notification.success({
          message: 'Payment Successful',
          description: 'Your payment has been successfully processed.',
        });
        setPaid(true);
      } else {
        notification.error({
          message: 'Payment Error',
          description: error.message || 'An error occurred during payment.',
        });
      }
      console.log('Error:', error);
      setLoading(false);
    } catch (error) {
      console.error('Payment error:', error);
      notification.error({
        message: 'Payment Error',
        description: error?.message || 'An error occurred during payment.',
      });
      setLoading(false);
      return;
    }
  };
  return isPaid ? (
    <div className="card animate-tada flex items-center justify-center flex-col">
      <div className="header">
        <div className="image">
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              {' '}
              <path
                d="M20 7L9.00004 18L3.99994 13"
                stroke="#000000"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>{' '}
            </g>
          </svg>
        </div>
        <div className="content">
          <span className="title">Thanks for subscribing</span>
          <p className="message">
            Thank you for your purchase. you package was delivered.
          </p>
        </div>
      </div>
    </div>
  ) : (
    <form className="flex items-center flex-col gap-4">
      <PaymentElement className="w-full" />
      <Button
        loading={loading}
        onClick={handleSubmit}
        type="primary"
        className="w-full"
      >
        Subscribe
      </Button>
    </form>
  );
};

export default CheckoutForm;
