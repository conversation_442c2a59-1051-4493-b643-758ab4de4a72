/* eslint-disable react/prop-types */
import { Row, Col, Collapse, Button, Modal, notification, Spin } from 'antd';
import { DeleteOutlined, ExpandAltOutlined } from '@ant-design/icons';

import _map from 'lodash/map';
import _get from 'lodash/get';

import JobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
import React, { useEffect, useState } from 'react';
import JobCardComponent from '../JobListV2/JobCardComponent';
import {
  deletePinnedJobGroupByCompany,
  generateJobSkills,
} from '../../services/search';
import _ from 'lodash';
import { stripJobDescription } from '../../utils/common';

function PinnedJobListGroupByCompany({
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  isSearchLoading,
  isFromSyncSearches = false,
  isFromReportedAgency = false,
}) {
  const [jobGroupedByCompany, setJobGroupedByCompany] = useState([]);
  const [isModalOpen, setModalOpen] = useState(false);
  const [jobToSync, setJobToSync] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [deleteCompanyList, setDeleteCompanyList] = useState([]);

  const [showAll, setShowAll] = useState([]);

  const handleDelete = async (jobIds, isRemoveFromUIOnly = false) => {
    if (isRemoveFromUIOnly) {
      const jobGroupedByCompanyTemp = { ...jobGroupedByCompany };
      jobIdsPayload.forEach((jobDeletedId) => {
        Object.keys(jobGroupedByCompany).forEach((key) => {
          jobGroupedByCompanyTemp[key] = jobGroupedByCompany[key]?.filter(
            (item) => item?.job_id !== jobDeletedId
          );
        });
      });
      setJobGroupedByCompany(jobGroupedByCompanyTemp);
      return;
    }
    setLoading(true);
    const searchId = searchData?.data?.search?.id;
    const jobIdsPayload = _.isArray(jobIds) ? jobIds : [jobIds];
    if (!searchId) return;
    await deletePinnedJobGroupByCompany(searchId, jobIdsPayload)
      .then((res) => {
        const jobGroupedByCompanyTemp = { ...jobGroupedByCompany };
        jobIdsPayload.forEach((jobDeletedId) => {
          Object.keys(jobGroupedByCompany).forEach((key) => {
            jobGroupedByCompanyTemp[key] = jobGroupedByCompany[key]?.filter(
              (item) => item?.job_id !== jobDeletedId
            );
          });
        });
        setJobGroupedByCompany(jobGroupedByCompanyTemp);

        notification.success({
          description: 'Deleted!',
        });
      })
      .catch((err) => {
        notification.error({
          description: 'Please try again later!',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (searchData?.data?.data) {
      const searchDataTemp = [...searchData?.data?.data];
      const groupByCompany = searchDataTemp.reduce(function (r, a) {
        r[a.company] = r[a.company] || [];
        r[a.company].push(a);
        return r;
      }, Object.create(null));
      setJobGroupedByCompany(groupByCompany);
    }
  }, [searchData]);

  //   const handleDeleteData = (id) => {
  //     const index = dataSearch?.findIndex((obj) => obj.job_id === id);
  //     if (index !== -1) {
  //       const newData = dataSearch.filter((_, i) => i !== index);
  //       setDeletedId(true);
  //       setDataSearch(newData);
  //       if(newData?.length == 0) {
  //         handleDeleteCompany(company);
  //       }
  //       if (index === FIRST_INDEX) {
  //         handleDeleteFirstData(id);
  //       }
  //     }
  //   };

  const handleDeleteJobByNames = async (data) => {
    setLoading(true);
    const searchId = searchData?.data?.search?.id;
    const jobs = [];
    data?.forEach((item) => {
      if (jobGroupedByCompany[item]) {
        jobs.push(...jobGroupedByCompany[item]);
      }
    });
    const jobIds = jobs?.map((item) => item.job_id);
    if (!searchId) return;
    await deletePinnedJobGroupByCompany(searchId, jobIds)
      .then((res) => {
        const jobGroupedByCompanyTemp = { ...jobGroupedByCompany };
        jobIds.forEach((jobDeletedId) => {
          Object.keys(jobGroupedByCompany).forEach((key) => {
            jobGroupedByCompanyTemp[key] = jobGroupedByCompany[key]?.filter(
              (item) => item?.job_id !== jobDeletedId
            );
          });
        });
        setJobGroupedByCompany(jobGroupedByCompanyTemp);

        notification.success({
          description: 'Deleted!',
        });
        setDeleteCompanyList((prev) => [...prev, ...data]);
      })
      .catch((err) => {
        notification.error({
          description: 'Please try again later!',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <main className="font-spartan relative text-left w-full">
      {isSearchLoading && (
        <div className="absolute w-full h-full bg-white bg-opacity-50 flex items-center justify-center z-10">
          <Spin />
        </div>
      )}
      {isModalOpen && (
        <JobSubmissionModal
          job={jobToSync}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
          searchData={searchData}
          handleDeleteData={handleDelete}
        />
      )}
      {Object.keys(jobGroupedByCompany)?.map((key, index) => {
        return (
          jobGroupedByCompany[key]?.length > 0 &&
          !deleteCompanyList?.includes(key) && (
            <React.Fragment key={index.toString()}>
              <Col span={24} key={index} className="mt-4">
                <Collapse
                  onChange={(key) => console.log('key: ', key)}
                  defaultActiveKey={['1']}
                  items={[
                    {
                      key: '1',
                      label: (
                        <>
                          <div
                            className="job-pinned"
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                            }}
                          >
                            <div>{key}</div>
                            <div>
                              <Button
                                disabled={isLoading}
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const listJobId = jobGroupedByCompany[
                                    key
                                  ].map(
                                    (job) =>
                                      job?.job_id ||
                                      job?.job_board_id ||
                                      job?.id
                                  );
                                  console.log('listJobId: ', listJobId);
                                  handleDelete(listJobId);
                                  // showModal();
                                  // handleDeleteJobInCompany()
                                }}
                              >
                                <DeleteOutlined />
                              </Button>
                            </div>
                          </div>
                        </>
                      ),
                      children: (
                        <>
                          {jobGroupedByCompany[key]?.length > 0 &&
                            jobGroupedByCompany[key].map((job, index) => {
                              if (
                                showAll.includes(jobGroupedByCompany[key]) ||
                                index === 0
                              ) {
                                return (
                                  <Row key={job?.job_id}>
                                    <JobCardComponent
                                      handleDeleteData={handleDelete}
                                      isPin
                                      userTrackings={_get(
                                        searchData,
                                        'data.data.search.userTrackings.0'
                                      )}
                                      isFromSync={isFromSyncSearches}
                                      isFromReportedAgency={
                                        isFromReportedAgency
                                      }
                                      job={job}
                                      populateSearchData={populateSearchData}
                                      populatePinnedJobsData={
                                        populatePinnedJobsData
                                      }
                                      onClickSync={async (jobToSyncRaw) => {
                                        const jobToSync = {
                                          ...jobToSyncRaw,
                                          description: stripJobDescription(
                                            jobToSyncRaw?.description
                                          ),
                                        };
                                        if (
                                          !jobToSync?.skills ||
                                          jobToSync?.skills?.length === 0
                                        ) {
                                          setJobToSync(jobToSync);
                                          setModalOpen(true);
                                          const { data } =
                                            await generateJobSkills(
                                              jobToSync?.job_id
                                            );
                                          const jobToSyncWithSkills = {
                                            ...jobToSync,
                                            skills: data?.result || [],
                                          };
                                          setJobToSync(jobToSyncWithSkills);
                                        } else {
                                          setJobToSync(jobToSync);
                                          setModalOpen(true);
                                        }
                                      }}
                                      keywords={_get(
                                        searchData,
                                        'data.search.keywords'
                                      )}
                                      dataSearch={_get(
                                        searchData,
                                        'data.search'
                                      )}
                                      handleDeleteDataByCompanyName={(e) =>
                                        handleDeleteJobByNames(e)
                                      }
                                      handleDeleteDataByCompanyNames={(e) =>
                                        handleDeleteJobByNames(e)
                                      }
                                    />
                                  </Row>
                                );
                              }
                              // else {
                              //   if (index === 0) {
                              //     return (
                              //       <>
                              //         <Row key={job?.job_id}>
                              //           <JobCardComponent
                              //             handleDeleteData={handleDelete}
                              //             isPin
                              //             userTrackings={_get(
                              //               searchData,
                              //               'data.data.search.userTrackings.0'
                              //             )}
                              //             isFromSync={isFromSyncSearches}
                              //             isFromReportedAgency={
                              //               isFromReportedAgency
                              //             }
                              //             job={job}
                              //             populateSearchData={
                              //               populateSearchData
                              //             }
                              //             populatePinnedJobsData={
                              //               populatePinnedJobsData
                              //             }
                              //             onClickSync={(jobToSync) => {
                              //               setJobToSync(jobToSync);
                              //               setModalOpen(true);
                              //             }}
                              //             keywords={_get(
                              //               searchData,
                              //               'data.search.keywords'
                              //             )}
                              //             dataSearch={_get(
                              //               searchData,
                              //               'data.search'
                              //             )}
                              //           />
                              //         </Row>
                              //       </>
                              //     );
                              //   }
                              // }
                            })}

                          {jobGroupedByCompany[key]?.length > 1 &&
                            !showAll.includes(jobGroupedByCompany[key]) && (
                              <Col
                                span={24}
                                style={{
                                  display: 'flex',
                                  justifyContent: 'center',
                                }}
                              >
                                <Button
                                  type="primary"
                                  className="text-black rounded-md px-3 py-1 bg-white flex items-center"
                                  onClick={() => {
                                    setShowAll([
                                      ...showAll,
                                      jobGroupedByCompany[key],
                                    ]);
                                  }}
                                >
                                  <ExpandAltOutlined />
                                  See more
                                </Button>
                              </Col>
                            )}
                        </>
                      ),
                    },
                  ]}
                />
              </Col>
              {/* <Modal title="Basic Modal" open={isModalOpen} onOk={handleOk} onCancel={handleCancel} footer={false}>
                <p>
                  Please make sure you want to delete jobs belonging to this company
                </p>
                <div style={{marginTop: "30px", display: "flex", justifyContent: "space-between"}}>
                  <div></div>
                  <div>
                    <Button type="default" onClick={handleCancel}>Cancel</Button>
                    <Button style={{marginLeft: "20px"}} loading={isLoading} disabled={isLoading} onClick={handleOk} type="primary">Confirm</Button>
                  </div>
                </div>
              </Modal> */}
            </React.Fragment>
          )
        );
      })}
    </main>
  );
}

export default PinnedJobListGroupByCompany;
