/* eslint-disable react/prop-types */
import { Row, Spin } from 'antd';

import _map from 'lodash/map';
import _get from 'lodash/get';

import JobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
import React, { useState } from 'react';
import JobCardComponent from '../JobListV2/JobCardComponent';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import ModalWarningSequence from '../JobListV2/ModalWarningSequence';
import { generateJobSkills } from '../../services/search';
import { stripJobDescription } from '../../utils/common';

function PinnedJobList({
  searchData,
  populateSearchData,
  populatePinnedJobsData,
  isFromSyncSearches = false,
  isFromReportedAgency = false,
}) {
  const [isModalOpen, setModalOpen] = useState(false);
  const [jobToSync, setJobToSync] = useState(null);
  const [dataRemoveCompany, setDataRemoveCompany] = useState([]);
  const [dataRemoveJobById, setDataRemoveJobById] = useState([]);
  const [openWarningModal, setOpenWarningModal] = useState(false);
  const { setAuth, profile: profileUserAuth } = useAuth();
  const { profileUser, setViewAs } = useViewAs();
  const [actionKey, setActionKey] = useState(null);

  const userToSet = profileUser || profileUserAuth;

  return (
    <main className="font-spartan relative text-left">
      {isModalOpen && (
        <JobSubmissionModal
          job={jobToSync}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
          searchData={searchData}
          handleDeleteData={(job) => {
            setDataRemoveJobById((prev) => [...prev, job]);
          }}
          actionKey={actionKey}
        />
      )}
      <section className="bg-lightCyan h-full pt-5 relative">
        {searchData?.loading && (
          <div className="absolute w-full h-full bg-white bg-opacity-50 flex items-center justify-center z-10">
            <Spin />
          </div>
        )}
        {searchData?.data?.data
          ?.filter((item) => !dataRemoveCompany.includes(item?.company))
          .filter((item) => !dataRemoveJobById.includes(item?.job_id))
          .map((job) => (
            <Row key={job?.job_id}>
              <JobCardComponent
                isPin
                userTrackings={_get(
                  searchData,
                  'data.data.search.userTrackings.0'
                )}
                isFromSync={isFromSyncSearches}
                isFromReportedAgency={isFromReportedAgency}
                job={job}
                populateSearchData={populateSearchData}
                populatePinnedJobsData={populatePinnedJobsData}
                onClickSync={async (jobToSyncRaw, key) => {
                  const jobToSync = {
                    ...jobToSyncRaw,
                    description: stripJobDescription(jobToSyncRaw?.description),
                  };
                  setJobToSync(jobToSync);
                  setActionKey(key);
                  if (!jobToSync?.skills || jobToSync?.skills?.length === 0) {
                    setModalOpen(true);
                    const { data } = await generateJobSkills(jobToSync?.job_id);
                    const jobToSyncWithSkills = {
                      ...jobToSync,
                      skills: data?.result || [],
                    };
                    setJobToSync(jobToSyncWithSkills);
                  } else {
                    setJobToSync(jobToSync);
                    setModalOpen(true);
                  }
                }}
                keywords={_get(searchData, 'data.search.keywords')}
                dataSearch={_get(searchData, 'data.search')}
                handleDeleteData={(job) => {
                  console.log("job", job)
                  setDataRemoveJobById((prev) => [...prev, ...[job]]);
                }}
                handleDeleteDataByCompanyName={(data) => {
                  setDataRemoveCompany((prev) => [...prev, ...data]);
                }}
                handleDeleteDataByCompanyNames={(data) => {
                  setDataRemoveCompany((prev) => [...prev, ...data]);
                }}
              />
            </Row>
          ))}
      </section>
      {/* <ModalWarningSequence setOpenWarningModal={setOpenWarningModal} openWarningModal={openWarningModal}/> */}
    </main>
  );
}

export default PinnedJobList;
