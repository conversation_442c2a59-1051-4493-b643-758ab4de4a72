/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';

import * as LabelPrimitive from '@radix-ui/react-label';
import * as SeparatorPrimitive from '@radix-ui/react-separator';
import {
  Briefcase,
  Calendar,
  FileX,
  FolderSyncIcon,
  PoundSterling,
  RefreshCw,
  Search,
  SlidersHorizontal,
  Sparkles,
  Users2,
} from 'lucide-react';

import _get from 'lodash/get';

import {
  AutoComplete,
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
  DatePicker,
  Switch,
  Spin,
  notification,
  Dropdown,
  Radio,
  Space,
  InputNumber,
  Checkbox,
  Slider,
} from 'antd';
import { Controller } from 'react-hook-form';
import './style.scss';

import { jobBoards } from '../../variables/selectOption';
import useSearchWithDebounce from '../../hooks/useSearchWithDebounce';
import { useSelector, useDispatch } from 'react-redux';

import { saveIsGroupCompany, selectIsGroupCompany } from '../../store/common';
import { useUpdateStatusGroupJobMutation } from '../../app/jobSearch';
import { useParams } from 'react-router-dom';
import {
  ClearOutlined,
  CloseOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { moneyParse, numberFormat, numberWithCommas } from '../../utils/common';
import { getCountries, getCounties } from '../../services/bullhorn';

const { RangePicker } = DatePicker;

const technicalSkills = [
  'JavaScript',
  'React',
  'Node.js',
  'Python',
  'Java',
  'HTML',
  'CSS',
  'SQL',
  'Angular',
  'Vue.js',
  'C#',
  'Ruby',
  'PHP',
  'Swift',
  'Flutter',
  'Django',
  'Spring Boot',
  'Express.js',
  'MongoDB',
  'MySQL',
  'PostgreSQL',
  'AWS',
  'Azure',
  'Git',
  'Docker',
  'Kubernetes',
  'Machine Learning',
  'Artificial Intelligence',
  'Blockchain',
  'DevOps',
  'CI/CD',
  'RESTful API',
  'GraphQL',
  'Redux',
  'Webpack',
  'TypeScript',
  'TensorFlow',
  'PyTorch',
  'Kotlin',
  'Rust',
  'Go',
];

const jobTypes = ['Full Time', 'Part Time', 'Contract'];

export const tagRender = (props, closable = true) => {
  const { label, onClose, tagColor } = props;
  const onPreventMouseDown = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    label && (
      <div
        className={`flex justify-center items-center gap-2 tag font-Montserrat ${tagColor || 'tag-normal'}`}
      >
        <span className="mr-1">{label}</span>
        {closable && (
          <CloseOutlined
            onClick={onClose}
            onMouseDown={onPreventMouseDown}
            className="text-[10px] hover:cursor-pointer"
          />
        )}
      </div>
    )
  );
};

const NewSearchFilterComponent = ({
  isEdit = false,
  control,
  onFinish,
  onToggleSync,
  toggleStatus,
  setValue,
  handleSubmit,
  setSentJobStatus,
  sentJobStatus,
  handleFormSubmit,
  reset,
  search,
  showRecentJobs,
  recentJobs = [],
  isRefineBySearchLoading,
  isRefineBySearch,
  watch,
  handleUpdateRefineBySearch = () => {},
}) => {
  const dispatch = useDispatch();
  const { searchId } = useParams();
  const isGroupCompany = useSelector(selectIsGroupCompany);
  const [updateStatusGroupJob, { isLoading }] =
    useUpdateStatusGroupJobMutation();

  const [keywordsOptions, setKeyWordsOptions] = useState(technicalSkills);
  const [isChecked, setIsChecked] = useState(false);
  const [searchCountryCode, setSearchCountryCode] = useState('');
  const [loadingCity, setLoadingCity] = useState(false);

  const handleToggle = () => {
    onToggleSync(isChecked ? 'OFF' : 'ON');
    setIsChecked(!isChecked);
  };

  // const {
  //   searchOptions: addressOptions,
  //   setSearchText: setAddressSearchText,
  //   isLoading: isLoadingAddresses,
  // } = useSearchWithDebounce(sea);

  // const handleUserInput = async (inputValue) => {
  //   if (!inputValue || inputValue === '') return;
  //   setAddressSearchText(inputValue);
  // };

  const onRangeChange = (dates) => {
    setValue('postedWithin', dates);
    setValue('datePostedText', 'Date posted');
  };

  const [listCities, setListCities] = useState([]);

  const handleUpdateStatusGroupJob = async () => {
    try {
      const convertStatus = isGroupCompany ? 'OFF' : 'ON';

      const payload = {
        searchId: searchId,
        status: convertStatus,
      };

      const response = await updateStatusGroupJob(payload);

      if (_get(response, 'data.success')) {
        await dispatch(
          saveIsGroupCompany(_get(response, 'data.result.isGroupByCompany'))
        );

        await notification.open({
          message: 'Success!',
          description: 'Toggled "Group Job by Company" status successfully!',
        });
      } else {
        notification.error({
          message: 'Error!',
          description: 'Toggled "Group Job by Company" status failed!',
        });
      }
    } catch {
      notification.error({
        message: 'Error!',
        description: 'Toggled "Group Job by Company" status failed!',
      });
    }
  };

  useEffect(() => {
    setIsChecked(toggleStatus);
  }, [toggleStatus]);

  useEffect(() => {
    handleGetLocation();
  }, [search?.data?.data?.result?.search?.country]);

  const handleGetLocation = async () => {
    if (!search?.data?.data?.result?.search?.country) return;
    const { data } = await getCountries(
      search?.data?.data?.result?.search?.country
    );

    if (data?.result?.length > 0) {
      await handleGetListCitiesStateFromBH('', data?.result[0]?.value);
      setSearchCountryCode(data?.result[0]?.value);
    } else {
      setSearchCountryCode(2359);
      await handleGetListCitiesStateFromBH('', 2359);
    }
  };

  const onChangeRadio = (value) => {
    if (value === sentJobStatus) {
      setSentJobStatus(null);
      setValue('isIncludeSentJob', null);
      return;
    }
    setSentJobStatus(value);
    setValue('isIncludeSentJob', value);
  };

  const handleGetListCitiesStateFromBH = async (filter = '', countryId) => {
    setLoadingCity(true);
    try {
      setListCities([]);
      const { data } = await getCounties({ filter, countryId });

      setListCities(data?.result);
      setLoadingCity(false);
    } catch (err) {
      notification.error({ message: err?.response?.data?.message });
      setLoadingCity(false);
    }
  };

  const handleInputCity = async (inputValue, countryId) => {
    await handleGetListCitiesStateFromBH(inputValue, countryId);
  };

  return (
    <Form
      layout="vertical"
      className="relative sync-search-filter-container"
      onFinish={onFinish}
    >
      <div className="w-[21rem] bg-white border border-gray-200 flex flex-col">
        {/* Filter Header - Reduced Size */}
        <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-cyan-50 to-purple-50">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="p-1.5 bg-gradient-to-br from-cyan-500 to-purple-600 rounded-lg shadow-lg">
                <SlidersHorizontal className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-base font-semibold text-gray-900">
                  Smart Filters
                </h2>
                <p className="text-xs text-gray-600">Refine your job search</p>
              </div>
            </div>
          </div>

          {/* Quick Stats - Reduced Size */}
          <div className="flex justify-center">
            <div
              className="bg-white/80 rounded-lg p-2 border border-cyan-200/50 min-w-[120px] text-center cursor-pointer hover:scale-105 transition-transform duration-200 ease-in-out shadow-sm"
              onClick={() => {
                showRecentJobs();
              }}
            >
              <div className="text-xl font-bold text-cyan-600">
                {recentJobs?.length || 0}
              </div>
              <div className="text-xs text-gray-600">Recently Added Jobs</div>
            </div>
          </div>
        </div>

        {/* Filter Content - Fixed Height with Auto Scroll */}
        <div className="flex-1 p-6 space-y-6 max-h-[75vh] overflow-y-auto">
          {/* AI-Powered Search Toggle with AI Gradient Colors */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-cyan-600" />
              <LabelPrimitive.Root
                htmlFor="ai-search"
                className="text-sm font-medium text-gray-900"
              >
                AI-Powered Search
              </LabelPrimitive.Root>
            </div>
            <div className="relative">
              <Switch
                className="ai-switch"
                loading={isRefineBySearchLoading}
                checked={isRefineBySearch}
                onChange={(event) => {
                  handleUpdateRefineBySearch(event);
                }}
              />
            </div>
          </div>

          {/* Job Information Section */}
          <div>
            <h2 className="font-semibold text-gray-900 mb-3 flex items-center text-lg">
              <Briefcase className="w-4 h-4 mr-2 text-cyan-600 " />
              Job Information
            </h2>
            <p className="text-xs text-gray-700 mb-3">
              Based on Keywords, Job Titles and Title fields.
            </p>

            <div className="space-y-3">
              <Form.Item name="keywords">
                <Controller
                  name="keywords"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="keywords"
                        className="text-sm text-gray-800"
                      >
                        Keywords
                      </LabelPrimitive.Root>
                      <Select
                        id="keywords"
                        // mode="tags"
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-orange' })
                        }
                        prefix
                        placeholder="Keywords"
                        className="custom-filter"
                        labelInValue
                        mode="multiple"
                        value={field.value}
                        ref={field.ref}
                        onBlur={field.onBlur}
                        onSearch={(inputValue) => {
                          if (inputValue) {
                            setKeyWordsOptions([
                              inputValue,
                              ...technicalSkills,
                            ]);
                          } else {
                            setKeyWordsOptions([...technicalSkills]);
                          }
                        }}
                        options={keywordsOptions.map((val) => ({
                          label: val,
                          value: val,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) !== -1
                        }
                        onChange={(selectedOptions) => {
                          field.onChange(selectedOptions.map((a) => a.value));
                        }}
                      />
                    </>
                  )}
                />
              </Form.Item>
              <Form.Item name="jobTitles">
                <Controller
                  name="jobTitles"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="job-titles"
                        className="text-sm text-gray-800"
                      >
                        Job Titles
                      </LabelPrimitive.Root>
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-blue' })
                        }
                        placeholder="Job Titles"
                        className="custom-filter"
                        mode="tags"
                        onSelect={() => onFinish()}
                        dropdownRender={(menu) => (
                          <div style={{ display: 'none' }}>{menu}</div>
                        )}
                        dropdownStyle={{ display: 'none' }}
                        {...field}
                      />
                    </>
                  )}
                />
              </Form.Item>

              <Form.Item name="location">
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="location-input"
                        className="text-sm text-gray-800"
                      >
                        Location
                      </LabelPrimitive.Root>
                      <Select
                        tagRender={(props) => tagRender({ ...props })}
                        disabled={listCities?.length == 0 || !listCities}
                        {...field}
                        className="search-input"
                        mode="multiple"
                        options={listCities
                          .filter(
                            (item, index, self) =>
                              self.findIndex((t) => t.value === item.value) ===
                              index
                          )
                          .map((ao) => ({
                            label: ao.label,
                            value: ao.value,
                          }))}
                        placeholder="Location"
                        allowClear
                        showSearch
                        optionFilterProp="label"
                        {...field}
                        loading={loadingCity}
                        onClear={() => {
                          setValue('location', '');
                        }}
                      />
                    </>
                  )}
                />
              </Form.Item>
            </div>
          </div>

          <SeparatorPrimitive.Root className="shrink-0 bg-border h-[1px] w-full border-b" />

          {/* Exclude Section */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h2 className="font-semibold text-gray-900 flex items-center">
                <FileX className="w-4 h-4 mr-2 text-red-600" />
                Exclude
              </h2>
              <div className="flex items-center space-x-2">
                <Form.Item className="!mb-0" name="isSavedExclude">
                  <Controller
                    name="isSavedExclude"
                    control={control}
                    render={({ field }) => (
                      <>
                        <Switch
                          defaultChecked={true}
                          {...field}
                          title="Saved Exclude"
                          className="data-[state=checked]:bg-cyan-500 mr-2"
                          id="sync-toggle"
                        ></Switch>
                        <span className="text-xs font-medium text-gray-800">
                          Saved Exclude
                        </span>
                      </>
                    )}
                  />
                </Form.Item>
              </div>
            </div>

            <div className="space-y-3">
              <Form.Item name="excludeCompanies">
                <Controller
                  name="excludeCompanies"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="exclude-companies"
                        className="text-sm text-gray-800"
                      >
                        Exclude Companies
                      </LabelPrimitive.Root>
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-orange' })
                        }
                        placeholder="Exclude Companies"
                        className="custom-filter"
                        mode="tags"
                        {...field}
                      />
                    </>
                  )}
                />
              </Form.Item>
              <Form.Item name="excludeKeywords">
                <Controller
                  name="excludeKeywords"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="exclude-keywords"
                        className="text-sm text-gray-800"
                      >
                        Exclude Keywords
                      </LabelPrimitive.Root>
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-orange' })
                        }
                        placeholder="Exclude Keywords"
                        className="custom-filter"
                        mode="tags"
                        {...field}
                      />
                    </>
                  )}
                />
              </Form.Item>

              <Form.Item name="excludeTitles">
                <Controller
                  name="excludeTitles"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <>
                      <LabelPrimitive.Root
                        htmlFor="exclude-titles"
                        className="text-sm text-gray-800"
                      >
                        Exclude Title
                      </LabelPrimitive.Root>
                      <Select
                        tagRender={(props) =>
                          tagRender({ ...props, tagColor: 'tag-orange' })
                        }
                        placeholder="Exclude Job Titles"
                        className="custom-filter"
                        mode="tags"
                        {...field}
                      />
                    </>
                  )}
                />
              </Form.Item>

              <Radio
                checked={sentJobStatus === 'NOT_SENT'}
                onClick={() => onChangeRadio('NOT_SENT')}
                value={'NOT_SENT'}
              >
                Exclude Jobs Sent To Bullhorn
              </Radio>
            </div>
          </div>

          <SeparatorPrimitive.Root className="shrink-0 bg-border h-[1px] w-full border-b" />

          {/* Job Type */}
          {/* <div>
            <h2 className="font-semibold text-gray-900 mb-3 flex items-center text-lg">
              <Briefcase className="w-4 h-4 mr-2 text-gray-600" />
              Job Type
            </h2>
            <div className="space-y-2">
              {jobTypes.map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={type}
                    // checked={selectedJobTypes.includes(type)}
                    // onChange={() => toggleJobType(type)}
                    className="rounded border-gray-300 text-cyan-600 focus:ring-cyan-500"
                  />
                  <label
                    htmlFor={type}
                    className="text-sm text-gray-800 cursor-pointer"
                  >
                    {type}
                  </label>
                </div>
              ))}
            </div>
          </div> */}

          {/* Salary Range with Manual Input */}
          <div>
            <h2 className="font-semibold text-gray-900 mb-3 flex items-center text-lg">
              <PoundSterling className="w-4 h-4 mr-2 text-gray-600" />
              Salary Range
            </h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Controller
                  name="minSalary"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <LabelPrimitive.Root
                        htmlFor="minimum"
                        className="text-sm text-gray-800"
                      >
                        Minimum
                      </LabelPrimitive.Root>
                      <InputNumber
                        id="minimum"
                        formatter={numberWithCommas}
                        prefix="£"
                        min={0}
                        {...field}
                        className="search-input w-full py-1 px-2"
                        placeholder="Min Salary"
                      />
                    </div>
                  )}
                />
                <Controller
                  name="maxSalary"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <LabelPrimitive.Root
                        htmlFor="maximum"
                        className="text-xs text-gray-700 mb-1 block"
                      >
                        Maximum
                      </LabelPrimitive.Root>
                      <InputNumber
                        id="maximum"
                        formatter={numberWithCommas}
                        min={0}
                        prefix="£"
                        {...field}
                        className="search-input w-full py-1 px-2"
                        placeholder="Max Salary"
                      />
                    </div>
                  )}
                />
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-xs text-gray-700 mb-1 block">
                    Minimum: £{watch('minSalary')?.toLocaleString()}
                  </label>
                  <Slider
                    value={watch('minSalary')}
                    onChange={(value) => {
                      setValue('minSalary', value);
                    }}
                    max={99999}
                    min={0}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-700 mb-1 block">
                    Maximum: £{watch('maxSalary')?.toLocaleString()}
                  </label>
                  <Slider
                    value={watch('maxSalary')}
                    onChange={(value) => {
                      setValue('maxSalary', value);
                    }}
                    max={99999}
                    min={0}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Date Posted */}
          {/* <div>
            <h2 className="font-semibold text-gray-900 mb-3 flex items-center text-lg">
              <Calendar className="w-4 h-4 mr-2 text-gray-600" />
              Date Posted
            </h2>
          </div> */}

          {/* System Toggles with Teal Color */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FolderSyncIcon className="w-4 h-4 text-cyan-600" />
                <LabelPrimitive.Root
                  htmlFor="sync-toggle"
                  className="text-sm font-medium text-gray-900"
                >
                  Sync
                </LabelPrimitive.Root>
              </div>
              <Switch
                checked={isChecked}
                onChange={handleToggle}
                defaultChecked={isChecked}
                className="data-[state=checked]:bg-cyan-500"
                id="sync-toggle"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users2 className="w-4 h-4 text-purple-600" />
                <LabelPrimitive.Root
                  htmlFor="group-jobs-toggle"
                  className="text-sm font-medium text-gray-900"
                >
                  Group Jobs by Company
                </LabelPrimitive.Root>
              </div>
              <Switch
                // disabled={isLoading}
                loading={isLoading}
                checked={isGroupCompany}
                onChange={(event) => {
                  handleUpdateStatusGroupJob(event);
                }}
                className="data-[state=checked]:bg-cyan-500"
              />
            </div>
          </div>
        </div>

        {/* Filter Actions */}
        <div className="p-6 border-t border-gray-100 bg-gray-50">
          <div className="flex justify-between items-center w-full gap-2">
            <Button
              variant="outline"
              onClick={() => onFinish('CLEAR')}
              className="flex items-center justify-center gap-2 w-full"
            >
              <RefreshCw className="w-4 h-4" />
              Clear Filter
            </Button>
            <Button
              htmlType="submit"
              type="primary"
              className="flex items-center justify-center gap-2 w-full"
            >
              <Search className="w-4 h-4" />
              Submit
            </Button>
          </div>
        </div>
      </div>
      {/* <Row className="max-h-[22rem] overflow-hidden hover:overflow-y-auto">
          <div className="pt-3 px-6 mb-3 border-b">
            <div className="flex flex-col pb-3">
              <span className="font-semibold">Salary Range (per Month)</span>
              <span className="text-xs text-[#afb2b6]">
                Month prices before taxes and fees.
              </span>
            </div>
            <Form.Item name="salary">
              <Row gutter={8}>
                <Col span={12}>
                  <Controller
                    name="minSalary"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        formatter={numberWithCommas}
                        addonBefore="£"
                        {...field}
                        className="search-input"
                        placeholder="Min Salary"
                      />
                    )}
                  />
                </Col>
                <Col span={12}>
                  <Controller
                    name="maxSalary"
                    control={control}
                    render={({ field }) => (
                      <InputNumber
                        formatter={numberWithCommas}
                        addonBefore="£"
                        {...field}
                        className="search-input"
                        placeholder="Max Salary"
                      />
                    )}
                  />
                </Col>
              </Row>
            </Form.Item>
          </div>
          <div className="pt-3 px-6 mb-3 border-b">
            <div className="flex flex-col pb-3">
              <span className="font-semibold">Job Posted Date</span>
              <span className="text-xs text-[#afb2b6]">
                Jobs searched in the range of date.
              </span>
            </div>
            <Col span={isEdit ? 24 : 12}>
              <Form.Item name="postedWithin">
                <Controller
                  name="postedWithin"
                  control={control}
                  render={({ field }) => (
                    <RangePicker
                      className="rounded-md"
                      {...field}
                      onChange={onRangeChange}
                    />
                  )}
                />
              </Form.Item>
            </Col>
          </div>
          <div className="pt-3 px-6 mb-3 border-b w-full">
            <div className="flex flex-col pb-3">
              <span className="font-semibold">Job Information</span>
              <span className="text-xs text-[#afb2b6]">
                Based on Keywords, Boards and Title fields.
              </span>
            </div>
            <Col span={isEdit ? 24 : 12}>
              <Form.Item name="keywords">
                <Controller
                  name="keywords"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <Select
                      // mode="tags"
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-orange' })
                      }
                      prefix
                      placeholder="Keywords"
                      className="custom-filter"
                      labelInValue
                      mode="multiple"
                      value={field.value}
                      ref={field.ref}
                      onBlur={field.onBlur}
                      onSearch={(inputValue) => {
                        if (inputValue) {
                          setKeyWordsOptions([inputValue, ...technicalSkills]);
                        } else {
                          setKeyWordsOptions([...technicalSkills]);
                        }
                      }}
                      options={keywordsOptions.map((val) => ({
                        label: val,
                        value: val,
                      }))}
                      filterOption={(inputValue, option) =>
                        option.label
                          .toLowerCase()
                          .indexOf(inputValue.toLowerCase()) !== -1
                      }
                      onChange={(selectedOptions) => {
                        field.onChange(selectedOptions.map((a) => a.value));
                      }}
                    />
                  )}
                />
              </Form.Item>            

              <Form.Item name="keywords">
                <Controller
                  name="jobTitles"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <Select
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-blue' })
                      }
                      placeholder="Job Titles"
                      className="custom-filter"
                      mode="tags"
                      onSelect={() => onFinish()}
                      dropdownRender={(menu) => (
                        <div style={{ display: 'none' }}>{menu}</div>
                      )}
                      dropdownStyle={{ display: 'none' }}
                      {...field}
                    />
                  )}
                />
              </Form.Item>
              
              <Form.Item name="location">
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Select
                      tagRender={(props) => tagRender({ ...props })}
                      disabled={listCities?.length == 0 || !listCities}
                      {...field}
                      className="search-input"
                      mode="multiple"
                      options={listCities
                        .filter(
                          (item, index, self) =>
                            self.findIndex((t) => t.value === item.value) ===
                            index
                        )
                        .map((ao) => ({
                          label: ao.label,
                          value: ao.value,
                        }))}
                      placeholder="Location"
                      allowClear
                      showSearch
                      optionFilterProp="label"
                      {...field}
                      loading={loadingCity}
                      onClear={() => {
                        setValue('location', '');
                      }}
                    />
                    
                  )}
                />
              </Form.Item>
            </Col>
          </div>
          <div className="pt-3 px-6 pb-3 border-b w-full">
            <div className="flex flex-col pb-3">
              <div className="flex items-center justify-between">
                <span className="font-semibold">Exclude</span>
                <Form.Item className="!mb-0" name="isSavedExclude">
                  <Controller
                    name="isSavedExclude"
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        defaultChecked={true}
                        {...field}
                        title="Saved Exclude"
                      >
                        <span className="text-xs font-medium">
                          Saved Exclude
                        </span>
                      </Checkbox>
                    )}
                  />
                </Form.Item>
              </div>
            </div>

            <div className="w-full flex flex-col">
              <Form.Item name="excludeCompanies">
                <Controller
                  name="excludeCompanies"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <Select
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-orange' })
                      }
                      placeholder="Exclude Companies"
                      className="custom-filter"
                      mode="tags"
                      {...field}
                    />
                  )}
                />
              </Form.Item>
              <Form.Item name="excludeKeywords">
                <Controller
                  name="excludeKeywords"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <Select
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-orange' })
                      }
                      placeholder="Exclude Keywords"
                      className="custom-filter"
                      mode="tags"
                      {...field}
                    />
                  )}
                />
              </Form.Item>
              <Form.Item name="excludeTitles">
                <Controller
                  name="excludeTitles"
                  style={{ borderRadius: '2rem' }}
                  control={control}
                  render={({ field }) => (
                    <Select
                      tagRender={(props) =>
                        tagRender({ ...props, tagColor: 'tag-orange' })
                      }
                      placeholder="Exclude Job Titles"
                      className="custom-filter"
                      mode="tags"
                      {...field}
                    />
                  )}
                />
              </Form.Item>
              <Radio
                checked={sentJobStatus === 'NOT_SENT'}
                onClick={() => onChangeRadio('NOT_SENT')}
                value={'NOT_SENT'}
              >
                Exclude Jobs Sent To Bullhorn
              </Radio>
            </div>
          </div>
        </Row>
        {sentJobStatus && (
          <div
            style={{
              position: 'absolute',
              zIndex: '100',
              right: '0',
              marginTop: '-47px',
              width: '40px',
              height: '40px',
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onClick={() => {
              setSentJobStatus(null);
              setValue('includeSentJob', null);
              handleSubmit((data) =>
                handleFormSubmit({ ...data, includeSentJob: null })
              )();
            }}
          >
            <CloseOutlined
              style={{ color: '#fff', fontSize: '20px', marginTop: '-10px' }}
            />
          </div>
        )}
        <div className="pt-3 px-6 mb-3 w-full">
          <Form.Item>
            <Button
              type="default"
              htmlType="submit"
              className="bg-white"
              style={{ width: '100%' }}
            >
              Submit
            </Button>
          </Form.Item>
        </div> */}
    </Form>
  );
};

export default NewSearchFilterComponent;
