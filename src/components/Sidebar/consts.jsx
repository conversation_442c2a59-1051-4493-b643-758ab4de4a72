import React from 'react';
import {
  FEATURES,
  SALES,
  SUPER_ADMIN,
  userRole,
} from '../../constants/common.constant';

// Icon Imports
import {
  Calendar,
  Download,
  FileText,
  Home,
  LogOut,
  Mail as LucideMailIcon,
  Search,
  Settings,
  TrendingUp,
  Users as LucideUsers,
  Target,
  Database,
  UserPlus,
  Building2,
  Bell,
  ChevronDown,
  MousePointer,
  Eye,
  Reply,
  UserCheck,
  MessageSquare,
  Grid3X3,
  Zap,
  BarChart,
  Lightbulb,
  Briefcase as LucideBriefcase,
  UserSearch,
  TestTube,
  HeadphonesIcon,
  Sparkles,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
} from 'lucide-react';
import PropTypes from 'prop-types';
import {
  ApartmentOutlined,
  AppstoreAddOutlined,
  CreditCardOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';

ZileoLogo.propTypes = {
  className: PropTypes.string,
};

function ZileoLogo({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="14"
      viewBox="0 0 20 14"
      fill="none"
      className={className}
    >
      <path
        d="M9.28571 5.74359L0 0L14.1071 5.02564L10.7143 8.07692L20 14L5.89286 8.79487L9.28571 5.74359Z"
        fill="currentColor"
      />
    </svg>
  );
}

Parcel.propTypes = {
  className: PropTypes.string,
};

function Parcel({ className }) {
  return (
    <svg
      //
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.4"
        d="M19.5,18c-1.38,0-2.5-1.12-2.5-2.5s1.12-2.5,2.5-2.5,2.5,1.12,2.5,2.5-1.12,2.5-2.5,2.5Zm0-4c-.83,0-1.5,.67-1.5,1.5s.67,1.5,1.5,1.5,1.5-.67,1.5-1.5-.67-1.5-1.5-1.5Zm4.12,9.98c.27-.07,.43-.34,.36-.61-.5-1.96-2.39-3.38-4.48-3.38s-3.98,1.42-4.48,3.38c-.07,.27,.09,.54,.36,.61,.27,.07,.54-.09,.61-.36,.38-1.5,1.9-2.62,3.52-2.62s3.13,1.13,3.52,2.62c.06,.23,.26,.38,.48,.38,.04,0,.08,0,.12-.02Zm-10.62-.48c0-.28-.22-.5-.5-.5H4.5c-1.93,0-3.5-1.57-3.5-3.5V4.5c0-1.93,1.57-3.5,3.5-3.5h15c1.93,0,3.5,1.57,3.5,3.5V12.5c0,.28,.22,.5,.5,.5s.5-.22,.5-.5V4.5c0-2.48-2.02-4.5-4.5-4.5H4.5C2.02,0,0,2.02,0,4.5v15c0,2.48,2.02,4.5,4.5,4.5H12.5c.28,0,.5-.22,.5-.5Zm2.73-4.42c.24-.13,.34-.43,.21-.68-.13-.25-.43-.34-.68-.21-1,.53-2.13,.81-3.27,.81-3.86,0-7-3.14-7-7s3.14-7,7-7c3.32,0,6.2,2.35,6.86,5.6,.05,.27,.32,.44,.59,.39,.27-.06,.45-.32,.39-.59-.75-3.71-4.05-6.4-7.84-6.4-4.41,0-8,3.59-8,8s3.59,8,8,8c1.3,0,2.59-.32,3.73-.92Zm-3.73-6.08c.55,0,1-.45,1-1s-.45-1-1-1-1,.45-1,1,.45,1,1,1Z"
      />
    </svg>

    // <svg
    //   xmlns="http://www.w3.org/2000/svg"
    //   fill="none"
    //   viewBox="0 0 20 20"
    //   className={className}
    // >
    //   <g stroke="currentColor" strokeWidth="1.5" clipPath="url(#a)">
    //     <path
    //       strokeLinejoin="round"
    //       d="M18.333 14.334V7.237a4 4 0 0 0-.876-2.499L16.2 3.168a4 4 0 0 0-3.123-1.501H6.923a4 4 0 0 0-3.124 1.501l-1.256 1.57a4 4 0 0 0-.876 2.499v7.097a4 4 0 0 0 4 4h8.666a4 4 0 0 0 4-4Z"
    //     />
    //     <path strokeLinecap="round" strokeLinejoin="round" d="M2.5 5.833h15" />
    //     <path
    //       strokeLinecap="round"
    //       d="m9.167 11.322-1.078-1.078a.833.833 0 0 0-1.178 0l-1.078 1.078M7.5 14.167v-3.679M14.167 12.845l-1.078 1.078a.833.833 0 0 1-1.178 0l-1.078-1.078M12.5 10v3.679"
    //     />
    //   </g>
    //   <defs>
    //     <clipPath id="a">
    //       <path fill="#fff" d="M0 0h20v20H0z" />
    //     </clipPath>
    //   </defs>
    // </svg>
  );
}

ChartArrowUp.propTypes = {
  className: PropTypes.string,
};

function ChartArrowUp({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <g clipPath="url(#a)">
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="M15.833 1.667H17.5c.46 0 .833.373.833.833v1.667M17.5 2.5C10.882 7.826 7.452 9.261 1.667 10M15 8.334v8.333a1.667 1.667 0 1 0 3.333 0V8.334a1.667 1.667 0 0 0-3.333 0ZM1.667 15v1.667a1.667 1.667 0 1 0 3.333 0V15a1.667 1.667 0 0 0-3.333 0Zm6.666-3.333v5a1.667 1.667 0 1 0 3.334 0v-5a1.667 1.667 0 1 0-3.334 0Z"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="currentColor" d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

IdCard.propTypes = {
  className: PropTypes.string,
};

function IdCard({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M15 14.167h-2.5m2.5-2.5h-2.5M5 17.5h10a3.333 3.333 0 0 0 3.333-3.333V5.833A3.333 3.333 0 0 0 15 2.5H5a3.333 3.333 0 0 0-3.333 3.333v8.334A3.333 3.333 0 0 0 5 17.5ZM6.667 5.833h1.666a1.667 1.667 0 1 1 0 3.334H6.667a1.667 1.667 0 0 1 0-3.334Z"
      />
    </svg>
  );
}

IDashBoard.propTypes = {
  className: PropTypes.string,
};

function IDashBoard({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 25 21"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.3"
        d="m23.235,8.817c-1.565-4.206-5.432-7.243-9.85-7.739-3.468-.387-6.801.667-9.381,2.973C1.46,6.326,0,9.588,0,13c0,3.073,1.261,6.138,3.459,8.408.979,1.012,2.391,1.592,3.874,1.592h9.332c1.488,0,2.908-.587,3.896-1.611,3.271-3.396,4.297-8.213,2.674-12.571Zm-3.394,11.877c-.801.83-1.959,1.306-3.177,1.306H7.333c-1.214,0-2.364-.469-3.155-1.287-2.02-2.086-3.178-4.897-3.178-7.713,0-3.128,1.338-6.117,3.672-8.204,2.043-1.826,4.601-2.797,7.312-2.797.427,0,.857.024,1.291.073,4.046.454,7.588,3.238,9.023,7.095,1.487,3.994.546,8.411-2.456,11.527Zm-1.988-13.548c-.195-.195-.512-.195-.707,0l-4.137,4.137c-.297-.176-.64-.284-1.009-.284-1.103,0-2,.897-2,2s.897,2,2,2,2-.897,2-2c0-.37-.108-.712-.284-1.009l4.137-4.137c.195-.195.195-.512,0-.707Zm-5.854,6.854c-.552,0-1-.448-1-1s.448-1,1-1,1,.448,1,1-.448,1-1,1Zm7.334-4.2c.442,1.012.666,2.088.666,3.2,0,1.972-.724,3.866-2.038,5.334-.099.109-.235.166-.372.166-.119,0-.238-.042-.334-.128-.205-.184-.223-.5-.038-.706,1.149-1.283,1.782-2.94,1.782-4.666,0-.974-.196-1.915-.582-2.8-.11-.253.005-.548.258-.658.254-.107.548.005.658.258Zm-7.334-3.8c-3.859,0-7,3.141-7,7,0,1.726.633,3.383,1.782,4.666.185.206.167.522-.038.706-.096.086-.215.128-.334.128-.137,0-.273-.057-.372-.166-1.314-1.468-2.038-3.362-2.038-5.334,0-4.411,3.589-8,8-8,1.112,0,2.188.224,3.2.666.253.11.368.405.258.658s-.404.368-.658.258c-.885-.386-1.826-.582-2.8-.582Z"
      />
    </svg>
  );
}

Idea.propTypes = {
  className: PropTypes.string,
};

function Idea({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="M6.667 15h6.666m-6.667 0a3.333 3.333 0 1 0 6.667 0m-6.667 0v-1.842c0-.545-.277-1.044-.673-1.418a5.833 5.833 0 1 1 8.014 0c-.396.374-.674.873-.674 1.418V15m-5-7.5L10 9.167m0 0L11.666 7.5M10 9.167V15"
      />
    </svg>
  );
}

Briefcase.propTypes = {
  className: PropTypes.string,
};

function Briefcase({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <g clipPath="url(#a)">
        <path
          stroke="currentColor"
          strokeLinecap="round"
          strokeWidth="1.5"
          d="M18.333 8.334V15A3.333 3.333 0 0 1 15 18.334H5A3.333 3.333 0 0 1 1.667 15V8.334m16.666 0A3.333 3.333 0 0 0 15 5H5a3.333 3.333 0 0 0-3.333 3.334m16.666 0s-2.635 2.303-8.333 2.15c-5.698-.152-8.333-2.15-8.333-2.15m5-3.334v-.833a2.5 2.5 0 0 1 2.5-2.5h1.666a2.5 2.5 0 0 1 2.5 2.5V5M6.666 8.898v2.5m6.667-2.5v2.5"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="currentColor" d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

Users.propTypes = {
  className: PropTypes.string,
};

function Users({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <ellipse
        cx={10}
        cy="13.75"
        stroke="currentColor"
        strokeLinejoin="round"
        strokeWidth="1.5"
        rx={5}
        ry="2.083"
      />
      <circle
        cx={10}
        cy="6.667"
        r="2.5"
        stroke="currentColor"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M5.629 10.919c-1.144.02-2.206.214-3.02.54-.43.172-.828.395-1.13.681-.305.288-.562.691-.562 1.194s.257.906.562 1.193c.302.287.7.51 1.13.682.469.188 1.02.331 1.623.424-.535-.519-.856-1.116-.895-1.753a4.14 4.14 0 0 1-.172-.064c-.324-.13-.537-.266-.656-.378a.455.455 0 0 1-.087-.104.454.454 0 0 1 .087-.104c.12-.113.332-.248.656-.378.132-.053.276-.102.43-.147.362-.702 1.079-1.32 2.034-1.786ZM2.414 13.35l.001-.004a.013.013 0 0 1 0 .004Zm.001-.03v-.005.004ZM16.663 13.88c-.039.637-.36 1.234-.896 1.752a7.445 7.445 0 0 0 1.624-.423c.43-.172.828-.395 1.13-.682.304-.287.562-.69.562-1.193 0-.503-.258-.906-.562-1.194-.303-.286-.7-.51-1.13-.681-.814-.326-1.876-.52-3.02-.54.955.467 1.671 1.084 2.033 1.786.155.045.299.094.43.147.325.13.538.265.657.378.**************.087.104a.457.457 0 0 1-.087.104c-.12.112-.332.248-.657.378a4.106 4.106 0 0 1-.171.064Zm.922-.529v-.004.004Zm0-.03v-.005.004ZM13.814 8.346c-.206.468-.496.891-.851 1.25a2.417 2.417 0 1 0 .9-4.494c.186.459.292.958.303 1.481a.917.917 0 1 1-.352 1.763ZM6.137 5.102a2.417 2.417 0 1 0 .9 4.494 4.174 4.174 0 0 1-.851-1.25.917.917 0 1 1-.352-1.763c.01-.523.117-1.022.303-1.481Z"
        clipRule="evenodd"
      />
    </svg>
  );
}

CurrencyExchange.propTypes = {
  className: PropTypes.string,
};

function CurrencyExchange({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 25 24"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
        d="m16,18.5v1c0,2.481-2.019,4.5-4.5,4.5h-7c-2.481,0-4.5-2.019-4.5-4.5v-7c0-2.481,2.019-4.5,4.5-4.5h1c.276,0,.5.224.5.5s-.224.5-.5.5h-1c-1.93,0-3.5,1.57-3.5,3.5v7c0,1.93,1.57,3.5,3.5,3.5h7c1.93,0,3.5-1.57,3.5-3.5v-1c0-.276.224-.5.5-.5s.5.224.5.5Zm8-14v7c0,2.481-2.019,4.5-4.5,4.5h-7c-2.481,0-4.5-2.019-4.5-4.5v-7c0-2.481,2.019-4.5,4.5-4.5h7c2.481,0,4.5,2.019,4.5,4.5Zm-1,0c0-1.93-1.57-3.5-3.5-3.5h-7c-1.93,0-3.5,1.57-3.5,3.5v7c0,1.93,1.57,3.5,3.5,3.5h7c1.93,0,3.5-1.57,3.5-3.5v-7Z"
      />
      {/* <path
        fill="currentColor"
        d="M9.307 5.346h-.825a.32.32 0 0 0-.31.315v.737C7.1 6.608 6.335 7.62 6.44 8.756c.103 1.095 1.073 1.958 2.229 1.958h.536c.186 0 .392.084.516.232a.835.835 0 0 1 .186.547c-.041.358-.371.632-.743.632H8.42a.444.444 0 0 1-.433-.358.292.292 0 0 0-.29-.232h-.846a.32.32 0 0 0-.31.337c.083.884.764 1.6 1.631 1.705v.716a.32.32 0 0 0 .31.316h.825a.32.32 0 0 0 .31-.316v-.716c.929-.21 1.63-.99 1.734-1.915a2.163 2.163 0 0 0-.537-1.685 2.125 2.125 0 0 0-1.59-.716h-.639a.697.697 0 0 1-.516-.231.835.835 0 0 1-.186-.547c.042-.358.372-.632.743-.632h.743c.207 0 .393.147.434.358.02.147.144.231.289.231h.846a.32.32 0 0 0 .31-.337c-.083-.884-.764-1.6-1.63-1.705v-.715c0-.19-.145-.337-.31-.337Z"
      />
      <path
        fill="currentColor"
        d="M9.08 1.43V.46a.453.453 0 0 0-.227-.4.4.4 0 0 0-.454.022l-2.6 1.62a.473.473 0 0 0-.207.38c0 .147.083.295.207.379l2.58 1.642a.463.463 0 0 0 .227.063.463.463 0 0 0 .227-.063.453.453 0 0 0 .227-.4V2.86c3.756.147 6.79 3.284 6.79 7.137 0 1.894-.722 3.684-2.043 5.01a.748.748 0 0 0 0 1.053.71.71 0 0 0 .516.21.709.709 0 0 0 .516-.21 8.608 8.608 0 0 0 2.477-6.063c0-4.674-3.695-8.464-8.236-8.569ZM9.41 15.87a.496.496 0 0 0-.453-.02.453.453 0 0 0-.227.4v.842c-3.778-.105-6.79-3.242-6.79-7.095 0-1.895.721-3.663 2.042-5.01a.747.747 0 0 0 0-1.053.713.713 0 0 0-1.032 0A8.608 8.608 0 0 0 .474 9.997c0 4.674 3.674 8.484 8.235 8.569v.968c0 .168.082.316.227.4a.41.41 0 0 0 .227.063.41.41 0 0 0 .227-.063l2.6-1.642a.473.473 0 0 0 .207-.38.473.473 0 0 0-.206-.378L9.41 15.87Z"
      /> */}
    </svg>
  );
}

function CRMIcon({ className }) {
  return <ApartmentOutlined className={className} />;
}

Mail.propTypes = {
  className: PropTypes.string,
};

export function Mail({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width={24}
      height={24}
      color="currentColor"
      fill="none"
      className={className}
    >
      <path
        d="M2 6L8.91302 9.91697C11.4616 11.361 12.5384 11.361 15.087 9.91697L22 6"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M2.01577 13.4756C2.08114 16.5412 2.11383 18.0739 3.24496 19.2094C4.37608 20.3448 5.95033 20.3843 9.09883 20.4634C11.0393 20.5122 12.9607 20.5122 14.9012 20.4634C18.0497 20.3843 19.6239 20.3448 20.7551 19.2094C21.8862 18.0739 21.9189 16.5412 21.9842 13.4756C22.0053 12.4899 22.0053 11.5101 21.9842 10.5244C21.9189 7.45886 21.8862 5.92609 20.7551 4.79066C19.6239 3.65523 18.0497 3.61568 14.9012 3.53657C12.9607 3.48781 11.0393 3.48781 9.09882 3.53656C5.95033 3.61566 4.37608 3.65521 3.24495 4.79065C2.11382 5.92608 2.08114 7.45885 2.01576 10.5244C1.99474 11.5101 1.99475 12.4899 2.01577 13.4756Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function Sequence({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width={24}
      height={24}
      color="currentColor"
      fill="none"
      className={className}
    >
      <path
        stroke="currentColor"
        // strokeWidth=""
        strokeLinejoin="round"
        d="m21,6c1.654,0,3-1.346,3-3s-1.346-3-3-3c-1.304,0-2.415.836-2.828,2H5.828c-.413-1.164-1.524-2-2.828-2C1.346,0,0,1.346,0,3c0,1.304.836,2.415,2,2.828v10.344c-1.164.413-2,1.524-2,2.828,0,1.654,1.346,3,3,3,1.304,0,2.415-.836,2.828-2h16.038c-.043.075-.096.144-.159.207l-2.939,2.939c-.195.195-.195.512,0,.707.098.098.226.146.354.146s.256-.049.354-.146l2.939-2.939c.378-.377.586-.88.586-1.414s-.208-1.037-.586-1.414l-2.939-2.939c-.195-.195-.512-.195-.707,0s-.195.512,0,.707l2.939,2.939c.063.063.116.132.159.207H6c0-1.654-1.346-3-3-3V6c1.654,0,3-1.346,3-3h12c0,1.654,1.346,3,3,3ZM5,19c0,1.103-.897,2-2,2s-2-.897-2-2,.897-2,2-2,2,.897,2,2ZM3,5c-1.103,0-2-.897-2-2S1.897,1,3,1s2,.897,2,2-.897,2-2,2ZM21,1c1.103,0,2,.897,2,2s-.897,2-2,2-2-.897-2-2,.897-2,2-2Zm-12,10c0,1.654,1.346,3,3,3s3-1.346,3-3-1.346-3-3-3-3,1.346-3,3Zm5,0c0,1.103-.897,2-2,2s-2-.897-2-2,.897-2,2-2,2,.897,2,2Z"
      />
    </svg>
  );
}

export function CompanyIcon({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className={className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"
      />
    </svg>
  );
}

export function UserGroupIcon({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className={className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
      />
    </svg>
  );
}

const routes = [
  {
    name: 'Dashboard',
    path: 'dashboard',
    keyCode: 'DASHBOARD',
    icon: (
      <Home className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.DASHBOARD,
  },
  {
    name: 'Tasks',
    path: 'task',
    keyCode: 'EMAIL_FINDER',
    icon: (
      <BarChart className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.TASK,
  },
  // {
  //   name: 'User Management',
  //   path: 'user-management',
  //   keyCode: 'USER_MANAGEMENT',
  //   icon: (
  //     <LucideUsers className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
  //   ),
  //   roles: [userRole.ADMIN, SUPER_ADMIN, SALES],
  //   permission: FEATURES.USER_MANAGEMENT,
  // },
  // {
  //   name: 'Staff Performance',
  //   path: 'staff-performance',
  //   keyCode: 'STAFF_PERFORMANCE',
  //   icon: <MdInsertChartOutlined className="h-5 w-5" />,
  //   roles: [userRole.MANAGEMENT, userRole.ADMIN, SUPER_ADMIN, SALES],
  //   permission: FEATURES.STAFF_PERFORMANCE,
  // },
  {
    name: 'Search',
    keyCode: 'JOB_SEARCH',
    icon: (
      <Search className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    path: 'search',
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SEARCH,
    // badge: 'new',
  },
  {
    name: 'Syncs',
    path: 'syncs',
    keyCode: 'JOB_SYNC',
    icon: (
      <TrendingUp className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SYNC,
    // badge: 'new',
  },
  {
    name: 'My Leads',
    path: 'my-leads',
    keyCode: 'JOB_LEAD',
    icon: (
      <Database className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MY_LEADS,
  },
  {
    name: 'Mailbox',
    path: 'mail-box',
    keyCode: 'JOB_LEAD',
    icon: (
      <LucideMailIcon className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MAILBOX,
  },
  {
    name: 'Sequences',
    path: 'sequence?activeKey=sequence-tab',
    keyCode: 'JOB_LEAD',
    icon: (
      <Target className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.SEQUENCE,
  },
  {
    name: 'Manual Leads',
    path: 'manual-leads',
    keyCode: 'JOB_LEAD',
    icon: (
      <Lightbulb className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.MANUAL_LEADS,
  },
  {
    name: 'Reported Agencies',
    path: 'reported_agencies',
    keyCode: 'REPORTED_AGENCIES',
    icon: (
      <LucideBriefcase className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.REPORTED_AGENCIES,
  },
  {
    name: 'Contact Finder',
    path: 'email-finder',
    keyCode: 'EMAIL_FINDER',
    icon: (
      <UserSearch className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.EMAIL_FINDER,
  },
  {
    name: 'Contact Finder Beta',
    path: 'email-finder-beta',
    keyCode: 'EMAIL_FINDER',
    icon: (
      <TestTube className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [
      userRole.BASIC_USER,
      userRole.MANAGEMENT,
      userRole.ADMIN,
      SUPER_ADMIN,
      SALES,
    ],
    permission: FEATURES.EMAIL_FINDER,
    badge: 'β',
  },
  {
    name: 'Duplicate Jobs',
    path: 'duplicate-jobs',
    keyCode: 'EMAIL_FINDER',
    icon: (
      <Sparkles className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.DUPLICATED_JOBS,
  },
  {
    name: 'CRM',
    path: 'crm',
    keyCode: 'EMAIL_FINDER',
    icon: (
      <HeadphonesIcon className="w-4 h-4 mb-1 transition-all duration-300  'text-gray-400 group-hover:text-gray-600 group-hover:scale-110" />
    ),
    roles: [SUPER_ADMIN, SALES],
    permission: FEATURES.CRM,
  },
  // {
  //   name: 'Credit Management',
  //   path: 'credit-management',
  //   keyCode: 'CREDIT_MANAGEMENT',
  //   icon: <CreditCardOutlined />,
  //   roles: [
  //     userRole.ADMIN,
  //     SUPER_ADMIN,
  //   ],
  //   permission: FEATURES.CREDIT_MANAGEMENT,
  // },
];

export default routes;
