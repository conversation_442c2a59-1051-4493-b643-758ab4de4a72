/* eslint-disable */
import { HiX } from 'react-icons/hi';
import Links from './components/Links';
import { Image, Layout, Button, Flex } from 'antd';
import logo from '../../assets/img/welcome/logo.png';
import { SidebarLink } from './components/Link';
import { useViewAs } from '../../store/viewAs';
import { useAuth } from '../../store/auth';
import { useQueryClient } from '@tanstack/react-query';
import {
  MdKeyboardArrowLeft,
  MdKeyboardArrowRight,
  MdOutlineSettings,
} from 'react-icons/md';
import './style.css';
import useResponsive from '../../hooks/useResponsive';
import { Link } from 'react-router-dom';
import { LogOut, Zap } from 'lucide-react';

const { Sider } = Layout;

const Sidebar = ({ open, onClose, mode, routes, setOpen }) => {
  const { clearAuth } = useAuth();
  const queryClient = useQueryClient();
  const { clearViewAs } = useViewAs();
  const { isMobile } = useResponsive();

  const onLogout = async () => {
    clearViewAs();
    clearAuth();
    queryClient.setQueryData(['CURRENT_USER'], null);
    window.location.reload(); // reload the page to clear the cache
  };

  const calculateWidthOfSidebarFooter = () => {
    if (open) {
      return isMobile ? '75%' : 190;
    }
    return isMobile ? 0 : 80;
  };

  const currentPath = window?.location?.pathname;
  const isInCRMPage = currentPath.includes('/crm');

  return (
    <div
      style={{
        position: 'relative',
        width: !open ? (isMobile ? '0' : '79px') : '210px',
      }}
    >
      <Sider
        className={`border-r h-screen shadow-md`}
        collapsible
        collapsed={!open}
        trigger={null}
        collapsedWidth={isMobile ? 0 : 80}
        width={isMobile ? '75%' : 210}
        style={
          isMobile ? { position: 'fixed', zIndex: 41 } : { position: 'fixed' }
        }
      >
        <span
          className="absolute top-4 right-4 block cursor-pointer xl:hidden"
          onClick={onClose}
        >
          <HiX />
        </span>

        <div className="w-52 bg-white/80 backdrop-blur-sm b flex flex-col shadow-sm">
          {/* Logo */}
          <div className="px-4 py-7 border-b border-gray-100/50 flex-shrink-0">
            <div className="flex items-center space-x-2">
              <div className="relative group">
                <div className="w-7 h-7 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-cyan-500/25 transition-all duration-300">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </div>
              <div className="text-base font-bold gradient-text">
                ZILEO
              </div>
            </div>
          </div>
        </div>
        {/* <hr /> */}
        <ul className="flex flex-col flex-1 px-2">
          <div>
            {/* <li
            className={`py-4 text-sm font-semibold ${open ? 'pl-5' : 'text-center'}`}
          >
            MENU
          </li> */}
            <div
              className="customer_scroll_bar overflow-y-auto pr-1"
              style={{
                height: '80vh',
                scrollbarWidth: 'thin',
              }}
            >
              <Links
                routes={routes}
                onClose={onClose}
                mode={mode}
                showName={open}
              />
            </div>
            <div
              className={`fixed left-0 bottom-0 pt-3 px-2 bg-white z-10 ${isMobile && !open ? 'hidden' : ''}`}
              style={{ width: calculateWidthOfSidebarFooter() }}
            >
              {/* <Flex className="mb-4" justify={open ? 'flex-end' : 'center'}>
              <Button
                type="primary"
                icon={open ? <MdKeyboardArrowLeft /> : <MdKeyboardArrowRight />}
                onClick={() => {
                  setOpen((value) => !value);
                }}
              />
            </Flex> */}
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <SidebarLink
                  mode={mode}
                  name="Logout"
                  showName={open}
                  onClose={onClose}
                  onClick={onLogout}
                  Icon={<LogOut className="h-6 w-6 text-secondary" />}
                />
                <div className="hover:bg-primary ">
                  <Link to={`/settings?${isInCRMPage ? 'tab=crm' : ''}`}>
                    <MdOutlineSettings
                      style={{
                        fontSize: '25px',
                        color: '#5e768d',
                      }}
                    />
                  </Link>
                </div>
              </div>
              {/* <SidebarLink
              mode={mode}
              name="Logout"
              showName={open}
              onClose={onClose}
              onClick={onLogout}
              Icon={<LogoutIcon className="h-6 w-6 text-secondary" />}
            /> */}
            </div>
          </div>
        </ul>
      </Sider>
    </div>
  );
};

export default Sidebar;

function LogoutIcon({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20"
      className={className}
    >
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
        d="m15 11.667.96-.96a1 1 0 0 0 0-1.414l-.96-.96"
      />
      <path
        stroke="currentColor"
        strokeLinecap="round"
        strokeWidth="1.5"
        d="M15.834 10h-5m-7.5 4.389V6.055m10 8.334c0 .92-.747 1.666-1.667 1.666H8.334m5-10c0-.92-.747-1.666-1.667-1.666H8.334M4.076 16.55l1.666 1.111c1.108.739 2.592-.055 2.592-1.387V4.17c0-1.33-1.484-2.125-2.592-1.386l-1.666 1.11c-.464.31-.742.83-.742 1.388v9.882c0 .558.278 1.078.742 1.387Z"
      />
    </svg>
  );
}

function ZleoShortIcon({ className }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 48 33"
      className={className}
      width={48}
      height={33}
    >
      <path
        fill="currentColor"
        d="M22.286 13.539 0 0l33.857 11.846-8.143 7.192L48 33 14.143 20.73l8.143-7.191Z"
      />
    </svg>
  );
}
