import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import DashIcon from '../../icons/DashIcon';
import PropTypes from 'prop-types';
import { getLinkedInTotalMessage } from '../../../services/mailBox';

SidebarLink.propTypes = {
  onClose: PropTypes.func,
  onClick: PropTypes.func,
  mode: PropTypes.string,
  to: PropTypes.string,
  isActive: PropTypes.bool,
  Icon: PropTypes.element,
  showName: PropTypes.bool,
  name: PropTypes.string,
  badge: PropTypes.string,
};

const STAGING_ENV_TEXT = 'staging';

export function SidebarLink({
  onClose,
  onClick,
  mode,
  to,
  isActive,
  Icon,
  name,
  showName,
  path,
  badge,
}) {
  const currentEnv = import.meta.env.VITE_CURRENT_ENV;
  const isStaging = currentEnv.includes(STAGING_ENV_TEXT);

  const [totalLinkedIn, setTotalLinkedIn] = useState(0);

  const handleGetLinkedInMessage = async () => {
    const { data } = await getLinkedInTotalMessage();
    setTotalLinkedIn(data?.result?.totalLinkedinMessage);
  };

  useEffect(() => {
    if (path === 'mail-box' && !isStaging) {
      handleGetLinkedInMessage();
    }
  }, [path]);

  return (
    <Link
      to={to}
      onClick={onClick}
      className={`w-full flex flex-col items-center py-2 px-1.5 rounded-lg text-xs transition-all duration-300 group relative my-1 ${
        isActive
          ? 'bg-gradient-to-r from-cyan-50 to-cyan-100 text-cyan-600 shadow-sm border border-cyan-200/50'
          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50/80 hover:shadow-sm'
      }`}
    >
      <div
        className={`relative flex hover:cursor-pointer ${!showName ? 'justify-center' : ''} w-full justify-center`}
        onClick={() => {
          if (mode === 'mobile') onClose(false);
        }}
      >
        {badge && (
          <div
            className={`absolute -top-1 -right-1 min-w-[16px] h-4 rounded-full flex items-center justify-center text-[10px] font-bold text-white ${
              badge === 'new'
                ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 px-1.5'
                : badge === '!'
                  ? 'bg-gradient-to-r from-red-500 to-red-600'
                  : badge === 'β'
                    ? 'bg-gradient-to-r from-purple-500 to-purple-600'
                    : 'bg-gradient-to-r from-cyan-500 to-cyan-600'
            }`}
          >
            {badge}
          </div>
        )}
        <li className="flex flex-col cursor-pointer items-center justify-center px-4">
          <span className={`pb-1 ${isActive && 'text-cyan-600'}`}>
            {Icon ? Icon : <DashIcon />}{' '}
          </span>
          {showName && (
            <p
              className={`font-semibold text-center leading-tight ${isActive ? 'text-cyan-600' : 'text-gray-700'}`}
            >
              {name}
            </p>
          )}
          {totalLinkedIn > 0 && path === 'mail-box' && !isStaging && (
            <div
              style={{
                position: 'absolute',
                background: 'red',
                width: '25px',
                height: '25px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '50%',
                right: '5px',
                top: '5px',
                color: 'white',
                fontSize: '11px',
                fontWeight: 'bold',
                transform: 'translateY(-50%)',
              }}
            >
              {totalLinkedIn}
            </div>
          )}
        </li>
      </div>
    </Link>
  );
}
