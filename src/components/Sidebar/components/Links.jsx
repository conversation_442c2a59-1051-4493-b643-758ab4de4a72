/* eslint-disable */
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useUserRole } from '../../../hooks/useUserRole';
// chakra imports
import { useViewAs } from '../../../store/viewAs';
import { SidebarLink } from './Link';
import { SUPER_ADMIN, userRole } from '../../../constants/common.constant';

export function SidebarLinks(props) {
  // Chakra color mode
  let location = useLocation();
  const { isOrganizationalAdmin } = useUserRole();
  const { routes, onClose, mode, showName } = props;
  const { profileUser, viewerAsUser } = useViewAs();

  // verifies if routeName is the one active (in browser input)
  const activeRoute = (routeName) => {
    const routeNameOffical = routeName?.includes('?')
      ? routeName?.split('?')[0]
      : routeName;
    const match = location.pathname.match(/\/([^/]+)(\/|$)/);
    return match && match.includes(routeNameOffical);
  };

  const createLinks = (routes) => {
    const conditionalRoutes = [...routes];
    let newRouterList = conditionalRoutes;
    if (viewerAsUser && profileUser?.role?.keyCode !== SUPER_ADMIN) {
      newRouterList = conditionalRoutes.filter(
        (item) => item.path !== 'duplicate-jobs'
      );
    }
    return newRouterList.map((route, index) => {
      return (
        <SidebarLink
          mode={mode}
          Icon={route.icon}
          name={route.name}
          onClose={onClose}
          showName={showName}
          path={route.path}
          to={`/${route.path}`}
          key={`sidebar-link-${index}`}
          isActive={activeRoute(route.path) === true}
          badge={route?.badge || ''}
        />
      );
    });
  };
  // BRAND
  return createLinks(routes);
}

export default SidebarLinks;
