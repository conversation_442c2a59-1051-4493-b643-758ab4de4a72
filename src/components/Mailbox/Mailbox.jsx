import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Row,
  Col,
  Button,
  Badge,
  Tabs,
  notification,
  message,
} from 'antd';
import { MailOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import LeadCard from '../JobsLeads/LeadCard';
import { getListLeadMailBox } from '../../services/myLead';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import LoadingAdvanced from '../../common/LoadingAdvanced';
import { MdMailOutline } from 'react-icons/md';
import PropTypes from 'prop-types';
import { getJobDetail, getSentJob } from '../../services/jobLeads';
import BullHornJobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
import { generateJobSkills } from '../../services/search';

import { useSearchParams } from 'react-router-dom';
import clsx from 'clsx';
import { useRef } from 'react';
import { stripJobDescription } from '../../utils/common';

const PAGE_LIMIT = 10;

Mailbox.propTypes = {
  countLeadMailBox: PropTypes.number.isRequired,
  userId: PropTypes.string.isRequired,
  handleUpdateCountLeadAfterChangeStatusToDone: PropTypes.func.isRequired,
  className: PropTypes.string,
};

export default function Mailbox({
  countLeadMailBox,
  userId,
  handleUpdateCountLeadAfterChangeStatusToDone,
  className,
  reloadBoardData,
}) {
  const [searchParams] = useSearchParams();
  const notiJobBoardId = searchParams.get('leadJobBoardId');
  const inMailType = searchParams.get('inMailType');
  const selectedLeadRef = useRef(null);

  const executeScroll = () =>
    selectedLeadRef.current.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });

  const [isModalVisible, setIsModalVisible] = useState(!!notiJobBoardId);
  const [isShowInMailbox, setIsShowInMailbox] = useState(!!notiJobBoardId);
  const [dataInMailBox, setDataMailBox] = useState([]);
  const [dataInMailLoading, setDataMailLoading] = useState(true);
  const [jobToSync, setJobToSync] = useState();
  const [isModalOpen, setModalOpen] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [sentJobId, setSentJobId] = useState();
  const [sentJodIdCheck, setSentJodIdCheck] = useState();
  const [actionKey, setActionKey] = useState();
  const [refresh, setRefresh] = useState(false);
  const { ref, inView } = useInView({
    threshold: 0,
  });

  const { data, fetchNextPage, hasNextPage, isLoading, refetch } =
    useInfiniteQuery(['GET_LIST_LEAD_MAIL_BOX', userId], getListLeadMailBox, {
      getNextPageParam: (lastPage, allPages) => {
        const nextPage =
          lastPage?.data?.result?.items?.length >= PAGE_LIMIT
            ? allPages.length + 1
            : undefined;
        return nextPage;
      },
      // refetchOnWindowFocus: false,
      enabled: !refresh,
    });

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, fetchNextPage]);

  const handleSentJob = async () => {
    setDataMailLoading(true);
    const { data } = await getSentJob();
    setDataMailBox(data?.result?.items);
    setDataMailLoading(false);
  };

  useEffect(() => {
    handleSentJob();
  }, []);

  useEffect(() => {
    if (
      dataInMailBox?.length > 0 &&
      notiJobBoardId &&
      selectedLeadRef.current
    ) {
      executeScroll();
    }
  }, [dataInMailBox, notiJobBoardId]);

  const handleOnClick = () => {
    setIsModalVisible(true);
    setIsShowInMailbox(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    reloadBoardData();
  };

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleAfterUpdateStatusCompleted = () => {
    handleUpdateCountLeadAfterChangeStatusToDone();
    refetch();
  };

  const handleSyncJob = async (id, key) => {
    try {
      setSentJobId(id);
      messageApi.open({
        type: 'loading',
        content: 'Loading ...',
        duration: 0,
      });
      const { data } = await getJobDetail(id);

      const job = {
        ...data.result.item,
        description: stripJobDescription(data.result.item.description),
      };

      if (data) {
        if (!job?.skills || job?.skills?.length === 0) {
          setModalOpen(true);
          const { data: skillsData } = await generateJobSkills(job?.job_id);
          const jobToSyncWithSkills = {
            ...job,
            skills: skillsData?.result || [],
          };
          setActionKey(key);
          setJobToSync(jobToSyncWithSkills);
        } else {
          setJobToSync(job);
          setModalOpen(true);
          setActionKey(key);
        }
        messageApi.destroy();
      }
    } catch (err) {
      notification.error({
        messages: 'Something went wrong',
      });
      messageApi.destroy();
    }
  };

  useEffect(() => {
    if (!isModalOpen) {
      handleSentJob();
    }
  }, [isModalOpen]);

  const items = [
    {
      key: 'existing-leads',
      label: 'Existing Leads',
      children: (
        <div
          className="existing-leads-container"
          style={{ maxHeight: '700px' }}
        >
          {isLoading && (
            <Spin
              style={{
                float: 'right',
                marginTop: '-25px',
                marginRight: '10px',
              }}
            />
          )}
          {data?.pages?.map((page) =>
            page?.data?.result?.items?.map((item) => (
              <div key={item.id} className="mb-4">
                <LeadCard
                  setRefresh={setRefresh}
                  refresh={refresh}
                  fromExisting={true}
                  lead={item}
                  isShowInMailbox={isShowInMailbox}
                  handleAfterUpdateStatusCompleted={
                    handleAfterUpdateStatusCompleted
                  }
                  reloadJobLeads={() => refetch()}
                  fromExistingTab={true}
                />
              </div>
            ))
          )}
          {hasNextPage && (
            <Row ref={ref}>
              <Col span={24}>
                <LoadingAdvanced isSkeleton />
              </Col>
            </Row>
          )}
        </div>
      ),
    },
    {
      key: 'new-leads',
      label: 'New Leads',
      children: (
        <div
          className="new-leads-container"
          style={{ maxHeight: '700px', overflowY: 'auto' }}
        >
          {(isLoading || dataInMailLoading) && (
            <Spin
              className="absolute"
              style={{
                float: 'right',
                top: '-25px',
                right: '0rem',
              }}
            />
          )}
          {data?.pages?.map((page) =>
            dataInMailBox?.map((item) => (
              <div
                ref={
                  notiJobBoardId === item?.jobBoardId ? selectedLeadRef : null
                }
                tabIndex={notiJobBoardId === item?.jobBoardId && 0}
                autoFocus={notiJobBoardId === item?.jobBoardId}
                key={item.id}
                className={clsx(
                  'mb-4 rounded-lg',
                  notiJobBoardId === item?.jobBoardId && 'animate-bounce-short'
                )}
              >
                <LeadCard
                  lead={item}
                  isShowInMailbox={isShowInMailbox}
                  handleAfterUpdateStatusCompleted={
                    handleAfterUpdateStatusCompleted
                  }
                  review={true}
                  onClickSync={handleSyncJob}
                  onClickCheckId={setSentJodIdCheck}
                  reloadJobLeads={() => {
                    handleSentJob();
                  }}
                />
              </div>
            ))
          )}
          {hasNextPage && (
            <Row ref={ref}>
              <Col span={24}>
                <LoadingAdvanced isSkeleton />
              </Col>
            </Row>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      {contextHolder}
      <Badge count={countLeadMailBox}>
        <Button
          icon={<MdMailOutline />}
          onClick={handleOnClick}
          type="primary"
        />
      </Badge>
      {isModalOpen && (
        <BullHornJobSubmissionModal
          job={jobToSync}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
          sentJobId={sentJodIdCheck}
          actionKey={actionKey}
          // handleDeleteData={handleDeleteData}
        />
      )}
      <Modal
        title="Mailbox"
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={false}
      >
        <Tabs defaultActiveKey={inMailType || 'existing-leads'} items={items} />
      </Modal>
    </>
  );
}
