import { useParams } from 'react-router-dom';
import { getSearchesBySyncId } from '../../services/searchSync';
import SearchTableV2 from '../SearchTableV2/SearchTableV2';
import React, { useEffect, useState } from 'react';

const useSyncDetail = () => {
  let { id: syncId } = useParams();
  const [sync, setSync] = useState({ jobSearches: [] });
  const [isLoading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalCount: 0,
  });
  const [prevPagination, setPrevPagination] = useState(null);

  const populateSearchData = async (pagination) => {
    setLoading(true);
    const { data } = await getSearchesBySyncId(syncId, pagination);
    setPrevPagination({
      page: data.result.currentPage,
      limit: data.result.pageSize,
      totalCount: data.result.totalCount,
    });
    setSync(data.result.data[0]);
    setPagination({
      page: data.result.currentPage,
      limit: data.result.pageSize,
      totalCount: data.result.totalCount,
    });
    setLoading(false);
  };

  return {
    sync,
    isLoading,
    populateSearchData,
    syncId,
    pagination,
    setPagination,
    setPrevPagination,
    prevPagination,
  };
};

const SyncDetailV2 = () => {
  const {
    sync,
    populateSearchData,
    syncId,
    pagination,
    setPagination,
    prevPagination,
  } = useSyncDetail();

  useEffect(() => {
    if (
      pagination.page !== prevPagination?.page ||
      pagination.limit !== prevPagination?.limit
    ) {
      populateSearchData(pagination);
    }
  }, [pagination]);

  return (
    <>
      <SearchTableV2
        searches={sync?.jobSearches}
        syncId={syncId}
        pagination={pagination}
        setPagination={setPagination}
      />
    </>
  );
};

export default SyncDetailV2;
