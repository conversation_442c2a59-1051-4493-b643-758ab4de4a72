/* eslint-disable react/prop-types */
import React from 'react';
import { CSVLink } from 'react-csv';
import { Empty } from 'antd';
import './style.css';

export default function EmailTable({ verifiedData }) {
  const csvData = [
    [
      'No',
      'Email Address',
      'Domain',
      'Free Eamil Provider',
      'Syntax Error',
      'Valid MX record',
      'SMTP',
      'Verified',
      'ServerDown',
      'Greylisted',
      'Disposable',
      'Suppressed',
      'Role',
      'High Risk',
      'Catch All',
      'Mailbox Validator Score',
      'Time Take',
      'Status',
      'Error Code',
      'Error Message',
    ],
    ...verifiedData.map(
      ({
        no,
        email_address,
        domain,
        is_free,
        is_syntax,
        is_domain,
        is_smtp,
        is_verified,
        is_server_down,
        is_greylisted,
        is_disposable,
        is_suppressed,
        is_role,
        is_high_risk,
        is_catchall,
        mailboxvalidator_score,
        time_taken,
        status,
        error_code,
        error_message,
      }) => [
        no,
        email_address,
        domain,
        is_free,
        is_syntax,
        is_domain,
        is_smtp,
        is_verified,
        is_server_down,
        is_greylisted,
        is_disposable,
        is_suppressed,
        is_role,
        is_high_risk,
        is_catchall,
        mailboxvalidator_score,
        time_taken,
        status,
        error_code,
        error_message,
      ]
    ),
  ];

  if (verifiedData.length == 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  else
    return (
      <>
        <CSVLink
          className="p-2 text-white mr-0 mt-12 min-w-[340px] sm:w-[180px] rounded-md h-[56px] bg-gradient-to-r from-cyan-500 to-blue-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-cyan-300 dark:focus:ring-cyan-800 font-medium border-none"
          filename="Email Data.csv"
          data={csvData}
          
        >
          Export to CSV
        </CSVLink>
        <div className="flex flex-col">
          <div className="overflow-x-auto">
            <div className="py-3 align-middle inline-block min-w-full">
              <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-[#e30075] dark:bg-brandLinear text-white font-[PoppinsMedium]">
                    <tr>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">No</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Email</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Domain</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Free</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Syntax</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[120px] inline-table">MX Record</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">SMTP</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Verified</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">
                          Server Down
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Greylisted</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Disposable</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Suppressed</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Role</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">High Risk</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Catch All</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-2 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">
                          MailboxValidator Score
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Time Taken</div>
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-6 text-left text-xs font-medium text-black uppercase tracking-wider"
                      >
                        <div className="w-[100px] inline-table">Status</div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:text-white">
                    {verifiedData.map((item) => (
                      <tr
                        key={item.email_address}
                        className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.no}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {item.email_address}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.domain}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div
                            className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full text-green-800"
                          >
                            {item.is_free}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div
                            className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full text-green-800"
                          >
                            {item.is_syntax}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_domain}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div
                            className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full text-green-800"
                          >
                            {item.is_smtp}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_verified}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_server_down}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_greylisted}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_disposable}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_suppressed}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_role}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_high_risk}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.is_catchall}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.mailboxvalidator_score}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.time_taken}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          {item.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </>
    );
}
