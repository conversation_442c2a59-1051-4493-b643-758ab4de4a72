/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { Drawer, Modal, Input, Tooltip, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  PoundOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  ShoppingOutlined,
  FieldTimeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleFilled,
  MailOutlined,
  HomeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  addEmailAddress,
  getJobs,
  syncJobToBullhorn,
} from '../../services/jobs';
import JobDetail from '../JobDetail';
import JobSubmissionModal from '../BullHorn/BullHornJobSubmissionModal';
const { confirm } = Modal;

function CardComponent({
  job,
  setSavedSearchData,
  savedSearchData,
  method,
  page,
  searchData,
  setSearchLoading,
  setViewData,
}) {
  const [view, setView] = useState('mobile');
  const [open, setOpen] = useState(false);
  const [isEdit, setEdit] = useState(true);
  const [isModalOpen, setModalOpen] = useState(false);
  const [isSyncingJob, setSyncingJob] = useState(false);
  const [emailAddress, setEamilAddress] = useState({
    email: '',
    address: '',
  });

  function useOutsideAlerter(ref) {
    useEffect(() => {
      /**
       * Alert if clicked on outside of element
       */
      function handleClickOutside(event) {
        if (ref.current && !ref.current.contains(event.target)) {
          setOpen(false);
        }
      }
      // Bind the event listener
      if (open) document.addEventListener('mousedown', handleClickOutside);
      return () => {
        // Unbind the event listener on clean up
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [ref, open]);
  }

  React.useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200 ? setView('mobile') : setView('website')
    );
  }, []);
  const wrapperRef = useRef(null);
  useOutsideAlerter(wrapperRef);

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  const showConfirm = () => {
    confirm({
      title: 'Do you want to report as an Agency?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        setSearchLoading(true);
        setSavedSearchData(
          savedSearchData.map((item) =>
            item.id == searchData.id
              ? {
                  ...item,
                  agencies: [...item.agencies, job.company],
                  page: page,
                }
              : { ...item, page: page }
          )
        );
        localStorage.setItem(
          'savedSearch',
          JSON.stringify(
            savedSearchData.map((item) =>
              item.id == searchData.id
                ? {
                    ...item,
                    agencies: [...item.agencies, job.company],
                    page: page,
                  }
                : { ...item, page: page }
            )
          )
        );

        getJobs({
          searchData: {
            ...searchData,
            agencies: [...searchData.agencies, job.company],
          },
        }).then((data) => {
          setSearchLoading(false);
          const jobData = data.data.jobs;

          setSavedSearchData(
            savedSearchData.map((item) =>
              item.id == searchData.id
                ? {
                    ...item,
                    agencies: [...item.agencies, job.company],
                    data: jobData,
                    totalCount: data.data.totalCount,
                    page: page,
                  }
                : { ...item, page: page }
            )
          );

          localStorage.setItem(
            'savedSearch',
            JSON.stringify(
              savedSearchData.map((item) =>
                item.id == searchData.id
                  ? {
                      ...item,
                      agencies: [...item.agencies, job.company],
                      data: jobData,
                      totalCount: data.data.totalCount,
                      page: page,
                    }
                  : { ...item, page: page }
              )
            )
          );
          if (localStorage.getItem('savedAgency') == null)
            localStorage.setItem('savedAgency', JSON.stringify([]));

          getJobs({
            searchData: {
              ...searchData,
              companies: job.company,
            },
          }).then((data1) => {
            setSearchLoading(false);
            localStorage.setItem(
              'savedAgency',
              JSON.stringify([
                ...JSON.parse(localStorage.getItem('savedAgency')),
                {
                  ...savedSearchData.filter(
                    (item) => item.id == searchData.id
                  )[0],
                  companies: job.company,
                  page: page,
                  data: data1.data.jobs,
                  totalCount: data1.data.totalCount,
                },
              ])
            );
          });
        });
      },
      onCancel() {
        return;
      },
    });
  };

  const onSendClick = () => {
    setModalOpen(true);
  };

  const onInputChange = (e, id) => {
    const { name, value } = e.target;
    setEamilAddress({ ...emailAddress, [name]: value });
    // setSavedSearchData([
    //   ...savedSearchData,
    //   {
    //     ...job,
    // data: job.data.map((item) =>
    //   item.job_id == id ? { ...item, ...emailAddress } : item
    // ),
    //   },
    // ]);
    setViewData([
      {
        ...searchData,
        data: searchData.data.map((item) =>
          item.job_id == job.job_id ? { ...item, ...emailAddress } : item
        ),
      },
    ]);
  };

  const onSaveClick = (job_id) => {
    setEdit(!isEdit);
    if (!isEdit) {
      addEmailAddress({
        email: emailAddress.email,
        address: emailAddress.address,
        id: job_id,
      }).then((res) => {});

      localStorage.setItem(
        'savedSync',
        JSON.stringify(
          savedSearchData.map((item) =>
            item.id != searchData.id
              ? item
              : {
                  ...item,
                  data: item.data.map((item1) =>
                    item1.job_id == job_id
                      ? { ...item1, ...emailAddress }
                      : item1
                  ),
                }
          )
        )
      );
    }
  };

  const handleModalOk = async () => {
    setSyncingJob(true);
    await syncJobToBullhorn(job.job_id);
    setModalOpen(false);
    setSyncingJob(false);
  };
  if (job === undefined) return <></>;
  else
    return (
      <div className="py-1 sm:px-10 px-2 max-w-7xl cursor-pointer">
        {isModalOpen && <JobSubmissionModal
          job={job}
          isModalVisible={isModalOpen}
          setIsModalVisible={setModalOpen}
        />}
        {view === 'website' && (
          <Drawer
            title="Job Detail"
            placement="right"
            onClose={onClose}
            open={open}
            width="70%"
          >
            <JobDetail job={job} />
          </Drawer>
        )}
        {view === 'mobile' && (
          <Drawer
            title="Job Detail"
            placement="right"
            onClose={onClose}
            open={open}
            width="100%"
          >
            <JobDetail job={job} />
          </Drawer>
        )}

        <div className="w-full bg-white py-5 sm:px-10 px-5 items-center drop-shadow-2xl rounded-lg dark:text-white dark:bg-gray-800">
          <div className=" w-full">
            <div className="grid grid-cols-8 gap-4">
              <h1 className="sm:col-span-4 col-span-7 text-darkCyan font-bold pt-1 text-lg">
                {job.jobtitle}
              </h1>
              {method == 'sync' && (
                <div className="flex sm:hidden col-span-1 justify-end">
                  <div onClick={onSendClick}>
                    <CheckCircleOutlined
                      style={{ fontSize: '20px', color: '#0cb61f' }}
                    />
                  </div>
                  <div className="ms-1">
                    <CloseCircleOutlined
                      style={{ fontSize: '20px', color: '#c31406' }}
                    />
                  </div>
                </div>
              )}
              <div className="sm:col-span-3 flex mt-[5px] col-span-7">
                <div className="pr-1 flex justify-center">
                  <div className="">{`${job.company}`}</div>
                </div>
              </div>
              {method == 'sync' && (
                <div className="sm:flex hidden col-span-1 justify-end">
                  <div onClick={onSendClick}>
                    <CheckCircleOutlined
                      style={{ fontSize: '20px', color: '#0cb61f' }}
                    />
                  </div>
                  <div className="ms-1">
                    <CloseCircleOutlined
                      style={{ fontSize: '20px', color: '#c31406' }}
                    />
                  </div>
                </div>
              )}
            </div>
            <div onClick={showDrawer}>{`${
              job.description != null ? job.description?.slice(0, 500) : ''
            }...`}</div>
            <div className="sm:flex">
              <div className="flex mt-3">
                <div className="pr-1 flex justify-center mt-[2px]">
                  <PoundOutlined />
                </div>
                {`${job.salary}`}
              </div>
              <div className="flex mt-3">
                <div className="pr-1 flex justify-center mt-[2px] lg:ps-3">
                  <ShoppingOutlined />
                </div>
                {`${job.jobtype}`}
              </div>
            </div>
            <div className="sm:flex gap-3 text-md text-darkCyan">
              <div className="flex mt-3">
                <div className="pr-1 flex justify-center mt-[2px]">
                  <FileTextOutlined />
                </div>
                {`${job.source}`}
              </div>
              <div className="flex mt-3">
                <div className="pr-1 flex justify-center mt-[2px]">
                  <EnvironmentOutlined />
                </div>
                {`${job?.joblocationcity}`}
              </div>
              <div className="flex mt-3">
                <div className="pr-1 flex justify-center mt-[2px]">
                  <FieldTimeOutlined />
                </div>
                {`${job.posted}`}
              </div>
            </div>
            {method == 'sync' && (
              <div className="sm:flex gap-3 text-md text-darkCyan justify-between text-center">
                <div className="flex mt-3">
                  <div className="pr-2 flex justify-center mt-[6px]">
                    <MailOutlined />
                  </div>
                  {isEdit ? (
                    job.email
                  ) : (
                    <Input
                      className="w-full"
                      disabled={isEdit}
                      placeholder="Enter Business Eamil"
                      name="email"
                      // defaultValue={job.email}
                      value={emailAddress.email}
                      suffix={
                        <Tooltip title="Extra information">
                          <InfoCircleOutlined
                            style={{ color: 'rgba(0,0,0,.45)' }}
                          />
                        </Tooltip>
                      }
                      onChange={(e) => onInputChange(e, job.job_id)}
                    />
                  )}
                </div>
                <div className="flex mt-3 item-center">
                  <div className="pr-2 flex justify-center mt-[6px]">
                    <HomeOutlined />
                  </div>
                  {isEdit ? (
                    job.address
                  ) : (
                    <Input
                      className="w-full"
                      placeholder="Enter Business Address"
                      disabled={isEdit}
                      // defaultValue={job.address}
                      value={emailAddress.address}
                      name="address"
                      suffix={
                        <Tooltip title="Extra information">
                          <InfoCircleOutlined
                            style={{ color: 'rgba(0,0,0,.45)' }}
                          />
                        </Tooltip>
                      }
                      onChange={(e) => onInputChange(e, job.job_id)}
                    />
                  )}
                </div>
                <div
                  className="mt-[16px] inline-block text-cyan-600 hover:text-[#84A6D5]"
                  onClick={() => onSaveClick(job.job_id)}
                >
                  {isEdit ? 'Edit' : 'Save'}
                </div>
              </div>
            )}
          </div>
          <div className="flex justify-end">
            {method == 'search' && (
              <Button
                className="text-black inline-block rounded-md ms-2 px-3 py-1"
                onClick={() => showConfirm(job.job_id)}
              >
                Report as an Agency
              </Button>
            )}
          </div>
        </div>
      </div>
    );
}

export default CardComponent;
