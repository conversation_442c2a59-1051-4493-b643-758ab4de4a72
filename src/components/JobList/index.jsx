/* eslint-disable react/prop-types */
import React from 'react';
import { Pagination, Empty } from 'antd';
import CardComponent from './CardComponent';
import { getJobs } from '../../services/jobs';

function JobList({
  searchData,
  isSearchLoading,
  setSavedSearchData,
  savedSearchData,
  method,
  setSearchLoading,
  setViewData,
}) {
  const onPageChange = (page, pageSize) => {
    setSearchLoading(true);

    const updatedSearchData = {
      ...searchData,
      pageSize: pageSize,
      page: page,
    };
    getJobs({ searchData: updatedSearchData }).then((data) => {
      setSearchLoading(false);
      const jobData = data.data.jobs;
      setViewData([
        {
          ...searchData,
          data: jobData,
          totalCount: data.data.totalCount,
          page: data.data.currentPage,
          pageSize: data.data.pageSize,
        },
      ]);
      localStorage.setItem(
        'savedSearch',
        JSON.stringify(
          savedSearchData.map((item) =>
            item.id === searchData.id
              ? { ...item, data: jobData, page: data.data.currentPage }
              : item
          )
        )
      );
    });
  };

  if (
    isSearchLoading == false &&
    searchData !== undefined &&
    searchData.data !== undefined &&
    searchData.data.length !== 0
  )
    return (
      <main className="font-spartan relative text-left">
        <section className="bg-lightCyan h-full pt-5">
          {searchData.data.map((job) => (
            <div key={job.job_id}>
              <CardComponent
                job={job}
                setSavedSearchData={setSavedSearchData}
                savedSearchData={savedSearchData}
                method={method}
                page={searchData.page}
                searchData={searchData}
                setSearchLoading={setSearchLoading}
                setViewData={setViewData}
              />
            </div>
          ))}
        </section>
        <div className="sm:ms-14 dark:text-white ms-0">
          <Pagination
            className="customized-style-pagination"
            current={searchData.page}
            onChange={onPageChange}
            total={searchData.totalCount}
            pageSize={Number(searchData.pageSize)}
          />
        </div>
      </main>
    );
  else
    return (
      <div className="text-center mt-20">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
}

export default JobList;
