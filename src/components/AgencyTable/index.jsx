/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Drawer, Modal, Empty } from 'antd';
import { useNavigate } from 'react-router-dom';
import JobDetail from '../JobDetail';
import './style.css';
const { confirm } = Modal;

export default function AgencyTable({ agencyData, setAgencyData }) {
  const [open, setOpen] = useState(false);
  const [agencyDetail, setAgencyDetail] = useState([]);
  const showConfirm = (id) => {
    confirm({
      title: 'Do you want to unreport this item?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        const buffer = JSON.parse(localStorage.getItem('savedAgency'));
        setAgencyData(buffer.filter((item) => item.id != id));
        localStorage.setItem(
          'savedAgency',
          JSON.stringify(buffer.filter((item) => item.id != id))
        );
      },
      onCancel() {
        return;
      },
    });
  };
  const navigate = useNavigate();
  const onShowDetail = (id) => {
    navigate(`/reported_agencies/${id}`);
  };

  const onClose = () => {
    setOpen(false);
  };

  if (agencyData == undefined)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  if (agencyData.length === 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  return (
    <div className="flex flex-col">
      <Drawer
        title="Job Detail"
        placement="right"
        onClose={onClose}
        open={open}
        width="100%"
      >
        <JobDetail job={agencyDetail[0]} />
      </Drawer>

      <div className="overflow-x-auto">
        <div className="py-3 align-middle inline-block min-w-full">
          <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#e30075] dark:bg-brandLinear text-white font-[PoppinsMedium]">
                <tr>
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Search Title
                  </th>
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Agency
                  </th>
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    City
                  </th>
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Keywords
                  </th>
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Salary
                  </th>
                  {/* <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Date
                  </th> */}
                  <th className="px-3 py-6 w-[100px] text-left text-xs font-medium text-black uppercase tracking-wider">
                    Counts
                  </th>
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Edit</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:text-white">
                {agencyData.map((job) => (
                  <tr
                    key={job.companies}
                    className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                  >
                    <td
                      className="px-6 py-4 whitespace-nowrap"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="flex items-center">
                        <div className="ml-4">
                          <div className="w-[100px] inline-table text-sm font-medium text-gray-900 dark:text-white">
                            {job.jobTitle}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td
                      className="px-6 py-4 whitespace-nowrap text-sm text-black"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">
                        {job.company}
                      </div>
                    </td>
                    <td
                      className="px-6 py-4 whitespace-nowrap text-sm text-black"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">{job.city}</div>
                    </td>
                    <td
                      className="px-6 py-4 whitespace-nowrap"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">
                        {job.keywords}
                      </div>
                    </td>
                    <td
                      className="px-6 py-4 whitespace-nowrap text-sm text-black"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">
                        {`£${job.minSalary} - £${job.maxSalary}`}
                      </div>
                    </td>
                    {/* <td
                      className="px-6 py-4 whitespace-nowrap text-sm text-black"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">{"post"}</div>
                    </td> */}
                    <td
                      className="px-6 py-4 whitespace-nowrap text-sm text-black"
                      onClick={() => onShowDetail(job.companies)}
                    >
                      <div className="w-[100px] inline-table">
                        {job.totalCount}
                      </div>
                    </td>
                    <td
                      className="px-3 py-4 whitespace-nowrap text-sm font-medium"
                      onClick={() => showConfirm(job.companies)}
                    >
                      <div className="w-[100px] rounded-md font-semibold px-3 py-1 text-black hover:text-white hover:bg-cyan-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        unreport
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
