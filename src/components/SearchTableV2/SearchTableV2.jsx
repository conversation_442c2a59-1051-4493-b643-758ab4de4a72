/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import {
  Table,
  Button,
  notification,
  Modal,
  Drawer,
  Pagination,
  Typography,
  Flex,
  Spin,
  Dropdown,
  Tooltip,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  ArrowDownOutlined,
  DownOutlined,
  EnvironmentOutlined,
  UserOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { createSearchParams, useNavigate } from 'react-router-dom';
import ScraperToggle from './ScraperToggle';
import {
  deleteJobSearchBySearchId,
  postSearch,
  updateSearchById,
} from '../../services/search';
import NewSearchFormComponent from '../SearchActionsV2/NewSearchFormComponent';
import { useForm } from 'react-hook-form';
import { updateKeywordJobSearch } from '../../services/getJson';
import _, { isNaN } from 'lodash';
import { postSync } from '../../services/searchSync';
import NewCloneFormComponent from '../SearchActionsV2/NewCloneFormComponent';
import { countries } from 'country-list-json';
import { useDispatch } from 'react-redux';
import { setIsResetSearch } from '../../store/common';
import { COMMON_STRINGS } from '../../constants/common.constant';

import { RestyleDrawer } from '../SearchActionsV2/SearchFormV2';
import { X } from 'lucide-react';
import clsx from 'clsx';

const useFormDrawer = (
  submitToAPIMethod,
  populateSearchData,
  isClone = false
) => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const handleDrawerOpen = () => {
    setDrawerVisible(true);
  };

  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  const onSubmit = async (payload) => {
    setIsLoading(true);
    try {
      if (!isClone) {
        await submitToAPIMethod(payload.id, payload);
      } else {
        await submitToAPIMethod(payload);
      }
      await populateSearchData();
      setIsLoading(false);
      if (payload?.keywords) await updateKeywordJobSearch(payload?.keywords[0]);
      notification.success({
        message: 'Success',
        description: `Success update job search ${payload.searchName}`,
      });
    } catch (error) {
      setIsLoading(false);
      notification.error({ message: error?.response?.data?.message });
    }
    handleDrawerClose();
  };
  return {
    drawerVisible,
    handleDrawerOpen,
    handleDrawerClose,
    onSubmit,
    isLoading,
  };
};

export const DATE_ADDED_TEXT = {
  last_1_hour: 'Last 1 hour',
  last_one_days: 'Last 1 day',
  last_seven_days: 'Last 7 days',
  last_fourteen_days: 'Last 14 days',
  last_one_month: 'Last one month',
};

const SearchTableV2 = ({
  searches,
  syncId = null,
  populateSearchData = null,
  sortField,
  setSortField,
  sortOrder,
  setSortOrder,
  pagination,
  setPagination,
  isGetAll,
  pinnedData,
  totalJobs = -1,
  isSearching = false,
  selectedRowKeys,
  setSelectedRowKeys,
  isInSearchDetail = false, // used for search detail page
}) => {
  const countryList = countries.map((country) => ({
    text: country.name,
    value: country.name,
  }));

  // console.log("countryList: ", countryList)
  // const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [recordTable, setRecordTable] = useState({});
  const [cloneValue, setCloneValue] = useState();
  const {
    handleSubmit: handleSubmitSearch,
    control: controlSearch,
    setValue,
    getValues,
    reset,
    watch,
  } = useForm();
  const {
    drawerVisible: searchDrawerVisible,
    handleDrawerOpen: handleSearchDrawerOpen,
    handleDrawerClose: handleSearchDrawerClose,
    onSubmit: onSubmitSearchDrawer,
    isLoading: isLoadingSearchDrawer,
  } = useFormDrawer(updateSearchById, populateSearchData);

  const {
    drawerVisible: cloneDrawlerVisible,
    handleDrawerOpen: handleCloneDrawerOpen,
    handleDrawerClose: handleCloneDrawerClose,
    // eslint-disable-next-line no-unused-vars
    onSubmit: onSubmitCloneDrawer,
    isLoading: isLoadingCloneDrawer,
  } = useFormDrawer(postSearch, populateSearchData, true);

  const handleSort = async (field) => {
    let newSortOrder = 'ASC';
    if (field === sortField) {
      newSortOrder = sortOrder === 'ASC' ? 'DESC' : 'ASC';
    }
    setSortField(field);
    setSortOrder(newSortOrder);
  };

  const columns = [
    {
      title: COMMON_STRINGS.STATUS,
      key: 'activeSubprocessIds',
      dataIndex: 'activeSubprocessIds',
      align: 'center',
      render: (text, record) => {
        const newText = record?.activeSubprocessId;
        const isActive = _.isArray(text) ? text?.length > 0 : false;
        const isNewActive = _.isArray(newText) ? newText?.length > 0 : false;
        return (
          <div>
            {isActive || isNewActive ? (
              <span className="px-2 py-1 border-[#9ce9cf] bg-[#e6fff8] text-[#00996d]">
                Active
              </span>
            ) : (
              <span className="px-2 py-1 border-[#e6e9ed] bg-[#eff1f3] text-[#674677]">
                Paused
              </span>
            )}
          </div>
        );
      },
      // sorter: true,
      // sortOrder: sortField === 'active_subprocess_id' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('active_subprocess_id'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'active_subprocess_id' && sortOrder === 'ASC' ? 'asc' : sortField === 'active_subprocess_id' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.SEARCH_NAME,
      dataIndex: 'searchName',
      width: '10%',
      key: 'searchName',
      filterSearch: true,
      // sorter: true,
      // sortOrder: sortField === 'searchName' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('searchName'),
      }),
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div className="font-semibold line-clamp-1" title={text}>
              {text}
            </div>
            {/* <div className="flex gap-1 items-center text-[#7b838b] text-xs italic">
              <UserOutlined />
              <span>{record.adminLevelOneArea || '-'}</span>
            </div> */}
          </div>
        );
      },
      // showSorterTooltip: false,
      // className: `custom-hover-th ${sortField === 'searchName' && sortOrder === 'ASC' ? 'asc' : sortField === 'searchName' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.LOCATION,
      dataIndex: 'location',
      key: 'location',
      responsive: ['lg'],
      filters: countryList,
      onFilter: (value, record) => record?.country?.startsWith(value),
      filterSearch: true,
      // sorter: true,
      // sortOrder: sortField === 'location' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('location'),
      }),
      render: (text, record) => (
        <p className="font-semibold flex gap-2 items-center">
          {/* {record.adminLevelOneArea ? record.adminLevelOneArea + ',' : ''}{' '} */}
          <EnvironmentOutlined />
          {record.country || '-'}
        </p>
      ),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'location' && sortOrder === 'ASC' ? 'asc' : sortField === 'location' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.KEYWORDS,
      dataIndex: 'keywords',
      key: 'keywords',
      // responsive: ['sm'],
      width: '10%',
      // sorter: true,
      // sortOrder: sortField === 'keywords' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('keywords'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'keywords' && sortOrder === 'ASC' ? 'asc' : sortField === 'keywords' && sortOrder === 'DESC' ? 'desc' : ''}`,
      render: (text, record) => {
        if (!text) return;
        const isMultipleKeywords = text?.includes(',');
        const keywordList = isMultipleKeywords ? text.split(',') : [text];
        const displayedKeywordList = keywordList?.slice(0, 3);
        const moreKeywordList = keywordList?.slice(3, keywordList?.length);
        return (
          <div className=" max-w-md">
            {displayedKeywordList.map(
              (keyword) =>
                keyword && (
                  <span className="border border-[#ecf3fb] text-[#194783] rounded-xl px-4 py-1 font-semibold mr-2 whitespace-nowrap mt-2 inline">
                    {keyword}
                  </span>
                )
            )}
            {displayedKeywordList?.length < keywordList?.length && (
              <Tooltip title={moreKeywordList.join(',')}>
                <span className="text-xs italic text-nowrap break-normal">
                  more...
                </span>
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      title: COMMON_STRINGS.SALARY,
      dataIndex: 'salary',
      key: 'salary',
      align: 'center',
      render: (text, record) =>
        `${record.minSalary ? '£' + record.minSalary : ''} - ${record.maxSalary ? '£' + record.maxSalary : ''}`,
      responsive: ['lg'],
      // sorter: true,
      // sortOrder: sortField === 'minSalary' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('minSalary'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'minSalary' && sortOrder === 'ASC' ? 'asc' : sortField === 'minSalary' && sortOrder === 'ASC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.DATE,
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      render: (text, record) =>
        `${record.postedStartDate ? dayjs(record.postedStartDate).format('DD/MM/YYYY') : ''} - ${record.postedEndDate ? dayjs(record.postedEndDate).format('DD/MM/YYYY') : ''}`,
      responsive: ['lg'],
      // sorter: true,
      // sortOrder: sortField === 'postedStartDate' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('postedStartDate'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'postedStartDate' && sortOrder === 'ASC' ? 'asc' : sortField === 'postedStartDate' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.SEARCH_STATUS,
      key: 'scraperStatus',
      dataIndex: 'active_subprocess_id',
      align: 'center',
      render: (text, record) => (
        <ScraperToggle
          jobSearch={record}
          populateSearchData={populateSearchData}
        />
      ),
      // sorter: true,
      // sortOrder: sortField === 'active_subprocess_id' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('active_subprocess_id'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'active_subprocess_id' && sortOrder === 'ASC' ? 'asc' : sortField === 'active_subprocess_id' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.COUNT,
      dataIndex: 'counts',
      key: 'counts',
      align: 'center',
      // sorter: true,
      render: (text, record) => (
        <>
          {/* {isSearching && <Spin />} */}
          {!isSearching && totalJobs > -1
            ? totalJobs
            : text + (pinnedData ? pinnedData?.length : 0)}
        </>
      ),
      // sortOrder: sortField === 'counts' && sortOrder,
      onHeaderCell: () => ({
        onClick: () => handleSort('counts'),
      }),
      showSorterTooltip: false,
      className: `custom-hover-th ${sortField === 'counts' && sortOrder === 'ASC' ? 'asc' : sortField === 'counts' && sortOrder === 'DESC' ? 'desc' : ''}`,
    },
    {
      title: COMMON_STRINGS.ACTION,
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: '5%',
      render: (text, record) => {
        const actionItems = [
          {
            key: 'edit-search',
            label: (
              <Button
                className="w-full Montserrat"
                type="text"
                icon={<EditOutlined />}
                onClick={async (e) => {
                  e.stopPropagation();
                  handleSearchDrawerOpen();
                  setValue('id', record.id);
                  setValue('searchName', record.searchName);
                  setValue(
                    'keywords',
                    record?.keywords?.split(',')?.map((keyword) => ({
                      label: keyword,
                      value: keyword,
                    })) || []
                  );
                  setValue('country', record.country);
                  setValue(
                    'adminLevelOneArea',
                    record?.adminLevelOneArea?.includes(',')
                      ? record?.adminLevelOneArea?.split(',')
                      : [record?.adminLevelOneArea]
                  );
                  setValue(
                    'jobBoards',
                    record.jobBoards ? record.jobBoards.split(',') : []
                  );
                  if (record.postedStartDate && record.postedEndDate)
                    setValue('postedWithin', [
                      dayjs(record.postedStartDate),
                      dayjs(record.postedEndDate),
                    ]);
                  else setValue('postedWithin', null);
                  setValue('minSalary', record.minSalary);
                  setValue('maxSalary', record.maxSalary);
                  setValue('city', record?.city);
                  setValue('datePosted', record.datePosted);
                  setValue(
                    'jobTitles',
                    record?.jobTitles?.map((title) => ({
                      label: title,
                      value: title,
                    })) || []
                  );
                  setValue(
                    'datePostedText',
                    DATE_ADDED_TEXT[record.datePosted]
                  );
                }}
              >
                Edit Search
              </Button>
            ),
          },
          {
            key: 'delete-search',
            label: !isGetAll && (
              <Button
                // danger
                type="text"
                // type="link"
                className="w-full Montserrat"
                icon={<DeleteOutlined />}
                onClick={async (e) => {
                  e.stopPropagation();
                  setRecordTable(record);
                  setDeleteModalOpen(true);
                }}
              >
                Delete Search
              </Button>
            ),
          },
        ];
        if (window?.location?.href?.includes('search')) {
          actionItems.push({
            key: 'clone-search',
            label: (
              <Button
                // danger
                type="text"
                // type="link"
                className="w-full Montserrat"
                icon={<CopyOutlined />}
                onClick={async (e) => {
                  e.stopPropagation();
                  handleCloneDrawerOpen();
                  const handledRecord = {
                    ...record,
                    adminLevelOneArea: record?.adminLevelOneArea?.includes(',')
                      ? record?.adminLevelOneArea?.split(',')
                      : [record?.adminLevelOneArea],
                  };
                  setCloneValue(handledRecord);
                }}
              >
                Clone Search
              </Button>
            ),
          });
        }
        return (
          <Dropdown
            menu={{
              items: actionItems,
            }}
            placement="bottom"
            arrow
          >
            <Button
              onClick={(e) => e.stopPropagation()}
              className="!border-[#b2b8be] flex gap-2 items-center text-[#b2b8be]"
            >
              <p className="Montserrat">Select Action</p>
              <DownOutlined />
            </Button>
          </Dropdown>
          // <Flex justify="center">

          // </Flex>
        );
      },
    },
  ];

  const navigate = useNavigate();
  const rowLink = (record) => ({
    onClick: () => {
      const {
        excludeCompanies = [],
        excludeKeywords = [],
        excludeTitles = [],
      } = record;
      const url = syncId
        ? `/syncs/${syncId}/search/${record.id}`
        : `/search/${record.id}`;
      navigate({
        pathname: url,
        search: createSearchParams({
          ...(excludeCompanies && { excludeCompanies }),
          ...(excludeKeywords && { excludeKeywords }),
          ...(excludeTitles && { excludeTitles }),
        }).toString(),
      });
    },
  });

  const dispatch = useDispatch();

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [confirmLoadingDelete, setConfirmLoadingDelete] = useState(false);
  const handleDelete = async () => {
    try {
      await deleteJobSearchBySearchId(recordTable.id);
      await populateSearchData();
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
      notification.success({
        message: 'Success',
        description: `Success delete job search ${recordTable.searchName}`,
      });
    } catch (error) {
      const errorMessage = error?.response?.data?.message;
      notification.error({
        message: 'Error',
        description: errorMessage,
      });
      setConfirmLoadingDelete(false);
      setDeleteModalOpen(false);
    }
  };
  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
  };

  const handlePagination = (page, pageSize) => {
    setPagination({
      page: page,
      limit: pageSize,
      totalCount: pagination.totalCount,
    });
  };

  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <>
      <Modal
        centered
        width={1000}
        bodyStyle={{ overflowY: 'auto', maxHeight: 'calc(100vh - 200px)' }}
        title="Delete job search"
        open={deleteModalOpen}
        okText={'Confirm'}
        okButtonProps={{
          loading: confirmLoadingDelete,
        }}
        onOk={handleDelete}
        onCancel={handleCancelDelete}
      >
        Are you sure to delete {recordTable.searchName}?
      </Modal>
      <div className={'drawer-container'}>
        {searchDrawerVisible && (
          <Drawer
            title={<span className="Montserrat">Edit Search Criteria</span>}
            width={600}
            height={400}
            onClose={() => {
              reset();
              handleSearchDrawerClose();
            }}
            visible={searchDrawerVisible}
          >
            <NewSearchFormComponent
              searchDrawerVisible={searchDrawerVisible}
              setValue={setValue}
              getValues={getValues}
              watch={watch}
              isLoadingSearchDrawer={isLoadingSearchDrawer}
              onFinish={handleSubmitSearch(async (data) => {
                if (!data?.country) {
                  return notification?.warning({
                    description: 'Countries is required!',
                  });
                }
                const payload = {
                  ...data,
                  adminLevelOneArea: data?.adminLevelOneArea?.join(','),
                  keywords: data?.keywords?.map(
                    (keyword) => keyword?.value || keyword
                  ),
                  jobTitles: data?.jobTitles?.map(
                    (jobTitle) => jobTitle?.value || jobTitle
                  ),
                };
                await onSubmitSearchDrawer(payload);
                dispatch(setIsResetSearch(getValues('datePostedText')));
              })}
              control={controlSearch}
            />
          </Drawer>
        )}
      </div>
      <div className="search-table-new-design-container w-full">
        <Table
          rowSelection={
            window?.location?.href?.includes('syncs') || isInSearchDetail
              ? null
              : rowSelection
          }
          rowKey={(record) => record.id}
          loading={isLoadingSearchDrawer || isSearching}
          dataSource={searches}
          columns={columns}
          className={clsx('custom-table')}
          onRow={rowLink}
          pagination={false}
          rowClassName="custom-row"

          // bordered
        />
      </div>
      {!isGetAll && (
        <Flex className="mt-4" justify="center">
          <Pagination
            defaultCurrent={pagination.page}
            defaultPageSize={pagination.limit}
            total={pagination.totalCount}
            showSizeChanger
            onChange={handlePagination}
          />
        </Flex>
      )}

      <div className={'drawer-container'}>
        <RestyleDrawer
          title={
            <div className="flex items-center justify-between bg-white">
              <h2 className="text-lg font-medium text-gray-900">
                Clone Search
              </h2>
              <button
                onClick={handleCloneDrawerClose}
                className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
          }
          width={400}
          // onClose={handleSearchDrawerClose}
          closable={false}
          destroyOnClose={true}
          visible={cloneDrawlerVisible}
        >
          <NewCloneFormComponent
            onClose={() => {
              handleCloneDrawerClose();
              setCloneValue(null);
            }}
            cloneValue={cloneValue}
            isLoadingSyncDrawer={isLoadingCloneDrawer}
            searches={searches}
            openDrawler={cloneDrawlerVisible}
            setValue={setValue}
            getValues={getValues}
            watch={watch}
            onFinish={handleSubmitSearch(async (data) => {
              delete data?.searchIds;
              delete data?.id;
              const payload = {
                ...data,
                adminLevelOneArea: data?.adminLevelOneArea?.join(','),
                keywords: data?.keywords?.map((keyword) => keyword?.value),
                jobTitles: data?.jobTitles?.map(
                  (jobTitle) => jobTitle?.value || jobTitle
                ),
              };
              await onSubmitCloneDrawer(payload);
              dispatch(setIsResetSearch(getValues('datePostedText')));
              handleCloneDrawerClose();
            })}
            control={controlSearch}
            handleSubmit={handleSubmitSearch}
          />
        </RestyleDrawer>
      </div>

      {/* {isGetAll && (
        <Typography
          style={{
            fontWeight: '500',
            fontSize: '24px',
            paddingTop: '16px',
          }}
        >
          - Filter
        </Typography>
      )} */}
    </>
  );
};

export default SearchTableV2;
