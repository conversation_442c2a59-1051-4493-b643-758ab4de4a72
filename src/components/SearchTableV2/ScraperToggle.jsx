/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { Switch, message } from 'antd';
import { startSearchById, stopSearchById } from '../../services/search';

function ScraperToggle({ jobSearch, disabled, populateSearchData }) {
  const [isChecked, setIsChecked] = useState(false);
  const [isLoading, setLoading] = useState(false);
  useEffect(() => {
    setIsChecked(jobSearch.activeSubprocessId != null || jobSearch.activeSubprocessIds != null);
  }, [jobSearch]);
  const handleToggle = async (checked, event) => {
    setLoading(true);
    event.stopPropagation();
    if (checked) {
      const data = await startSearchById(jobSearch.id);
      if (!data || data?.status !== 200 || data?.data?.result?.error) {
        message.error(data?.message ?? 'Failed to update scraper status');
        return setLoading(false);
      }
    } else {
      const data = await stopSearchById(jobSearch.id);
      if (!data || data?.status !== 200 || data?.data?.result?.error) {
        message.error(data?.message ?? 'Failed to update scraper status');
        return setLoading(false);
      }
    }
    setIsChecked(checked);
    setLoading(false);
    populateSearchData()
  };

  return (
    <Switch
      disabled={disabled ? disabled : isLoading}
      loading={isLoading}
      checked={isChecked}
      onChange={handleToggle}
    />
  );
}

export default ScraperToggle;
