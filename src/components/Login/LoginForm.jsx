/* eslint-disable react/no-unescaped-entities */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { Form, Input, Button, Typography } from 'antd';
import { Controller, useForm } from 'react-hook-form';
import * as LabelPrimitive from '@radix-ui/react-label';
import useBreakpoint from 'antd/lib/grid/hooks/useBreakpoint';
import { useState } from 'react';
import {
  BarChart3,
  ChevronRight,
  Eye,
  EyeOff,
  Lock,
  Mail,
  Users,
  Zap,
} from 'lucide-react';
import { Link } from 'react-router-dom';

const LoginForm = ({ onSubmit }) => {
  const { handleSubmit, control } = useForm();
  const [loading, setLoading] = useState(false);
  const onFinish = async (values) => {
    setLoading(true);
    try {
      await onSubmit(values);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };
  const screens = useBreakpoint();

  useEffect(() => {
    return () => {
      setLoading(false);
    };
  }, []);

  return (
    <div className="min-h-screen bg-white flex">
      {/* Left side - Login form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-8">
        <div className="w-full max-w-md">
          <div className="mb-8 border-b border-gray-100/50 flex-shrink-0">
            <div className="flex items-center space-x-2">
              <div className="relative group">
                <div className="w-7 h-7 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-cyan-500/25 transition-all duration-300">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </div>
              <div className="text-base font-bold gradient-text">
                {/* <h1 className="text-base font-bold bg-gradient-to-r from-cyan-500 via-cyan-600 to-purple-600 bg-clip-text text-transparent"> */}
                ZILEO
                {/* </h1> */}
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              Welcome back
            </h1>
            <p className="text-gray-600 font-medium">
              Sign in to your dashboard
            </p>
          </div>

          <Form onFinish={handleSubmit(onFinish)} className="space-y-5">
            <div className="space-y-2">
              <LabelPrimitive.Root
                htmlFor="email"
                className="text-sm font-medium text-gray-700"
              >
                Email or Username
              </LabelPrimitive.Root>
              <Form.Item name="emailOrUsername">
                <Controller
                  render={({ field }) => (
                    <Input
                      prefix={<Mail className="h-5 w-5 text-gray-400" />}
                      id="email"
                      // type="email"
                      placeholder="Enter your email"
                      {...field}
                      className="h-11 border-gray-300 focus:border-cyan-500 focus:ring-cyan-500"
                    />
                  )}
                  name="emailOrUsername"
                  control={control}
                />
              </Form.Item>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <LabelPrimitive.Root
                  htmlFor="password"
                  className="text-sm font-medium text-gray-700"
                >
                  Password
                </LabelPrimitive.Root>
                <Link
                  href="#"
                  className="text-sm font-medium text-cyan-600 hover:text-cyan-500"
                >
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Form.Item name="password">
                  <Controller
                    render={({ field }) => (
                      <Input.Password
                        prefix={<Lock className="h-5 w-5 text-gray-400" />}
                        iconRender={(showPassword) =>
                          showPassword ? (
                            <EyeOff className="h-5 w-5 " />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )
                        }
                        id="password"
                        type="password"
                        placeholder="Enter your password"
                        className="h-11 border-gray-300 focus:border-cyan-500 focus:ring-cyan-500 text-gray-400 hover:text-gray-600"
                        {...field}
                      />
                    )}
                    name="password"
                    control={control}
                  />
                </Form.Item>
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-300 rounded"
              />
              <LabelPrimitive.Root
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-700"
              >
                Remember me
              </LabelPrimitive.Root>
            </div>

            <Button
              loading={loading}
              htmlType="submit"
              className="w-full h-11 bg-cyan-500 hover:bg-cyan-600 text-white font-medium rounded-md flex items-center gap-3 justify-center"
            >
              Sign in
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </Form>

          <div className="mt-8 text-center text-sm text-gray-500">
            <p>© {new Date().getFullYear()} Zileo. All rights reserved.</p>
          </div>
        </div>
      </div>

      {/* Right side - Features */}
      <div className="hidden lg:block lg:w-1/2 bg-gradient-to-b from-cyan-50 to-white p-8">
        <div className="h-full flex flex-col justify-center max-w-lg mx-auto">
          <h2 className="text-2xl font-semibold text-gray-900 mb-8">
            Transform your recruitment process with ZILEO
          </h2>

          <div className="space-y-8">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 h-12 w-12 rounded-lg bg-cyan-100 flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-cyan-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Find Jobs Efficiently
                </h3>
                <p className="mt-2 text-gray-600">
                  Access a database of job opportunities and match them with
                  your candidate pool using our intelligent matching algorithm.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 h-12 w-12 rounded-lg bg-purple-100 flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Powerful Outreach Tools
                </h3>
                <p className="mt-2 text-gray-600">
                  Connect with contacts and send personalized sequences to
                  engage potential clients with tracking and analytics.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 h-12 w-12 rounded-lg bg-orange-100 flex items-center justify-center">
                <Zap className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Automated Sequences
                </h3>
                <p className="mt-2 text-gray-600">
                  Save 25+ hours with active sequences that automate your
                  recruitment workflow and follow-ups for maximum efficiency.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              "Zileo has transformed our recruitment process, saving us
              countless hours and helping us win more clients."
            </p>
            <p className="mt-2 text-sm font-medium text-gray-900">
              — Director, Henley Morgan
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
