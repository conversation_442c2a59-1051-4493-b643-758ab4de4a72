/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Drawer, Modal, Empty, Select, notification, Spin, Input } from 'antd';
import JobDetail from '../JobDetail';
import routes from '../Sidebar/consts';
import './style.css';
import {
  deleteUser,
  setPermission,
  getAllUsers,
  editUser,
} from '../../services/auth';

const { confirm } = Modal;
const OPTIONS = routes.map((item) => item.name);

export default function AgencyTable({ userView, setUserView, pageData }) {
  const [open, setOpen] = useState(false);
  const [agencyDetail] = useState([]);
  const [isEdit, setEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newName, setNewName] = useState('');
  const [currentId, setCurrentId] = useState('');
  const showConfirm = (id) => {
    confirm({
      title: 'Do you want to delete this item?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        deleteUser({ id }).then((res) => {
          getAllUsers(pageData).then((res) => {
            setUserView(res.data.jobs);
          });
          if (res.data.success)
            notification.open({
              message: 'Success!',
              description: 'Seleted User is deleted successfully!',
            });
        });
      },
      onCancel() {
        return;
      },
    });
  };

  const onClose = () => {
    setOpen(false);
  };
  const onPermChange = (value, id) => {
    setUserView(
      userView.map((item) =>
        item.id == id ? { ...item, permission: value } : item
      )
    );
  };

  const onUserChange = (id) => {
    setCurrentId(id);
    setEdit(true);
  };

  const onSaveChange = (id) => {
    setEdit(false);
    setLoading(true);
    editUser({ id: id, username: newName }).then(() => {
      getAllUsers(pageData).then((res) => {
        setUserView(res.data.jobs);
        setLoading(false);
        if (res.data.success)
          notification.open({
            message: 'Success!',
            description: 'Username is updated successfully!',
          });
      });
    });
  };

  const onEditChange = (e) => {
    setNewName(e.target.value);
  };
  const onSavePerm = (id) => {
    setLoading(true);
    let perm = userView.filter((item) => item.id == id)[0].permission;
    setPermission({ id: id, permission: perm }).then(() => setLoading(false));
  };

  const [selectedItems] = useState([]);
  const filteredOptions = OPTIONS.filter((o) => !selectedItems.includes(o));

  if (loading)
    return (
      <div className="text-center mt-40">
        <Spin size="large" />
      </div>
    );
  if (userView.length === 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  else
    return (
      <div className="flex flex-col">
        {agencyDetail.length !== 0 && (
          <Drawer
            title="Job Detail"
            placement="right"
            onClose={onClose}
            open={open}
            width="100%"
          >
            <JobDetail job={agencyDetail[0]} />
          </Drawer>
        )}
        <div className="overflow-x-auto">
          <div className="py-3 text-center inline-block min-w-full">
            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-[#e30075] dark:bg-brandLinear text-white text-center font-[PoppinsMedium]">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table text-center">
                        No
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table text-center">
                        Username
                      </div>
                    </th>
                    {/* <th
                      scope="col"
                      className="px-4 py-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Password</div>
                    </th> */}
                    <th
                      scope="col"
                      className="px-4 py-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Permissions</div>
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <div className="inline-table text-xs font-medium uppercase w-[150px]">
                        Action
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:text-white">
                  {userView.map((user, index) => (
                    <tr
                      key={user.id}
                      className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="text-sm font-medium text-gray-900 dark:text-white text-center">
                          {index + 1}
                        </div>
                      </td>
                      <td className="px-2 py-4 whitespace-nowrap">
                        <div
                          className="w-[150px] text-sm font-medium text-gray-900 dark:text-white text-center"
                          onClick={() => onUserChange(user.id)}
                        >
                          {isEdit && user.id == currentId ? (
                            <Input
                              placeholder="Type new name"
                              className="w-full"
                              onChange={(e) => onEditChange(e, user.id)}
                              value={newName}
                              defaultValue={user.username}
                            />
                          ) : (
                            user.username
                          )}
                        </div>
                      </td>
                      {/* <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {user.password}
                        </div>
                      </td> */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black flex justify-center">
                        <div className="w-[500px] flex items-center">
                          <Select
                            mode="multiple"
                            placeholder="Inserted are removed"
                            value={user.permission}
                            onChange={(e) => onPermChange(e, user.id)}
                            style={{
                              width: '100%',
                            }}
                            options={filteredOptions.map((item) => ({
                              value: item,
                              label: item,
                            }))}
                          />

                          <div
                            className="text-black rounded-md pt-4 px-3 bg-cyan-600 text-white font-semibold h-full focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                            onClick={() => onSavePerm(user.id)}
                          >
                            Save
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm font-medium">
                        <div className="flex justify-between">
                          <div
                            className="rounded-md bg-[#439229] text-white font-semibold px-4 py-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                            onClick={() => onSaveChange(user.id)}
                          >
                            Save
                          </div>

                          <div
                            className="rounded-md bg-[#9e0505] text-white font-semibold px-4 py-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                            onClick={() => showConfirm(user.id)}
                          >
                            Delete
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    );
}
