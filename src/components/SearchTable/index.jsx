/* eslint-disable react/jsx-key */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { DownOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { Modal, Switch, Empty, notification, Dropdown, Space } from 'antd';
import './style.css';
import { startScraper, saveKeywords } from '../../services/jobs';
const { confirm } = Modal;
const Context = React.createContext({
  name: 'Default',
});

export default function SearchTable({ jobData, setSavedSearchData }) {
  const [api, contextHolder] = notification.useNotification();
  const showConfirm = (id) => {
    confirm({
      title: 'Do you Want to delete these items?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        // const buffer = JSON.parse(localStorage.getItem("savedSearch"));
        setSavedSearchData(jobData.filter((item) => item.id != id));
        localStorage.setItem(
          'savedSearch',
          JSON.stringify(jobData.filter((item) => item.id != id))
        );
      },
      onCancel() {
        return;
      },
    });
  };

  const navigate = useNavigate();
  const onShowDetail = (id) => {
    navigate(`/search/${id}`);
  };

  const onStatusChange = (checked, id) => {
    const { keywords } = jobData.filter((item) => item.id == id)[0];
    const data = JSON.parse(JSON.stringify({ keywords }));
    if (checked) {
      saveKeywords(data)
        .then((res) => {
          setTimeout(() => {
            startScraper({ status: 'on', keywords: keywords.join(',') })
              .then((res) => {
                api.open({
                  message: 'Scraper Turn on',
                  description: 'Scraper starts working...',
                  duration: 1,
                });
              })
              .catch((err) => {
                return err;
              });
          }, 1000); // Delay of 1 second (1000 milliseconds)
        })
        .catch((err) => {});
    } else {
      startScraper({ status: 'off' });
    }
    setSavedSearchData(
      jobData.map((item) =>
        item.id == id ? { ...item, status: checked } : item
      )
    );
  };

  if (jobData.length == 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  else
    return (
      <>
        {contextHolder}
        <div className="flex flex-col text-center justify-center w-[100%]">
          <div className="overflow-x-auto">
            <div className="py-3 align-middle inline-block min-w-full">
              <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                <table className="min-w-full divide-y divide-gray-200 table-fixed">
                  <thead className="bg-[#0080FE] dark:bg-brandLinear text-white font-[PoppinsMedium]">
                    <tr>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Job Title
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Location
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Keywords
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Salary
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Date
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Counts
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Edit</span>
                      </th>
                      <th className="py-2 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                        Scraper Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200  dark:bg-gray-800 dark:text-white">
                    {jobData.map((job) => (
                      <tr
                        key={job.id}
                        className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                      >
                        <td
                          className="px-6 py-4 whitespace-nowrap"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="flex items-center">
                            <div className="ml-4">
                              <div className="w-[100px] inline-table text-sm font-medium text-gray-900 dark:text-white">
                                {job.jobTitle}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm text-black"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="w-[100px] inline-table">
                            {job.city ? job.city : '-'}
                          </div>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="w-[100px] inline-table">
                            {job.keywords.length != 0
                              ? job.keywords.map((item) => (
                                  <div
                                    className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full bg-[#fff2e8] text-green-800"
                                  >
                                    {item}
                                  </div>
                                ))
                              : ''}
                          </div>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm text-black"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="w-[100px] inline-table">{`${job.minSalary ? `£${job.minSalary}` : ''} - ${job.maxSalary ? `£${job.maxSalary}` : ''}`}</div>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm text-black"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="w-[100px] inline-table">
                            {`${job.posted[0]} - ${job.posted[1]}`}
                          </div>
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm text-black"
                          onClick={() => onShowDetail(job.id)}
                        >
                          <div className="w-[100px] inline-table">
                            {job.totalCount}
                          </div>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm font-medium">
                          <Dropdown
                            menu={{
                              items: [
                                {
                                  key: '1',
                                  label: (
                                    <a
                                      onClick={(e) => {
                                        e.preventDefault();
                                        showConfirm(job.id);
                                      }}
                                    >
                                      Delete
                                    </a>
                                  ),
                                },
                                {
                                  key: '2',
                                  label: (
                                    <a
                                      onClick={(e) => {
                                        e.preventDefault();
                                      }}
                                    >
                                      Copy Search
                                    </a>
                                  ),
                                },
                              ],
                            }}
                          >
                            <Space>
                              Select action
                              <DownOutlined />
                            </Space>
                          </Dropdown>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                          <div className="w-[100px] inline-table">
                            <Switch
                              checked={job.status}
                              onChange={(checked) =>
                                onStatusChange(checked, job.id)
                              }
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </>
    );
}
