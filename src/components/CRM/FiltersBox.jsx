import {
  BgColorsOutlined,
  CloseOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Button, Form, Input, Select, Space } from 'antd';
import { useEffect, useState } from 'react';

const FiltersBox = ({
  fieldItems,
  closeFilterModal,
  handleSubmit,
  defaultFilters,
}) => {
  const [filtersWatch, setFiltersWatch] = useState([]);
  const [form] = Form.useForm();
  const filters = Form.useWatch('filters', form);

  const onFinish = async (values) => {
    let payload = {};
    if (values?.filters && values?.filters?.length > 0) {
      for await (const filter of values?.filters) {
        let value = filter?.value;
        if (!value) return;
        payload[filter?.field] = value;
      }
    }
    console.log('onFinish values: ', values);
    handleSubmit(values);
    closeFilterModal();
  };

  const handleChangeFieldName = (key) => (value) => {
    // setValue(name, value);
    const newFiltersList = filtersWatch.map((filter) => {
      if (filter?.key === key) {
        return { ...filter, value };
      } else {
        return { ...filter };
      }
    });
    setFiltersWatch(newFiltersList);
  };

  const checkValueInput = (index) => {
    return (
      <Input
        style={{
          width: 260,
        }}
      />
    );
  };

  useEffect(() => {
    form.setFieldsValue({ filters: defaultFilters });
  }, [defaultFilters]);

  const handleClearFilter = () => {
    form.resetFields();
    setFiltersWatch([]);
    closeFilterModal();
    handleSubmit({filters: []})
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <div className="border p-5 rounded-lg">
          <div className="text-base font-medium pb-2">
            Add Filters (condition <p className="font-bold inline">AND </p>
            condition)
          </div>
          <Form
            form={form}
            name="dynamic_form_nest_item"
            onFinish={onFinish}
            autoComplete="off"
            className="flex flex-col gap-2"
          >
            <Form.List name="filters">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <Space
                      key={key}
                      style={{
                        display: 'flex',
                      }}
                      align="baseline"
                    >
                      <Form.Item
                        {...restField}
                        name={[name, 'field']}
                        rules={[
                          {
                            required: true,
                            message: 'Please choose a field name',
                          },
                        ]}
                      >
                        <Select
                          showSearch
                          style={{
                            width: 220,
                          }}
                          placeholder="Search to Select"
                          optionFilterProp="children"
                          filterOption={(input, option) =>
                            (option?.label ?? '').includes(input)
                          }
                          filterSort={(optionA, optionB) =>
                            (optionA?.label ?? '')
                              .toLowerCase()
                              .localeCompare(
                                (optionB?.label ?? '').toLowerCase()
                              )
                          }
                          options={fieldItems}
                          onChange={handleChangeFieldName(key)}
                        />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[
                          {
                            required: true,
                            message: 'Missing value',
                          },
                        ]}
                      >
                        {checkValueInput(index)}
                      </Form.Item>
                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                    >
                      Add a Field to Filter
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>

            <div className="w-full grid grid-cols-2 gap-2">
              <Button
                htmlType="button"
                // disabled={filtersWatch?.length === 0 || !filtersWatch}
                onClick={handleClearFilter}
                icon={<CloseOutlined />}
                danger
              >
                Clear
              </Button>
              <Button
                // disabled={filters?.length === 0}
                htmlType="submit"
                icon={<BgColorsOutlined />}
              >
                Apply Filters
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default FiltersBox;
