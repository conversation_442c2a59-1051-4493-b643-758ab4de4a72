import { Button, Col, Form, Input, Row, Select, Upload } from 'antd';
import {
  BasicFormWrapper,
  HorizontalFormStyleWrap,
} from '../../containers/CRM/styled';
import { Cards } from '../Cards';
import {
  BranchesOutlined,
  CloudUploadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { tagRender } from '../SearchDetailV2/NewSearchFilterComponent';
import { MODE_CONSTANTS } from '../../constants/common.constant';
import { useEffect, useState } from 'react';
import { getCRMTagsByUser } from '../../services/crm/tag';

const BaseForm = ({ mode }) => {
  const [tagsOptions, setTagsOptions] = useState([]);
  const [loading, setLoading] = useState(true);

  const uploadImage = (options) => {
    const { onSuccess, file } = options;
    onSuccess(file);
  };

  const getData = async () => {
    setLoading(true);
    try {
      // const { data } = await getCRMTagsByOrg(organizationId);
      const { data } = await getCRMTagsByUser();
      if (data?.result?.length > 0) {
        const newData = data?.result?.map((item) => ({
          label: item?.name,
          value: item?.id,
        }));
        setTagsOptions([...newData]);
      } else {
        setDataSource([]);
      }
      setLoading(false);
      console.log('data: ', data);
    } catch (error) {
      setLoading(false);
      console.log('error: ', error);
    }
  };

  useEffect(() => {
    getData();
  }, []);
  return (
    <>
      {/* {mode !== MODE_CONSTANTS.EDIT && (
        <Row align="middle" className="pb-4 border-b">
          <Col lg={8} md={9} xs={24}>
            <label htmlFor="notes">
              <div className="flex flex-col">
                <span>Notes</span>
                <span className="text-xs opacity-60">
                  Add any relevant details, thoughts, or observations here.
                </span>
              </div>
            </label>
          </Col>
          <Col lg={16} md={15} xs={24}>
            <Form.Item name="notes">
              <Input.TextArea rows={4} placeholder="Enter something..." />
            </Form.Item>
          </Col>
        </Row>
      )}

      {mode !== MODE_CONSTANTS.EDIT && (
        <Row align="middle" className="pb-4 border-b">
          <Col lg={8} md={9} xs={24}>
            <label htmlFor="emailNSequence">
              <div className="flex flex-col">
                <span>Emails & Sequence</span>
                <span className="text-xs opacity-60">
                  Manage email communication and automated follow-up sequences.
                </span>
              </div>
            </label>
          </Col>
          <Col lg={16} md={15} xs={24}>
            <Form.Item name="emailNSequence">
              <Input placeholder="Set emails and sequence..." />
            </Form.Item>
          </Col>
        </Row>
      )}

      {mode !== MODE_CONSTANTS.EDIT && (
        <Row align="middle" className="pb-4 border-b">
          <Col lg={8} md={9} xs={24}>
            <label htmlFor="files">
              <div className="flex flex-col">
                <span>Files</span>
                <span className="text-xs opacity-60">
                  Upload and store documents, images, or other relevant
                  attachments.
                </span>
              </div>
            </label>
          </Col>
          <Col lg={16} md={15} xs={24}>
            <Form.Item name="files">
              <Upload.Dragger
                customRequest={uploadImage}
                name="files"
                action=""
              >
                <div className="flex flex-col items-center gap-4">
                  <CloudUploadOutlined className="text-3xl font-bold" />
                  <div>
                    <span className="font-medium text-cyan-600">
                      Click to upload
                    </span>{' '}
                    or <span>drag and drop</span>
                  </div>
                </div>
              </Upload.Dragger>
            </Form.Item>
          </Col>
        </Row>
      )} */}

      <Row align="middle" className="pb-4 border-b">
        <Col lg={8} md={9} xs={24}>
          <label htmlFor="tags">
            <div className="flex flex-col">
              <span>Tags</span>
              <span className="text-xs opacity-60">
                Categorize and organize items using keywords for easy filtering.
              </span>
            </div>
          </label>
        </Col>
        <Col lg={16} md={15} xs={24}>
          <Form.Item name="tags">
            <Select
              loading={loading}
              tagRender={(props) =>
                tagRender({ ...props, tagColor: 'tag-cyan' })
              }
              optionFilterProp="label"
              //   size="large"
              mode="multiple"
              options={tagsOptions}
              style={{
                width: '100%',
              }}
              placeholder="Add your tags..."
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default BaseForm;
