import { useEffect, useState } from 'react';
import { ActivitiesWrapper } from '../../containers/CRM/styled';
import dayjs from 'dayjs';
import { Drawer, Table, Tag } from 'antd';
import { getAllSequences } from '../../services/crm/sequences';
import { isArray } from 'lodash';
import TagStatus from '../TagStatus';
import { MAIL_STATUS_COLOR } from '../BullHorn/EmailtriggerStep';
import ReadOnlySequence from '../ReadOnlySequence';

const initialPagination = {
  current: 1,
  pageSize: 10,
  total: 0,
};

const Sequences = ({ resourceId, resourceName }) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState(initialPagination);
  const [open, setOpen] = useState(false);
  const [selectedSequenceId, setSelectedSequenceId] = useState('');

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const getData = async (submitPage) => {
    setLoading(true);
    try {
      const payload = {
        entity: resourceName,
        entityId: resourceId,
        page: submitPage || pagination?.current,
        limit: pagination?.pageSize,
      };
      const { data } = await getAllSequences(payload);
      if (data?.result?.sequences?.length > 0) {
        const sequences = data?.result?.sequences;
        setDataSource([...sequences]);
      }

      const paginationTemp = data?.result?.pagination || null;
      if (paginationTemp) {
        setPagination({
          ...pagination,
          current: paginationTemp?.page,
          total: paginationTemp?.total,
        });
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    getData();
  }, [resourceId]);

  const columns = [
    {
      title: 'Date Sent',
      dataIndex: 'sentAt',
      key: 'sentAt',
      width: '10%',
      render: (text, record) => {
        return (
          <div className="font-medium line-clamp-1 max-w-xs" title={text}>
            {dayjs(text).format('DD/MM/YYYY')}
          </div>
        );
      },
    },
    {
      title: 'Sequence Name',
      dataIndex: 'sequence',
      key: 'sequence',
      width: '40%',
      render: (sequence, record) => {
        const text = sequence?.name || '';
        return (
          <div
            className="font-semibold text-cyan-600 line-clamp-1 max-w-md"
            title={text}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: 'Total Steps',
      dataIndex: 'totalSteps',
      key: 'totalSteps',
      width: '10%',
      align: 'center',
      render: (text, record) => {
        return (
          <div
            className="font-semibold text-cyan-600 line-clamp-1 flex justify-center w-full"
            title={text}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: 'From',
      dataIndex: 'fromEmail',
      key: 'fromEmail',
      width: '20%',
      render: (text, record) => {
        return (
          <div className="font-medium line-clamp-1 max-w-xs" title={text}>
            {text}
          </div>
        );
      },
    },
    // {
    //   title: 'To',
    //   dataIndex: 'toEmail',
    //   key: 'toEmail',
    //   width: '20%',
    //   render: (tos, record) => {
    //     return (isArray(tos) && tos?.length) > 0 ? (
    //       <div className="font-medium text-cyan-600 line-clamp-1">
    //         {tos?.join(', ')}
    //       </div>
    //     ) : (
    //       <div className="font-medium text-cyan-600 line-clamp-1">{tos}</div>
    //     );
    //   },
    // },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '20%',
      render: (status, record) => {
        const sequenceData = record?.sequence || null;
        return (
          <div className="flex gap-4">
            <TagStatus
              record={{
                status: sequenceData?.status,
                isMarkedAsCompleted: sequenceData?.isMarkedAsCompleted,
              }}
              sequenceChange={[]}
            />
          </div>
        );
      },
    },
  ];

  const handlePagination = (page) => {
    setPagination({
      ...pagination,
      current: page,
    });
    getData(page);
  };

  return (
    <ActivitiesWrapper>
      <div className="search-table-new-design-container">
        <Table
          className="customized-style-pagination w-full"
          loading={loading}
          size="small"
          dataSource={dataSource}
          columns={columns}
          onRow={(record) => {
            return {
              onClick: () => {
                // handleClick(record);
                const sequenceId = record?.sequence?.id || null;
                console.log('Sequence ID:', sequenceId);
                setSelectedSequenceId(sequenceId);
                showDrawer();
              },
              style: { cursor: 'pointer' },
            };
          }}
          pagination={{
            ...pagination,
            showSizeChanger: false,
            onChange: handlePagination,
            showTotal: (total) => `Total ${total} Sequences`,
          }}
        />
      </div>
      <Drawer
        title="Sequence Summary"
        onClose={onClose}
        open={open}
        width={'30vw'}
      >
        <ReadOnlySequence sequenceId={selectedSequenceId} />
      </Drawer>
    </ActivitiesWrapper>
  );
};

export default Sequences;
