import { MailFilled } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { getLinkS3 } from '../../services/aws';
import { <PERSON><PERSON>, Collapse, Spin } from 'antd';

const EmailItemView = ({ emailProp, onClose }) => {
  const {
    subject = '',
    toEmail = '',
    fromEmail = '',
    attachments = [],
    sequence = null,
    sentAt = '',
    signatureImages = [],
    replies = [],
    // content,
  } = emailProp;

  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleMappingData = async () => {
    setLoading(true);
    try {
      const listSigImageIds = signatureImages?.map((item) => item?.imgId) || [];
      const listSigImageUrl = await Promise.all(
        listSigImageIds.map(async (imageId) => {
          try {
            const { data } = await getLinkS3(imageId);
            return data;
          } catch (error) {
            return '';
          }
        })
      );

      let newContent = emailProp?.content;
      listSigImageUrl.forEach((url, index) => {
        newContent = newContent.replaceAll(`cid:SIGNATURE-${index}`, url);
      });

      setLoading(false);
      setContent(newContent);
    } catch (error) {
      console.error('Error fetching signature images:', error);
      setContent(emailProp?.content);
      setLoading(false);
    }
  };

  const handleDownloadAttachments = async () => {
    setLoading(true);
    try {
      const listAttachmentIds = attachments?.map((item) => item?.fileId) || [];
      const listAttachmentUrl = await Promise.all(
        listAttachmentIds.map(async (fileId) => {
          try {
            const { data } = await getLinkS3(fileId);
            return data;
          } catch (error) {
            return '';
          }
        })
      );

      listAttachmentUrl.forEach((url) => {
        if (url) {
          const link = document.createElement('a');
          link.href = url;
          link.download = ''; // Optional: Set a default filename if needed
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      });
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleMappingData();
  }, [emailProp?.content]);

  return (
    <div>
      <div className="flex justify-between mb-4 pb-4 px-2 pt-2 border-b border-gray-300 ">
        <div className="flex items-center gap-4">
          <MailFilled className="text-2xl" />
          <h2>{sequence?.name || ''}</h2>
        </div>
        {attachments?.length > 0 && (
          <div className="flex items-center gap-2">
            <Button
              type="text"
              loading={loading}
              onClick={handleDownloadAttachments}
              className="text-sm font-medium text-cyan-600 hover:underline cursor-pointer"
            >
              Download {attachments?.length} attachments
            </Button>
          </div>
        )}
      </div>
      <email>
        <ul className="mb-4">
          <li>
            <strong>From: </strong>
            <a className="text-cyan-600 font-medium">
              <span>&lt;{fromEmail}&gt;</span> <span>(Contact)</span>
            </a>
          </li>
          <li>
            <strong>Sent: </strong>
            <span>{dayjs(sentAt).format('hh:mm:ss DD/MM/YYYY')}</span>
          </li>
          <li>
            <strong>To: </strong>
            <a className="text-cyan-600 font-medium">
              <span>&lt;{toEmail}&gt;</span>
            </a>
          </li>
          <li>
            <strong>Subject: </strong>
            <span>{subject}</span>
          </li>
        </ul>
        <p>
          <div
            dangerouslySetInnerHTML={{
              __html: content,
            }}
            className="WordSection1"
          ></div>
        </p>
      </email>
      {replies?.length > 0 && (
        <div className="mt-4">
          {replies?.map((reply, index) => (
            <Collapse
              rootClassName={'active-customized-collapse'}
              expandIconPosition="end"
              className="w-full"
              accordion
              items={[
                {
                  key: index,
                  label: (
                    <div>
                      Reply from{' '}
                      <span className="text-cyan-600 font-semibold">
                        {reply?.fromEmail}
                      </span>
                    </div>
                  ),
                  children: (
                    <div>
                      <EmailItemView emailProp={reply} />
                    </div>
                  ),
                },
              ]}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default EmailItemView;
