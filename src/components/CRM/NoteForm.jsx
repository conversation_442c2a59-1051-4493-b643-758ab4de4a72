import { useEffect, useState } from 'react';
import { Button, Input, Select } from 'antd';
import { ACTION_NOTE } from '../JobsLeads/ViewVacancyDetail/ShortListTab/ModalEditEmail';

const NoteForm = ({ note, handleSubmit, handleCancel }) => {
  const [noteData, setNoteData] = useState({
    action: ACTION_NOTE[0]?.value,
    title: '',
    content: '',
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setNoteData({ ...note });
  }, [note]);

  return (
    <div className=" grid grid-cols-1">
      <div className="w-full">
        <div className="custom-vacancy-form">
          Action <span style={{ color: 'red' }}>*</span>
          <Select
            options={ACTION_NOTE}
            onSelect={(value) => setNoteData({ ...noteData, action: value })}
            value={noteData.action}
            placeholder="Select action"
          />
        </div>
        <div className="custom-vacancy-form mt-5">
          Title <span style={{ color: 'red' }}>*</span>
          <Input
            onChange={(e) =>
              setNoteData({ ...noteData, title: e.target.value })
            }
            placeholder="Enter title"
            value={noteData.title}
          />
        </div>
        <div className="mt-5">
          <div>
            Note <span style={{ color: 'red' }}>*</span>
          </div>
          <Input.TextArea
            placeholder="Enter note..."
            onChange={(e) =>
              setNoteData({ ...noteData, content: e.target.value })
            }
            value={noteData.content}
            rows={7}
          />
        </div>
      </div>
      <div className="flex justify-end gap-3 mt-5 col-span-1">
        <Button onClick={handleCancel} type="default">
          Cancel
        </Button>
        <Button
          loading={loading}
          disabled={!(noteData?.action && noteData?.title && noteData?.content)}
          type="primary"
          onClick={() => {
            setLoading(true);
            handleSubmit(noteData).finally(() => {
              setLoading(false);
            });
          }}
        >
          Submit
        </Button>
      </div>
    </div>
  );
};

export default NoteForm;
