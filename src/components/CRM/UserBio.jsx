import React from 'react';
import { Link } from 'react-router-dom';
import { UserBioBox } from '../../containers/CRM/styled';
import { Cards } from '../Cards';
import {
  BankOutlined,
  EnvironmentOutlined,
  ExclamationCircleOutlined,
  LinkedinOutlined,
  LinkOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import { Button, Tag } from 'antd';
import { FaUserCircle } from 'react-icons/fa';

const UserBio = ({
  bio = '',
  email,
  telephone,
  skills = [],
  industries = [],
  address = '',
  tags = [],
  name = '',
  cardTitle = 'Contact Info',
  companyName = '',
  companyWebsite = '',
  linkedinUrl = '',
}) => {
  return (
    <UserBioBox>
      <Cards headless>
        {name && (
          <div className="w-full flex flex-col items-center justify-center gap-3 pb-7">
            <FaUserCircle className="text-9xl opacity-50" />
            <div className="text-xl font-semibold text-cyan-600">{name}</div>
          </div>
        )}
        {bio && (
          <article className="user-info">
            <h5 className="user-info__title font-semibold">User Bio</h5>
            <p>{bio}</p>
          </article>
        )}
        <address className="user-info">
          <h5 className="user-info__title font-semibold">{cardTitle}</h5>
          <ul className="user-info__contact">
            {companyName && (
              <li>
                <BankOutlined />{' '}
                <span className="font-semibold text-cyan-600">
                  {companyName}
                </span>
              </li>
            )}
            {companyWebsite && (
              <li>
                <LinkOutlined />{' '}
                <a href={`${companyWebsite}`}>{companyWebsite}</a>
              </li>
            )}
            {email && (
              <li>
                <MailOutlined /> <a href={`mailTo:${email}`}>{email}</a>
              </li>
            )}
            {linkedinUrl && (
              <li>
                <LinkedinOutlined />{' '}
                <a href={encodeURIComponent(linkedinUrl)} target='_blank'>{linkedinUrl}</a>
              </li>
            )}
            <li>
              <PhoneOutlined /> <a href={`tel:${telephone}`}>{telephone}</a>
            </li>
            {/* <li>
              <ExclamationCircleOutlined /> <span>www.example.com</span>
            </li> */}
            <li>
              <EnvironmentOutlined /> <span>{address}</span>
            </li>
          </ul>
        </address>
        <div className="user-info">
          <h5 className="user-info__title font-semibold">Industries</h5>
          <div className="user-info__skills flex items-center gap-3 flex-wrap">
            {industries?.map((item) => (
              <Tag key={item?.id} color="cyan">
                {item?.name}
              </Tag>
            ))}
          </div>
        </div>
        <div className="user-info">
          <h5 className="user-info__title font-semibold">Tags</h5>
          <div className="user-info__skills flex items-center gap-3 flex-wrap">
            {tags?.map((item) => (
              <Tag key={item?.id} color="cyan">
                {item?.name}
              </Tag>
            ))}
          </div>
        </div>
      </Cards>
    </UserBioBox>
  );
};

export default UserBio;
