import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Col, Row } from 'antd';
import { UserCard } from '../../containers/CRM/styled';
import { Cards } from '../Cards';
import { UserOutlined } from '@ant-design/icons';
import Heading from '../Heading';
import { FaUserCircle } from 'react-icons/fa';

const UserCards = ({ user }) => {
  const { name, designation } = user;
  return (
    <UserCard>
      <div className="card user-card">
        <Cards headless>
          <figure className='py-4 flex items-center justify-center'>
            <FaUserCircle className="font-semibold text-8xl opacity-50" />
          </figure>
          <figcaption>
            <div className="card__content">
              <Heading className="card__name" as="h6">
                <Link to="#" className='font-semibold text-cyan-600'>{name}</Link>
              </Heading>
              <p className="card__designation">{designation}</p>
            </div>

            {/* <div className="card__actions">
              <Button size="default" type="white">
                <FeatherIcon icon="mail" size={14} />
                Message
              </Button>
              <Button size="default" type="white">
                <FeatherIcon icon="user-plus" size={14} />
                Following
              </Button>
            </div> */}
            <div className="card__info">
              <Row gutter={15}>
                <Col xs={8}>
                  <div className="info-single">
                    <Heading className="info-single__title" as="h2">
                      $72,572
                    </Heading>
                    <p>Total Lead Value</p>
                  </div>
                </Col>
                <Col xs={8}>
                  <div className="info-single">
                    <Heading className="info-single__title" as="h2">
                      3,257
                    </Heading>
                    <p>Synced Leads</p>
                  </div>
                </Col>
                <Col xs={8}>
                  <div className="info-single">
                    <Heading className="info-single__title" as="h2">
                      74
                    </Heading>
                    <p>Created Leads</p>
                  </div>
                </Col>
              </Row>
            </div>
          </figcaption>
        </Cards>
      </div>
    </UserCard>
  );
};

UserCards.propTypes = {
  user: PropTypes.object,
};

export default UserCards;
