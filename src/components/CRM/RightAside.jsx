import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { RightAsideWrapper } from '../../containers/CRM/styled';
import { Cards } from '../Cards';
import { getAllContacts } from '../../services/crm/contacts';
import { UserOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const RightAside = ({ company }) => {
  const [contacts, setContacts] = useState([]);
  const getContactsInCompany = async () => {
    try {
      const { data } = await getAllContacts({
        page: 1,
        limit: 5,
        companyIds: [company?.id],
      });
      if (data?.result?.data?.length > 0) {
        setContacts([...data?.result?.data]);
      }
      console.log('getContactsInCompany: ', data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getContactsInCompany();
  }, [company?.id]);

  return (
    contacts?.length > 0 && (
      <RightAsideWrapper>
        <Cards title={`Also in ${company?.name || 'Company'}`}>
          <ul className="ff-widget">
            {contacts?.map(
              ({ firstName, middleName, surName, id, address }) => {
                return (
                  <li key={id}>
                    <div className="ff-info">
                      <UserOutlined className="font-semibold text-3xl text-cyan-600 opacity-60 pr-4" />
                      <p>
                        {`${firstName} ${middleName} ${surName}`}{' '}
                        <span>{address?.country}</span>
                      </p>
                    </div>
                    <Button
                      onClick={() =>
                        window.open(`/crm/contacts/view/${id}`, ' _blank')
                      }
                      className="btn-ff"
                      type={'primary'}
                    >
                      View detail
                    </Button>
                  </li>
                );
              }
            )}

            <Link
              to={`/crm/contacts?ci=${company?.id}`}
              target="_blank"
              className="btn-loadMore"
            >
              View all contacts.
            </Link>
          </ul>
        </Cards>
      </RightAsideWrapper>
    )
  );
};

export default RightAside;
