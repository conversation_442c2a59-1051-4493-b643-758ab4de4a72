import clsx from 'clsx';
import { Link } from 'react-router-dom';

const InforNavMenu = ({
  items = [],
  selectedItemKey = '',
  setSelectedItemKey = () => {},
  className = '',
}) => {
  return (
    <div class="bg-gray-100 w-full">
      <div class="flex overflow-x-auto bg-white w-full">
        {items?.map((item) => (
          <Link
            onClick={() => setSelectedItemKey(item?.key)}
            to={item?.to}
            key={item?.key}
            class={clsx(
              `flex-none min-w-[10rem] p-4 text-center arrow-box shadow-sm cursor-pointer hover:bg-cyan-100 transition-all duration-200 ease-in-out`,
              selectedItemKey === item?.to ? ' bg-cyan-50' : 'bg-[#f8f9fb]',
              className && className
            )}
          >
            <div class="text-xl font-bold text-[#08979c]">{item?.label}</div>
            <div class="text-sm text-gray-500 font-medium">
              {item?.description}
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default InforNavMenu;
