import { Button, message, notification, Spin, Upload } from 'antd';
import { ActivitiesWrapper } from '../../containers/CRM/styled';
import FileListCard from './FileListCard';
import { UploadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { LIMIT_ATTACHMENTS_SIZE } from '../BullHorn/BullhornSendEmailModal';
import { v4 as uuid } from 'uuid';
import { uploadFile } from '../../services/users';
import { createNewCRMFile, getAllFiles } from '../../services/crm/file';

const Files = ({ resourceId, resourceName }) => {
  const [loading, setLoading] = useState(true);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  const props = {
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file?.response, info.fileList);
      }
      if (info.file.status === 'done') {
        setFileList([info.file?.response, ...fileList]);
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    customRequest: async ({ file, onSuccess, onError }) => {
      setUploadLoading(true);
      const isAllow = file.size < LIMIT_ATTACHMENTS_SIZE;
      if (!isAllow) {
        const uploadedFile = {
          uid: uuid(),
          name: `${uuid()}${file?.name}`,
          status: 'failed',
        };
        onError(
          {
            event: { status: 500 },
          },
          { ...uploadedFile }
        );
        message.error('Reaching size limitation (20mb)!');
        return;
      }
      try {
        let formData = new FormData();

        formData.append('file', file);
        const { data } = await uploadFile(formData);
        const responsFile = data?.result?.data || null;

        if (responsFile) {
          const uploadedFileNote = {
            name: file?.name,
            status: 'done',
            url: responsFile?.url,
            fileId: responsFile?.fileId,
            size: file?.size,
            type: file?.type,
          };

          const payload = {
            resourceName,
            resourceId,
            fileId: responsFile?.fileId,
            note: JSON.stringify(uploadedFileNote),
          };
          const { data: newCRMFile } = await createNewCRMFile(payload);
          onSuccess({
            ...payload,
          });
        } else {
          onError({ event: { status: 500 } });
        }
        setUploadLoading(false);
      } catch (error) {
        setUploadLoading(false);
        onError({ event: error });
      }
    },
  };

  const getData = async () => {
    setLoading(true);
    try {
      const { data } = await getAllFiles({
        resourceName,
        resourceId,
      });
      if (data?.result?.files?.length > 0) {
        setFileList([...data?.result?.files]);
      } else {
        setFileList([]);
      }
      console.log('data', data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <ActivitiesWrapper>
      <div className="flex w-full justify-end">
        <Upload disabled={loading} showUploadList={false} {...props}>
          <Button
            loading={uploadLoading}
            type="primary"
            icon={<UploadOutlined />}
          >
            Click to Upload
          </Button>
        </Upload>
      </div>
      {loading ? (
        <div className="w-full flex items-center justify-center italic opacity-60">
          <Spin spinning />
        </div>
      ) : fileList?.length > 0 ? (
        <div className="file-list">
          <FileListCard fileList={fileList} setFileList={setFileList} />
        </div>
      ) : (
        <div className="w-full flex items-center justify-center italic opacity-60">
          No files found
        </div>
      )}
    </ActivitiesWrapper>
  );
};
export default Files;
