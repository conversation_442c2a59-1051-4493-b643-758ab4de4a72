import {
  ExportOutlined,
  FileAddOutlined,
  FileDoneOutlined,
  FileProtectOutlined,
  PoundOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ActivitiesWrapper } from '../../containers/CRM/styled';
import { Spin, Tag } from 'antd';
import { getAllLeads } from '../../services/crm/leads';
import { useEffect, useState } from 'react';

const Leads = ({ companyId = '' }) => {
  const [leads, setLeads] = useState([]);
  const [loading, setLoading] = useState(true);

  const getLeads = async () => {
    setLoading(true);
    try {
      const { data } = await getAllLeads({ companyIds: [companyId] });
      if (data?.result?.data?.length > 0) {
        const { data: newData } = data?.result;
        setLeads([...newData]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };
  useEffect(() => {
    getLeads();
  }, [companyId]);

  return (
    <ActivitiesWrapper>
      <div className="flex w-full justify-end">
        <div
          onClick={() => window.open(`/crm/leads?ci=${companyId}`, '_blank')}
          className="flex items-center gap-1 font-semibold text-cyan-600 w-fit hover:underline cursor-pointer"
        >
          View all leads
          <ExportOutlined />
        </div>
      </div>
      {!loading && (
        <div className="flex flex-col gap-2">
          {leads?.length > 0 &&
            leads?.map((item) => (
              <div
                key={item?.id}
                onClick={() => window.open(`/crm/leads/view/${item?.id}`, '_blank')}
                className="flex flex-col items-start justify-start gap-1 border-b p-3 hover:bg-[#f7f7f7] cursor-pointer"
              >
                <div className="flex items-center gap-1 font-semibold text-cyan-600 w-full">
                  <FileDoneOutlined />
                  {item?.jobTitle}
                </div>
                <div className="grid grid-cols-3 font-medium justify-between w-full">
                  <Tag color="cyan" icon={<UserOutlined />} className="w-fit">
                    {item.creator.fullName}
                  </Tag>
                  <Tag
                    color="cyan"
                    icon={<FileProtectOutlined />}
                    className="w-fit"
                  >
                    {item.jobType}
                  </Tag>
                  <Tag color="cyan" icon={<PoundOutlined />} className="w-fit">
                    {item.salary}
                  </Tag>
                </div>
              </div>
            ))}

          {leads?.length === 0 && (
            <div className="flex items-center justify-center w-full h-full">
              <p className="text-gray-500">No leads found</p>
            </div>
          )}
        </div>
      )}
      {loading && (
        <div className="flex items-center justify-center w-full h-full">
          <Spin />
        </div>
      )}
    </ActivitiesWrapper>
  );
};

export default Leads;
