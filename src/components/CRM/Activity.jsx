import React, { lazy, Suspense } from 'react';
import { Row, Col, Skeleton } from 'antd';
import { Cards } from '../Cards';
import ActivityContent from './ActivityContent';

const Activity = ({ company }) => {
  return (
    <Row gutter={25}>
      <Col md={24} xs={24}>
        <Suspense
          fallback={
            <Cards headless>
              <Skeleton active paragraph={{ rows: 10 }} />
            </Cards>
          }
        >
          <ActivityContent />
        </Suspense>
      </Col>
    </Row>
  );
};

export default Activity;
