import { useEffect, useState } from 'react';
import { ActivitiesWrapper } from '../../containers/CRM/styled';
import dayjs from 'dayjs';
import { Button, Drawer, notification, Popconfirm, Table } from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  UserOutlined,
} from '@ant-design/icons';
import NoteForm from './NoteForm';
import {
  createNewCRMNote,
  deleteNote,
  getAllNotes,
  updateNoteById,
} from '../../services/crm/note';
import { ACTION_NOTE } from '../JobsLeads/ViewVacancyDetail/ShortListTab/ModalEditEmail';

const initialNote = {
  action: ACTION_NOTE[0]?.value,
  title: '',
  content: '',
};

const Notes = ({ resourceName, resourceId }) => {
  const [note, setNote] = useState({ ...initialNote });
  const [dataSource, setDataSource] = useState([]);
  const [isShowNoteModal, setIsShowNoteModal] = useState(false);
  const [loading, setLoading] = useState(true);

  const showNoteModal = () => setIsShowNoteModal(true);
  const closeNoteModal = () => setIsShowNoteModal(false);

  const handleDelete = async (record) => {
    try {
      const { data } = await deleteNote(record?.id);
      const newDataSource = dataSource.filter(
        (item) => item?.id !== record?.id
      );
      setDataSource(newDataSource);
      notification.success({
        description: 'Note deleted successfully',
      });
    } catch (error) {
      console.log(error);
      notification.error({
        description: 'Note delete failed',
      });
    }
  };

  const columns = [
    {
      title: 'Date Added',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '20%',
      render: (text, record) => {
        return (
          <div className="font-medium line-clamp-1 max-w-xs" title={text}>
            {dayjs(text).format('DD/MM/YYYY HH:mm:ss')}
          </div>
        );
      },
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: '20%',
      render: (text, record) => {
        return (
          <div
            className="font-semibold text-cyan-600 line-clamp-1 max-w-xs flex items-center gap-1"
            title={text}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: '20%',
      render: (action, record) => {
        return <div className="font-medium line-clamp-1 ">{action}</div>;
      },
    },
    {
      title: 'Content',
      dataIndex: 'content',
      key: 'content',
      width: '30%',
      render: (comments, record) => {
        return (
          <div className="font-medium line-clamp-2 max-w-xs" title={comments}>
            {comments}
          </div>
        );
      },
    },
    {
      title: '',
      dataIndex: 'noteAction',
      key: 'noteAction',
      width: '10%',
      render: (text, record) => {
        return (
          <div className="flex w-full justify-end items-center gap-2">
            <Button
              onClick={() => {
                setNote({
                  ...record,
                });
                showNoteModal();
              }}
              type="primary"
              icon={<EditOutlined />}
            ></Button>
            <Popconfirm
              title="Delete the note"
              description={`Are you sure you want to delete ${record?.title} note?`}
              onConfirm={(e) => handleDelete(record)}
              okText="Yes"
              cancelText="No"
            >
              <Button danger icon={<DeleteOutlined />}></Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const getData = async () => {
    setLoading(true);
    try {
      const { data } = await getAllNotes({
        resourceName,
        resourceId,
      });
      if (data?.result?.notes?.length > 0) {
        setDataSource([...data?.result?.notes]);
      }
      setLoading(false);
      console.log(data);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const handleEditNote = async (record) => {
    try {
      const { data } = await updateNoteById({
        resourceName,
        resourceId,
        ...record,
      });
      const newDataSource = [...dataSource];
      const index = newDataSource.findIndex((item) => item?.id === record?.id);
      newDataSource[index] = { ...record };
      setDataSource(newDataSource);
      closeNoteModal();
      notification.success({
        description: 'Note updated successfully',
      });
    } catch (error) {
      notification.error({
        description: 'Failed to update note',
      });
    }
  };

  const handleCreateNote = async (record) => {
    try {
      const { data } = await createNewCRMNote({
        resourceName,
        resourceId,
        ...record,
      });

      const id = data?.result?.raw?.[0]?.id || '';
      const newDataSource = [{ ...record, id }, ...dataSource];
      setDataSource(newDataSource);
      closeNoteModal();
      notification.success({
        description: 'Note added successfully',
      });
    } catch (error) {
      notification.error({
        description: 'Failed to add note',
      });
      console.log(error);
    }
  };

  const handleSubmit = async (data) => {
    console.log('handleSubmit', data);
    if (data?.id) {
      await handleEditNote(data);
    } else {
      await handleCreateNote(data);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <ActivitiesWrapper>
      <div className="flex items-center justify-end mb-2">
        <Button
          disabled={loading}
          onClick={() => {
            setNote({
              ...initialNote,
            });
            showNoteModal();
          }}
          type="primary"
          icon={<PlusOutlined />}
        >
          Add Note
        </Button>
      </div>
      <div className="search-table-new-design-container">
        <Table
          loading={loading}
          size="small"
          dataSource={dataSource}
          columns={columns}
        />
      </div>

      {/* Note drawer */}
      <Drawer
        getContainer={false}
        title={note?.id ? 'Edit Note' : 'Add Note'}
        width={'50%'}
        onClose={closeNoteModal}
        open={isShowNoteModal}
        destroyOnClose={true}
      >
        <NoteForm
          note={note}
          handleCancel={closeNoteModal}
          handleSubmit={handleSubmit}
        />
      </Drawer>
    </ActivitiesWrapper>
  );
};

export default Notes;
