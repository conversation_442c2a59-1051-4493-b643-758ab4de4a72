import { useEffect, useState } from 'react';
import { ActivitiesWrapper } from '../../containers/CRM/styled';
import dayjs from 'dayjs';
import { Drawer, Table } from 'antd';
import { getAllEmails } from '../../services/crm/email';
import { isArray } from 'lodash';
import EmailItemView from './EmailItemView';

const initialPagination = {
  current: 1,
  pageSize: 10,
  total: 0,
};

const Emails = ({ resourceId, resourceName }) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState(initialPagination);
  const [open, setOpen] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState('');

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const getData = async (submitPage) => {
    setLoading(true);
    try {
      const payload = {
        entity: resourceName,
        entityId: resourceId,
        page: submitPage || pagination?.current,
        limit: pagination?.pageSize,
      };
      const { data } = await getAllEmails(payload);
      if (data?.result?.emails?.length > 0) {
        const emails = data?.result?.emails;
        setDataSource([...emails]);
      }

      const paginationTemp = data?.result?.pagination || null;
      if (paginationTemp) {
        setPagination({
          ...pagination,
          current: paginationTemp?.page,
          total: paginationTemp?.total,
        });
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    getData();
  }, [resourceId]);

  const columns = [
    {
      title: 'Date Sent',
      dataIndex: 'sentAt',
      key: 'sentAt',
      width: '10%',
      render: (text, record) => {
        return (
          <div className="font-medium line-clamp-1 max-w-xs" title={text}>
            {dayjs(text).format('DD/MM/YYYY')}
          </div>
        );
      },
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      key: 'subject',
      width: '60%',
      render: (text, record) => {
        return (
          <div
            className="font-semibold text-cyan-600 line-clamp-1 max-w-2xl"
            title={text}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: 'From',
      dataIndex: 'fromEmail',
      key: 'fromEmail',
      width: '20%',
      render: (text, record) => {
        return (
          <div className="font-medium line-clamp-1 max-w-xs" title={text}>
            {text}
          </div>
        );
      },
    },
    {
      title: 'To',
      dataIndex: 'toEmail',
      key: 'toEmail',
      width: '20%',
      render: (tos, record) => {
        return (isArray(tos) && tos?.length) > 0 ? (
          <div className="font-medium text-cyan-600 line-clamp-1">
            {tos?.join(', ')}
          </div>
        ) : (
          <div className="font-medium text-cyan-600 line-clamp-1">{tos}</div>
        );
      },
    },
  ];

  const handlePagination = (page) => {
    setPagination({
      ...pagination,
      current: page,
    });
    getData(page);
  };

  return (
    <ActivitiesWrapper>
      <div className="search-table-new-design-container">
        <Table
          className="customized-style-pagination w-full"
          pagination={{
            ...pagination,
            showSizeChanger: false,
            onChange: handlePagination,
            showTotal: (total) => `Total ${total} Sequences`,
          }}
          onRow={(record) => {
            return {
              onClick: () => {
                setSelectedEmail(record);
                showDrawer();
              },
              style: { cursor: 'pointer' },
            };
          }}
          loading={loading}
          size="small"
          dataSource={dataSource}
          columns={columns}
        />
      </div>
      <Drawer
        title="Email Summary"
        onClose={onClose}
        open={open}
        width={'50vw'}
      >
        <div className='px-5'>
          <EmailItemView emailProp={selectedEmail} />
        </div>
      </Drawer>
    </ActivitiesWrapper>
  );
};

export default Emails;
