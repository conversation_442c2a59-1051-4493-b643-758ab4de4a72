import { Link } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import styled from 'styled-components';

const StyledDiv = styled.div`
  .card {
    position: relative;
    width: 190px;
    height: 254px;
    background-color: #fff;
    border-radius: 20px;
    /*box-shadow: 0px 5px 5px #313131;*/
    overflow: hidden;
  }

  .top-card {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    height: 65%;
    transition: height 0.3s ease;
    background-color: #fff;
  }

  .bottom-card {
    border-top-right-radius: 20px;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    height: 35%;
    transition: height 0.3s ease;
    background-color: #0891b2;
  }

  .bottom-card::before {
    content: '';
    position: absolute;
    background-color: transparent;
    bottom: 89px;
    height: 50px;
    width: 175px;
    transition: bottom 0.3s ease;
    border-bottom-left-radius: 20px;
    box-shadow: 0 30px 0 0 #0891b2;
  }

  .card-content {
    padding-top: 13%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
  }
  .card-title {
    font-weight: 700;
    font-size: 18px;
  }
  .card-txt {
    font-size: 14px;
  }

  .card-btn {
    font-size: 13px;
    margin-top: 15%;
    text-decoration: none;
    color: white;
    background-color: transparent;
    border: solid 2px #fff;
    border-radius: 15px;
    padding: 5%;
  }

  .card:hover {
    box-shadow: 0px 2px 2px #0a3bffa1;
    border: solid 1px #0891b2;
  }

  .card:hover .top-card {
    height: 35%;
    transition: height 0.3s ease;
  }
  .card:hover .bottom-card {
    height: 65%;
    transition: height 0.3s ease;
  }
  .card:hover .bottom-card::before {
    bottom: 164px;
    transition: bottom 0.3s ease;
  }

  .card-btn:hover {
    color: black;
    background-color: #fff;
    transition: background-color 0.4s ease;
  }
`;

const ContainerCard = ({
  title,
  description,
  url = '',
  icon,
  key = '',
  onClick = null,
}) => {
  return (
    <StyledDiv key={key || uuid()}>
      <div class="card">
        <div class="top-card flex w-full items-center justify-center">
          {icon}
        </div>
        <div class="bottom-card">
          <div class="card-content">
            <span class="card-title">{title}</span>
            <p class="card-txt">{description}</p>
            {url && (
              <Link to={url} class="card-btn font-semibold">
                Access
              </Link>
            )}
            {onClick && (
              <button class="card-btn font-semibold" onClick={() => onClick(title)}>
                Access
              </button>
            )}
          </div>
        </div>
      </div>
    </StyledDiv>
  );
};

export default ContainerCard;
