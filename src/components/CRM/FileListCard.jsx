import { Link } from 'react-router-dom';
import { Cards } from '../Cards';
import {
  fileLogo,
  getThumbnailByType,
  imageExtensions,
  SALES,
  SUPER_ADMIN,
} from '../../constants/common.constant';
import { formatBytes } from '../../helpers/util';
import { getLinkS3 } from '../../services/aws';
import { useEffect, useState } from 'react';
import { message, Popconfirm } from 'antd';
import { DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { deleteFile } from '../../services/crm/file';
import { getUserViewAsRole } from '../../helpers/getUserViewAs';

const FileRow = ({
  file,
  submitDelete,
  deleteFromSource = null,
  allowDelete,
}) => {
  const fileNote = file?.note ? JSON.parse(file?.note) : {};
  const fileType = fileNote?.type?.split('/')?.[1] || 'pdf';
  const thumnail = getThumbnailByType(fileType);

  const [imageUrl, setImageUrl] = useState(thumnail);
  const [loading, setLoading] = useState(true);

  const fetchImageUrl = async (imageId) => {
    if (!imageExtensions.includes(fileType)) {
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      const { data } = await getLinkS3(imageId);
      setImageUrl(data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleDelete = async (fileId) => {
    setLoading(true);
    try {
      deleteFromSource
        ? await deleteFromSource(fileId)
        : await deleteFile(fileId);
      message.success('File deleted successfully');
      submitDelete(fileId);
      setLoading(false);
    } catch (error) {
      console.log('error', error);
      message.error('File deleted failed!');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchImageUrl(file?.fileId);
  }, [file?.fileId]);

  return (
    <div key={file?.fileId} className="file-list__single flex w-full">
      <div className="file-single-info flex !w-full">
        <div className="file-single-logo">
          <img src={imageUrl} alt={fileNote?.name} />
        </div>
        <div className="file-single__content w-full">
          <span className="file-name text-cyan-600 font-medium w-full">
            {fileNote?.name}
          </span>
          <span className="file-size">{formatBytes(fileNote?.size)}</span>
          <span className="file-content-action flex items-center gap-5">
            <a href={fileNote?.url} className="font-medium">
              Download
            </a>
            {allowDelete && (
              <Popconfirm
                title={'Confirmation'}
                description={
                  <div>
                    Are you sure to delete{' '}
                    <span className="font-semibold">{fileNote?.name}</span>{' '}
                    File?
                  </div>
                }
                onConfirm={async () =>
                  await handleDelete(file?.id || file?.fileId)
                }
                // onCancel={cancel}
                okText="Delete"
                cancelText="Cancel"
                // placement="topLeft"
              >
                <a className="font-medium text-red-600">Delete</a>
              </Popconfirm>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

const FileListCard = ({
  fileList = [],
  setFileList,
  deleteFromSource = null,
  allowDelete = true,
}) => {
  const submitDelete = (fileId) => {
    const newFileList = fileList.filter((file) => {
      const id = file?.id || file?.fileId;
      return id !== fileId;
    });
    setFileList([...newFileList]);
  };
  return (
    <div className="file-list">
      {fileList?.map((file) => (
        <FileRow
          allowDelete={allowDelete}
          file={file}
          submitDelete={submitDelete}
          deleteFromSource={deleteFromSource}
        />
      ))}
    </div>
  );
};

export default FileListCard;
