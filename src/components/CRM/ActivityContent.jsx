import React from 'react';
import { Link } from 'react-router-dom';
import { ActivityContents } from '../../containers/CRM/styled';
import { Cards } from '../Cards';
import { FileAddOutlined, FileDoneOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const ActivityContent = () => {
  return (
    <ActivityContents>
      <Cards headless>
        <ul className="activity-list">
          <li className="activity-list__single">
            <span className="activity-icon primary">
              <FileDoneOutlined />
            </span>
            <div className="activity-content">
              <div className="activity-info">
                <p>
                  Synced{' '}
                  <strong>Mobile App Full Stack Software Engineer</strong>{' '}
                  <span className="hour">5 hours ago</span>
                </p>
              </div>
              <Button className="activity-more bg-white" type="link">
                View detail
              </Button>
            </div>
          </li>
          <li className="activity-list__single">
            <span className="activity-icon primary">
              <FileAddOutlined />
            </span>
            <div className="activity-content">
              <div className="activity-info">
                <p>
                  Added <strong>Mobile App Full Stack Software Engineer</strong>{' '}
                  <span className="hour">7 hours ago</span>
                </p>
              </div>
              <Button className="activity-more bg-white" type="link">
                View detail
              </Button>
            </div>
          </li>
          <li className="activity-list__single">
            <span className="activity-icon primary">
              <FileAddOutlined />
            </span>
            <div className="activity-content">
              <div className="activity-info">
                <p>
                  Added <strong>Mobile App Full Stack Software Engineer 2</strong>{' '}
                  <span className="hour">15 hours ago</span>
                </p>
              </div>
              <Button className="activity-more bg-white" type="link">
                View detail
              </Button>
            </div>
          </li>
        </ul>
      </Cards>
    </ActivityContents>
  );
};

export default ActivityContent;
