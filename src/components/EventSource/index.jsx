import { useEffect } from 'react';

const EventSourceRender = ({
  updateRecentlyData,
  sseName = 'ENRICH_CONTACT_DATA',
  sseEndpoint = 'sse/enrich-data-floqer-linkedin-url'
}) => {
  useEffect(() => {
    const sseUrl = `${import.meta.env.VITE_API_URL}/${sseEndpoint}`;
    const sseJobs = new EventSource(sseUrl);

    sseJobs.onopen = () =>
      console.log('[SSE] Enrich contacts data connection is opened');
    sseJobs.onerror = (error) =>
      console.log(
        '[SSE] Enrich contacts data connection error occurred.',
        error
      );
    sseJobs.addEventListener(sseName, updateRecentlyData);

    return () => {
      console.log('destroyinggg..');
      sseJobs.removeEventListener(sseName, updateRecentlyData);
      sseJobs.close();
    };
  }, []);
  return <></>;
};

export default EventSourceRender;
