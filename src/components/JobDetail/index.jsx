/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import {
  Spin,
  Image,
  Flex,
  Button,
  Dropdown,
  Modal,
  Drawer,
  Form,
  Input,
  Select,
  notification,
  Avatar,
  Typography,
} from 'antd';
import {
  FileTextOutlined,
  FieldTimeOutlined,
  BankFilled,
  EnvironmentOutlined,
  PoundOutlined,
  StarOutlined,
  WalletOutlined,
  SettingOutlined,
  CheckOutlined,
  CloseOutlined,
  HeartOutlined,
  TrophyOutlined,
  NotificationOutlined,
  MoreOutlined,
  SendOutlined,
} from '@ant-design/icons';
import zileoIcon from '../../assets/img/zileoIcon.png';
import { getLinkS3 } from '../../services/aws';
import useResponsive from '../../hooks/useResponsive';
import handleRenderTime from '../../function/handleRenderTime';
import dayjs from 'dayjs';
import { removeTrackingParams } from '../../utils/common';
import { useNavigate, useParams } from 'react-router-dom';

// Icon source
import companyIcon from '../../assets/img/icons/company-icon.png';
import jobTypeIcon from '../../assets/img/icons/jobtype-icon.png';
import locationIcon from '../../assets/img/icons/location-icon.png';
import salaryIcon from '../../assets/img/icons/salary-icon.png';
import sourceIcon from '../../assets/img/icons/source-icon.png';
import useReportedAgencyModal from '../../hooks/useReportedAgencyModal';
import { Controller, useForm } from 'react-hook-form';
import useReportedAgencies from '../../hooks/useReportedAgencies';
import _ from 'lodash';
import { countries } from 'country-list-json';
import { useSelector } from 'react-redux';
import { selectAllUsers } from '../../store/common';
import { createSentJob } from '../../services/jobLeads';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import { getColorHexFromName } from '../../function/getRandomColor';
import { stringAvatar } from '../../function/stringAvatar';
import styled from 'styled-components';
import { licenseType } from '../../constants/common.constant';
import { getUserViewAsLicenseType } from '../../helpers/getUserViewAs';

function TextWithLineBreaks(text, keywords) {
  if (text && keywords) {
    let newText = text?.replace(/(?:\r\n|\r|\n)/g, '<br />');
    // replace all {keyword} with <mark>{keyword}</mark>
    keywords?.split(',').forEach((keyword) => {
      keyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      newText = newText?.replace(
        new RegExp(keyword, 'gi'),
        (match) =>
          `<span class='bg-[#f6ffed] text-[#389e0d] border border-[#b7eb8f] px-1 font-semibold'>${match.toUpperCase()}</span>`
      );
    });

    return <div dangerouslySetInnerHTML={{ __html: newText }} />;
  }
  return (
    <div
      style={{ whiteSpace: 'pre-line' }}
      dangerouslySetInnerHTML={{ __html: text }}
      className="w-full h-[25rem]"
    />
  );
}

export const SelectContainer = styled.div`
  .ant-select-selector {
    padding: 10px 11px !important;
  }
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
`;

function JobDetail({
  job,
  keywords,
  isFromSync,
  handleEditLogs,
  onClickSync,
  setDeleteModalOpen,
  handleDelete,
  closeDrawer,
  loading,
}) {
  const { searchId } = useParams();
  const { profileUser } = useViewAs();
  const { profile: profileUserAuth } = useAuth();

  const currentUserLicenseType = getUserViewAsLicenseType();
  const isStandardUser = currentUserLicenseType === licenseType.STANDARD;

  const userToSet = profileUser || profileUserAuth;

  const navigate = useNavigate();
  const [showSendJobForm, setSendJobForm] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [userSentJob, setUserSendJob] = useState(null);
  const [loadingSentJob, setLoadingSentJob] = useState(false);

  const countriesSelectItems = countries?.map((country) => ({
    label: country.name,
    value: country.name,
  }));

  const { populateReportedAgencies, isLoading } = useReportedAgencies();

  const {
    open,
    companyName,
    listAgencyName,
    isLoadingNewListAgency,
    showModal,
    handleOk,
    handleCancel,
    handleGetNewFuzzySearch,
    setCompanyName,
    confirmLoading,
  } = useReportedAgencyModal();

  const { control, getValues, reset } = useForm({
    defaultValues: {
      fuzzySearch: [],
      country: 'United Kingdom',
    },
  });

  const handleSearchChange = _.debounce((searchText) => {
    setCompanyName(searchText);
    handleGetNewFuzzySearch(searchText);
  }, 1000);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleSyncClick = () => {
    handleEditLogs();
    const searchJobId = searchId || 'non-search';
    const rawJobId = job?.job_id || job?.jobBoardId || job?.id;
    const jobId = encodeURIComponent(rawJobId);

    // Save job in Local Storage
    const jsonJob = JSON.stringify(job);
    // const compressedJob = LZString.compressToEncodedURIComponent(jsonJob);
    localStorage.setItem('job', jsonJob);

    if (isStandardUser) {
      navigate(`/sync-standard-lead/${searchJobId}/${jobId}`);
    } else {
      navigate(`/sync-vacancy/${searchJobId}/${jobId}`);
    }
  };

  useEffect(() => {
    const fetchImageUrl = async () => {
      try {
        const { data } = await getLinkS3(job.logoCompany);
        setImageUrl(data);
      } catch (error) {
        setImageError(true);
      }
    };
    fetchImageUrl();
  }, [job.logoCompany]);

  const items = [
    {
      key: 'report-agency',
      label: <div className="font-medium">Report Agency</div>,
      icon: <NotificationOutlined className="font-medium" />,
    },
    {
      key: 'send-job',
      label: <div className="font-medium">Send job</div>,
      icon: <SendOutlined className="font-medium" />,
    },
  ];

  const handleMoreBtnClick = ({ key }) => {
    if (key === 'report-agency') {
      if (job?.company) {
        handleSearchChange(job?.company);
      }
      showModal();
    } else if (key === 'send-job') {
      setSendJobForm(true);
    }
  };

  const allUsers = useSelector(selectAllUsers);

  const handleSendJobToLead = async () => {
    if (!userSentJob) {
      notification.error({
        message: 'Please select a user',
      });
      return;
    }
    setLoadingSentJob(true);
    try {
      const { data } = await createSentJob({
        jobBoardId: job?.job_id,
        sentToUser: [userSentJob],
        senderId: userToSet?.user?.id || userToSet?.id,
      });
      if (data) {
        notification.success({
          message: 'Sent job successfully',
        });
        setSendJobForm(false);
        setLoadingSentJob(false);
      }
    } catch (err) {
      notification.error({
        message: err?.response?.data?.message,
      });
      setLoadingSentJob(false);
    }
  };

  if (job === undefined)
    return (
      <div className="text-center mt-20">
        <Spin tip="Loading" size="large">
          <div className="content" />
        </Spin>
      </div>
    );
  else
    return (
      <div>
        <div>
          <div>
            <div>
              <Flex vertical gap={24}>
                <Flex gap={16} style={{ justifyContent: 'space-between' }}>
                  <Flex gap={16}>
                    {imageError ? (
                      <Image
                        className="rounded-md border border-solid border-primary p-1"
                        src={zileoIcon}
                        alt="Zileo Icon"
                        width="48px"
                        height="48px"
                      />
                    ) : imageUrl ? (
                      <Image
                        className="rounded-md border border-solid border-primary p-1"
                        src={imageUrl || zileoIcon}
                        alt={'Company Logo'}
                        width="48px"
                        height="48px"
                        onError={handleImageError}
                      />
                    ) : (
                      <Spin />
                    )}
                    <div>
                      <h4 className="font-bold text-lg">
                        {job?.jobtitle || job?.title}
                      </h4>
                      {/* <Flex style={{ flexWrap: 'wrap' }}>
                        <div>
                          <FieldTimeOutlined className="mr-2" />
                          <span>
                            {dayjs(job?.posted || job?.dateAdded).format(
                              'DD-MM-YYYY'
                            )}
                          </span>
                        </div>
                      </Flex> */}
                    </div>
                  </Flex>
                  <div>
                    <div className="basis-full lg:basis-auto flex justify-end flex items-center gap-3">
                      {isFromSync && (
                        <div>
                          <Button
                            className="text-black rounded-md bg-white flex items-center justify-center"
                            style={{
                              backgroundColor: 'green',
                              color: '#fff',
                            }}
                            onClick={handleSyncClick}
                          >
                            <CheckOutlined
                              style={{ color: '#fff' }}
                              className="text-blue-400"
                            />
                          </Button>
                        </div>
                      )}
                      {isFromSync && handleDelete && (
                        <div>
                          <Button
                            loading={loading}
                            className="text-black rounded-md px-3 py-1 bg-white flex items-center"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete();
                            }}
                            style={{
                              backgroundColor: 'red',
                              color: '#fff',
                            }}
                          >
                            <CloseOutlined
                              style={{ color: '#fff' }}
                              className="text-blue-400"
                            />
                          </Button>
                        </div>
                      )}
                      {isFromSync && (
                        <Dropdown
                          arrow
                          menu={{ items, onClick: handleMoreBtnClick }}
                          placement="bottomLeft"
                        >
                          <Button
                            type="primary"
                            icon={<MoreOutlined />}
                          ></Button>
                        </Dropdown>
                      )}
                    </div>
                  </div>
                </Flex>
                <hr />
                <div>
                  <div className="grid grid-cols-8 gap-x-4">
                    <div className="col-span-8 sm:col-span-4">
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <Image
                            className="mr-3 text-blue-400"
                            src={companyIcon}
                            alt="Company Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-3 text-graySecondary font-semibold">
                            Company
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6 text-secondary">
                          {job.company}
                        </span>
                      </div>
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <Image
                            className="mr-3 text-blue-400"
                            src={jobTypeIcon}
                            alt="Job Type Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-3 text-graySecondary font-semibold">
                            Job Type
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6 text-secondary">
                          {job.jobtype || '-'}
                        </span>
                      </div>
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <Image
                            className="mr-3 text-blue-400"
                            src={locationIcon}
                            alt="Location Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-3 text-graySecondary font-semibold">
                            Location
                          </span>
                        </div>
                        <span
                          className="pr-3 font-medium col-span-6 text-secondary line-clamp-1"
                          title={job?.joblocationcity}
                        >
                          {job?.joblocationcity}
                        </span>
                      </div>
                    </div>
                    <div className="col-span-8 sm:col-span-4">
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <Image
                            className="mr-3 text-blue-400"
                            src={salaryIcon}
                            alt="Salary Icon"
                            width="16px"
                            height="16px"
                            preview={false}
                          />
                          <span className="pl-3 text-graySecondary font-semibold">
                            Salary
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6 text-secondary">
                          {job.salary || '-'}
                        </span>
                      </div>
                      {job?.link && (
                        <div className="items-center py-1 grid grid-cols-10">
                          <div className="col-span-3 flex items-center">
                            <Image
                              className="mr-3 text-blue-400"
                              src={sourceIcon}
                              alt="Source Icon"
                              width="16px"
                              height="16px"
                              preview={false}
                            />
                            <span className="pl-3 text-graySecondary font-semibold">
                              Source
                            </span>
                          </div>
                          <span className="font-medium col-span-6 text-secondary">
                            <Button
                              onClick={() => {
                                window.open(
                                  removeTrackingParams(job?.link),
                                  '_blank'
                                );
                              }}
                              type={'primary'}
                            >
                              Click to View Job
                            </Button>
                          </span>
                        </div>
                      )}
                      <div className="items-center py-1 grid grid-cols-10">
                        <div className="col-span-3 flex items-center">
                          <FieldTimeOutlined className="pr-3 text-blue-400" />
                          <span className="pr-3 text-graySecondary font-semibold">
                            Job Posted
                          </span>
                        </div>
                        <span className="pr-3 font-medium col-span-6 text-secondary">
                          {` ${dayjs(job?.posted || job?.dateAdded).format('DD-MM-YYYY')}`}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <p className="font-semibold mb-2">Description</p>

                  <div className="max-h-[35rem] overflow-y-auto p-2 border rounded-md bg-[#fcfcfc] p-2">
                    {TextWithLineBreaks(job.description, keywords)}
                  </div>
                </div>
              </Flex>
            </div>
          </div>
        </div>
        <Drawer
          title="Add Reported Agency"
          open={open}
          onClose={handleCancel}
          destroyOnClose={true}
          footer={
            <>
              <Button
                loading={confirmLoading || isLoading}
                disabled={!companyName}
                onClick={async () => {
                  await handleOk(
                    getValues().fuzzySearch,
                    getValues()?.country || 'United Kingdom'
                  );
                  await populateReportedAgencies().then(() => {
                    window.location.reload();
                  });
                }}
              >
                Submit
              </Button>
            </>
          }
        >
          <div className="mt-6">
            <Form.Item
              className="m-0"
              label="Agency Name"
              name="agencyName"
              required={true}
            >
              <Input
                defaultValue={job?.company || ''}
                required={true}
                placeholder="Fill the Agency Name"
                onChange={(ev) => {
                  const agencyName = ev.target.value;
                  if (agencyName) handleSearchChange(agencyName);
                }}
              />
            </Form.Item>
            <Form.Item className="my-2" label="Country Name" name="country">
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    defaultValue={{
                      label: 'United Kingdom',
                      value: 'United Kingdom',
                    }}
                    showSearch
                    placeholder="Select the country"
                    options={countriesSelectItems}
                  />
                )}
              ></Controller>
            </Form.Item>
            {isLoadingNewListAgency ? (
              <Spin
                style={{ position: 'inherit' }}
                className="m-auto py-6 mb-6"
                tip={`We are looking for the similar agencies that might be the same as ${companyName}`}
              >
                <div className="content" />
              </Spin>
            ) : (
              <>
                <p className="mb-2 mt-4">{`We have found some similar agencies that might be the same as the Agency you mentioned. Would you like to report these as a agency?`}</p>
                <Form.Item label="" name="fuzzySearch">
                  <Controller
                    name="fuzzySearch"
                    control={control}
                    render={({ field }) => (
                      <Select
                        loading={isLoadingNewListAgency}
                        mode="multiple"
                        notFoundContent={
                          isLoadingNewListAgency ? <Spin size="small" /> : ''
                        }
                        {...field}
                        options={Array.from(
                          new Set(listAgencyName.map((item) => item.trim()))
                        )
                          .filter((item) => item !== '')
                          .map((option) => ({
                            value: option.trim(),
                            label: option.trim(),
                          }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            ?.toLowerCase()
                            ?.indexOf(inputValue?.toLowerCase()) !== -1
                        }
                      />
                    )}
                  />
                </Form.Item>
              </>
            )}
          </div>
        </Drawer>

        {/* Send job drawer */}
        <Drawer
          destroyOnClose={true}
          title="Send Job"
          footer={false}
          open={showSendJobForm}
          onClose={() => setSendJobForm(false)}
        >
          <SelectContainer>
            <Select
              showSearch
              options={_.map(allUsers, (option) => ({
                value: _.get(option, 'username') || _.get(option, 'email'),
                key: _.get(option, 'id'),
                id: _.get(option, 'id'),
                label: (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <Avatar
                      style={{
                        backgroundColor: getColorHexFromName(
                          _.get(option, 'username') || _.get(option, 'email')
                        ),
                        verticalAlign: 'middle',
                      }}
                    >
                      {stringAvatar(
                        _.get(option, 'username') || _.get(option, 'email')
                      )}
                    </Avatar>
                    &ensp;
                    <Typography.Text
                      style={{
                        width: '150px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {_.get(option, 'username') || _.get(option, 'email')}
                    </Typography.Text>
                  </div>
                ),
              }))}
              placeholder="None"
              className="w-full h-full"
              onSelect={async (value, option) => {
                setUserSendJob(option?.id);
              }}
            />
            <Button
              loading={loadingSentJob}
              onClick={handleSendJobToLead}
              type={'primary'}
            >
              Send Lead
            </Button>
          </SelectContainer>
        </Drawer>
      </div>
    );
}

export default JobDetail;
