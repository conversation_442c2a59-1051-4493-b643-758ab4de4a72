/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';

export default function FilterBar({ setSearchData }) {
  const [distance, setDistance] = useState();
  const [jobType, setJobType] = useState();
  const [salary, setSalary] = useState({
    maxSalary: '',
    minSalary: '',
  });
  const [location, setLocation] = useState();
  useEffect(() => {
    setSearchData({
      distance: distance,
      jobType: jobType,
      maxSalary: salary.maxSalary,
      minSalary: salary.minSalary,
      location: location,
    });
  }, [distance, jobType, salary, location]);
  const handleRadioChange = (event) => {
    setDistance(event.target.value);
  };
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setSalary({ ...salary, [name]: value });
  };
  const handleJobType = (event) => {
    setJobType(event.target.value);
  };
  const handleLocationChange = (event) => {
    setLocation(event.target.value);
  };

  return (
    <form className="outline outline-gray-100 outline-4 outline-offset-8 rounded-lg mt-10 text-left">
      {/* <div className="sm:col-span-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Select JobBoard
        </label>
        <Select role={jobboards} />
      </div> */}

      <div className="sm:col-span-3 pt-8">
        <label
          htmlFor="first-name"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Company Name
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="first-name"
            id="first-name"
            autoComplete="given-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>

      <div className="sm:col-span-3 mt-8">
        <label
          htmlFor="country"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
        >
          Country
        </label>
        <div className="mt-2">
          <select
            id="country"
            name="country"
            autoComplete="country-name"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6"
          >
            <option>United Kingdom</option>
          </select>
        </div>
      </div>

      <div className="col-span-full mt-8">
        <label
          htmlFor="location"
          className="block text-sm font-medium leading-6 text-gray-900 dark:text-white dark:text-white"
        >
          Location
        </label>
        <div className="mt-2">
          <input
            type="text"
            name="location"
            id="location"
            value={location}
            onChange={handleLocationChange}
            autoComplete="location"
            className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>
      </div>

      <fieldset className="rounded-lg mt-10">
        <legend className="text-sm font-semibold leading-6 text-gray-900 dark:text-white mt-10">
          Distance From Location
        </legend>
        <div className="mt-6 space-y-3">
          <div className="flex items-center gap-x-3">
            <input
              id="zero"
              name="distancefromlocation"
              type="radio"
              onChange={handleRadioChange}
              value={0}
              checked={distance == 0}
              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <label
              htmlFor="zero"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              0
            </label>
          </div>
          <div className="flex items-center gap-x-3">
            <input
              id="five"
              name="distancefromlocation"
              type="radio"
              onChange={handleRadioChange}
              checked={distance == 5}
              value={5}
              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <label
              htmlFor="five"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              5
            </label>
          </div>
          <div className="flex items-center gap-x-3">
            <input
              id="twenty"
              name="distancefromlocation"
              type="radio"
              onChange={handleRadioChange}
              checked={distance == 20}
              value={20}
              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <label
              htmlFor="twenty"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              20
            </label>
          </div>
          <div className="flex items-center gap-x-3">
            <input
              id="fifty"
              name="distancefromlocation"
              type="radio"
              onChange={handleRadioChange}
              checked={distance == 50}
              value={50}
              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <label
              htmlFor="fifty"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              50
            </label>
          </div>
          <div className="flex items-center gap-x-3">
            <input
              id="hundred"
              name="distancefromlocation"
              type="radio"
              onChange={handleRadioChange}
              checked={distance == 100}
              value={100}
              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
            />
            <label
              htmlFor="hundred"
              className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
            >
              100
            </label>
          </div>
        </div>
      </fieldset>

      <div className="col-span-full mt-8 flex">
        <div className="sm:col-span-3 pt-8">
          <label
            htmlFor="minsalary"
            className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
          >
            Min Salary
          </label>
          <div className="mt-2">
            <input
              type="text"
              name="minSalary"
              value={salary.minSalary}
              onChange={handleInputChange}
              id="minsalary"
              autoComplete="given-name"
              className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </div>
        </div>

        <div className="sm:col-span-3 pt-8 ps-3">
          <label
            htmlFor="maxsalery"
            className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
          >
            Max Salary
          </label>
          <div className="mt-2">
            <input
              type="text"
              name="maxSalary"
              value={salary.maxSalary}
              onChange={handleInputChange}
              id="maxsalery"
              autoComplete="family-name"
              className="block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </div>
        </div>
      </div>

      <div className="space-y-12 mt-6">
        <div className="border-b border-gray-900/10 pb-12 mt-10">
          <fieldset className="rounded-lg mt-10">
            <legend className="text-sm font-semibold leading-6 text-gray-900 dark:text-white">
              Type of Job
            </legend>
            <div className="mt-6 space-y-3">
              <div className="flex items-center gap-x-3">
                <input
                  id="zero"
                  name="typeofjob"
                  type="radio"
                  value="permanet"
                  checked={jobType == 'permanet'}
                  onChange={handleJobType}
                  className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label
                  htmlFor="zero"
                  className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
                >
                  Permanent
                </label>
              </div>
              <div className="flex items-center gap-x-3">
                <input
                  id="push-email"
                  name="typeofjob"
                  type="radio"
                  value="temporary"
                  checked={jobType == 'temporary'}
                  onChange={handleJobType}
                  className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label
                  htmlFor="push-email"
                  className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
                >
                  Temporary
                </label>
              </div>
              <div className="flex items-center gap-x-3">
                <input
                  id="push-nothing"
                  name="typeofjob"
                  type="radio"
                  value="contract"
                  checked={jobType == 'contract'}
                  onChange={handleJobType}
                  className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label
                  htmlFor="push-nothing"
                  className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
                >
                  Contract
                </label>
              </div>
              <div className="flex items-center gap-x-3">
                <input
                  id="push-nothing"
                  name="typeofjob"
                  type="radio"
                  value="fulltime"
                  checked={jobType == 'fulltime'}
                  onChange={handleJobType}
                  className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label
                  htmlFor="push-nothing"
                  className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
                >
                  Full Time
                </label>
              </div>
              <div className="flex items-center gap-x-3">
                <input
                  id="push-nothing"
                  name="typeofjob"
                  value="parttime"
                  checked={jobType == 'parttime'}
                  type="radio"
                  onChange={handleJobType}
                  className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                />
                <label
                  htmlFor="push-nothing"
                  className="block text-sm font-medium leading-6 text-gray-900 dark:text-white"
                >
                  Part Time
                </label>
              </div>
            </div>
          </fieldset>
        </div>

        <div className="border-b border-gray-900/10 pb-12">
          <div className="mt-10 space-y-10">
            <fieldset className="rounded-lg mt-10">
              <div className="mt-6 space-y-3">
                <div className="relative flex gap-x-3">
                  <div className="flex h-6 items-center">
                    <input
                      id="recuritment"
                      name="recuritment"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                  </div>
                  <div className="text-sm leading-6">
                    <label
                      htmlFor="recuritment"
                      className="font-medium text-gray-900 dark:text-white"
                    >
                      PostedByRecuritmentAgency
                    </label>
                  </div>
                </div>
                <div className="relative flex gap-x-3">
                  <div className="flex h-6 items-center">
                    <input
                      id="directemployer"
                      name="directemployer"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                  </div>
                  <div className="text-sm leading-6">
                    <label
                      htmlFor="directemployer"
                      className="font-medium text-gray-900 dark:text-white"
                    >
                      PostedByDriectEmployer
                    </label>
                  </div>
                </div>
                <div className="relative flex gap-x-3">
                  <div className="flex h-6 items-center">
                    <input
                      id="graduate"
                      name="graduate"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                  </div>
                  <div className="text-sm leading-6">
                    <label
                      htmlFor="graduate"
                      className="font-medium text-gray-900 dark:text-white"
                    >
                      Graduate
                    </label>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </form>
  );
}
