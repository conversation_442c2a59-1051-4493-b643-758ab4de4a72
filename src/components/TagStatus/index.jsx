/* eslint-disable react/prop-types */
import React from 'react';
import { Tag } from 'antd';

const TagStatus = (props) => {
  const { record, sequenceChange } = props;
  const isMarkedAsCompleted = record?.isMarkedAsCompleted;
  const foundItem = sequenceChange.find((item) => item.seqId === record?.id);
  let status = foundItem?.status || record?.status;
  let color = '#00996d';
  let textContent = 'Active';
  let backgroundColor = '#e6fff8';
  let borderColor = '#9ce9cf';
  if (isMarkedAsCompleted && status === 'STOP') {
    status = 'COMPLETED';
  }
  switch (status) {
    case 'LIVE':
      color = '#00996d';
      textContent = 'Active';
      backgroundColor = '#e6fff8';
      borderColor = '#9ce9cf';
      break;
    case 'STOP':
      color = '#c24658';
      textContent = 'Stopped';
      backgroundColor = '#e6d5d5';
      borderColor = '#1d961d';
      break;
    case 'COMPLETED':
      color = '#f7f7f7';
      textContent = 'Completed';
      backgroundColor = '#5c98fa';
      borderColor = '#8a8487';
      break;
    case 'PAUSE':
      color = '#e37e4b';
      textContent = 'Paused';
      backgroundColor = '#f7c5ab';
      borderColor = '#b37336';
      break;
    case 'DRAFT':
      color = '#fff';
      textContent = 'Draft';
      backgroundColor = '#1A1A19';
      borderColor = '#b37336';
      break;
    default:
      color = '#ffffff';
      textContent = 'Active';
      backgroundColor = '#4fe37c';
      borderColor = '#1d961d';
      break;
  }
  return (
    // <Tag className='tag_status_sequence_list' color={color}>{textContent}</Tag>
    <span
      style={{
        backgroundColor: `${backgroundColor}`,
        borderColor: `${borderColor}`,
        color: `${color}`,
      }}
      className={`tag_status_sequence_list px-1 py-1`}
    >
      {textContent}
    </span>
  );
};

export default TagStatus;
