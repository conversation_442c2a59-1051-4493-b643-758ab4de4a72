/* eslint-disable react/no-unknown-property */
/* eslint-disable react/no-children-prop */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useContext, useState, useRef, useEffect, useMemo } from 'react';

import _get from 'lodash/get';

import {
  notification,
  Spin,
  Dropdown,
  Tag,
  Button,
  Modal,
  Avatar,
  Badge,
  Divider,
} from 'antd';
import DropdownComponent from '../Dropdown';
import { FiAlignJustify, FiHelpCircle } from 'react-icons/fi';
import { IoHelp } from 'react-icons/io5';
import { createSearchParams, Link, useNavigate } from 'react-router-dom';
import { IoMdNotificationsOutline } from 'react-icons/io';
import SearchContext from '../../context';
import {
  changePassword,
  getDataUniplieAccount,
  getUserById,
} from '../../services/auth';
import avatar from '../../assets/img/zileoIcon.png';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../store/auth';
import { useViewAs } from '../../store/viewAs';
import {
  updateReadNotification,
  markAllReadViewAs,
} from '../../services/notification';
import moment from 'moment';
import {
  BellOutlined,
  CheckSquareFilled,
  LogoutOutlined,
  QuestionOutlined,
} from '@ant-design/icons';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  getCountUnreadNotifi,
  getListNotifications,
  updateNotificationAllRead,
  updateNotificationIsRead,
} from '../../services/search';
import { getUserViewAs, isViewAs } from '../../helpers/getUserViewAs';
import { useLazyGetJobLeadDetailQuery } from '../../app/myLeads';
import LeadCard from '../JobsLeads/LeadCard';
import useJobLeads from '../../hooks/useJobLeads';
import { useDispatch } from 'react-redux';
import { useLazyGetAllUsersQuery } from '../../app/users';
import { saveAllStatusLead, saveAllUsers } from '../../store/common';
import { useCookies } from 'react-cookie';
import { requestPermission } from '../../services/search';
import { LINKEDIN_HELPER_COOKIES_NAMES } from '../../containers/Settings/LinkedIn';
import {
  reConnectedAccount,
  reConnectLinkedinAccount,
} from '../../services/users';
import { addHours } from 'date-fns';
import { SUPER_ADMIN } from '../../constants/common.constant';
import { getLinkS3 } from '../../services/aws';
import {
  Bell,
  Building,
  LinkedinIcon,
  ScrollText,
  Sparkles,
  Truck,
  UserRoundCheck,
  X,
} from 'lucide-react';
import { v4 } from 'uuid';
import { Separator } from '@radix-ui/react-separator';
import clsx from 'clsx';

const getIconNBgColor = (notificationType) => {
  switch (notificationType) {
    case 'ASSIGNED_LEAD':
      return {
        icon: <ScrollText className="h-3 w-3 text-blue-600" />,
        bgColor: 'bg-blue-50',
      };
    case 'LINKEDIN_DISCONNECTED':
      return {
        icon: <LinkedinIcon className="h-3 w-3 text-cyan-600" />,
        bgColor: 'bg-cyan-50',
      };
    case 'BULK_ADD_TO_BULLHORN':
      return {
        icon: <UserRoundCheck className="h-3 w-3 text-green-600" />,
        bgColor: 'bg-green-50',
      };
    case 'ONBOARDING_COMPANY_REQUEST':
      return {
        icon: <Building className="h-3 w-3 text-purple-600" />,
        bgColor: 'bg-purple-50',
      };
    case 'SENT_JOB':
      return {
        icon: <Truck className="h-3 w-3 text-navy-600" />,
        bgColor: 'bg-navy-50',
      };
    default:
      return {
        icon: <Bell className="h-3 w-3 text-gray-600" />,
        bgColor: 'bg-gray-50',
      };
  }
};

const Navbar = (props) => {
  const [cookies, setCookie, removeCookie] = useCookies(
    LINKEDIN_HELPER_COOKIES_NAMES
  );
  const currentUserId = getUserViewAs();
  const navigate = useNavigate();
  const { user } = useContext(SearchContext);
  const { profileUser, viewerAsUser, clearViewAs, setViewAs } = useViewAs();
  const { clearAuth, profile } = useAuth();
  const userDetails = profile?.user;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialNotificationList, setInitialNotificationList] = useState([]);
  const [countUnread, setCountUnread] = useState([]);
  const [password, setPassword] = useState({
    oldPassword: '',
    newPassword: '',
  });
  const [isLoadingNotif, setIsLoadingNotif] = useState(0);
  const [viewAsModalOpen, setViewAsModalOpen] = useState(false);
  const dropdownRef = useRef(null);
  const isSuperAdmin = userDetails?.role?.keyCode === SUPER_ADMIN;
  const queryClient = useQueryClient();
  const { onOpenSidenav, brandText } = props;

  const [userToSet, setUserToSet] = useState(null);
  const [isModalRequestOpen, setIsModalRequestOpen] = useState(false);
  const { setAuth, profile: profileUserAuth } = useAuth();
  const savingLocalStorage = () => {
    localStorage.setItem('PERMISSION_MESSAGE_DISPLAYING', 'false');
  };
  const showRequestModal = () => {
    setIsModalRequestOpen(true);
  };
  const handleRequestOk = async () => {
    requestPermission(viewerAsUser ? profileUser?.id : userDetails?.id)
      .then((res) => {
        console.log('res: ', res);
        notification.success({
          message: 'Success!',
          description: 'Request successfully!',
        });
      })
      .catch((err) => {
        console.log('err: ', err);
      })
      .finally(() => {
        savingLocalStorage();
        setIsModalRequestOpen(false);
      });
  };
  const handleRequestCancel = () => {
    savingLocalStorage();
    setIsModalRequestOpen(false);
  };

  const onLogout = async () => {
    // await axiosInstance.post("/auth/log-out")
    clearViewAs();
    clearAuth();
    queryClient.setQueryData(['CURRENT_USER'], null);
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    changePassword({
      oldPassword: password.oldPassword,
      newPassword: password.newPassword,
      id: user.id,
    }).then((res) => {
      if (res.data.success) {
        setIsModalOpen(false);
        notification.open({
          message: 'Success!',
          description: 'Your password is changed successfully!',
        });
      } else {
        notification.open({
          message: 'Failed!',
          description: `${res.data.msg}`,
        });
      }
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const onPasswordChange = (e) => {
    const { name, value } = e.target;
    setPassword({ ...password, [name]: value });
  };

  const handleClickNotif = async (notif) => {
    if (notif.id && notif.notificationType === 'NEW_JOB') {
      setIsLoadingNotif(notif.id);
      await updateReadNotification({ id: notif?.id, data: { isRead: true } });
      const updatedData = initialNotificationList.items.map((item) => {
        if (item.id === notif.id) {
          return { ...item, isRead: true };
        }
        return item;
      });
      const resultCount = initialNotificationList?.count?.notReadCount - 1;
      setInitialNotificationList({
        count: { notReadCount: resultCount },
        items: updatedData,
      });
      setIsLoadingNotif(0);
      navigate(
        `/search/${notif?.jobSearchId}?newJobOnly=true&notificationId=${notif.id}`
      );
    }
  };

  const handleClickAllRead = async () => {
    await updateNotificationAllRead();
    const updatedData = initialNotificationList.map((item) => {
      return { ...item, isRead: true };
    });
    setInitialNotificationList(updatedData);
    setCountUnread(0);
  };

  const [hasMoreNotifications, setHasMoreNotifications] = useState(true);
  const [loadingMoreNotif, setLoadinMoreNotif] = useState(false);

  const [pageNumber, setPageNumber] = useState(1);
  const { isFetching } = useQuery(
    ['GET_COUNT_NEW_SEARCH', pageNumber, getUserViewAs()],
    async () => {
      const countNoti = await getCountUnreadNotifi();
      setCountUnread(countNoti?.data?.result);
      const { data } = await getListNotifications(pageNumber, 10);
      const newNotifications = data.result;
      newNotifications.forEach((item) => {
        if (
          !item.isRead &&
          item.notificationType === 'LINKEDIN_DISCONNECTED' &&
          !localStorage.getItem(`NOTIF#${item.id}`)
        ) {
          notification.open({
            message: (
              <>
                <strong>{item.title}</strong>
              </>
            ),
            description: (
              <>
                {item.content}
                <p style={{ color: '#ccc', textAlign: 'right' }}>
                  {moment(item.updatedAt).format('DD/MM/YYYY HH:mm')}
                </p>
              </>
            ),
            duration: 30,
          });
          localStorage.setItem(`NOTIF#${item.id}`, true);
        }
      });
      if (pageNumber === 1) {
        setInitialNotificationList(newNotifications);
      } else {
        if (hasMoreNotifications) {
          if (!loadingMoreNotif) return;
          setInitialNotificationList(
            initialNotificationList.concat(newNotifications)
          );
          setLoadinMoreNotif(false);
        }
      }

      return data.result;
    },
    {
      refetchInterval: 60000,
    }
  );

  useEffect(() => {
    setInitialNotificationList([]);
    setPageNumber(1);
    setLoadinMoreNotif(false);
    setHasMoreNotifications(true);
  }, [getUserViewAs()]);

  const getUser = async () => {
    const { data } = await getUserById(
      viewerAsUser ? profileUser?.id : userDetails?.id
    );
    setUserToSet(data);
    isViewAs()
      ? setViewAs({ profileUser: data })
      : setAuth({ profile: { user: data } });
    if (data && !data?.grantId && !data?.grantUnipileId) {
      setIsModalRequestOpen(true);
    }
  };
  useEffect(() => {
    getUser();
  }, []);

  const loadMoreNotifications = async () => {
    if (initialNotificationList?.length === 0)
      return setHasMoreNotifications(false);
    setLoadinMoreNotif(true);
    setPageNumber((prevPageNumber) => prevPageNumber + 1);
  };

  const updateReadStatusById = (id) => {
    for (let i = 0; i < initialNotificationList.length; i++) {
      if (initialNotificationList[i].id === id) {
        initialNotificationList[i].isRead = true;
        setCountUnread(+countUnread - 1);
        setHasMoreNotifications(initialNotificationList);
        break;
      }
    }
  };

  //
  const [visible, setVisible] = useState(false);

  //
  const dispatch = useDispatch();

  const [getAllUsers] = useLazyGetAllUsersQuery();

  const userId = getUserViewAs();

  useEffect(() => {
    try {
      getAllUsers({
        userId,
      })
        .unwrap()
        .then(async (data) => {
          const sortedAllUsers = [...data].sort((a, b) => {
            const aName = a?.fullName || a?.username || a?.email;
            const bName = b?.fullName || b?.username || b?.email;
            return aName.localeCompare(bName);
          });
          const allUserWithAvatar = await Promise.allSettled(
            sortedAllUsers.map(async (user) => {
              if (user?.avatarId) {
                try {
                  const { data } = await getLinkS3(user?.avatarId);
                  return { ...user, avatarUrl: data };
                } catch (error) {
                  return { ...user };
                }
              } else {
                return { ...user };
              }
            })
          );
          dispatch(saveAllUsers(allUserWithAvatar?.map((item) => item.value)));
        })
        .catch((err) => {
          console.error('getAllUsers', err);
        });
    } catch {
      console.error('getAllUsers');
    }
  }, []);

  //
  const [getJobLeadDetail, { data: jobLeadDetailData }] =
    useLazyGetJobLeadDetailQuery();

  const handleReadNotifi = async (id, isRead) => {
    if (!isRead) {
      const { data } = await updateNotificationIsRead(id);
      if (data) {
        updateReadStatusById(id);
      }
    }

    await getJobLeadDetail({ id: id });
  };

  const handleGetJobLeadDetail = async (leadId) => {
    if (leadId) {
      await getJobLeadDetail({ id: leadId });
      await setVisible(false);
    }
  };

  //
  const { jobLeads: jobLeadsWithStatuses, reloadJobLeads } = useJobLeads();

  useEffect(() => {
    if (jobLeadsWithStatuses) {
      dispatch(saveAllStatusLead(jobLeadsWithStatuses));
    }
  }, [jobLeadsWithStatuses]);

  const getLinkedinPublicIdentifier = async (userId) => {
    try {
      const { data } = await getDataUniplieAccount(userId);
      const publicIdentifier =
        data?.result?.data?.dataLinkedInProfile?.public_identifier || null;
      const isDisconnected = data.result.data?.sources?.[0]?.status !== 'OK';
      const status =
        data.result.data?.sources?.[0]?.status !== 'OK'
          ? 'DISCONNECTED'
          : 'CONNECTED';
      return { publicIdentifier, isDisconnected, status };
    } catch (error) {
      console.log('error on getLinkedinPublicIdentifier: ', error);
      return {
        publicIdentifier: null,
        isDisconnected: undefined,
        status: null,
      };
    }
  };
  const handleReconnectLinkedin = async (payload) => {
    let attempt = 0;
    let success = false;
    const retries = 3;
    do {
      try {
        const { data } = await reConnectLinkedinAccount(
          {
            ...payload,
          },
          userId
        );
        console.log('LinkedIn reconnected successfully: ', data);
        setCookie('zl_latest_connect', new Date().toISOString());
        success = true;
      } catch (error) {
        attempt++;
        if (attempt >= retries) {
          console.error('Failed to reconnect LinkedIn after 3 attempts', error);
          const nowDate = new Date();
          const expiryDate = addHours(nowDate, 8); // Try again in 12 hours
          console.log('expiryDate', expiryDate);
          setCookie('zl_latest_connect', expiryDate);
        } else {
          console.warn(`Attempt ${attempt} failed. Retrying...`);
        }
      }
    } while (!success && attempt < retries);
    return attempt;
  };

  const autoReconnectLinkedin = async () => {
    const userToSet = profileUser || profileUserAuth?.user;
    const zlPIdentifier = cookies['zl_pidentifier'] || null;
    const { isDisconnected, status } =
      await getLinkedinPublicIdentifier(userId);
    if (status && status !== userToSet?.unipileAccountStatus) {
      if (isViewAs()) {
        setViewAs({
          profileUser: {
            ...profileUser,
            unipileAccountStatus: status,
          },
        });
      } else {
        setAuth({
          profile: {
            ...profile,
            user: {
              ...profile?.user,
              unipileAccountStatus: status,
            },
          },
        });
      }
    }

    const isSameIdentifier =
      zlPIdentifier && zlPIdentifier === userToSet?.linkedinPublicIdentifier;

    const payload = {
      country: userToSet?.linkedInCountryCode,
      userAgent: navigator.userAgent,
    };
    if (cookies['zl_atoken']) {
      payload.accessToken = cookies['zl_atoken'];
    }
    if (cookies['zl_ptoken']) {
      payload.premiumToken = cookies['zl_ptoken'];
    }

    if (payload.accessToken || payload.premiumToken) {
      if (isSameIdentifier && isDisconnected) {
        await handleReconnectLinkedin(payload);
      }
    }
  };

  useEffect(() => {
    if (profileUser) {
      reloadJobLeads();
    }
  }, [profileUser]);

  useEffect(() => {
    // Call autoReconnectLinkedin in the first time
    console.info('autoReconnectLinkedin in the first time');
    autoReconnectLinkedin();

    // Call autoReconnectLinkedin every 5 mins
    let interval = setInterval(autoReconnectLinkedin, 1000 * 5 * 60); // 5 mins
    return () => {
      clearInterval(interval);
    };
  }, [profileUser, profile]);

  return (
    <>
      <div className="bg-gradient-to-r from-cyan-500 via-purple-500 to-cyan-600 pb-1">
        <nav className="sticky top-0 z-40 flex flex-column md:flex-row flex-wrap items-center justify-between px-6 py-2 backdrop-blur-xl bg-white">
          <div className="md:h-[64px] flex flex-col justify-center">
            <div className="Montserrat">
              <a
                className="text-sm font-normal text-navy-700 hover:underline dark:text-white dark:hover:text-white"
                href=" "
              >
                Pages
                <span className="mx-1 text-sm text-navy-700 hover:text-navy-700 dark:text-white">
                  {' '}
                  /{' '}
                </span>
              </a>
              <Link
                className="text-sm font-semibold capitalize text-cyan-600 hover:underline dark:text-white dark:hover:text-white"
                to="#"
              >
                {brandText}
              </Link>
            </div>
            <h1 className="capitalize text-navy-700 dark:text-white text-2xl">
              <Link
                to="#"
                className="flex items-center gap-1 font-bold Montserrat capitalize"
              >
                {brandText}
                <Sparkles className="w-5 h-5 ml-2 text-cyan-500" />
              </Link>
            </h1>
          </div>

          <div
            className={`relative flex justify-center items-center gap-2 shadow-shadow-500 dark:!bg-navy-800 dark:shadow-none w-full md:w-auto md:gap-1`}
          >
            <span
              className="flex cursor-pointer text-xl text-gray-600 dark:text-white md:hidden"
              onClick={onOpenSidenav}
            >
              <FiAlignJustify className="h-5 w-5" />
            </span>
            <div className="flex flex-col items-start pl-2 rounded-full text-navy-700 dark:bg-navy-900 dark:text-white ml-auto">
              <Modal
                centered
                width={1000}
                bodyStyle={{
                  overflowY: 'auto',
                  maxHeight: 'calc(100vh - 200px)',
                }}
                title="Exit View As Mode"
                open={viewAsModalOpen}
                okText={'Confirm'}
                onOk={() => {
                  clearViewAs();
                  setViewAsModalOpen(false);
                  navigate('/settings', { replace: true });
                  window.location.reload(); // reload page to get new instance data
                }}
                onCancel={() => {
                  setViewAsModalOpen(false);
                }}
              >
                Do you want to exist view-as mode?
              </Modal>

              <div className="w-max text-sm font-medium text-gray-700 dark:text-white">
                {viewerAsUser ? '' : userDetails?.username} (
                {viewerAsUser ? profileUser.email : userDetails?.email})
                {viewerAsUser && (
                  <Tag
                    className="h-fit text-xs p-0.5 px-1 rounded-full ml-2"
                    color="success"
                  >
                    Viewer
                  </Tag>
                )}
              </div>
              <div className="text-sm text-gray-600 flex items-center">
                <span>
                  {viewerAsUser
                    ? profileUser?.role?.name || '-'
                    : userDetails?.role?.name}
                </span>
                {!isSuperAdmin && (
                  <>
                    <span className="border h-3 mx-2 border-sky-500"></span>
                    <span className="">
                      {viewerAsUser
                        ? profileUser?.organization?.name || '-'
                        : userDetails?.organization?.name}
                    </span>
                  </>
                )}
              </div>
            </div>
            {viewerAsUser && (
              <Button
                icon={<LogoutOutlined />}
                onClick={() => {
                  setViewAsModalOpen(true);
                }}
                danger
                shape="circle"
                className="flex items-center justify-center p-2 h-auto relative"
              />
            )}
            <Divider className="h-6" type="vertical" />
            <Button
              target="_blank"
              href={`${import.meta.env.VITE_HELP_URL}`}
              icon={
                <QuestionOutlined className="font-semibold text-gray-700" />
              }
              size="large"
              type="text"
              className="flex items-center justify-center p-2 h-auto relative"
            />
            {/* start Notification */}
            <Dropdown
              trigger={['click']}
              overlay={
                <div className="flex w-[25rem] min-h-[200px] overflow-auto flex-col gap-2 rounded-[20px] bg-white shadow-lg shadow-shadow-500 dark:!bg-navy-700 dark:text-white dark:shadow-none">
                  <div className="py-3 px-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-gray-600" />
                      <h3 className="font-semibold text-sm">Notifications</h3>
                      {+countUnread > 0 && (
                        <div
                          variant="secondary"
                          className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-gray-100 text-gray-800 hover:bg-gray-100/80 text-xs"
                        >
                          {countUnread} new
                        </div>
                      )}
                    </div>
                    <div>
                      <Button
                        type="text"
                        icon={<X size={20} />}
                        onClick={() => setVisible(false)}
                      />
                    </div>
                  </div>
                  {/* <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div
                      // onClick={handleClickAllRead}
                      className="text-sm text-navy-900 "
                      style={{
                        cursor: 'pointer',
                        hover: {
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Latest
                    </div>
                    <div
                      onClick={handleClickAllRead}
                      className="text-sm text-navy-900"
                      style={{
                        cursor: 'pointer',
                        '&:hover': {
                          textDecoration: 'underline',
                        },
                      }}
                    >
                      Mark all read
                    </div>
                  </div> */}
                  <InfiniteScroll
                    height={350}
                    dataLength={initialNotificationList?.length}
                    next={loadMoreNotifications}
                    hasMore={hasMoreNotifications}
                  >
                    {!isFetching &&
                      (initialNotificationList?.length <= 0
                        ? ''
                        : initialNotificationList?.map((notif, index) => {
                            const { icon, bgColor } = getIconNBgColor(
                              notif?.notificationType
                            );
                            return (
                              <>
                                <button
                                  key={notif.id}
                                  className={clsx(
                                    'flex w-full items-center hover:bg-gray-200 px-2 py-4 rounded-md p-3 hover:bg-gray-50 cursor-pointer transition-colors',
                                    !notif?.isRead ? 'bg-blue-50/50' : '',
                                    index <
                                      initialNotificationList.length - 1 &&
                                      'border-b border-gray-200'
                                  )}
                                  onClick={() => {
                                    handleReadNotifi(notif.id, notif.isRead);
                                    if (
                                      notif.notificationType ===
                                      'LINKEDIN_DISCONNECTED'
                                    ) {
                                      navigate('/settings?tab=linkedin');
                                      setVisible(false);
                                    } else if (
                                      notif.notificationType === 'SENT_JOB'
                                    ) {
                                      setVisible(false);
                                      navigate({
                                        pathname: '/mail-box',
                                        search: createSearchParams({
                                          tab: 'MYLEADS',
                                          leadJobBoardId: notif?.leadId,
                                          inMailType: 'NEW_LEAD',
                                        }).toString(),
                                      });
                                      // Do nothing for now
                                    } else {
                                      handleGetJobLeadDetail(notif?.leadId);
                                    }
                                  }}
                                >
                                  <LeadCard
                                    isNotification
                                    notificationType={notif.notificationType}
                                    componentNotification={
                                      <>
                                        <div
                                          onClick={() => {
                                            if (
                                              notif.notificationType !==
                                              'ONBOARDING_COMPANY_REQUEST'
                                            )
                                              return;
                                            navigate(
                                              `/user-management/company/${notif?.targetId}`
                                            );
                                          }}
                                          key={notif?.id || v4()}
                                          className={clsx(
                                            index <
                                              initialNotificationList.length -
                                                1 && 'px-2',
                                            'w-full'
                                          )}
                                        >
                                          <div>
                                            <div className="flex items-start gap-3">
                                              <div
                                                className={`p-1.5 rounded-full ${bgColor}`}
                                              >
                                                {icon}
                                              </div>
                                              <div className="flex-1 min-w-0 justify-start">
                                                <div className="flex items-center gap-2">
                                                  <p className="text-sm font-medium text-gray-900 truncate">
                                                    {notif?.title ||
                                                      notif?.notificationType
                                                        ?.replaceAll('_', ' ')
                                                        ?.toLowerCase()
                                                        ?.capitalize()}
                                                  </p>
                                                  {!notif?.isRead && (
                                                    <div className="h-1.5 w-1.5 bg-blue-500 rounded-full flex-shrink-0" />
                                                  )}
                                                </div>
                                                <p className="text-xs text-gray-600 mt-0.5 line-clamp-2 flex items-center justify-start text-start">
                                                  {notif.notificationType ===
                                                  'LINKEDIN_DISCONNECTED' ? (
                                                    <>{notif?.content}</>
                                                  ) : (
                                                    <>
                                                      {notif?.notificationType !==
                                                        'ONBOARDING_COMPANY_REQUEST' && (
                                                        <p className="mr-1">
                                                          {
                                                            notif?.creatorUsername
                                                          }
                                                        </p>
                                                      )}

                                                      {notif.notificationType ===
                                                      'SENT_JOB'
                                                        ? 'sent you a job at'
                                                        : notif.notificationType ===
                                                              'BULK_ADD_TO_BULLHORN' ||
                                                            notif.notificationType ===
                                                              'ONBOARDING_COMPANY_REQUEST'
                                                          ? notif?.content
                                                          : 'has assigned a new lead to you'}
                                                    </>
                                                  )}
                                                </p>
                                                <span className="text-xs text-gray-500 mt-1 block flex items-center justify-start">
                                                  {moment(
                                                    notif?.createdAt
                                                  ).format(
                                                    'DD/MM/YYYY HH:mm'
                                                  )}{' '}
                                                </span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        {/* <div
                                        style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          width: '100%',
                                        }}
                                        onClick={() => {
                                          if (
                                            notif.notificationType !==
                                            'ONBOARDING_COMPANY_REQUEST'
                                          )
                                            return;
                                          navigate(
                                            `/user-management/company/${notif?.targetId}`
                                          );
                                        }}
                                      >
                                        <div style={{ width: '100%' }}>
                                          <div
                                            style={{
                                              display: 'flex',
                                              justifyContent: 'space-between',
                                              alignItems: 'center',
                                              textAlign: 'left',
                                            }}
                                          >
                                            {notif.notificationType ===
                                            'LINKEDIN_DISCONNECTED' ? (
                                              <div>
                                                <p
                                                  style={{ fontWeight: '600' }}
                                                >
                                                  {notif?.title}
                                                </p>
                                                <p style={{ color: '#ccc' }}>
                                                  {notif?.content}
                                                </p>
                                                <p
                                                  style={{
                                                    color: '#ccc',
                                                    textAlign: 'right',
                                                  }}
                                                >
                                                  {moment(
                                                    notif.updatedAt
                                                  ).format('DD/MM/YYYY HH:mm')}
                                                </p>
                                              </div>
                                            ) : (
                                              <div>
                                                {!notif.notificationType ===
                                                  'ONBOARDING_COMPANY_REQUEST' && (
                                                  <span
                                                    style={{
                                                      fontWeight: '600',
                                                    }}
                                                  >
                                                    {notif?.creatorUsername}
                                                  </span>
                                                )}

                                                {notif.notificationType ===
                                                'SENT_JOB'
                                                  ? 'sent you a job at'
                                                  : notif.notificationType ===
                                                        'BULK_ADD_TO_BULLHORN' ||
                                                      notif.notificationType ===
                                                        'ONBOARDING_COMPANY_REQUEST'
                                                    ? notif?.content
                                                    : 'has assigned a new lead to you at'}
                                                <span style={{ color: '#ccc' }}>
                                                  {' '}
                                                  {moment(
                                                    notif?.createdAt
                                                  ).format(
                                                    'DD/MM/YYYY HH:mm'
                                                  )}{' '}
                                                </span>
                                              </div>
                                            )}
                                            <div>
                                              {!notif?.isRead && (
                                                <div
                                                  style={{
                                                    background: 'blue',
                                                    width: '10px',
                                                    height: '10px',
                                                    borderRadius: '50%',
                                                  }}
                                                ></div>
                                              )}
                                            </div>
                                          </div>
                                          <div
                                            style={{
                                              display: 'flex',
                                              marginTop: '5px',
                                              alignItems: 'center',
                                              height: '10px',
                                              color: '#888585',
                                            }}
                                          >
                                            <div style={{ fontSize: '11px' }}>
                                              {notif?.isRead
                                                ? 'READ'
                                                : 'UNREAD'}
                                            </div>
                                          </div>
                                        </div>
                                      </div> */}
                                      </>
                                    }
                                    lead={
                                      notif.notificationType === 'SENT_JOB'
                                        ? { jobBoardId: notif?.leadId }
                                        : _get(jobLeadDetailData, 'result')
                                    }
                                    reloadJobLeads={reloadJobLeads}
                                  />
                                </button>
                              </>
                            );
                          }))}
                    {isFetching && (
                      <div className="w-full h-44 flex justify-center items-center">
                        <Spin />
                      </div>
                    )}
                  </InfiniteScroll>
                  {/* {loadingMoreNotif ? <Spin /> : ''} */}
                </div>
              }
              visible={visible}
              onVisibleChange={setVisible}
            >
              <Button
                ref={dropdownRef}
                onClick={() => {
                  setPageNumber(1);
                }}
                icon={
                  <Badge count={countUnread} offset={[8, -8]}>
                    <Bell size={20} className="font-semibold text-gray-700" />
                  </Badge>
                }
                size="large"
                type="text"
                className="flex items-center justify-center p-2 h-auto relative"
              />
            </Dropdown>

            {/* Profile & Dropdown */}
            <DropdownComponent
              button={
                // <img
                //   className="h-10 w-10 rounded-full"
                //   src={avatar}
                //   alt="Elon Musk"
                // />
                // <Avatar src={} />
                <Button
                  shape="circle"
                  type="text"
                  size="large"
                  className="flex items-center justify-center p-2 h-auto shadow-md"
                >
                  <img className="h-6 w-6" src={avatar} />
                </Button>
              }
              children={
                <div className="flex w-56 flex-col justify-start rounded-[20px] bg-white bg-cover bg-no-repeat shadow-xl shadow-shadow-500 dark:!bg-navy-700 dark:text-white dark:shadow-none">
                  <div className="p-4">
                    <div className="flex items-center gap-2 cursor-pointer">
                      <p className="text-sm font-bold text-navy-700 dark:text-white">
                        Zileo
                      </p>
                    </div>
                  </div>
                  <div className="h-px w-full bg-gray-200 dark:bg-white/20 " />

                  <div className="flex flex-col p-4">
                    <a
                      href={`/user-management/${currentUserId}/edit`}
                      className="text-sm text-gray-800 dark:text-white hover:dark:text-white cursor-pointer"
                    >
                      Profile
                    </a>
                    <div
                      className="mt-3 text-sm font-medium text-red-500 hover:text-red-500 cursor-pointer"
                      onClick={onLogout}
                    >
                      Log Out
                    </div>
                  </div>
                </div>
              }
              classNames={'py-2 top-8 -left-[180px] w-max'}
            />
          </div>
        </nav>
      </div>
      {/* Grant permission modal */}
      {/* <Modal
        title="You need permission to use Sequence"
        open={isModalRequestOpen}
        onOk={handleRequestOk}
        onCancel={handleRequestCancel}
        okText="Request access"
        cancelText="Later"
      >
        An email has been set to the email address associated with the account
        please click the button to verify.
      </Modal> */}
    </>
  );
};

export default Navbar;
