import Styled from 'styled-components';

const ChartContainer = Styled.div`
display: block;
.chart-divider {
    display: block;
    width: 100%;
    height: 100px;
}
.chartjs-tooltip {
    font-family: "Inter", sans-serif;
    opacity: 1;
    position: absolute;
    background: #fff;
    box-shadow: 0 8px 10px #9299B815;
    padding: 10px 12px !important;
    border-radius: 3px;
    border: 1px solid #F1F2F6;
    min-width: 80px;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    pointer-events: none;
    transform: translate(-50%, 5%);
    z-index: 222;
    top: 0;
    left: 0;
    @media only screen and (max-width: 1199px){
        padding: 6px 8px !important;
    }
    &:before {
        position: absolute;
        content: '';
        border-top: 5px solid #fff;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        bottom: -5px;
        ${({ theme }) => (!theme.rtl ? 'left' : 'right')}: 50%;
        transform: translateX(-50%);
    }
}
.chartjs-tooltip-key {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: "pink";
    ${({ theme }) => (theme.rtl ? 'margin-left' : 'margin-right')}
    : 10px;
}
.tooltip-title {
    color: ${({ theme }) => theme['gray-color']};
    font-size: 12px;
    font-weight: 500 !important;
    font-family: 'Inter', sans-serif;
    text-transform: capitalize;
    margin-bottom: 4px;
}
.tooltip-value {
    color: #63b963;
    font-size: 22px;
    font-weight: 600 !important;
    font-family: 'Inter', sans-serif;
}
.tooltip-value sup {
    font-size: 12px;
    @media only screen and (max-width: 1199px){
        font-size: 11px;
    }
}
table{
    tbody{
        td{
            font-size: 13px;
            font-weight: 500;
            padding-bottom: 3px;
            white-space: nowrap;
            color: ${({ theme }) => theme['dark-color']};
            @media only screen and (max-width: 1199px){
                font-size: 12px;
            }
            .data-label{
                ${({ theme }) => (theme.rtl ? 'margin-right' : 'margin-left')}: 3px;
                color: ${({ theme }) => theme['light-gray-color']};
            }
        }
    }
}
`;

const LineChartWrapper = Styled.div`
    width: 100%;
    .linkedin-chart-wrap{
        min-height: 388px;
    }
    .growth-upward,
    .growth-downward{
        line-height: 2.2;
        h1{
            margin-bottom: 0;
            svg,
            i,
            img{
                margin-right: 6px;
            }
        }
    }
    .line-chart-row{
        &:not(:last-child){
            margin-bottom: 24px;
        }
        .border-linechart{
            border-bottom: 1px solid ${({ theme }) => theme['border-color-deep']};
            position: relative;
            &:before{
                position: absolute;
                content: '';
                width: 10px;
                height: 2px;
                ${({ theme }) => (theme.rtl ? 'right' : 'left')}: 0;
                bottom: -1px;
                background: #fff;
            }
        }
    }
    .overview-container{
        .line-chart-row{
            &:not(:last-child){
                margin-bottom: 18px;
                ${({ theme }) => (theme.topMenu ? 'margin-bottom: 25px' : 'margin-bottom: 18px')};
            }
        }
    }
`;

export { ChartContainer, LineChartWrapper };
