/* eslint-disable react/jsx-key */
/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable react/prop-types */
import { useNavigate } from 'react-router-dom';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal, Empty } from 'antd';
import './style.css';
const { confirm } = Modal;

export default function SyncTable({ jobData, setTableData }) {
  const showConfirm = (id) => {
    confirm({
      title: 'Do you Want to delete these items?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        const buffer = JSON.parse(localStorage.getItem('savedSync'));
        setTableData(buffer.filter((item) => item.id != id));
        localStorage.setItem(
          'savedSync',
          JSON.stringify(buffer.filter((item) => item.id != id))
        );
      },
      onCancel() {},
    });
  };

  const navigate = useNavigate();
  const onShowDetail = (title) => {
    navigate(`/syncs/${title}`);
  };

  if (jobData.length == 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  else
    return (
      <div className="flex flex-col text-center justify-center px-4 mt-24">
        <div className="overflow-x-auto">
          <div className="py-3 align-middle inline-block min-w-full">
            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200 table-fixed">
                <thead className="bg-[#e30075] dark:bg-brandLinear text-white font-[PoppinsMedium]">
                  <tr>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      Search Title
                    </th>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      City
                    </th>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      Keywords
                    </th>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      Salary
                    </th>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-3 py-6 w-[100px] text-center text-xs font-medium text-black uppercase tracking-wider">
                      Counts
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Edit</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200  dark:bg-gray-800 dark:text-white">
                  {jobData.map((job) => (
                    <tr
                      key={job.id}
                      className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                    >
                      <td
                        className="px-6 py-4 whitespace-nowrap"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className="w-[100px] inline-table text-sm font-medium text-gray-900 dark:text-white">
                              {job.jobTitle}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="w-[100px] inline-table">{job.city}</div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="w-[100px] inline-table">
                          {job.keywords.length != 0
                            ? job.keywords.map((item) => (
                                <div
                                  className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full bg-[#fff2e8] text-green-800"
                                >
                                  {item}
                                </div>
                              ))
                            : ''}
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="w-[100px] inline-table">{`£${job.minSalary} - £${job.maxSalary}`}</div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="w-[100px] inline-table">
                          {`${job.posted[0]} ~ ${job.posted[1]}`}
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => onShowDetail(job.id)}
                      >
                        <div className="w-[100px] inline-table">
                          {job.totalCount}
                        </div>
                      </td>
                      <td
                        className="px-3 py-4 whitespace-nowrap text-sm font-medium"
                        onClick={() => showConfirm(job.id)}
                      >
                        <div className="w-[100px] rounded-md font-semibold px-3 py-1 text-black hover:text-white hover:bg-cyan-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                          Delete
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    );
}
