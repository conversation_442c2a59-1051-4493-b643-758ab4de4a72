/* eslint-disable react/prop-types */
import React from 'react';
import { Spin } from 'antd';
import parse from 'html-react-parser';
import {
  ToolOutlined,
  TagOutlined,
  FileTextOutlined,
  ShoppingOutlined,
  FieldTimeOutlined,
  BankOutlined,
} from '@ant-design/icons';

function JobDetail({ job }) {
  if (job === undefined)
    return (
      <div className="text-center mt-20">
        <Spin tip="Loading" size="large">
          <div className="content" />
        </Spin>
      </div>
    );
  else
    return (
      <div className="py-1 sm:px-10 px-2 max-w-7xl mx-auto cursor-pointer">
        <div className="bg-white py-5 sm:px-10 px-0 lg:flex lg:justify-between items-center rounded-lg">
          <div className="lg:flex gap-5 items-center">
            <div className="pb-1 pt-1">
              <div>
                <div className="lg:flex md:flex sm:flex gap-3 pb-1 justify-between">
                  <h1 className="text-darkCyan font-bold pt-1 text-lg">
                    {job.job_title}
                  </h1>
                  <div className="flex mt-[10px]">
                    <div className="pr-1 flex justify-center mt-[2px] align-middle">
                      <BankOutlined />
                    </div>
                    {job.company}
                  </div>
                </div>
                <div className="mt-10 mb-10">{parse(job.description)}</div>
                <div className="lg:flex md:flex sm:flex">
                  <div className="flex mt-3">
                    <div className="pr-1 flex justify-center mt-[2px]">
                      <ToolOutlined />
                    </div>
                    {` Skill: ${job.skill}`}
                  </div>
                  <div className="flex mt-3">
                    <div className="pr-1 flex justify-center mt-[2px] lg:ps-3">
                      <ShoppingOutlined />
                    </div>
                    {` Status: ${job.status}`}
                  </div>
                </div>
                <div className="lg:flex md:flex sm:flex gap-3 text-md text-darkCyan">
                  <div className="flex mt-3">
                    <div className="pr-1 flex justify-center mt-[2px]">
                      <FileTextOutlined />
                    </div>
                    {` Industries: ${job.industries}`}
                  </div>
                  <div className="flex mt-3">
                    <div className="pr-1 flex justify-center mt-[2px]">
                      <TagOutlined />
                    </div>
                    {` Vacancy: ${job.vacancy}`}
                  </div>
                  <div className="flex mt-3">
                    <div className="pr-1 flex justify-center mt-[2px]">
                      <FieldTimeOutlined />
                    </div>
                    {` Lead Type: ${job.lead_type}`}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}

export default JobDetail;
