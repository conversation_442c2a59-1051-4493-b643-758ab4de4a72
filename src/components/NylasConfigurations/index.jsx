import * as React from 'react';
import { useState } from 'react';
import { useEffect } from 'react';

import { getNylasSchedulingConfigs } from '../../services/nylas';
import { Button, Spin, notification } from 'antd';
import { useNavigate } from 'react-router-dom';

const NylasConfigurations = ({ onSubmitEvent }) => {
  const navigate = useNavigate();
  const [nylasConfigurations, setNylasConfigurations] = useState([]);
  const [loading, setLoading] = useState(true);

  const getConfigurations = async () => {
    const { data } = await getNylasSchedulingConfigs();
    if (data) {
      const configurationsTemp = data?.result?.data || [];
      setNylasConfigurations(configurationsTemp);
    } else {
      notification.error({
        description: 'Network error! Try again!',
      });
    }
    setLoading(false);
    console.log('data: ', data);
  };

  useEffect(() => {
    getConfigurations();
  }, []);

  return (
    <>
      {loading && (
        <div className="w-full h-48 flex items-center justify-center">
          <Spin />
        </div>
      )}
      {!loading && nylasConfigurations.length === 0 && (
        <div className="w-full h-48 flex items-center justify-center">
          <span className="text-base">
            There are no events found. Click on{' '}
            <a
              target="_blank"
              className="text-blue-500 font-medium"
              href="/settings?scheduler=true"
            >
              this link
            </a>{' '}
            to add new event.
          </span>
        </div>
      )}
      {!loading && nylasConfigurations.length !== 0 && (
        <div className="flex gap-2 flex-col">
          {nylasConfigurations?.map((config) => (
            <div className="flex justify-between items-center px-2 py-3 border rounded-lg">
              <div>{config?.event_booking?.title}</div>

              <div className="flex gap-3 justify-center">
                <Button onClick={() => onSubmitEvent(config)}>Insert</Button>
                {/* <Button onClick={()=>navigator.clipboard.writeText(opt)} >Copy</Button> */}
                <Button
                  onClick={() =>
                    window.open(`/schedule?config_id=${config?.id}`, '_blank')
                  }
                >
                  Preview
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default NylasConfigurations;
