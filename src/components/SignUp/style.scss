@font-face {
  font-family: var(--font-family);
  src: url('../../constants/fonts/PoppinsRegular.ttf');
}
.login-wrapper {
  width: 100%;
  .login-page-container {
    //min-height: 80vh;
    //width: 95vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin: auto;
    padding-top: 6%;
    padding-right: 6%;
    .logo-title {
      color: var(--color-primary-2-hex);
      font-size: 130px;
      font-family: var(--font-family);
    }
  }
  .hivesys-logo {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 100%;
    }
  }
  @media (max-width: 1000px) {
    .login-page-container {
      flex-direction: column;
      padding: 7%;
      .logo-title {
        font-size: 50px;
      }
    }
  }
  .input-field-item {
    height: 80px;
    border-radius: 8px;
    color: #14182f;
    border: 1px solid #d5d7e4;
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(20, 24, 47, 0.12);
  }
  .main_div {
    width: 100%;
    height: 35vh;
    position: relative;
  }
  .box {
    width: 100%;
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 50px;
  }
  .box .inputBox {
    display: flex;
    position: relative;
    width: 100%;
  }
  .box .inputBox input {
    width: 100%;
    font-family: var(--font-family);
    font-weight: 600;
    padding-top: 35px;
    margin-left: 15px;
    font-size: 16px;
    color: var(--form-field-font-color);
    letter-spacing: 1px;
    margin-bottom: 25px;
    border: none;
    background: var(--form-field-background-color);
    outline: none;
  }
  .box .inputBox label {
    position: absolute;
    top: 0;
    left: 0;
    font-family: var(--font-family);
    margin-left: 75px;
    padding: 30px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--form-label-font-color);
    transition: 0.5s;
  }
  .box .inputBox input:focus ~ label,
  .box .inputBox input:valid ~ label {
    top: -15px;
    left: 0;
    font-family: var(--font-family);
    font-size: 12px;
    color: #14182f;
  }
  .prefix-icon {
    margin: auto;
    padding: 15px;
    padding-left: 20px;
    svg {
      height: 2em !important;
      width: 2em !important;
    }
  }
  .suffix-icon {
    height: 90%;
    display: flex;
    align-items: center;
    .password-icon-style {
      width: var(--icon-size);
    }
    .anticon {
      padding: 12px;
    }
    svg {
      height: 1.3em !important;
      width: 1.3em !important;
    }
  }
  .login-form-container {
    // height: 95%;
    // width: 42%;
    flex: 0.7;
    border-radius: 8px;
    box-shadow:
      -4px 4px 8px 0 rgba(60, 66, 87, 0.1),
      -12px 12px 24px 0 rgba(33, 54, 89, 0.06),
      -16px 20px 40px 0 rgba(89, 116, 166, 0.04);
    background-color: #fff;
    padding: 3%;
    // background-color: #2E3032;
    .container-padding {
      padding: 5% 5% 20% 5%;
      @media (max-width: 1000px) {
        padding: 5% 5% 30% 5%;
      }
    }
    .login-form-title {
      color: var(--form-title-font-color);
      letter-spacing: 0;
      width: 70%;
      font-size: 36px;
      font-weight: 500;
      font-family: var(--font-family);
      text-align: initial;
      span {
        color: var(--secondary-color);
        font-weight: 900;
      }
    }
  }
  .hivesys-login-form {
    margin-top: 15%;
    #login-form {
      margin-bottom: 10%;
    }
    .remember-me {
      font-family: poppins;
      color: #14182f;
      font-weight: 400;
    }
    .login-btn-item {
      margin-top: 7%;
      .ant-form-item-control-input-content {
        display: block;
      }
    }
    .login-form-button {
      height: 60px;
      width: 45%;
      font-size: 17px;
      font-family: var(--font-family);
      font-weight: 600;
      background: var(--color-primary-2-hex);
    }
    .ant-form-item-control-input-content {
      display: flex;
      justify-content: space-between;
    }
    .ant-input-prefix {
      color: #14182f;
      padding: 1.5%;
      width: 12%;
      font-size: 24px;
    }
    .ant-input-prefix img {
      width: var(--icon-size);
    }
    .ant-input {
      color: #14182f;
    }
    .ant-input::placeholder {
      color: var(--form-field-placeholder-color);
    }
    .anticon.ant-input-password-icon {
      color: #14182f;
    }
    .ant-input-affix-wrapper {
      height: 60px;
      border-radius: 8px;
      border: none;
    }
  }
}
