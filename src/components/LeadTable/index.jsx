/* eslint-disable react/prop-types */
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal, Spin, Empty, Pagination, Drawer } from 'antd';
import LeadDetail from '../LeadDetail';
import React, { useState } from 'react';
import './style.css';
const { confirm } = Modal;

export default function SearchTable({
  leadData,
  setLeadData,
  pageInfo,
  setPageInfo,
  isLoading,
  setLoading,
  deleteJob,
  getJobsLeads,
}) {
  const [view, setView] = useState('website');
  const [leadDetail, setLeadDetail] = useState([]);
  const [open, setOpen] = useState(false);

  const showConfirm = (id) => {
    confirm({
      title: 'Do you Want to delete these items?',
      icon: <ExclamationCircleFilled />,
      content: ' ',
      onOk() {
        deleteJob(id).then(() => {
          getJobsLeads({ page: pageInfo.page, pageSize: 10 }).then((data) => {
            setLeadData(data.data.jobs);
            setPageInfo({ ...pageInfo, totalCount: data.data.totalCount });
            setLoading(false);
          });
        });
      },
      onCancel() {},
    });
  };

  React.useEffect(() => {
    window.addEventListener('resize', () =>
      window.innerWidth < 1200 ? setView('mobile') : setView('website')
    );
  }, []);

  const showDrawer = (id) => {
    setLeadDetail(leadData.filter((lead) => lead.id == id));
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  const onPageChange = (page) => {
    setLoading(true);
    setPageInfo({ page: page });
  };

  if (isLoading)
    return (
      <div className="text-center mt-40">
        <Spin size="large" />
      </div>
    );

  if (leadData.length == 0)
    return (
      <div className="text-center mt-40">
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  else
    return (
      <div className="flex flex-col text-center justify-center">
        {view === 'website' ? (
          <Drawer
            title="Lead Detail"
            placement="right"
            onClose={onClose}
            open={open}
            width="80%"
          >
            <LeadDetail job={leadDetail[0]} />
          </Drawer>
        ) : (
          <Drawer
            title="Lead Detail"
            placement="right"
            onClose={onClose}
            open={open}
            width="100%"
          >
            <LeadDetail job={leadDetail[0]} />
          </Drawer>
        )}
        <div className="overflow-x-auto">
          <div className="py-3 align-middle inline-block min-w-full">
            <div className="overflow-hidden sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-[#e30075] dark:bg-brandLinear text-white font-[PoppinsMedium]">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Job Title</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Company</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Skills</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Industry</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Vacancy</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Lead Type</div>
                    </th>
                    <th
                      scope="col"
                      className="px-3 p-6 text-center text-xs font-medium text-black uppercase tracking-wider"
                    >
                      <div className="w-[100px] inline-table">Status</div>
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Edit</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200  dark:bg-gray-800 dark:text-white">
                  {leadData.map((job) => (
                    <tr
                      key={job.id}
                      className="hover:bg-[#a8cbfc] clickable-row cursor-pointer"
                    >
                      <td
                        className="px-6 py-4 whitespace-nowrap"
                        onClick={() => showDrawer(job.id)}
                      >
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {job.job_title}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => showDrawer(job.id)}
                      >
                        {job.company}
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap"
                        onClick={() => showDrawer(job.id)}
                      >
                        <div
                          className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full bg-green-100 text-green-800"
                        >
                          {job.skill}
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap"
                        onClick={() => showDrawer(job.id)}
                      >
                        <div
                          className="px-2 inline-flex text-xs leading-5
                      font-semibold rounded-full bg-[#fff2e8] text-green-800"
                        >
                          {job.industries}
                        </div>
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => showDrawer(job.id)}
                      >
                        {job.vacancy}
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => showDrawer(job.id)}
                      >
                        {job.lead_type}
                      </td>
                      <td
                        className="px-6 py-4 whitespace-nowrap text-sm text-black"
                        onClick={() => showDrawer(job.id)}
                      >
                        {job.status}
                      </td>
                      <td
                        className="px-3 py-4 whitespace-nowrap text-sm font-medium"
                        onClick={() => showConfirm(job.id)}
                      >
                        <div className="rounded-md font-semibold px-3 py-1 text-black hover:text-white hover:bg-cyan-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                          Delete
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <div className="ms-14 dark:text-white">
                <Pagination
                  current={pageInfo.page}
                  onChange={onPageChange}
                  total={pageInfo.totalCount}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}
