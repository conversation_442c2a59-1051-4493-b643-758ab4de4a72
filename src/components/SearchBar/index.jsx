/* eslint-disable react/prop-types */
import React from 'react';
import { FiSearch } from 'react-icons/fi';
import FilterTag from '../../components/FilterTag';
import { getJobs } from '../../actions';
import { ReactComponent as Filter } from '../../assets/svg/Filter.svg';

function SearchBar({
  savedSearchData,
  setSavedSearchData,
  setSearchData,
  searchData,
  // onSaveSearchClick,
  setSearchLoading,
  setShowFilter,
  showFilter,
}) {
  const handleChange = (event) => {
    setSearchData({ ...searchData, keywords: event.target.value });
  };
  return (
    <div className="text-center ">
      <div className="relative mt-[52px] flex h-[61px] flex-grow items-right text-right justify-around gap-0 rounded-full bg-white py-2 shadow-xl shadow-shadow-500 dark:!bg-navy-800 dark:shadow-none w-full sm:w-[100%]  md:w-[100%] md:flex-grow-0 md:gap-1 xl:w-[100%] xl:gap-2">
        <div className="flex h-full items-center rounded-full bg-lightPrimary text-navy-700 dark:bg-navy-900 dark:text-white w-[50%] sm:w-[60%] md:w-[75%] xl:w-[75%]">
          <p className="pl-3 pr-2 text-xl">
            <FiSearch className="h-4 w-4 text-gray-400 dark:text-white" />
          </p>
          <input
            type="text"
            placeholder="Search..."
            value={searchData.keyword}
            onChange={handleChange}
            className="block h-full w-full rounded-full bg-lightPrimary text-sm font-medium text-navy-700 outline-none placeholder:!text-gray-400 dark:bg-navy-900 dark:text-white dark:placeholder:!text-white sm:w-fit"
          />
        </div>
        <button
          onClick={() => {
            setSearchLoading(true);
            getJobs({ searchData })
              .then((res) => {
                setSearchLoading(false);
                setSearchData({ ...searchData, data: res.data.data });
              })
              .catch((err) => {
                return err;
              });
          }}
          type="submit"
          className="rounded-[50px] bg-indigo-600 px-1 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 md:w-[15%] xl:w-[18%] w-[25%]"
        >
          Search
        </button>
        <span
          className="sm:hidden flex align-middle"
          onClick={() => setShowFilter(!showFilter)}
        >
          <Filter className="w-5 text-gray-400 mr-3 pb-2" />
        </span>
      </div>
      <div className="flex justify-between">
        <div className="mt-10">
          <FilterTag
            tags={savedSearchData}
            setTags={setSavedSearchData}
            setSearchData={setSearchData}
          />
        </div>
        <div className="mt-6 flex items-center justify-end gap-x-6">
          <button
            // onClick={onSaveSearchClick}
            className="rounded-md bg-indigo-600 px-2 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Save Search
          </button>
        </div>
      </div>
    </div>
  );
}

export default SearchBar;
