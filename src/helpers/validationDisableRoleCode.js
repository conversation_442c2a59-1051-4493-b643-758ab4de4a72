import { userRole } from '../constants/common.constant';
import { useAuth } from '../store/auth';
import { useViewAs } from '../store/viewAs';

export const validationDisableRoleCode = ({ user, loading }) => {
  const { profile } = useAuth();
  const { profileUser } = useViewAs();
  return user?.id === profile?.user?.id ||
    profile?.user?.role?.keyCode === userRole.BASIC_USER ||
    profileUser?.role?.keyCode === userRole.BASIC_USER ||
    profileUser?.id === user.id
    ? true
    : loading;
};
