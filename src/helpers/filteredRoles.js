import { SUPER_ADMIN, userRole } from '../constants/common.constant';

export const filterAndTransformRoles = ({
  data,
  viewerAsUser,
  viewAsRole,
  userLoginRole,
}) => {
  const currentUserRole = viewerAsUser ? viewAsRole : userLoginRole;
  let filteredRoles = [];
  if (currentUserRole === SUPER_ADMIN) {
    filteredRoles = data.result;
  } else if (currentUserRole === userRole.ADMIN) {
    filteredRoles = data.result.filter(
      (role) =>
        role.keyCode === userRole.ADMIN ||
        role.keyCode === userRole.BASIC_USER ||
        role.keyCode === userRole.MANAGEMENT
    );
  } else if (currentUserRole === userRole.BASIC_USER) {
    filteredRoles = data.result.filter(
      (role) => role.keyCode === userRole.BASIC_USER
    );
  }
  const output = filteredRoles.map((item) => ({
    value: item.id,
    label: item.name,
  }));
  return output;
};
