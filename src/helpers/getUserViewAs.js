import { useViewAs } from '../store/viewAs';
import { useAuth } from '../store/auth';
import { userRole } from '../constants/common.constant';

export const getUserViewAs = () => {
  return useViewAs?.getState()?.viewerAsUser
    ? useViewAs?.getState()?.profileUser?.id
    : useAuth?.getState()?.profile?.user?.id;
};

export const getNylasGrantIdViewAs = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser.grantId
    : useAuth.getState().profile.user.grantId;
};

export const getEMailViewAs = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser.grantEmail
    : useAuth.getState().profile.user.grantEmail;
};

export const getNameViewAs = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser.fullName
    : useAuth.getState().profile.user.fullName;
};

export const getUserViewAsBullhorn = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.organization?.bhClientSecret
    : useAuth.getState().profile?.user?.organization?.bhClientSecret;
};

export const getUserViewAsRole = () => {
  return useViewAs?.getState()?.viewerAsUser
    ? useViewAs?.getState()?.profileUser?.role?.keyCode
    : useAuth?.getState()?.profile?.user?.role?.keyCode;
};

export const getUserViewAsLicense = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.organization?.planType
    : useAuth.getState().profile?.user?.organization?.planType;
};

export const isViewAs = () => {
  return useViewAs.getState().viewerAsUser;
};

export const isGrantedSequence = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.grantId ||
        useViewAs.getState().profileUser?.grantUnipileId
    : useAuth.getState().profile?.user?.grantId ||
        useAuth.getState().profile?.user?.grantUnipileId;
};

export const getUserViewAsOrganizationId = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.organization?.id
    : useAuth.getState().profile?.user?.organization?.id;
};

export const getUserViewAsPermissions = () => {
  const permissionsList = useViewAs?.getState()?.viewerAsUser
    ? useViewAs?.getState()?.profileUser?.role?.rolePermissions
    : useAuth?.getState()?.profile?.user?.role?.rolePermissions;

  const existingPermissions = permissionsList
    ?.filter((item) => item?.allowRead && item?.allowWrite)
    .map((item) => item?.permission?.keyCode);

  return existingPermissions;
};

export const isGoToOnboardingFlow = () => {
  const user = useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser
    : useAuth.getState().profile?.user;
  const userRoleKeycode = user?.role?.keyCode;

  if (userRoleKeycode === userRole.ADMIN) {
    const isAdminOnboardingCompleted =
      user?.organization?.adminOnboardingCompleted;
    return !isAdminOnboardingCompleted;
  }

  //TODO: need to handle for BASIC USER, MANAGEMENT
  return false;
};

export const getUserViewAsProfile = () => {
  return useViewAs?.getState()?.viewerAsUser
    ? useViewAs?.getState()?.profileUser
    : useAuth?.getState()?.profile?.user;
};

export const getUserViewAsSubscriptionId = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.organization?.subscriptionId
    : useAuth.getState().profile?.user?.organization?.subscriptionId;
};

export const getUserViewAsLicenseType = () => {
  return useViewAs.getState().viewerAsUser
    ? useViewAs.getState().profileUser?.licenseType
    : useAuth.getState().profile?.user?.licenseType;
};
