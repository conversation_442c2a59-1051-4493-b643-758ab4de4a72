import { notification } from 'antd';
import { MERTAGS_DEFINITION } from '../constants/common.constant';

export const migrateCompanyData = (oldData) =>
  oldData?.length > 0 &&
  oldData?.map((item) => ({
    ...item,
    id: item?.id,
    summary: item?.summary,
    unique_company_id: item?.id,
    name: item?.name,
    website_url: item?.website_url,
    email_domain: item?.primary_domain,
    info_url: '',
    best_address: item?.location,
    street: item?.street_address,
    city: item?.city,
    region: item?.country,
    postal_code: item?.postal_code,
    country_code: '',
    founded: item?.founded_year,
    funding: null,
    revenue: '',
    revenue_val: '',
    industry: item?.industry,
    industry_str: item?.industry,
    // industry_tags: [...item?.industries],
    type: '',
    phone: item?.primary_phone?.number,
    fax: item?.primary_phone?.number,
    description: '',
    num_followers: item?.estimated_num_employees,
    employees: item?.estimated_num_employees,
    calculated_employee_size: item?.estimated_num_employees,
    total_profiles: '',
    sic_codes: [],
    naics_codes: [],
    ticker_symbol: '',
    ticker_symbol_url: '',
    logo_url: item?.logo_url,
    links: {
      linkedin: item?.profile_url,
      facebook: item?.facebook_url,
      twitter: item?.twitter_url,
      website: item?.website_url,
    },
    affiliated_companies_count: null,
    website_rank: null,
    website_category: null,
    website_total_visits: null,
    website_search_keywords: [],
    similar_websites: null,
    competitor_company_ids: [],
    top_employees: [],
    profile_count: 0,
    company_growth: null,
    // techstack: [...item?.keywords],
    restrictions: {},
    company_index: 0,
    company_lists: [],
    intent_signals: [],
    industry_keywords: [],
    company_news: {
      funding_news: [],
      mna_child_news: [],
      mna_parent_news: [],
      exec_change_news: [],
    },
    genai_company_competitors: [],
  }));

export const migrateCompanyDetailData = (companyObject) => ({
  ...companyObject,
});

export const migrateContactData = (oldData) =>
  oldData?.length > 0 &&
  oldData?.map((item) => ({
    ...item,
    id: item?.id,
    summary: item?.summary,
    experiences:
      item?.work_experience?.length > 0 ? [...item?.work_experience] : [],
    name: item?.name,
    first_name: item?.first_name,
    last_name: item?.last_name,
    profile_pic: item?.profile_picture_url_large,
    lookup_type: item?.type,
    links: {
      linkedin: item?.public_profile_url,
      // facebook: 'https://www.facebook.com/john.doe',
      // twitter: 'https://www.twitter.com/johndoe',
    },
    url: '',
    linkedin_url: item?.public_profile_url,
    location: item?.location,
    current_title: item?.headline,
    current_employer: item?.work_experience?.[0]?.company || '',
    company_id: 1234567,
    company_info_url: '',
    teaser: {
      emails: [],
      phones: [
        // {
        //   number: '',
        //   is_premium: false,
        // },
        // {
        //   number: '****** 987 6543',
        //   is_premium: true,
        // },
      ],
      personal_emails: [],
      professional_emails: [],
    },
    education: item?.education?.length > 0 ? [...item?.education] : [],
    jobs: [],
    skills: [],
    last_job_change: '',
    work_email: item.work_email,
    company_details:
      item?.work_experience?.length > 0
        ? {
            id: item?.work_experience?.[0]?.company_id,
            name: item?.work_experience?.[0]?.company,
            logo_url: item?.work_experience?.[0]?.logo,
            info_url: '',
            website_category: item?.work_experience?.[0]?.industry,
          }
        : null,
  }));

export const handleGenerateNotificationBulkAddContact = (
  saveArr,
  sendArr,
  unretrievableContactIds = [],
  emailNotFound = []
) => {
  if (unretrievableContactIds.length) {
    let content = `${unretrievableContactIds.length} / ${sendArr.length} contacts are unable to retrieve. Please retry again in few minutes.`;
    if (saveArr.length) {
      content += `\n${saveArr.length} / ${saveArr.length} contacts are added`;
    }
    notification.error({
      message: content,
    });
    return;
  }

  if (emailNotFound.length) {
    let content = '';
    if (saveArr.length) {
      content += `${saveArr.length} / ${saveArr.length} contacts added`;
    }
    content += `\n${emailNotFound.length} / ${sendArr.length} contacts have no available email`;

    if (saveArr.length < sendArr.length && saveArr.length > 0) {
      content += `\n${saveArr.length} / ${sendArr.length} contacts are successfully added, ${sendArr.length - saveArr.length} / ${sendArr.length} contacts already exist`;
    }
    notification.warning({
      message: content,
    });
    return;
  }

  if (saveArr.length == 0) {
    notification.warning({
      message: `${sendArr.length} / ${sendArr.length} contacts already exist`,
    });
    return;
  }

  if (saveArr.length === sendArr.length) {
    notification.success({
      message: `${sendArr.length} / ${sendArr.length} contacts are successfully added`,
    });
    return;
  }

  if (saveArr.length < sendArr.length && saveArr.length > 0) {
    notification.success({
      message: `${saveArr.length} / ${sendArr.length} contacts are successfully added, ${sendArr.length - saveArr.length} / ${sendArr.length} contacts already exist`,
    });
  }
};

export const cleanPayload = (payload) => {
  // clean payload to add contac to Bullhorn
  if (payload === null || payload === undefined) {
    return {};
  }

  const cleanObject = {};
  Object.keys(payload).forEach((key) => {
    const value = payload[key];

    if (value !== '' && value !== undefined) {
      if (value !== '' && value !== null) {
        if (value.length !== 0) {
          if (typeof value === 'object' && !Array.isArray(value)) {
            const cleanedSubObject = cleanPayload(value);
            if (Object.keys(cleanedSubObject).length !== 0) {
              cleanObject[key] = cleanedSubObject;
            }
          } else if (Array.isArray(value) && value.length > 0) {
            const cleanedArray = value.reduce((acc, item) => {
              if (item !== '' && item !== undefined) {
                acc.push(item);
              }
              return acc;
            }, []);
            cleanObject[key] = cleanedArray;
          } else {
            cleanObject[key] = value;
          }
        }
      }
    }
  });

  return cleanObject;
};

const INVALID_MESSAGES = {
  INVALID_MERGE_TAG: 'Email contain invalid merge tag(s)',
};

export const getInvalidMessage = (content) => {
  const validMergetags = MERTAGS_DEFINITION.map((item) => `{{${item.id}}}`);
  let message = '';
  if (!content) return message;

  // Check invalid merge tags
  const mergeTags = content?.match(/\{{(.*?)\}}/g);
  if (mergeTags?.length > 0) {
    const invalidMergeTagChecker = mergeTags?.find(
      (merTagItem) => !validMergetags.includes(merTagItem)
    );
    if (invalidMergeTagChecker) {
      message = INVALID_MESSAGES.INVALID_MERGE_TAG;
    }
  }

  return message;
};

export const findProp = (obj, key, out) => {
  var i,
    proto = Object.prototype,
    ts = proto.toString,
    hasOwn = proto.hasOwnProperty.bind(obj);

  if ('[object Array]' !== ts.call(out)) out = [];

  for (i in obj) {
    if (hasOwn(i)) {
      if (i === key) {
        out.push(obj[i]);
        if (obj[i]?.['sequence']) {
          findProp(obj[i]?.['sequence'], key, out);
        }
      } else if (
        '[object Array]' === ts.call(obj[i]) ||
        '[object Object]' === ts.call(obj[i])
      ) {
        findProp(obj[i]?.['sequence'], key, out);
      }
    }
  }

  return out;
};

export const decodeBase64 = (str) => {
  return atob(str);
};

export const encodeBase64 = (str) => {
  return btoa(str);
};

export const calculateCurrentPage = (start, pageSize) => {
  if (pageSize === 0) return 1; // Avoid division by zero
  return Math.floor(start / pageSize) + 1;
};

export const currencyFormatter = (value = 0, currency = 'GBP') => {
  try {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      trailingZeroDisplay: 'stripIfInteger',
    });

    return formatter.format(value);
  } catch (error) {
    return value;
  }
};

export const formatBytes = (bytes, decimals = 2) => {
  if (!+bytes) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = [
    'Bytes',
    'KiB',
    'MiB',
    'GiB',
    'TiB',
    'PiB',
    'EiB',
    'ZiB',
    'YiB',
  ];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

export const textContent = (elem) => {
  if (!elem) {
    return '';
  }
  if (typeof elem === 'string') {
    return elem;
  }
  const children = elem.props && elem.props.children;
  if (children instanceof Array) {
    return children.map(textContent).join('');
  }
  return textContent(children);
};

export const generateRandomPassword = (length = 12) => {
  const charset =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+[]{}|;:,.<>?';
  let password = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  return password;
};

export const generateRandomUsername = () => {
  const adjectives = [
    'Brave',
    'Clever',
    'Bright',
    'Swift',
    'Kind',
    'Bold',
    'Happy',
    'Calm',
    'Witty',
    'Loyal',
  ];
  const nouns = [
    'Explorer',
    'Pioneer',
    'Guardian',
    'Seeker',
    'Creator',
    'Builder',
    'Dreamer',
    'Warrior',
    'Scholar',
    'Hero',
  ];

  const randomAdjective =
    adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
  const randomNumber = Math.floor(Math.random() * 1000); // Optional random number for uniqueness

  return `${randomAdjective}${randomNoun}${randomNumber}`;
};

export const urlToFile = async (url, fileName) => {
  try {
    // Fetch the file from the URL
    const response = await fetch(url);
    const blob = await response.blob();

    // Create a File object from the Blob
    const file = new File([blob], fileName, { type: blob.type });
    return file;
  } catch (error) {
    console.error('Error converting URL to File:', error);
    throw error;
  }
};

export const getInitials = (name) => {
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();
};
