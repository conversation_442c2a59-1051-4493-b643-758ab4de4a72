/* eslint-disable react/no-deprecated */
import React from 'react';
import { createRoot } from 'react-dom/client';
import 'antd/dist/reset.css';
import './index.css';
import App from './App';
import { queryClient } from './utils/restClient';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import {
  persistQueryClient,
  PersistQueryClientProvider,
} from '@tanstack/react-query-persist-client';
import { Provider } from 'react-redux';
import store from './store';

const localStoragePersister = createSyncStoragePersister({
  storage: window.localStorage,
});

persistQueryClient({
  queryClient,
  persister: localStoragePersister,
  maxAge: 0,
  hydrateOptions: '{},',
  dehydrateOptions: {
    shouldDehydrateQuery: ({ queryKey }) => {
      if (queryKey.includes('CURRENT_USER')) {
        return false;
      }
      return true;
    },
  },
});

const container = document.getElementById('root');
container.classList.add('zileo-auto-reconnect-linkedin');
const root = createRoot(container);
root.render(
  <>
    <Provider store={store}>
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{ persister: localStoragePersister }}
      >
        <App />
      </PersistQueryClientProvider>
    </Provider>
  </>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals();

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
// serviceWorker.unregister();
